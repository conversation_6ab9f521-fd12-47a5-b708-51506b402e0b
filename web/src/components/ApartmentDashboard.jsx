import { useState, useEffect } from "react";
import {
  Search,
  Bell,
  Menu,
  Plus,
  Calendar,
  Euro,
  Users,
  AlertCircle,
  CheckCircle2,
  Clock,
  Building2,
  TrendingUp,
  User,
  Receipt,
  CreditCard,
  Phone,
  Mail,
  MapPin,
} from "lucide-react";
import ApartmentSwitcher from "./ApartmentSwitcher";
import PaymentModal from "./PaymentModal";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

export default function ApartmentDashboard({
  sidebarOpen,
  setSidebarOpen,
  activeItem,
  setActiveItem,
}) {
  const [selectedApartment, setSelectedApartment] = useState(1);
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const queryClient = useQueryClient();

  // Get current month for payment status
  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

  // Fetch apartments
  const { data: apartments = [] } = useQuery({
    queryKey: ["apartments"],
    queryFn: async () => {
      const response = await fetch("/api/apartments");
      if (!response.ok) throw new Error("Failed to fetch apartments");
      return response.json();
    },
  });

  // Fetch dashboard data for selected apartment
  const { data: dashboardData, isLoading: isLoadingApartment } = useQuery({
    queryKey: ["dashboard", selectedApartment, currentMonth],
    queryFn: async () => {
      const response = await fetch(
        `/api/dashboard/${selectedApartment}?month=${currentMonth}`,
      );
      if (!response.ok) throw new Error("Failed to fetch dashboard data");
      return response.json();
    },
    enabled: !!selectedApartment,
  });

  // Get selected apartment info
  const selectedApartmentInfo = apartments.find(
    (apt) => apt.id === selectedApartment
  );

  const handlePaymentSuccess = () => {
    queryClient.invalidateQueries(["dashboard", selectedApartment]);
    setShowPaymentModal(false);
    setSelectedTenant(null);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case "paid":
        return "text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900";
      case "unpaid":
        return "text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900";
      default:
        return "text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900";
    }
  };

  const getBillStatusColor = (bill) => {
    if (bill.paid) {
      return "text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900";
    }
    if (new Date(bill.due_date) < new Date()) {
      return "text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900";
    }
    if (bill.ready_to_pay) {
      return "text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900";
    }
    return "text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900";
  };

  const getBillStatusText = (bill) => {
    if (bill.paid) return "Paid";
    if (new Date(bill.due_date) < new Date()) return "Overdue";
    if (bill.ready_to_pay) return "Ready to Pay";
    return "Pending";
  };

  return (
    <div className="flex-1 bg-gray-50 dark:bg-gray-900 h-full overflow-hidden">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-3 sm:px-4 lg:px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <Menu size={18} className="text-gray-600 dark:text-gray-400" />
            </button>

            <div className="flex items-center space-x-3">
              <Building2 size={24} className="text-blue-600 dark:text-blue-400" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Apartment Dashboard
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Tenant management and billing overview
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <ApartmentSwitcher
              apartments={apartments}
              selectedApartment={selectedApartment}
              onApartmentChange={setSelectedApartment}
            />
            <button className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center hover:bg-gray-200 dark:hover:bg-gray-600">
              <Search size={18} className="text-gray-600 dark:text-gray-400" />
            </button>
            <button className="relative w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center hover:bg-gray-200 dark:hover:bg-gray-600">
              <Bell size={18} className="text-gray-600 dark:text-gray-400" />
              {dashboardData?.overdue > 0 && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-3 sm:p-4 lg:p-6 max-w-full overflow-x-hidden h-full">
        <div className="space-y-6 sm:space-y-8 h-full">
          {/* Apartment Info & KPIs */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {selectedApartmentInfo?.name || "Loading..."}
                </h2>
                {selectedApartmentInfo?.address && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
                    <MapPin size={14} className="mr-1" />
                    {selectedApartmentInfo.address}
                  </p>
                )}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {new Date().toLocaleDateString("en-US", {
                  month: "long",
                  year: "numeric",
                })}
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(dashboardData?.rentDue)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Rent Due
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(dashboardData?.rentReceived)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Received
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(dashboardData?.billsDue)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Bills Due
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {dashboardData?.overdue || 0}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Overdue
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {dashboardData?.occupancy || 0}%
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Occupancy
                </div>
              </div>
            </div>
          </div>

          {/* Two-Column Layout: Tenants (Left) & Bills (Right) */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1">
            {/* Left Side: Tenant Status & Payments */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <Users size={20} className="mr-2 text-blue-600 dark:text-blue-400" />
                  Tenants & Payments
                </h3>
                <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                  <Plus size={20} />
                </button>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {dashboardData?.tenants?.map((tenant) => (
                  <div
                    key={tenant.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <User size={16} className="text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {tenant.first_name} {tenant.last_name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            Room {tenant.room_number}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-gray-900 dark:text-white">
                          {formatCurrency(tenant.monthly_rent)}
                        </div>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(
                            tenant.payment_status
                          )}`}
                        >
                          {tenant.payment_status === "paid" ? "Paid" : "Unpaid"}
                        </span>
                      </div>
                    </div>

                    {tenant.payment_status === "unpaid" && (
                      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                        <button
                          onClick={() => {
                            setSelectedTenant(tenant);
                            setShowPaymentModal(true);
                          }}
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                        >
                          <CreditCard size={16} className="mr-2" />
                          Record Payment
                        </button>
                      </div>
                    )}

                    {(tenant.email || tenant.phone) && (
                      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                        {tenant.email && (
                          <div className="flex items-center">
                            <Mail size={14} className="mr-1" />
                            {tenant.email}
                          </div>
                        )}
                        {tenant.phone && (
                          <div className="flex items-center">
                            <Phone size={14} className="mr-1" />
                            {tenant.phone}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}

                {(!dashboardData?.tenants || dashboardData.tenants.length === 0) && (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <Users size={48} className="mx-auto mb-4 opacity-50" />
                    <p>No tenants found for this apartment</p>
                  </div>
                )}
              </div>
            </div>

            {/* Right Side: Bills */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <Receipt size={20} className="mr-2 text-orange-600 dark:text-orange-400" />
                  Bills & Expenses
                </h3>
                <button className="text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300">
                  <Plus size={20} />
                </button>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {dashboardData?.bills?.map((bill) => (
                  <div
                    key={bill.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {bill.provider}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                          {bill.bill_type}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-gray-900 dark:text-white">
                          {formatCurrency(bill.amount)}
                        </div>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBillStatusColor(
                            bill
                          )}`}
                        >
                          {getBillStatusText(bill)}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1" />
                        Due: {formatDate(bill.due_date)}
                      </div>
                      <div>
                        {formatDate(bill.period_start)} - {formatDate(bill.period_end)}
                      </div>
                    </div>

                    {bill.pdf_url && (
                      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                        <a
                          href={bill.pdf_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm flex items-center"
                        >
                          <Receipt size={14} className="mr-1" />
                          View Bill PDF
                        </a>
                      </div>
                    )}
                  </div>
                ))}

                {(!dashboardData?.bills || dashboardData.bills.length === 0) && (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <Receipt size={48} className="mx-auto mb-4 opacity-50" />
                    <p>No bills found for this apartment</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && selectedTenant && (
        <PaymentModal
          tenant={selectedTenant}
          onClose={() => {
            setShowPaymentModal(false);
            setSelectedTenant(null);
          }}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </div>
  );
}
