import { useState } from "react";
import {
  Building2,
  Home,
  User,
  FileText,
  Receipt,
  CreditCard,
  Plus,
  X,
  Save,
  Menu,
} from "lucide-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export default function DataEntryForms({ sidebarOpen, setSidebarOpen }) {
  const [activeForm, setActiveForm] = useState("apartment");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();

  // Fetch apartments for dropdowns
  const { data: apartments = [] } = useQuery({
    queryKey: ["apartments"],
    queryFn: async () => {
      const response = await fetch("/api/apartments");
      if (!response.ok) throw new Error("Failed to fetch apartments");
      return response.json();
    },
  });

  // Fetch tenants for dropdowns
  const { data: tenants = [] } = useQuery({
    queryKey: ["tenants"],
    queryFn: async () => {
      const response = await fetch("/api/tenants");
      if (!response.ok) throw new Error("Failed to fetch tenants");
      return response.json();
    },
  });

  // Fetch rooms for dropdowns
  const { data: rooms = [] } = useQuery({
    queryKey: ["rooms"],
    queryFn: async () => {
      const response = await fetch("/api/rooms");
      if (!response.ok) throw new Error("Failed to fetch rooms");
      return response.json();
    },
  });

  const forms = [
    { id: "apartment", label: "Apartment", icon: Building2 },
    { id: "room", label: "Room", icon: Home },
    { id: "tenant", label: "Tenant", icon: User },
    { id: "lease", label: "Lease", icon: FileText },
    { id: "bill", label: "Bill", icon: Receipt },
    { id: "payment", label: "Payment", icon: CreditCard },
  ];

  const handleSubmit = async (formData, endpoint) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to submit form");
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries(["apartments"]);
      queryClient.invalidateQueries(["tenants"]);
      queryClient.invalidateQueries(["rooms"]);
      queryClient.invalidateQueries(["dashboard"]);

      alert("Data saved successfully!");
      return true;
    } catch (error) {
      alert(`Error: ${error.message}`);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  const ApartmentForm = () => {
    const [formData, setFormData] = useState({
      name: "",
      address: "",
      total_rooms: "",
    });

    const handleSubmitForm = async (e) => {
      e.preventDefault();
      const success = await handleSubmit(
        {
          ...formData,
          total_rooms: parseInt(formData.total_rooms) || 0,
        },
        "/api/apartments"
      );
      if (success) {
        setFormData({ name: "", address: "", total_rooms: "" });
      }
    };

    return (
      <form onSubmit={handleSubmitForm} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Apartment Name *
          </label>
          <input
            type="text"
            required
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="e.g., Sunset Apartments"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Address
          </label>
          <textarea
            value={formData.address}
            onChange={(e) =>
              setFormData({ ...formData, address: e.target.value })
            }
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Full address of the apartment building"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Total Rooms
          </label>
          <input
            type="number"
            min="0"
            value={formData.total_rooms}
            onChange={(e) =>
              setFormData({ ...formData, total_rooms: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Number of rooms in the apartment"
          />
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
        >
          <Save size={16} className="mr-2" />
          {isSubmitting ? "Saving..." : "Save Apartment"}
        </button>
      </form>
    );
  };

  const RoomForm = () => {
    const [formData, setFormData] = useState({
      apartment_id: "",
      room_number: "",
      room_type: "bedroom",
      monthly_rent: "",
    });

    const handleSubmitForm = async (e) => {
      e.preventDefault();
      if (!formData.apartment_id) {
        alert("Please select an apartment");
        return;
      }

      const success = await handleSubmit(
        {
          ...formData,
          monthly_rent: parseFloat(formData.monthly_rent) || null,
        },
        `/api/apartments/${formData.apartment_id}/rooms`
      );
      if (success) {
        setFormData({
          apartment_id: "",
          room_number: "",
          room_type: "bedroom",
          monthly_rent: "",
        });
      }
    };

    return (
      <form onSubmit={handleSubmitForm} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Apartment *
          </label>
          <select
            required
            value={formData.apartment_id}
            onChange={(e) =>
              setFormData({ ...formData, apartment_id: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">Select an apartment</option>
            {apartments.map((apt) => (
              <option key={apt.id} value={apt.id}>
                {apt.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Room Number *
          </label>
          <input
            type="text"
            required
            value={formData.room_number}
            onChange={(e) =>
              setFormData({ ...formData, room_number: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="e.g., A1, B2, 101"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Room Type
          </label>
          <select
            value={formData.room_type}
            onChange={(e) =>
              setFormData({ ...formData, room_type: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="bedroom">Bedroom</option>
            <option value="studio">Studio</option>
            <option value="suite">Suite</option>
            <option value="shared">Shared Room</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Monthly Rent (€)
          </label>
          <input
            type="number"
            step="0.01"
            min="0"
            value={formData.monthly_rent}
            onChange={(e) =>
              setFormData({ ...formData, monthly_rent: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="e.g., 800.00"
          />
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
        >
          <Save size={16} className="mr-2" />
          {isSubmitting ? "Saving..." : "Save Room"}
        </button>
      </form>
    );
  };

  const TenantForm = () => {
    const [formData, setFormData] = useState({
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      emergency_contact_name: "",
      emergency_contact_phone: "",
      date_of_birth: "",
    });

    const handleSubmitForm = async (e) => {
      e.preventDefault();
      const success = await handleSubmit(formData, "/api/tenants");
      if (success) {
        setFormData({
          first_name: "",
          last_name: "",
          email: "",
          phone: "",
          emergency_contact_name: "",
          emergency_contact_phone: "",
          date_of_birth: "",
        });
      }
    };

    return (
      <form onSubmit={handleSubmitForm} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              First Name *
            </label>
            <input
              type="text"
              required
              value={formData.first_name}
              onChange={(e) =>
                setFormData({ ...formData, first_name: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="John"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Last Name *
            </label>
            <input
              type="text"
              required
              value={formData.last_name}
              onChange={(e) =>
                setFormData({ ...formData, last_name: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Smith"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Email
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Phone
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) =>
              setFormData({ ...formData, phone: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="******-0123"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Date of Birth
          </label>
          <input
            type="date"
            value={formData.date_of_birth}
            onChange={(e) =>
              setFormData({ ...formData, date_of_birth: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Emergency Contact Name
            </label>
            <input
              type="text"
              value={formData.emergency_contact_name}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  emergency_contact_name: e.target.value,
                })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Jane Smith"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Emergency Contact Phone
            </label>
            <input
              type="tel"
              value={formData.emergency_contact_phone}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  emergency_contact_phone: e.target.value,
                })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="******-0124"
            />
          </div>
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
        >
          <Save size={16} className="mr-2" />
          {isSubmitting ? "Saving..." : "Save Tenant"}
        </button>
      </form>
    );
  };

  const renderActiveForm = () => {
    switch (activeForm) {
      case "apartment":
        return <ApartmentForm />;
      case "room":
        return <RoomForm />;
      case "tenant":
        return <TenantForm />;
      case "lease":
        return (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p>Lease form coming soon...</p>
          </div>
        );
      case "bill":
        return (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Receipt size={48} className="mx-auto mb-4 opacity-50" />
            <p>Bill form coming soon...</p>
          </div>
        );
      case "payment":
        return (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <CreditCard size={48} className="mx-auto mb-4 opacity-50" />
            <p>Payment form coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex-1 bg-gray-50 dark:bg-gray-900 h-full overflow-hidden">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-3 sm:px-4 lg:px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <Menu size={18} className="text-gray-600 dark:text-gray-400" />
            </button>

            <div className="flex items-center space-x-3">
              <Plus size={24} className="text-blue-600 dark:text-blue-400" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Data Entry
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Add new apartments, tenants, and manage your properties
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Form Navigation */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-3 sm:px-4 lg:px-6 py-3">
        <div className="flex space-x-1 overflow-x-auto">
          {forms.map((form) => {
            const Icon = form.icon;
            return (
              <button
                key={form.id}
                onClick={() => setActiveForm(form.id)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                  activeForm === form.id
                    ? "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300"
                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                <Icon size={16} className="mr-2" />
                {form.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Form Content */}
      <div className="p-3 sm:p-4 lg:p-6 max-w-2xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Add New {forms.find((f) => f.id === activeForm)?.label}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Fill in the details below to add a new{" "}
              {forms.find((f) => f.id === activeForm)?.label.toLowerCase()}.
            </p>
          </div>

          {renderActiveForm()}
        </div>
      </div>
    </div>
  );
}
