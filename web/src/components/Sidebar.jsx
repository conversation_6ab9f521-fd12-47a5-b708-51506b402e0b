import { useState } from "react";
import {
  Home,
  Building2,
  Plus,
  Users,
  FileText,
  Receipt,
  CreditCard,
  Calculator,
  Settings,
  LogOut,
} from "lucide-react";

export default function Sidebar({ activeItem, setActiveItem }) {
  const handleItemClick = (itemId) => {
    setActiveItem(itemId);
  };

  const MenuItem = ({ id, icon: Icon, label, isActive, onClick }) => (
    <button
      onClick={() => onClick(id)}
      className={`
        relative w-full flex items-center h-10 px-6 rounded-lg transition-all duration-150 ease-in-out
        hover:bg-gray-100 dark:hover:bg-gray-700
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-0
        ${isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"}
      `}
      aria-current={isActive ? "page" : undefined}
    >
      {isActive && (
        <div className="absolute left-0 top-0 w-1 h-full bg-blue-600 dark:bg-blue-400 rounded-r-full" />
      )}
      <Icon size={24} strokeWidth={1.5} className="flex-shrink-0" />
      <span className="ml-5 text-[15px] font-normal">{label}</span>
    </button>
  );

  const SectionLabel = ({ children }) => (
    <h3 className="text-[13px] font-light tracking-wide text-gray-500 dark:text-gray-400 mb-5 uppercase">
      {children}
    </h3>
  );

  return (
    <div className="w-64 bg-white dark:bg-gray-800 h-screen flex flex-col">
      {/* Brand Block */}
      <div className="px-6 py-8 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          {/* Simple Logo */}
          <div className="w-12 h-12 rounded-full bg-blue-600 dark:bg-blue-500 flex items-center justify-center flex-shrink-0">
            <span className="text-white text-xl font-bold">R</span>
          </div>

          {/* Wordmark */}
          <span className="ml-3 text-2xl font-semibold text-gray-900 dark:text-white">
            RentHub
          </span>
        </div>
      </div>

      {/* Navigation Sections */}
      <nav className="flex-1 px-6 py-8 space-y-8 overflow-y-auto">
        {/* Management Section */}
        <div>
          <SectionLabel>Management</SectionLabel>
          <div className="space-y-4">
            <MenuItem
              id="dashboard"
              icon={Home}
              label="Dashboard"
              isActive={activeItem === "dashboard"}
              onClick={handleItemClick}
            />
            <MenuItem
              id="apartment-dashboard"
              icon={Building2}
              label="Apartment View"
              isActive={activeItem === "apartment-dashboard"}
              onClick={handleItemClick}
            />
            <MenuItem
              id="data-entry"
              icon={Plus}
              label="Add Data"
              isActive={activeItem === "data-entry"}
              onClick={handleItemClick}
            />
            <MenuItem
              id="tenants"
              icon={Users}
              label="Tenants"
              isActive={activeItem === "tenants"}
              onClick={handleItemClick}
            />
            <MenuItem
              id="leases"
              icon={FileText}
              label="Leases"
              isActive={activeItem === "leases"}
              onClick={handleItemClick}
            />
            <MenuItem
              id="bills"
              icon={Receipt}
              label="Bills"
              isActive={activeItem === "bills"}
              onClick={handleItemClick}
            />
            <MenuItem
              id="payments"
              icon={CreditCard}
              label="Payments"
              isActive={activeItem === "payments"}
              onClick={handleItemClick}
            />
            <MenuItem
              id="taxes"
              icon={Calculator}
              label="Taxes"
              isActive={activeItem === "taxes"}
              onClick={handleItemClick}
            />
          </div>
        </div>

        {/* Divider */}
        <div className="my-7">
          <hr className="border-gray-200 dark:border-gray-700" />
        </div>

        {/* Tools Section */}
        <div>
          <SectionLabel>Tools</SectionLabel>
          <div className="space-y-4">
            <MenuItem
              id="settings"
              icon={Settings}
              label="Settings"
              isActive={activeItem === "settings"}
              onClick={handleItemClick}
            />
          </div>
        </div>
      </nav>

      {/* Log Out Row */}
      <div className="px-6 pb-8">
        <MenuItem
          id="logout"
          icon={LogOut}
          label="Log out"
          isActive={false}
          onClick={() => {
            console.log("Logging out...");
          }}
        />
      </div>
    </div>
  );
}