import { useState } from "react";
import Sidebar from "@/components/Sidebar";
import RentalDashboard from "@/components/RentalDashboard";
import ApartmentDashboard from "@/components/ApartmentDashboard";
import DataEntryForms from "@/components/DataEntryForms";
import TenantsView from "@/components/TenantsView";
import LeasesView from "@/components/LeasesView";
import BillsView from "@/components/BillsView";
import PaymentsView from "@/components/PaymentsView";
import TaxesView from "@/components/TaxesView";
import SettingsView from "@/components/SettingsView";
import { X } from "lucide-react";

export default function HomePage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeItem, setActiveItem] = useState("dashboard");

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* LEFT SIDEBAR - Navigation panel with RentHub branding */}
      {/* Desktop sidebar */}
      <div className="hidden lg:block h-screen">
        <Sidebar activeItem={activeItem} setActiveItem={setActiveItem} />
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div
            className="fixed inset-0 bg-black opacity-50 dark:opacity-70"
            onClick={() => setSidebarOpen(false)}
          />
          <div className="relative w-64 h-screen bg-white dark:bg-gray-800 shadow-xl">
            <div className="absolute top-4 right-4">
              <button
                onClick={() => setSidebarOpen(false)}
                className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
              >
                <X size={24} />
              </button>
            </div>
            <Sidebar activeItem={activeItem} setActiveItem={setActiveItem} />
          </div>
        </div>
      )}

      {/* MAIN CONTENT AREA */}
      <div className="flex-1 flex min-w-0 h-screen">
        {/* Conditional Content Based on Active Item */}
        {activeItem === "dashboard" && (
          <RentalDashboard
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
            activeItem={activeItem}
            setActiveItem={setActiveItem}
          />
        )}

        {activeItem === "apartment-dashboard" && (
          <ApartmentDashboard
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
            activeItem={activeItem}
            setActiveItem={setActiveItem}
          />
        )}

        {activeItem === "data-entry" && (
          <DataEntryForms
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
          />
        )}

        {activeItem === "tenants" && (
          <TenantsView
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
          />
        )}

        {activeItem === "leases" && (
          <LeasesView
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
          />
        )}

        {activeItem === "bills" && (
          <BillsView
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
          />
        )}

        {activeItem === "payments" && (
          <PaymentsView
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
          />
        )}

        {activeItem === "taxes" && (
          <TaxesView
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
          />
        )}

        {activeItem === "settings" && (
          <SettingsView
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
          />
        )}

        {![
          "dashboard",
          "apartment-dashboard",
          "data-entry",
          "tenants",
          "leases",
          "bills",
          "payments",
          "taxes",
          "settings",
        ].includes(activeItem) && (
          <div className="flex-1 bg-gray-50 dark:bg-gray-900 h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚧</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {activeItem.charAt(0).toUpperCase() + activeItem.slice(1)} -
                Coming Soon
              </h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md">
                This section is under development. For now, you can use the
                dashboard to manage rent collection and bills.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
