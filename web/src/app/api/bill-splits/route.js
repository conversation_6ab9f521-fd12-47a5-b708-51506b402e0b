import sql from "@/app/api/utils/sql";

export async function GET(request) {
  try {
    const url = new URL(request.url);
    const billId = url.searchParams.get('bill_id');
    const tenantId = url.searchParams.get('tenant_id');
    const apartmentId = url.searchParams.get('apartment_id');
    
    let whereConditions = [];
    let params = [];
    
    if (billId) {
      whereConditions.push('bs.bill_id = $' + (params.length + 1));
      params.push(billId);
    }
    
    if (tenantId) {
      whereConditions.push('bs.tenant_id = $' + (params.length + 1));
      params.push(tenantId);
    }
    
    if (apartmentId) {
      whereConditions.push('b.apartment_id = $' + (params.length + 1));
      params.push(apartmentId);
    }
    
    const whereClause = whereConditions.length > 0 ? 
      'WHERE ' + whereConditions.join(' AND ') : '';

    const billSplits = await sql(
      `SELECT 
        bs.*,
        t.first_name,
        t.last_name,
        t.email,
        b.provider,
        b.bill_type,
        b.due_date,
        b.period_start,
        b.period_end,
        a.name as apartment_name
      FROM bill_splits bs
      JOIN tenants t ON bs.tenant_id = t.id
      JOIN bills b ON bs.bill_id = b.id
      JOIN apartments a ON b.apartment_id = a.id
      ${whereClause}
      ORDER BY b.due_date DESC, t.last_name, t.first_name`,
      params
    );

    return Response.json(billSplits);
  } catch (error) {
    console.error('Error fetching bill splits:', error);
    return Response.json(
      { error: 'Failed to fetch bill splits' }, 
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { bill_id, tenant_id, amount } = body;

    // Validate required fields
    if (!bill_id || !tenant_id || !amount) {
      return Response.json(
        { error: 'bill_id, tenant_id, and amount are required' }, 
        { status: 400 }
      );
    }

    // Verify bill exists
    const [bill] = await sql`
      SELECT b.id, b.amount as total_amount, a.name as apartment_name
      FROM bills b
      JOIN apartments a ON b.apartment_id = a.id
      WHERE b.id = ${bill_id}
    `;

    if (!bill) {
      return Response.json(
        { error: 'Bill not found' }, 
        { status: 404 }
      );
    }

    // Verify tenant exists
    const [tenant] = await sql`
      SELECT id, first_name, last_name FROM tenants WHERE id = ${tenant_id}
    `;

    if (!tenant) {
      return Response.json(
        { error: 'Tenant not found' }, 
        { status: 404 }
      );
    }

    // Check if split already exists for this bill and tenant
    const [existingSplit] = await sql`
      SELECT id FROM bill_splits 
      WHERE bill_id = ${bill_id} AND tenant_id = ${tenant_id}
    `;

    if (existingSplit) {
      return Response.json(
        { error: 'Bill split already exists for this tenant' }, 
        { status: 400 }
      );
    }

    // Check if total splits would exceed bill amount
    const [totalSplits] = await sql`
      SELECT COALESCE(SUM(amount), 0) as total_split_amount
      FROM bill_splits 
      WHERE bill_id = ${bill_id}
    `;

    if (parseFloat(totalSplits.total_split_amount) + parseFloat(amount) > parseFloat(bill.total_amount)) {
      return Response.json(
        { error: 'Total bill splits cannot exceed bill amount' }, 
        { status: 400 }
      );
    }

    // Create the bill split
    const [billSplit] = await sql`
      INSERT INTO bill_splits (
        bill_id, 
        tenant_id, 
        amount,
        paid
      )
      VALUES (
        ${bill_id}, 
        ${tenant_id}, 
        ${amount},
        false
      )
      RETURNING *
    `;

    // Return bill split with additional info
    const billSplitWithInfo = {
      ...billSplit,
      tenant_name: `${tenant.first_name} ${tenant.last_name}`,
      apartment_name: bill.apartment_name
    };

    return Response.json(billSplitWithInfo, { status: 201 });
  } catch (error) {
    console.error('Error creating bill split:', error);
    return Response.json(
      { error: 'Failed to create bill split' }, 
      { status: 500 }
    );
  }
}

export async function PUT(request) {
  try {
    const body = await request.json();
    const { id, paid, paid_date } = body;

    if (!id) {
      return Response.json(
        { error: 'Bill split ID is required' }, 
        { status: 400 }
      );
    }

    // Build update fields dynamically
    let updateFields = [];
    let params = [];
    
    if (paid !== undefined) {
      updateFields.push('paid = $' + (params.length + 1));
      params.push(paid);
    }
    
    if (paid_date !== undefined) {
      updateFields.push('paid_date = $' + (params.length + 1));
      params.push(paid_date);
    }
    
    if (updateFields.length === 0) {
      return Response.json(
        { error: 'No fields to update' }, 
        { status: 400 }
      );
    }
    
    params.push(id);
    
    const [billSplit] = await sql(
      `UPDATE bill_splits 
       SET ${updateFields.join(', ')}
       WHERE id = $${params.length}
       RETURNING *`,
      params
    );

    if (!billSplit) {
      return Response.json(
        { error: 'Bill split not found' }, 
        { status: 404 }
      );
    }

    return Response.json(billSplit);
  } catch (error) {
    console.error('Error updating bill split:', error);
    return Response.json(
      { error: 'Failed to update bill split' }, 
      { status: 500 }
    );
  }
}
