import sql from "@/app/api/utils/sql";

export async function GET(request, { params }) {
  try {
    const apartmentId = params.id;

    const rooms = await sql`
      SELECT
        r.*,
        CASE
          WHEN l.id IS NOT NULL THEN
            CONCAT(t.first_name, ' ', t.last_name)
          ELSE NULL
        END as tenant_name,
        l.id as lease_id,
        l.status as lease_status,
        l.start_date,
        l.end_date
      FROM rooms r
      LEFT JOIN leases l ON r.id = l.room_id AND l.status = 'active'
      LEFT JOIN tenants t ON l.tenant_id = t.id
      WHERE r.apartment_id = ${apartmentId}
      ORDER BY r.room_number
    `;

    return Response.json(rooms);
  } catch (error) {
    console.error("Error fetching rooms:", error);
    return Response.json({ error: "Failed to fetch rooms" }, { status: 500 });
  }
}

export async function POST(request, { params }) {
  try {
    const apartmentId = params.id;
    const body = await request.json();
    const { room_number, room_type, monthly_rent } = body;

    if (!room_number) {
      return Response.json(
        { error: 'Room number is required' },
        { status: 400 }
      );
    }

    // Verify apartment exists
    const [apartment] = await sql`
      SELECT id, name FROM apartments WHERE id = ${apartmentId}
    `;

    if (!apartment) {
      return Response.json(
        { error: 'Apartment not found' },
        { status: 404 }
      );
    }

    // Check if room number already exists in this apartment
    const [existingRoom] = await sql`
      SELECT id FROM rooms
      WHERE apartment_id = ${apartmentId} AND room_number = ${room_number}
    `;

    if (existingRoom) {
      return Response.json(
        { error: 'Room number already exists in this apartment' },
        { status: 400 }
      );
    }

    // Create the room
    const [room] = await sql`
      INSERT INTO rooms (
        apartment_id,
        room_number,
        room_type,
        monthly_rent,
        is_available
      )
      VALUES (
        ${apartmentId},
        ${room_number},
        ${room_type || 'bedroom'},
        ${monthly_rent || null},
        true
      )
      RETURNING *
    `;

    return Response.json(room, { status: 201 });
  } catch (error) {
    console.error('Error creating room:', error);
    return Response.json(
      { error: 'Failed to create room' },
      { status: 500 }
    );
  }
}