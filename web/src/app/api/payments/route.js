import sql from "@/app/api/utils/sql";

export async function GET(request) {
  try {
    const url = new URL(request.url);
    const leaseId = url.searchParams.get('lease_id');
    const apartmentId = url.searchParams.get('apartment_id');
    
    let whereClause = '';
    let params = [];
    
    if (leaseId) {
      whereClause = 'WHERE p.lease_id = $1';
      params = [leaseId];
    } else if (apartmentId) {
      whereClause = 'WHERE r.apartment_id = $1';
      params = [apartmentId];
    }

    const payments = await sql(
      `SELECT 
        p.*,
        t.first_name,
        t.last_name,
        r.room_number,
        a.name as apartment_name
      FROM payments p
      JOIN leases l ON p.lease_id = l.id
      JOIN tenants t ON l.tenant_id = t.id
      JOIN rooms r ON l.room_id = r.id
      JOIN apartments a ON r.apartment_id = a.id
      ${whereClause}
      ORDER BY p.payment_date DESC`,
      params
    );

    return Response.json(payments);
  } catch (error) {
    console.error('Error fetching payments:', error);
    return Response.json(
      { error: 'Failed to fetch payments' }, 
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { lease_id, amount, payment_date, payment_month, payment_method, reference_number, notes } = body;

    // Validate required fields
    if (!lease_id || !amount || !payment_date || !payment_month) {
      return Response.json(
        { error: 'lease_id, amount, payment_date, and payment_month are required' }, 
        { status: 400 }
      );
    }

    // Verify lease exists and is active
    const [lease] = await sql`
      SELECT l.id, l.status, t.first_name, t.last_name, r.room_number
      FROM leases l
      JOIN tenants t ON l.tenant_id = t.id
      JOIN rooms r ON l.room_id = r.id
      WHERE l.id = ${lease_id}
    `;

    if (!lease) {
      return Response.json(
        { error: 'Lease not found' }, 
        { status: 404 }
      );
    }

    if (lease.status !== 'active') {
      return Response.json(
        { error: 'Cannot create payment for inactive lease' }, 
        { status: 400 }
      );
    }

    // Check if payment already exists for this lease and month
    const [existingPayment] = await sql`
      SELECT id FROM payments 
      WHERE lease_id = ${lease_id} 
      AND payment_month = ${payment_month}
    `;

    if (existingPayment) {
      return Response.json(
        { error: 'Payment already exists for this month' }, 
        { status: 400 }
      );
    }

    // Create the payment
    const [payment] = await sql`
      INSERT INTO payments (
        lease_id,
        amount,
        payment_date,
        payment_month,
        payment_method,
        reference_number,
        notes
      )
      VALUES (
        ${lease_id},
        ${amount},
        ${payment_date},
        ${payment_month},
        ${payment_method || 'bank_transfer'},
        ${reference_number},
        ${notes}
      )
      RETURNING *
    `;

    // Return payment with tenant info
    const paymentWithInfo = {
      ...payment,
      tenant_name: `${lease.first_name} ${lease.last_name}`,
      room_number: lease.room_number
    };

    return Response.json(paymentWithInfo, { status: 201 });
  } catch (error) {
    console.error('Error creating payment:', error);
    return Response.json(
      { error: 'Failed to create payment' }, 
      { status: 500 }
    );
  }
}