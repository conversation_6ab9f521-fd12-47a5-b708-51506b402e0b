# Tenant Management System - Usage Guide

This guide will walk you through setting up and using your new tenant management system.

## 🚀 Getting Started

### 1. Database Setup

First, set up your database with the provided schema:

```bash
# Set your DATABASE_URL environment variable
echo "DATABASE_URL=your_neon_database_url" > .env

# Run the database setup script
node setup-database.js --with-sample
```

This will create all necessary tables and load sample data for testing.

### 2. Start the Application

```bash
# Install dependencies (if not already done)
npm install

# Start the development server
npm run dev
```

Visit `http://localhost:3000` to access your tenant management dashboard.

## 📊 Dashboard Overview

Your application now has three main dashboard views:

### 1. **General Dashboard** (Home icon)
- Overview of all apartments
- Portfolio-wide KPIs
- Recent payments and pending bills across all properties

### 2. **Apartment View** (Building icon) - **NEW!**
- **Left Side**: Tenant statuses and payment information
  - List of all tenants in the selected apartment
  - Their room numbers and monthly rent
  - Payment status (paid/unpaid) for the current month
  - Quick payment recording buttons
  - Contact information (email/phone)

- **Right Side**: Bills associated with the apartment
  - Utility bills (electricity, water, gas, internet)
  - Due dates and payment status
  - Bill amounts and periods
  - Links to PDF bills (if available)

### 3. **Add Data** (Plus icon) - **NEW!**
- Manual data entry forms for:
  - Apartments
  - Rooms
  - Tenants
  - Leases (coming soon)
  - Bills (coming soon)
  - Payments (coming soon)

## 🏠 Managing Your Properties

### Adding a New Apartment

1. Click "Add Data" in the sidebar
2. Select "Apartment" tab
3. Fill in:
   - **Apartment Name** (required): e.g., "Sunset Apartments"
   - **Address**: Full address of the building
   - **Total Rooms**: Number of rooms in the apartment
4. Click "Save Apartment"

### Adding Rooms to an Apartment

1. Go to "Add Data" → "Room" tab
2. Fill in:
   - **Apartment**: Select from dropdown
   - **Room Number** (required): e.g., "A1", "B2", "101"
   - **Room Type**: Bedroom, Studio, Suite, or Shared Room
   - **Monthly Rent**: Rent amount in euros
3. Click "Save Room"

### Adding Tenants

1. Go to "Add Data" → "Tenant" tab
2. Fill in:
   - **First Name** and **Last Name** (required)
   - **Email** and **Phone** (optional but recommended)
   - **Date of Birth** (optional)
   - **Emergency Contact** information (optional)
3. Click "Save Tenant"

## 💰 Payment Management

### Recording Rent Payments

**Method 1: From Apartment View**
1. Go to "Apartment View" in the sidebar
2. Select the apartment from the dropdown
3. In the left panel, find the tenant with "Unpaid" status
4. Click "Record Payment" button
5. Fill in payment details and submit

**Method 2: Using API** (for bulk operations)
```bash
curl -X POST http://localhost:3000/api/payments \
  -H "Content-Type: application/json" \
  -d '{
    "lease_id": "lease-uuid",
    "amount": 800.00,
    "payment_date": "2024-08-15",
    "payment_month": "2024-08-01",
    "payment_method": "bank_transfer",
    "reference_number": "TXN123"
  }'
```

### Viewing Payment Status

In the **Apartment View**:
- **Green "Paid"** badge: Tenant has paid for the current month
- **Red "Unpaid"** badge: Payment is still pending
- **Payment amount** is displayed next to each tenant

## 🧾 Bill Management

### Adding Bills

Currently, bills can be added via API:

```bash
curl -X POST http://localhost:3000/api/bills \
  -H "Content-Type: application/json" \
  -d '{
    "apartment_id": "apartment-uuid",
    "provider": "City Electric",
    "bill_type": "electricity",
    "amount": 245.50,
    "due_date": "2024-08-15",
    "period_start": "2024-07-01",
    "period_end": "2024-07-31",
    "ready_to_pay": true,
    "paid": false
  }'
```

### Viewing Bills

In the **Apartment View**, the right panel shows:
- **Bill provider** and type (electricity, water, gas, etc.)
- **Amount** and **due date**
- **Status badges**:
  - **Green "Paid"**: Bill has been paid
  - **Red "Overdue"**: Bill is past due date
  - **Blue "Ready to Pay"**: Bill is ready for payment
  - **Yellow "Pending"**: Bill is not yet ready for payment
- **Period covered** by the bill
- **PDF links** (if available)

## 🔄 Data Flow

### Typical Workflow

1. **Setup Properties**:
   - Add apartments using "Add Data" → "Apartment"
   - Add rooms using "Add Data" → "Room"

2. **Add Tenants**:
   - Create tenant profiles using "Add Data" → "Tenant"
   - Create leases (via API for now) to assign tenants to rooms

3. **Monthly Operations**:
   - View "Apartment View" to see payment statuses
   - Record payments as they come in
   - Add bills as they arrive
   - Monitor overdue payments and bills

4. **Monitoring**:
   - Use "General Dashboard" for portfolio overview
   - Use "Apartment View" for detailed property management

## 🛠️ API Endpoints

Your system provides these API endpoints:

### Apartments
- `GET /api/apartments` - List all apartments
- `POST /api/apartments` - Create new apartment
- `GET /api/apartments/{id}/rooms` - Get rooms in apartment

### Tenants
- `GET /api/tenants` - List all tenants with lease info
- `POST /api/tenants` - Create new tenant

### Bills
- `GET /api/bills?apartment_id={id}` - Get bills for apartment
- `POST /api/bills` - Create new bill
- `PUT /api/bills` - Update bill status

### Payments
- `GET /api/payments?apartment_id={id}` - Get payments for apartment
- `POST /api/payments` - Record new payment

### Dashboard Data
- `GET /api/dashboard/{apartmentId}?month=YYYY-MM` - Apartment dashboard data
- `GET /api/dashboard/general?month=YYYY-MM` - General dashboard data

## 🎯 Best Practices

### Data Entry
1. **Start with apartments** before adding rooms
2. **Add tenants** before creating leases
3. **Use consistent naming** for room numbers (e.g., A1, A2, B1, B2)
4. **Include contact information** for tenants for better communication

### Payment Tracking
1. **Record payments promptly** to maintain accurate status
2. **Use reference numbers** for bank transfers and checks
3. **Check payment status** regularly in Apartment View

### Bill Management
1. **Add bills as soon as received** to track due dates
2. **Mark bills as "ready to pay"** when processed
3. **Update payment status** when bills are paid

## 🔧 Troubleshooting

### Common Issues

1. **"Failed to fetch" errors**: Check that your development server is running
2. **Database connection errors**: Verify your `DATABASE_URL` environment variable
3. **Empty dropdowns**: Ensure you've added apartments before trying to add rooms

### Getting Help

1. Check the browser console for error messages
2. Verify API responses using browser developer tools
3. Check the database setup if data isn't appearing

## 🚀 Next Steps

Your system is now ready for:
1. **Manual data entry** through the web interface
2. **Real-time dashboard monitoring** of tenant payments and bills
3. **API integration** for automated data import (future enhancement)

The foundation is solid and ready for your tenant management needs!
