#!/usr/bin/env node

/**
 * Database Setup Script for Tenant Management System
 * 
 * This script sets up the complete database schema and optionally loads sample data.
 * 
 * Usage:
 *   node setup-database.js --schema-only    # Only create tables and schema
 *   node setup-database.js --with-sample    # Create schema and load sample data
 *   node setup-database.js --sample-only    # Only load sample data (assumes schema exists)
 */

import { readFile } from 'fs/promises';
import { neon } from '@neondatabase/serverless';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get command line arguments
const args = process.argv.slice(2);
const schemaOnly = args.includes('--schema-only');
const withSample = args.includes('--with-sample');
const sampleOnly = args.includes('--sample-only');

// Validate arguments
if (!schemaOnly && !withSample && !sampleOnly) {
  console.log('Usage:');
  console.log('  node setup-database.js --schema-only    # Only create tables and schema');
  console.log('  node setup-database.js --with-sample    # Create schema and load sample data');
  console.log('  node setup-database.js --sample-only    # Only load sample data (assumes schema exists)');
  process.exit(1);
}

// Check for DATABASE_URL
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  console.error('Please set your Neon database connection string in the DATABASE_URL environment variable');
  process.exit(1);
}

const sql = neon(process.env.DATABASE_URL);

async function runSchema() {
  try {
    console.log('📋 Reading database schema...');
    const schemaSQL = await readFile(join(__dirname, 'database-schema.sql'), 'utf8');
    
    console.log('🔧 Creating database schema...');
    await sql(schemaSQL);
    
    console.log('✅ Database schema created successfully!');
  } catch (error) {
    console.error('❌ Error creating database schema:', error.message);
    throw error;
  }
}

async function runSampleData() {
  try {
    console.log('📋 Reading sample data...');
    const sampleSQL = await readFile(join(__dirname, 'sample-data.sql'), 'utf8');
    
    console.log('📊 Loading sample data...');
    await sql(sampleSQL);
    
    console.log('✅ Sample data loaded successfully!');
  } catch (error) {
    console.error('❌ Error loading sample data:', error.message);
    throw error;
  }
}

async function verifySetup() {
  try {
    console.log('🔍 Verifying database setup...');
    
    // Check if tables exist and have data
    const apartmentCount = await sql`SELECT COUNT(*) as count FROM apartments`;
    const tenantCount = await sql`SELECT COUNT(*) as count FROM tenants`;
    const roomCount = await sql`SELECT COUNT(*) as count FROM rooms`;
    const leaseCount = await sql`SELECT COUNT(*) as count FROM leases`;
    const billCount = await sql`SELECT COUNT(*) as count FROM bills`;
    const paymentCount = await sql`SELECT COUNT(*) as count FROM payments`;
    
    console.log('📊 Database Statistics:');
    console.log(`   Apartments: ${apartmentCount[0].count}`);
    console.log(`   Rooms: ${roomCount[0].count}`);
    console.log(`   Tenants: ${tenantCount[0].count}`);
    console.log(`   Leases: ${leaseCount[0].count}`);
    console.log(`   Bills: ${billCount[0].count}`);
    console.log(`   Payments: ${paymentCount[0].count}`);
    
  } catch (error) {
    console.error('❌ Error verifying setup:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting database setup...');
    console.log(`📡 Connecting to database: ${process.env.DATABASE_URL.split('@')[1]?.split('/')[0] || 'hidden'}`);
    
    if (schemaOnly || withSample) {
      await runSchema();
    }
    
    if (sampleOnly || withSample) {
      await runSampleData();
    }
    
    await verifySetup();
    
    console.log('🎉 Database setup completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Visit your application to see the tenant management dashboard');
    console.log('3. Use the API endpoints to manage apartments, tenants, and bills');
    
  } catch (error) {
    console.error('💥 Database setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
main();
