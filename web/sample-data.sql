-- Sample data for testing the tenant management system
-- Run this after setting up the database schema

-- Insert sample apartments
INSERT INTO apartments (id, name, address, total_rooms) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Sunset Apartments', '123 Main Street, Downtown', 4),
('550e8400-e29b-41d4-a716-446655440002', 'Ocean View Complex', '456 Beach Avenue, Seaside', 6),
('550e8400-e29b-41d4-a716-************', 'Mountain Ridge', '789 Hill Road, Uptown', 3);

-- Insert sample rooms
INSERT INTO rooms (id, apartment_id, room_number, room_type, monthly_rent, is_available) VALUES
-- Sunset Apartments rooms
('550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440001', 'A1', 'bedroom', 800.00, false),
('550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440001', 'A2', 'bedroom', 850.00, false),
('550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-446655440001', 'A3', 'studio', 750.00, true),
('550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-446655440001', 'A4', 'bedroom', 900.00, false),

-- Ocean View Complex rooms
('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440002', 'B1', 'bedroom', 1200.00, false),
('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440002', 'B2', 'bedroom', 1150.00, false),
('550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440002', 'B3', 'studio', 1000.00, true),
('550e8400-e29b-41d4-a716-446655440024', '550e8400-e29b-41d4-a716-446655440002', 'B4', 'bedroom', 1300.00, false),
('550e8400-e29b-41d4-a716-446655440025', '550e8400-e29b-41d4-a716-446655440002', 'B5', 'bedroom', 1250.00, true),
('550e8400-e29b-41d4-a716-446655440026', '550e8400-e29b-41d4-a716-446655440002', 'B6', 'bedroom', 1100.00, false),

-- Mountain Ridge rooms
('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-************', 'C1', 'bedroom', 950.00, false),
('550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-************', 'C2', 'bedroom', 1000.00, false),
('550e8400-e29b-41d4-a716-446655440033', '550e8400-e29b-41d4-a716-************', 'C3', 'studio', 900.00, true);

-- Insert sample tenants
INSERT INTO tenants (id, first_name, last_name, email, phone, emergency_contact_name, emergency_contact_phone) VALUES
('550e8400-e29b-41d4-a716-446655440101', 'John', 'Smith', '<EMAIL>', '******-0101', 'Jane Smith', '******-0102'),
('550e8400-e29b-41d4-a716-446655440102', 'Maria', 'Garcia', '<EMAIL>', '******-0201', 'Carlos Garcia', '******-0202'),
('550e8400-e29b-41d4-a716-446655440103', 'David', 'Johnson', '<EMAIL>', '******-0301', 'Sarah Johnson', '******-0302'),
('550e8400-e29b-41d4-a716-446655440104', 'Lisa', 'Chen', '<EMAIL>', '******-0401', 'Michael Chen', '******-0402'),
('550e8400-e29b-41d4-a716-446655440105', 'Robert', 'Williams', '<EMAIL>', '******-0501', 'Emma Williams', '******-0502'),
('550e8400-e29b-41d4-a716-446655440106', 'Emily', 'Brown', '<EMAIL>', '******-0601', 'James Brown', '******-0602'),
('550e8400-e29b-41d4-a716-446655440107', 'Michael', 'Davis', '<EMAIL>', '******-0701', 'Anna Davis', '******-0702');

-- Insert sample leases (active leases for occupied rooms)
INSERT INTO leases (id, tenant_id, room_id, start_date, end_date, monthly_rent, security_deposit, status) VALUES
-- Sunset Apartments leases
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440101', '550e8400-e29b-41d4-a716-446655440011', '2024-01-01', '2024-12-31', 800.00, 1600.00, 'active'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440012', '2024-02-01', '2025-01-31', 850.00, 1700.00, 'active'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440014', '2024-03-01', '2025-02-28', 900.00, 1800.00, 'active'),

-- Ocean View Complex leases
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440104', '550e8400-e29b-41d4-a716-446655440021', '2024-01-15', '2025-01-14', 1200.00, 2400.00, 'active'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-446655440022', '2024-04-01', '2025-03-31', 1150.00, 2300.00, 'active'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440106', '550e8400-e29b-41d4-a716-446655440024', '2024-05-01', '2025-04-30', 1300.00, 2600.00, 'active'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440107', '550e8400-e29b-41d4-a716-446655440026', '2024-06-01', '2025-05-31', 1100.00, 2200.00, 'active'),

-- Mountain Ridge leases
('550e8400-e29b-41d4-a716-446655440208', '550e8400-e29b-41d4-a716-446655440101', '550e8400-e29b-41d4-a716-446655440031', '2024-07-01', '2025-06-30', 950.00, 1900.00, 'active'),
('550e8400-e29b-41d4-a716-446655440209', '550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440032', '2024-08-01', '2025-07-31', 1000.00, 2000.00, 'active');

-- Insert sample bills for apartments
INSERT INTO bills (id, apartment_id, provider, bill_type, amount, due_date, period_start, period_end, ready_to_pay, paid) VALUES
-- Sunset Apartments bills
('550e8400-e29b-41d4-a716-446655440301', '550e8400-e29b-41d4-a716-446655440001', 'City Electric', 'electricity', 245.50, '2024-08-15', '2024-07-01', '2024-07-31', true, false),
('550e8400-e29b-41d4-a716-446655440302', '550e8400-e29b-41d4-a716-446655440001', 'Metro Water', 'water', 89.25, '2024-08-20', '2024-07-01', '2024-07-31', true, true),
('550e8400-e29b-41d4-a716-446655440303', '550e8400-e29b-41d4-a716-446655440001', 'FastNet Internet', 'internet', 79.99, '2024-08-10', '2024-08-01', '2024-08-31', false, false),

-- Ocean View Complex bills
('550e8400-e29b-41d4-a716-446655440304', '550e8400-e29b-41d4-a716-446655440002', 'City Electric', 'electricity', 387.75, '2024-08-15', '2024-07-01', '2024-07-31', true, false),
('550e8400-e29b-41d4-a716-446655440305', '550e8400-e29b-41d4-a716-446655440002', 'Metro Water', 'water', 156.80, '2024-08-20', '2024-07-01', '2024-07-31', true, false),
('550e8400-e29b-41d4-a716-446655440306', '550e8400-e29b-41d4-a716-446655440002', 'Ocean Gas', 'gas', 67.45, '2024-08-25', '2024-07-01', '2024-07-31', false, false),

-- Mountain Ridge bills
('550e8400-e29b-41d4-a716-446655440307', '550e8400-e29b-41d4-a716-************', 'City Electric', 'electricity', 198.30, '2024-08-15', '2024-07-01', '2024-07-31', true, true),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Metro Water', 'water', 72.15, '2024-08-20', '2024-07-01', '2024-07-31', true, false);

-- Insert sample payments (some tenants have paid, others haven't)
INSERT INTO payments (id, lease_id, amount, payment_date, payment_month, payment_method, reference_number) VALUES
-- August 2024 payments (some paid, some not)
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 800.00, '2024-08-01', '2024-08-01', 'bank_transfer', 'TXN001'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 850.00, '2024-08-03', '2024-08-01', 'cash', 'CASH001'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1200.00, '2024-08-05', '2024-08-01', 'bank_transfer', 'TXN002'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1300.00, '2024-08-02', '2024-08-01', 'check', 'CHK001'),

-- July 2024 payments (all paid)
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 800.00, '2024-07-01', '2024-07-01', 'bank_transfer', 'TXN003'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 850.00, '2024-07-02', '2024-07-01', 'cash', 'CASH002'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 900.00, '2024-07-01', '2024-07-01', 'bank_transfer', 'TXN004'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1200.00, '2024-07-03', '2024-07-01', 'bank_transfer', 'TXN005'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1150.00, '2024-07-01', '2024-07-01', 'cash', 'CASH003'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1300.00, '2024-07-02', '2024-07-01', 'check', 'CHK002'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1100.00, '2024-07-01', '2024-07-01', 'bank_transfer', 'TXN006');
