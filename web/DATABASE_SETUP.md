# Tenant Management System - Database Setup Guide

This guide will help you set up the database for your tenant management system.

## Prerequisites

1. **Neon Database Account**: You need a Neon PostgreSQL database
2. **Environment Variables**: Set up your `DATABASE_URL` environment variable

## Quick Setup

### 1. Set Environment Variable

Create a `.env` file in the `web` directory with your Neon database connection string:

```bash
DATABASE_URL="****************************************************************"
```

### 2. Run Database Setup

Choose one of the following options:

```bash
# Option 1: Create schema and load sample data (recommended for development)
node setup-database.js --with-sample

# Option 2: Only create the database schema (for production)
node setup-database.js --schema-only

# Option 3: Only load sample data (if schema already exists)
node setup-database.js --sample-only
```

## Database Schema Overview

The system includes the following main tables:

### Core Tables

1. **apartments** - Rental properties
   - `id`, `name`, `address`, `total_rooms`

2. **rooms** - Individual rooms within apartments
   - `id`, `apartment_id`, `room_number`, `room_type`, `monthly_rent`, `is_available`

3. **tenants** - People who rent rooms
   - `id`, `first_name`, `last_name`, `email`, `phone`, `emergency_contact_*`

4. **leases** - Rental agreements between tenants and rooms
   - `id`, `tenant_id`, `room_id`, `start_date`, `end_date`, `monthly_rent`, `status`

5. **bills** - Utility bills and expenses for apartments
   - `id`, `apartment_id`, `provider`, `bill_type`, `amount`, `due_date`, `paid`

6. **payments** - Rent payments made by tenants
   - `id`, `lease_id`, `amount`, `payment_date`, `payment_month`

7. **bill_splits** - How bills are divided among tenants
   - `id`, `bill_id`, `tenant_id`, `amount`, `paid`

### Key Relationships

- Apartments contain multiple Rooms
- Tenants have Leases for specific Rooms
- Bills are associated with Apartments
- Payments are linked to Leases
- Bill Splits divide Bills among Tenants

## Sample Data

The sample data includes:

- **3 Apartments**: Sunset Apartments, Ocean View Complex, Mountain Ridge
- **13 Rooms**: Various room types (bedroom, studio) with different rents
- **7 Tenants**: With contact information and emergency contacts
- **9 Active Leases**: Current rental agreements
- **8 Bills**: Utility bills (electricity, water, gas, internet)
- **11 Payments**: Some current month payments and all previous month payments

## API Endpoints

Your existing API endpoints should work with this schema:

- `GET /api/apartments` - List all apartments
- `GET /api/tenants` - List all tenants with lease info
- `GET /api/bills` - List bills (can filter by apartment)
- `GET /api/dashboard/[apartmentId]` - Get dashboard data for specific apartment
- `POST /api/*` - Create new records

## Manual Data Entry

For manual data entry, you can:

1. **Use the API endpoints** directly with tools like Postman or curl
2. **Create forms in your React app** (recommended)
3. **Use SQL INSERT statements** directly in your database

### Example API Calls

```bash
# Add a new apartment
curl -X POST http://localhost:3000/api/apartments \
  -H "Content-Type: application/json" \
  -d '{"name": "New Building", "address": "123 New St", "total_rooms": 5}'

# Add a new tenant
curl -X POST http://localhost:3000/api/tenants \
  -H "Content-Type: application/json" \
  -d '{"first_name": "Jane", "last_name": "Doe", "email": "<EMAIL>", "phone": "******-1234"}'
```

## Dashboard Layout

Your dashboard will show:

**Left Side**: Tenant statuses and payment information
- List of tenants in the apartment
- Their room numbers
- Payment status (paid/unpaid)
- Amount owed

**Right Side**: Bills associated with the apartment
- Utility bills (electricity, water, gas, etc.)
- Due dates
- Payment status
- Amounts

## Troubleshooting

### Common Issues

1. **Connection Error**: Check your `DATABASE_URL` environment variable
2. **Permission Error**: Ensure your database user has CREATE privileges
3. **Table Already Exists**: Use `--sample-only` if schema is already created

### Reset Database

To start fresh:

```sql
-- Connect to your database and run:
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
```

Then run the setup script again.

## Next Steps

1. Start your development server: `npm run dev`
2. Visit your application to see the dashboard
3. Test the API endpoints
4. Create forms for manual data entry
5. Customize the dashboard layout to match your preferences
