html, body, #__next {
  -webkit-overflow-scrolling: touch;
}
#__next {
  display: flex;
  flex-direction: column;
  height: 100%;
}
html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}
body {
  /* Allows you to scroll below the viewport; default value is visible */
  overflow-y: auto;
  overscroll-behavior-y: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -ms-overflow-style: scrollbar;
}
input:focus,
textarea:focus,
button:focus,
select:focus {
  outline: none;        /* removes the glow */
  box-shadow: none;     /* Chrome fallback */
}

/* Firefox also adds padding on some controls */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
}
