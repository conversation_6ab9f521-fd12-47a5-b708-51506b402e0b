{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.VERTICAL = exports.HORIZONTAL = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var ScrollViewContext = /*#__PURE__*/React.createContext(null);\n  if (__DEV__) {\n    ScrollViewContext.displayName = 'ScrollViewContext';\n  }\n  var _default = exports.default = ScrollViewContext;\n  var HORIZONTAL = exports.HORIZONTAL = Object.freeze({\n    horizontal: true\n  });\n  var VERTICAL = exports.VERTICAL = Object.freeze({\n    horizontal: false\n  });\n});", "lineCount": 19, "map": [[6, 2, 11, 0], [6, 6, 11, 0, "React"], [6, 11, 11, 0], [6, 14, 11, 0, "_interopRequireWildcard"], [6, 37, 11, 0], [6, 38, 11, 0, "require"], [6, 45, 11, 0], [6, 46, 11, 0, "_dependencyMap"], [6, 60, 11, 0], [7, 2, 11, 31], [7, 11, 11, 31, "_interopRequireWildcard"], [7, 35, 11, 31, "e"], [7, 36, 11, 31], [7, 38, 11, 31, "t"], [7, 39, 11, 31], [7, 68, 11, 31, "WeakMap"], [7, 75, 11, 31], [7, 81, 11, 31, "r"], [7, 82, 11, 31], [7, 89, 11, 31, "WeakMap"], [7, 96, 11, 31], [7, 100, 11, 31, "n"], [7, 101, 11, 31], [7, 108, 11, 31, "WeakMap"], [7, 115, 11, 31], [7, 127, 11, 31, "_interopRequireWildcard"], [7, 150, 11, 31], [7, 162, 11, 31, "_interopRequireWildcard"], [7, 163, 11, 31, "e"], [7, 164, 11, 31], [7, 166, 11, 31, "t"], [7, 167, 11, 31], [7, 176, 11, 31, "t"], [7, 177, 11, 31], [7, 181, 11, 31, "e"], [7, 182, 11, 31], [7, 186, 11, 31, "e"], [7, 187, 11, 31], [7, 188, 11, 31, "__esModule"], [7, 198, 11, 31], [7, 207, 11, 31, "e"], [7, 208, 11, 31], [7, 214, 11, 31, "o"], [7, 215, 11, 31], [7, 217, 11, 31, "i"], [7, 218, 11, 31], [7, 220, 11, 31, "f"], [7, 221, 11, 31], [7, 226, 11, 31, "__proto__"], [7, 235, 11, 31], [7, 243, 11, 31, "default"], [7, 250, 11, 31], [7, 252, 11, 31, "e"], [7, 253, 11, 31], [7, 270, 11, 31, "e"], [7, 271, 11, 31], [7, 294, 11, 31, "e"], [7, 295, 11, 31], [7, 320, 11, 31, "e"], [7, 321, 11, 31], [7, 330, 11, 31, "f"], [7, 331, 11, 31], [7, 337, 11, 31, "o"], [7, 338, 11, 31], [7, 341, 11, 31, "t"], [7, 342, 11, 31], [7, 345, 11, 31, "n"], [7, 346, 11, 31], [7, 349, 11, 31, "r"], [7, 350, 11, 31], [7, 358, 11, 31, "o"], [7, 359, 11, 31], [7, 360, 11, 31, "has"], [7, 363, 11, 31], [7, 364, 11, 31, "e"], [7, 365, 11, 31], [7, 375, 11, 31, "o"], [7, 376, 11, 31], [7, 377, 11, 31, "get"], [7, 380, 11, 31], [7, 381, 11, 31, "e"], [7, 382, 11, 31], [7, 385, 11, 31, "o"], [7, 386, 11, 31], [7, 387, 11, 31, "set"], [7, 390, 11, 31], [7, 391, 11, 31, "e"], [7, 392, 11, 31], [7, 394, 11, 31, "f"], [7, 395, 11, 31], [7, 409, 11, 31, "_t"], [7, 411, 11, 31], [7, 415, 11, 31, "e"], [7, 416, 11, 31], [7, 432, 11, 31, "_t"], [7, 434, 11, 31], [7, 441, 11, 31, "hasOwnProperty"], [7, 455, 11, 31], [7, 456, 11, 31, "call"], [7, 460, 11, 31], [7, 461, 11, 31, "e"], [7, 462, 11, 31], [7, 464, 11, 31, "_t"], [7, 466, 11, 31], [7, 473, 11, 31, "i"], [7, 474, 11, 31], [7, 478, 11, 31, "o"], [7, 479, 11, 31], [7, 482, 11, 31, "Object"], [7, 488, 11, 31], [7, 489, 11, 31, "defineProperty"], [7, 503, 11, 31], [7, 508, 11, 31, "Object"], [7, 514, 11, 31], [7, 515, 11, 31, "getOwnPropertyDescriptor"], [7, 539, 11, 31], [7, 540, 11, 31, "e"], [7, 541, 11, 31], [7, 543, 11, 31, "_t"], [7, 545, 11, 31], [7, 552, 11, 31, "i"], [7, 553, 11, 31], [7, 554, 11, 31, "get"], [7, 557, 11, 31], [7, 561, 11, 31, "i"], [7, 562, 11, 31], [7, 563, 11, 31, "set"], [7, 566, 11, 31], [7, 570, 11, 31, "o"], [7, 571, 11, 31], [7, 572, 11, 31, "f"], [7, 573, 11, 31], [7, 575, 11, 31, "_t"], [7, 577, 11, 31], [7, 579, 11, 31, "i"], [7, 580, 11, 31], [7, 584, 11, 31, "f"], [7, 585, 11, 31], [7, 586, 11, 31, "_t"], [7, 588, 11, 31], [7, 592, 11, 31, "e"], [7, 593, 11, 31], [7, 594, 11, 31, "_t"], [7, 596, 11, 31], [7, 607, 11, 31, "f"], [7, 608, 11, 31], [7, 613, 11, 31, "e"], [7, 614, 11, 31], [7, 616, 11, 31, "t"], [7, 617, 11, 31], [8, 2, 15, 0], [8, 6, 15, 6, "ScrollViewContext"], [8, 23, 15, 45], [8, 39, 15, 48, "React"], [8, 44, 15, 53], [8, 45, 15, 54, "createContext"], [8, 58, 15, 67], [8, 59, 15, 68], [8, 63, 15, 72], [8, 64, 15, 73], [9, 2, 16, 0], [9, 6, 16, 4, "__DEV__"], [9, 13, 16, 11], [9, 15, 16, 13], [10, 4, 17, 2, "ScrollViewContext"], [10, 21, 17, 19], [10, 22, 17, 20, "displayName"], [10, 33, 17, 31], [10, 36, 17, 34], [10, 55, 17, 53], [11, 2, 18, 0], [12, 2, 18, 1], [12, 6, 18, 1, "_default"], [12, 14, 18, 1], [12, 17, 18, 1, "exports"], [12, 24, 18, 1], [12, 25, 18, 1, "default"], [12, 32, 18, 1], [12, 35, 19, 15, "ScrollViewContext"], [12, 52, 19, 32], [13, 2, 22, 7], [13, 6, 22, 13, "HORIZONTAL"], [13, 16, 22, 30], [13, 19, 22, 30, "exports"], [13, 26, 22, 30], [13, 27, 22, 30, "HORIZONTAL"], [13, 37, 22, 30], [13, 40, 22, 33, "Object"], [13, 46, 22, 39], [13, 47, 22, 40, "freeze"], [13, 53, 22, 46], [13, 54, 22, 47], [14, 4, 22, 48, "horizontal"], [14, 14, 22, 58], [14, 16, 22, 60], [15, 2, 22, 64], [15, 3, 22, 65], [15, 4, 22, 66], [16, 2, 24, 7], [16, 6, 24, 13, "VERTICAL"], [16, 14, 24, 28], [16, 17, 24, 28, "exports"], [16, 24, 24, 28], [16, 25, 24, 28, "VERTICAL"], [16, 33, 24, 28], [16, 36, 24, 31, "Object"], [16, 42, 24, 37], [16, 43, 24, 38, "freeze"], [16, 49, 24, 44], [16, 50, 24, 45], [17, 4, 24, 46, "horizontal"], [17, 14, 24, 56], [17, 16, 24, 58], [18, 2, 24, 63], [18, 3, 24, 64], [18, 4, 24, 65], [19, 0, 24, 66], [19, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}