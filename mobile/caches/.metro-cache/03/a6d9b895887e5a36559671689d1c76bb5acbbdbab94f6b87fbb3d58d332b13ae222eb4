{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectSpread2", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 65, "index": 65}}], "key": "SfRhzMj3Ex6qA89WTFEUm9Lj49A=", "exportNames": ["*"]}}, {"name": "./NativeAnimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 276}, "end": {"line": 12, "column": 66, "index": 342}}], "key": "Gpw0Vp0/8UXqvSPp8bAyn6KzWD0=", "exportNames": ["*"]}}, {"name": "./NativeAnimatedTurboModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 343}, "end": {"line": 13, "column": 68, "index": 411}}], "key": "fsZk/4GgfTJHQ+RN6FK5yY+fn+A=", "exportNames": ["*"]}}, {"name": "../EventEmitter/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 412}, "end": {"line": 14, "column": 68, "index": 480}}], "key": "Jk6GODPsD+OS/XTex7hK2MSfvW0=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 481}, "end": {"line": 15, "column": 45, "index": 526}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}, {"name": "../ReactNative/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 527}, "end": {"line": 16, "column": 77, "index": 604}}], "key": "jeGCs62wKc88qqna7jEhszykhK4=", "exportNames": ["*"]}}, {"name": "fbjs/lib/invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 605}, "end": {"line": 17, "column": 43, "index": 648}}], "key": "bGUa+dDG2WEhPiIlobT3urS95UE=", "exportNames": ["*"]}}, {"name": "../EventEmitter/RCTDeviceEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 649}, "end": {"line": 18, "column": 74, "index": 723}}], "key": "XoPAg1BdnOZCXdEAjKNXTGpZCQ4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.API = void 0;\n  exports.addWhitelistedInterpolationParam = addWhitelistedInterpolationParam;\n  exports.addWhitelistedStyleProp = addWhitelistedStyleProp;\n  exports.addWhitelistedTransformProp = addWhitelistedTransformProp;\n  exports.assertNativeAnimatedModule = assertNativeAnimatedModule;\n  exports.default = void 0;\n  exports.generateNewAnimationId = generateNewAnimationId;\n  exports.generateNewNodeTag = generateNewNodeTag;\n  exports.isSupportedColorStyleProp = isSupportedColorStyleProp;\n  exports.isSupportedInterpolationParam = isSupportedInterpolationParam;\n  exports.isSupportedStyleProp = isSupportedStyleProp;\n  exports.isSupportedTransformProp = isSupportedTransformProp;\n  exports.shouldUseNativeDriver = shouldUseNativeDriver;\n  exports.transformDataType = transformDataType;\n  exports.validateInterpolation = validateInterpolation;\n  exports.validateStyles = validateStyles;\n  exports.validateTransform = validateTransform;\n  var _objectSpread2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectSpread2\"));\n  var _NativeAnimatedModule = _interopRequireDefault(require(_dependencyMap[2], \"./NativeAnimatedModule\"));\n  var _NativeAnimatedTurboModule = _interopRequireDefault(require(_dependencyMap[3], \"./NativeAnimatedTurboModule\"));\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[4], \"../EventEmitter/NativeEventEmitter\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"../Utilities/Platform\"));\n  var _ReactNativeFeatureFlags = _interopRequireDefault(require(_dependencyMap[6], \"../ReactNative/ReactNativeFeatureFlags\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[7], \"fbjs/lib/invariant\"));\n  var _RCTDeviceEventEmitter = _interopRequireDefault(require(_dependencyMap[8], \"../EventEmitter/RCTDeviceEventEmitter\"));\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  // TODO ********* @petetheheat - Delete this fork when Fabric ships to 100%.\n  var NativeAnimatedModule = _Platform.default.OS === 'ios' && global.RN$Bridgeless === true ? _NativeAnimatedTurboModule.default : _NativeAnimatedModule.default;\n  var __nativeAnimatedNodeTagCount = 1; /* used for animated nodes */\n  var __nativeAnimationIdCount = 1; /* used for started animations */\n\n  var nativeEventEmitter;\n  var waitingForQueuedOperations = new Set();\n  var queueOperations = false;\n  var queue = [];\n  // $FlowFixMe\n  var singleOpQueue = [];\n  var useSingleOpBatching = false;\n  _Platform.default.OS === 'android' && !!(NativeAnimatedModule != null && NativeAnimatedModule.queueAndExecuteBatchedOperations) && _ReactNativeFeatureFlags.default.animatedShouldUseSingleOp();\n  var flushQueueTimeout = null;\n  var eventListenerGetValueCallbacks = {};\n  var eventListenerAnimationFinishedCallbacks = {};\n  var globalEventEmitterGetValueListener = null;\n  var globalEventEmitterAnimationFinishedListener = null;\n  var nativeOps = useSingleOpBatching ? function () {\n    var apis = ['createAnimatedNode',\n    // 1\n    'updateAnimatedNodeConfig',\n    // 2\n    'getValue',\n    // 3\n    'startListeningToAnimatedNodeValue',\n    // 4\n    'stopListeningToAnimatedNodeValue',\n    // 5\n    'connectAnimatedNodes',\n    // 6\n    'disconnectAnimatedNodes',\n    // 7\n    'startAnimatingNode',\n    // 8\n    'stopAnimation',\n    // 9\n    'setAnimatedNodeValue',\n    // 10\n    'setAnimatedNodeOffset',\n    // 11\n    'flattenAnimatedNodeOffset',\n    // 12\n    'extractAnimatedNodeOffset',\n    // 13\n    'connectAnimatedNodeToView',\n    // 14\n    'disconnectAnimatedNodeFromView',\n    // 15\n    'restoreDefaultValues',\n    // 16\n    'dropAnimatedNode',\n    // 17\n    'addAnimatedEventToView',\n    // 18\n    'removeAnimatedEventFromView',\n    // 19\n    'addListener',\n    // 20\n    'removeListener' // 21\n    ];\n    return apis.reduce((acc, functionName, i) => {\n      // These indices need to be kept in sync with the indices in native (see NativeAnimatedModule in Java, or the equivalent for any other native platform).\n      // $FlowFixMe[prop-missing]\n      acc[functionName] = i + 1;\n      return acc;\n    }, {});\n  }() : NativeAnimatedModule;\n\n  /**\n   * Wrappers around NativeAnimatedModule to provide flow and autocomplete support for\n   * the native module methods, and automatic queue management on Android\n   */\n  var API = exports.API = {\n    getValue: function getValue(tag, saveValueCallback) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      if (useSingleOpBatching) {\n        if (saveValueCallback) {\n          eventListenerGetValueCallbacks[tag] = saveValueCallback;\n        }\n        // $FlowFixMe\n        API.queueOperation(nativeOps.getValue, tag);\n      } else {\n        API.queueOperation(nativeOps.getValue, tag, saveValueCallback);\n      }\n    },\n    setWaitingForIdentifier: function setWaitingForIdentifier(id) {\n      waitingForQueuedOperations.add(id);\n      queueOperations = true;\n      if (_ReactNativeFeatureFlags.default.animatedShouldDebounceQueueFlush() && flushQueueTimeout) {\n        clearTimeout(flushQueueTimeout);\n      }\n    },\n    unsetWaitingForIdentifier: function unsetWaitingForIdentifier(id) {\n      waitingForQueuedOperations.delete(id);\n      if (waitingForQueuedOperations.size === 0) {\n        queueOperations = false;\n        API.disableQueue();\n      }\n    },\n    disableQueue: function disableQueue() {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      if (_ReactNativeFeatureFlags.default.animatedShouldDebounceQueueFlush()) {\n        var prevTimeout = flushQueueTimeout;\n        clearImmediate(prevTimeout);\n        flushQueueTimeout = setImmediate(API.flushQueue);\n      } else {\n        API.flushQueue();\n      }\n    },\n    flushQueue: function flushQueue() {\n      /*\n      invariant(NativeAnimatedModule, 'Native animated module is not available');\n      flushQueueTimeout = null;\n       // Early returns before calling any APIs\n      if (useSingleOpBatching && singleOpQueue.length === 0) {\n        return;\n      }\n      if (!useSingleOpBatching && queue.length === 0) {\n        return;\n      }\n       if (useSingleOpBatching) {\n        // Set up event listener for callbacks if it's not set up\n        if (\n          !globalEventEmitterGetValueListener ||\n          !globalEventEmitterAnimationFinishedListener\n        ) {\n          setupGlobalEventEmitterListeners();\n        }\n        // Single op batching doesn't use callback functions, instead we\n        // use RCTDeviceEventEmitter. This reduces overhead of sending lots of\n        // JSI functions across to native code; but also, TM infrastructure currently\n        // does not support packing a function into native arrays.\n        NativeAnimatedModule.queueAndExecuteBatchedOperations?.(singleOpQueue);\n        singleOpQueue.length = 0;\n      } else {\n        Platform.OS === 'android' && NativeAnimatedModule.startOperationBatch?.();\n        for (let q = 0, l = queue.length; q < l; q++) {\n          queue[q]();\n        }\n        queue.length = 0;\n        Platform.OS === 'android' &&\n          NativeAnimatedModule.finishOperationBatch?.();\n      }\n      */\n    },\n    queueOperation: function queueOperation(fn) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      if (useSingleOpBatching) {\n        // Get the command ID from the queued function, and push that ID and any arguments needed to execute the operation\n        // $FlowFixMe: surprise, fn is actually a number\n        singleOpQueue.push(fn, ...args);\n        return;\n      }\n\n      // If queueing is explicitly on, *or* the queue has not yet\n      // been flushed, use the queue. This is to prevent operations\n      // from being executed out of order.\n      if (queueOperations || queue.length !== 0) {\n        queue.push(() => fn(...args));\n      } else {\n        fn(...args);\n      }\n    },\n    createAnimatedNode: function createAnimatedNode(tag, config) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.createAnimatedNode, tag, config);\n    },\n    updateAnimatedNodeConfig: function updateAnimatedNodeConfig(tag, config) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      //if (nativeOps.updateAnimatedNodeConfig) {\n      //  API.queueOperation(nativeOps.updateAnimatedNodeConfig, tag, config);\n      //}\n    },\n    startListeningToAnimatedNodeValue: function startListeningToAnimatedNodeValue(tag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.startListeningToAnimatedNodeValue, tag);\n    },\n    stopListeningToAnimatedNodeValue: function stopListeningToAnimatedNodeValue(tag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.stopListeningToAnimatedNodeValue, tag);\n    },\n    connectAnimatedNodes: function connectAnimatedNodes(parentTag, childTag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.connectAnimatedNodes, parentTag, childTag);\n    },\n    disconnectAnimatedNodes: function disconnectAnimatedNodes(parentTag, childTag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.disconnectAnimatedNodes, parentTag, childTag);\n    },\n    startAnimatingNode: function startAnimatingNode(animationId, nodeTag, config, endCallback) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      if (useSingleOpBatching) {\n        if (endCallback) {\n          eventListenerAnimationFinishedCallbacks[animationId] = endCallback;\n        }\n        // $FlowFixMe\n        API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config);\n      } else {\n        API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config, endCallback);\n      }\n    },\n    stopAnimation: function stopAnimation(animationId) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.stopAnimation, animationId);\n    },\n    setAnimatedNodeValue: function setAnimatedNodeValue(nodeTag, value) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.setAnimatedNodeValue, nodeTag, value);\n    },\n    setAnimatedNodeOffset: function setAnimatedNodeOffset(nodeTag, offset) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.setAnimatedNodeOffset, nodeTag, offset);\n    },\n    flattenAnimatedNodeOffset: function flattenAnimatedNodeOffset(nodeTag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.flattenAnimatedNodeOffset, nodeTag);\n    },\n    extractAnimatedNodeOffset: function extractAnimatedNodeOffset(nodeTag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.extractAnimatedNodeOffset, nodeTag);\n    },\n    connectAnimatedNodeToView: function connectAnimatedNodeToView(nodeTag, viewTag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.connectAnimatedNodeToView, nodeTag, viewTag);\n    },\n    disconnectAnimatedNodeFromView: function disconnectAnimatedNodeFromView(nodeTag, viewTag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.disconnectAnimatedNodeFromView, nodeTag, viewTag);\n    },\n    restoreDefaultValues: function restoreDefaultValues(nodeTag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      // Backwards compat with older native runtimes, can be removed later.\n      if (nativeOps.restoreDefaultValues != null) {\n        API.queueOperation(nativeOps.restoreDefaultValues, nodeTag);\n      }\n    },\n    dropAnimatedNode: function dropAnimatedNode(tag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.dropAnimatedNode, tag);\n    },\n    addAnimatedEventToView: function addAnimatedEventToView(viewTag, eventName, eventMapping) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.addAnimatedEventToView, viewTag, eventName, eventMapping);\n    },\n    removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag) {\n      (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n      API.queueOperation(nativeOps.removeAnimatedEventFromView, viewTag, eventName, animatedNodeTag);\n    }\n  };\n  function setupGlobalEventEmitterListeners() {\n    globalEventEmitterGetValueListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleGetValue', function (params) {\n      var tag = params.tag;\n      var callback = eventListenerGetValueCallbacks[tag];\n      if (!callback) {\n        return;\n      }\n      callback(params.value);\n      delete eventListenerGetValueCallbacks[tag];\n    });\n    globalEventEmitterAnimationFinishedListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleAnimationFinished', function (params) {\n      var animationId = params.animationId;\n      var callback = eventListenerAnimationFinishedCallbacks[animationId];\n      if (!callback) {\n        return;\n      }\n      callback(params);\n      delete eventListenerAnimationFinishedCallbacks[animationId];\n    });\n  }\n\n  /**\n   * Styles allowed by the native animated implementation.\n   *\n   * In general native animated implementation should support any numeric or color property that\n   * doesn't need to be updated through the shadow view hierarchy (all non-layout properties).\n   */\n  var SUPPORTED_COLOR_STYLES = {\n    backgroundColor: true,\n    borderBottomColor: true,\n    borderColor: true,\n    borderEndColor: true,\n    borderLeftColor: true,\n    borderRightColor: true,\n    borderStartColor: true,\n    borderTopColor: true,\n    color: true,\n    tintColor: true\n  };\n  var SUPPORTED_STYLES = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, SUPPORTED_COLOR_STYLES), {}, {\n    borderBottomEndRadius: true,\n    borderBottomLeftRadius: true,\n    borderBottomRightRadius: true,\n    borderBottomStartRadius: true,\n    borderRadius: true,\n    borderTopEndRadius: true,\n    borderTopLeftRadius: true,\n    borderTopRightRadius: true,\n    borderTopStartRadius: true,\n    elevation: true,\n    opacity: true,\n    transform: true,\n    zIndex: true,\n    /* ios styles */\n    shadowOpacity: true,\n    shadowRadius: true,\n    /* legacy android transform properties */\n    scaleX: true,\n    scaleY: true,\n    translateX: true,\n    translateY: true\n  });\n  var SUPPORTED_TRANSFORMS = {\n    translateX: true,\n    translateY: true,\n    scale: true,\n    scaleX: true,\n    scaleY: true,\n    rotate: true,\n    rotateX: true,\n    rotateY: true,\n    rotateZ: true,\n    perspective: true\n  };\n  var SUPPORTED_INTERPOLATION_PARAMS = {\n    inputRange: true,\n    outputRange: true,\n    extrapolate: true,\n    extrapolateRight: true,\n    extrapolateLeft: true\n  };\n  function addWhitelistedStyleProp(prop) {\n    SUPPORTED_STYLES[prop] = true;\n  }\n  function addWhitelistedTransformProp(prop) {\n    SUPPORTED_TRANSFORMS[prop] = true;\n  }\n  function addWhitelistedInterpolationParam(param) {\n    SUPPORTED_INTERPOLATION_PARAMS[param] = true;\n  }\n  function isSupportedColorStyleProp(prop) {\n    return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);\n  }\n  function isSupportedStyleProp(prop) {\n    return SUPPORTED_STYLES.hasOwnProperty(prop);\n  }\n  function isSupportedTransformProp(prop) {\n    return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);\n  }\n  function isSupportedInterpolationParam(param) {\n    return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);\n  }\n  function validateTransform(configs) {\n    configs.forEach(config => {\n      if (!isSupportedTransformProp(config.property)) {\n        throw new Error(\"Property '\" + config.property + \"' is not supported by native animated module\");\n      }\n    });\n  }\n  function validateStyles(styles) {\n    for (var _key2 in styles) {\n      if (!isSupportedStyleProp(_key2)) {\n        throw new Error(\"Style property '\" + _key2 + \"' is not supported by native animated module\");\n      }\n    }\n  }\n  function validateInterpolation(config) {\n    for (var _key3 in config) {\n      if (!isSupportedInterpolationParam(_key3)) {\n        throw new Error(\"Interpolation property '\" + _key3 + \"' is not supported by native animated module\");\n      }\n    }\n  }\n  function generateNewNodeTag() {\n    return __nativeAnimatedNodeTagCount++;\n  }\n  function generateNewAnimationId() {\n    return __nativeAnimationIdCount++;\n  }\n  function assertNativeAnimatedModule() {\n    (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');\n  }\n  var _warnedMissingNativeAnimated = false;\n  function shouldUseNativeDriver(config) {\n    if (config.useNativeDriver == null) {\n      console.warn('Animated: `useNativeDriver` was not specified. This is a required ' + 'option and must be explicitly set to `true` or `false`');\n    }\n    if (config.useNativeDriver === true && !NativeAnimatedModule) {\n      if (!_warnedMissingNativeAnimated) {\n        console.warn('Animated: `useNativeDriver` is not supported because the native ' + 'animated module is missing. Falling back to JS-based animation. To ' + 'resolve this, add `RCTAnimation` module to this app, or remove ' + '`useNativeDriver`. ' + 'Make sure to run `bundle exec pod install` first. Read more about autolinking: https://github.com/react-native-community/cli/blob/master/docs/autolinking.md');\n        _warnedMissingNativeAnimated = true;\n      }\n      return false;\n    }\n    return config.useNativeDriver || false;\n  }\n  function transformDataType(value) {\n    // Change the string type to number type so we can reuse the same logic in\n    // iOS and Android platform\n    if (typeof value !== 'string') {\n      return value;\n    }\n    if (/deg$/.test(value)) {\n      var degrees = parseFloat(value) || 0;\n      var radians = degrees * Math.PI / 180.0;\n      return radians;\n    } else {\n      return value;\n    }\n  }\n  var _default = exports.default = {\n    API,\n    isSupportedColorStyleProp,\n    isSupportedStyleProp,\n    isSupportedTransformProp,\n    isSupportedInterpolationParam,\n    addWhitelistedStyleProp,\n    addWhitelistedTransformProp,\n    addWhitelistedInterpolationParam,\n    validateStyles,\n    validateTransform,\n    validateInterpolation,\n    generateNewNodeTag,\n    generateNewAnimationId,\n    assertNativeAnimatedModule,\n    shouldUseNativeDriver,\n    transformDataType,\n    // $FlowExpectedError[unsafe-getters-setters] - unsafe getter lint suppresion\n    // $FlowExpectedError[missing-type-arg] - unsafe getter lint suppresion\n    get nativeEventEmitter() {\n      if (!nativeEventEmitter) {\n        nativeEventEmitter = new _NativeEventEmitter.default(\n        // *********: NativeEventEmitter only used this parameter on iOS. Now it uses it on all platforms, so this code was modified automatically to preserve its behavior\n        // If you want to use the native module on other platforms, please remove this condition and test its behavior\n        _Platform.default.OS !== 'ios' ? null : NativeAnimatedModule);\n      }\n      return nativeEventEmitter;\n    }\n  };\n});", "lineCount": 482, "map": [[23, 2, 1, 0], [23, 6, 1, 0, "_objectSpread2"], [23, 20, 1, 0], [23, 23, 1, 0, "_interopRequireDefault"], [23, 45, 1, 0], [23, 46, 1, 0, "require"], [23, 53, 1, 0], [23, 54, 1, 0, "_dependencyMap"], [23, 68, 1, 0], [24, 2, 12, 0], [24, 6, 12, 0, "_NativeAnimatedModule"], [24, 27, 12, 0], [24, 30, 12, 0, "_interopRequireDefault"], [24, 52, 12, 0], [24, 53, 12, 0, "require"], [24, 60, 12, 0], [24, 61, 12, 0, "_dependencyMap"], [24, 75, 12, 0], [25, 2, 13, 0], [25, 6, 13, 0, "_NativeAnimatedTurboModule"], [25, 32, 13, 0], [25, 35, 13, 0, "_interopRequireDefault"], [25, 57, 13, 0], [25, 58, 13, 0, "require"], [25, 65, 13, 0], [25, 66, 13, 0, "_dependencyMap"], [25, 80, 13, 0], [26, 2, 14, 0], [26, 6, 14, 0, "_NativeEventEmitter"], [26, 25, 14, 0], [26, 28, 14, 0, "_interopRequireDefault"], [26, 50, 14, 0], [26, 51, 14, 0, "require"], [26, 58, 14, 0], [26, 59, 14, 0, "_dependencyMap"], [26, 73, 14, 0], [27, 2, 15, 0], [27, 6, 15, 0, "_Platform"], [27, 15, 15, 0], [27, 18, 15, 0, "_interopRequireDefault"], [27, 40, 15, 0], [27, 41, 15, 0, "require"], [27, 48, 15, 0], [27, 49, 15, 0, "_dependencyMap"], [27, 63, 15, 0], [28, 2, 16, 0], [28, 6, 16, 0, "_ReactNativeFeatureFlags"], [28, 30, 16, 0], [28, 33, 16, 0, "_interopRequireDefault"], [28, 55, 16, 0], [28, 56, 16, 0, "require"], [28, 63, 16, 0], [28, 64, 16, 0, "_dependencyMap"], [28, 78, 16, 0], [29, 2, 17, 0], [29, 6, 17, 0, "_invariant"], [29, 16, 17, 0], [29, 19, 17, 0, "_interopRequireDefault"], [29, 41, 17, 0], [29, 42, 17, 0, "require"], [29, 49, 17, 0], [29, 50, 17, 0, "_dependencyMap"], [29, 64, 17, 0], [30, 2, 18, 0], [30, 6, 18, 0, "_RCTDeviceEventEmitter"], [30, 28, 18, 0], [30, 31, 18, 0, "_interopRequireDefault"], [30, 53, 18, 0], [30, 54, 18, 0, "require"], [30, 61, 18, 0], [30, 62, 18, 0, "_dependencyMap"], [30, 76, 18, 0], [31, 2, 2, 0], [32, 0, 3, 0], [33, 0, 4, 0], [34, 0, 5, 0], [35, 0, 6, 0], [36, 0, 7, 0], [37, 0, 8, 0], [38, 0, 9, 0], [39, 0, 10, 0], [41, 2, 19, 0], [42, 2, 20, 0], [42, 6, 20, 4, "NativeAnimatedModule"], [42, 26, 20, 24], [42, 29, 20, 27, "Platform"], [42, 46, 20, 35], [42, 47, 20, 36, "OS"], [42, 49, 20, 38], [42, 54, 20, 43], [42, 59, 20, 48], [42, 63, 20, 52, "global"], [42, 69, 20, 58], [42, 70, 20, 59, "RN$Bridgeless"], [42, 83, 20, 72], [42, 88, 20, 77], [42, 92, 20, 81], [42, 95, 20, 84, "NativeAnimatedTurboModule"], [42, 129, 20, 109], [42, 132, 20, 112, "NativeAnimatedNonTurboModule"], [42, 161, 20, 140], [43, 2, 21, 0], [43, 6, 21, 4, "__nativeAnimatedNodeTagCount"], [43, 34, 21, 32], [43, 37, 21, 35], [43, 38, 21, 36], [43, 39, 21, 37], [43, 40, 21, 38], [44, 2, 22, 0], [44, 6, 22, 4, "__nativeAnimationIdCount"], [44, 30, 22, 28], [44, 33, 22, 31], [44, 34, 22, 32], [44, 35, 22, 33], [44, 36, 22, 34], [46, 2, 24, 0], [46, 6, 24, 4, "nativeEventEmitter"], [46, 24, 24, 22], [47, 2, 25, 0], [47, 6, 25, 4, "waitingForQueuedOperations"], [47, 32, 25, 30], [47, 35, 25, 33], [47, 39, 25, 37, "Set"], [47, 42, 25, 40], [47, 43, 25, 41], [47, 44, 25, 42], [48, 2, 26, 0], [48, 6, 26, 4, "queueOperations"], [48, 21, 26, 19], [48, 24, 26, 22], [48, 29, 26, 27], [49, 2, 27, 0], [49, 6, 27, 4, "queue"], [49, 11, 27, 9], [49, 14, 27, 12], [49, 16, 27, 14], [50, 2, 28, 0], [51, 2, 29, 0], [51, 6, 29, 4, "singleOpQueue"], [51, 19, 29, 17], [51, 22, 29, 20], [51, 24, 29, 22], [52, 2, 30, 0], [52, 6, 30, 4, "useSingleOpBatching"], [52, 25, 30, 23], [52, 28, 30, 26], [52, 33, 30, 31], [53, 2, 31, 0, "Platform"], [53, 19, 31, 8], [53, 20, 31, 9, "OS"], [53, 22, 31, 11], [53, 27, 31, 16], [53, 36, 31, 25], [53, 40, 31, 29], [53, 41, 31, 30], [53, 43, 31, 32, "NativeAnimatedModule"], [53, 63, 31, 52], [53, 67, 31, 56], [53, 71, 31, 60], [53, 75, 31, 64, "NativeAnimatedModule"], [53, 95, 31, 84], [53, 96, 31, 85, "queueAndExecuteBatchedOperations"], [53, 128, 31, 117], [53, 129, 31, 118], [53, 133, 31, 122, "ReactNativeFeatureFlags"], [53, 165, 31, 145], [53, 166, 31, 146, "animatedShouldUseSingleOp"], [53, 191, 31, 171], [53, 192, 31, 172], [53, 193, 31, 173], [54, 2, 32, 0], [54, 6, 32, 4, "flushQueueTimeout"], [54, 23, 32, 21], [54, 26, 32, 24], [54, 30, 32, 28], [55, 2, 33, 0], [55, 6, 33, 4, "eventListenerGetValueCallbacks"], [55, 36, 33, 34], [55, 39, 33, 37], [55, 40, 33, 38], [55, 41, 33, 39], [56, 2, 34, 0], [56, 6, 34, 4, "eventListenerAnimationFinishedCallbacks"], [56, 45, 34, 43], [56, 48, 34, 46], [56, 49, 34, 47], [56, 50, 34, 48], [57, 2, 35, 0], [57, 6, 35, 4, "globalEventEmitterGetValueListener"], [57, 40, 35, 38], [57, 43, 35, 41], [57, 47, 35, 45], [58, 2, 36, 0], [58, 6, 36, 4, "globalEventEmitterAnimationFinishedListener"], [58, 49, 36, 47], [58, 52, 36, 50], [58, 56, 36, 54], [59, 2, 37, 0], [59, 6, 37, 4, "nativeOps"], [59, 15, 37, 13], [59, 18, 37, 16, "useSingleOpBatching"], [59, 37, 37, 35], [59, 40, 37, 38], [59, 52, 37, 50], [60, 4, 38, 2], [60, 8, 38, 6, "apis"], [60, 12, 38, 10], [60, 15, 38, 13], [60, 16, 38, 14], [60, 36, 38, 34], [61, 4, 39, 2], [62, 4, 40, 2], [62, 30, 40, 28], [63, 4, 41, 2], [64, 4, 42, 2], [64, 14, 42, 12], [65, 4, 43, 2], [66, 4, 44, 2], [66, 39, 44, 37], [67, 4, 45, 2], [68, 4, 46, 2], [68, 38, 46, 36], [69, 4, 47, 2], [70, 4, 48, 2], [70, 26, 48, 24], [71, 4, 49, 2], [72, 4, 50, 2], [72, 29, 50, 27], [73, 4, 51, 2], [74, 4, 52, 2], [74, 24, 52, 22], [75, 4, 53, 2], [76, 4, 54, 2], [76, 19, 54, 17], [77, 4, 55, 2], [78, 4, 56, 2], [78, 26, 56, 24], [79, 4, 57, 2], [80, 4, 58, 2], [80, 27, 58, 25], [81, 4, 59, 2], [82, 4, 60, 2], [82, 31, 60, 29], [83, 4, 61, 2], [84, 4, 62, 2], [84, 31, 62, 29], [85, 4, 63, 2], [86, 4, 64, 2], [86, 31, 64, 29], [87, 4, 65, 2], [88, 4, 66, 2], [88, 36, 66, 34], [89, 4, 67, 2], [90, 4, 68, 2], [90, 26, 68, 24], [91, 4, 69, 2], [92, 4, 70, 2], [92, 22, 70, 20], [93, 4, 71, 2], [94, 4, 72, 2], [94, 28, 72, 26], [95, 4, 73, 2], [96, 4, 74, 2], [96, 33, 74, 31], [97, 4, 75, 2], [98, 4, 76, 2], [98, 17, 76, 15], [99, 4, 77, 2], [100, 4, 78, 2], [100, 20, 78, 18], [100, 21, 78, 19], [101, 4, 78, 19], [101, 5, 79, 3], [102, 4, 80, 2], [102, 11, 80, 9, "apis"], [102, 15, 80, 13], [102, 16, 80, 14, "reduce"], [102, 22, 80, 20], [102, 23, 80, 21], [102, 24, 80, 22, "acc"], [102, 27, 80, 25], [102, 29, 80, 27, "functionName"], [102, 41, 80, 39], [102, 43, 80, 41, "i"], [102, 44, 80, 42], [102, 49, 80, 47], [103, 6, 81, 4], [104, 6, 82, 4], [105, 6, 83, 4, "acc"], [105, 9, 83, 7], [105, 10, 83, 8, "functionName"], [105, 22, 83, 20], [105, 23, 83, 21], [105, 26, 83, 24, "i"], [105, 27, 83, 25], [105, 30, 83, 28], [105, 31, 83, 29], [106, 6, 84, 4], [106, 13, 84, 11, "acc"], [106, 16, 84, 14], [107, 4, 85, 2], [107, 5, 85, 3], [107, 7, 85, 5], [107, 8, 85, 6], [107, 9, 85, 7], [107, 10, 85, 8], [108, 2, 86, 0], [108, 3, 86, 1], [108, 4, 86, 2], [108, 5, 86, 3], [108, 8, 86, 6, "NativeAnimatedModule"], [108, 28, 86, 26], [110, 2, 88, 0], [111, 0, 89, 0], [112, 0, 90, 0], [113, 0, 91, 0], [114, 2, 92, 0], [114, 6, 92, 4, "API"], [114, 9, 92, 7], [114, 12, 92, 7, "exports"], [114, 19, 92, 7], [114, 20, 92, 7, "API"], [114, 23, 92, 7], [114, 26, 92, 10], [115, 4, 93, 2, "getValue"], [115, 12, 93, 10], [115, 14, 93, 12], [115, 23, 93, 21, "getValue"], [115, 31, 93, 29, "getValue"], [115, 32, 93, 30, "tag"], [115, 35, 93, 33], [115, 37, 93, 35, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [115, 54, 93, 52], [115, 56, 93, 54], [116, 6, 94, 4], [116, 10, 94, 4, "invariant"], [116, 28, 94, 13], [116, 30, 94, 14, "nativeOps"], [116, 39, 94, 23], [116, 41, 94, 25], [116, 82, 94, 66], [116, 83, 94, 67], [117, 6, 95, 4], [117, 10, 95, 8, "useSingleOpBatching"], [117, 29, 95, 27], [117, 31, 95, 29], [118, 8, 96, 6], [118, 12, 96, 10, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [118, 29, 96, 27], [118, 31, 96, 29], [119, 10, 97, 8, "eventListenerGetValueCallbacks"], [119, 40, 97, 38], [119, 41, 97, 39, "tag"], [119, 44, 97, 42], [119, 45, 97, 43], [119, 48, 97, 46, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [119, 65, 97, 63], [120, 8, 98, 6], [121, 8, 99, 6], [122, 8, 100, 6, "API"], [122, 11, 100, 9], [122, 12, 100, 10, "queueOperation"], [122, 26, 100, 24], [122, 27, 100, 25, "nativeOps"], [122, 36, 100, 34], [122, 37, 100, 35, "getValue"], [122, 45, 100, 43], [122, 47, 100, 45, "tag"], [122, 50, 100, 48], [122, 51, 100, 49], [123, 6, 101, 4], [123, 7, 101, 5], [123, 13, 101, 11], [124, 8, 102, 6, "API"], [124, 11, 102, 9], [124, 12, 102, 10, "queueOperation"], [124, 26, 102, 24], [124, 27, 102, 25, "nativeOps"], [124, 36, 102, 34], [124, 37, 102, 35, "getValue"], [124, 45, 102, 43], [124, 47, 102, 45, "tag"], [124, 50, 102, 48], [124, 52, 102, 50, "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [124, 69, 102, 67], [124, 70, 102, 68], [125, 6, 103, 4], [126, 4, 104, 2], [126, 5, 104, 3], [127, 4, 105, 2, "setWaitingForIdentifier"], [127, 27, 105, 25], [127, 29, 105, 27], [127, 38, 105, 36, "setWaitingForIdentifier"], [127, 61, 105, 59, "setWaitingForIdentifier"], [127, 62, 105, 60, "id"], [127, 64, 105, 62], [127, 66, 105, 64], [128, 6, 106, 4, "waitingForQueuedOperations"], [128, 32, 106, 30], [128, 33, 106, 31, "add"], [128, 36, 106, 34], [128, 37, 106, 35, "id"], [128, 39, 106, 37], [128, 40, 106, 38], [129, 6, 107, 4, "queueOperations"], [129, 21, 107, 19], [129, 24, 107, 22], [129, 28, 107, 26], [130, 6, 108, 4], [130, 10, 108, 8, "ReactNativeFeatureFlags"], [130, 42, 108, 31], [130, 43, 108, 32, "animatedShouldDebounceQueueFlush"], [130, 75, 108, 64], [130, 76, 108, 65], [130, 77, 108, 66], [130, 81, 108, 70, "flushQueueTimeout"], [130, 98, 108, 87], [130, 100, 108, 89], [131, 8, 109, 6, "clearTimeout"], [131, 20, 109, 18], [131, 21, 109, 19, "flushQueueTimeout"], [131, 38, 109, 36], [131, 39, 109, 37], [132, 6, 110, 4], [133, 4, 111, 2], [133, 5, 111, 3], [134, 4, 112, 2, "unsetWaitingForIdentifier"], [134, 29, 112, 27], [134, 31, 112, 29], [134, 40, 112, 38, "unsetWaitingForIdentifier"], [134, 65, 112, 63, "unsetWaitingForIdentifier"], [134, 66, 112, 64, "id"], [134, 68, 112, 66], [134, 70, 112, 68], [135, 6, 113, 4, "waitingForQueuedOperations"], [135, 32, 113, 30], [135, 33, 113, 31, "delete"], [135, 39, 113, 37], [135, 40, 113, 38, "id"], [135, 42, 113, 40], [135, 43, 113, 41], [136, 6, 114, 4], [136, 10, 114, 8, "waitingForQueuedOperations"], [136, 36, 114, 34], [136, 37, 114, 35, "size"], [136, 41, 114, 39], [136, 46, 114, 44], [136, 47, 114, 45], [136, 49, 114, 47], [137, 8, 115, 6, "queueOperations"], [137, 23, 115, 21], [137, 26, 115, 24], [137, 31, 115, 29], [138, 8, 116, 6, "API"], [138, 11, 116, 9], [138, 12, 116, 10, "disableQueue"], [138, 24, 116, 22], [138, 25, 116, 23], [138, 26, 116, 24], [139, 6, 117, 4], [140, 4, 118, 2], [140, 5, 118, 3], [141, 4, 119, 2, "disableQueue"], [141, 16, 119, 14], [141, 18, 119, 16], [141, 27, 119, 25, "disableQueue"], [141, 39, 119, 37, "disableQueue"], [141, 40, 119, 37], [141, 42, 119, 40], [142, 6, 120, 4], [142, 10, 120, 4, "invariant"], [142, 28, 120, 13], [142, 30, 120, 14, "nativeOps"], [142, 39, 120, 23], [142, 41, 120, 25], [142, 82, 120, 66], [142, 83, 120, 67], [143, 6, 121, 4], [143, 10, 121, 8, "ReactNativeFeatureFlags"], [143, 42, 121, 31], [143, 43, 121, 32, "animatedShouldDebounceQueueFlush"], [143, 75, 121, 64], [143, 76, 121, 65], [143, 77, 121, 66], [143, 79, 121, 68], [144, 8, 122, 6], [144, 12, 122, 10, "prevTimeout"], [144, 23, 122, 21], [144, 26, 122, 24, "flushQueueTimeout"], [144, 43, 122, 41], [145, 8, 123, 6, "clearImmediate"], [145, 22, 123, 20], [145, 23, 123, 21, "prevTimeout"], [145, 34, 123, 32], [145, 35, 123, 33], [146, 8, 124, 6, "flushQueueTimeout"], [146, 25, 124, 23], [146, 28, 124, 26, "setImmediate"], [146, 40, 124, 38], [146, 41, 124, 39, "API"], [146, 44, 124, 42], [146, 45, 124, 43, "flushQueue"], [146, 55, 124, 53], [146, 56, 124, 54], [147, 6, 125, 4], [147, 7, 125, 5], [147, 13, 125, 11], [148, 8, 126, 6, "API"], [148, 11, 126, 9], [148, 12, 126, 10, "flushQueue"], [148, 22, 126, 20], [148, 23, 126, 21], [148, 24, 126, 22], [149, 6, 127, 4], [150, 4, 128, 2], [150, 5, 128, 3], [151, 4, 129, 2, "flushQueue"], [151, 14, 129, 12], [151, 16, 129, 14], [151, 25, 129, 23, "flushQueue"], [151, 35, 129, 33, "flushQueue"], [151, 36, 129, 33], [151, 38, 129, 36], [152, 6, 130, 4], [153, 0, 131, 0], [154, 0, 132, 0], [155, 0, 133, 0], [156, 0, 134, 0], [157, 0, 135, 0], [158, 0, 136, 0], [159, 0, 137, 0], [160, 0, 138, 0], [161, 0, 139, 0], [162, 0, 140, 0], [163, 0, 141, 0], [164, 0, 142, 0], [165, 0, 143, 0], [166, 0, 144, 0], [167, 0, 145, 0], [168, 0, 146, 0], [169, 0, 147, 0], [170, 0, 148, 0], [171, 0, 149, 0], [172, 0, 150, 0], [173, 0, 151, 0], [174, 0, 152, 0], [175, 0, 153, 0], [176, 0, 154, 0], [177, 0, 155, 0], [178, 0, 156, 0], [179, 0, 157, 0], [180, 0, 158, 0], [181, 0, 159, 0], [182, 0, 160, 0], [183, 0, 161, 0], [184, 0, 162, 0], [185, 0, 163, 0], [186, 4, 130, 4], [186, 5, 164, 3], [187, 4, 165, 2, "queueOperation"], [187, 18, 165, 16], [187, 20, 165, 18], [187, 29, 165, 27, "queueOperation"], [187, 43, 165, 41, "queueOperation"], [187, 44, 165, 42, "fn"], [187, 46, 165, 44], [187, 48, 165, 46], [188, 6, 166, 4], [188, 11, 166, 9], [188, 15, 166, 13, "_len"], [188, 19, 166, 17], [188, 22, 166, 20, "arguments"], [188, 31, 166, 29], [188, 32, 166, 30, "length"], [188, 38, 166, 36], [188, 40, 166, 38, "args"], [188, 44, 166, 42], [188, 47, 166, 45], [188, 51, 166, 49, "Array"], [188, 56, 166, 54], [188, 57, 166, 55, "_len"], [188, 61, 166, 59], [188, 64, 166, 62], [188, 65, 166, 63], [188, 68, 166, 66, "_len"], [188, 72, 166, 70], [188, 75, 166, 73], [188, 76, 166, 74], [188, 79, 166, 77], [188, 80, 166, 78], [188, 81, 166, 79], [188, 83, 166, 81, "_key"], [188, 87, 166, 85], [188, 90, 166, 88], [188, 91, 166, 89], [188, 93, 166, 91, "_key"], [188, 97, 166, 95], [188, 100, 166, 98, "_len"], [188, 104, 166, 102], [188, 106, 166, 104, "_key"], [188, 110, 166, 108], [188, 112, 166, 110], [188, 114, 166, 112], [189, 8, 167, 6, "args"], [189, 12, 167, 10], [189, 13, 167, 11, "_key"], [189, 17, 167, 15], [189, 20, 167, 18], [189, 21, 167, 19], [189, 22, 167, 20], [189, 25, 167, 23, "arguments"], [189, 34, 167, 32], [189, 35, 167, 33, "_key"], [189, 39, 167, 37], [189, 40, 167, 38], [190, 6, 168, 4], [191, 6, 169, 4], [191, 10, 169, 8, "useSingleOpBatching"], [191, 29, 169, 27], [191, 31, 169, 29], [192, 8, 170, 6], [193, 8, 171, 6], [194, 8, 172, 6, "singleOpQueue"], [194, 21, 172, 19], [194, 22, 172, 20, "push"], [194, 26, 172, 24], [194, 27, 172, 25, "fn"], [194, 29, 172, 27], [194, 31, 172, 29], [194, 34, 172, 32, "args"], [194, 38, 172, 36], [194, 39, 172, 37], [195, 8, 173, 6], [196, 6, 174, 4], [198, 6, 176, 4], [199, 6, 177, 4], [200, 6, 178, 4], [201, 6, 179, 4], [201, 10, 179, 8, "queueOperations"], [201, 25, 179, 23], [201, 29, 179, 27, "queue"], [201, 34, 179, 32], [201, 35, 179, 33, "length"], [201, 41, 179, 39], [201, 46, 179, 44], [201, 47, 179, 45], [201, 49, 179, 47], [202, 8, 180, 6, "queue"], [202, 13, 180, 11], [202, 14, 180, 12, "push"], [202, 18, 180, 16], [202, 19, 180, 17], [202, 25, 180, 23, "fn"], [202, 27, 180, 25], [202, 28, 180, 26], [202, 31, 180, 29, "args"], [202, 35, 180, 33], [202, 36, 180, 34], [202, 37, 180, 35], [203, 6, 181, 4], [203, 7, 181, 5], [203, 13, 181, 11], [204, 8, 182, 6, "fn"], [204, 10, 182, 8], [204, 11, 182, 9], [204, 14, 182, 12, "args"], [204, 18, 182, 16], [204, 19, 182, 17], [205, 6, 183, 4], [206, 4, 184, 2], [206, 5, 184, 3], [207, 4, 185, 2, "createAnimatedNode"], [207, 22, 185, 20], [207, 24, 185, 22], [207, 33, 185, 31, "createAnimatedNode"], [207, 51, 185, 49, "createAnimatedNode"], [207, 52, 185, 50, "tag"], [207, 55, 185, 53], [207, 57, 185, 55, "config"], [207, 63, 185, 61], [207, 65, 185, 63], [208, 6, 186, 4], [208, 10, 186, 4, "invariant"], [208, 28, 186, 13], [208, 30, 186, 14, "nativeOps"], [208, 39, 186, 23], [208, 41, 186, 25], [208, 82, 186, 66], [208, 83, 186, 67], [209, 6, 187, 4, "API"], [209, 9, 187, 7], [209, 10, 187, 8, "queueOperation"], [209, 24, 187, 22], [209, 25, 187, 23, "nativeOps"], [209, 34, 187, 32], [209, 35, 187, 33, "createAnimatedNode"], [209, 53, 187, 51], [209, 55, 187, 53, "tag"], [209, 58, 187, 56], [209, 60, 187, 58, "config"], [209, 66, 187, 64], [209, 67, 187, 65], [210, 4, 188, 2], [210, 5, 188, 3], [211, 4, 189, 2, "updateAnimatedNodeConfig"], [211, 28, 189, 26], [211, 30, 189, 28], [211, 39, 189, 37, "updateAnimatedNodeConfig"], [211, 63, 189, 61, "updateAnimatedNodeConfig"], [211, 64, 189, 62, "tag"], [211, 67, 189, 65], [211, 69, 189, 67, "config"], [211, 75, 189, 73], [211, 77, 189, 75], [212, 6, 190, 4], [212, 10, 190, 4, "invariant"], [212, 28, 190, 13], [212, 30, 190, 14, "nativeOps"], [212, 39, 190, 23], [212, 41, 190, 25], [212, 82, 190, 66], [212, 83, 190, 67], [213, 6, 191, 4], [214, 6, 192, 4], [215, 6, 193, 4], [216, 4, 194, 2], [216, 5, 194, 3], [217, 4, 195, 2, "startListeningToAnimatedNodeValue"], [217, 37, 195, 35], [217, 39, 195, 37], [217, 48, 195, 46, "startListeningToAnimatedNodeValue"], [217, 81, 195, 79, "startListeningToAnimatedNodeValue"], [217, 82, 195, 80, "tag"], [217, 85, 195, 83], [217, 87, 195, 85], [218, 6, 196, 4], [218, 10, 196, 4, "invariant"], [218, 28, 196, 13], [218, 30, 196, 14, "nativeOps"], [218, 39, 196, 23], [218, 41, 196, 25], [218, 82, 196, 66], [218, 83, 196, 67], [219, 6, 197, 4, "API"], [219, 9, 197, 7], [219, 10, 197, 8, "queueOperation"], [219, 24, 197, 22], [219, 25, 197, 23, "nativeOps"], [219, 34, 197, 32], [219, 35, 197, 33, "startListeningToAnimatedNodeValue"], [219, 68, 197, 66], [219, 70, 197, 68, "tag"], [219, 73, 197, 71], [219, 74, 197, 72], [220, 4, 198, 2], [220, 5, 198, 3], [221, 4, 199, 2, "stopListeningToAnimatedNodeValue"], [221, 36, 199, 34], [221, 38, 199, 36], [221, 47, 199, 45, "stopListeningToAnimatedNodeValue"], [221, 79, 199, 77, "stopListeningToAnimatedNodeValue"], [221, 80, 199, 78, "tag"], [221, 83, 199, 81], [221, 85, 199, 83], [222, 6, 200, 4], [222, 10, 200, 4, "invariant"], [222, 28, 200, 13], [222, 30, 200, 14, "nativeOps"], [222, 39, 200, 23], [222, 41, 200, 25], [222, 82, 200, 66], [222, 83, 200, 67], [223, 6, 201, 4, "API"], [223, 9, 201, 7], [223, 10, 201, 8, "queueOperation"], [223, 24, 201, 22], [223, 25, 201, 23, "nativeOps"], [223, 34, 201, 32], [223, 35, 201, 33, "stopListeningToAnimatedNodeValue"], [223, 67, 201, 65], [223, 69, 201, 67, "tag"], [223, 72, 201, 70], [223, 73, 201, 71], [224, 4, 202, 2], [224, 5, 202, 3], [225, 4, 203, 2, "connectAnimatedNodes"], [225, 24, 203, 22], [225, 26, 203, 24], [225, 35, 203, 33, "connectAnimatedNodes"], [225, 55, 203, 53, "connectAnimatedNodes"], [225, 56, 203, 54, "parentTag"], [225, 65, 203, 63], [225, 67, 203, 65, "childTag"], [225, 75, 203, 73], [225, 77, 203, 75], [226, 6, 204, 4], [226, 10, 204, 4, "invariant"], [226, 28, 204, 13], [226, 30, 204, 14, "nativeOps"], [226, 39, 204, 23], [226, 41, 204, 25], [226, 82, 204, 66], [226, 83, 204, 67], [227, 6, 205, 4, "API"], [227, 9, 205, 7], [227, 10, 205, 8, "queueOperation"], [227, 24, 205, 22], [227, 25, 205, 23, "nativeOps"], [227, 34, 205, 32], [227, 35, 205, 33, "connectAnimatedNodes"], [227, 55, 205, 53], [227, 57, 205, 55, "parentTag"], [227, 66, 205, 64], [227, 68, 205, 66, "childTag"], [227, 76, 205, 74], [227, 77, 205, 75], [228, 4, 206, 2], [228, 5, 206, 3], [229, 4, 207, 2, "disconnectAnimatedNodes"], [229, 27, 207, 25], [229, 29, 207, 27], [229, 38, 207, 36, "disconnectAnimatedNodes"], [229, 61, 207, 59, "disconnectAnimatedNodes"], [229, 62, 207, 60, "parentTag"], [229, 71, 207, 69], [229, 73, 207, 71, "childTag"], [229, 81, 207, 79], [229, 83, 207, 81], [230, 6, 208, 4], [230, 10, 208, 4, "invariant"], [230, 28, 208, 13], [230, 30, 208, 14, "nativeOps"], [230, 39, 208, 23], [230, 41, 208, 25], [230, 82, 208, 66], [230, 83, 208, 67], [231, 6, 209, 4, "API"], [231, 9, 209, 7], [231, 10, 209, 8, "queueOperation"], [231, 24, 209, 22], [231, 25, 209, 23, "nativeOps"], [231, 34, 209, 32], [231, 35, 209, 33, "disconnectAnimatedNodes"], [231, 58, 209, 56], [231, 60, 209, 58, "parentTag"], [231, 69, 209, 67], [231, 71, 209, 69, "childTag"], [231, 79, 209, 77], [231, 80, 209, 78], [232, 4, 210, 2], [232, 5, 210, 3], [233, 4, 211, 2, "startAnimatingNode"], [233, 22, 211, 20], [233, 24, 211, 22], [233, 33, 211, 31, "startAnimatingNode"], [233, 51, 211, 49, "startAnimatingNode"], [233, 52, 211, 50, "animationId"], [233, 63, 211, 61], [233, 65, 211, 63, "nodeTag"], [233, 72, 211, 70], [233, 74, 211, 72, "config"], [233, 80, 211, 78], [233, 82, 211, 80, "endCallback"], [233, 93, 211, 91], [233, 95, 211, 93], [234, 6, 212, 4], [234, 10, 212, 4, "invariant"], [234, 28, 212, 13], [234, 30, 212, 14, "nativeOps"], [234, 39, 212, 23], [234, 41, 212, 25], [234, 82, 212, 66], [234, 83, 212, 67], [235, 6, 213, 4], [235, 10, 213, 8, "useSingleOpBatching"], [235, 29, 213, 27], [235, 31, 213, 29], [236, 8, 214, 6], [236, 12, 214, 10, "endCallback"], [236, 23, 214, 21], [236, 25, 214, 23], [237, 10, 215, 8, "eventListenerAnimationFinishedCallbacks"], [237, 49, 215, 47], [237, 50, 215, 48, "animationId"], [237, 61, 215, 59], [237, 62, 215, 60], [237, 65, 215, 63, "endCallback"], [237, 76, 215, 74], [238, 8, 216, 6], [239, 8, 217, 6], [240, 8, 218, 6, "API"], [240, 11, 218, 9], [240, 12, 218, 10, "queueOperation"], [240, 26, 218, 24], [240, 27, 218, 25, "nativeOps"], [240, 36, 218, 34], [240, 37, 218, 35, "startAnimatingNode"], [240, 55, 218, 53], [240, 57, 218, 55, "animationId"], [240, 68, 218, 66], [240, 70, 218, 68, "nodeTag"], [240, 77, 218, 75], [240, 79, 218, 77, "config"], [240, 85, 218, 83], [240, 86, 218, 84], [241, 6, 219, 4], [241, 7, 219, 5], [241, 13, 219, 11], [242, 8, 220, 6, "API"], [242, 11, 220, 9], [242, 12, 220, 10, "queueOperation"], [242, 26, 220, 24], [242, 27, 220, 25, "nativeOps"], [242, 36, 220, 34], [242, 37, 220, 35, "startAnimatingNode"], [242, 55, 220, 53], [242, 57, 220, 55, "animationId"], [242, 68, 220, 66], [242, 70, 220, 68, "nodeTag"], [242, 77, 220, 75], [242, 79, 220, 77, "config"], [242, 85, 220, 83], [242, 87, 220, 85, "endCallback"], [242, 98, 220, 96], [242, 99, 220, 97], [243, 6, 221, 4], [244, 4, 222, 2], [244, 5, 222, 3], [245, 4, 223, 2, "stopAnimation"], [245, 17, 223, 15], [245, 19, 223, 17], [245, 28, 223, 26, "stopAnimation"], [245, 41, 223, 39, "stopAnimation"], [245, 42, 223, 40, "animationId"], [245, 53, 223, 51], [245, 55, 223, 53], [246, 6, 224, 4], [246, 10, 224, 4, "invariant"], [246, 28, 224, 13], [246, 30, 224, 14, "nativeOps"], [246, 39, 224, 23], [246, 41, 224, 25], [246, 82, 224, 66], [246, 83, 224, 67], [247, 6, 225, 4, "API"], [247, 9, 225, 7], [247, 10, 225, 8, "queueOperation"], [247, 24, 225, 22], [247, 25, 225, 23, "nativeOps"], [247, 34, 225, 32], [247, 35, 225, 33, "stopAnimation"], [247, 48, 225, 46], [247, 50, 225, 48, "animationId"], [247, 61, 225, 59], [247, 62, 225, 60], [248, 4, 226, 2], [248, 5, 226, 3], [249, 4, 227, 2, "setAnimatedNodeValue"], [249, 24, 227, 22], [249, 26, 227, 24], [249, 35, 227, 33, "setAnimatedNodeValue"], [249, 55, 227, 53, "setAnimatedNodeValue"], [249, 56, 227, 54, "nodeTag"], [249, 63, 227, 61], [249, 65, 227, 63, "value"], [249, 70, 227, 68], [249, 72, 227, 70], [250, 6, 228, 4], [250, 10, 228, 4, "invariant"], [250, 28, 228, 13], [250, 30, 228, 14, "nativeOps"], [250, 39, 228, 23], [250, 41, 228, 25], [250, 82, 228, 66], [250, 83, 228, 67], [251, 6, 229, 4, "API"], [251, 9, 229, 7], [251, 10, 229, 8, "queueOperation"], [251, 24, 229, 22], [251, 25, 229, 23, "nativeOps"], [251, 34, 229, 32], [251, 35, 229, 33, "setAnimatedNodeValue"], [251, 55, 229, 53], [251, 57, 229, 55, "nodeTag"], [251, 64, 229, 62], [251, 66, 229, 64, "value"], [251, 71, 229, 69], [251, 72, 229, 70], [252, 4, 230, 2], [252, 5, 230, 3], [253, 4, 231, 2, "setAnimatedNodeOffset"], [253, 25, 231, 23], [253, 27, 231, 25], [253, 36, 231, 34, "setAnimatedNodeOffset"], [253, 57, 231, 55, "setAnimatedNodeOffset"], [253, 58, 231, 56, "nodeTag"], [253, 65, 231, 63], [253, 67, 231, 65, "offset"], [253, 73, 231, 71], [253, 75, 231, 73], [254, 6, 232, 4], [254, 10, 232, 4, "invariant"], [254, 28, 232, 13], [254, 30, 232, 14, "nativeOps"], [254, 39, 232, 23], [254, 41, 232, 25], [254, 82, 232, 66], [254, 83, 232, 67], [255, 6, 233, 4, "API"], [255, 9, 233, 7], [255, 10, 233, 8, "queueOperation"], [255, 24, 233, 22], [255, 25, 233, 23, "nativeOps"], [255, 34, 233, 32], [255, 35, 233, 33, "setAnimatedNodeOffset"], [255, 56, 233, 54], [255, 58, 233, 56, "nodeTag"], [255, 65, 233, 63], [255, 67, 233, 65, "offset"], [255, 73, 233, 71], [255, 74, 233, 72], [256, 4, 234, 2], [256, 5, 234, 3], [257, 4, 235, 2, "flattenAnimatedNodeOffset"], [257, 29, 235, 27], [257, 31, 235, 29], [257, 40, 235, 38, "flattenAnimatedNodeOffset"], [257, 65, 235, 63, "flattenAnimatedNodeOffset"], [257, 66, 235, 64, "nodeTag"], [257, 73, 235, 71], [257, 75, 235, 73], [258, 6, 236, 4], [258, 10, 236, 4, "invariant"], [258, 28, 236, 13], [258, 30, 236, 14, "nativeOps"], [258, 39, 236, 23], [258, 41, 236, 25], [258, 82, 236, 66], [258, 83, 236, 67], [259, 6, 237, 4, "API"], [259, 9, 237, 7], [259, 10, 237, 8, "queueOperation"], [259, 24, 237, 22], [259, 25, 237, 23, "nativeOps"], [259, 34, 237, 32], [259, 35, 237, 33, "flattenAnimatedNodeOffset"], [259, 60, 237, 58], [259, 62, 237, 60, "nodeTag"], [259, 69, 237, 67], [259, 70, 237, 68], [260, 4, 238, 2], [260, 5, 238, 3], [261, 4, 239, 2, "extractAnimatedNodeOffset"], [261, 29, 239, 27], [261, 31, 239, 29], [261, 40, 239, 38, "extractAnimatedNodeOffset"], [261, 65, 239, 63, "extractAnimatedNodeOffset"], [261, 66, 239, 64, "nodeTag"], [261, 73, 239, 71], [261, 75, 239, 73], [262, 6, 240, 4], [262, 10, 240, 4, "invariant"], [262, 28, 240, 13], [262, 30, 240, 14, "nativeOps"], [262, 39, 240, 23], [262, 41, 240, 25], [262, 82, 240, 66], [262, 83, 240, 67], [263, 6, 241, 4, "API"], [263, 9, 241, 7], [263, 10, 241, 8, "queueOperation"], [263, 24, 241, 22], [263, 25, 241, 23, "nativeOps"], [263, 34, 241, 32], [263, 35, 241, 33, "extractAnimatedNodeOffset"], [263, 60, 241, 58], [263, 62, 241, 60, "nodeTag"], [263, 69, 241, 67], [263, 70, 241, 68], [264, 4, 242, 2], [264, 5, 242, 3], [265, 4, 243, 2, "connectAnimatedNodeToView"], [265, 29, 243, 27], [265, 31, 243, 29], [265, 40, 243, 38, "connectAnimatedNodeToView"], [265, 65, 243, 63, "connectAnimatedNodeToView"], [265, 66, 243, 64, "nodeTag"], [265, 73, 243, 71], [265, 75, 243, 73, "viewTag"], [265, 82, 243, 80], [265, 84, 243, 82], [266, 6, 244, 4], [266, 10, 244, 4, "invariant"], [266, 28, 244, 13], [266, 30, 244, 14, "nativeOps"], [266, 39, 244, 23], [266, 41, 244, 25], [266, 82, 244, 66], [266, 83, 244, 67], [267, 6, 245, 4, "API"], [267, 9, 245, 7], [267, 10, 245, 8, "queueOperation"], [267, 24, 245, 22], [267, 25, 245, 23, "nativeOps"], [267, 34, 245, 32], [267, 35, 245, 33, "connectAnimatedNodeToView"], [267, 60, 245, 58], [267, 62, 245, 60, "nodeTag"], [267, 69, 245, 67], [267, 71, 245, 69, "viewTag"], [267, 78, 245, 76], [267, 79, 245, 77], [268, 4, 246, 2], [268, 5, 246, 3], [269, 4, 247, 2, "disconnectAnimatedNodeFromView"], [269, 34, 247, 32], [269, 36, 247, 34], [269, 45, 247, 43, "disconnectAnimatedNodeFromView"], [269, 75, 247, 73, "disconnectAnimatedNodeFromView"], [269, 76, 247, 74, "nodeTag"], [269, 83, 247, 81], [269, 85, 247, 83, "viewTag"], [269, 92, 247, 90], [269, 94, 247, 92], [270, 6, 248, 4], [270, 10, 248, 4, "invariant"], [270, 28, 248, 13], [270, 30, 248, 14, "nativeOps"], [270, 39, 248, 23], [270, 41, 248, 25], [270, 82, 248, 66], [270, 83, 248, 67], [271, 6, 249, 4, "API"], [271, 9, 249, 7], [271, 10, 249, 8, "queueOperation"], [271, 24, 249, 22], [271, 25, 249, 23, "nativeOps"], [271, 34, 249, 32], [271, 35, 249, 33, "disconnectAnimatedNodeFromView"], [271, 65, 249, 63], [271, 67, 249, 65, "nodeTag"], [271, 74, 249, 72], [271, 76, 249, 74, "viewTag"], [271, 83, 249, 81], [271, 84, 249, 82], [272, 4, 250, 2], [272, 5, 250, 3], [273, 4, 251, 2, "restoreDefaultValues"], [273, 24, 251, 22], [273, 26, 251, 24], [273, 35, 251, 33, "restoreDefaultValues"], [273, 55, 251, 53, "restoreDefaultValues"], [273, 56, 251, 54, "nodeTag"], [273, 63, 251, 61], [273, 65, 251, 63], [274, 6, 252, 4], [274, 10, 252, 4, "invariant"], [274, 28, 252, 13], [274, 30, 252, 14, "nativeOps"], [274, 39, 252, 23], [274, 41, 252, 25], [274, 82, 252, 66], [274, 83, 252, 67], [275, 6, 253, 4], [276, 6, 254, 4], [276, 10, 254, 8, "nativeOps"], [276, 19, 254, 17], [276, 20, 254, 18, "restoreDefaultValues"], [276, 40, 254, 38], [276, 44, 254, 42], [276, 48, 254, 46], [276, 50, 254, 48], [277, 8, 255, 6, "API"], [277, 11, 255, 9], [277, 12, 255, 10, "queueOperation"], [277, 26, 255, 24], [277, 27, 255, 25, "nativeOps"], [277, 36, 255, 34], [277, 37, 255, 35, "restoreDefaultValues"], [277, 57, 255, 55], [277, 59, 255, 57, "nodeTag"], [277, 66, 255, 64], [277, 67, 255, 65], [278, 6, 256, 4], [279, 4, 257, 2], [279, 5, 257, 3], [280, 4, 258, 2, "dropAnimatedNode"], [280, 20, 258, 18], [280, 22, 258, 20], [280, 31, 258, 29, "dropAnimatedNode"], [280, 47, 258, 45, "dropAnimatedNode"], [280, 48, 258, 46, "tag"], [280, 51, 258, 49], [280, 53, 258, 51], [281, 6, 259, 4], [281, 10, 259, 4, "invariant"], [281, 28, 259, 13], [281, 30, 259, 14, "nativeOps"], [281, 39, 259, 23], [281, 41, 259, 25], [281, 82, 259, 66], [281, 83, 259, 67], [282, 6, 260, 4, "API"], [282, 9, 260, 7], [282, 10, 260, 8, "queueOperation"], [282, 24, 260, 22], [282, 25, 260, 23, "nativeOps"], [282, 34, 260, 32], [282, 35, 260, 33, "dropAnimatedNode"], [282, 51, 260, 49], [282, 53, 260, 51, "tag"], [282, 56, 260, 54], [282, 57, 260, 55], [283, 4, 261, 2], [283, 5, 261, 3], [284, 4, 262, 2, "addAnimatedEventToView"], [284, 26, 262, 24], [284, 28, 262, 26], [284, 37, 262, 35, "addAnimatedEventToView"], [284, 59, 262, 57, "addAnimatedEventToView"], [284, 60, 262, 58, "viewTag"], [284, 67, 262, 65], [284, 69, 262, 67, "eventName"], [284, 78, 262, 76], [284, 80, 262, 78, "eventMapping"], [284, 92, 262, 90], [284, 94, 262, 92], [285, 6, 263, 4], [285, 10, 263, 4, "invariant"], [285, 28, 263, 13], [285, 30, 263, 14, "nativeOps"], [285, 39, 263, 23], [285, 41, 263, 25], [285, 82, 263, 66], [285, 83, 263, 67], [286, 6, 264, 4, "API"], [286, 9, 264, 7], [286, 10, 264, 8, "queueOperation"], [286, 24, 264, 22], [286, 25, 264, 23, "nativeOps"], [286, 34, 264, 32], [286, 35, 264, 33, "addAnimatedEventToView"], [286, 57, 264, 55], [286, 59, 264, 57, "viewTag"], [286, 66, 264, 64], [286, 68, 264, 66, "eventName"], [286, 77, 264, 75], [286, 79, 264, 77, "eventMapping"], [286, 91, 264, 89], [286, 92, 264, 90], [287, 4, 265, 2], [287, 5, 265, 3], [288, 4, 266, 2, "removeAnimatedEventFromView"], [288, 31, 266, 29, "removeAnimatedEventFromView"], [288, 32, 266, 30, "viewTag"], [288, 39, 266, 37], [288, 41, 266, 39, "eventName"], [288, 50, 266, 48], [288, 52, 266, 50, "animatedNodeTag"], [288, 67, 266, 65], [288, 69, 266, 67], [289, 6, 267, 4], [289, 10, 267, 4, "invariant"], [289, 28, 267, 13], [289, 30, 267, 14, "nativeOps"], [289, 39, 267, 23], [289, 41, 267, 25], [289, 82, 267, 66], [289, 83, 267, 67], [290, 6, 268, 4, "API"], [290, 9, 268, 7], [290, 10, 268, 8, "queueOperation"], [290, 24, 268, 22], [290, 25, 268, 23, "nativeOps"], [290, 34, 268, 32], [290, 35, 268, 33, "removeAnimatedEventFromView"], [290, 62, 268, 60], [290, 64, 268, 62, "viewTag"], [290, 71, 268, 69], [290, 73, 268, 71, "eventName"], [290, 82, 268, 80], [290, 84, 268, 82, "animatedNodeTag"], [290, 99, 268, 97], [290, 100, 268, 98], [291, 4, 269, 2], [292, 2, 270, 0], [292, 3, 270, 1], [293, 2, 271, 0], [293, 11, 271, 9, "setupGlobalEventEmitterListeners"], [293, 43, 271, 41, "setupGlobalEventEmitterListeners"], [293, 44, 271, 41], [293, 46, 271, 44], [294, 4, 272, 2, "globalEventEmitterGetValueListener"], [294, 38, 272, 36], [294, 41, 272, 39, "RCTDeviceEventEmitter"], [294, 71, 272, 60], [294, 72, 272, 61, "addListener"], [294, 83, 272, 72], [294, 84, 272, 73], [294, 116, 272, 105], [294, 118, 272, 107], [294, 128, 272, 117, "params"], [294, 134, 272, 123], [294, 136, 272, 125], [295, 6, 273, 4], [295, 10, 273, 8, "tag"], [295, 13, 273, 11], [295, 16, 273, 14, "params"], [295, 22, 273, 20], [295, 23, 273, 21, "tag"], [295, 26, 273, 24], [296, 6, 274, 4], [296, 10, 274, 8, "callback"], [296, 18, 274, 16], [296, 21, 274, 19, "eventListenerGetValueCallbacks"], [296, 51, 274, 49], [296, 52, 274, 50, "tag"], [296, 55, 274, 53], [296, 56, 274, 54], [297, 6, 275, 4], [297, 10, 275, 8], [297, 11, 275, 9, "callback"], [297, 19, 275, 17], [297, 21, 275, 19], [298, 8, 276, 6], [299, 6, 277, 4], [300, 6, 278, 4, "callback"], [300, 14, 278, 12], [300, 15, 278, 13, "params"], [300, 21, 278, 19], [300, 22, 278, 20, "value"], [300, 27, 278, 25], [300, 28, 278, 26], [301, 6, 279, 4], [301, 13, 279, 11, "eventListenerGetValueCallbacks"], [301, 43, 279, 41], [301, 44, 279, 42, "tag"], [301, 47, 279, 45], [301, 48, 279, 46], [302, 4, 280, 2], [302, 5, 280, 3], [302, 6, 280, 4], [303, 4, 281, 2, "globalEventEmitterAnimationFinishedListener"], [303, 47, 281, 45], [303, 50, 281, 48, "RCTDeviceEventEmitter"], [303, 80, 281, 69], [303, 81, 281, 70, "addListener"], [303, 92, 281, 81], [303, 93, 281, 82], [303, 134, 281, 123], [303, 136, 281, 125], [303, 146, 281, 135, "params"], [303, 152, 281, 141], [303, 154, 281, 143], [304, 6, 282, 4], [304, 10, 282, 8, "animationId"], [304, 21, 282, 19], [304, 24, 282, 22, "params"], [304, 30, 282, 28], [304, 31, 282, 29, "animationId"], [304, 42, 282, 40], [305, 6, 283, 4], [305, 10, 283, 8, "callback"], [305, 18, 283, 16], [305, 21, 283, 19, "eventListenerAnimationFinishedCallbacks"], [305, 60, 283, 58], [305, 61, 283, 59, "animationId"], [305, 72, 283, 70], [305, 73, 283, 71], [306, 6, 284, 4], [306, 10, 284, 8], [306, 11, 284, 9, "callback"], [306, 19, 284, 17], [306, 21, 284, 19], [307, 8, 285, 6], [308, 6, 286, 4], [309, 6, 287, 4, "callback"], [309, 14, 287, 12], [309, 15, 287, 13, "params"], [309, 21, 287, 19], [309, 22, 287, 20], [310, 6, 288, 4], [310, 13, 288, 11, "eventListenerAnimationFinishedCallbacks"], [310, 52, 288, 50], [310, 53, 288, 51, "animationId"], [310, 64, 288, 62], [310, 65, 288, 63], [311, 4, 289, 2], [311, 5, 289, 3], [311, 6, 289, 4], [312, 2, 290, 0], [314, 2, 292, 0], [315, 0, 293, 0], [316, 0, 294, 0], [317, 0, 295, 0], [318, 0, 296, 0], [319, 0, 297, 0], [320, 2, 298, 0], [320, 6, 298, 4, "SUPPORTED_COLOR_STYLES"], [320, 28, 298, 26], [320, 31, 298, 29], [321, 4, 299, 2, "backgroundColor"], [321, 19, 299, 17], [321, 21, 299, 19], [321, 25, 299, 23], [322, 4, 300, 2, "borderBottomColor"], [322, 21, 300, 19], [322, 23, 300, 21], [322, 27, 300, 25], [323, 4, 301, 2, "borderColor"], [323, 15, 301, 13], [323, 17, 301, 15], [323, 21, 301, 19], [324, 4, 302, 2, "borderEndColor"], [324, 18, 302, 16], [324, 20, 302, 18], [324, 24, 302, 22], [325, 4, 303, 2, "borderLeftColor"], [325, 19, 303, 17], [325, 21, 303, 19], [325, 25, 303, 23], [326, 4, 304, 2, "borderRightColor"], [326, 20, 304, 18], [326, 22, 304, 20], [326, 26, 304, 24], [327, 4, 305, 2, "borderStartColor"], [327, 20, 305, 18], [327, 22, 305, 20], [327, 26, 305, 24], [328, 4, 306, 2, "borderTopColor"], [328, 18, 306, 16], [328, 20, 306, 18], [328, 24, 306, 22], [329, 4, 307, 2, "color"], [329, 9, 307, 7], [329, 11, 307, 9], [329, 15, 307, 13], [330, 4, 308, 2, "tintColor"], [330, 13, 308, 11], [330, 15, 308, 13], [331, 2, 309, 0], [331, 3, 309, 1], [332, 2, 310, 0], [332, 6, 310, 4, "SUPPORTED_STYLES"], [332, 22, 310, 20], [332, 25, 310, 23], [332, 29, 310, 23, "_objectSpread"], [332, 51, 310, 36], [332, 53, 310, 37], [332, 57, 310, 37, "_objectSpread"], [332, 79, 310, 50], [332, 81, 310, 51], [332, 82, 310, 52], [332, 83, 310, 53], [332, 85, 310, 55, "SUPPORTED_COLOR_STYLES"], [332, 107, 310, 77], [332, 108, 310, 78], [332, 110, 310, 80], [332, 111, 310, 81], [332, 112, 310, 82], [332, 114, 310, 84], [333, 4, 311, 2, "borderBottomEndRadius"], [333, 25, 311, 23], [333, 27, 311, 25], [333, 31, 311, 29], [334, 4, 312, 2, "borderBottomLeftRadius"], [334, 26, 312, 24], [334, 28, 312, 26], [334, 32, 312, 30], [335, 4, 313, 2, "borderBottomRightRadius"], [335, 27, 313, 25], [335, 29, 313, 27], [335, 33, 313, 31], [336, 4, 314, 2, "borderBottomStartRadius"], [336, 27, 314, 25], [336, 29, 314, 27], [336, 33, 314, 31], [337, 4, 315, 2, "borderRadius"], [337, 16, 315, 14], [337, 18, 315, 16], [337, 22, 315, 20], [338, 4, 316, 2, "borderTopEndRadius"], [338, 22, 316, 20], [338, 24, 316, 22], [338, 28, 316, 26], [339, 4, 317, 2, "borderTopLeftRadius"], [339, 23, 317, 21], [339, 25, 317, 23], [339, 29, 317, 27], [340, 4, 318, 2, "borderTopRightRadius"], [340, 24, 318, 22], [340, 26, 318, 24], [340, 30, 318, 28], [341, 4, 319, 2, "borderTopStartRadius"], [341, 24, 319, 22], [341, 26, 319, 24], [341, 30, 319, 28], [342, 4, 320, 2, "elevation"], [342, 13, 320, 11], [342, 15, 320, 13], [342, 19, 320, 17], [343, 4, 321, 2, "opacity"], [343, 11, 321, 9], [343, 13, 321, 11], [343, 17, 321, 15], [344, 4, 322, 2, "transform"], [344, 13, 322, 11], [344, 15, 322, 13], [344, 19, 322, 17], [345, 4, 323, 2, "zIndex"], [345, 10, 323, 8], [345, 12, 323, 10], [345, 16, 323, 14], [346, 4, 324, 2], [347, 4, 325, 2, "shadowOpacity"], [347, 17, 325, 15], [347, 19, 325, 17], [347, 23, 325, 21], [348, 4, 326, 2, "shadowRadius"], [348, 16, 326, 14], [348, 18, 326, 16], [348, 22, 326, 20], [349, 4, 327, 2], [350, 4, 328, 2, "scaleX"], [350, 10, 328, 8], [350, 12, 328, 10], [350, 16, 328, 14], [351, 4, 329, 2, "scaleY"], [351, 10, 329, 8], [351, 12, 329, 10], [351, 16, 329, 14], [352, 4, 330, 2, "translateX"], [352, 14, 330, 12], [352, 16, 330, 14], [352, 20, 330, 18], [353, 4, 331, 2, "translateY"], [353, 14, 331, 12], [353, 16, 331, 14], [354, 2, 332, 0], [354, 3, 332, 1], [354, 4, 332, 2], [355, 2, 333, 0], [355, 6, 333, 4, "SUPPORTED_TRANSFORMS"], [355, 26, 333, 24], [355, 29, 333, 27], [356, 4, 334, 2, "translateX"], [356, 14, 334, 12], [356, 16, 334, 14], [356, 20, 334, 18], [357, 4, 335, 2, "translateY"], [357, 14, 335, 12], [357, 16, 335, 14], [357, 20, 335, 18], [358, 4, 336, 2, "scale"], [358, 9, 336, 7], [358, 11, 336, 9], [358, 15, 336, 13], [359, 4, 337, 2, "scaleX"], [359, 10, 337, 8], [359, 12, 337, 10], [359, 16, 337, 14], [360, 4, 338, 2, "scaleY"], [360, 10, 338, 8], [360, 12, 338, 10], [360, 16, 338, 14], [361, 4, 339, 2, "rotate"], [361, 10, 339, 8], [361, 12, 339, 10], [361, 16, 339, 14], [362, 4, 340, 2, "rotateX"], [362, 11, 340, 9], [362, 13, 340, 11], [362, 17, 340, 15], [363, 4, 341, 2, "rotateY"], [363, 11, 341, 9], [363, 13, 341, 11], [363, 17, 341, 15], [364, 4, 342, 2, "rotateZ"], [364, 11, 342, 9], [364, 13, 342, 11], [364, 17, 342, 15], [365, 4, 343, 2, "perspective"], [365, 15, 343, 13], [365, 17, 343, 15], [366, 2, 344, 0], [366, 3, 344, 1], [367, 2, 345, 0], [367, 6, 345, 4, "SUPPORTED_INTERPOLATION_PARAMS"], [367, 36, 345, 34], [367, 39, 345, 37], [368, 4, 346, 2, "inputRange"], [368, 14, 346, 12], [368, 16, 346, 14], [368, 20, 346, 18], [369, 4, 347, 2, "outputRange"], [369, 15, 347, 13], [369, 17, 347, 15], [369, 21, 347, 19], [370, 4, 348, 2, "extrapolate"], [370, 15, 348, 13], [370, 17, 348, 15], [370, 21, 348, 19], [371, 4, 349, 2, "extrapolateRight"], [371, 20, 349, 18], [371, 22, 349, 20], [371, 26, 349, 24], [372, 4, 350, 2, "extrapolateLeft"], [372, 19, 350, 17], [372, 21, 350, 19], [373, 2, 351, 0], [373, 3, 351, 1], [374, 2, 352, 0], [374, 11, 352, 9, "addWhitelistedStyleProp"], [374, 34, 352, 32, "addWhitelistedStyleProp"], [374, 35, 352, 33, "prop"], [374, 39, 352, 37], [374, 41, 352, 39], [375, 4, 353, 2, "SUPPORTED_STYLES"], [375, 20, 353, 18], [375, 21, 353, 19, "prop"], [375, 25, 353, 23], [375, 26, 353, 24], [375, 29, 353, 27], [375, 33, 353, 31], [376, 2, 354, 0], [377, 2, 355, 0], [377, 11, 355, 9, "addWhitelistedTransformProp"], [377, 38, 355, 36, "addWhitelistedTransformProp"], [377, 39, 355, 37, "prop"], [377, 43, 355, 41], [377, 45, 355, 43], [378, 4, 356, 2, "SUPPORTED_TRANSFORMS"], [378, 24, 356, 22], [378, 25, 356, 23, "prop"], [378, 29, 356, 27], [378, 30, 356, 28], [378, 33, 356, 31], [378, 37, 356, 35], [379, 2, 357, 0], [380, 2, 358, 0], [380, 11, 358, 9, "addWhitelistedInterpolationParam"], [380, 43, 358, 41, "addWhitelistedInterpolationParam"], [380, 44, 358, 42, "param"], [380, 49, 358, 47], [380, 51, 358, 49], [381, 4, 359, 2, "SUPPORTED_INTERPOLATION_PARAMS"], [381, 34, 359, 32], [381, 35, 359, 33, "param"], [381, 40, 359, 38], [381, 41, 359, 39], [381, 44, 359, 42], [381, 48, 359, 46], [382, 2, 360, 0], [383, 2, 361, 0], [383, 11, 361, 9, "isSupportedColorStyleProp"], [383, 36, 361, 34, "isSupportedColorStyleProp"], [383, 37, 361, 35, "prop"], [383, 41, 361, 39], [383, 43, 361, 41], [384, 4, 362, 2], [384, 11, 362, 9, "SUPPORTED_COLOR_STYLES"], [384, 33, 362, 31], [384, 34, 362, 32, "hasOwnProperty"], [384, 48, 362, 46], [384, 49, 362, 47, "prop"], [384, 53, 362, 51], [384, 54, 362, 52], [385, 2, 363, 0], [386, 2, 364, 0], [386, 11, 364, 9, "isSupportedStyleProp"], [386, 31, 364, 29, "isSupportedStyleProp"], [386, 32, 364, 30, "prop"], [386, 36, 364, 34], [386, 38, 364, 36], [387, 4, 365, 2], [387, 11, 365, 9, "SUPPORTED_STYLES"], [387, 27, 365, 25], [387, 28, 365, 26, "hasOwnProperty"], [387, 42, 365, 40], [387, 43, 365, 41, "prop"], [387, 47, 365, 45], [387, 48, 365, 46], [388, 2, 366, 0], [389, 2, 367, 0], [389, 11, 367, 9, "isSupportedTransformProp"], [389, 35, 367, 33, "isSupportedTransformProp"], [389, 36, 367, 34, "prop"], [389, 40, 367, 38], [389, 42, 367, 40], [390, 4, 368, 2], [390, 11, 368, 9, "SUPPORTED_TRANSFORMS"], [390, 31, 368, 29], [390, 32, 368, 30, "hasOwnProperty"], [390, 46, 368, 44], [390, 47, 368, 45, "prop"], [390, 51, 368, 49], [390, 52, 368, 50], [391, 2, 369, 0], [392, 2, 370, 0], [392, 11, 370, 9, "isSupportedInterpolationParam"], [392, 40, 370, 38, "isSupportedInterpolationParam"], [392, 41, 370, 39, "param"], [392, 46, 370, 44], [392, 48, 370, 46], [393, 4, 371, 2], [393, 11, 371, 9, "SUPPORTED_INTERPOLATION_PARAMS"], [393, 41, 371, 39], [393, 42, 371, 40, "hasOwnProperty"], [393, 56, 371, 54], [393, 57, 371, 55, "param"], [393, 62, 371, 60], [393, 63, 371, 61], [394, 2, 372, 0], [395, 2, 373, 0], [395, 11, 373, 9, "validateTransform"], [395, 28, 373, 26, "validateTransform"], [395, 29, 373, 27, "configs"], [395, 36, 373, 34], [395, 38, 373, 36], [396, 4, 374, 2, "configs"], [396, 11, 374, 9], [396, 12, 374, 10, "for<PERSON>ach"], [396, 19, 374, 17], [396, 20, 374, 18, "config"], [396, 26, 374, 24], [396, 30, 374, 28], [397, 6, 375, 4], [397, 10, 375, 8], [397, 11, 375, 9, "isSupportedTransformProp"], [397, 35, 375, 33], [397, 36, 375, 34, "config"], [397, 42, 375, 40], [397, 43, 375, 41, "property"], [397, 51, 375, 49], [397, 52, 375, 50], [397, 54, 375, 52], [398, 8, 376, 6], [398, 14, 376, 12], [398, 18, 376, 16, "Error"], [398, 23, 376, 21], [398, 24, 376, 22], [398, 36, 376, 34], [398, 39, 376, 37, "config"], [398, 45, 376, 43], [398, 46, 376, 44, "property"], [398, 54, 376, 52], [398, 57, 376, 55], [398, 103, 376, 101], [398, 104, 376, 102], [399, 6, 377, 4], [400, 4, 378, 2], [400, 5, 378, 3], [400, 6, 378, 4], [401, 2, 379, 0], [402, 2, 380, 0], [402, 11, 380, 9, "validateStyles"], [402, 25, 380, 23, "validateStyles"], [402, 26, 380, 24, "styles"], [402, 32, 380, 30], [402, 34, 380, 32], [403, 4, 381, 2], [403, 9, 381, 7], [403, 13, 381, 11, "_key2"], [403, 18, 381, 16], [403, 22, 381, 20, "styles"], [403, 28, 381, 26], [403, 30, 381, 28], [404, 6, 382, 4], [404, 10, 382, 8], [404, 11, 382, 9, "isSupportedStyleProp"], [404, 31, 382, 29], [404, 32, 382, 30, "_key2"], [404, 37, 382, 35], [404, 38, 382, 36], [404, 40, 382, 38], [405, 8, 383, 6], [405, 14, 383, 12], [405, 18, 383, 16, "Error"], [405, 23, 383, 21], [405, 24, 383, 22], [405, 42, 383, 40], [405, 45, 383, 43, "_key2"], [405, 50, 383, 48], [405, 53, 383, 51], [405, 99, 383, 97], [405, 100, 383, 98], [406, 6, 384, 4], [407, 4, 385, 2], [408, 2, 386, 0], [409, 2, 387, 0], [409, 11, 387, 9, "validateInterpolation"], [409, 32, 387, 30, "validateInterpolation"], [409, 33, 387, 31, "config"], [409, 39, 387, 37], [409, 41, 387, 39], [410, 4, 388, 2], [410, 9, 388, 7], [410, 13, 388, 11, "_key3"], [410, 18, 388, 16], [410, 22, 388, 20, "config"], [410, 28, 388, 26], [410, 30, 388, 28], [411, 6, 389, 4], [411, 10, 389, 8], [411, 11, 389, 9, "isSupportedInterpolationParam"], [411, 40, 389, 38], [411, 41, 389, 39, "_key3"], [411, 46, 389, 44], [411, 47, 389, 45], [411, 49, 389, 47], [412, 8, 390, 6], [412, 14, 390, 12], [412, 18, 390, 16, "Error"], [412, 23, 390, 21], [412, 24, 390, 22], [412, 50, 390, 48], [412, 53, 390, 51, "_key3"], [412, 58, 390, 56], [412, 61, 390, 59], [412, 107, 390, 105], [412, 108, 390, 106], [413, 6, 391, 4], [414, 4, 392, 2], [415, 2, 393, 0], [416, 2, 394, 0], [416, 11, 394, 9, "generateNewNodeTag"], [416, 29, 394, 27, "generateNewNodeTag"], [416, 30, 394, 27], [416, 32, 394, 30], [417, 4, 395, 2], [417, 11, 395, 9, "__nativeAnimatedNodeTagCount"], [417, 39, 395, 37], [417, 41, 395, 39], [418, 2, 396, 0], [419, 2, 397, 0], [419, 11, 397, 9, "generateNewAnimationId"], [419, 33, 397, 31, "generateNewAnimationId"], [419, 34, 397, 31], [419, 36, 397, 34], [420, 4, 398, 2], [420, 11, 398, 9, "__nativeAnimationIdCount"], [420, 35, 398, 33], [420, 37, 398, 35], [421, 2, 399, 0], [422, 2, 400, 0], [422, 11, 400, 9, "assertNativeAnimatedModule"], [422, 37, 400, 35, "assertNativeAnimatedModule"], [422, 38, 400, 35], [422, 40, 400, 38], [423, 4, 401, 2], [423, 8, 401, 2, "invariant"], [423, 26, 401, 11], [423, 28, 401, 12, "NativeAnimatedModule"], [423, 48, 401, 32], [423, 50, 401, 34], [423, 91, 401, 75], [423, 92, 401, 76], [424, 2, 402, 0], [425, 2, 403, 0], [425, 6, 403, 4, "_warnedMissingNativeAnimated"], [425, 34, 403, 32], [425, 37, 403, 35], [425, 42, 403, 40], [426, 2, 404, 0], [426, 11, 404, 9, "shouldUseNativeDriver"], [426, 32, 404, 30, "shouldUseNativeDriver"], [426, 33, 404, 31, "config"], [426, 39, 404, 37], [426, 41, 404, 39], [427, 4, 405, 2], [427, 8, 405, 6, "config"], [427, 14, 405, 12], [427, 15, 405, 13, "useNativeDriver"], [427, 30, 405, 28], [427, 34, 405, 32], [427, 38, 405, 36], [427, 40, 405, 38], [428, 6, 406, 4, "console"], [428, 13, 406, 11], [428, 14, 406, 12, "warn"], [428, 18, 406, 16], [428, 19, 406, 17], [428, 87, 406, 85], [428, 90, 406, 88], [428, 146, 406, 144], [428, 147, 406, 145], [429, 4, 407, 2], [430, 4, 408, 2], [430, 8, 408, 6, "config"], [430, 14, 408, 12], [430, 15, 408, 13, "useNativeDriver"], [430, 30, 408, 28], [430, 35, 408, 33], [430, 39, 408, 37], [430, 43, 408, 41], [430, 44, 408, 42, "NativeAnimatedModule"], [430, 64, 408, 62], [430, 66, 408, 64], [431, 6, 409, 4], [431, 10, 409, 8], [431, 11, 409, 9, "_warnedMissingNativeAnimated"], [431, 39, 409, 37], [431, 41, 409, 39], [432, 8, 410, 6, "console"], [432, 15, 410, 13], [432, 16, 410, 14, "warn"], [432, 20, 410, 18], [432, 21, 410, 19], [432, 87, 410, 85], [432, 90, 410, 88], [432, 159, 410, 157], [432, 162, 410, 160], [432, 227, 410, 225], [432, 230, 410, 228], [432, 251, 410, 249], [432, 254, 410, 252], [432, 412, 410, 410], [432, 413, 410, 411], [433, 8, 411, 6, "_warnedMissingNativeAnimated"], [433, 36, 411, 34], [433, 39, 411, 37], [433, 43, 411, 41], [434, 6, 412, 4], [435, 6, 413, 4], [435, 13, 413, 11], [435, 18, 413, 16], [436, 4, 414, 2], [437, 4, 415, 2], [437, 11, 415, 9, "config"], [437, 17, 415, 15], [437, 18, 415, 16, "useNativeDriver"], [437, 33, 415, 31], [437, 37, 415, 35], [437, 42, 415, 40], [438, 2, 416, 0], [439, 2, 417, 0], [439, 11, 417, 9, "transformDataType"], [439, 28, 417, 26, "transformDataType"], [439, 29, 417, 27, "value"], [439, 34, 417, 32], [439, 36, 417, 34], [440, 4, 418, 2], [441, 4, 419, 2], [442, 4, 420, 2], [442, 8, 420, 6], [442, 15, 420, 13, "value"], [442, 20, 420, 18], [442, 25, 420, 23], [442, 33, 420, 31], [442, 35, 420, 33], [443, 6, 421, 4], [443, 13, 421, 11, "value"], [443, 18, 421, 16], [444, 4, 422, 2], [445, 4, 423, 2], [445, 8, 423, 6], [445, 14, 423, 12], [445, 15, 423, 13, "test"], [445, 19, 423, 17], [445, 20, 423, 18, "value"], [445, 25, 423, 23], [445, 26, 423, 24], [445, 28, 423, 26], [446, 6, 424, 4], [446, 10, 424, 8, "degrees"], [446, 17, 424, 15], [446, 20, 424, 18, "parseFloat"], [446, 30, 424, 28], [446, 31, 424, 29, "value"], [446, 36, 424, 34], [446, 37, 424, 35], [446, 41, 424, 39], [446, 42, 424, 40], [447, 6, 425, 4], [447, 10, 425, 8, "radians"], [447, 17, 425, 15], [447, 20, 425, 18, "degrees"], [447, 27, 425, 25], [447, 30, 425, 28, "Math"], [447, 34, 425, 32], [447, 35, 425, 33, "PI"], [447, 37, 425, 35], [447, 40, 425, 38], [447, 45, 425, 43], [448, 6, 426, 4], [448, 13, 426, 11, "radians"], [448, 20, 426, 18], [449, 4, 427, 2], [449, 5, 427, 3], [449, 11, 427, 9], [450, 6, 428, 4], [450, 13, 428, 11, "value"], [450, 18, 428, 16], [451, 4, 429, 2], [452, 2, 430, 0], [453, 2, 430, 1], [453, 6, 430, 1, "_default"], [453, 14, 430, 1], [453, 17, 430, 1, "exports"], [453, 24, 430, 1], [453, 25, 430, 1, "default"], [453, 32, 430, 1], [453, 35, 432, 15], [454, 4, 433, 2, "API"], [454, 7, 433, 5], [455, 4, 434, 2, "isSupportedColorStyleProp"], [455, 29, 434, 27], [456, 4, 435, 2, "isSupportedStyleProp"], [456, 24, 435, 22], [457, 4, 436, 2, "isSupportedTransformProp"], [457, 28, 436, 26], [458, 4, 437, 2, "isSupportedInterpolationParam"], [458, 33, 437, 31], [459, 4, 438, 2, "addWhitelistedStyleProp"], [459, 27, 438, 25], [460, 4, 439, 2, "addWhitelistedTransformProp"], [460, 31, 439, 29], [461, 4, 440, 2, "addWhitelistedInterpolationParam"], [461, 36, 440, 34], [462, 4, 441, 2, "validateStyles"], [462, 18, 441, 16], [463, 4, 442, 2, "validateTransform"], [463, 21, 442, 19], [464, 4, 443, 2, "validateInterpolation"], [464, 25, 443, 23], [465, 4, 444, 2, "generateNewNodeTag"], [465, 22, 444, 20], [466, 4, 445, 2, "generateNewAnimationId"], [466, 26, 445, 24], [467, 4, 446, 2, "assertNativeAnimatedModule"], [467, 30, 446, 28], [468, 4, 447, 2, "shouldUseNativeDriver"], [468, 25, 447, 23], [469, 4, 448, 2, "transformDataType"], [469, 21, 448, 19], [470, 4, 449, 2], [471, 4, 450, 2], [472, 4, 451, 2], [472, 8, 451, 6, "nativeEventEmitter"], [472, 26, 451, 24, "nativeEventEmitter"], [472, 27, 451, 24], [472, 29, 451, 27], [473, 6, 452, 4], [473, 10, 452, 8], [473, 11, 452, 9, "nativeEventEmitter"], [473, 29, 452, 27], [473, 31, 452, 29], [474, 8, 453, 6, "nativeEventEmitter"], [474, 26, 453, 24], [474, 29, 453, 27], [474, 33, 453, 31, "NativeEventEmitter"], [474, 60, 453, 49], [475, 8, 454, 6], [476, 8, 455, 6], [477, 8, 456, 6, "Platform"], [477, 25, 456, 14], [477, 26, 456, 15, "OS"], [477, 28, 456, 17], [477, 33, 456, 22], [477, 38, 456, 27], [477, 41, 456, 30], [477, 45, 456, 34], [477, 48, 456, 37, "NativeAnimatedModule"], [477, 68, 456, 57], [477, 69, 456, 58], [478, 6, 457, 4], [479, 6, 458, 4], [479, 13, 458, 11, "nativeEventEmitter"], [479, 31, 458, 29], [480, 4, 459, 2], [481, 2, 460, 0], [481, 3, 460, 1], [482, 0, 460, 1], [482, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "apis.reduce$argument_0", "getValue", "setWaitingForIdentifier", "unsetWaitingForIdentifier", "disableQueue", "flushQueue", "queueOperation", "queue.push$argument_0", "createAnimatedNode", "updateAnimatedNodeConfig", "startListeningToAnimatedNodeValue", "stopListeningToAnimatedNodeValue", "connectAnimatedNodes", "disconnectAnimatedNodes", "startAnimatingNode", "stopAnimation", "setAnimatedNodeValue", "setAnimatedNodeOffset", "flattenAnimatedNodeOffset", "extractAnimatedNodeOffset", "connectAnimatedNodeToView", "disconnectAnimatedNodeFromView", "restoreDefaultValues", "dropAnimatedNode", "addAnimatedEventToView", "API.removeAnimatedEventFromView", "setupGlobalEventEmitterListeners", "RCTDeviceEventEmitter.addListener$argument_1", "addWhitelistedStyleProp", "addWhitelistedTransformProp", "addWhitelistedInterpolationParam", "isSupportedColorStyleProp", "isSupportedStyleProp", "isSupportedTransformProp", "isSupportedInterpolationParam", "validateTransform", "configs.forEach$argument_0", "validateStyles", "validateInterpolation", "generateNewNodeTag", "generateNewAnimationId", "assertNativeAnimatedModule", "shouldUseNativeDriver", "transformDataType", "default.get__nativeEventEmitter"], "mappings": "AAA;sCCoC;qBC2C;GDK;CDC;YGO;GHW;2BIC;GJM;6BKC;GLM;gBMC;GNS;cOC;GPmC;kBQC;iBCe,iBD;GRI;sBUC;GVG;4BWC;GXK;qCYC;GZG;oCaC;GbG;wBcC;GdG;2BeC;GfG;sBgBC;GhBW;iBiBC;GjBG;wBkBC;GlBG;yBmBC;GnBG;6BoBC;GpBG;6BqBC;GrBG;6BsBC;GtBG;kCuBC;GvBG;wBwBC;GxBM;oByBC;GzBG;0B0BC;G1BG;E2BC;G3BG;A4BE;2GCC;GDQ;6HCC;GDQ;C5BC;A8B8D;C9BE;A+BC;C/BE;AgCC;ChCE;AiCC;CjCE;AkCC;ClCE;AmCC;CnCE;AoCC;CpCE;AqCC;kBCC;GDI;CrCC;AuCC;CvCM;AwCC;CxCM;AyCC;CzCE;A0CC;C1CE;A2CC;C3CE;A4CE;C5CY;A6CC;C7Ca;E8CqB;G9CQ"}}, "type": "js/module"}]}