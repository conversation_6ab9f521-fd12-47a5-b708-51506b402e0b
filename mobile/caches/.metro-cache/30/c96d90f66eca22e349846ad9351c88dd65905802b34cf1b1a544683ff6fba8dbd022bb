{"dependencies": [{"name": "./errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 46, "index": 61}}], "key": "sBFAilsnlkNTfGhyvhhjLjsyBXM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Bezier = void 0;\n  var _errors = require(_dependencyMap[0], \"./errors.js\");\n  /**\n   * https://github.com/gre/bezier-easing BezierEasing - use bezier curve for\n   * transition easing function by <PERSON><PERSON><PERSON><PERSON> 2014 - 2015 – MIT License\n   */\n\n  // These values are established by empiricism with tests (tradeoff: performance VS precision)\n\n  const NEWTON_ITERATIONS = 4;\n  const NEWTON_MIN_SLOPE = 0.001;\n  const SUBDIVISION_PRECISION = 0.0000001;\n  const SUBDIVISION_MAX_ITERATIONS = 10;\n  const kSplineTableSize = 11;\n  const kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\n  const _worklet_8828646082676_init_data = {\n    code: \"function A_reactNativeReanimated_BezierJs1(aA1,aA2){return 1.0-3.0*aA2*****aA1;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"A_reactNativeReanimated_BezierJs1\\\",\\\"aA1\\\",\\\"aA2\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AAiBA,SAAAA,iCAAqBA,CAAAC,GAAA,CAAAC,GAAA,EAGnB,MAAO,IAAG,CAAG,GAAG,CAAGA,GAAG,CAAG,GAAG,CAAGD,GAAG,CACpC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const A = function () {\n    const _e = [new global.Error(), 1, -27];\n    const A = function (aA1, aA2) {\n      return 1.0 - 3.0 * aA2 + 3.0 * aA1;\n    };\n    A.__closure = {};\n    A.__workletHash = 8828646082676;\n    A.__initData = _worklet_8828646082676_init_data;\n    A.__stackDetails = _e;\n    return A;\n  }();\n  const _worklet_4203953384949_init_data = {\n    code: \"function B_reactNativeReanimated_BezierJs2(aA1,aA2){return 3.0*aA2-6.0*aA1;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"B_reactNativeReanimated_BezierJs2\\\",\\\"aA1\\\",\\\"aA2\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AAsBA,SAAAA,iCAAqBA,CAAAC,GAAA,CAAAC,GAAA,EAGnB,MAAO,IAAG,CAAGA,GAAG,CAAG,GAAG,CAAGD,GAAG,CAC9B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const B = function () {\n    const _e = [new global.Error(), 1, -27];\n    const B = function (aA1, aA2) {\n      return 3.0 * aA2 - 6.0 * aA1;\n    };\n    B.__closure = {};\n    B.__workletHash = 4203953384949;\n    B.__initData = _worklet_4203953384949_init_data;\n    B.__stackDetails = _e;\n    return B;\n  }();\n  const _worklet_8388140024310_init_data = {\n    code: \"function C_reactNativeReanimated_BezierJs3(aA1){return 3.0*aA1;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"C_reactNativeReanimated_BezierJs3\\\",\\\"aA1\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AA2BA,SAAAA,iCAAgBA,CAAAC,GAAA,EAGd,MAAO,IAAG,CAAGA,GAAG,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const C = function () {\n    const _e = [new global.Error(), 1, -27];\n    const C = function (aA1) {\n      return 3.0 * aA1;\n    };\n    C.__closure = {};\n    C.__workletHash = 8388140024310;\n    C.__initData = _worklet_8388140024310_init_data;\n    C.__stackDetails = _e;\n    return C;\n  }(); // Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\n  const _worklet_15740570894910_init_data = {\n    code: \"function calcBezier_reactNativeReanimated_BezierJs4(aT,aA1,aA2){const{A,B,C}=this.__closure;return((A(aA1,aA2)*aT+B(aA1,aA2))*aT+C(aA1))*aT;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"calcBezier_reactNativeReanimated_BezierJs4\\\",\\\"aT\\\",\\\"aA1\\\",\\\"aA2\\\",\\\"A\\\",\\\"B\\\",\\\"C\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AAiCA,SAAAA,2CAAAC,EAAA,CAAAC,GAAA,CAAAC,GAAA,QAAAC,CAAA,CAAAC,CAAA,CAAAC,CAAA,OAAAC,SAAA,SAAAH,CAAA,CAAAF,GAAA,CAAAC,GAAA,EAAAF,EAAA,CAAAI,CAAA,CAAAH,GAAA,CAAAC,GAAA,GAAAF,EAAA,CAAAK,CAAA,CAAAJ,GAAA,GAAAD,EAAA,CACA\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const calcBezier = function () {\n    const _e = [new global.Error(), -4, -27];\n    const calcBezier = function (aT, aA1, aA2) {\n      return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\n    };\n    calcBezier.__closure = {\n      A,\n      B,\n      C\n    };\n    calcBezier.__workletHash = 15740570894910;\n    calcBezier.__initData = _worklet_15740570894910_init_data;\n    calcBezier.__stackDetails = _e;\n    return calcBezier;\n  }(); // Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\n  const _worklet_4162364291299_init_data = {\n    code: \"function getSlope_reactNativeReanimated_BezierJs5(aT,aA1,aA2){const{A,B,C}=this.__closure;return 3.0*A(aA1,aA2)*aT*aT*****B(aA1,aA2)*aT+C(aA1);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getSlope_reactNativeReanimated_BezierJs5\\\",\\\"aT\\\",\\\"aA1\\\",\\\"aA2\\\",\\\"A\\\",\\\"B\\\",\\\"C\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AAwCA,SAAAA,yCAAAC,EAAA,CAAAC,GAAA,CAAAC,GAAA,QAAAC,CAAA,CAAAC,CAAA,CAAAC,CAAA,OAAAC,SAAA,YAAAH,CAAA,CAAAF,GAAA,CAAAC,GAAA,EAAAF,EAAA,CAAAA,EAAA,KAAAI,CAAA,CAAAH,GAAA,CAAAC,GAAA,EAAAF,EAAA,CAAAK,CAAA,CAAAJ,GAAA,EACA\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const getSlope = function () {\n    const _e = [new global.Error(), -4, -27];\n    const getSlope = function (aT, aA1, aA2) {\n      return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\n    };\n    getSlope.__closure = {\n      A,\n      B,\n      C\n    };\n    getSlope.__workletHash = 4162364291299;\n    getSlope.__initData = _worklet_4162364291299_init_data;\n    getSlope.__stackDetails = _e;\n    return getSlope;\n  }();\n  const _worklet_9166841024279_init_data = {\n    code: \"function binarySubdivide_reactNativeReanimated_BezierJs6(aX,aA,aB,mX1,mX2){const{calcBezier,SUBDIVISION_PRECISION,SUBDIVISION_MAX_ITERATIONS}=this.__closure;let currentX;let currentT;let i=0;do{currentT=aA+(aB-aA)/2.0;currentX=calcBezier(currentT,mX1,mX2)-aX;if(currentX>0.0){aB=currentT;}else{aA=currentT;}}while(Math.abs(currentX)>SUBDIVISION_PRECISION&&++i<SUBDIVISION_MAX_ITERATIONS);return currentT;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"binarySubdivide_reactNativeReanimated_BezierJs6\\\",\\\"aX\\\",\\\"aA\\\",\\\"aB\\\",\\\"mX1\\\",\\\"mX2\\\",\\\"calcBezier\\\",\\\"SUBDIVISION_PRECISION\\\",\\\"SUBDIVISION_MAX_ITERATIONS\\\",\\\"__closure\\\",\\\"currentX\\\",\\\"currentT\\\",\\\"i\\\",\\\"Math\\\",\\\"abs\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AA8CA,SAAAA,+CAA+CA,CAAAC,EAAA,CAAAC,EAAA,CAAAC,EAAA,CAAAC,GAAA,CAAAC,GAAA,QAAAC,UAAA,CAAAC,qBAAA,CAAAC,0BAAA,OAAAC,SAAA,CAG7C,GAAI,CAAAC,QAAQ,CACZ,GAAI,CAAAC,QAAQ,CACZ,GAAI,CAAAC,CAAC,CAAG,CAAC,CACT,EAAG,CACDD,QAAQ,CAAGT,EAAE,CAAG,CAACC,EAAE,CAAGD,EAAE,EAAI,GAAG,CAC/BQ,QAAQ,CAAGJ,UAAU,CAACK,QAAQ,CAAEP,GAAG,CAAEC,GAAG,CAAC,CAAGJ,EAAE,CAC9C,GAAIS,QAAQ,CAAG,GAAG,CAAE,CAClBP,EAAE,CAAGQ,QAAQ,CACf,CAAC,IAAM,CACLT,EAAE,CAAGS,QAAQ,CACf,CACF,CAAC,MAAQE,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC,CAAGH,qBAAqB,EAAI,EAAEK,CAAC,CAAGJ,0BAA0B,EACvF,MAAO,CAAAG,QAAQ,CACjB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const binarySubdivide = function () {\n    const _e = [new global.Error(), -4, -27];\n    const binarySubdivide = function (aX, aA, aB, mX1, mX2) {\n      let currentX;\n      let currentT;\n      let i = 0;\n      do {\n        currentT = aA + (aB - aA) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - aX;\n        if (currentX > 0.0) {\n          aB = currentT;\n        } else {\n          aA = currentT;\n        }\n      } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n      return currentT;\n    };\n    binarySubdivide.__closure = {\n      calcBezier,\n      SUBDIVISION_PRECISION,\n      SUBDIVISION_MAX_ITERATIONS\n    };\n    binarySubdivide.__workletHash = 9166841024279;\n    binarySubdivide.__initData = _worklet_9166841024279_init_data;\n    binarySubdivide.__stackDetails = _e;\n    return binarySubdivide;\n  }();\n  const _worklet_13606782449305_init_data = {\n    code: \"function newtonRaphsonIterate_reactNativeReanimated_BezierJs7(aX,aGuessT,mX1,mX2){const{NEWTON_ITERATIONS,getSlope,calcBezier}=this.__closure;for(let i=0;i<NEWTON_ITERATIONS;++i){const currentSlope=getSlope(aGuessT,mX1,mX2);if(currentSlope===0.0){return aGuessT;}const currentX=calcBezier(aGuessT,mX1,mX2)-aX;aGuessT-=currentX/currentSlope;}return aGuessT;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"newtonRaphsonIterate_reactNativeReanimated_BezierJs7\\\",\\\"aX\\\",\\\"aGuessT\\\",\\\"mX1\\\",\\\"mX2\\\",\\\"NEWTON_ITERATIONS\\\",\\\"getSlope\\\",\\\"calcBezier\\\",\\\"__closure\\\",\\\"i\\\",\\\"currentSlope\\\",\\\"currentX\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AA+DA,SAAAA,oDAAqDA,CAAAC,EAAA,CAAAC,OAAA,CAAAC,GAAA,CAAAC,GAAA,QAAAC,iBAAA,CAAAC,QAAA,CAAAC,UAAA,OAAAC,SAAA,CAGnD,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,iBAAiB,CAAE,EAAEI,CAAC,CAAE,CAC1C,KAAM,CAAAC,YAAY,CAAGJ,QAAQ,CAACJ,OAAO,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAChD,GAAIM,YAAY,GAAK,GAAG,CAAE,CACxB,MAAO,CAAAR,OAAO,CAChB,CACA,KAAM,CAAAS,QAAQ,CAAGJ,UAAU,CAACL,OAAO,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAAGH,EAAE,CACnDC,OAAO,EAAIS,QAAQ,CAAGD,YAAY,CACpC,CACA,MAAO,CAAAR,OAAO,CAChB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const newtonRaphsonIterate = function () {\n    const _e = [new global.Error(), -4, -27];\n    const newtonRaphsonIterate = function (aX, aGuessT, mX1, mX2) {\n      for (let i = 0; i < NEWTON_ITERATIONS; ++i) {\n        const currentSlope = getSlope(aGuessT, mX1, mX2);\n        if (currentSlope === 0.0) {\n          return aGuessT;\n        }\n        const currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n        aGuessT -= currentX / currentSlope;\n      }\n      return aGuessT;\n    };\n    newtonRaphsonIterate.__closure = {\n      NEWTON_ITERATIONS,\n      getSlope,\n      calcBezier\n    };\n    newtonRaphsonIterate.__workletHash = 13606782449305;\n    newtonRaphsonIterate.__initData = _worklet_13606782449305_init_data;\n    newtonRaphsonIterate.__stackDetails = _e;\n    return newtonRaphsonIterate;\n  }();\n  const _worklet_6918608934740_init_data = {\n    code: \"function Bezier_reactNativeReanimated_BezierJs8(mX1,mY1,mX2,mY2){const{kSplineTableSize,calcBezier,kSampleStepSize,getSlope,NEWTON_MIN_SLOPE,newtonRaphsonIterate,binarySubdivide}=this.__closure;function LinearEasing(x){'worklet';return x;}if(!(mX1>=0&&mX1<=1&&mX2>=0&&mX2<=1)){throw new ReanimatedError('Bezier x values must be in [0, 1] range.');}if(mX1===mY1&&mX2===mY2){return LinearEasing;}const sampleValues=new Array(kSplineTableSize);for(let i=0;i<kSplineTableSize;++i){sampleValues[i]=calcBezier(i*kSampleStepSize,mX1,mX2);}function getTForX(aX){'worklet';let intervalStart=0.0;let currentSample=1;const lastSample=kSplineTableSize-1;for(;currentSample!==lastSample&&sampleValues[currentSample]<=aX;++currentSample){intervalStart+=kSampleStepSize;}--currentSample;const dist=(aX-sampleValues[currentSample])/(sampleValues[currentSample+1]-sampleValues[currentSample]);const guessForT=intervalStart+dist*kSampleStepSize;const initialSlope=getSlope(guessForT,mX1,mX2);if(initialSlope>=NEWTON_MIN_SLOPE){return newtonRaphsonIterate(aX,guessForT,mX1,mX2);}else if(initialSlope===0.0){return guessForT;}else{return binarySubdivide(aX,intervalStart,intervalStart+kSampleStepSize,mX1,mX2);}}return function Bezier_reactNativeReanimated_BezierJs8(x){'worklet';if(mX1===mY1&&mX2===mY2){return x;}if(x===0){return 0;}if(x===1){return 1;}return calcBezier(getTForX(x),mY1,mY2);};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"Bezier_reactNativeReanimated_BezierJs8\\\",\\\"mX1\\\",\\\"mY1\\\",\\\"mX2\\\",\\\"mY2\\\",\\\"kSplineTableSize\\\",\\\"calcBezier\\\",\\\"kSampleStepSize\\\",\\\"getSlope\\\",\\\"NEWTON_MIN_SLOPE\\\",\\\"newtonRaphsonIterate\\\",\\\"binarySubdivide\\\",\\\"__closure\\\",\\\"LinearEasing\\\",\\\"x\\\",\\\"ReanimatedError\\\",\\\"sampleValues\\\",\\\"Array\\\",\\\"i\\\",\\\"getTForX\\\",\\\"aX\\\",\\\"intervalStart\\\",\\\"currentSample\\\",\\\"lastSample\\\",\\\"dist\\\",\\\"guessForT\\\",\\\"initialSlope\\\",\\\"BezierEasing\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AA4EO,SAAAA,sCAAoCA,CAAAC,GAAA,CAAAC,GAAA,CAAAC,GAAA,CAAAC,GAAA,QAAAC,gBAAA,CAAAC,UAAA,CAAAC,eAAA,CAAAC,QAAA,CAAAC,gBAAA,CAAAC,oBAAA,CAAAC,eAAA,OAAAC,SAAA,CAGzC,QAAS,CAAAC,YAAYA,CAACC,CAAC,CAAE,CACvB,SAAS,CAET,MAAO,CAAAA,CAAC,CACV,CACA,GAAI,EAAEb,GAAG,EAAI,CAAC,EAAIA,GAAG,EAAI,CAAC,EAAIE,GAAG,EAAI,CAAC,EAAIA,GAAG,EAAI,CAAC,CAAC,CAAE,CACnD,KAAM,IAAI,CAAAY,eAAe,CAAC,0CAA0C,CAAC,CACvE,CACA,GAAId,GAAG,GAAKC,GAAG,EAAIC,GAAG,GAAKC,GAAG,CAAE,CAC9B,MAAO,CAAAS,YAAY,CACrB,CACA,KAAM,CAAAG,YAAY,CAAG,GAAI,CAAAC,KAAK,CAACZ,gBAAgB,CAAC,CAGhD,IAAK,GAAI,CAAAa,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGb,gBAAgB,CAAE,EAAEa,CAAC,CAAE,CACzCF,YAAY,CAACE,CAAC,CAAC,CAAGZ,UAAU,CAACY,CAAC,CAAGX,eAAe,CAAEN,GAAG,CAAEE,GAAG,CAAC,CAC7D,CACA,QAAS,CAAAgB,QAAQA,CAACC,EAAE,CAAE,CACpB,SAAS,CAET,GAAI,CAAAC,aAAa,CAAG,GAAG,CACvB,GAAI,CAAAC,aAAa,CAAG,CAAC,CACrB,KAAM,CAAAC,UAAU,CAAGlB,gBAAgB,CAAG,CAAC,CACvC,KAAOiB,aAAa,GAAKC,UAAU,EAAIP,YAAY,CAACM,aAAa,CAAC,EAAIF,EAAE,CAAE,EAAEE,aAAa,CAAE,CACzFD,aAAa,EAAId,eAAe,CAClC,CACA,EAAEe,aAAa,CAGf,KAAM,CAAAE,IAAI,CAAG,CAACJ,EAAE,CAAGJ,YAAY,CAACM,aAAa,CAAC,GAAKN,YAAY,CAACM,aAAa,CAAG,CAAC,CAAC,CAAGN,YAAY,CAACM,aAAa,CAAC,CAAC,CACjH,KAAM,CAAAG,SAAS,CAAGJ,aAAa,CAAGG,IAAI,CAAGjB,eAAe,CACxD,KAAM,CAAAmB,YAAY,CAAGlB,QAAQ,CAACiB,SAAS,CAAExB,GAAG,CAAEE,GAAG,CAAC,CAClD,GAAIuB,YAAY,EAAIjB,gBAAgB,CAAE,CACpC,MAAO,CAAAC,oBAAoB,CAACU,EAAE,CAAEK,SAAS,CAAExB,GAAG,CAAEE,GAAG,CAAC,CACtD,CAAC,IAAM,IAAIuB,YAAY,GAAK,GAAG,CAAE,CAC/B,MAAO,CAAAD,SAAS,CAClB,CAAC,IAAM,CACL,MAAO,CAAAd,eAAe,CAACS,EAAE,CAAEC,aAAa,CAAEA,aAAa,CAAGd,eAAe,CAAEN,GAAG,CAAEE,GAAG,CAAC,CACtF,CACF,CACA,MAAO,SAAS,CAAAH,sCAAgB2B,CAAAb,CAAA,EAC9B,SAAS,CAET,GAAIb,GAAG,GAAKC,GAAG,EAAIC,GAAG,GAAKC,GAAG,CAAE,CAC9B,MAAO,CAAAU,CAAC,CACV,CAEA,GAAIA,CAAC,GAAK,CAAC,CAAE,CACX,MAAO,EAAC,CACV,CACA,GAAIA,CAAC,GAAK,CAAC,CAAE,CACX,MAAO,EAAC,CACV,CACA,MAAO,CAAAR,UAAU,CAACa,QAAQ,CAACL,CAAC,CAAC,CAAEZ,GAAG,CAAEE,GAAG,CAAC,CAC1C,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_17244837042130_init_data = {\n    code: \"function LinearEasing_reactNativeReanimated_BezierJs9(x){return x;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"LinearEasing_reactNativeReanimated_BezierJs9\\\",\\\"x\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AA+EE,SAAAA,4CAAyBA,CAAAC,CAAA,EAGvB,MAAO,CAAAA,CAAC,CACV\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_15383647275891_init_data = {\n    code: \"function getTForX_reactNativeReanimated_BezierJs10(aX){const{kSplineTableSize,sampleValues,kSampleStepSize,getSlope,mX1,mX2,NEWTON_MIN_SLOPE,newtonRaphsonIterate,binarySubdivide}=this.__closure;let intervalStart=0.0;let currentSample=1;const lastSample=kSplineTableSize-1;for(;currentSample!==lastSample&&sampleValues[currentSample]<=aX;++currentSample){intervalStart+=kSampleStepSize;}--currentSample;const dist=(aX-sampleValues[currentSample])/(sampleValues[currentSample+1]-sampleValues[currentSample]);const guessForT=intervalStart+dist*kSampleStepSize;const initialSlope=getSlope(guessForT,mX1,mX2);if(initialSlope>=NEWTON_MIN_SLOPE){return newtonRaphsonIterate(aX,guessForT,mX1,mX2);}else if(initialSlope===0.0){return guessForT;}else{return binarySubdivide(aX,intervalStart,intervalStart+kSampleStepSize,mX1,mX2);}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getTForX_reactNativeReanimated_BezierJs10\\\",\\\"aX\\\",\\\"kSplineTableSize\\\",\\\"sampleValues\\\",\\\"kSampleStepSize\\\",\\\"getSlope\\\",\\\"mX1\\\",\\\"mX2\\\",\\\"NEWTON_MIN_SLOPE\\\",\\\"newtonRaphsonIterate\\\",\\\"binarySubdivide\\\",\\\"__closure\\\",\\\"intervalStart\\\",\\\"currentSample\\\",\\\"lastSample\\\",\\\"dist\\\",\\\"guessForT\\\",\\\"initialSlope\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AAgGE,SAAAA,yCAAsBA,CAAAC,EAAA,QAAAC,gBAAA,CAAAC,YAAA,CAAAC,eAAA,CAAAC,QAAA,CAAAC,GAAA,CAAAC,GAAA,CAAAC,gBAAA,CAAAC,oBAAA,CAAAC,eAAA,OAAAC,SAAA,CAGpB,GAAI,CAAAC,aAAa,CAAG,GAAG,CACvB,GAAI,CAAAC,aAAa,CAAG,CAAC,CACrB,KAAM,CAAAC,UAAU,CAAGZ,gBAAgB,CAAG,CAAC,CACvC,KAAOW,aAAa,GAAKC,UAAU,EAAIX,YAAY,CAACU,aAAa,CAAC,EAAIZ,EAAE,CAAE,EAAEY,aAAa,CAAE,CACzFD,aAAa,EAAIR,eAAe,CAClC,CACA,EAAES,aAAa,CAGf,KAAM,CAAAE,IAAI,CAAG,CAACd,EAAE,CAAGE,YAAY,CAACU,aAAa,CAAC,GAAKV,YAAY,CAACU,aAAa,CAAG,CAAC,CAAC,CAAGV,YAAY,CAACU,aAAa,CAAC,CAAC,CACjH,KAAM,CAAAG,SAAS,CAAGJ,aAAa,CAAGG,IAAI,CAAGX,eAAe,CACxD,KAAM,CAAAa,YAAY,CAAGZ,QAAQ,CAACW,SAAS,CAAEV,GAAG,CAAEC,GAAG,CAAC,CAClD,GAAIU,YAAY,EAAIT,gBAAgB,CAAE,CACpC,MAAO,CAAAC,oBAAoB,CAACR,EAAE,CAAEe,SAAS,CAAEV,GAAG,CAAEC,GAAG,CAAC,CACtD,CAAC,IAAM,IAAIU,YAAY,GAAK,GAAG,CAAE,CAC/B,MAAO,CAAAD,SAAS,CAClB,CAAC,IAAM,CACL,MAAO,CAAAN,eAAe,CAACT,EAAE,CAAEW,aAAa,CAAEA,aAAa,CAAGR,eAAe,CAAEE,GAAG,CAAEC,GAAG,CAAC,CACtF,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_7919274967463_init_data = {\n    code: \"function BezierEasing_reactNativeReanimated_BezierJs11(x){const{mX1,mY1,mX2,mY2,calcBezier,getTForX}=this.__closure;if(mX1===mY1&&mX2===mY2){return x;}if(x===0){return 0;}if(x===1){return 1;}return calcBezier(getTForX(x),mY1,mY2);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"BezierEasing_reactNativeReanimated_BezierJs11\\\",\\\"x\\\",\\\"mX1\\\",\\\"mY1\\\",\\\"mX2\\\",\\\"mY2\\\",\\\"calcBezier\\\",\\\"getTForX\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/Bezier.js\\\"],\\\"mappings\\\":\\\"AAuHS,SAAAA,6CAAyBA,CAAAC,CAAA,QAAAC,GAAA,CAAAC,GAAA,CAAAC,GAAA,CAAAC,GAAA,CAAAC,UAAA,CAAAC,QAAA,OAAAC,SAAA,CAG9B,GAAIN,GAAG,GAAKC,GAAG,EAAIC,GAAG,GAAKC,GAAG,CAAE,CAC9B,MAAO,CAAAJ,CAAC,CACV,CAEA,GAAIA,CAAC,GAAK,CAAC,CAAE,CACX,MAAO,EAAC,CACV,CACA,GAAIA,CAAC,GAAK,CAAC,CAAE,CACX,MAAO,EAAC,CACV,CACA,MAAO,CAAAK,UAAU,CAACC,QAAQ,CAACN,CAAC,CAAC,CAAEE,GAAG,CAAEE,GAAG,CAAC,CAC1C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const Bezier = exports.Bezier = function () {\n    const _e = [new global.Error(), -8, -27];\n    const Bezier = function (mX1, mY1, mX2, mY2) {\n      const LinearEasing = function () {\n        const _e = [new global.Error(), 1, -27];\n        const LinearEasing = function (x) {\n          return x;\n        };\n        LinearEasing.__closure = {};\n        LinearEasing.__workletHash = 17244837042130;\n        LinearEasing.__initData = _worklet_17244837042130_init_data;\n        LinearEasing.__stackDetails = _e;\n        return LinearEasing;\n      }();\n      if (!(mX1 >= 0 && mX1 <= 1 && mX2 >= 0 && mX2 <= 1)) {\n        throw new _errors.ReanimatedError('Bezier x values must be in [0, 1] range.');\n      }\n      if (mX1 === mY1 && mX2 === mY2) {\n        return LinearEasing;\n      }\n      const sampleValues = new Array(kSplineTableSize);\n\n      // Precompute samples table\n      for (let i = 0; i < kSplineTableSize; ++i) {\n        sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n      }\n      const getTForX = function () {\n        const _e = [new global.Error(), -10, -27];\n        const getTForX = function (aX) {\n          let intervalStart = 0.0;\n          let currentSample = 1;\n          const lastSample = kSplineTableSize - 1;\n          for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n            intervalStart += kSampleStepSize;\n          }\n          --currentSample;\n\n          // Interpolate to provide an initial guess for t\n          const dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n          const guessForT = intervalStart + dist * kSampleStepSize;\n          const initialSlope = getSlope(guessForT, mX1, mX2);\n          if (initialSlope >= NEWTON_MIN_SLOPE) {\n            return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n          } else if (initialSlope === 0.0) {\n            return guessForT;\n          } else {\n            return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n          }\n        };\n        getTForX.__closure = {\n          kSplineTableSize,\n          sampleValues,\n          kSampleStepSize,\n          getSlope,\n          mX1,\n          mX2,\n          NEWTON_MIN_SLOPE,\n          newtonRaphsonIterate,\n          binarySubdivide\n        };\n        getTForX.__workletHash = 15383647275891;\n        getTForX.__initData = _worklet_15383647275891_init_data;\n        getTForX.__stackDetails = _e;\n        return getTForX;\n      }();\n      return function () {\n        const _e = [new global.Error(), -7, -27];\n        const BezierEasing = function (x) {\n          if (mX1 === mY1 && mX2 === mY2) {\n            return x; // linear\n          }\n          // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n          if (x === 0) {\n            return 0;\n          }\n          if (x === 1) {\n            return 1;\n          }\n          return calcBezier(getTForX(x), mY1, mY2);\n        };\n        BezierEasing.__closure = {\n          mX1,\n          mY1,\n          mX2,\n          mY2,\n          calcBezier,\n          getTForX\n        };\n        BezierEasing.__workletHash = 7919274967463;\n        BezierEasing.__initData = _worklet_7919274967463_init_data;\n        BezierEasing.__stackDetails = _e;\n        return BezierEasing;\n      }();\n    };\n    Bezier.__closure = {\n      kSplineTableSize,\n      calcBezier,\n      kSampleStepSize,\n      getSlope,\n      NEWTON_MIN_SLOPE,\n      newtonRaphsonIterate,\n      binarySubdivide\n    };\n    Bezier.__workletHash = 6918608934740;\n    Bezier.__initData = _worklet_6918608934740_init_data;\n    Bezier.__stackDetails = _e;\n    return Bezier;\n  }();\n});", "lineCount": 309, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "<PERSON><PERSON>"], [7, 16, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_errors"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 5, 0], [10, 0, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [14, 2, 10, 0], [16, 2, 12, 0], [16, 8, 12, 6, "NEWTON_ITERATIONS"], [16, 25, 12, 23], [16, 28, 12, 26], [16, 29, 12, 27], [17, 2, 13, 0], [17, 8, 13, 6, "NEWTON_MIN_SLOPE"], [17, 24, 13, 22], [17, 27, 13, 25], [17, 32, 13, 30], [18, 2, 14, 0], [18, 8, 14, 6, "SUBDIVISION_PRECISION"], [18, 29, 14, 27], [18, 32, 14, 30], [18, 41, 14, 39], [19, 2, 15, 0], [19, 8, 15, 6, "SUBDIVISION_MAX_ITERATIONS"], [19, 34, 15, 32], [19, 37, 15, 35], [19, 39, 15, 37], [20, 2, 16, 0], [20, 8, 16, 6, "kSplineTableSize"], [20, 24, 16, 22], [20, 27, 16, 25], [20, 29, 16, 27], [21, 2, 17, 0], [21, 8, 17, 6, "kSampleStepSize"], [21, 23, 17, 21], [21, 26, 17, 24], [21, 29, 17, 27], [21, 33, 17, 31, "kSplineTableSize"], [21, 49, 17, 47], [21, 52, 17, 50], [21, 55, 17, 53], [21, 56, 17, 54], [22, 2, 17, 55], [22, 8, 17, 55, "_worklet_8828646082676_init_data"], [22, 40, 17, 55], [23, 4, 17, 55, "code"], [23, 8, 17, 55], [24, 4, 17, 55, "location"], [24, 12, 17, 55], [25, 4, 17, 55, "sourceMap"], [25, 13, 17, 55], [26, 4, 17, 55, "version"], [26, 11, 17, 55], [27, 2, 17, 55], [28, 2, 17, 55], [28, 8, 17, 55, "A"], [28, 9, 17, 55], [28, 12, 18, 0], [29, 4, 18, 0], [29, 10, 18, 0, "_e"], [29, 12, 18, 0], [29, 20, 18, 0, "global"], [29, 26, 18, 0], [29, 27, 18, 0, "Error"], [29, 32, 18, 0], [30, 4, 18, 0], [30, 10, 18, 0, "A"], [30, 11, 18, 0], [30, 23, 18, 0, "A"], [30, 24, 18, 11, "aA1"], [30, 27, 18, 14], [30, 29, 18, 16, "aA2"], [30, 32, 18, 19], [30, 34, 18, 21], [31, 6, 21, 2], [31, 13, 21, 9], [31, 16, 21, 12], [31, 19, 21, 15], [31, 22, 21, 18], [31, 25, 21, 21, "aA2"], [31, 28, 21, 24], [31, 31, 21, 27], [31, 34, 21, 30], [31, 37, 21, 33, "aA1"], [31, 40, 21, 36], [32, 4, 22, 0], [32, 5, 22, 1], [33, 4, 22, 1, "A"], [33, 5, 22, 1], [33, 6, 22, 1, "__closure"], [33, 15, 22, 1], [34, 4, 22, 1, "A"], [34, 5, 22, 1], [34, 6, 22, 1, "__workletHash"], [34, 19, 22, 1], [35, 4, 22, 1, "A"], [35, 5, 22, 1], [35, 6, 22, 1, "__initData"], [35, 16, 22, 1], [35, 19, 22, 1, "_worklet_8828646082676_init_data"], [35, 51, 22, 1], [36, 4, 22, 1, "A"], [36, 5, 22, 1], [36, 6, 22, 1, "__stackDetails"], [36, 20, 22, 1], [36, 23, 22, 1, "_e"], [36, 25, 22, 1], [37, 4, 22, 1], [37, 11, 22, 1, "A"], [37, 12, 22, 1], [38, 2, 22, 1], [38, 3, 18, 0], [39, 2, 18, 0], [39, 8, 18, 0, "_worklet_4203953384949_init_data"], [39, 40, 18, 0], [40, 4, 18, 0, "code"], [40, 8, 18, 0], [41, 4, 18, 0, "location"], [41, 12, 18, 0], [42, 4, 18, 0, "sourceMap"], [42, 13, 18, 0], [43, 4, 18, 0, "version"], [43, 11, 18, 0], [44, 2, 18, 0], [45, 2, 18, 0], [45, 8, 18, 0, "B"], [45, 9, 18, 0], [45, 12, 23, 0], [46, 4, 23, 0], [46, 10, 23, 0, "_e"], [46, 12, 23, 0], [46, 20, 23, 0, "global"], [46, 26, 23, 0], [46, 27, 23, 0, "Error"], [46, 32, 23, 0], [47, 4, 23, 0], [47, 10, 23, 0, "B"], [47, 11, 23, 0], [47, 23, 23, 0, "B"], [47, 24, 23, 11, "aA1"], [47, 27, 23, 14], [47, 29, 23, 16, "aA2"], [47, 32, 23, 19], [47, 34, 23, 21], [48, 6, 26, 2], [48, 13, 26, 9], [48, 16, 26, 12], [48, 19, 26, 15, "aA2"], [48, 22, 26, 18], [48, 25, 26, 21], [48, 28, 26, 24], [48, 31, 26, 27, "aA1"], [48, 34, 26, 30], [49, 4, 27, 0], [49, 5, 27, 1], [50, 4, 27, 1, "B"], [50, 5, 27, 1], [50, 6, 27, 1, "__closure"], [50, 15, 27, 1], [51, 4, 27, 1, "B"], [51, 5, 27, 1], [51, 6, 27, 1, "__workletHash"], [51, 19, 27, 1], [52, 4, 27, 1, "B"], [52, 5, 27, 1], [52, 6, 27, 1, "__initData"], [52, 16, 27, 1], [52, 19, 27, 1, "_worklet_4203953384949_init_data"], [52, 51, 27, 1], [53, 4, 27, 1, "B"], [53, 5, 27, 1], [53, 6, 27, 1, "__stackDetails"], [53, 20, 27, 1], [53, 23, 27, 1, "_e"], [53, 25, 27, 1], [54, 4, 27, 1], [54, 11, 27, 1, "B"], [54, 12, 27, 1], [55, 2, 27, 1], [55, 3, 23, 0], [56, 2, 23, 0], [56, 8, 23, 0, "_worklet_8388140024310_init_data"], [56, 40, 23, 0], [57, 4, 23, 0, "code"], [57, 8, 23, 0], [58, 4, 23, 0, "location"], [58, 12, 23, 0], [59, 4, 23, 0, "sourceMap"], [59, 13, 23, 0], [60, 4, 23, 0, "version"], [60, 11, 23, 0], [61, 2, 23, 0], [62, 2, 23, 0], [62, 8, 23, 0, "C"], [62, 9, 23, 0], [62, 12, 28, 0], [63, 4, 28, 0], [63, 10, 28, 0, "_e"], [63, 12, 28, 0], [63, 20, 28, 0, "global"], [63, 26, 28, 0], [63, 27, 28, 0, "Error"], [63, 32, 28, 0], [64, 4, 28, 0], [64, 10, 28, 0, "C"], [64, 11, 28, 0], [64, 23, 28, 0, "C"], [64, 24, 28, 11, "aA1"], [64, 27, 28, 14], [64, 29, 28, 16], [65, 6, 31, 2], [65, 13, 31, 9], [65, 16, 31, 12], [65, 19, 31, 15, "aA1"], [65, 22, 31, 18], [66, 4, 32, 0], [66, 5, 32, 1], [67, 4, 32, 1, "C"], [67, 5, 32, 1], [67, 6, 32, 1, "__closure"], [67, 15, 32, 1], [68, 4, 32, 1, "C"], [68, 5, 32, 1], [68, 6, 32, 1, "__workletHash"], [68, 19, 32, 1], [69, 4, 32, 1, "C"], [69, 5, 32, 1], [69, 6, 32, 1, "__initData"], [69, 16, 32, 1], [69, 19, 32, 1, "_worklet_8388140024310_init_data"], [69, 51, 32, 1], [70, 4, 32, 1, "C"], [70, 5, 32, 1], [70, 6, 32, 1, "__stackDetails"], [70, 20, 32, 1], [70, 23, 32, 1, "_e"], [70, 25, 32, 1], [71, 4, 32, 1], [71, 11, 32, 1, "C"], [71, 12, 32, 1], [72, 2, 32, 1], [72, 3, 28, 0], [72, 7, 34, 0], [73, 2, 34, 0], [73, 8, 34, 0, "_worklet_15740570894910_init_data"], [73, 41, 34, 0], [74, 4, 34, 0, "code"], [74, 8, 34, 0], [75, 4, 34, 0, "location"], [75, 12, 34, 0], [76, 4, 34, 0, "sourceMap"], [76, 13, 34, 0], [77, 4, 34, 0, "version"], [77, 11, 34, 0], [78, 2, 34, 0], [79, 2, 34, 0], [79, 8, 34, 0, "calcBezier"], [79, 18, 34, 0], [79, 21, 35, 0], [80, 4, 35, 0], [80, 10, 35, 0, "_e"], [80, 12, 35, 0], [80, 20, 35, 0, "global"], [80, 26, 35, 0], [80, 27, 35, 0, "Error"], [80, 32, 35, 0], [81, 4, 35, 0], [81, 10, 35, 0, "calcBezier"], [81, 20, 35, 0], [81, 32, 35, 0, "calcBezier"], [81, 33, 35, 20, "aT"], [81, 35, 35, 22], [81, 37, 35, 24, "aA1"], [81, 40, 35, 27], [81, 42, 35, 29, "aA2"], [81, 45, 35, 32], [81, 47, 35, 34], [82, 6, 38, 2], [82, 13, 38, 9], [82, 14, 38, 10], [82, 15, 38, 11, "A"], [82, 16, 38, 12], [82, 17, 38, 13, "aA1"], [82, 20, 38, 16], [82, 22, 38, 18, "aA2"], [82, 25, 38, 21], [82, 26, 38, 22], [82, 29, 38, 25, "aT"], [82, 31, 38, 27], [82, 34, 38, 30, "B"], [82, 35, 38, 31], [82, 36, 38, 32, "aA1"], [82, 39, 38, 35], [82, 41, 38, 37, "aA2"], [82, 44, 38, 40], [82, 45, 38, 41], [82, 49, 38, 45, "aT"], [82, 51, 38, 47], [82, 54, 38, 50, "C"], [82, 55, 38, 51], [82, 56, 38, 52, "aA1"], [82, 59, 38, 55], [82, 60, 38, 56], [82, 64, 38, 60, "aT"], [82, 66, 38, 62], [83, 4, 39, 0], [83, 5, 39, 1], [84, 4, 39, 1, "calcBezier"], [84, 14, 39, 1], [84, 15, 39, 1, "__closure"], [84, 24, 39, 1], [85, 6, 39, 1, "A"], [85, 7, 39, 1], [86, 6, 39, 1, "B"], [86, 7, 39, 1], [87, 6, 39, 1, "C"], [88, 4, 39, 1], [89, 4, 39, 1, "calcBezier"], [89, 14, 39, 1], [89, 15, 39, 1, "__workletHash"], [89, 28, 39, 1], [90, 4, 39, 1, "calcBezier"], [90, 14, 39, 1], [90, 15, 39, 1, "__initData"], [90, 25, 39, 1], [90, 28, 39, 1, "_worklet_15740570894910_init_data"], [90, 61, 39, 1], [91, 4, 39, 1, "calcBezier"], [91, 14, 39, 1], [91, 15, 39, 1, "__stackDetails"], [91, 29, 39, 1], [91, 32, 39, 1, "_e"], [91, 34, 39, 1], [92, 4, 39, 1], [92, 11, 39, 1, "calcBezier"], [92, 21, 39, 1], [93, 2, 39, 1], [93, 3, 35, 0], [93, 7, 41, 0], [94, 2, 41, 0], [94, 8, 41, 0, "_worklet_4162364291299_init_data"], [94, 40, 41, 0], [95, 4, 41, 0, "code"], [95, 8, 41, 0], [96, 4, 41, 0, "location"], [96, 12, 41, 0], [97, 4, 41, 0, "sourceMap"], [97, 13, 41, 0], [98, 4, 41, 0, "version"], [98, 11, 41, 0], [99, 2, 41, 0], [100, 2, 41, 0], [100, 8, 41, 0, "getSlope"], [100, 16, 41, 0], [100, 19, 42, 0], [101, 4, 42, 0], [101, 10, 42, 0, "_e"], [101, 12, 42, 0], [101, 20, 42, 0, "global"], [101, 26, 42, 0], [101, 27, 42, 0, "Error"], [101, 32, 42, 0], [102, 4, 42, 0], [102, 10, 42, 0, "getSlope"], [102, 18, 42, 0], [102, 30, 42, 0, "getSlope"], [102, 31, 42, 18, "aT"], [102, 33, 42, 20], [102, 35, 42, 22, "aA1"], [102, 38, 42, 25], [102, 40, 42, 27, "aA2"], [102, 43, 42, 30], [102, 45, 42, 32], [103, 6, 45, 2], [103, 13, 45, 9], [103, 16, 45, 12], [103, 19, 45, 15, "A"], [103, 20, 45, 16], [103, 21, 45, 17, "aA1"], [103, 24, 45, 20], [103, 26, 45, 22, "aA2"], [103, 29, 45, 25], [103, 30, 45, 26], [103, 33, 45, 29, "aT"], [103, 35, 45, 31], [103, 38, 45, 34, "aT"], [103, 40, 45, 36], [103, 43, 45, 39], [103, 46, 45, 42], [103, 49, 45, 45, "B"], [103, 50, 45, 46], [103, 51, 45, 47, "aA1"], [103, 54, 45, 50], [103, 56, 45, 52, "aA2"], [103, 59, 45, 55], [103, 60, 45, 56], [103, 63, 45, 59, "aT"], [103, 65, 45, 61], [103, 68, 45, 64, "C"], [103, 69, 45, 65], [103, 70, 45, 66, "aA1"], [103, 73, 45, 69], [103, 74, 45, 70], [104, 4, 46, 0], [104, 5, 46, 1], [105, 4, 46, 1, "getSlope"], [105, 12, 46, 1], [105, 13, 46, 1, "__closure"], [105, 22, 46, 1], [106, 6, 46, 1, "A"], [106, 7, 46, 1], [107, 6, 46, 1, "B"], [107, 7, 46, 1], [108, 6, 46, 1, "C"], [109, 4, 46, 1], [110, 4, 46, 1, "getSlope"], [110, 12, 46, 1], [110, 13, 46, 1, "__workletHash"], [110, 26, 46, 1], [111, 4, 46, 1, "getSlope"], [111, 12, 46, 1], [111, 13, 46, 1, "__initData"], [111, 23, 46, 1], [111, 26, 46, 1, "_worklet_4162364291299_init_data"], [111, 58, 46, 1], [112, 4, 46, 1, "getSlope"], [112, 12, 46, 1], [112, 13, 46, 1, "__stackDetails"], [112, 27, 46, 1], [112, 30, 46, 1, "_e"], [112, 32, 46, 1], [113, 4, 46, 1], [113, 11, 46, 1, "getSlope"], [113, 19, 46, 1], [114, 2, 46, 1], [114, 3, 42, 0], [115, 2, 42, 0], [115, 8, 42, 0, "_worklet_9166841024279_init_data"], [115, 40, 42, 0], [116, 4, 42, 0, "code"], [116, 8, 42, 0], [117, 4, 42, 0, "location"], [117, 12, 42, 0], [118, 4, 42, 0, "sourceMap"], [118, 13, 42, 0], [119, 4, 42, 0, "version"], [119, 11, 42, 0], [120, 2, 42, 0], [121, 2, 42, 0], [121, 8, 42, 0, "binarySubdivide"], [121, 23, 42, 0], [121, 26, 47, 0], [122, 4, 47, 0], [122, 10, 47, 0, "_e"], [122, 12, 47, 0], [122, 20, 47, 0, "global"], [122, 26, 47, 0], [122, 27, 47, 0, "Error"], [122, 32, 47, 0], [123, 4, 47, 0], [123, 10, 47, 0, "binarySubdivide"], [123, 25, 47, 0], [123, 37, 47, 0, "binarySubdivide"], [123, 38, 47, 25, "aX"], [123, 40, 47, 27], [123, 42, 47, 29, "aA"], [123, 44, 47, 31], [123, 46, 47, 33, "aB"], [123, 48, 47, 35], [123, 50, 47, 37, "mX1"], [123, 53, 47, 40], [123, 55, 47, 42, "mX2"], [123, 58, 47, 45], [123, 60, 47, 47], [124, 6, 50, 2], [124, 10, 50, 6, "currentX"], [124, 18, 50, 14], [125, 6, 51, 2], [125, 10, 51, 6, "currentT"], [125, 18, 51, 14], [126, 6, 52, 2], [126, 10, 52, 6, "i"], [126, 11, 52, 7], [126, 14, 52, 10], [126, 15, 52, 11], [127, 6, 53, 2], [127, 9, 53, 5], [128, 8, 54, 4, "currentT"], [128, 16, 54, 12], [128, 19, 54, 15, "aA"], [128, 21, 54, 17], [128, 24, 54, 20], [128, 25, 54, 21, "aB"], [128, 27, 54, 23], [128, 30, 54, 26, "aA"], [128, 32, 54, 28], [128, 36, 54, 32], [128, 39, 54, 35], [129, 8, 55, 4, "currentX"], [129, 16, 55, 12], [129, 19, 55, 15, "calcBezier"], [129, 29, 55, 25], [129, 30, 55, 26, "currentT"], [129, 38, 55, 34], [129, 40, 55, 36, "mX1"], [129, 43, 55, 39], [129, 45, 55, 41, "mX2"], [129, 48, 55, 44], [129, 49, 55, 45], [129, 52, 55, 48, "aX"], [129, 54, 55, 50], [130, 8, 56, 4], [130, 12, 56, 8, "currentX"], [130, 20, 56, 16], [130, 23, 56, 19], [130, 26, 56, 22], [130, 28, 56, 24], [131, 10, 57, 6, "aB"], [131, 12, 57, 8], [131, 15, 57, 11, "currentT"], [131, 23, 57, 19], [132, 8, 58, 4], [132, 9, 58, 5], [132, 15, 58, 11], [133, 10, 59, 6, "aA"], [133, 12, 59, 8], [133, 15, 59, 11, "currentT"], [133, 23, 59, 19], [134, 8, 60, 4], [135, 6, 61, 2], [135, 7, 61, 3], [135, 15, 61, 11, "Math"], [135, 19, 61, 15], [135, 20, 61, 16, "abs"], [135, 23, 61, 19], [135, 24, 61, 20, "currentX"], [135, 32, 61, 28], [135, 33, 61, 29], [135, 36, 61, 32, "SUBDIVISION_PRECISION"], [135, 57, 61, 53], [135, 61, 61, 57], [135, 63, 61, 59, "i"], [135, 64, 61, 60], [135, 67, 61, 63, "SUBDIVISION_MAX_ITERATIONS"], [135, 93, 61, 89], [136, 6, 62, 2], [136, 13, 62, 9, "currentT"], [136, 21, 62, 17], [137, 4, 63, 0], [137, 5, 63, 1], [138, 4, 63, 1, "binarySubdivide"], [138, 19, 63, 1], [138, 20, 63, 1, "__closure"], [138, 29, 63, 1], [139, 6, 63, 1, "calcBezier"], [139, 16, 63, 1], [140, 6, 63, 1, "SUBDIVISION_PRECISION"], [140, 27, 63, 1], [141, 6, 63, 1, "SUBDIVISION_MAX_ITERATIONS"], [142, 4, 63, 1], [143, 4, 63, 1, "binarySubdivide"], [143, 19, 63, 1], [143, 20, 63, 1, "__workletHash"], [143, 33, 63, 1], [144, 4, 63, 1, "binarySubdivide"], [144, 19, 63, 1], [144, 20, 63, 1, "__initData"], [144, 30, 63, 1], [144, 33, 63, 1, "_worklet_9166841024279_init_data"], [144, 65, 63, 1], [145, 4, 63, 1, "binarySubdivide"], [145, 19, 63, 1], [145, 20, 63, 1, "__stackDetails"], [145, 34, 63, 1], [145, 37, 63, 1, "_e"], [145, 39, 63, 1], [146, 4, 63, 1], [146, 11, 63, 1, "binarySubdivide"], [146, 26, 63, 1], [147, 2, 63, 1], [147, 3, 47, 0], [148, 2, 47, 0], [148, 8, 47, 0, "_worklet_13606782449305_init_data"], [148, 41, 47, 0], [149, 4, 47, 0, "code"], [149, 8, 47, 0], [150, 4, 47, 0, "location"], [150, 12, 47, 0], [151, 4, 47, 0, "sourceMap"], [151, 13, 47, 0], [152, 4, 47, 0, "version"], [152, 11, 47, 0], [153, 2, 47, 0], [154, 2, 47, 0], [154, 8, 47, 0, "newtonRaphsonIterate"], [154, 28, 47, 0], [154, 31, 64, 0], [155, 4, 64, 0], [155, 10, 64, 0, "_e"], [155, 12, 64, 0], [155, 20, 64, 0, "global"], [155, 26, 64, 0], [155, 27, 64, 0, "Error"], [155, 32, 64, 0], [156, 4, 64, 0], [156, 10, 64, 0, "newtonRaphsonIterate"], [156, 30, 64, 0], [156, 42, 64, 0, "newtonRaphsonIterate"], [156, 43, 64, 30, "aX"], [156, 45, 64, 32], [156, 47, 64, 34, "aGuessT"], [156, 54, 64, 41], [156, 56, 64, 43, "mX1"], [156, 59, 64, 46], [156, 61, 64, 48, "mX2"], [156, 64, 64, 51], [156, 66, 64, 53], [157, 6, 67, 2], [157, 11, 67, 7], [157, 15, 67, 11, "i"], [157, 16, 67, 12], [157, 19, 67, 15], [157, 20, 67, 16], [157, 22, 67, 18, "i"], [157, 23, 67, 19], [157, 26, 67, 22, "NEWTON_ITERATIONS"], [157, 43, 67, 39], [157, 45, 67, 41], [157, 47, 67, 43, "i"], [157, 48, 67, 44], [157, 50, 67, 46], [158, 8, 68, 4], [158, 14, 68, 10, "currentSlope"], [158, 26, 68, 22], [158, 29, 68, 25, "getSlope"], [158, 37, 68, 33], [158, 38, 68, 34, "aGuessT"], [158, 45, 68, 41], [158, 47, 68, 43, "mX1"], [158, 50, 68, 46], [158, 52, 68, 48, "mX2"], [158, 55, 68, 51], [158, 56, 68, 52], [159, 8, 69, 4], [159, 12, 69, 8, "currentSlope"], [159, 24, 69, 20], [159, 29, 69, 25], [159, 32, 69, 28], [159, 34, 69, 30], [160, 10, 70, 6], [160, 17, 70, 13, "aGuessT"], [160, 24, 70, 20], [161, 8, 71, 4], [162, 8, 72, 4], [162, 14, 72, 10, "currentX"], [162, 22, 72, 18], [162, 25, 72, 21, "calcBezier"], [162, 35, 72, 31], [162, 36, 72, 32, "aGuessT"], [162, 43, 72, 39], [162, 45, 72, 41, "mX1"], [162, 48, 72, 44], [162, 50, 72, 46, "mX2"], [162, 53, 72, 49], [162, 54, 72, 50], [162, 57, 72, 53, "aX"], [162, 59, 72, 55], [163, 8, 73, 4, "aGuessT"], [163, 15, 73, 11], [163, 19, 73, 15, "currentX"], [163, 27, 73, 23], [163, 30, 73, 26, "currentSlope"], [163, 42, 73, 38], [164, 6, 74, 2], [165, 6, 75, 2], [165, 13, 75, 9, "aGuessT"], [165, 20, 75, 16], [166, 4, 76, 0], [166, 5, 76, 1], [167, 4, 76, 1, "newtonRaphsonIterate"], [167, 24, 76, 1], [167, 25, 76, 1, "__closure"], [167, 34, 76, 1], [168, 6, 76, 1, "NEWTON_ITERATIONS"], [168, 23, 76, 1], [169, 6, 76, 1, "getSlope"], [169, 14, 76, 1], [170, 6, 76, 1, "calcBezier"], [171, 4, 76, 1], [172, 4, 76, 1, "newtonRaphsonIterate"], [172, 24, 76, 1], [172, 25, 76, 1, "__workletHash"], [172, 38, 76, 1], [173, 4, 76, 1, "newtonRaphsonIterate"], [173, 24, 76, 1], [173, 25, 76, 1, "__initData"], [173, 35, 76, 1], [173, 38, 76, 1, "_worklet_13606782449305_init_data"], [173, 71, 76, 1], [174, 4, 76, 1, "newtonRaphsonIterate"], [174, 24, 76, 1], [174, 25, 76, 1, "__stackDetails"], [174, 39, 76, 1], [174, 42, 76, 1, "_e"], [174, 44, 76, 1], [175, 4, 76, 1], [175, 11, 76, 1, "newtonRaphsonIterate"], [175, 31, 76, 1], [176, 2, 76, 1], [176, 3, 64, 0], [177, 2, 64, 0], [177, 8, 64, 0, "_worklet_6918608934740_init_data"], [177, 40, 64, 0], [178, 4, 64, 0, "code"], [178, 8, 64, 0], [179, 4, 64, 0, "location"], [179, 12, 64, 0], [180, 4, 64, 0, "sourceMap"], [180, 13, 64, 0], [181, 4, 64, 0, "version"], [181, 11, 64, 0], [182, 2, 64, 0], [183, 2, 64, 0], [183, 8, 64, 0, "_worklet_17244837042130_init_data"], [183, 41, 64, 0], [184, 4, 64, 0, "code"], [184, 8, 64, 0], [185, 4, 64, 0, "location"], [185, 12, 64, 0], [186, 4, 64, 0, "sourceMap"], [186, 13, 64, 0], [187, 4, 64, 0, "version"], [187, 11, 64, 0], [188, 2, 64, 0], [189, 2, 64, 0], [189, 8, 64, 0, "_worklet_15383647275891_init_data"], [189, 41, 64, 0], [190, 4, 64, 0, "code"], [190, 8, 64, 0], [191, 4, 64, 0, "location"], [191, 12, 64, 0], [192, 4, 64, 0, "sourceMap"], [192, 13, 64, 0], [193, 4, 64, 0, "version"], [193, 11, 64, 0], [194, 2, 64, 0], [195, 2, 64, 0], [195, 8, 64, 0, "_worklet_7919274967463_init_data"], [195, 40, 64, 0], [196, 4, 64, 0, "code"], [196, 8, 64, 0], [197, 4, 64, 0, "location"], [197, 12, 64, 0], [198, 4, 64, 0, "sourceMap"], [198, 13, 64, 0], [199, 4, 64, 0, "version"], [199, 11, 64, 0], [200, 2, 64, 0], [201, 2, 64, 0], [201, 8, 64, 0, "<PERSON><PERSON>"], [201, 14, 64, 0], [201, 17, 64, 0, "exports"], [201, 24, 64, 0], [201, 25, 64, 0, "<PERSON><PERSON>"], [201, 31, 64, 0], [201, 34, 77, 7], [202, 4, 77, 7], [202, 10, 77, 7, "_e"], [202, 12, 77, 7], [202, 20, 77, 7, "global"], [202, 26, 77, 7], [202, 27, 77, 7, "Error"], [202, 32, 77, 7], [203, 4, 77, 7], [203, 10, 77, 7, "<PERSON><PERSON>"], [203, 16, 77, 7], [203, 28, 77, 7, "<PERSON><PERSON>"], [203, 29, 77, 23, "mX1"], [203, 32, 77, 26], [203, 34, 77, 28, "mY1"], [203, 37, 77, 31], [203, 39, 77, 33, "mX2"], [203, 42, 77, 36], [203, 44, 77, 38, "mY2"], [203, 47, 77, 41], [203, 49, 77, 43], [204, 6, 77, 43], [204, 12, 77, 43, "LinearEasing"], [204, 24, 77, 43], [204, 27, 80, 2], [205, 8, 80, 2], [205, 14, 80, 2, "_e"], [205, 16, 80, 2], [205, 24, 80, 2, "global"], [205, 30, 80, 2], [205, 31, 80, 2, "Error"], [205, 36, 80, 2], [206, 8, 80, 2], [206, 14, 80, 2, "LinearEasing"], [206, 26, 80, 2], [206, 38, 80, 2, "LinearEasing"], [206, 39, 80, 24, "x"], [206, 40, 80, 25], [206, 42, 80, 27], [207, 10, 83, 4], [207, 17, 83, 11, "x"], [207, 18, 83, 12], [208, 8, 84, 2], [208, 9, 84, 3], [209, 8, 84, 3, "LinearEasing"], [209, 20, 84, 3], [209, 21, 84, 3, "__closure"], [209, 30, 84, 3], [210, 8, 84, 3, "LinearEasing"], [210, 20, 84, 3], [210, 21, 84, 3, "__workletHash"], [210, 34, 84, 3], [211, 8, 84, 3, "LinearEasing"], [211, 20, 84, 3], [211, 21, 84, 3, "__initData"], [211, 31, 84, 3], [211, 34, 84, 3, "_worklet_17244837042130_init_data"], [211, 67, 84, 3], [212, 8, 84, 3, "LinearEasing"], [212, 20, 84, 3], [212, 21, 84, 3, "__stackDetails"], [212, 35, 84, 3], [212, 38, 84, 3, "_e"], [212, 40, 84, 3], [213, 8, 84, 3], [213, 15, 84, 3, "LinearEasing"], [213, 27, 84, 3], [214, 6, 84, 3], [214, 7, 80, 2], [215, 6, 85, 2], [215, 10, 85, 6], [215, 12, 85, 8, "mX1"], [215, 15, 85, 11], [215, 19, 85, 15], [215, 20, 85, 16], [215, 24, 85, 20, "mX1"], [215, 27, 85, 23], [215, 31, 85, 27], [215, 32, 85, 28], [215, 36, 85, 32, "mX2"], [215, 39, 85, 35], [215, 43, 85, 39], [215, 44, 85, 40], [215, 48, 85, 44, "mX2"], [215, 51, 85, 47], [215, 55, 85, 51], [215, 56, 85, 52], [215, 57, 85, 53], [215, 59, 85, 55], [216, 8, 86, 4], [216, 14, 86, 10], [216, 18, 86, 14, "ReanimatedError"], [216, 41, 86, 29], [216, 42, 86, 30], [216, 84, 86, 72], [216, 85, 86, 73], [217, 6, 87, 2], [218, 6, 88, 2], [218, 10, 88, 6, "mX1"], [218, 13, 88, 9], [218, 18, 88, 14, "mY1"], [218, 21, 88, 17], [218, 25, 88, 21, "mX2"], [218, 28, 88, 24], [218, 33, 88, 29, "mY2"], [218, 36, 88, 32], [218, 38, 88, 34], [219, 8, 89, 4], [219, 15, 89, 11, "LinearEasing"], [219, 27, 89, 23], [220, 6, 90, 2], [221, 6, 91, 2], [221, 12, 91, 8, "sampleValues"], [221, 24, 91, 20], [221, 27, 91, 23], [221, 31, 91, 27, "Array"], [221, 36, 91, 32], [221, 37, 91, 33, "kSplineTableSize"], [221, 53, 91, 49], [221, 54, 91, 50], [223, 6, 93, 2], [224, 6, 94, 2], [224, 11, 94, 7], [224, 15, 94, 11, "i"], [224, 16, 94, 12], [224, 19, 94, 15], [224, 20, 94, 16], [224, 22, 94, 18, "i"], [224, 23, 94, 19], [224, 26, 94, 22, "kSplineTableSize"], [224, 42, 94, 38], [224, 44, 94, 40], [224, 46, 94, 42, "i"], [224, 47, 94, 43], [224, 49, 94, 45], [225, 8, 95, 4, "sampleValues"], [225, 20, 95, 16], [225, 21, 95, 17, "i"], [225, 22, 95, 18], [225, 23, 95, 19], [225, 26, 95, 22, "calcBezier"], [225, 36, 95, 32], [225, 37, 95, 33, "i"], [225, 38, 95, 34], [225, 41, 95, 37, "kSampleStepSize"], [225, 56, 95, 52], [225, 58, 95, 54, "mX1"], [225, 61, 95, 57], [225, 63, 95, 59, "mX2"], [225, 66, 95, 62], [225, 67, 95, 63], [226, 6, 96, 2], [227, 6, 96, 3], [227, 12, 96, 3, "getTForX"], [227, 20, 96, 3], [227, 23, 97, 2], [228, 8, 97, 2], [228, 14, 97, 2, "_e"], [228, 16, 97, 2], [228, 24, 97, 2, "global"], [228, 30, 97, 2], [228, 31, 97, 2, "Error"], [228, 36, 97, 2], [229, 8, 97, 2], [229, 14, 97, 2, "getTForX"], [229, 22, 97, 2], [229, 34, 97, 2, "getTForX"], [229, 35, 97, 20, "aX"], [229, 37, 97, 22], [229, 39, 97, 24], [230, 10, 100, 4], [230, 14, 100, 8, "intervalStart"], [230, 27, 100, 21], [230, 30, 100, 24], [230, 33, 100, 27], [231, 10, 101, 4], [231, 14, 101, 8, "currentSample"], [231, 27, 101, 21], [231, 30, 101, 24], [231, 31, 101, 25], [232, 10, 102, 4], [232, 16, 102, 10, "lastSample"], [232, 26, 102, 20], [232, 29, 102, 23, "kSplineTableSize"], [232, 45, 102, 39], [232, 48, 102, 42], [232, 49, 102, 43], [233, 10, 103, 4], [233, 17, 103, 11, "currentSample"], [233, 30, 103, 24], [233, 35, 103, 29, "lastSample"], [233, 45, 103, 39], [233, 49, 103, 43, "sampleValues"], [233, 61, 103, 55], [233, 62, 103, 56, "currentSample"], [233, 75, 103, 69], [233, 76, 103, 70], [233, 80, 103, 74, "aX"], [233, 82, 103, 76], [233, 84, 103, 78], [233, 86, 103, 80, "currentSample"], [233, 99, 103, 93], [233, 101, 103, 95], [234, 12, 104, 6, "intervalStart"], [234, 25, 104, 19], [234, 29, 104, 23, "kSampleStepSize"], [234, 44, 104, 38], [235, 10, 105, 4], [236, 10, 106, 4], [236, 12, 106, 6, "currentSample"], [236, 25, 106, 19], [238, 10, 108, 4], [239, 10, 109, 4], [239, 16, 109, 10, "dist"], [239, 20, 109, 14], [239, 23, 109, 17], [239, 24, 109, 18, "aX"], [239, 26, 109, 20], [239, 29, 109, 23, "sampleValues"], [239, 41, 109, 35], [239, 42, 109, 36, "currentSample"], [239, 55, 109, 49], [239, 56, 109, 50], [239, 61, 109, 55, "sampleValues"], [239, 73, 109, 67], [239, 74, 109, 68, "currentSample"], [239, 87, 109, 81], [239, 90, 109, 84], [239, 91, 109, 85], [239, 92, 109, 86], [239, 95, 109, 89, "sampleValues"], [239, 107, 109, 101], [239, 108, 109, 102, "currentSample"], [239, 121, 109, 115], [239, 122, 109, 116], [239, 123, 109, 117], [240, 10, 110, 4], [240, 16, 110, 10, "guessForT"], [240, 25, 110, 19], [240, 28, 110, 22, "intervalStart"], [240, 41, 110, 35], [240, 44, 110, 38, "dist"], [240, 48, 110, 42], [240, 51, 110, 45, "kSampleStepSize"], [240, 66, 110, 60], [241, 10, 111, 4], [241, 16, 111, 10, "initialSlope"], [241, 28, 111, 22], [241, 31, 111, 25, "getSlope"], [241, 39, 111, 33], [241, 40, 111, 34, "guessForT"], [241, 49, 111, 43], [241, 51, 111, 45, "mX1"], [241, 54, 111, 48], [241, 56, 111, 50, "mX2"], [241, 59, 111, 53], [241, 60, 111, 54], [242, 10, 112, 4], [242, 14, 112, 8, "initialSlope"], [242, 26, 112, 20], [242, 30, 112, 24, "NEWTON_MIN_SLOPE"], [242, 46, 112, 40], [242, 48, 112, 42], [243, 12, 113, 6], [243, 19, 113, 13, "newtonRaphsonIterate"], [243, 39, 113, 33], [243, 40, 113, 34, "aX"], [243, 42, 113, 36], [243, 44, 113, 38, "guessForT"], [243, 53, 113, 47], [243, 55, 113, 49, "mX1"], [243, 58, 113, 52], [243, 60, 113, 54, "mX2"], [243, 63, 113, 57], [243, 64, 113, 58], [244, 10, 114, 4], [244, 11, 114, 5], [244, 17, 114, 11], [244, 21, 114, 15, "initialSlope"], [244, 33, 114, 27], [244, 38, 114, 32], [244, 41, 114, 35], [244, 43, 114, 37], [245, 12, 115, 6], [245, 19, 115, 13, "guessForT"], [245, 28, 115, 22], [246, 10, 116, 4], [246, 11, 116, 5], [246, 17, 116, 11], [247, 12, 117, 6], [247, 19, 117, 13, "binarySubdivide"], [247, 34, 117, 28], [247, 35, 117, 29, "aX"], [247, 37, 117, 31], [247, 39, 117, 33, "intervalStart"], [247, 52, 117, 46], [247, 54, 117, 48, "intervalStart"], [247, 67, 117, 61], [247, 70, 117, 64, "kSampleStepSize"], [247, 85, 117, 79], [247, 87, 117, 81, "mX1"], [247, 90, 117, 84], [247, 92, 117, 86, "mX2"], [247, 95, 117, 89], [247, 96, 117, 90], [248, 10, 118, 4], [249, 8, 119, 2], [249, 9, 119, 3], [250, 8, 119, 3, "getTForX"], [250, 16, 119, 3], [250, 17, 119, 3, "__closure"], [250, 26, 119, 3], [251, 10, 119, 3, "kSplineTableSize"], [251, 26, 119, 3], [252, 10, 119, 3, "sampleValues"], [252, 22, 119, 3], [253, 10, 119, 3, "kSampleStepSize"], [253, 25, 119, 3], [254, 10, 119, 3, "getSlope"], [254, 18, 119, 3], [255, 10, 119, 3, "mX1"], [255, 13, 119, 3], [256, 10, 119, 3, "mX2"], [256, 13, 119, 3], [257, 10, 119, 3, "NEWTON_MIN_SLOPE"], [257, 26, 119, 3], [258, 10, 119, 3, "newtonRaphsonIterate"], [258, 30, 119, 3], [259, 10, 119, 3, "binarySubdivide"], [260, 8, 119, 3], [261, 8, 119, 3, "getTForX"], [261, 16, 119, 3], [261, 17, 119, 3, "__workletHash"], [261, 30, 119, 3], [262, 8, 119, 3, "getTForX"], [262, 16, 119, 3], [262, 17, 119, 3, "__initData"], [262, 27, 119, 3], [262, 30, 119, 3, "_worklet_15383647275891_init_data"], [262, 63, 119, 3], [263, 8, 119, 3, "getTForX"], [263, 16, 119, 3], [263, 17, 119, 3, "__stackDetails"], [263, 31, 119, 3], [263, 34, 119, 3, "_e"], [263, 36, 119, 3], [264, 8, 119, 3], [264, 15, 119, 3, "getTForX"], [264, 23, 119, 3], [265, 6, 119, 3], [265, 7, 97, 2], [266, 6, 120, 2], [266, 13, 120, 9], [267, 8, 120, 9], [267, 14, 120, 9, "_e"], [267, 16, 120, 9], [267, 24, 120, 9, "global"], [267, 30, 120, 9], [267, 31, 120, 9, "Error"], [267, 36, 120, 9], [268, 8, 120, 9], [268, 14, 120, 9, "BezierEasing"], [268, 26, 120, 9], [268, 38, 120, 9, "BezierEasing"], [268, 39, 120, 31, "x"], [268, 40, 120, 32], [268, 42, 120, 34], [269, 10, 123, 4], [269, 14, 123, 8, "mX1"], [269, 17, 123, 11], [269, 22, 123, 16, "mY1"], [269, 25, 123, 19], [269, 29, 123, 23, "mX2"], [269, 32, 123, 26], [269, 37, 123, 31, "mY2"], [269, 40, 123, 34], [269, 42, 123, 36], [270, 12, 124, 6], [270, 19, 124, 13, "x"], [270, 20, 124, 14], [270, 21, 124, 15], [270, 22, 124, 16], [271, 10, 125, 4], [272, 10, 126, 4], [273, 10, 127, 4], [273, 14, 127, 8, "x"], [273, 15, 127, 9], [273, 20, 127, 14], [273, 21, 127, 15], [273, 23, 127, 17], [274, 12, 128, 6], [274, 19, 128, 13], [274, 20, 128, 14], [275, 10, 129, 4], [276, 10, 130, 4], [276, 14, 130, 8, "x"], [276, 15, 130, 9], [276, 20, 130, 14], [276, 21, 130, 15], [276, 23, 130, 17], [277, 12, 131, 6], [277, 19, 131, 13], [277, 20, 131, 14], [278, 10, 132, 4], [279, 10, 133, 4], [279, 17, 133, 11, "calcBezier"], [279, 27, 133, 21], [279, 28, 133, 22, "getTForX"], [279, 36, 133, 30], [279, 37, 133, 31, "x"], [279, 38, 133, 32], [279, 39, 133, 33], [279, 41, 133, 35, "mY1"], [279, 44, 133, 38], [279, 46, 133, 40, "mY2"], [279, 49, 133, 43], [279, 50, 133, 44], [280, 8, 134, 2], [280, 9, 134, 3], [281, 8, 134, 3, "BezierEasing"], [281, 20, 134, 3], [281, 21, 134, 3, "__closure"], [281, 30, 134, 3], [282, 10, 134, 3, "mX1"], [282, 13, 134, 3], [283, 10, 134, 3, "mY1"], [283, 13, 134, 3], [284, 10, 134, 3, "mX2"], [284, 13, 134, 3], [285, 10, 134, 3, "mY2"], [285, 13, 134, 3], [286, 10, 134, 3, "calcBezier"], [286, 20, 134, 3], [287, 10, 134, 3, "getTForX"], [288, 8, 134, 3], [289, 8, 134, 3, "BezierEasing"], [289, 20, 134, 3], [289, 21, 134, 3, "__workletHash"], [289, 34, 134, 3], [290, 8, 134, 3, "BezierEasing"], [290, 20, 134, 3], [290, 21, 134, 3, "__initData"], [290, 31, 134, 3], [290, 34, 134, 3, "_worklet_7919274967463_init_data"], [290, 66, 134, 3], [291, 8, 134, 3, "BezierEasing"], [291, 20, 134, 3], [291, 21, 134, 3, "__stackDetails"], [291, 35, 134, 3], [291, 38, 134, 3, "_e"], [291, 40, 134, 3], [292, 8, 134, 3], [292, 15, 134, 3, "BezierEasing"], [292, 27, 134, 3], [293, 6, 134, 3], [293, 7, 120, 9], [294, 4, 135, 0], [294, 5, 135, 1], [295, 4, 135, 1, "<PERSON><PERSON>"], [295, 10, 135, 1], [295, 11, 135, 1, "__closure"], [295, 20, 135, 1], [296, 6, 135, 1, "kSplineTableSize"], [296, 22, 135, 1], [297, 6, 135, 1, "calcBezier"], [297, 16, 135, 1], [298, 6, 135, 1, "kSampleStepSize"], [298, 21, 135, 1], [299, 6, 135, 1, "getSlope"], [299, 14, 135, 1], [300, 6, 135, 1, "NEWTON_MIN_SLOPE"], [300, 22, 135, 1], [301, 6, 135, 1, "newtonRaphsonIterate"], [301, 26, 135, 1], [302, 6, 135, 1, "binarySubdivide"], [303, 4, 135, 1], [304, 4, 135, 1, "<PERSON><PERSON>"], [304, 10, 135, 1], [304, 11, 135, 1, "__workletHash"], [304, 24, 135, 1], [305, 4, 135, 1, "<PERSON><PERSON>"], [305, 10, 135, 1], [305, 11, 135, 1, "__initData"], [305, 21, 135, 1], [305, 24, 135, 1, "_worklet_6918608934740_init_data"], [305, 56, 135, 1], [306, 4, 135, 1, "<PERSON><PERSON>"], [306, 10, 135, 1], [306, 11, 135, 1, "__stackDetails"], [306, 25, 135, 1], [306, 28, 135, 1, "_e"], [306, 30, 135, 1], [307, 4, 135, 1], [307, 11, 135, 1, "<PERSON><PERSON>"], [307, 17, 135, 1], [308, 2, 135, 1], [308, 3, 77, 7], [309, 0, 77, 7], [309, 3]], "functionMap": {"names": ["<global>", "A", "B", "C", "calcBezier", "getSlope", "binarySubdivide", "newtonRaphsonIterate", "<PERSON><PERSON>", "LinearEasing", "getTForX", "BezierEasing"], "mappings": "AAA;ACiB;CDI;AEC;CFI;AGC;CHI;AIG;CJI;AKG;CLI;AMC;CNgB;AOC;CPY;OQC;ECG;GDI;EEa;GFsB;SGC;GHc;CRC"}}, "type": "js/module"}]}