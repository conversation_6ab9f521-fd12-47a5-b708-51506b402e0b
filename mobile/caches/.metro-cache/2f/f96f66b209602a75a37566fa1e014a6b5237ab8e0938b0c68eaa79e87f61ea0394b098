{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _default = exports.default = {\n    get forceTouchAvailable() {\n      return false;\n    }\n  };\n});", "lineCount": 11, "map": [[6, 35, 1, 15], [7, 4, 2, 2], [7, 8, 2, 6, "forceTouchAvailable"], [7, 27, 2, 25, "forceTouchAvailable"], [7, 28, 2, 25], [7, 30, 2, 28], [8, 6, 3, 4], [8, 13, 3, 11], [8, 18, 3, 16], [9, 4, 4, 2], [10, 2, 6, 0], [10, 3, 6, 1], [11, 0, 6, 1], [11, 3]], "functionMap": {"names": ["<global>", "default.get__forceTouchAvailable"], "mappings": "AAA;ECC;GDE"}}, "type": "js/module"}]}