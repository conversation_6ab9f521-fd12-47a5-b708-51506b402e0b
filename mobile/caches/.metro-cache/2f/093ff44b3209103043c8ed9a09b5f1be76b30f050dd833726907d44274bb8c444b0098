{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isImageFilter = exports.TileMode = void 0;\n  let TileMode = exports.TileMode = /*#__PURE__*/function (TileMode) {\n    /**\n     *  Replicate the edge color if the shader draws outside of its\n     *  original bounds.\n     */\n    TileMode[TileMode[\"Clamp\"] = 0] = \"Clamp\";\n    /**\n     *  Repeat the shader's image horizontally and vertically.\n     */\n    TileMode[TileMode[\"Repeat\"] = 1] = \"Repeat\";\n    /**\n     *  Repeat the shader's image horizontally and vertically, alternating\n     *  mirror images so that adjacent images always seam.\n     */\n    TileMode[TileMode[\"Mirror\"] = 2] = \"Mirror\";\n    /**\n     *  Only draw within the original domain, return transparent-black everywhere else.\n     */\n    TileMode[TileMode[\"Decal\"] = 3] = \"Decal\";\n    return TileMode;\n  }({});\n  const isImageFilter = obj => obj !== null && obj.__typename__ === \"ImageFilter\";\n  exports.isImageFilter = isImageFilter;\n});", "lineCount": 29, "map": [[6, 2, 1, 7], [6, 6, 1, 11, "TileMode"], [6, 14, 1, 19], [6, 17, 1, 19, "exports"], [6, 24, 1, 19], [6, 25, 1, 19, "TileMode"], [6, 33, 1, 19], [6, 36, 1, 22], [6, 49, 1, 35], [6, 59, 1, 45, "TileMode"], [6, 67, 1, 53], [6, 69, 1, 55], [7, 4, 2, 2], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 4, 6, 2, "TileMode"], [11, 12, 6, 10], [11, 13, 6, 11, "TileMode"], [11, 21, 6, 19], [11, 22, 6, 20], [11, 29, 6, 27], [11, 30, 6, 28], [11, 33, 6, 31], [11, 34, 6, 32], [11, 35, 6, 33], [11, 38, 6, 36], [11, 45, 6, 43], [12, 4, 7, 2], [13, 0, 8, 0], [14, 0, 9, 0], [15, 4, 10, 2, "TileMode"], [15, 12, 10, 10], [15, 13, 10, 11, "TileMode"], [15, 21, 10, 19], [15, 22, 10, 20], [15, 30, 10, 28], [15, 31, 10, 29], [15, 34, 10, 32], [15, 35, 10, 33], [15, 36, 10, 34], [15, 39, 10, 37], [15, 47, 10, 45], [16, 4, 11, 2], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 4, 15, 2, "TileMode"], [20, 12, 15, 10], [20, 13, 15, 11, "TileMode"], [20, 21, 15, 19], [20, 22, 15, 20], [20, 30, 15, 28], [20, 31, 15, 29], [20, 34, 15, 32], [20, 35, 15, 33], [20, 36, 15, 34], [20, 39, 15, 37], [20, 47, 15, 45], [21, 4, 16, 2], [22, 0, 17, 0], [23, 0, 18, 0], [24, 4, 19, 2, "TileMode"], [24, 12, 19, 10], [24, 13, 19, 11, "TileMode"], [24, 21, 19, 19], [24, 22, 19, 20], [24, 29, 19, 27], [24, 30, 19, 28], [24, 33, 19, 31], [24, 34, 19, 32], [24, 35, 19, 33], [24, 38, 19, 36], [24, 45, 19, 43], [25, 4, 20, 2], [25, 11, 20, 9, "TileMode"], [25, 19, 20, 17], [26, 2, 21, 0], [26, 3, 21, 1], [26, 4, 21, 2], [26, 5, 21, 3], [26, 6, 21, 4], [26, 7, 21, 5], [27, 2, 22, 7], [27, 8, 22, 13, "isImageFilter"], [27, 21, 22, 26], [27, 24, 22, 29, "obj"], [27, 27, 22, 32], [27, 31, 22, 36, "obj"], [27, 34, 22, 39], [27, 39, 22, 44], [27, 43, 22, 48], [27, 47, 22, 52, "obj"], [27, 50, 22, 55], [27, 51, 22, 56, "__typename__"], [27, 63, 22, 68], [27, 68, 22, 73], [27, 81, 22, 86], [28, 2, 22, 87, "exports"], [28, 9, 22, 87], [28, 10, 22, 87, "isImageFilter"], [28, 23, 22, 87], [28, 26, 22, 87, "isImageFilter"], [28, 39, 22, 87], [29, 0, 22, 87], [29, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "isImageFilter"], "mappings": "AAA,mCC;CDoB;6BEC,yDF"}}, "type": "js/module"}]}