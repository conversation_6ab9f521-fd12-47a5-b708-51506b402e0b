{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 16, "index": 171}, "end": {"line": 5, "column": 32, "index": 187}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./global-state/routing", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 18, "index": 207}, "end": {"line": 6, "column": 51, "index": 240}}], "key": "DFKojlqGLHtBQo5/sne1vHRUyuU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.router = void 0;\n  exports.ImperativeApiEmitter = ImperativeApiEmitter;\n  var react_1 = require(_dependencyMap[0], \"react\");\n  var routing_1 = require(_dependencyMap[1], \"./global-state/routing\");\n  /**\n   * @hidden\n   */\n  exports.router = {\n    navigate: routing_1.navigate,\n    push: routing_1.push,\n    dismiss: routing_1.dismiss,\n    dismissAll: routing_1.dismissAll,\n    dismissTo: routing_1.dismissTo,\n    canDismiss: routing_1.canDismiss,\n    replace: routing_1.replace,\n    back: () => (0, routing_1.goBack)(),\n    canGoBack: routing_1.canGoBack,\n    reload: routing_1.reload,\n    prefetch: routing_1.prefetch,\n    setParams: routing_1.setParams\n  };\n  function ImperativeApiEmitter() {\n    var events = (0, react_1.useSyncExternalStore)(routing_1.routingQueue.subscribe, routing_1.routingQueue.snapshot, routing_1.routingQueue.snapshot);\n    (0, react_1.useEffect)(() => {\n      routing_1.routingQueue.run();\n    }, [events]);\n    return null;\n  }\n});", "lineCount": 35, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "router"], [7, 16, 3, 14], [7, 19, 3, 17], [7, 24, 3, 22], [7, 25, 3, 23], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "ImperativeApiEmitter"], [8, 30, 4, 28], [8, 33, 4, 31, "ImperativeApiEmitter"], [8, 53, 4, 51], [9, 2, 5, 0], [9, 6, 5, 6, "react_1"], [9, 13, 5, 13], [9, 16, 5, 16, "require"], [9, 23, 5, 23], [9, 24, 5, 23, "_dependencyMap"], [9, 38, 5, 23], [9, 50, 5, 31], [9, 51, 5, 32], [10, 2, 6, 0], [10, 6, 6, 6, "routing_1"], [10, 15, 6, 15], [10, 18, 6, 18, "require"], [10, 25, 6, 25], [10, 26, 6, 25, "_dependencyMap"], [10, 40, 6, 25], [10, 69, 6, 50], [10, 70, 6, 51], [11, 2, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 2, 10, 0, "exports"], [14, 9, 10, 7], [14, 10, 10, 8, "router"], [14, 16, 10, 14], [14, 19, 10, 17], [15, 4, 11, 4, "navigate"], [15, 12, 11, 12], [15, 14, 11, 14, "routing_1"], [15, 23, 11, 23], [15, 24, 11, 24, "navigate"], [15, 32, 11, 32], [16, 4, 12, 4, "push"], [16, 8, 12, 8], [16, 10, 12, 10, "routing_1"], [16, 19, 12, 19], [16, 20, 12, 20, "push"], [16, 24, 12, 24], [17, 4, 13, 4, "dismiss"], [17, 11, 13, 11], [17, 13, 13, 13, "routing_1"], [17, 22, 13, 22], [17, 23, 13, 23, "dismiss"], [17, 30, 13, 30], [18, 4, 14, 4, "dismissAll"], [18, 14, 14, 14], [18, 16, 14, 16, "routing_1"], [18, 25, 14, 25], [18, 26, 14, 26, "dismissAll"], [18, 36, 14, 36], [19, 4, 15, 4, "dismissTo"], [19, 13, 15, 13], [19, 15, 15, 15, "routing_1"], [19, 24, 15, 24], [19, 25, 15, 25, "dismissTo"], [19, 34, 15, 34], [20, 4, 16, 4, "<PERSON><PERSON><PERSON><PERSON>"], [20, 14, 16, 14], [20, 16, 16, 16, "routing_1"], [20, 25, 16, 25], [20, 26, 16, 26, "<PERSON><PERSON><PERSON><PERSON>"], [20, 36, 16, 36], [21, 4, 17, 4, "replace"], [21, 11, 17, 11], [21, 13, 17, 13, "routing_1"], [21, 22, 17, 22], [21, 23, 17, 23, "replace"], [21, 30, 17, 30], [22, 4, 18, 4, "back"], [22, 8, 18, 8], [22, 10, 18, 10, "back"], [22, 11, 18, 10], [22, 16, 18, 16], [22, 17, 18, 17], [22, 18, 18, 18], [22, 20, 18, 20, "routing_1"], [22, 29, 18, 29], [22, 30, 18, 30, "goBack"], [22, 36, 18, 36], [22, 38, 18, 38], [22, 39, 18, 39], [23, 4, 19, 4, "canGoBack"], [23, 13, 19, 13], [23, 15, 19, 15, "routing_1"], [23, 24, 19, 24], [23, 25, 19, 25, "canGoBack"], [23, 34, 19, 34], [24, 4, 20, 4, "reload"], [24, 10, 20, 10], [24, 12, 20, 12, "routing_1"], [24, 21, 20, 21], [24, 22, 20, 22, "reload"], [24, 28, 20, 28], [25, 4, 21, 4, "prefetch"], [25, 12, 21, 12], [25, 14, 21, 14, "routing_1"], [25, 23, 21, 23], [25, 24, 21, 24, "prefetch"], [25, 32, 21, 32], [26, 4, 22, 4, "setParams"], [26, 13, 22, 13], [26, 15, 22, 15, "routing_1"], [26, 24, 22, 24], [26, 25, 22, 25, "setParams"], [27, 2, 23, 0], [27, 3, 23, 1], [28, 2, 24, 0], [28, 11, 24, 9, "ImperativeApiEmitter"], [28, 31, 24, 29, "ImperativeApiEmitter"], [28, 32, 24, 29], [28, 34, 24, 32], [29, 4, 25, 4], [29, 8, 25, 10, "events"], [29, 14, 25, 16], [29, 17, 25, 19], [29, 18, 25, 20], [29, 19, 25, 21], [29, 21, 25, 23, "react_1"], [29, 28, 25, 30], [29, 29, 25, 31, "useSyncExternalStore"], [29, 49, 25, 51], [29, 51, 25, 53, "routing_1"], [29, 60, 25, 62], [29, 61, 25, 63, "routingQueue"], [29, 73, 25, 75], [29, 74, 25, 76, "subscribe"], [29, 83, 25, 85], [29, 85, 25, 87, "routing_1"], [29, 94, 25, 96], [29, 95, 25, 97, "routingQueue"], [29, 107, 25, 109], [29, 108, 25, 110, "snapshot"], [29, 116, 25, 118], [29, 118, 25, 120, "routing_1"], [29, 127, 25, 129], [29, 128, 25, 130, "routingQueue"], [29, 140, 25, 142], [29, 141, 25, 143, "snapshot"], [29, 149, 25, 151], [29, 150, 25, 152], [30, 4, 26, 4], [30, 5, 26, 5], [30, 6, 26, 6], [30, 8, 26, 8, "react_1"], [30, 15, 26, 15], [30, 16, 26, 16, "useEffect"], [30, 25, 26, 25], [30, 27, 26, 27], [30, 33, 26, 33], [31, 6, 27, 8, "routing_1"], [31, 15, 27, 17], [31, 16, 27, 18, "routingQueue"], [31, 28, 27, 30], [31, 29, 27, 31, "run"], [31, 32, 27, 34], [31, 33, 27, 35], [31, 34, 27, 36], [32, 4, 28, 4], [32, 5, 28, 5], [32, 7, 28, 7], [32, 8, 28, 8, "events"], [32, 14, 28, 14], [32, 15, 28, 15], [32, 16, 28, 16], [33, 4, 29, 4], [33, 11, 29, 11], [33, 15, 29, 15], [34, 2, 30, 0], [35, 0, 30, 1], [35, 3]], "functionMap": {"names": ["<global>", "exports.router.back", "ImperativeApiEmitter", "<anonymous>"], "mappings": "AAA;UCiB,6BD;AEM;2BCE;KDE;CFE"}}, "type": "js/module"}]}