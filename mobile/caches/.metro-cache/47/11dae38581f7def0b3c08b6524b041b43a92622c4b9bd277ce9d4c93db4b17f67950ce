{"dependencies": [{"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 71, "index": 86}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 87}, "end": {"line": 4, "column": 31, "index": 118}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./useLinkBuilder.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 119}, "end": {"line": 5, "column": 53, "index": 172}}], "key": "GFkuOnxplgG4Cj6Xcb1yAsF8qu4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useLinkTo = useLinkTo;\n  var _core = require(_dependencyMap[0], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _useLinkBuilder = require(_dependencyMap[2], \"./useLinkBuilder.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Helper to navigate to a screen using a href based on the linking options.\n   *\n   * @returns function that receives the href to navigate to.\n   */\n  function useLinkTo() {\n    const navigation = React.useContext(_core.NavigationContainerRefContext);\n    const {\n      buildAction\n    } = (0, _useLinkBuilder.useLinkBuilder)();\n    const linkTo = React.useCallback(href => {\n      if (navigation === undefined) {\n        throw new Error(\"Couldn't find a navigation object. Is your component inside NavigationContainer?\");\n      }\n      const action = buildAction(href);\n      navigation.dispatch(action);\n    }, [buildAction, navigation]);\n    return linkTo;\n  }\n});", "lineCount": 31, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useLinkTo"], [7, 19, 1, 13], [7, 22, 1, 13, "useLinkTo"], [7, 31, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_useLinkBuilder"], [10, 21, 5, 0], [10, 24, 5, 0, "require"], [10, 31, 5, 0], [10, 32, 5, 0, "_dependencyMap"], [10, 46, 5, 0], [11, 2, 5, 53], [11, 11, 5, 53, "_interopRequireWildcard"], [11, 35, 5, 53, "e"], [11, 36, 5, 53], [11, 38, 5, 53, "t"], [11, 39, 5, 53], [11, 68, 5, 53, "WeakMap"], [11, 75, 5, 53], [11, 81, 5, 53, "r"], [11, 82, 5, 53], [11, 89, 5, 53, "WeakMap"], [11, 96, 5, 53], [11, 100, 5, 53, "n"], [11, 101, 5, 53], [11, 108, 5, 53, "WeakMap"], [11, 115, 5, 53], [11, 127, 5, 53, "_interopRequireWildcard"], [11, 150, 5, 53], [11, 162, 5, 53, "_interopRequireWildcard"], [11, 163, 5, 53, "e"], [11, 164, 5, 53], [11, 166, 5, 53, "t"], [11, 167, 5, 53], [11, 176, 5, 53, "t"], [11, 177, 5, 53], [11, 181, 5, 53, "e"], [11, 182, 5, 53], [11, 186, 5, 53, "e"], [11, 187, 5, 53], [11, 188, 5, 53, "__esModule"], [11, 198, 5, 53], [11, 207, 5, 53, "e"], [11, 208, 5, 53], [11, 214, 5, 53, "o"], [11, 215, 5, 53], [11, 217, 5, 53, "i"], [11, 218, 5, 53], [11, 220, 5, 53, "f"], [11, 221, 5, 53], [11, 226, 5, 53, "__proto__"], [11, 235, 5, 53], [11, 243, 5, 53, "default"], [11, 250, 5, 53], [11, 252, 5, 53, "e"], [11, 253, 5, 53], [11, 270, 5, 53, "e"], [11, 271, 5, 53], [11, 294, 5, 53, "e"], [11, 295, 5, 53], [11, 320, 5, 53, "e"], [11, 321, 5, 53], [11, 330, 5, 53, "f"], [11, 331, 5, 53], [11, 337, 5, 53, "o"], [11, 338, 5, 53], [11, 341, 5, 53, "t"], [11, 342, 5, 53], [11, 345, 5, 53, "n"], [11, 346, 5, 53], [11, 349, 5, 53, "r"], [11, 350, 5, 53], [11, 358, 5, 53, "o"], [11, 359, 5, 53], [11, 360, 5, 53, "has"], [11, 363, 5, 53], [11, 364, 5, 53, "e"], [11, 365, 5, 53], [11, 375, 5, 53, "o"], [11, 376, 5, 53], [11, 377, 5, 53, "get"], [11, 380, 5, 53], [11, 381, 5, 53, "e"], [11, 382, 5, 53], [11, 385, 5, 53, "o"], [11, 386, 5, 53], [11, 387, 5, 53, "set"], [11, 390, 5, 53], [11, 391, 5, 53, "e"], [11, 392, 5, 53], [11, 394, 5, 53, "f"], [11, 395, 5, 53], [11, 411, 5, 53, "t"], [11, 412, 5, 53], [11, 416, 5, 53, "e"], [11, 417, 5, 53], [11, 433, 5, 53, "t"], [11, 434, 5, 53], [11, 441, 5, 53, "hasOwnProperty"], [11, 455, 5, 53], [11, 456, 5, 53, "call"], [11, 460, 5, 53], [11, 461, 5, 53, "e"], [11, 462, 5, 53], [11, 464, 5, 53, "t"], [11, 465, 5, 53], [11, 472, 5, 53, "i"], [11, 473, 5, 53], [11, 477, 5, 53, "o"], [11, 478, 5, 53], [11, 481, 5, 53, "Object"], [11, 487, 5, 53], [11, 488, 5, 53, "defineProperty"], [11, 502, 5, 53], [11, 507, 5, 53, "Object"], [11, 513, 5, 53], [11, 514, 5, 53, "getOwnPropertyDescriptor"], [11, 538, 5, 53], [11, 539, 5, 53, "e"], [11, 540, 5, 53], [11, 542, 5, 53, "t"], [11, 543, 5, 53], [11, 550, 5, 53, "i"], [11, 551, 5, 53], [11, 552, 5, 53, "get"], [11, 555, 5, 53], [11, 559, 5, 53, "i"], [11, 560, 5, 53], [11, 561, 5, 53, "set"], [11, 564, 5, 53], [11, 568, 5, 53, "o"], [11, 569, 5, 53], [11, 570, 5, 53, "f"], [11, 571, 5, 53], [11, 573, 5, 53, "t"], [11, 574, 5, 53], [11, 576, 5, 53, "i"], [11, 577, 5, 53], [11, 581, 5, 53, "f"], [11, 582, 5, 53], [11, 583, 5, 53, "t"], [11, 584, 5, 53], [11, 588, 5, 53, "e"], [11, 589, 5, 53], [11, 590, 5, 53, "t"], [11, 591, 5, 53], [11, 602, 5, 53, "f"], [11, 603, 5, 53], [11, 608, 5, 53, "e"], [11, 609, 5, 53], [11, 611, 5, 53, "t"], [11, 612, 5, 53], [12, 2, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 2, 12, 7], [17, 11, 12, 16, "useLinkTo"], [17, 20, 12, 25, "useLinkTo"], [17, 21, 12, 25], [17, 23, 12, 28], [18, 4, 13, 2], [18, 10, 13, 8, "navigation"], [18, 20, 13, 18], [18, 23, 13, 21, "React"], [18, 28, 13, 26], [18, 29, 13, 27, "useContext"], [18, 39, 13, 37], [18, 40, 13, 38, "NavigationContainerRefContext"], [18, 75, 13, 67], [18, 76, 13, 68], [19, 4, 14, 2], [19, 10, 14, 8], [20, 6, 15, 4, "buildAction"], [21, 4, 16, 2], [21, 5, 16, 3], [21, 8, 16, 6], [21, 12, 16, 6, "useLinkBuilder"], [21, 42, 16, 20], [21, 44, 16, 21], [21, 45, 16, 22], [22, 4, 17, 2], [22, 10, 17, 8, "linkTo"], [22, 16, 17, 14], [22, 19, 17, 17, "React"], [22, 24, 17, 22], [22, 25, 17, 23, "useCallback"], [22, 36, 17, 34], [22, 37, 17, 35, "href"], [22, 41, 17, 39], [22, 45, 17, 43], [23, 6, 18, 4], [23, 10, 18, 8, "navigation"], [23, 20, 18, 18], [23, 25, 18, 23, "undefined"], [23, 34, 18, 32], [23, 36, 18, 34], [24, 8, 19, 6], [24, 14, 19, 12], [24, 18, 19, 16, "Error"], [24, 23, 19, 21], [24, 24, 19, 22], [24, 106, 19, 104], [24, 107, 19, 105], [25, 6, 20, 4], [26, 6, 21, 4], [26, 12, 21, 10, "action"], [26, 18, 21, 16], [26, 21, 21, 19, "buildAction"], [26, 32, 21, 30], [26, 33, 21, 31, "href"], [26, 37, 21, 35], [26, 38, 21, 36], [27, 6, 22, 4, "navigation"], [27, 16, 22, 14], [27, 17, 22, 15, "dispatch"], [27, 25, 22, 23], [27, 26, 22, 24, "action"], [27, 32, 22, 30], [27, 33, 22, 31], [28, 4, 23, 2], [28, 5, 23, 3], [28, 7, 23, 5], [28, 8, 23, 6, "buildAction"], [28, 19, 23, 17], [28, 21, 23, 19, "navigation"], [28, 31, 23, 29], [28, 32, 23, 30], [28, 33, 23, 31], [29, 4, 24, 2], [29, 11, 24, 9, "linkTo"], [29, 17, 24, 15], [30, 2, 25, 0], [31, 0, 25, 1], [31, 3]], "functionMap": {"names": ["<global>", "useLinkTo", "linkTo"], "mappings": "AAA;OCW;mCCK;GDM;CDE"}}, "type": "js/module"}]}