{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 118}, "end": {"line": 5, "column": 111, "index": 229}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "./useFrameSize.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 230}, "end": {"line": 6, "column": 54, "index": 284}}], "key": "dRzp9Mme73SbFUGqz80tDHJoVo0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 285}, "end": {"line": 7, "column": 48, "index": 333}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SafeAreaProviderCompat = SafeAreaProviderCompat;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Dimensions\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/View\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[6], \"react-native-safe-area-context\");\n  var _useFrameSize = require(_dependencyMap[7], \"./useFrameSize.js\");\n  var _jsxRuntime = require(_dependencyMap[8], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width = 0,\n    height = 0\n  } = _Dimensions.default.get('window');\n\n  // To support SSR on web, we need to have empty insets for initial values\n  // Otherwise there can be mismatch between SSR and client output\n  // We also need to specify empty values to support tests environments\n  const initialMetrics = _Platform.default.OS === 'web' || _reactNativeSafeAreaContext.initialWindowMetrics == null ? {\n    frame: {\n      x: 0,\n      y: 0,\n      width,\n      height\n    },\n    insets: {\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0\n    }\n  } : _reactNativeSafeAreaContext.initialWindowMetrics;\n  function SafeAreaProviderCompat({\n    children,\n    style\n  }) {\n    const insets = React.useContext(_reactNativeSafeAreaContext.SafeAreaInsetsContext);\n    children = /*#__PURE__*/(0, _jsxRuntime.jsx)(_useFrameSize.FrameSizeProvider, {\n      initialFrame: initialMetrics.frame,\n      children: children\n    });\n    if (insets) {\n      // If we already have insets, don't wrap the stack in another safe area provider\n      // This avoids an issue with updates at the cost of potentially incorrect values\n      // https://github.com/react-navigation/react-navigation/issues/174\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n        style: [styles.container, style],\n        children: children\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeSafeAreaContext.SafeAreaProvider, {\n      initialMetrics: initialMetrics,\n      style: style,\n      children: children\n    });\n  }\n  SafeAreaProviderCompat.initialMetrics = initialMetrics;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1\n    }\n  });\n});", "lineCount": 70, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "SafeAreaProviderCompat"], [8, 32, 1, 13], [8, 35, 1, 13, "SafeAreaProviderCompat"], [8, 57, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "React"], [9, 11, 3, 0], [9, 14, 3, 0, "_interopRequireWildcard"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 3, 31], [10, 6, 3, 31, "_Dimensions"], [10, 17, 3, 31], [10, 20, 3, 31, "_interopRequireDefault"], [10, 42, 3, 31], [10, 43, 3, 31, "require"], [10, 50, 3, 31], [10, 51, 3, 31, "_dependencyMap"], [10, 65, 3, 31], [11, 2, 3, 31], [11, 6, 3, 31, "_Platform"], [11, 15, 3, 31], [11, 18, 3, 31, "_interopRequireDefault"], [11, 40, 3, 31], [11, 41, 3, 31, "require"], [11, 48, 3, 31], [11, 49, 3, 31, "_dependencyMap"], [11, 63, 3, 31], [12, 2, 3, 31], [12, 6, 3, 31, "_StyleSheet"], [12, 17, 3, 31], [12, 20, 3, 31, "_interopRequireDefault"], [12, 42, 3, 31], [12, 43, 3, 31, "require"], [12, 50, 3, 31], [12, 51, 3, 31, "_dependencyMap"], [12, 65, 3, 31], [13, 2, 3, 31], [13, 6, 3, 31, "_View"], [13, 11, 3, 31], [13, 14, 3, 31, "_interopRequireDefault"], [13, 36, 3, 31], [13, 37, 3, 31, "require"], [13, 44, 3, 31], [13, 45, 3, 31, "_dependencyMap"], [13, 59, 3, 31], [14, 2, 5, 0], [14, 6, 5, 0, "_reactNativeSafeAreaContext"], [14, 33, 5, 0], [14, 36, 5, 0, "require"], [14, 43, 5, 0], [14, 44, 5, 0, "_dependencyMap"], [14, 58, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_useFrameSize"], [15, 19, 6, 0], [15, 22, 6, 0, "require"], [15, 29, 6, 0], [15, 30, 6, 0, "_dependencyMap"], [15, 44, 6, 0], [16, 2, 7, 0], [16, 6, 7, 0, "_jsxRuntime"], [16, 17, 7, 0], [16, 20, 7, 0, "require"], [16, 27, 7, 0], [16, 28, 7, 0, "_dependencyMap"], [16, 42, 7, 0], [17, 2, 7, 48], [17, 11, 7, 48, "_interopRequireWildcard"], [17, 35, 7, 48, "e"], [17, 36, 7, 48], [17, 38, 7, 48, "t"], [17, 39, 7, 48], [17, 68, 7, 48, "WeakMap"], [17, 75, 7, 48], [17, 81, 7, 48, "r"], [17, 82, 7, 48], [17, 89, 7, 48, "WeakMap"], [17, 96, 7, 48], [17, 100, 7, 48, "n"], [17, 101, 7, 48], [17, 108, 7, 48, "WeakMap"], [17, 115, 7, 48], [17, 127, 7, 48, "_interopRequireWildcard"], [17, 150, 7, 48], [17, 162, 7, 48, "_interopRequireWildcard"], [17, 163, 7, 48, "e"], [17, 164, 7, 48], [17, 166, 7, 48, "t"], [17, 167, 7, 48], [17, 176, 7, 48, "t"], [17, 177, 7, 48], [17, 181, 7, 48, "e"], [17, 182, 7, 48], [17, 186, 7, 48, "e"], [17, 187, 7, 48], [17, 188, 7, 48, "__esModule"], [17, 198, 7, 48], [17, 207, 7, 48, "e"], [17, 208, 7, 48], [17, 214, 7, 48, "o"], [17, 215, 7, 48], [17, 217, 7, 48, "i"], [17, 218, 7, 48], [17, 220, 7, 48, "f"], [17, 221, 7, 48], [17, 226, 7, 48, "__proto__"], [17, 235, 7, 48], [17, 243, 7, 48, "default"], [17, 250, 7, 48], [17, 252, 7, 48, "e"], [17, 253, 7, 48], [17, 270, 7, 48, "e"], [17, 271, 7, 48], [17, 294, 7, 48, "e"], [17, 295, 7, 48], [17, 320, 7, 48, "e"], [17, 321, 7, 48], [17, 330, 7, 48, "f"], [17, 331, 7, 48], [17, 337, 7, 48, "o"], [17, 338, 7, 48], [17, 341, 7, 48, "t"], [17, 342, 7, 48], [17, 345, 7, 48, "n"], [17, 346, 7, 48], [17, 349, 7, 48, "r"], [17, 350, 7, 48], [17, 358, 7, 48, "o"], [17, 359, 7, 48], [17, 360, 7, 48, "has"], [17, 363, 7, 48], [17, 364, 7, 48, "e"], [17, 365, 7, 48], [17, 375, 7, 48, "o"], [17, 376, 7, 48], [17, 377, 7, 48, "get"], [17, 380, 7, 48], [17, 381, 7, 48, "e"], [17, 382, 7, 48], [17, 385, 7, 48, "o"], [17, 386, 7, 48], [17, 387, 7, 48, "set"], [17, 390, 7, 48], [17, 391, 7, 48, "e"], [17, 392, 7, 48], [17, 394, 7, 48, "f"], [17, 395, 7, 48], [17, 411, 7, 48, "t"], [17, 412, 7, 48], [17, 416, 7, 48, "e"], [17, 417, 7, 48], [17, 433, 7, 48, "t"], [17, 434, 7, 48], [17, 441, 7, 48, "hasOwnProperty"], [17, 455, 7, 48], [17, 456, 7, 48, "call"], [17, 460, 7, 48], [17, 461, 7, 48, "e"], [17, 462, 7, 48], [17, 464, 7, 48, "t"], [17, 465, 7, 48], [17, 472, 7, 48, "i"], [17, 473, 7, 48], [17, 477, 7, 48, "o"], [17, 478, 7, 48], [17, 481, 7, 48, "Object"], [17, 487, 7, 48], [17, 488, 7, 48, "defineProperty"], [17, 502, 7, 48], [17, 507, 7, 48, "Object"], [17, 513, 7, 48], [17, 514, 7, 48, "getOwnPropertyDescriptor"], [17, 538, 7, 48], [17, 539, 7, 48, "e"], [17, 540, 7, 48], [17, 542, 7, 48, "t"], [17, 543, 7, 48], [17, 550, 7, 48, "i"], [17, 551, 7, 48], [17, 552, 7, 48, "get"], [17, 555, 7, 48], [17, 559, 7, 48, "i"], [17, 560, 7, 48], [17, 561, 7, 48, "set"], [17, 564, 7, 48], [17, 568, 7, 48, "o"], [17, 569, 7, 48], [17, 570, 7, 48, "f"], [17, 571, 7, 48], [17, 573, 7, 48, "t"], [17, 574, 7, 48], [17, 576, 7, 48, "i"], [17, 577, 7, 48], [17, 581, 7, 48, "f"], [17, 582, 7, 48], [17, 583, 7, 48, "t"], [17, 584, 7, 48], [17, 588, 7, 48, "e"], [17, 589, 7, 48], [17, 590, 7, 48, "t"], [17, 591, 7, 48], [17, 602, 7, 48, "f"], [17, 603, 7, 48], [17, 608, 7, 48, "e"], [17, 609, 7, 48], [17, 611, 7, 48, "t"], [17, 612, 7, 48], [18, 2, 8, 0], [18, 8, 8, 6], [19, 4, 9, 2, "width"], [19, 9, 9, 7], [19, 12, 9, 10], [19, 13, 9, 11], [20, 4, 10, 2, "height"], [20, 10, 10, 8], [20, 13, 10, 11], [21, 2, 11, 0], [21, 3, 11, 1], [21, 6, 11, 4, "Dimensions"], [21, 25, 11, 14], [21, 26, 11, 15, "get"], [21, 29, 11, 18], [21, 30, 11, 19], [21, 38, 11, 27], [21, 39, 11, 28], [23, 2, 13, 0], [24, 2, 14, 0], [25, 2, 15, 0], [26, 2, 16, 0], [26, 8, 16, 6, "initialMetrics"], [26, 22, 16, 20], [26, 25, 16, 23, "Platform"], [26, 42, 16, 31], [26, 43, 16, 32, "OS"], [26, 45, 16, 34], [26, 50, 16, 39], [26, 55, 16, 44], [26, 59, 16, 48, "initialWindowMetrics"], [26, 107, 16, 68], [26, 111, 16, 72], [26, 115, 16, 76], [26, 118, 16, 79], [27, 4, 17, 2, "frame"], [27, 9, 17, 7], [27, 11, 17, 9], [28, 6, 18, 4, "x"], [28, 7, 18, 5], [28, 9, 18, 7], [28, 10, 18, 8], [29, 6, 19, 4, "y"], [29, 7, 19, 5], [29, 9, 19, 7], [29, 10, 19, 8], [30, 6, 20, 4, "width"], [30, 11, 20, 9], [31, 6, 21, 4, "height"], [32, 4, 22, 2], [32, 5, 22, 3], [33, 4, 23, 2, "insets"], [33, 10, 23, 8], [33, 12, 23, 10], [34, 6, 24, 4, "top"], [34, 9, 24, 7], [34, 11, 24, 9], [34, 12, 24, 10], [35, 6, 25, 4, "left"], [35, 10, 25, 8], [35, 12, 25, 10], [35, 13, 25, 11], [36, 6, 26, 4, "right"], [36, 11, 26, 9], [36, 13, 26, 11], [36, 14, 26, 12], [37, 6, 27, 4, "bottom"], [37, 12, 27, 10], [37, 14, 27, 12], [38, 4, 28, 2], [39, 2, 29, 0], [39, 3, 29, 1], [39, 6, 29, 4, "initialWindowMetrics"], [39, 54, 29, 24], [40, 2, 30, 7], [40, 11, 30, 16, "SafeAreaProviderCompat"], [40, 33, 30, 38, "SafeAreaProviderCompat"], [40, 34, 30, 39], [41, 4, 31, 2, "children"], [41, 12, 31, 10], [42, 4, 32, 2, "style"], [43, 2, 33, 0], [43, 3, 33, 1], [43, 5, 33, 3], [44, 4, 34, 2], [44, 10, 34, 8, "insets"], [44, 16, 34, 14], [44, 19, 34, 17, "React"], [44, 24, 34, 22], [44, 25, 34, 23, "useContext"], [44, 35, 34, 33], [44, 36, 34, 34, "SafeAreaInsetsContext"], [44, 85, 34, 55], [44, 86, 34, 56], [45, 4, 35, 2, "children"], [45, 12, 35, 10], [45, 15, 35, 13], [45, 28, 35, 26], [45, 32, 35, 26, "_jsx"], [45, 47, 35, 30], [45, 49, 35, 31, "FrameSizeProvider"], [45, 80, 35, 48], [45, 82, 35, 50], [46, 6, 36, 4, "initialFrame"], [46, 18, 36, 16], [46, 20, 36, 18, "initialMetrics"], [46, 34, 36, 32], [46, 35, 36, 33, "frame"], [46, 40, 36, 38], [47, 6, 37, 4, "children"], [47, 14, 37, 12], [47, 16, 37, 14, "children"], [48, 4, 38, 2], [48, 5, 38, 3], [48, 6, 38, 4], [49, 4, 39, 2], [49, 8, 39, 6, "insets"], [49, 14, 39, 12], [49, 16, 39, 14], [50, 6, 40, 4], [51, 6, 41, 4], [52, 6, 42, 4], [53, 6, 43, 4], [53, 13, 43, 11], [53, 26, 43, 24], [53, 30, 43, 24, "_jsx"], [53, 45, 43, 28], [53, 47, 43, 29, "View"], [53, 60, 43, 33], [53, 62, 43, 35], [54, 8, 44, 6, "style"], [54, 13, 44, 11], [54, 15, 44, 13], [54, 16, 44, 14, "styles"], [54, 22, 44, 20], [54, 23, 44, 21, "container"], [54, 32, 44, 30], [54, 34, 44, 32, "style"], [54, 39, 44, 37], [54, 40, 44, 38], [55, 8, 45, 6, "children"], [55, 16, 45, 14], [55, 18, 45, 16, "children"], [56, 6, 46, 4], [56, 7, 46, 5], [56, 8, 46, 6], [57, 4, 47, 2], [58, 4, 48, 2], [58, 11, 48, 9], [58, 24, 48, 22], [58, 28, 48, 22, "_jsx"], [58, 43, 48, 26], [58, 45, 48, 27, "SafeAreaProvider"], [58, 89, 48, 43], [58, 91, 48, 45], [59, 6, 49, 4, "initialMetrics"], [59, 20, 49, 18], [59, 22, 49, 20, "initialMetrics"], [59, 36, 49, 34], [60, 6, 50, 4, "style"], [60, 11, 50, 9], [60, 13, 50, 11, "style"], [60, 18, 50, 16], [61, 6, 51, 4, "children"], [61, 14, 51, 12], [61, 16, 51, 14, "children"], [62, 4, 52, 2], [62, 5, 52, 3], [62, 6, 52, 4], [63, 2, 53, 0], [64, 2, 54, 0, "SafeAreaProviderCompat"], [64, 24, 54, 22], [64, 25, 54, 23, "initialMetrics"], [64, 39, 54, 37], [64, 42, 54, 40, "initialMetrics"], [64, 56, 54, 54], [65, 2, 55, 0], [65, 8, 55, 6, "styles"], [65, 14, 55, 12], [65, 17, 55, 15, "StyleSheet"], [65, 36, 55, 25], [65, 37, 55, 26, "create"], [65, 43, 55, 32], [65, 44, 55, 33], [66, 4, 56, 2, "container"], [66, 13, 56, 11], [66, 15, 56, 13], [67, 6, 57, 4, "flex"], [67, 10, 57, 8], [67, 12, 57, 10], [68, 4, 58, 2], [69, 2, 59, 0], [69, 3, 59, 1], [69, 4, 59, 2], [70, 0, 59, 3], [70, 3]], "functionMap": {"names": ["<global>", "SafeAreaProviderCompat"], "mappings": "AAA;OC6B;CDuB"}}, "type": "js/module"}]}