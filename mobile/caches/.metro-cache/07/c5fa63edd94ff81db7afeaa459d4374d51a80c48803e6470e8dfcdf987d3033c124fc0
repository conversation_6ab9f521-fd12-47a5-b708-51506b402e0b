{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../parseErrorStack", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 225}, "end": {"line": 9, "column": 49, "index": 274}}], "key": "wrPJudFh/xDUxzGHJn8hfBxvTBo=", "exportNames": ["*"]}}, {"name": "../../LogBox", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 73, "column": 2, "index": 1988}, "end": {"line": 73, "column": 25, "index": 2011}}], "key": "+FR1rWrNkgiWnPcsbGf1WAzH/eI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _parseErrorStack = _interopRequireDefault(require(_dependencyMap[1], \"../parseErrorStack\"));\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  class SyntheticError extends Error {\n    name = '';\n  }\n\n  /**\n   * Handles the developer-visible aspect of errors and exceptions\n   */\n  let exceptionID = 0;\n  function parseException(e, isFatal) {\n    const stack = (0, _parseErrorStack.default)(e?.stack);\n    const currentExceptionID = ++exceptionID;\n    const originalMessage = e.message || '';\n    let message = originalMessage;\n    if (e.componentStack != null) {\n      message += `\\n\\nThis error is located at:${e.componentStack}`;\n    }\n    const namePrefix = e.name == null || e.name === '' ? '' : `${e.name}: `;\n    if (!message.startsWith(namePrefix)) {\n      message = namePrefix + message;\n    }\n    message = e.jsEngine == null ? message : `${message}, js engine: ${e.jsEngine}`;\n    const data = {\n      message,\n      originalMessage: message === originalMessage ? null : originalMessage,\n      name: e.name == null || e.name === '' ? null : e.name,\n      componentStack: typeof e.componentStack === 'string' ? e.componentStack : null,\n      stack,\n      id: currentExceptionID,\n      isFatal,\n      extraData: {\n        jsEngine: e.jsEngine,\n        rawStack: e.stack\n      }\n    };\n    return {\n      ...data,\n      isComponentError: !!e.isComponentError\n    };\n  }\n\n  /**\n   * Logs exceptions to the (native) console and displays them\n   */\n  function handleException(e) {\n    let error;\n    if (e instanceof Error) {\n      error = e;\n    } else {\n      // Workaround for reporting errors caused by `throw 'some string'`\n      // Unfortunately there is no way to figure out the stacktrace in this\n      // case, so if you ended up here trying to trace an error, look for\n      // `throw '<error message>'` somewhere in your codebase.\n      error = new SyntheticError(e);\n    }\n    require(_dependencyMap[2], \"../../LogBox\").default.addException(parseException(error, true));\n  }\n  const ErrorUtils = {\n    parseException,\n    handleException,\n    SyntheticError\n  };\n  var _default = exports.default = ErrorUtils;\n});", "lineCount": 78, "map": [[7, 2, 9, 0], [7, 6, 9, 0, "_parseErrorStack"], [7, 22, 9, 0], [7, 25, 9, 0, "_interopRequireDefault"], [7, 47, 9, 0], [7, 48, 9, 0, "require"], [7, 55, 9, 0], [7, 56, 9, 0, "_dependencyMap"], [7, 70, 9, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [16, 2, 13, 0], [16, 8, 13, 6, "SyntheticError"], [16, 22, 13, 20], [16, 31, 13, 29, "Error"], [16, 36, 13, 34], [16, 37, 13, 35], [17, 4, 14, 2, "name"], [17, 8, 14, 6], [17, 11, 14, 17], [17, 13, 14, 19], [18, 2, 15, 0], [20, 2, 17, 0], [21, 0, 18, 0], [22, 0, 19, 0], [23, 2, 20, 0], [23, 6, 20, 4, "exceptionID"], [23, 17, 20, 15], [23, 20, 20, 18], [23, 21, 20, 19], [24, 2, 22, 0], [24, 11, 22, 9, "parseException"], [24, 25, 22, 23, "parseException"], [24, 26, 22, 24, "e"], [24, 27, 22, 40], [24, 29, 22, 42, "isFatal"], [24, 36, 22, 58], [24, 38, 22, 60], [25, 4, 23, 2], [25, 10, 23, 8, "stack"], [25, 15, 23, 13], [25, 18, 23, 16], [25, 22, 23, 16, "parseError<PERSON>tack"], [25, 46, 23, 31], [25, 48, 23, 32, "e"], [25, 49, 23, 33], [25, 51, 23, 35, "stack"], [25, 56, 23, 40], [25, 57, 23, 41], [26, 4, 24, 2], [26, 10, 24, 8, "currentExceptionID"], [26, 28, 24, 26], [26, 31, 24, 29], [26, 33, 24, 31, "exceptionID"], [26, 44, 24, 42], [27, 4, 25, 2], [27, 10, 25, 8, "originalMessage"], [27, 25, 25, 23], [27, 28, 25, 26, "e"], [27, 29, 25, 27], [27, 30, 25, 28, "message"], [27, 37, 25, 35], [27, 41, 25, 39], [27, 43, 25, 41], [28, 4, 26, 2], [28, 8, 26, 6, "message"], [28, 15, 26, 13], [28, 18, 26, 16, "originalMessage"], [28, 33, 26, 31], [29, 4, 27, 2], [29, 8, 27, 6, "e"], [29, 9, 27, 7], [29, 10, 27, 8, "componentStack"], [29, 24, 27, 22], [29, 28, 27, 26], [29, 32, 27, 30], [29, 34, 27, 32], [30, 6, 28, 4, "message"], [30, 13, 28, 11], [30, 17, 28, 15], [30, 49, 28, 47, "e"], [30, 50, 28, 48], [30, 51, 28, 49, "componentStack"], [30, 65, 28, 63], [30, 67, 28, 65], [31, 4, 29, 2], [32, 4, 30, 2], [32, 10, 30, 8, "namePrefix"], [32, 20, 30, 18], [32, 23, 30, 21, "e"], [32, 24, 30, 22], [32, 25, 30, 23, "name"], [32, 29, 30, 27], [32, 33, 30, 31], [32, 37, 30, 35], [32, 41, 30, 39, "e"], [32, 42, 30, 40], [32, 43, 30, 41, "name"], [32, 47, 30, 45], [32, 52, 30, 50], [32, 54, 30, 52], [32, 57, 30, 55], [32, 59, 30, 57], [32, 62, 30, 60], [32, 65, 30, 63, "e"], [32, 66, 30, 64], [32, 67, 30, 65, "name"], [32, 71, 30, 69], [32, 75, 30, 73], [33, 4, 32, 2], [33, 8, 32, 6], [33, 9, 32, 7, "message"], [33, 16, 32, 14], [33, 17, 32, 15, "startsWith"], [33, 27, 32, 25], [33, 28, 32, 26, "namePrefix"], [33, 38, 32, 36], [33, 39, 32, 37], [33, 41, 32, 39], [34, 6, 33, 4, "message"], [34, 13, 33, 11], [34, 16, 33, 14, "namePrefix"], [34, 26, 33, 24], [34, 29, 33, 27, "message"], [34, 36, 33, 34], [35, 4, 34, 2], [36, 4, 36, 2, "message"], [36, 11, 36, 9], [36, 14, 36, 12, "e"], [36, 15, 36, 13], [36, 16, 36, 14, "jsEngine"], [36, 24, 36, 22], [36, 28, 36, 26], [36, 32, 36, 30], [36, 35, 36, 33, "message"], [36, 42, 36, 40], [36, 45, 36, 43], [36, 48, 36, 46, "message"], [36, 55, 36, 53], [36, 71, 36, 69, "e"], [36, 72, 36, 70], [36, 73, 36, 71, "jsEngine"], [36, 81, 36, 79], [36, 83, 36, 81], [37, 4, 38, 2], [37, 10, 38, 8, "data"], [37, 14, 38, 12], [37, 17, 38, 15], [38, 6, 39, 4, "message"], [38, 13, 39, 11], [39, 6, 40, 4, "originalMessage"], [39, 21, 40, 19], [39, 23, 40, 21, "message"], [39, 30, 40, 28], [39, 35, 40, 33, "originalMessage"], [39, 50, 40, 48], [39, 53, 40, 51], [39, 57, 40, 55], [39, 60, 40, 58, "originalMessage"], [39, 75, 40, 73], [40, 6, 41, 4, "name"], [40, 10, 41, 8], [40, 12, 41, 10, "e"], [40, 13, 41, 11], [40, 14, 41, 12, "name"], [40, 18, 41, 16], [40, 22, 41, 20], [40, 26, 41, 24], [40, 30, 41, 28, "e"], [40, 31, 41, 29], [40, 32, 41, 30, "name"], [40, 36, 41, 34], [40, 41, 41, 39], [40, 43, 41, 41], [40, 46, 41, 44], [40, 50, 41, 48], [40, 53, 41, 51, "e"], [40, 54, 41, 52], [40, 55, 41, 53, "name"], [40, 59, 41, 57], [41, 6, 42, 4, "componentStack"], [41, 20, 42, 18], [41, 22, 42, 20], [41, 29, 42, 27, "e"], [41, 30, 42, 28], [41, 31, 42, 29, "componentStack"], [41, 45, 42, 43], [41, 50, 42, 48], [41, 58, 42, 56], [41, 61, 42, 59, "e"], [41, 62, 42, 60], [41, 63, 42, 61, "componentStack"], [41, 77, 42, 75], [41, 80, 42, 78], [41, 84, 42, 82], [42, 6, 43, 4, "stack"], [42, 11, 43, 9], [43, 6, 44, 4, "id"], [43, 8, 44, 6], [43, 10, 44, 8, "currentExceptionID"], [43, 28, 44, 26], [44, 6, 45, 4, "isFatal"], [44, 13, 45, 11], [45, 6, 46, 4, "extraData"], [45, 15, 46, 13], [45, 17, 46, 15], [46, 8, 47, 6, "jsEngine"], [46, 16, 47, 14], [46, 18, 47, 16, "e"], [46, 19, 47, 17], [46, 20, 47, 18, "jsEngine"], [46, 28, 47, 26], [47, 8, 48, 6, "rawStack"], [47, 16, 48, 14], [47, 18, 48, 16, "e"], [47, 19, 48, 17], [47, 20, 48, 18, "stack"], [48, 6, 49, 4], [49, 4, 50, 2], [49, 5, 50, 3], [50, 4, 52, 2], [50, 11, 52, 9], [51, 6, 53, 4], [51, 9, 53, 7, "data"], [51, 13, 53, 11], [52, 6, 54, 4, "isComponentError"], [52, 22, 54, 20], [52, 24, 54, 22], [52, 25, 54, 23], [52, 26, 54, 24, "e"], [52, 27, 54, 25], [52, 28, 54, 26, "isComponentError"], [53, 4, 55, 2], [53, 5, 55, 3], [54, 2, 56, 0], [56, 2, 58, 0], [57, 0, 59, 0], [58, 0, 60, 0], [59, 2, 61, 0], [59, 11, 61, 9, "handleException"], [59, 26, 61, 24, "handleException"], [59, 27, 61, 25, "e"], [59, 28, 61, 31], [59, 30, 61, 33], [60, 4, 62, 2], [60, 8, 62, 6, "error"], [60, 13, 62, 18], [61, 4, 63, 2], [61, 8, 63, 6, "e"], [61, 9, 63, 7], [61, 21, 63, 19, "Error"], [61, 26, 63, 24], [61, 28, 63, 26], [62, 6, 64, 4, "error"], [62, 11, 64, 9], [62, 14, 64, 12, "e"], [62, 15, 64, 13], [63, 4, 65, 2], [63, 5, 65, 3], [63, 11, 65, 9], [64, 6, 66, 4], [65, 6, 67, 4], [66, 6, 68, 4], [67, 6, 69, 4], [68, 6, 70, 4, "error"], [68, 11, 70, 9], [68, 14, 70, 12], [68, 18, 70, 16, "SyntheticError"], [68, 32, 70, 30], [68, 33, 70, 31, "e"], [68, 34, 70, 32], [68, 35, 70, 33], [69, 4, 71, 2], [70, 4, 73, 2, "require"], [70, 11, 73, 9], [70, 12, 73, 9, "_dependencyMap"], [70, 26, 73, 9], [70, 45, 73, 24], [70, 46, 73, 25], [70, 47, 73, 26, "default"], [70, 54, 73, 33], [70, 55, 73, 34, "addException"], [70, 67, 73, 46], [70, 68, 73, 47, "parseException"], [70, 82, 73, 61], [70, 83, 73, 62, "error"], [70, 88, 73, 67], [70, 90, 73, 69], [70, 94, 73, 73], [70, 95, 73, 74], [70, 96, 73, 75], [71, 2, 74, 0], [72, 2, 76, 0], [72, 8, 76, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [72, 18, 76, 16], [72, 21, 76, 19], [73, 4, 77, 2, "parseException"], [73, 18, 77, 16], [74, 4, 78, 2, "handleException"], [74, 19, 78, 17], [75, 4, 79, 2, "SyntheticError"], [76, 2, 80, 0], [76, 3, 80, 1], [77, 2, 80, 2], [77, 6, 80, 2, "_default"], [77, 14, 80, 2], [77, 17, 80, 2, "exports"], [77, 24, 80, 2], [77, 25, 80, 2, "default"], [77, 32, 80, 2], [77, 35, 82, 15, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [77, 45, 82, 25], [78, 0, 82, 25], [78, 3]], "functionMap": {"names": ["<global>", "SyntheticError", "parseException", "handleException"], "mappings": "AAA;ACY;CDE;AEO;CFkC;AGK;CHa"}}, "type": "js/module"}]}