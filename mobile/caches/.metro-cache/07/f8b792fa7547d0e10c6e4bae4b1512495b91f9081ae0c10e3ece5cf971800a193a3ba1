{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.AnimatedHeaderHeightContext = void 0;\n  exports.useAnimatedHeaderHeight = useAnimatedHeaderHeight;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const AnimatedHeaderHeightContext = exports.AnimatedHeaderHeightContext = /*#__PURE__*/React.createContext(undefined);\n  function useAnimatedHeaderHeight() {\n    const animatedValue = React.useContext(AnimatedHeaderHeightContext);\n    if (animatedValue === undefined) {\n      throw new Error(\"Couldn't find the header height. Are you inside a screen in a native stack navigator?\");\n    }\n    return animatedValue;\n  }\n});", "lineCount": 19, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "AnimatedHeaderHeightContext"], [7, 37, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useAnimatedHeaderHeight"], [8, 33, 1, 13], [8, 36, 1, 13, "useAnimatedHeaderHeight"], [8, 59, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "React"], [9, 11, 3, 0], [9, 14, 3, 0, "_interopRequireWildcard"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 3, 31], [10, 11, 3, 31, "_interopRequireWildcard"], [10, 35, 3, 31, "e"], [10, 36, 3, 31], [10, 38, 3, 31, "t"], [10, 39, 3, 31], [10, 68, 3, 31, "WeakMap"], [10, 75, 3, 31], [10, 81, 3, 31, "r"], [10, 82, 3, 31], [10, 89, 3, 31, "WeakMap"], [10, 96, 3, 31], [10, 100, 3, 31, "n"], [10, 101, 3, 31], [10, 108, 3, 31, "WeakMap"], [10, 115, 3, 31], [10, 127, 3, 31, "_interopRequireWildcard"], [10, 150, 3, 31], [10, 162, 3, 31, "_interopRequireWildcard"], [10, 163, 3, 31, "e"], [10, 164, 3, 31], [10, 166, 3, 31, "t"], [10, 167, 3, 31], [10, 176, 3, 31, "t"], [10, 177, 3, 31], [10, 181, 3, 31, "e"], [10, 182, 3, 31], [10, 186, 3, 31, "e"], [10, 187, 3, 31], [10, 188, 3, 31, "__esModule"], [10, 198, 3, 31], [10, 207, 3, 31, "e"], [10, 208, 3, 31], [10, 214, 3, 31, "o"], [10, 215, 3, 31], [10, 217, 3, 31, "i"], [10, 218, 3, 31], [10, 220, 3, 31, "f"], [10, 221, 3, 31], [10, 226, 3, 31, "__proto__"], [10, 235, 3, 31], [10, 243, 3, 31, "default"], [10, 250, 3, 31], [10, 252, 3, 31, "e"], [10, 253, 3, 31], [10, 270, 3, 31, "e"], [10, 271, 3, 31], [10, 294, 3, 31, "e"], [10, 295, 3, 31], [10, 320, 3, 31, "e"], [10, 321, 3, 31], [10, 330, 3, 31, "f"], [10, 331, 3, 31], [10, 337, 3, 31, "o"], [10, 338, 3, 31], [10, 341, 3, 31, "t"], [10, 342, 3, 31], [10, 345, 3, 31, "n"], [10, 346, 3, 31], [10, 349, 3, 31, "r"], [10, 350, 3, 31], [10, 358, 3, 31, "o"], [10, 359, 3, 31], [10, 360, 3, 31, "has"], [10, 363, 3, 31], [10, 364, 3, 31, "e"], [10, 365, 3, 31], [10, 375, 3, 31, "o"], [10, 376, 3, 31], [10, 377, 3, 31, "get"], [10, 380, 3, 31], [10, 381, 3, 31, "e"], [10, 382, 3, 31], [10, 385, 3, 31, "o"], [10, 386, 3, 31], [10, 387, 3, 31, "set"], [10, 390, 3, 31], [10, 391, 3, 31, "e"], [10, 392, 3, 31], [10, 394, 3, 31, "f"], [10, 395, 3, 31], [10, 411, 3, 31, "t"], [10, 412, 3, 31], [10, 416, 3, 31, "e"], [10, 417, 3, 31], [10, 433, 3, 31, "t"], [10, 434, 3, 31], [10, 441, 3, 31, "hasOwnProperty"], [10, 455, 3, 31], [10, 456, 3, 31, "call"], [10, 460, 3, 31], [10, 461, 3, 31, "e"], [10, 462, 3, 31], [10, 464, 3, 31, "t"], [10, 465, 3, 31], [10, 472, 3, 31, "i"], [10, 473, 3, 31], [10, 477, 3, 31, "o"], [10, 478, 3, 31], [10, 481, 3, 31, "Object"], [10, 487, 3, 31], [10, 488, 3, 31, "defineProperty"], [10, 502, 3, 31], [10, 507, 3, 31, "Object"], [10, 513, 3, 31], [10, 514, 3, 31, "getOwnPropertyDescriptor"], [10, 538, 3, 31], [10, 539, 3, 31, "e"], [10, 540, 3, 31], [10, 542, 3, 31, "t"], [10, 543, 3, 31], [10, 550, 3, 31, "i"], [10, 551, 3, 31], [10, 552, 3, 31, "get"], [10, 555, 3, 31], [10, 559, 3, 31, "i"], [10, 560, 3, 31], [10, 561, 3, 31, "set"], [10, 564, 3, 31], [10, 568, 3, 31, "o"], [10, 569, 3, 31], [10, 570, 3, 31, "f"], [10, 571, 3, 31], [10, 573, 3, 31, "t"], [10, 574, 3, 31], [10, 576, 3, 31, "i"], [10, 577, 3, 31], [10, 581, 3, 31, "f"], [10, 582, 3, 31], [10, 583, 3, 31, "t"], [10, 584, 3, 31], [10, 588, 3, 31, "e"], [10, 589, 3, 31], [10, 590, 3, 31, "t"], [10, 591, 3, 31], [10, 602, 3, 31, "f"], [10, 603, 3, 31], [10, 608, 3, 31, "e"], [10, 609, 3, 31], [10, 611, 3, 31, "t"], [10, 612, 3, 31], [11, 2, 4, 7], [11, 8, 4, 13, "AnimatedHeaderHeightContext"], [11, 35, 4, 40], [11, 38, 4, 40, "exports"], [11, 45, 4, 40], [11, 46, 4, 40, "AnimatedHeaderHeightContext"], [11, 73, 4, 40], [11, 76, 4, 43], [11, 89, 4, 56, "React"], [11, 94, 4, 61], [11, 95, 4, 62, "createContext"], [11, 108, 4, 75], [11, 109, 4, 76, "undefined"], [11, 118, 4, 85], [11, 119, 4, 86], [12, 2, 5, 7], [12, 11, 5, 16, "useAnimatedHeaderHeight"], [12, 34, 5, 39, "useAnimatedHeaderHeight"], [12, 35, 5, 39], [12, 37, 5, 42], [13, 4, 6, 2], [13, 10, 6, 8, "animatedValue"], [13, 23, 6, 21], [13, 26, 6, 24, "React"], [13, 31, 6, 29], [13, 32, 6, 30, "useContext"], [13, 42, 6, 40], [13, 43, 6, 41, "AnimatedHeaderHeightContext"], [13, 70, 6, 68], [13, 71, 6, 69], [14, 4, 7, 2], [14, 8, 7, 6, "animatedValue"], [14, 21, 7, 19], [14, 26, 7, 24, "undefined"], [14, 35, 7, 33], [14, 37, 7, 35], [15, 6, 8, 4], [15, 12, 8, 10], [15, 16, 8, 14, "Error"], [15, 21, 8, 19], [15, 22, 8, 20], [15, 109, 8, 107], [15, 110, 8, 108], [16, 4, 9, 2], [17, 4, 10, 2], [17, 11, 10, 9, "animatedValue"], [17, 24, 10, 22], [18, 2, 11, 0], [19, 0, 11, 1], [19, 3]], "functionMap": {"names": ["<global>", "useAnimatedHeaderHeight"], "mappings": "AAA;OCI;CDM"}}, "type": "js/module"}]}