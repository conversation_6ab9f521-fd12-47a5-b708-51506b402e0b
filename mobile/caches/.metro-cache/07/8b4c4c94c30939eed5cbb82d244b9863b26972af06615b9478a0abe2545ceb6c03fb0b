{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.customDirectEventTypes = exports.customBubblingEventTypes = void 0;\n  exports.get = get;\n  exports.register = register;\n  var _invariant = _interopRequireDefault(require(_dependencyMap[1], \"invariant\"));\n  var customBubblingEventTypes = exports.customBubblingEventTypes = {};\n  var customDirectEventTypes = exports.customDirectEventTypes = {};\n  var viewConfigCallbacks = new Map();\n  var viewConfigs = new Map();\n  function processEventTypes(viewConfig) {\n    var bubblingEventTypes = viewConfig.bubblingEventTypes,\n      directEventTypes = viewConfig.directEventTypes;\n    if (__DEV__) {\n      if (bubblingEventTypes != null && directEventTypes != null) {\n        for (var topLevelType in directEventTypes) {\n          (0, _invariant.default)(bubblingEventTypes[topLevelType] == null, 'Event cannot be both direct and bubbling: %s', topLevelType);\n        }\n      }\n    }\n    if (bubblingEventTypes != null) {\n      for (var _topLevelType in bubblingEventTypes) {\n        if (customBubblingEventTypes[_topLevelType] == null) {\n          customBubblingEventTypes[_topLevelType] = bubblingEventTypes[_topLevelType];\n        }\n      }\n    }\n    if (directEventTypes != null) {\n      for (var _topLevelType2 in directEventTypes) {\n        if (customDirectEventTypes[_topLevelType2] == null) {\n          customDirectEventTypes[_topLevelType2] = directEventTypes[_topLevelType2];\n        }\n      }\n    }\n  }\n  function register(name, callback) {\n    (0, _invariant.default)(!viewConfigCallbacks.has(name), 'Tried to register two views with the same name %s', name);\n    (0, _invariant.default)(typeof callback === 'function', 'View config getter callback for component `%s` must be a function (received `%s`)', name, callback === null ? 'null' : typeof callback);\n    viewConfigCallbacks.set(name, callback);\n    return name;\n  }\n  function get(name) {\n    var viewConfig = viewConfigs.get(name);\n    if (viewConfig == null) {\n      var callback = viewConfigCallbacks.get(name);\n      if (typeof callback !== 'function') {\n        (0, _invariant.default)(false, 'View config getter callback for component `%s` must be a function (received `%s`).%s', name, callback === null ? 'null' : typeof callback, typeof name[0] === 'string' && /[a-z]/.test(name[0]) ? ' Make sure to start component names with a capital letter.' : '');\n      }\n      viewConfig = callback();\n      (0, _invariant.default)(viewConfig, 'View config not found for component `%s`', name);\n      processEventTypes(viewConfig);\n      viewConfigs.set(name, viewConfig);\n      viewConfigCallbacks.set(name, null);\n    }\n    return viewConfig;\n  }\n});", "lineCount": 62, "map": [[2, 2, 13, 0], [2, 14, 13, 12], [4, 2, 13, 13], [4, 6, 13, 13, "_interopRequireDefault"], [4, 28, 13, 13], [4, 31, 13, 13, "require"], [4, 38, 13, 13], [4, 39, 13, 13, "_dependencyMap"], [4, 53, 13, 13], [5, 2, 13, 13, "Object"], [5, 8, 13, 13], [5, 9, 13, 13, "defineProperty"], [5, 23, 13, 13], [5, 24, 13, 13, "exports"], [5, 31, 13, 13], [6, 4, 13, 13, "value"], [6, 9, 13, 13], [7, 2, 13, 13], [8, 2, 13, 13, "exports"], [8, 9, 13, 13], [8, 10, 13, 13, "customDirectEventTypes"], [8, 32, 13, 13], [8, 35, 13, 13, "exports"], [8, 42, 13, 13], [8, 43, 13, 13, "customBubblingEventTypes"], [8, 67, 13, 13], [9, 2, 13, 13, "exports"], [9, 9, 13, 13], [9, 10, 13, 13, "get"], [9, 13, 13, 13], [9, 16, 13, 13, "get"], [9, 19, 13, 13], [10, 2, 13, 13, "exports"], [10, 9, 13, 13], [10, 10, 13, 13, "register"], [10, 18, 13, 13], [10, 21, 13, 13, "register"], [10, 29, 13, 13], [11, 2, 16, 0], [11, 6, 16, 0, "_invariant"], [11, 16, 16, 0], [11, 19, 16, 0, "_interopRequireDefault"], [11, 41, 16, 0], [11, 42, 16, 0, "require"], [11, 49, 16, 0], [11, 50, 16, 0, "_dependencyMap"], [11, 64, 16, 0], [12, 2, 19, 7], [12, 6, 19, 13, "customBubblingEventTypes"], [12, 30, 27, 1], [12, 33, 27, 1, "exports"], [12, 40, 27, 1], [12, 41, 27, 1, "customBubblingEventTypes"], [12, 65, 27, 1], [12, 68, 27, 4], [12, 69, 27, 5], [12, 70, 27, 6], [13, 2, 28, 7], [13, 6, 28, 13, "customDirectEventTypes"], [13, 28, 32, 1], [13, 31, 32, 1, "exports"], [13, 38, 32, 1], [13, 39, 32, 1, "customDirectEventTypes"], [13, 61, 32, 1], [13, 64, 32, 4], [13, 65, 32, 5], [13, 66, 32, 6], [14, 2, 34, 0], [14, 6, 34, 6, "viewConfigCallbacks"], [14, 25, 34, 25], [14, 28, 34, 28], [14, 32, 34, 32, "Map"], [14, 35, 34, 35], [14, 36, 34, 63], [14, 37, 34, 64], [15, 2, 35, 0], [15, 6, 35, 6, "viewConfigs"], [15, 17, 35, 17], [15, 20, 35, 20], [15, 24, 35, 24, "Map"], [15, 27, 35, 27], [15, 28, 35, 48], [15, 29, 35, 49], [16, 2, 37, 0], [16, 11, 37, 9, "processEventTypes"], [16, 28, 37, 26, "processEventTypes"], [16, 29, 37, 27, "viewConfig"], [16, 39, 37, 49], [16, 41, 37, 57], [17, 4, 38, 2], [17, 8, 38, 9, "bubblingEventTypes"], [17, 26, 38, 27], [17, 29, 38, 49, "viewConfig"], [17, 39, 38, 59], [17, 40, 38, 9, "bubblingEventTypes"], [17, 58, 38, 27], [18, 6, 38, 29, "directEventTypes"], [18, 22, 38, 45], [18, 25, 38, 49, "viewConfig"], [18, 35, 38, 59], [18, 36, 38, 29, "directEventTypes"], [18, 52, 38, 45], [19, 4, 40, 2], [19, 8, 40, 6, "__DEV__"], [19, 15, 40, 13], [19, 17, 40, 15], [20, 6, 41, 4], [20, 10, 41, 8, "bubblingEventTypes"], [20, 28, 41, 26], [20, 32, 41, 30], [20, 36, 41, 34], [20, 40, 41, 38, "directEventTypes"], [20, 56, 41, 54], [20, 60, 41, 58], [20, 64, 41, 62], [20, 66, 41, 64], [21, 8, 42, 6], [21, 13, 42, 11], [21, 17, 42, 17, "topLevelType"], [21, 29, 42, 29], [21, 33, 42, 33, "directEventTypes"], [21, 49, 42, 49], [21, 51, 42, 51], [22, 10, 43, 8], [22, 14, 43, 8, "invariant"], [22, 32, 43, 17], [22, 34, 44, 10, "bubblingEventTypes"], [22, 52, 44, 28], [22, 53, 44, 29, "topLevelType"], [22, 65, 44, 41], [22, 66, 44, 42], [22, 70, 44, 46], [22, 74, 44, 50], [22, 76, 45, 10], [22, 122, 45, 56], [22, 124, 46, 10, "topLevelType"], [22, 136, 47, 8], [22, 137, 47, 9], [23, 8, 48, 6], [24, 6, 49, 4], [25, 4, 50, 2], [26, 4, 52, 2], [26, 8, 52, 6, "bubblingEventTypes"], [26, 26, 52, 24], [26, 30, 52, 28], [26, 34, 52, 32], [26, 36, 52, 34], [27, 6, 53, 4], [27, 11, 53, 9], [27, 15, 53, 15, "topLevelType"], [27, 28, 53, 27], [27, 32, 53, 31, "bubblingEventTypes"], [27, 50, 53, 49], [27, 52, 53, 51], [28, 8, 54, 6], [28, 12, 54, 10, "customBubblingEventTypes"], [28, 36, 54, 34], [28, 37, 54, 35, "topLevelType"], [28, 50, 54, 47], [28, 51, 54, 48], [28, 55, 54, 52], [28, 59, 54, 56], [28, 61, 54, 58], [29, 10, 55, 8, "customBubblingEventTypes"], [29, 34, 55, 32], [29, 35, 55, 33, "topLevelType"], [29, 48, 55, 45], [29, 49, 55, 46], [29, 52, 56, 10, "bubblingEventTypes"], [29, 70, 56, 28], [29, 71, 56, 29, "topLevelType"], [29, 84, 56, 41], [29, 85, 56, 42], [30, 8, 57, 6], [31, 6, 58, 4], [32, 4, 59, 2], [33, 4, 61, 2], [33, 8, 61, 6, "directEventTypes"], [33, 24, 61, 22], [33, 28, 61, 26], [33, 32, 61, 30], [33, 34, 61, 32], [34, 6, 62, 4], [34, 11, 62, 9], [34, 15, 62, 15, "topLevelType"], [34, 29, 62, 27], [34, 33, 62, 31, "directEventTypes"], [34, 49, 62, 47], [34, 51, 62, 49], [35, 8, 63, 6], [35, 12, 63, 10, "customDirectEventTypes"], [35, 34, 63, 32], [35, 35, 63, 33, "topLevelType"], [35, 49, 63, 45], [35, 50, 63, 46], [35, 54, 63, 50], [35, 58, 63, 54], [35, 60, 63, 56], [36, 10, 64, 8, "customDirectEventTypes"], [36, 32, 64, 30], [36, 33, 64, 31, "topLevelType"], [36, 47, 64, 43], [36, 48, 64, 44], [36, 51, 64, 47, "directEventTypes"], [36, 67, 64, 63], [36, 68, 64, 64, "topLevelType"], [36, 82, 64, 76], [36, 83, 64, 77], [37, 8, 65, 6], [38, 6, 66, 4], [39, 4, 67, 2], [40, 2, 68, 0], [41, 2, 75, 7], [41, 11, 75, 16, "register"], [41, 19, 75, 24, "register"], [41, 20, 75, 25, "name"], [41, 24, 75, 37], [41, 26, 75, 39, "callback"], [41, 34, 75, 65], [41, 36, 75, 75], [42, 4, 76, 2], [42, 8, 76, 2, "invariant"], [42, 26, 76, 11], [42, 28, 77, 4], [42, 29, 77, 5, "viewConfigCallbacks"], [42, 48, 77, 24], [42, 49, 77, 25, "has"], [42, 52, 77, 28], [42, 53, 77, 29, "name"], [42, 57, 77, 33], [42, 58, 77, 34], [42, 60, 78, 4], [42, 111, 78, 55], [42, 113, 79, 4, "name"], [42, 117, 80, 2], [42, 118, 80, 3], [43, 4, 81, 2], [43, 8, 81, 2, "invariant"], [43, 26, 81, 11], [43, 28, 82, 4], [43, 35, 82, 11, "callback"], [43, 43, 82, 19], [43, 48, 82, 24], [43, 58, 82, 34], [43, 60, 83, 4], [43, 143, 83, 87], [43, 145, 84, 4, "name"], [43, 149, 84, 8], [43, 151, 85, 4, "callback"], [43, 159, 85, 12], [43, 164, 85, 17], [43, 168, 85, 21], [43, 171, 85, 24], [43, 177, 85, 30], [43, 180, 85, 33], [43, 187, 85, 40, "callback"], [43, 195, 86, 2], [43, 196, 86, 3], [44, 4, 87, 2, "viewConfigCallbacks"], [44, 23, 87, 21], [44, 24, 87, 22, "set"], [44, 27, 87, 25], [44, 28, 87, 26, "name"], [44, 32, 87, 30], [44, 34, 87, 32, "callback"], [44, 42, 87, 40], [44, 43, 87, 41], [45, 4, 88, 2], [45, 11, 88, 9, "name"], [45, 15, 88, 13], [46, 2, 89, 0], [47, 2, 96, 7], [47, 11, 96, 16, "get"], [47, 14, 96, 19, "get"], [47, 15, 96, 20, "name"], [47, 19, 96, 32], [47, 21, 96, 46], [48, 4, 97, 2], [48, 8, 97, 6, "viewConfig"], [48, 18, 97, 16], [48, 21, 97, 19, "viewConfigs"], [48, 32, 97, 30], [48, 33, 97, 31, "get"], [48, 36, 97, 34], [48, 37, 97, 35, "name"], [48, 41, 97, 39], [48, 42, 97, 40], [49, 4, 98, 2], [49, 8, 98, 6, "viewConfig"], [49, 18, 98, 16], [49, 22, 98, 20], [49, 26, 98, 24], [49, 28, 98, 26], [50, 6, 99, 4], [50, 10, 99, 10, "callback"], [50, 18, 99, 18], [50, 21, 99, 21, "viewConfigCallbacks"], [50, 40, 99, 40], [50, 41, 99, 41, "get"], [50, 44, 99, 44], [50, 45, 99, 45, "name"], [50, 49, 99, 49], [50, 50, 99, 50], [51, 6, 100, 4], [51, 10, 100, 8], [51, 17, 100, 15, "callback"], [51, 25, 100, 23], [51, 30, 100, 28], [51, 40, 100, 38], [51, 42, 100, 40], [52, 8, 101, 6], [52, 12, 101, 6, "invariant"], [52, 30, 101, 15], [52, 32, 102, 8], [52, 37, 102, 13], [52, 39, 103, 8], [52, 125, 103, 94], [52, 127, 104, 8, "name"], [52, 131, 104, 12], [52, 133, 105, 8, "callback"], [52, 141, 105, 16], [52, 146, 105, 21], [52, 150, 105, 25], [52, 153, 105, 28], [52, 159, 105, 34], [52, 162, 105, 37], [52, 169, 105, 44, "callback"], [52, 177, 105, 52], [52, 179, 107, 8], [52, 186, 107, 15, "name"], [52, 190, 107, 19], [52, 191, 107, 20], [52, 192, 107, 21], [52, 193, 107, 22], [52, 198, 107, 27], [52, 206, 107, 35], [52, 210, 107, 39], [52, 217, 107, 46], [52, 218, 107, 47, "test"], [52, 222, 107, 51], [52, 223, 107, 52, "name"], [52, 227, 107, 56], [52, 228, 107, 57], [52, 229, 107, 58], [52, 230, 107, 59], [52, 231, 107, 60], [52, 234, 108, 12], [52, 294, 108, 72], [52, 297, 109, 12], [52, 299, 110, 6], [52, 300, 110, 7], [53, 6, 111, 4], [54, 6, 112, 4, "viewConfig"], [54, 16, 112, 14], [54, 19, 112, 17, "callback"], [54, 27, 112, 25], [54, 28, 112, 26], [54, 29, 112, 27], [55, 6, 113, 4], [55, 10, 113, 4, "invariant"], [55, 28, 113, 13], [55, 30, 113, 14, "viewConfig"], [55, 40, 113, 24], [55, 42, 113, 26], [55, 84, 113, 68], [55, 86, 113, 70, "name"], [55, 90, 113, 74], [55, 91, 113, 75], [56, 6, 115, 4, "processEventTypes"], [56, 23, 115, 21], [56, 24, 115, 22, "viewConfig"], [56, 34, 115, 32], [56, 35, 115, 33], [57, 6, 116, 4, "viewConfigs"], [57, 17, 116, 15], [57, 18, 116, 16, "set"], [57, 21, 116, 19], [57, 22, 116, 20, "name"], [57, 26, 116, 24], [57, 28, 116, 26, "viewConfig"], [57, 38, 116, 36], [57, 39, 116, 37], [58, 6, 120, 4, "viewConfigCallbacks"], [58, 25, 120, 23], [58, 26, 120, 24, "set"], [58, 29, 120, 27], [58, 30, 120, 28, "name"], [58, 34, 120, 32], [58, 36, 120, 34], [58, 40, 120, 38], [58, 41, 120, 39], [59, 4, 121, 2], [60, 4, 122, 2], [60, 11, 122, 9, "viewConfig"], [60, 21, 122, 19], [61, 2, 123, 0], [62, 0, 123, 1], [62, 3]], "functionMap": {"names": ["<global>", "processEventTypes", "register", "get"], "mappings": "AAA;ACoC;CD+B;OEO;CFc;OGO"}}, "type": "js/module"}]}