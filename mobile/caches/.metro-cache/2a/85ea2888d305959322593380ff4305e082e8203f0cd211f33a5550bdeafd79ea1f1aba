{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = composeStyles;\n  function composeStyles(style1, style2) {\n    if (style1 == null) {\n      return style2;\n    }\n    if (style2 == null) {\n      return style1;\n    }\n    return [style1, style2];\n  }\n});", "lineCount": 15, "map": [[6, 2, 16, 15], [6, 11, 16, 24, "composeStyles"], [6, 24, 16, 37, "composeStyles"], [6, 25, 17, 2, "style1"], [6, 31, 17, 13], [6, 33, 18, 2, "style2"], [6, 39, 18, 13], [6, 41, 19, 40], [7, 4, 20, 2], [7, 8, 20, 6, "style1"], [7, 14, 20, 12], [7, 18, 20, 16], [7, 22, 20, 20], [7, 24, 20, 22], [8, 6, 21, 4], [8, 13, 21, 11, "style2"], [8, 19, 21, 17], [9, 4, 22, 2], [10, 4, 23, 2], [10, 8, 23, 6, "style2"], [10, 14, 23, 12], [10, 18, 23, 16], [10, 22, 23, 20], [10, 24, 23, 22], [11, 6, 24, 4], [11, 13, 24, 11, "style1"], [11, 19, 24, 17], [12, 4, 25, 2], [13, 4, 26, 2], [13, 11, 26, 9], [13, 12, 26, 10, "style1"], [13, 18, 26, 16], [13, 20, 26, 18, "style2"], [13, 26, 26, 24], [13, 27, 26, 25], [14, 2, 27, 0], [15, 0, 27, 1], [15, 3]], "functionMap": {"names": ["<global>", "composeStyles"], "mappings": "AAA;eCe"}}, "type": "js/module"}]}