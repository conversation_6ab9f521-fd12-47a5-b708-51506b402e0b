{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/SafeAreaView/SafeAreaView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 70}}], "key": "3QVUNIaP8S9RRUnN6oy5HbhEEko=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorHeaderButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 72}}], "key": "PCvEzKBAfAlrDut3o/xhXaVJxRM=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "./LogBoxImages/chevron-left.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 59}}], "key": "x8uI0JSerK/ln25O5Zgp145wBS4=", "exportNames": ["*"]}}, {"name": "./LogBoxImages/chevron-right.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 74, "column": 17}, "end": {"line": 74, "column": 60}}], "key": "AdN9bB7I7ffs3GLs4awYjctk/A0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxInspectorHeader;\n  var _SafeAreaView = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/SafeAreaView/SafeAreaView\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"../../Text/Text\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"../../Utilities/Platform\"));\n  var _LogBoxInspectorHeaderButton = _interopRequireDefault(require(_dependencyMap[6], \"./LogBoxInspectorHeaderButton\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[7], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[8], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[9], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorHeader.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var LogBoxInspectorHeaderSafeArea = _Platform.default.OS === 'android' ? _View.default : _SafeAreaView.default;\n  function LogBoxInspectorHeader(props) {\n    if (props.level === 'syntax') {\n      return (0, _jsxRuntime.jsx)(LogBoxInspectorHeaderSafeArea, {\n        style: styles[props.level],\n        children: (0, _jsxRuntime.jsx)(_View.default, {\n          style: styles.header,\n          children: (0, _jsxRuntime.jsx)(_View.default, {\n            style: styles.title,\n            children: (0, _jsxRuntime.jsx)(_Text.default, {\n              style: styles.titleText,\n              id: \"logbox_header_title_text\",\n              children: \"Failed to compile\"\n            })\n          })\n        })\n      });\n    }\n    var prevIndex = props.selectedIndex - 1 < 0 ? props.total - 1 : props.selectedIndex - 1;\n    var nextIndex = props.selectedIndex + 1 > props.total - 1 ? 0 : props.selectedIndex + 1;\n    var titleText = `Log ${props.selectedIndex + 1} of ${props.total}`;\n    return (0, _jsxRuntime.jsx)(LogBoxInspectorHeaderSafeArea, {\n      style: styles[props.level],\n      children: (0, _jsxRuntime.jsxs)(_View.default, {\n        style: styles.header,\n        children: [(0, _jsxRuntime.jsx)(_LogBoxInspectorHeaderButton.default, {\n          id: \"logbox_header_button_prev\",\n          disabled: props.total <= 1,\n          level: props.level,\n          image: require(_dependencyMap[10], \"./LogBoxImages/chevron-left.png\"),\n          onPress: () => props.onSelectIndex(prevIndex)\n        }), (0, _jsxRuntime.jsx)(_View.default, {\n          style: styles.title,\n          children: (0, _jsxRuntime.jsx)(_Text.default, {\n            style: styles.titleText,\n            id: \"logbox_header_title_text\",\n            children: titleText\n          })\n        }), (0, _jsxRuntime.jsx)(_LogBoxInspectorHeaderButton.default, {\n          id: \"logbox_header_button_next\",\n          disabled: props.total <= 1,\n          level: props.level,\n          image: require(_dependencyMap[11], \"./LogBoxImages/chevron-right.png\"),\n          onPress: () => props.onSelectIndex(nextIndex)\n        })]\n      })\n    });\n  }\n  var styles = _StyleSheet.default.create({\n    syntax: {\n      backgroundColor: LogBoxStyle.getFatalColor()\n    },\n    fatal: {\n      backgroundColor: LogBoxStyle.getFatalColor()\n    },\n    warn: {\n      backgroundColor: LogBoxStyle.getWarningColor()\n    },\n    error: {\n      backgroundColor: LogBoxStyle.getErrorColor()\n    },\n    header: {\n      flexDirection: 'row',\n      height: _Platform.default.select({\n        android: 48,\n        ios: 44\n      })\n    },\n    title: {\n      alignItems: 'center',\n      flex: 1,\n      justifyContent: 'center'\n    },\n    titleText: {\n      color: LogBoxStyle.getTextColor(),\n      fontSize: 16,\n      fontWeight: '600',\n      includeFontPadding: false,\n      lineHeight: 20\n    }\n  });\n});", "lineCount": 99, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "_SafeAreaView"], [7, 19, 14, 0], [7, 22, 14, 0, "_interopRequireDefault"], [7, 44, 14, 0], [7, 45, 14, 0, "require"], [7, 52, 14, 0], [7, 53, 14, 0, "_dependencyMap"], [7, 67, 14, 0], [8, 2, 15, 0], [8, 6, 15, 0, "_View"], [8, 11, 15, 0], [8, 14, 15, 0, "_interopRequireDefault"], [8, 36, 15, 0], [8, 37, 15, 0, "require"], [8, 44, 15, 0], [8, 45, 15, 0, "_dependencyMap"], [8, 59, 15, 0], [9, 2, 16, 0], [9, 6, 16, 0, "_StyleSheet"], [9, 17, 16, 0], [9, 20, 16, 0, "_interopRequireDefault"], [9, 42, 16, 0], [9, 43, 16, 0, "require"], [9, 50, 16, 0], [9, 51, 16, 0, "_dependencyMap"], [9, 65, 16, 0], [10, 2, 17, 0], [10, 6, 17, 0, "_Text"], [10, 11, 17, 0], [10, 14, 17, 0, "_interopRequireDefault"], [10, 36, 17, 0], [10, 37, 17, 0, "require"], [10, 44, 17, 0], [10, 45, 17, 0, "_dependencyMap"], [10, 59, 17, 0], [11, 2, 18, 0], [11, 6, 18, 0, "_Platform"], [11, 15, 18, 0], [11, 18, 18, 0, "_interopRequireDefault"], [11, 40, 18, 0], [11, 41, 18, 0, "require"], [11, 48, 18, 0], [11, 49, 18, 0, "_dependencyMap"], [11, 63, 18, 0], [12, 2, 19, 0], [12, 6, 19, 0, "_LogBoxInspectorHeaderButton"], [12, 34, 19, 0], [12, 37, 19, 0, "_interopRequireDefault"], [12, 59, 19, 0], [12, 60, 19, 0, "require"], [12, 67, 19, 0], [12, 68, 19, 0, "_dependencyMap"], [12, 82, 19, 0], [13, 2, 20, 0], [13, 6, 20, 0, "LogBoxStyle"], [13, 17, 20, 0], [13, 20, 20, 0, "_interopRequireWildcard"], [13, 43, 20, 0], [13, 44, 20, 0, "require"], [13, 51, 20, 0], [13, 52, 20, 0, "_dependencyMap"], [13, 66, 20, 0], [14, 2, 21, 0], [14, 6, 21, 0, "React"], [14, 11, 21, 0], [14, 14, 21, 0, "_interopRequireWildcard"], [14, 37, 21, 0], [14, 38, 21, 0, "require"], [14, 45, 21, 0], [14, 46, 21, 0, "_dependencyMap"], [14, 60, 21, 0], [15, 2, 21, 31], [15, 6, 21, 31, "_jsxRuntime"], [15, 17, 21, 31], [15, 20, 21, 31, "require"], [15, 27, 21, 31], [15, 28, 21, 31, "_dependencyMap"], [15, 42, 21, 31], [16, 2, 21, 31], [16, 6, 21, 31, "_jsxFileName"], [16, 18, 21, 31], [17, 2, 21, 31], [17, 11, 21, 31, "_interopRequireWildcard"], [17, 35, 21, 31, "e"], [17, 36, 21, 31], [17, 38, 21, 31, "t"], [17, 39, 21, 31], [17, 68, 21, 31, "WeakMap"], [17, 75, 21, 31], [17, 81, 21, 31, "r"], [17, 82, 21, 31], [17, 89, 21, 31, "WeakMap"], [17, 96, 21, 31], [17, 100, 21, 31, "n"], [17, 101, 21, 31], [17, 108, 21, 31, "WeakMap"], [17, 115, 21, 31], [17, 127, 21, 31, "_interopRequireWildcard"], [17, 150, 21, 31], [17, 162, 21, 31, "_interopRequireWildcard"], [17, 163, 21, 31, "e"], [17, 164, 21, 31], [17, 166, 21, 31, "t"], [17, 167, 21, 31], [17, 176, 21, 31, "t"], [17, 177, 21, 31], [17, 181, 21, 31, "e"], [17, 182, 21, 31], [17, 186, 21, 31, "e"], [17, 187, 21, 31], [17, 188, 21, 31, "__esModule"], [17, 198, 21, 31], [17, 207, 21, 31, "e"], [17, 208, 21, 31], [17, 214, 21, 31, "o"], [17, 215, 21, 31], [17, 217, 21, 31, "i"], [17, 218, 21, 31], [17, 220, 21, 31, "f"], [17, 221, 21, 31], [17, 226, 21, 31, "__proto__"], [17, 235, 21, 31], [17, 243, 21, 31, "default"], [17, 250, 21, 31], [17, 252, 21, 31, "e"], [17, 253, 21, 31], [17, 270, 21, 31, "e"], [17, 271, 21, 31], [17, 294, 21, 31, "e"], [17, 295, 21, 31], [17, 320, 21, 31, "e"], [17, 321, 21, 31], [17, 330, 21, 31, "f"], [17, 331, 21, 31], [17, 337, 21, 31, "o"], [17, 338, 21, 31], [17, 341, 21, 31, "t"], [17, 342, 21, 31], [17, 345, 21, 31, "n"], [17, 346, 21, 31], [17, 349, 21, 31, "r"], [17, 350, 21, 31], [17, 358, 21, 31, "o"], [17, 359, 21, 31], [17, 360, 21, 31, "has"], [17, 363, 21, 31], [17, 364, 21, 31, "e"], [17, 365, 21, 31], [17, 375, 21, 31, "o"], [17, 376, 21, 31], [17, 377, 21, 31, "get"], [17, 380, 21, 31], [17, 381, 21, 31, "e"], [17, 382, 21, 31], [17, 385, 21, 31, "o"], [17, 386, 21, 31], [17, 387, 21, 31, "set"], [17, 390, 21, 31], [17, 391, 21, 31, "e"], [17, 392, 21, 31], [17, 394, 21, 31, "f"], [17, 395, 21, 31], [17, 409, 21, 31, "_t"], [17, 411, 21, 31], [17, 415, 21, 31, "e"], [17, 416, 21, 31], [17, 432, 21, 31, "_t"], [17, 434, 21, 31], [17, 441, 21, 31, "hasOwnProperty"], [17, 455, 21, 31], [17, 456, 21, 31, "call"], [17, 460, 21, 31], [17, 461, 21, 31, "e"], [17, 462, 21, 31], [17, 464, 21, 31, "_t"], [17, 466, 21, 31], [17, 473, 21, 31, "i"], [17, 474, 21, 31], [17, 478, 21, 31, "o"], [17, 479, 21, 31], [17, 482, 21, 31, "Object"], [17, 488, 21, 31], [17, 489, 21, 31, "defineProperty"], [17, 503, 21, 31], [17, 508, 21, 31, "Object"], [17, 514, 21, 31], [17, 515, 21, 31, "getOwnPropertyDescriptor"], [17, 539, 21, 31], [17, 540, 21, 31, "e"], [17, 541, 21, 31], [17, 543, 21, 31, "_t"], [17, 545, 21, 31], [17, 552, 21, 31, "i"], [17, 553, 21, 31], [17, 554, 21, 31, "get"], [17, 557, 21, 31], [17, 561, 21, 31, "i"], [17, 562, 21, 31], [17, 563, 21, 31, "set"], [17, 566, 21, 31], [17, 570, 21, 31, "o"], [17, 571, 21, 31], [17, 572, 21, 31, "f"], [17, 573, 21, 31], [17, 575, 21, 31, "_t"], [17, 577, 21, 31], [17, 579, 21, 31, "i"], [17, 580, 21, 31], [17, 584, 21, 31, "f"], [17, 585, 21, 31], [17, 586, 21, 31, "_t"], [17, 588, 21, 31], [17, 592, 21, 31, "e"], [17, 593, 21, 31], [17, 594, 21, 31, "_t"], [17, 596, 21, 31], [17, 607, 21, 31, "f"], [17, 608, 21, 31], [17, 613, 21, 31, "e"], [17, 614, 21, 31], [17, 616, 21, 31, "t"], [17, 617, 21, 31], [18, 2, 30, 0], [18, 6, 30, 6, "LogBoxInspectorHeaderSafeArea"], [18, 35, 30, 67], [18, 38, 31, 2, "Platform"], [18, 55, 31, 10], [18, 56, 31, 11, "OS"], [18, 58, 31, 13], [18, 63, 31, 18], [18, 72, 31, 27], [18, 75, 31, 30, "View"], [18, 88, 31, 34], [18, 91, 31, 37, "SafeAreaView"], [18, 112, 31, 49], [19, 2, 33, 15], [19, 11, 33, 24, "LogBoxInspectorHeader"], [19, 32, 33, 45, "LogBoxInspectorHeader"], [19, 33, 33, 46, "props"], [19, 38, 33, 58], [19, 40, 33, 72], [20, 4, 34, 2], [20, 8, 34, 6, "props"], [20, 13, 34, 11], [20, 14, 34, 12, "level"], [20, 19, 34, 17], [20, 24, 34, 22], [20, 32, 34, 30], [20, 34, 34, 32], [21, 6, 35, 4], [21, 13, 36, 6], [21, 17, 36, 6, "_jsxRuntime"], [21, 28, 36, 6], [21, 29, 36, 6, "jsx"], [21, 32, 36, 6], [21, 34, 36, 7, "LogBoxInspectorHeaderSafeArea"], [21, 63, 36, 36], [22, 8, 36, 37, "style"], [22, 13, 36, 42], [22, 15, 36, 44, "styles"], [22, 21, 36, 50], [22, 22, 36, 51, "props"], [22, 27, 36, 56], [22, 28, 36, 57, "level"], [22, 33, 36, 62], [22, 34, 36, 64], [23, 8, 36, 64, "children"], [23, 16, 36, 64], [23, 18, 37, 8], [23, 22, 37, 8, "_jsxRuntime"], [23, 33, 37, 8], [23, 34, 37, 8, "jsx"], [23, 37, 37, 8], [23, 39, 37, 9, "_View"], [23, 44, 37, 9], [23, 45, 37, 9, "default"], [23, 52, 37, 13], [24, 10, 37, 14, "style"], [24, 15, 37, 19], [24, 17, 37, 21, "styles"], [24, 23, 37, 27], [24, 24, 37, 28, "header"], [24, 30, 37, 35], [25, 10, 37, 35, "children"], [25, 18, 37, 35], [25, 20, 38, 10], [25, 24, 38, 10, "_jsxRuntime"], [25, 35, 38, 10], [25, 36, 38, 10, "jsx"], [25, 39, 38, 10], [25, 41, 38, 11, "_View"], [25, 46, 38, 11], [25, 47, 38, 11, "default"], [25, 54, 38, 15], [26, 12, 38, 16, "style"], [26, 17, 38, 21], [26, 19, 38, 23, "styles"], [26, 25, 38, 29], [26, 26, 38, 30, "title"], [26, 31, 38, 36], [27, 12, 38, 36, "children"], [27, 20, 38, 36], [27, 22, 39, 12], [27, 26, 39, 12, "_jsxRuntime"], [27, 37, 39, 12], [27, 38, 39, 12, "jsx"], [27, 41, 39, 12], [27, 43, 39, 13, "_Text"], [27, 48, 39, 13], [27, 49, 39, 13, "default"], [27, 56, 39, 17], [28, 14, 39, 18, "style"], [28, 19, 39, 23], [28, 21, 39, 25, "styles"], [28, 27, 39, 31], [28, 28, 39, 32, "titleText"], [28, 37, 39, 42], [29, 14, 39, 43, "id"], [29, 16, 39, 45], [29, 18, 39, 46], [29, 44, 39, 72], [30, 14, 39, 72, "children"], [30, 22, 39, 72], [30, 24, 39, 73], [31, 12, 41, 12], [31, 13, 41, 18], [32, 10, 41, 19], [32, 11, 42, 16], [33, 8, 42, 17], [33, 9, 43, 14], [34, 6, 43, 15], [34, 7, 44, 37], [34, 8, 44, 38], [35, 4, 46, 2], [36, 4, 48, 2], [36, 8, 48, 8, "prevIndex"], [36, 17, 48, 17], [36, 20, 49, 4, "props"], [36, 25, 49, 9], [36, 26, 49, 10, "selectedIndex"], [36, 39, 49, 23], [36, 42, 49, 26], [36, 43, 49, 27], [36, 46, 49, 30], [36, 47, 49, 31], [36, 50, 49, 34, "props"], [36, 55, 49, 39], [36, 56, 49, 40, "total"], [36, 61, 49, 45], [36, 64, 49, 48], [36, 65, 49, 49], [36, 68, 49, 52, "props"], [36, 73, 49, 57], [36, 74, 49, 58, "selectedIndex"], [36, 87, 49, 71], [36, 90, 49, 74], [36, 91, 49, 75], [37, 4, 50, 2], [37, 8, 50, 8, "nextIndex"], [37, 17, 50, 17], [37, 20, 51, 4, "props"], [37, 25, 51, 9], [37, 26, 51, 10, "selectedIndex"], [37, 39, 51, 23], [37, 42, 51, 26], [37, 43, 51, 27], [37, 46, 51, 30, "props"], [37, 51, 51, 35], [37, 52, 51, 36, "total"], [37, 57, 51, 41], [37, 60, 51, 44], [37, 61, 51, 45], [37, 64, 51, 48], [37, 65, 51, 49], [37, 68, 51, 52, "props"], [37, 73, 51, 57], [37, 74, 51, 58, "selectedIndex"], [37, 87, 51, 71], [37, 90, 51, 74], [37, 91, 51, 75], [38, 4, 53, 2], [38, 8, 53, 8, "titleText"], [38, 17, 53, 17], [38, 20, 53, 20], [38, 27, 53, 27, "props"], [38, 32, 53, 32], [38, 33, 53, 33, "selectedIndex"], [38, 46, 53, 46], [38, 49, 53, 49], [38, 50, 53, 50], [38, 57, 53, 57, "props"], [38, 62, 53, 62], [38, 63, 53, 63, "total"], [38, 68, 53, 68], [38, 70, 53, 70], [39, 4, 55, 2], [39, 11, 56, 4], [39, 15, 56, 4, "_jsxRuntime"], [39, 26, 56, 4], [39, 27, 56, 4, "jsx"], [39, 30, 56, 4], [39, 32, 56, 5, "LogBoxInspectorHeaderSafeArea"], [39, 61, 56, 34], [40, 6, 56, 35, "style"], [40, 11, 56, 40], [40, 13, 56, 42, "styles"], [40, 19, 56, 48], [40, 20, 56, 49, "props"], [40, 25, 56, 54], [40, 26, 56, 55, "level"], [40, 31, 56, 60], [40, 32, 56, 62], [41, 6, 56, 62, "children"], [41, 14, 56, 62], [41, 16, 57, 6], [41, 20, 57, 6, "_jsxRuntime"], [41, 31, 57, 6], [41, 32, 57, 6, "jsxs"], [41, 36, 57, 6], [41, 38, 57, 7, "_View"], [41, 43, 57, 7], [41, 44, 57, 7, "default"], [41, 51, 57, 11], [42, 8, 57, 12, "style"], [42, 13, 57, 17], [42, 15, 57, 19, "styles"], [42, 21, 57, 25], [42, 22, 57, 26, "header"], [42, 28, 57, 33], [43, 8, 57, 33, "children"], [43, 16, 57, 33], [43, 19, 58, 8], [43, 23, 58, 8, "_jsxRuntime"], [43, 34, 58, 8], [43, 35, 58, 8, "jsx"], [43, 38, 58, 8], [43, 40, 58, 9, "_LogBoxInspectorHeaderButton"], [43, 68, 58, 9], [43, 69, 58, 9, "default"], [43, 76, 58, 36], [44, 10, 59, 10, "id"], [44, 12, 59, 12], [44, 14, 59, 13], [44, 41, 59, 40], [45, 10, 60, 10, "disabled"], [45, 18, 60, 18], [45, 20, 60, 20, "props"], [45, 25, 60, 25], [45, 26, 60, 26, "total"], [45, 31, 60, 31], [45, 35, 60, 35], [45, 36, 60, 37], [46, 10, 61, 10, "level"], [46, 15, 61, 15], [46, 17, 61, 17, "props"], [46, 22, 61, 22], [46, 23, 61, 23, "level"], [46, 28, 61, 29], [47, 10, 62, 10, "image"], [47, 15, 62, 15], [47, 17, 62, 17, "require"], [47, 24, 62, 24], [47, 25, 62, 24, "_dependencyMap"], [47, 39, 62, 24], [47, 78, 62, 58], [47, 79, 62, 60], [48, 10, 63, 10, "onPress"], [48, 17, 63, 17], [48, 19, 63, 19, "onPress"], [48, 20, 63, 19], [48, 25, 63, 25, "props"], [48, 30, 63, 30], [48, 31, 63, 31, "onSelectIndex"], [48, 44, 63, 44], [48, 45, 63, 45, "prevIndex"], [48, 54, 63, 54], [49, 8, 63, 56], [49, 9, 64, 9], [49, 10, 64, 10], [49, 12, 65, 8], [49, 16, 65, 8, "_jsxRuntime"], [49, 27, 65, 8], [49, 28, 65, 8, "jsx"], [49, 31, 65, 8], [49, 33, 65, 9, "_View"], [49, 38, 65, 9], [49, 39, 65, 9, "default"], [49, 46, 65, 13], [50, 10, 65, 14, "style"], [50, 15, 65, 19], [50, 17, 65, 21, "styles"], [50, 23, 65, 27], [50, 24, 65, 28, "title"], [50, 29, 65, 34], [51, 10, 65, 34, "children"], [51, 18, 65, 34], [51, 20, 66, 10], [51, 24, 66, 10, "_jsxRuntime"], [51, 35, 66, 10], [51, 36, 66, 10, "jsx"], [51, 39, 66, 10], [51, 41, 66, 11, "_Text"], [51, 46, 66, 11], [51, 47, 66, 11, "default"], [51, 54, 66, 15], [52, 12, 66, 16, "style"], [52, 17, 66, 21], [52, 19, 66, 23, "styles"], [52, 25, 66, 29], [52, 26, 66, 30, "titleText"], [52, 35, 66, 40], [53, 12, 66, 41, "id"], [53, 14, 66, 43], [53, 16, 66, 44], [53, 42, 66, 70], [54, 12, 66, 70, "children"], [54, 20, 66, 70], [54, 22, 67, 13, "titleText"], [55, 10, 67, 22], [55, 11, 68, 16], [56, 8, 68, 17], [56, 9, 69, 14], [56, 10, 69, 15], [56, 12, 70, 8], [56, 16, 70, 8, "_jsxRuntime"], [56, 27, 70, 8], [56, 28, 70, 8, "jsx"], [56, 31, 70, 8], [56, 33, 70, 9, "_LogBoxInspectorHeaderButton"], [56, 61, 70, 9], [56, 62, 70, 9, "default"], [56, 69, 70, 36], [57, 10, 71, 10, "id"], [57, 12, 71, 12], [57, 14, 71, 13], [57, 41, 71, 40], [58, 10, 72, 10, "disabled"], [58, 18, 72, 18], [58, 20, 72, 20, "props"], [58, 25, 72, 25], [58, 26, 72, 26, "total"], [58, 31, 72, 31], [58, 35, 72, 35], [58, 36, 72, 37], [59, 10, 73, 10, "level"], [59, 15, 73, 15], [59, 17, 73, 17, "props"], [59, 22, 73, 22], [59, 23, 73, 23, "level"], [59, 28, 73, 29], [60, 10, 74, 10, "image"], [60, 15, 74, 15], [60, 17, 74, 17, "require"], [60, 24, 74, 24], [60, 25, 74, 24, "_dependencyMap"], [60, 39, 74, 24], [60, 79, 74, 59], [60, 80, 74, 61], [61, 10, 75, 10, "onPress"], [61, 17, 75, 17], [61, 19, 75, 19, "onPress"], [61, 20, 75, 19], [61, 25, 75, 25, "props"], [61, 30, 75, 30], [61, 31, 75, 31, "onSelectIndex"], [61, 44, 75, 44], [61, 45, 75, 45, "nextIndex"], [61, 54, 75, 54], [62, 8, 75, 56], [62, 9, 76, 9], [62, 10, 76, 10], [63, 6, 76, 10], [63, 7, 77, 12], [64, 4, 77, 13], [64, 5, 78, 35], [64, 6, 78, 36], [65, 2, 80, 0], [66, 2, 82, 0], [66, 6, 82, 6, "styles"], [66, 12, 82, 12], [66, 15, 82, 15, "StyleSheet"], [66, 34, 82, 25], [66, 35, 82, 26, "create"], [66, 41, 82, 32], [66, 42, 82, 33], [67, 4, 83, 2, "syntax"], [67, 10, 83, 8], [67, 12, 83, 10], [68, 6, 84, 4, "backgroundColor"], [68, 21, 84, 19], [68, 23, 84, 21, "LogBoxStyle"], [68, 34, 84, 32], [68, 35, 84, 33, "getFatalColor"], [68, 48, 84, 46], [68, 49, 84, 47], [69, 4, 85, 2], [69, 5, 85, 3], [70, 4, 86, 2, "fatal"], [70, 9, 86, 7], [70, 11, 86, 9], [71, 6, 87, 4, "backgroundColor"], [71, 21, 87, 19], [71, 23, 87, 21, "LogBoxStyle"], [71, 34, 87, 32], [71, 35, 87, 33, "getFatalColor"], [71, 48, 87, 46], [71, 49, 87, 47], [72, 4, 88, 2], [72, 5, 88, 3], [73, 4, 89, 2, "warn"], [73, 8, 89, 6], [73, 10, 89, 8], [74, 6, 90, 4, "backgroundColor"], [74, 21, 90, 19], [74, 23, 90, 21, "LogBoxStyle"], [74, 34, 90, 32], [74, 35, 90, 33, "getWarningColor"], [74, 50, 90, 48], [74, 51, 90, 49], [75, 4, 91, 2], [75, 5, 91, 3], [76, 4, 92, 2, "error"], [76, 9, 92, 7], [76, 11, 92, 9], [77, 6, 93, 4, "backgroundColor"], [77, 21, 93, 19], [77, 23, 93, 21, "LogBoxStyle"], [77, 34, 93, 32], [77, 35, 93, 33, "getErrorColor"], [77, 48, 93, 46], [77, 49, 93, 47], [78, 4, 94, 2], [78, 5, 94, 3], [79, 4, 95, 2, "header"], [79, 10, 95, 8], [79, 12, 95, 10], [80, 6, 96, 4, "flexDirection"], [80, 19, 96, 17], [80, 21, 96, 19], [80, 26, 96, 24], [81, 6, 97, 4, "height"], [81, 12, 97, 10], [81, 14, 97, 12, "Platform"], [81, 31, 97, 20], [81, 32, 97, 21, "select"], [81, 38, 97, 27], [81, 39, 97, 28], [82, 8, 98, 6, "android"], [82, 15, 98, 13], [82, 17, 98, 15], [82, 19, 98, 17], [83, 8, 99, 6, "ios"], [83, 11, 99, 9], [83, 13, 99, 11], [84, 6, 100, 4], [84, 7, 100, 5], [85, 4, 101, 2], [85, 5, 101, 3], [86, 4, 102, 2, "title"], [86, 9, 102, 7], [86, 11, 102, 9], [87, 6, 103, 4, "alignItems"], [87, 16, 103, 14], [87, 18, 103, 16], [87, 26, 103, 24], [88, 6, 104, 4, "flex"], [88, 10, 104, 8], [88, 12, 104, 10], [88, 13, 104, 11], [89, 6, 105, 4, "justifyContent"], [89, 20, 105, 18], [89, 22, 105, 20], [90, 4, 106, 2], [90, 5, 106, 3], [91, 4, 107, 2, "titleText"], [91, 13, 107, 11], [91, 15, 107, 13], [92, 6, 108, 4, "color"], [92, 11, 108, 9], [92, 13, 108, 11, "LogBoxStyle"], [92, 24, 108, 22], [92, 25, 108, 23, "getTextColor"], [92, 37, 108, 35], [92, 38, 108, 36], [92, 39, 108, 37], [93, 6, 109, 4, "fontSize"], [93, 14, 109, 12], [93, 16, 109, 14], [93, 18, 109, 16], [94, 6, 110, 4, "fontWeight"], [94, 16, 110, 14], [94, 18, 110, 16], [94, 23, 110, 21], [95, 6, 111, 4, "includeFontPadding"], [95, 24, 111, 22], [95, 26, 111, 24], [95, 31, 111, 29], [96, 6, 112, 4, "lineHeight"], [96, 16, 112, 14], [96, 18, 112, 16], [97, 4, 113, 2], [98, 2, 114, 0], [98, 3, 114, 1], [98, 4, 114, 2], [99, 0, 114, 3], [99, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorHeader", "LogBoxInspectorHeaderButton.props.onPress"], "mappings": "AAA;eCgC;mBC8B,oCD;mBCY,oCD;CDK"}}, "type": "js/module"}]}