{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Parse a path into an array of parts with information about each segment.\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getPatternParts = getPatternParts;\n  function getPatternParts(path) {\n    const parts = [];\n    let current = {\n      segment: ''\n    };\n    let isRegex = false;\n    let isParam = false;\n    let regexInnerParens = 0;\n\n    // One extra iteration to add the last character\n    for (let i = 0; i <= path.length; i++) {\n      const char = path[i];\n      if (char != null) {\n        current.segment += char;\n      }\n      if (char === ':') {\n        // The segment must start with a colon if it's a param\n        if (current.segment === ':') {\n          isParam = true;\n        } else if (!isRegex) {\n          throw new Error(`Encountered ':' in the middle of a segment in path: ${path}`);\n        }\n      } else if (char === '(') {\n        if (isParam) {\n          if (isRegex) {\n            // The '(' is part of the regex if we're already inside one\n            regexInnerParens++;\n          } else {\n            isRegex = true;\n          }\n        } else {\n          throw new Error(`Encountered '(' without preceding ':' in path: ${path}`);\n        }\n      } else if (char === ')') {\n        if (isParam && isRegex) {\n          if (regexInnerParens) {\n            // The ')' is part of the regex if we're already inside one\n            regexInnerParens--;\n            current.regex += char;\n          } else {\n            isRegex = false;\n            isParam = false;\n          }\n        } else {\n          throw new Error(`Encountered ')' without preceding '(' in path: ${path}`);\n        }\n      } else if (char === '?') {\n        if (current.param) {\n          isParam = false;\n          current.optional = true;\n        } else {\n          throw new Error(`Encountered '?' without preceding ':' in path: ${path}`);\n        }\n      } else if (char == null || char === '/' && !isRegex) {\n        isParam = false;\n\n        // Remove trailing slash from segment\n        current.segment = current.segment.replace(/\\/$/, '');\n        if (current.segment === '') {\n          continue;\n        }\n        if (current.param) {\n          current.param = current.param.replace(/^:/, '');\n        }\n        if (current.regex) {\n          current.regex = current.regex.replace(/^\\(/, '').replace(/\\)$/, '');\n        }\n        parts.push(current);\n        if (char == null) {\n          break;\n        }\n        current = {\n          segment: ''\n        };\n      }\n      if (isRegex) {\n        current.regex = current.regex || '';\n        current.regex += char;\n      }\n      if (isParam && !isRegex) {\n        current.param = current.param || '';\n        current.param += char;\n      }\n    }\n    if (isRegex) {\n      throw new Error(`Could not find closing ')' in path: ${path}`);\n    }\n    const params = parts.map(part => part.param).filter(Boolean);\n    for (const [index, param] of params.entries()) {\n      if (params.indexOf(param) !== index) {\n        throw new Error(`Duplicate param name '${param}' found in path: ${path}`);\n      }\n    }\n    return parts;\n  }\n});", "lineCount": 106, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 2, 3, 0, "Object"], [7, 8, 3, 0], [7, 9, 3, 0, "defineProperty"], [7, 23, 3, 0], [7, 24, 3, 0, "exports"], [7, 31, 3, 0], [8, 4, 3, 0, "value"], [8, 9, 3, 0], [9, 2, 3, 0], [10, 2, 3, 0, "exports"], [10, 9, 3, 0], [10, 10, 3, 0, "getPatternParts"], [10, 25, 3, 0], [10, 28, 3, 0, "getPatternParts"], [10, 43, 3, 0], [11, 2, 6, 7], [11, 11, 6, 16, "getPatternParts"], [11, 26, 6, 31, "getPatternParts"], [11, 27, 6, 32, "path"], [11, 31, 6, 36], [11, 33, 6, 38], [12, 4, 7, 2], [12, 10, 7, 8, "parts"], [12, 15, 7, 13], [12, 18, 7, 16], [12, 20, 7, 18], [13, 4, 8, 2], [13, 8, 8, 6, "current"], [13, 15, 8, 13], [13, 18, 8, 16], [14, 6, 9, 4, "segment"], [14, 13, 9, 11], [14, 15, 9, 13], [15, 4, 10, 2], [15, 5, 10, 3], [16, 4, 11, 2], [16, 8, 11, 6, "isRegex"], [16, 15, 11, 13], [16, 18, 11, 16], [16, 23, 11, 21], [17, 4, 12, 2], [17, 8, 12, 6, "isParam"], [17, 15, 12, 13], [17, 18, 12, 16], [17, 23, 12, 21], [18, 4, 13, 2], [18, 8, 13, 6, "regexInnerParens"], [18, 24, 13, 22], [18, 27, 13, 25], [18, 28, 13, 26], [20, 4, 15, 2], [21, 4, 16, 2], [21, 9, 16, 7], [21, 13, 16, 11, "i"], [21, 14, 16, 12], [21, 17, 16, 15], [21, 18, 16, 16], [21, 20, 16, 18, "i"], [21, 21, 16, 19], [21, 25, 16, 23, "path"], [21, 29, 16, 27], [21, 30, 16, 28, "length"], [21, 36, 16, 34], [21, 38, 16, 36, "i"], [21, 39, 16, 37], [21, 41, 16, 39], [21, 43, 16, 41], [22, 6, 17, 4], [22, 12, 17, 10, "char"], [22, 16, 17, 14], [22, 19, 17, 17, "path"], [22, 23, 17, 21], [22, 24, 17, 22, "i"], [22, 25, 17, 23], [22, 26, 17, 24], [23, 6, 18, 4], [23, 10, 18, 8, "char"], [23, 14, 18, 12], [23, 18, 18, 16], [23, 22, 18, 20], [23, 24, 18, 22], [24, 8, 19, 6, "current"], [24, 15, 19, 13], [24, 16, 19, 14, "segment"], [24, 23, 19, 21], [24, 27, 19, 25, "char"], [24, 31, 19, 29], [25, 6, 20, 4], [26, 6, 21, 4], [26, 10, 21, 8, "char"], [26, 14, 21, 12], [26, 19, 21, 17], [26, 22, 21, 20], [26, 24, 21, 22], [27, 8, 22, 6], [28, 8, 23, 6], [28, 12, 23, 10, "current"], [28, 19, 23, 17], [28, 20, 23, 18, "segment"], [28, 27, 23, 25], [28, 32, 23, 30], [28, 35, 23, 33], [28, 37, 23, 35], [29, 10, 24, 8, "isParam"], [29, 17, 24, 15], [29, 20, 24, 18], [29, 24, 24, 22], [30, 8, 25, 6], [30, 9, 25, 7], [30, 15, 25, 13], [30, 19, 25, 17], [30, 20, 25, 18, "isRegex"], [30, 27, 25, 25], [30, 29, 25, 27], [31, 10, 26, 8], [31, 16, 26, 14], [31, 20, 26, 18, "Error"], [31, 25, 26, 23], [31, 26, 26, 24], [31, 81, 26, 79, "path"], [31, 85, 26, 83], [31, 87, 26, 85], [31, 88, 26, 86], [32, 8, 27, 6], [33, 6, 28, 4], [33, 7, 28, 5], [33, 13, 28, 11], [33, 17, 28, 15, "char"], [33, 21, 28, 19], [33, 26, 28, 24], [33, 29, 28, 27], [33, 31, 28, 29], [34, 8, 29, 6], [34, 12, 29, 10, "isParam"], [34, 19, 29, 17], [34, 21, 29, 19], [35, 10, 30, 8], [35, 14, 30, 12, "isRegex"], [35, 21, 30, 19], [35, 23, 30, 21], [36, 12, 31, 10], [37, 12, 32, 10, "regexInnerParens"], [37, 28, 32, 26], [37, 30, 32, 28], [38, 10, 33, 8], [38, 11, 33, 9], [38, 17, 33, 15], [39, 12, 34, 10, "isRegex"], [39, 19, 34, 17], [39, 22, 34, 20], [39, 26, 34, 24], [40, 10, 35, 8], [41, 8, 36, 6], [41, 9, 36, 7], [41, 15, 36, 13], [42, 10, 37, 8], [42, 16, 37, 14], [42, 20, 37, 18, "Error"], [42, 25, 37, 23], [42, 26, 37, 24], [42, 76, 37, 74, "path"], [42, 80, 37, 78], [42, 82, 37, 80], [42, 83, 37, 81], [43, 8, 38, 6], [44, 6, 39, 4], [44, 7, 39, 5], [44, 13, 39, 11], [44, 17, 39, 15, "char"], [44, 21, 39, 19], [44, 26, 39, 24], [44, 29, 39, 27], [44, 31, 39, 29], [45, 8, 40, 6], [45, 12, 40, 10, "isParam"], [45, 19, 40, 17], [45, 23, 40, 21, "isRegex"], [45, 30, 40, 28], [45, 32, 40, 30], [46, 10, 41, 8], [46, 14, 41, 12, "regexInnerParens"], [46, 30, 41, 28], [46, 32, 41, 30], [47, 12, 42, 10], [48, 12, 43, 10, "regexInnerParens"], [48, 28, 43, 26], [48, 30, 43, 28], [49, 12, 44, 10, "current"], [49, 19, 44, 17], [49, 20, 44, 18, "regex"], [49, 25, 44, 23], [49, 29, 44, 27, "char"], [49, 33, 44, 31], [50, 10, 45, 8], [50, 11, 45, 9], [50, 17, 45, 15], [51, 12, 46, 10, "isRegex"], [51, 19, 46, 17], [51, 22, 46, 20], [51, 27, 46, 25], [52, 12, 47, 10, "isParam"], [52, 19, 47, 17], [52, 22, 47, 20], [52, 27, 47, 25], [53, 10, 48, 8], [54, 8, 49, 6], [54, 9, 49, 7], [54, 15, 49, 13], [55, 10, 50, 8], [55, 16, 50, 14], [55, 20, 50, 18, "Error"], [55, 25, 50, 23], [55, 26, 50, 24], [55, 76, 50, 74, "path"], [55, 80, 50, 78], [55, 82, 50, 80], [55, 83, 50, 81], [56, 8, 51, 6], [57, 6, 52, 4], [57, 7, 52, 5], [57, 13, 52, 11], [57, 17, 52, 15, "char"], [57, 21, 52, 19], [57, 26, 52, 24], [57, 29, 52, 27], [57, 31, 52, 29], [58, 8, 53, 6], [58, 12, 53, 10, "current"], [58, 19, 53, 17], [58, 20, 53, 18, "param"], [58, 25, 53, 23], [58, 27, 53, 25], [59, 10, 54, 8, "isParam"], [59, 17, 54, 15], [59, 20, 54, 18], [59, 25, 54, 23], [60, 10, 55, 8, "current"], [60, 17, 55, 15], [60, 18, 55, 16, "optional"], [60, 26, 55, 24], [60, 29, 55, 27], [60, 33, 55, 31], [61, 8, 56, 6], [61, 9, 56, 7], [61, 15, 56, 13], [62, 10, 57, 8], [62, 16, 57, 14], [62, 20, 57, 18, "Error"], [62, 25, 57, 23], [62, 26, 57, 24], [62, 76, 57, 74, "path"], [62, 80, 57, 78], [62, 82, 57, 80], [62, 83, 57, 81], [63, 8, 58, 6], [64, 6, 59, 4], [64, 7, 59, 5], [64, 13, 59, 11], [64, 17, 59, 15, "char"], [64, 21, 59, 19], [64, 25, 59, 23], [64, 29, 59, 27], [64, 33, 59, 31, "char"], [64, 37, 59, 35], [64, 42, 59, 40], [64, 45, 59, 43], [64, 49, 59, 47], [64, 50, 59, 48, "isRegex"], [64, 57, 59, 55], [64, 59, 59, 57], [65, 8, 60, 6, "isParam"], [65, 15, 60, 13], [65, 18, 60, 16], [65, 23, 60, 21], [67, 8, 62, 6], [68, 8, 63, 6, "current"], [68, 15, 63, 13], [68, 16, 63, 14, "segment"], [68, 23, 63, 21], [68, 26, 63, 24, "current"], [68, 33, 63, 31], [68, 34, 63, 32, "segment"], [68, 41, 63, 39], [68, 42, 63, 40, "replace"], [68, 49, 63, 47], [68, 50, 63, 48], [68, 55, 63, 53], [68, 57, 63, 55], [68, 59, 63, 57], [68, 60, 63, 58], [69, 8, 64, 6], [69, 12, 64, 10, "current"], [69, 19, 64, 17], [69, 20, 64, 18, "segment"], [69, 27, 64, 25], [69, 32, 64, 30], [69, 34, 64, 32], [69, 36, 64, 34], [70, 10, 65, 8], [71, 8, 66, 6], [72, 8, 67, 6], [72, 12, 67, 10, "current"], [72, 19, 67, 17], [72, 20, 67, 18, "param"], [72, 25, 67, 23], [72, 27, 67, 25], [73, 10, 68, 8, "current"], [73, 17, 68, 15], [73, 18, 68, 16, "param"], [73, 23, 68, 21], [73, 26, 68, 24, "current"], [73, 33, 68, 31], [73, 34, 68, 32, "param"], [73, 39, 68, 37], [73, 40, 68, 38, "replace"], [73, 47, 68, 45], [73, 48, 68, 46], [73, 52, 68, 50], [73, 54, 68, 52], [73, 56, 68, 54], [73, 57, 68, 55], [74, 8, 69, 6], [75, 8, 70, 6], [75, 12, 70, 10, "current"], [75, 19, 70, 17], [75, 20, 70, 18, "regex"], [75, 25, 70, 23], [75, 27, 70, 25], [76, 10, 71, 8, "current"], [76, 17, 71, 15], [76, 18, 71, 16, "regex"], [76, 23, 71, 21], [76, 26, 71, 24, "current"], [76, 33, 71, 31], [76, 34, 71, 32, "regex"], [76, 39, 71, 37], [76, 40, 71, 38, "replace"], [76, 47, 71, 45], [76, 48, 71, 46], [76, 53, 71, 51], [76, 55, 71, 53], [76, 57, 71, 55], [76, 58, 71, 56], [76, 59, 71, 57, "replace"], [76, 66, 71, 64], [76, 67, 71, 65], [76, 72, 71, 70], [76, 74, 71, 72], [76, 76, 71, 74], [76, 77, 71, 75], [77, 8, 72, 6], [78, 8, 73, 6, "parts"], [78, 13, 73, 11], [78, 14, 73, 12, "push"], [78, 18, 73, 16], [78, 19, 73, 17, "current"], [78, 26, 73, 24], [78, 27, 73, 25], [79, 8, 74, 6], [79, 12, 74, 10, "char"], [79, 16, 74, 14], [79, 20, 74, 18], [79, 24, 74, 22], [79, 26, 74, 24], [80, 10, 75, 8], [81, 8, 76, 6], [82, 8, 77, 6, "current"], [82, 15, 77, 13], [82, 18, 77, 16], [83, 10, 78, 8, "segment"], [83, 17, 78, 15], [83, 19, 78, 17], [84, 8, 79, 6], [84, 9, 79, 7], [85, 6, 80, 4], [86, 6, 81, 4], [86, 10, 81, 8, "isRegex"], [86, 17, 81, 15], [86, 19, 81, 17], [87, 8, 82, 6, "current"], [87, 15, 82, 13], [87, 16, 82, 14, "regex"], [87, 21, 82, 19], [87, 24, 82, 22, "current"], [87, 31, 82, 29], [87, 32, 82, 30, "regex"], [87, 37, 82, 35], [87, 41, 82, 39], [87, 43, 82, 41], [88, 8, 83, 6, "current"], [88, 15, 83, 13], [88, 16, 83, 14, "regex"], [88, 21, 83, 19], [88, 25, 83, 23, "char"], [88, 29, 83, 27], [89, 6, 84, 4], [90, 6, 85, 4], [90, 10, 85, 8, "isParam"], [90, 17, 85, 15], [90, 21, 85, 19], [90, 22, 85, 20, "isRegex"], [90, 29, 85, 27], [90, 31, 85, 29], [91, 8, 86, 6, "current"], [91, 15, 86, 13], [91, 16, 86, 14, "param"], [91, 21, 86, 19], [91, 24, 86, 22, "current"], [91, 31, 86, 29], [91, 32, 86, 30, "param"], [91, 37, 86, 35], [91, 41, 86, 39], [91, 43, 86, 41], [92, 8, 87, 6, "current"], [92, 15, 87, 13], [92, 16, 87, 14, "param"], [92, 21, 87, 19], [92, 25, 87, 23, "char"], [92, 29, 87, 27], [93, 6, 88, 4], [94, 4, 89, 2], [95, 4, 90, 2], [95, 8, 90, 6, "isRegex"], [95, 15, 90, 13], [95, 17, 90, 15], [96, 6, 91, 4], [96, 12, 91, 10], [96, 16, 91, 14, "Error"], [96, 21, 91, 19], [96, 22, 91, 20], [96, 61, 91, 59, "path"], [96, 65, 91, 63], [96, 67, 91, 65], [96, 68, 91, 66], [97, 4, 92, 2], [98, 4, 93, 2], [98, 10, 93, 8, "params"], [98, 16, 93, 14], [98, 19, 93, 17, "parts"], [98, 24, 93, 22], [98, 25, 93, 23, "map"], [98, 28, 93, 26], [98, 29, 93, 27, "part"], [98, 33, 93, 31], [98, 37, 93, 35, "part"], [98, 41, 93, 39], [98, 42, 93, 40, "param"], [98, 47, 93, 45], [98, 48, 93, 46], [98, 49, 93, 47, "filter"], [98, 55, 93, 53], [98, 56, 93, 54, "Boolean"], [98, 63, 93, 61], [98, 64, 93, 62], [99, 4, 94, 2], [99, 9, 94, 7], [99, 15, 94, 13], [99, 16, 94, 14, "index"], [99, 21, 94, 19], [99, 23, 94, 21, "param"], [99, 28, 94, 26], [99, 29, 94, 27], [99, 33, 94, 31, "params"], [99, 39, 94, 37], [99, 40, 94, 38, "entries"], [99, 47, 94, 45], [99, 48, 94, 46], [99, 49, 94, 47], [99, 51, 94, 49], [100, 6, 95, 4], [100, 10, 95, 8, "params"], [100, 16, 95, 14], [100, 17, 95, 15, "indexOf"], [100, 24, 95, 22], [100, 25, 95, 23, "param"], [100, 30, 95, 28], [100, 31, 95, 29], [100, 36, 95, 34, "index"], [100, 41, 95, 39], [100, 43, 95, 41], [101, 8, 96, 6], [101, 14, 96, 12], [101, 18, 96, 16, "Error"], [101, 23, 96, 21], [101, 24, 96, 22], [101, 49, 96, 47, "param"], [101, 54, 96, 52], [101, 74, 96, 72, "path"], [101, 78, 96, 76], [101, 80, 96, 78], [101, 81, 96, 79], [102, 6, 97, 4], [103, 4, 98, 2], [104, 4, 99, 2], [104, 11, 99, 9, "parts"], [104, 16, 99, 14], [105, 2, 100, 0], [106, 0, 100, 1], [106, 3]], "functionMap": {"names": ["<global>", "getPatternParts", "parts.map$argument_0"], "mappings": "AAA;OCK;2BCuF,kBD;CDO"}}, "type": "js/module"}]}