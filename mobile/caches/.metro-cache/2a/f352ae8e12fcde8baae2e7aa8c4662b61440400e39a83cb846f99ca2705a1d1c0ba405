{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 63, "index": 78}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 79}, "end": {"line": 4, "column": 59, "index": 138}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 139}, "end": {"line": 5, "column": 48, "index": 187}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderIcon = HeaderIcon;\n  exports.ICON_SIZE = exports.ICON_MARGIN = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  var _excluded = [\"source\", \"style\"];\n  function HeaderIcon(_ref) {\n    var source = _ref.source,\n      style = _ref.style,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors;\n    var _useLocale = (0, _native.useLocale)(),\n      direction = _useLocale.direction;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Image, {\n      source: source,\n      resizeMode: \"contain\",\n      fadeDuration: 0,\n      tintColor: colors.text,\n      style: [styles.icon, direction === 'rtl' && styles.flip, style],\n      ...rest\n    });\n  }\n  var ICON_SIZE = exports.ICON_SIZE = _reactNative.Platform.OS === 'ios' ? 21 : 24;\n  var ICON_MARGIN = exports.ICON_MARGIN = _reactNative.Platform.OS === 'ios' ? 8 : 3;\n  var styles = _reactNative.StyleSheet.create({\n    icon: {\n      width: ICON_SIZE,\n      height: ICON_SIZE,\n      margin: ICON_MARGIN\n    },\n    flip: {\n      transform: 'scaleX(-1)'\n    }\n  });\n});", "lineCount": 44, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "HeaderIcon"], [8, 20, 1, 13], [8, 23, 1, 13, "HeaderIcon"], [8, 33, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "ICON_SIZE"], [9, 19, 1, 13], [9, 22, 1, 13, "exports"], [9, 29, 1, 13], [9, 30, 1, 13, "ICON_MARGIN"], [9, 41, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_objectWithoutProperties2"], [10, 31, 1, 13], [10, 34, 1, 13, "_interopRequireDefault"], [10, 56, 1, 13], [10, 57, 1, 13, "require"], [10, 64, 1, 13], [10, 65, 1, 13, "_dependencyMap"], [10, 79, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_native"], [11, 13, 3, 0], [11, 16, 3, 0, "require"], [11, 23, 3, 0], [11, 24, 3, 0, "_dependencyMap"], [11, 38, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_reactNative"], [12, 18, 4, 0], [12, 21, 4, 0, "require"], [12, 28, 4, 0], [12, 29, 4, 0, "_dependencyMap"], [12, 43, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_jsxRuntime"], [13, 17, 5, 0], [13, 20, 5, 0, "require"], [13, 27, 5, 0], [13, 28, 5, 0, "_dependencyMap"], [13, 42, 5, 0], [14, 2, 5, 48], [14, 6, 5, 48, "_excluded"], [14, 15, 5, 48], [15, 2, 6, 7], [15, 11, 6, 16, "HeaderIcon"], [15, 21, 6, 26, "HeaderIcon"], [15, 22, 6, 26, "_ref"], [15, 26, 6, 26], [15, 28, 10, 3], [16, 4, 10, 3], [16, 8, 7, 2, "source"], [16, 14, 7, 8], [16, 17, 7, 8, "_ref"], [16, 21, 7, 8], [16, 22, 7, 2, "source"], [16, 28, 7, 8], [17, 6, 8, 2, "style"], [17, 11, 8, 7], [17, 14, 8, 7, "_ref"], [17, 18, 8, 7], [17, 19, 8, 2, "style"], [17, 24, 8, 7], [18, 6, 9, 5, "rest"], [18, 10, 9, 9], [18, 17, 9, 9, "_objectWithoutProperties2"], [18, 42, 9, 9], [18, 43, 9, 9, "default"], [18, 50, 9, 9], [18, 52, 9, 9, "_ref"], [18, 56, 9, 9], [18, 58, 9, 9, "_excluded"], [18, 67, 9, 9], [19, 4, 11, 2], [19, 8, 11, 2, "_useTheme"], [19, 17, 11, 2], [19, 20, 13, 6], [19, 24, 13, 6, "useTheme"], [19, 40, 13, 14], [19, 42, 13, 15], [19, 43, 13, 16], [20, 6, 12, 4, "colors"], [20, 12, 12, 10], [20, 15, 12, 10, "_useTheme"], [20, 24, 12, 10], [20, 25, 12, 4, "colors"], [20, 31, 12, 10], [21, 4, 14, 2], [21, 8, 14, 2, "_useLocale"], [21, 18, 14, 2], [21, 21, 16, 6], [21, 25, 16, 6, "useLocale"], [21, 42, 16, 15], [21, 44, 16, 16], [21, 45, 16, 17], [22, 6, 15, 4, "direction"], [22, 15, 15, 13], [22, 18, 15, 13, "_useLocale"], [22, 28, 15, 13], [22, 29, 15, 4, "direction"], [22, 38, 15, 13], [23, 4, 17, 2], [23, 11, 17, 9], [23, 24, 17, 22], [23, 28, 17, 22, "_jsx"], [23, 43, 17, 26], [23, 45, 17, 27, "Image"], [23, 63, 17, 32], [23, 65, 17, 34], [24, 6, 18, 4, "source"], [24, 12, 18, 10], [24, 14, 18, 12, "source"], [24, 20, 18, 18], [25, 6, 19, 4, "resizeMode"], [25, 16, 19, 14], [25, 18, 19, 16], [25, 27, 19, 25], [26, 6, 20, 4, "fadeDuration"], [26, 18, 20, 16], [26, 20, 20, 18], [26, 21, 20, 19], [27, 6, 21, 4, "tintColor"], [27, 15, 21, 13], [27, 17, 21, 15, "colors"], [27, 23, 21, 21], [27, 24, 21, 22, "text"], [27, 28, 21, 26], [28, 6, 22, 4, "style"], [28, 11, 22, 9], [28, 13, 22, 11], [28, 14, 22, 12, "styles"], [28, 20, 22, 18], [28, 21, 22, 19, "icon"], [28, 25, 22, 23], [28, 27, 22, 25, "direction"], [28, 36, 22, 34], [28, 41, 22, 39], [28, 46, 22, 44], [28, 50, 22, 48, "styles"], [28, 56, 22, 54], [28, 57, 22, 55, "flip"], [28, 61, 22, 59], [28, 63, 22, 61, "style"], [28, 68, 22, 66], [28, 69, 22, 67], [29, 6, 23, 4], [29, 9, 23, 7, "rest"], [30, 4, 24, 2], [30, 5, 24, 3], [30, 6, 24, 4], [31, 2, 25, 0], [32, 2, 26, 7], [32, 6, 26, 13, "ICON_SIZE"], [32, 15, 26, 22], [32, 18, 26, 22, "exports"], [32, 25, 26, 22], [32, 26, 26, 22, "ICON_SIZE"], [32, 35, 26, 22], [32, 38, 26, 25, "Platform"], [32, 59, 26, 33], [32, 60, 26, 34, "OS"], [32, 62, 26, 36], [32, 67, 26, 41], [32, 72, 26, 46], [32, 75, 26, 49], [32, 77, 26, 51], [32, 80, 26, 54], [32, 82, 26, 56], [33, 2, 27, 7], [33, 6, 27, 13, "ICON_MARGIN"], [33, 17, 27, 24], [33, 20, 27, 24, "exports"], [33, 27, 27, 24], [33, 28, 27, 24, "ICON_MARGIN"], [33, 39, 27, 24], [33, 42, 27, 27, "Platform"], [33, 63, 27, 35], [33, 64, 27, 36, "OS"], [33, 66, 27, 38], [33, 71, 27, 43], [33, 76, 27, 48], [33, 79, 27, 51], [33, 80, 27, 52], [33, 83, 27, 55], [33, 84, 27, 56], [34, 2, 28, 0], [34, 6, 28, 6, "styles"], [34, 12, 28, 12], [34, 15, 28, 15, "StyleSheet"], [34, 38, 28, 25], [34, 39, 28, 26, "create"], [34, 45, 28, 32], [34, 46, 28, 33], [35, 4, 29, 2, "icon"], [35, 8, 29, 6], [35, 10, 29, 8], [36, 6, 30, 4, "width"], [36, 11, 30, 9], [36, 13, 30, 11, "ICON_SIZE"], [36, 22, 30, 20], [37, 6, 31, 4, "height"], [37, 12, 31, 10], [37, 14, 31, 12, "ICON_SIZE"], [37, 23, 31, 21], [38, 6, 32, 4, "margin"], [38, 12, 32, 10], [38, 14, 32, 12, "ICON_MARGIN"], [39, 4, 33, 2], [39, 5, 33, 3], [40, 4, 34, 2, "flip"], [40, 8, 34, 6], [40, 10, 34, 8], [41, 6, 35, 4, "transform"], [41, 15, 35, 13], [41, 17, 35, 15], [42, 4, 36, 2], [43, 2, 37, 0], [43, 3, 37, 1], [43, 4, 37, 2], [44, 0, 37, 3], [44, 3]], "functionMap": {"names": ["<global>", "HeaderIcon"], "mappings": "AAA;OCK;CDmB"}}, "type": "js/module"}]}