{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../nodes/AnimatedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 225}, "end": {"line": 13, "column": 51, "index": 276}}], "key": "DhCqDdmUAxb9MD3TztejYikc09s=", "exportNames": ["*"]}}, {"name": "../nodes/AnimatedValueXY", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 277}, "end": {"line": 14, "column": 55, "index": 332}}], "key": "NlNyohLzvoqWwWg6od1+IdejFcg=", "exportNames": ["*"]}}, {"name": "../nodes/AnimatedInterpolation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 333}, "end": {"line": 15, "column": 67, "index": 400}}], "key": "HMmMTNDO4DEfto/Sp/MnAq0YGgU=", "exportNames": ["*"]}}, {"name": "../../../../exports/Easing", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 401}, "end": {"line": 16, "column": 48, "index": 449}}], "key": "I3zDpjNJ83m9zTJjCtxOa9hZrgA=", "exportNames": ["*"]}}, {"name": "./Animation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 450}, "end": {"line": 17, "column": 36, "index": 486}}], "key": "IpfQWRKePvRqyvs8Q+vGFqyieVw=", "exportNames": ["*"]}}, {"name": "../NativeAnimatedHelper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 487}, "end": {"line": 18, "column": 64, "index": 551}}], "key": "7+Fs6fvkAbHB0IU2p+AMhuguGZA=", "exportNames": ["*"]}}, {"name": "../nodes/AnimatedColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 552}, "end": {"line": 19, "column": 51, "index": 603}}], "key": "POxzKTLNRaHZiLfC97dJcrsUHew=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _AnimatedValue = _interopRequireDefault(require(_dependencyMap[1], \"../nodes/AnimatedValue\"));\n  var _AnimatedValueXY = _interopRequireDefault(require(_dependencyMap[2], \"../nodes/AnimatedValueXY\"));\n  var _AnimatedInterpolation = _interopRequireDefault(require(_dependencyMap[3], \"../nodes/AnimatedInterpolation\"));\n  var _Easing = _interopRequireDefault(require(_dependencyMap[4], \"../../../../exports/Easing\"));\n  var _Animation = _interopRequireDefault(require(_dependencyMap[5], \"./Animation\"));\n  var _NativeAnimatedHelper = require(_dependencyMap[6], \"../NativeAnimatedHelper\");\n  var _AnimatedColor = _interopRequireDefault(require(_dependencyMap[7], \"../nodes/AnimatedColor\"));\n  var _easeInOut;\n  function easeInOut() {\n    if (!_easeInOut) {\n      _easeInOut = _Easing.default.inOut(_Easing.default.ease);\n    }\n    return _easeInOut;\n  }\n  class TimingAnimation extends _Animation.default {\n    constructor(config) {\n      var _config$easing, _config$duration, _config$delay, _config$iterations, _config$isInteraction;\n      super();\n      this._toValue = config.toValue;\n      this._easing = (_config$easing = config.easing) !== null && _config$easing !== void 0 ? _config$easing : easeInOut();\n      this._duration = (_config$duration = config.duration) !== null && _config$duration !== void 0 ? _config$duration : 500;\n      this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;\n      this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n      this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);\n      this._platformConfig = config.platformConfig;\n      this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !this._useNativeDriver;\n    }\n    __getNativeAnimationConfig() {\n      var frameDuration = 1000.0 / 60.0;\n      var frames = [];\n      var numFrames = Math.round(this._duration / frameDuration);\n      for (var frame = 0; frame < numFrames; frame++) {\n        frames.push(this._easing(frame / numFrames));\n      }\n      frames.push(this._easing(1));\n      return {\n        type: 'frames',\n        frames,\n        toValue: this._toValue,\n        iterations: this.__iterations,\n        platformConfig: this._platformConfig\n      };\n    }\n    start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n      this.__active = true;\n      this._fromValue = fromValue;\n      this._onUpdate = onUpdate;\n      this.__onEnd = onEnd;\n      var start = () => {\n        // Animations that sometimes have 0 duration and sometimes do not\n        // still need to use the native driver when duration is 0 so as to\n        // not cause intermixed JS and native animations.\n        if (this._duration === 0 && !this._useNativeDriver) {\n          this._onUpdate(this._toValue);\n          this.__debouncedOnEnd({\n            finished: true\n          });\n        } else {\n          this._startTime = Date.now();\n          if (this._useNativeDriver) {\n            this.__startNativeAnimation(animatedValue);\n          } else {\n            this._animationFrame = requestAnimationFrame(\n            // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n            this.onUpdate.bind(this));\n          }\n        }\n      };\n      if (this._delay) {\n        this._timeout = setTimeout(start, this._delay);\n      } else {\n        start();\n      }\n    }\n    onUpdate() {\n      var now = Date.now();\n      if (now >= this._startTime + this._duration) {\n        if (this._duration === 0) {\n          this._onUpdate(this._toValue);\n        } else {\n          this._onUpdate(this._fromValue + this._easing(1) * (this._toValue - this._fromValue));\n        }\n        this.__debouncedOnEnd({\n          finished: true\n        });\n        return;\n      }\n      this._onUpdate(this._fromValue + this._easing((now - this._startTime) / this._duration) * (this._toValue - this._fromValue));\n      if (this.__active) {\n        // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n        this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n      }\n    }\n    stop() {\n      super.stop();\n      this.__active = false;\n      clearTimeout(this._timeout);\n      global.cancelAnimationFrame(this._animationFrame);\n      this.__debouncedOnEnd({\n        finished: false\n      });\n    }\n  }\n  var _default = exports.default = TimingAnimation;\n});", "lineCount": 123, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 11, 13], [14, 6, 11, 13, "_interopRequireDefault"], [14, 28, 11, 13], [14, 31, 11, 13, "require"], [14, 38, 11, 13], [14, 39, 11, 13, "_dependencyMap"], [14, 53, 11, 13], [15, 2, 11, 13, "Object"], [15, 8, 11, 13], [15, 9, 11, 13, "defineProperty"], [15, 23, 11, 13], [15, 24, 11, 13, "exports"], [15, 31, 11, 13], [16, 4, 11, 13, "value"], [16, 9, 11, 13], [17, 2, 11, 13], [18, 2, 11, 13, "exports"], [18, 9, 11, 13], [18, 10, 11, 13, "default"], [18, 17, 11, 13], [19, 2, 13, 0], [19, 6, 13, 0, "_AnimatedValue"], [19, 20, 13, 0], [19, 23, 13, 0, "_interopRequireDefault"], [19, 45, 13, 0], [19, 46, 13, 0, "require"], [19, 53, 13, 0], [19, 54, 13, 0, "_dependencyMap"], [19, 68, 13, 0], [20, 2, 14, 0], [20, 6, 14, 0, "_AnimatedValueXY"], [20, 22, 14, 0], [20, 25, 14, 0, "_interopRequireDefault"], [20, 47, 14, 0], [20, 48, 14, 0, "require"], [20, 55, 14, 0], [20, 56, 14, 0, "_dependencyMap"], [20, 70, 14, 0], [21, 2, 15, 0], [21, 6, 15, 0, "_AnimatedInterpolation"], [21, 28, 15, 0], [21, 31, 15, 0, "_interopRequireDefault"], [21, 53, 15, 0], [21, 54, 15, 0, "require"], [21, 61, 15, 0], [21, 62, 15, 0, "_dependencyMap"], [21, 76, 15, 0], [22, 2, 16, 0], [22, 6, 16, 0, "_Easing"], [22, 13, 16, 0], [22, 16, 16, 0, "_interopRequireDefault"], [22, 38, 16, 0], [22, 39, 16, 0, "require"], [22, 46, 16, 0], [22, 47, 16, 0, "_dependencyMap"], [22, 61, 16, 0], [23, 2, 17, 0], [23, 6, 17, 0, "_Animation"], [23, 16, 17, 0], [23, 19, 17, 0, "_interopRequireDefault"], [23, 41, 17, 0], [23, 42, 17, 0, "require"], [23, 49, 17, 0], [23, 50, 17, 0, "_dependencyMap"], [23, 64, 17, 0], [24, 2, 18, 0], [24, 6, 18, 0, "_NativeAnimatedHelper"], [24, 27, 18, 0], [24, 30, 18, 0, "require"], [24, 37, 18, 0], [24, 38, 18, 0, "_dependencyMap"], [24, 52, 18, 0], [25, 2, 19, 0], [25, 6, 19, 0, "_AnimatedColor"], [25, 20, 19, 0], [25, 23, 19, 0, "_interopRequireDefault"], [25, 45, 19, 0], [25, 46, 19, 0, "require"], [25, 53, 19, 0], [25, 54, 19, 0, "_dependencyMap"], [25, 68, 19, 0], [26, 2, 20, 0], [26, 6, 20, 4, "_easeInOut"], [26, 16, 20, 14], [27, 2, 21, 0], [27, 11, 21, 9, "easeInOut"], [27, 20, 21, 18, "easeInOut"], [27, 21, 21, 18], [27, 23, 21, 21], [28, 4, 22, 2], [28, 8, 22, 6], [28, 9, 22, 7, "_easeInOut"], [28, 19, 22, 17], [28, 21, 22, 19], [29, 6, 23, 4, "_easeInOut"], [29, 16, 23, 14], [29, 19, 23, 17, "Easing"], [29, 34, 23, 23], [29, 35, 23, 24, "inOut"], [29, 40, 23, 29], [29, 41, 23, 30, "Easing"], [29, 56, 23, 36], [29, 57, 23, 37, "ease"], [29, 61, 23, 41], [29, 62, 23, 42], [30, 4, 24, 2], [31, 4, 25, 2], [31, 11, 25, 9, "_easeInOut"], [31, 21, 25, 19], [32, 2, 26, 0], [33, 2, 27, 0], [33, 8, 27, 6, "TimingAnimation"], [33, 23, 27, 21], [33, 32, 27, 30, "Animation"], [33, 50, 27, 39], [33, 51, 27, 40], [34, 4, 28, 2, "constructor"], [34, 15, 28, 13, "constructor"], [34, 16, 28, 14, "config"], [34, 22, 28, 20], [34, 24, 28, 22], [35, 6, 29, 4], [35, 10, 29, 8, "_config$easing"], [35, 24, 29, 22], [35, 26, 29, 24, "_config$duration"], [35, 42, 29, 40], [35, 44, 29, 42, "_config$delay"], [35, 57, 29, 55], [35, 59, 29, 57, "_config$iterations"], [35, 77, 29, 75], [35, 79, 29, 77, "_config$isInteraction"], [35, 100, 29, 98], [36, 6, 30, 4], [36, 11, 30, 9], [36, 12, 30, 10], [36, 13, 30, 11], [37, 6, 31, 4], [37, 10, 31, 8], [37, 11, 31, 9, "_toValue"], [37, 19, 31, 17], [37, 22, 31, 20, "config"], [37, 28, 31, 26], [37, 29, 31, 27, "toValue"], [37, 36, 31, 34], [38, 6, 32, 4], [38, 10, 32, 8], [38, 11, 32, 9, "_easing"], [38, 18, 32, 16], [38, 21, 32, 19], [38, 22, 32, 20, "_config$easing"], [38, 36, 32, 34], [38, 39, 32, 37, "config"], [38, 45, 32, 43], [38, 46, 32, 44, "easing"], [38, 52, 32, 50], [38, 58, 32, 56], [38, 62, 32, 60], [38, 66, 32, 64, "_config$easing"], [38, 80, 32, 78], [38, 85, 32, 83], [38, 90, 32, 88], [38, 91, 32, 89], [38, 94, 32, 92, "_config$easing"], [38, 108, 32, 106], [38, 111, 32, 109, "easeInOut"], [38, 120, 32, 118], [38, 121, 32, 119], [38, 122, 32, 120], [39, 6, 33, 4], [39, 10, 33, 8], [39, 11, 33, 9, "_duration"], [39, 20, 33, 18], [39, 23, 33, 21], [39, 24, 33, 22, "_config$duration"], [39, 40, 33, 38], [39, 43, 33, 41, "config"], [39, 49, 33, 47], [39, 50, 33, 48, "duration"], [39, 58, 33, 56], [39, 64, 33, 62], [39, 68, 33, 66], [39, 72, 33, 70, "_config$duration"], [39, 88, 33, 86], [39, 93, 33, 91], [39, 98, 33, 96], [39, 99, 33, 97], [39, 102, 33, 100, "_config$duration"], [39, 118, 33, 116], [39, 121, 33, 119], [39, 124, 33, 122], [40, 6, 34, 4], [40, 10, 34, 8], [40, 11, 34, 9, "_delay"], [40, 17, 34, 15], [40, 20, 34, 18], [40, 21, 34, 19, "_config$delay"], [40, 34, 34, 32], [40, 37, 34, 35, "config"], [40, 43, 34, 41], [40, 44, 34, 42, "delay"], [40, 49, 34, 47], [40, 55, 34, 53], [40, 59, 34, 57], [40, 63, 34, 61, "_config$delay"], [40, 76, 34, 74], [40, 81, 34, 79], [40, 86, 34, 84], [40, 87, 34, 85], [40, 90, 34, 88, "_config$delay"], [40, 103, 34, 101], [40, 106, 34, 104], [40, 107, 34, 105], [41, 6, 35, 4], [41, 10, 35, 8], [41, 11, 35, 9, "__iterations"], [41, 23, 35, 21], [41, 26, 35, 24], [41, 27, 35, 25, "_config$iterations"], [41, 45, 35, 43], [41, 48, 35, 46, "config"], [41, 54, 35, 52], [41, 55, 35, 53, "iterations"], [41, 65, 35, 63], [41, 71, 35, 69], [41, 75, 35, 73], [41, 79, 35, 77, "_config$iterations"], [41, 97, 35, 95], [41, 102, 35, 100], [41, 107, 35, 105], [41, 108, 35, 106], [41, 111, 35, 109, "_config$iterations"], [41, 129, 35, 127], [41, 132, 35, 130], [41, 133, 35, 131], [42, 6, 36, 4], [42, 10, 36, 8], [42, 11, 36, 9, "_useNativeDriver"], [42, 27, 36, 25], [42, 30, 36, 28], [42, 34, 36, 28, "shouldUseNativeDriver"], [42, 77, 36, 49], [42, 79, 36, 50, "config"], [42, 85, 36, 56], [42, 86, 36, 57], [43, 6, 37, 4], [43, 10, 37, 8], [43, 11, 37, 9, "_platformConfig"], [43, 26, 37, 24], [43, 29, 37, 27, "config"], [43, 35, 37, 33], [43, 36, 37, 34, "platformConfig"], [43, 50, 37, 48], [44, 6, 38, 4], [44, 10, 38, 8], [44, 11, 38, 9, "__isInteraction"], [44, 26, 38, 24], [44, 29, 38, 27], [44, 30, 38, 28, "_config$isInteraction"], [44, 51, 38, 49], [44, 54, 38, 52, "config"], [44, 60, 38, 58], [44, 61, 38, 59, "isInteraction"], [44, 74, 38, 72], [44, 80, 38, 78], [44, 84, 38, 82], [44, 88, 38, 86, "_config$isInteraction"], [44, 109, 38, 107], [44, 114, 38, 112], [44, 119, 38, 117], [44, 120, 38, 118], [44, 123, 38, 121, "_config$isInteraction"], [44, 144, 38, 142], [44, 147, 38, 145], [44, 148, 38, 146], [44, 152, 38, 150], [44, 153, 38, 151, "_useNativeDriver"], [44, 169, 38, 167], [45, 4, 39, 2], [46, 4, 40, 2, "__getNativeAnimationConfig"], [46, 30, 40, 28, "__getNativeAnimationConfig"], [46, 31, 40, 28], [46, 33, 40, 31], [47, 6, 41, 4], [47, 10, 41, 8, "frameDuration"], [47, 23, 41, 21], [47, 26, 41, 24], [47, 32, 41, 30], [47, 35, 41, 33], [47, 39, 41, 37], [48, 6, 42, 4], [48, 10, 42, 8, "frames"], [48, 16, 42, 14], [48, 19, 42, 17], [48, 21, 42, 19], [49, 6, 43, 4], [49, 10, 43, 8, "numFrames"], [49, 19, 43, 17], [49, 22, 43, 20, "Math"], [49, 26, 43, 24], [49, 27, 43, 25, "round"], [49, 32, 43, 30], [49, 33, 43, 31], [49, 37, 43, 35], [49, 38, 43, 36, "_duration"], [49, 47, 43, 45], [49, 50, 43, 48, "frameDuration"], [49, 63, 43, 61], [49, 64, 43, 62], [50, 6, 44, 4], [50, 11, 44, 9], [50, 15, 44, 13, "frame"], [50, 20, 44, 18], [50, 23, 44, 21], [50, 24, 44, 22], [50, 26, 44, 24, "frame"], [50, 31, 44, 29], [50, 34, 44, 32, "numFrames"], [50, 43, 44, 41], [50, 45, 44, 43, "frame"], [50, 50, 44, 48], [50, 52, 44, 50], [50, 54, 44, 52], [51, 8, 45, 6, "frames"], [51, 14, 45, 12], [51, 15, 45, 13, "push"], [51, 19, 45, 17], [51, 20, 45, 18], [51, 24, 45, 22], [51, 25, 45, 23, "_easing"], [51, 32, 45, 30], [51, 33, 45, 31, "frame"], [51, 38, 45, 36], [51, 41, 45, 39, "numFrames"], [51, 50, 45, 48], [51, 51, 45, 49], [51, 52, 45, 50], [52, 6, 46, 4], [53, 6, 47, 4, "frames"], [53, 12, 47, 10], [53, 13, 47, 11, "push"], [53, 17, 47, 15], [53, 18, 47, 16], [53, 22, 47, 20], [53, 23, 47, 21, "_easing"], [53, 30, 47, 28], [53, 31, 47, 29], [53, 32, 47, 30], [53, 33, 47, 31], [53, 34, 47, 32], [54, 6, 48, 4], [54, 13, 48, 11], [55, 8, 49, 6, "type"], [55, 12, 49, 10], [55, 14, 49, 12], [55, 22, 49, 20], [56, 8, 50, 6, "frames"], [56, 14, 50, 12], [57, 8, 51, 6, "toValue"], [57, 15, 51, 13], [57, 17, 51, 15], [57, 21, 51, 19], [57, 22, 51, 20, "_toValue"], [57, 30, 51, 28], [58, 8, 52, 6, "iterations"], [58, 18, 52, 16], [58, 20, 52, 18], [58, 24, 52, 22], [58, 25, 52, 23, "__iterations"], [58, 37, 52, 35], [59, 8, 53, 6, "platformConfig"], [59, 22, 53, 20], [59, 24, 53, 22], [59, 28, 53, 26], [59, 29, 53, 27, "_platformConfig"], [60, 6, 54, 4], [60, 7, 54, 5], [61, 4, 55, 2], [62, 4, 56, 2, "start"], [62, 9, 56, 7, "start"], [62, 10, 56, 8, "fromValue"], [62, 19, 56, 17], [62, 21, 56, 19, "onUpdate"], [62, 29, 56, 27], [62, 31, 56, 29, "onEnd"], [62, 36, 56, 34], [62, 38, 56, 36, "previousAnimation"], [62, 55, 56, 53], [62, 57, 56, 55, "animatedValue"], [62, 70, 56, 68], [62, 72, 56, 70], [63, 6, 57, 4], [63, 10, 57, 8], [63, 11, 57, 9, "__active"], [63, 19, 57, 17], [63, 22, 57, 20], [63, 26, 57, 24], [64, 6, 58, 4], [64, 10, 58, 8], [64, 11, 58, 9, "_fromValue"], [64, 21, 58, 19], [64, 24, 58, 22, "fromValue"], [64, 33, 58, 31], [65, 6, 59, 4], [65, 10, 59, 8], [65, 11, 59, 9, "_onUpdate"], [65, 20, 59, 18], [65, 23, 59, 21, "onUpdate"], [65, 31, 59, 29], [66, 6, 60, 4], [66, 10, 60, 8], [66, 11, 60, 9, "__onEnd"], [66, 18, 60, 16], [66, 21, 60, 19, "onEnd"], [66, 26, 60, 24], [67, 6, 61, 4], [67, 10, 61, 8, "start"], [67, 15, 61, 13], [67, 18, 61, 16, "start"], [67, 19, 61, 16], [67, 24, 61, 22], [68, 8, 62, 6], [69, 8, 63, 6], [70, 8, 64, 6], [71, 8, 65, 6], [71, 12, 65, 10], [71, 16, 65, 14], [71, 17, 65, 15, "_duration"], [71, 26, 65, 24], [71, 31, 65, 29], [71, 32, 65, 30], [71, 36, 65, 34], [71, 37, 65, 35], [71, 41, 65, 39], [71, 42, 65, 40, "_useNativeDriver"], [71, 58, 65, 56], [71, 60, 65, 58], [72, 10, 66, 8], [72, 14, 66, 12], [72, 15, 66, 13, "_onUpdate"], [72, 24, 66, 22], [72, 25, 66, 23], [72, 29, 66, 27], [72, 30, 66, 28, "_toValue"], [72, 38, 66, 36], [72, 39, 66, 37], [73, 10, 67, 8], [73, 14, 67, 12], [73, 15, 67, 13, "__debouncedOnEnd"], [73, 31, 67, 29], [73, 32, 67, 30], [74, 12, 68, 10, "finished"], [74, 20, 68, 18], [74, 22, 68, 20], [75, 10, 69, 8], [75, 11, 69, 9], [75, 12, 69, 10], [76, 8, 70, 6], [76, 9, 70, 7], [76, 15, 70, 13], [77, 10, 71, 8], [77, 14, 71, 12], [77, 15, 71, 13, "_startTime"], [77, 25, 71, 23], [77, 28, 71, 26, "Date"], [77, 32, 71, 30], [77, 33, 71, 31, "now"], [77, 36, 71, 34], [77, 37, 71, 35], [77, 38, 71, 36], [78, 10, 72, 8], [78, 14, 72, 12], [78, 18, 72, 16], [78, 19, 72, 17, "_useNativeDriver"], [78, 35, 72, 33], [78, 37, 72, 35], [79, 12, 73, 10], [79, 16, 73, 14], [79, 17, 73, 15, "__startNativeAnimation"], [79, 39, 73, 37], [79, 40, 73, 38, "animatedValue"], [79, 53, 73, 51], [79, 54, 73, 52], [80, 10, 74, 8], [80, 11, 74, 9], [80, 17, 74, 15], [81, 12, 75, 10], [81, 16, 75, 14], [81, 17, 75, 15, "_animationFrame"], [81, 32, 75, 30], [81, 35, 75, 33, "requestAnimationFrame"], [81, 56, 75, 54], [82, 12, 76, 10], [83, 12, 77, 10], [83, 16, 77, 14], [83, 17, 77, 15, "onUpdate"], [83, 25, 77, 23], [83, 26, 77, 24, "bind"], [83, 30, 77, 28], [83, 31, 77, 29], [83, 35, 77, 33], [83, 36, 77, 34], [83, 37, 77, 35], [84, 10, 78, 8], [85, 8, 79, 6], [86, 6, 80, 4], [86, 7, 80, 5], [87, 6, 81, 4], [87, 10, 81, 8], [87, 14, 81, 12], [87, 15, 81, 13, "_delay"], [87, 21, 81, 19], [87, 23, 81, 21], [88, 8, 82, 6], [88, 12, 82, 10], [88, 13, 82, 11, "_timeout"], [88, 21, 82, 19], [88, 24, 82, 22, "setTimeout"], [88, 34, 82, 32], [88, 35, 82, 33, "start"], [88, 40, 82, 38], [88, 42, 82, 40], [88, 46, 82, 44], [88, 47, 82, 45, "_delay"], [88, 53, 82, 51], [88, 54, 82, 52], [89, 6, 83, 4], [89, 7, 83, 5], [89, 13, 83, 11], [90, 8, 84, 6, "start"], [90, 13, 84, 11], [90, 14, 84, 12], [90, 15, 84, 13], [91, 6, 85, 4], [92, 4, 86, 2], [93, 4, 87, 2, "onUpdate"], [93, 12, 87, 10, "onUpdate"], [93, 13, 87, 10], [93, 15, 87, 13], [94, 6, 88, 4], [94, 10, 88, 8, "now"], [94, 13, 88, 11], [94, 16, 88, 14, "Date"], [94, 20, 88, 18], [94, 21, 88, 19, "now"], [94, 24, 88, 22], [94, 25, 88, 23], [94, 26, 88, 24], [95, 6, 89, 4], [95, 10, 89, 8, "now"], [95, 13, 89, 11], [95, 17, 89, 15], [95, 21, 89, 19], [95, 22, 89, 20, "_startTime"], [95, 32, 89, 30], [95, 35, 89, 33], [95, 39, 89, 37], [95, 40, 89, 38, "_duration"], [95, 49, 89, 47], [95, 51, 89, 49], [96, 8, 90, 6], [96, 12, 90, 10], [96, 16, 90, 14], [96, 17, 90, 15, "_duration"], [96, 26, 90, 24], [96, 31, 90, 29], [96, 32, 90, 30], [96, 34, 90, 32], [97, 10, 91, 8], [97, 14, 91, 12], [97, 15, 91, 13, "_onUpdate"], [97, 24, 91, 22], [97, 25, 91, 23], [97, 29, 91, 27], [97, 30, 91, 28, "_toValue"], [97, 38, 91, 36], [97, 39, 91, 37], [98, 8, 92, 6], [98, 9, 92, 7], [98, 15, 92, 13], [99, 10, 93, 8], [99, 14, 93, 12], [99, 15, 93, 13, "_onUpdate"], [99, 24, 93, 22], [99, 25, 93, 23], [99, 29, 93, 27], [99, 30, 93, 28, "_fromValue"], [99, 40, 93, 38], [99, 43, 93, 41], [99, 47, 93, 45], [99, 48, 93, 46, "_easing"], [99, 55, 93, 53], [99, 56, 93, 54], [99, 57, 93, 55], [99, 58, 93, 56], [99, 62, 93, 60], [99, 66, 93, 64], [99, 67, 93, 65, "_toValue"], [99, 75, 93, 73], [99, 78, 93, 76], [99, 82, 93, 80], [99, 83, 93, 81, "_fromValue"], [99, 93, 93, 91], [99, 94, 93, 92], [99, 95, 93, 93], [100, 8, 94, 6], [101, 8, 95, 6], [101, 12, 95, 10], [101, 13, 95, 11, "__debouncedOnEnd"], [101, 29, 95, 27], [101, 30, 95, 28], [102, 10, 96, 8, "finished"], [102, 18, 96, 16], [102, 20, 96, 18], [103, 8, 97, 6], [103, 9, 97, 7], [103, 10, 97, 8], [104, 8, 98, 6], [105, 6, 99, 4], [106, 6, 100, 4], [106, 10, 100, 8], [106, 11, 100, 9, "_onUpdate"], [106, 20, 100, 18], [106, 21, 100, 19], [106, 25, 100, 23], [106, 26, 100, 24, "_fromValue"], [106, 36, 100, 34], [106, 39, 100, 37], [106, 43, 100, 41], [106, 44, 100, 42, "_easing"], [106, 51, 100, 49], [106, 52, 100, 50], [106, 53, 100, 51, "now"], [106, 56, 100, 54], [106, 59, 100, 57], [106, 63, 100, 61], [106, 64, 100, 62, "_startTime"], [106, 74, 100, 72], [106, 78, 100, 76], [106, 82, 100, 80], [106, 83, 100, 81, "_duration"], [106, 92, 100, 90], [106, 93, 100, 91], [106, 97, 100, 95], [106, 101, 100, 99], [106, 102, 100, 100, "_toValue"], [106, 110, 100, 108], [106, 113, 100, 111], [106, 117, 100, 115], [106, 118, 100, 116, "_fromValue"], [106, 128, 100, 126], [106, 129, 100, 127], [106, 130, 100, 128], [107, 6, 101, 4], [107, 10, 101, 8], [107, 14, 101, 12], [107, 15, 101, 13, "__active"], [107, 23, 101, 21], [107, 25, 101, 23], [108, 8, 102, 6], [109, 8, 103, 6], [109, 12, 103, 10], [109, 13, 103, 11, "_animationFrame"], [109, 28, 103, 26], [109, 31, 103, 29, "requestAnimationFrame"], [109, 52, 103, 50], [109, 53, 103, 51], [109, 57, 103, 55], [109, 58, 103, 56, "onUpdate"], [109, 66, 103, 64], [109, 67, 103, 65, "bind"], [109, 71, 103, 69], [109, 72, 103, 70], [109, 76, 103, 74], [109, 77, 103, 75], [109, 78, 103, 76], [110, 6, 104, 4], [111, 4, 105, 2], [112, 4, 106, 2, "stop"], [112, 8, 106, 6, "stop"], [112, 9, 106, 6], [112, 11, 106, 9], [113, 6, 107, 4], [113, 11, 107, 9], [113, 12, 107, 10, "stop"], [113, 16, 107, 14], [113, 17, 107, 15], [113, 18, 107, 16], [114, 6, 108, 4], [114, 10, 108, 8], [114, 11, 108, 9, "__active"], [114, 19, 108, 17], [114, 22, 108, 20], [114, 27, 108, 25], [115, 6, 109, 4, "clearTimeout"], [115, 18, 109, 16], [115, 19, 109, 17], [115, 23, 109, 21], [115, 24, 109, 22, "_timeout"], [115, 32, 109, 30], [115, 33, 109, 31], [116, 6, 110, 4, "global"], [116, 12, 110, 10], [116, 13, 110, 11, "cancelAnimationFrame"], [116, 33, 110, 31], [116, 34, 110, 32], [116, 38, 110, 36], [116, 39, 110, 37, "_animationFrame"], [116, 54, 110, 52], [116, 55, 110, 53], [117, 6, 111, 4], [117, 10, 111, 8], [117, 11, 111, 9, "__debouncedOnEnd"], [117, 27, 111, 25], [117, 28, 111, 26], [118, 8, 112, 6, "finished"], [118, 16, 112, 14], [118, 18, 112, 16], [119, 6, 113, 4], [119, 7, 113, 5], [119, 8, 113, 6], [120, 4, 114, 2], [121, 2, 115, 0], [122, 2, 115, 1], [122, 6, 115, 1, "_default"], [122, 14, 115, 1], [122, 17, 115, 1, "exports"], [122, 24, 115, 1], [122, 25, 115, 1, "default"], [122, 32, 115, 1], [122, 35, 116, 15, "TimingAnimation"], [122, 50, 116, 30], [123, 0, 116, 30], [123, 3]], "functionMap": {"names": ["<global>", "easeInOut", "TimingAnimation", "constructor", "__getNativeAnimationConfig", "start", "onUpdate", "stop"], "mappings": "AAA;ACoB;CDK;AEC;ECC;GDW;EEC;GFe;EGC;GH8B;EIC;GJkB;EKC;GLQ;CFC"}}, "type": "js/module"}]}