{"dependencies": [{"name": "./toaster.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 47, "index": 62}}], "key": "T2WmRtyvYiai9mUWA+A4OvdsaCE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.toast = void 0;\n  var _toaster = require(_dependencyMap[0], \"./toaster.js\");\n  const toast = (title, options) => {\n    return (0, _toaster.getToastContext)().addToast({\n      title,\n      variant: 'info',\n      ...options\n    });\n  };\n  exports.toast = toast;\n  toast.success = (title, options = {}) => {\n    return (0, _toaster.getToastContext)().addToast({\n      ...options,\n      title,\n      variant: 'success'\n    });\n  };\n  toast.wiggle = id => {\n    return (0, _toaster.getToastContext)().wiggleToast(id);\n  };\n  toast.error = (title, options = {}) => {\n    return (0, _toaster.getToastContext)().addToast({\n      ...options,\n      title,\n      variant: 'error'\n    });\n  };\n  toast.warning = (title, options = {}) => {\n    return (0, _toaster.getToastContext)().addToast({\n      ...options,\n      title,\n      variant: 'warning'\n    });\n  };\n  toast.info = (title, options = {}) => {\n    return (0, _toaster.getToastContext)().addToast({\n      title,\n      ...options,\n      variant: 'info'\n    });\n  };\n  toast.promise = (promise, options) => {\n    return (0, _toaster.getToastContext)().addToast({\n      ...options,\n      title: options.loading,\n      variant: 'info',\n      promiseOptions: {\n        ...options,\n        promise\n      }\n    });\n  };\n  toast.custom = (jsx, options) => {\n    return (0, _toaster.getToastContext)().addToast({\n      title: '',\n      variant: 'info',\n      jsx,\n      ...options\n    });\n  };\n  toast.loading = (title, options = {}) => {\n    return (0, _toaster.getToastContext)().addToast({\n      title,\n      variant: 'loading',\n      ...options\n    });\n  };\n  toast.dismiss = id => {\n    return (0, _toaster.getToastContext)().dismissToast(id);\n  };\n});", "lineCount": 77, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "toast"], [7, 15, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_toaster"], [8, 14, 3, 0], [8, 17, 3, 0, "require"], [8, 24, 3, 0], [8, 25, 3, 0, "_dependencyMap"], [8, 39, 3, 0], [9, 2, 4, 7], [9, 8, 4, 13, "toast"], [9, 13, 4, 18], [9, 16, 4, 21, "toast"], [9, 17, 4, 22, "title"], [9, 22, 4, 27], [9, 24, 4, 29, "options"], [9, 31, 4, 36], [9, 36, 4, 41], [10, 4, 5, 2], [10, 11, 5, 9], [10, 15, 5, 9, "getToastContext"], [10, 39, 5, 24], [10, 41, 5, 25], [10, 42, 5, 26], [10, 43, 5, 27, "addToast"], [10, 51, 5, 35], [10, 52, 5, 36], [11, 6, 6, 4, "title"], [11, 11, 6, 9], [12, 6, 7, 4, "variant"], [12, 13, 7, 11], [12, 15, 7, 13], [12, 21, 7, 19], [13, 6, 8, 4], [13, 9, 8, 7, "options"], [14, 4, 9, 2], [14, 5, 9, 3], [14, 6, 9, 4], [15, 2, 10, 0], [15, 3, 10, 1], [16, 2, 10, 2, "exports"], [16, 9, 10, 2], [16, 10, 10, 2, "toast"], [16, 15, 10, 2], [16, 18, 10, 2, "toast"], [16, 23, 10, 2], [17, 2, 11, 0, "toast"], [17, 7, 11, 5], [17, 8, 11, 6, "success"], [17, 15, 11, 13], [17, 18, 11, 16], [17, 19, 11, 17, "title"], [17, 24, 11, 22], [17, 26, 11, 24, "options"], [17, 33, 11, 31], [17, 36, 11, 34], [17, 37, 11, 35], [17, 38, 11, 36], [17, 43, 11, 41], [18, 4, 12, 2], [18, 11, 12, 9], [18, 15, 12, 9, "getToastContext"], [18, 39, 12, 24], [18, 41, 12, 25], [18, 42, 12, 26], [18, 43, 12, 27, "addToast"], [18, 51, 12, 35], [18, 52, 12, 36], [19, 6, 13, 4], [19, 9, 13, 7, "options"], [19, 16, 13, 14], [20, 6, 14, 4, "title"], [20, 11, 14, 9], [21, 6, 15, 4, "variant"], [21, 13, 15, 11], [21, 15, 15, 13], [22, 4, 16, 2], [22, 5, 16, 3], [22, 6, 16, 4], [23, 2, 17, 0], [23, 3, 17, 1], [24, 2, 18, 0, "toast"], [24, 7, 18, 5], [24, 8, 18, 6, "wiggle"], [24, 14, 18, 12], [24, 17, 18, 15, "id"], [24, 19, 18, 17], [24, 23, 18, 21], [25, 4, 19, 2], [25, 11, 19, 9], [25, 15, 19, 9, "getToastContext"], [25, 39, 19, 24], [25, 41, 19, 25], [25, 42, 19, 26], [25, 43, 19, 27, "wiggleToast"], [25, 54, 19, 38], [25, 55, 19, 39, "id"], [25, 57, 19, 41], [25, 58, 19, 42], [26, 2, 20, 0], [26, 3, 20, 1], [27, 2, 21, 0, "toast"], [27, 7, 21, 5], [27, 8, 21, 6, "error"], [27, 13, 21, 11], [27, 16, 21, 14], [27, 17, 21, 15, "title"], [27, 22, 21, 20], [27, 24, 21, 22, "options"], [27, 31, 21, 29], [27, 34, 21, 32], [27, 35, 21, 33], [27, 36, 21, 34], [27, 41, 21, 39], [28, 4, 22, 2], [28, 11, 22, 9], [28, 15, 22, 9, "getToastContext"], [28, 39, 22, 24], [28, 41, 22, 25], [28, 42, 22, 26], [28, 43, 22, 27, "addToast"], [28, 51, 22, 35], [28, 52, 22, 36], [29, 6, 23, 4], [29, 9, 23, 7, "options"], [29, 16, 23, 14], [30, 6, 24, 4, "title"], [30, 11, 24, 9], [31, 6, 25, 4, "variant"], [31, 13, 25, 11], [31, 15, 25, 13], [32, 4, 26, 2], [32, 5, 26, 3], [32, 6, 26, 4], [33, 2, 27, 0], [33, 3, 27, 1], [34, 2, 28, 0, "toast"], [34, 7, 28, 5], [34, 8, 28, 6, "warning"], [34, 15, 28, 13], [34, 18, 28, 16], [34, 19, 28, 17, "title"], [34, 24, 28, 22], [34, 26, 28, 24, "options"], [34, 33, 28, 31], [34, 36, 28, 34], [34, 37, 28, 35], [34, 38, 28, 36], [34, 43, 28, 41], [35, 4, 29, 2], [35, 11, 29, 9], [35, 15, 29, 9, "getToastContext"], [35, 39, 29, 24], [35, 41, 29, 25], [35, 42, 29, 26], [35, 43, 29, 27, "addToast"], [35, 51, 29, 35], [35, 52, 29, 36], [36, 6, 30, 4], [36, 9, 30, 7, "options"], [36, 16, 30, 14], [37, 6, 31, 4, "title"], [37, 11, 31, 9], [38, 6, 32, 4, "variant"], [38, 13, 32, 11], [38, 15, 32, 13], [39, 4, 33, 2], [39, 5, 33, 3], [39, 6, 33, 4], [40, 2, 34, 0], [40, 3, 34, 1], [41, 2, 35, 0, "toast"], [41, 7, 35, 5], [41, 8, 35, 6, "info"], [41, 12, 35, 10], [41, 15, 35, 13], [41, 16, 35, 14, "title"], [41, 21, 35, 19], [41, 23, 35, 21, "options"], [41, 30, 35, 28], [41, 33, 35, 31], [41, 34, 35, 32], [41, 35, 35, 33], [41, 40, 35, 38], [42, 4, 36, 2], [42, 11, 36, 9], [42, 15, 36, 9, "getToastContext"], [42, 39, 36, 24], [42, 41, 36, 25], [42, 42, 36, 26], [42, 43, 36, 27, "addToast"], [42, 51, 36, 35], [42, 52, 36, 36], [43, 6, 37, 4, "title"], [43, 11, 37, 9], [44, 6, 38, 4], [44, 9, 38, 7, "options"], [44, 16, 38, 14], [45, 6, 39, 4, "variant"], [45, 13, 39, 11], [45, 15, 39, 13], [46, 4, 40, 2], [46, 5, 40, 3], [46, 6, 40, 4], [47, 2, 41, 0], [47, 3, 41, 1], [48, 2, 42, 0, "toast"], [48, 7, 42, 5], [48, 8, 42, 6, "promise"], [48, 15, 42, 13], [48, 18, 42, 16], [48, 19, 42, 17, "promise"], [48, 26, 42, 24], [48, 28, 42, 26, "options"], [48, 35, 42, 33], [48, 40, 42, 38], [49, 4, 43, 2], [49, 11, 43, 9], [49, 15, 43, 9, "getToastContext"], [49, 39, 43, 24], [49, 41, 43, 25], [49, 42, 43, 26], [49, 43, 43, 27, "addToast"], [49, 51, 43, 35], [49, 52, 43, 36], [50, 6, 44, 4], [50, 9, 44, 7, "options"], [50, 16, 44, 14], [51, 6, 45, 4, "title"], [51, 11, 45, 9], [51, 13, 45, 11, "options"], [51, 20, 45, 18], [51, 21, 45, 19, "loading"], [51, 28, 45, 26], [52, 6, 46, 4, "variant"], [52, 13, 46, 11], [52, 15, 46, 13], [52, 21, 46, 19], [53, 6, 47, 4, "promiseOptions"], [53, 20, 47, 18], [53, 22, 47, 20], [54, 8, 48, 6], [54, 11, 48, 9, "options"], [54, 18, 48, 16], [55, 8, 49, 6, "promise"], [56, 6, 50, 4], [57, 4, 51, 2], [57, 5, 51, 3], [57, 6, 51, 4], [58, 2, 52, 0], [58, 3, 52, 1], [59, 2, 53, 0, "toast"], [59, 7, 53, 5], [59, 8, 53, 6, "custom"], [59, 14, 53, 12], [59, 17, 53, 15], [59, 18, 53, 16, "jsx"], [59, 21, 53, 19], [59, 23, 53, 21, "options"], [59, 30, 53, 28], [59, 35, 53, 33], [60, 4, 54, 2], [60, 11, 54, 9], [60, 15, 54, 9, "getToastContext"], [60, 39, 54, 24], [60, 41, 54, 25], [60, 42, 54, 26], [60, 43, 54, 27, "addToast"], [60, 51, 54, 35], [60, 52, 54, 36], [61, 6, 55, 4, "title"], [61, 11, 55, 9], [61, 13, 55, 11], [61, 15, 55, 13], [62, 6, 56, 4, "variant"], [62, 13, 56, 11], [62, 15, 56, 13], [62, 21, 56, 19], [63, 6, 57, 4, "jsx"], [63, 9, 57, 7], [64, 6, 58, 4], [64, 9, 58, 7, "options"], [65, 4, 59, 2], [65, 5, 59, 3], [65, 6, 59, 4], [66, 2, 60, 0], [66, 3, 60, 1], [67, 2, 61, 0, "toast"], [67, 7, 61, 5], [67, 8, 61, 6, "loading"], [67, 15, 61, 13], [67, 18, 61, 16], [67, 19, 61, 17, "title"], [67, 24, 61, 22], [67, 26, 61, 24, "options"], [67, 33, 61, 31], [67, 36, 61, 34], [67, 37, 61, 35], [67, 38, 61, 36], [67, 43, 61, 41], [68, 4, 62, 2], [68, 11, 62, 9], [68, 15, 62, 9, "getToastContext"], [68, 39, 62, 24], [68, 41, 62, 25], [68, 42, 62, 26], [68, 43, 62, 27, "addToast"], [68, 51, 62, 35], [68, 52, 62, 36], [69, 6, 63, 4, "title"], [69, 11, 63, 9], [70, 6, 64, 4, "variant"], [70, 13, 64, 11], [70, 15, 64, 13], [70, 24, 64, 22], [71, 6, 65, 4], [71, 9, 65, 7, "options"], [72, 4, 66, 2], [72, 5, 66, 3], [72, 6, 66, 4], [73, 2, 67, 0], [73, 3, 67, 1], [74, 2, 68, 0, "toast"], [74, 7, 68, 5], [74, 8, 68, 6, "dismiss"], [74, 15, 68, 13], [74, 18, 68, 16, "id"], [74, 20, 68, 18], [74, 24, 68, 22], [75, 4, 69, 2], [75, 11, 69, 9], [75, 15, 69, 9, "getToastContext"], [75, 39, 69, 24], [75, 41, 69, 25], [75, 42, 69, 26], [75, 43, 69, 27, "dismissToast"], [75, 55, 69, 39], [75, 56, 69, 40, "id"], [75, 58, 69, 42], [75, 59, 69, 43], [76, 2, 70, 0], [76, 3, 70, 1], [77, 0, 70, 2], [77, 3]], "functionMap": {"names": ["<global>", "toast", "toast.success", "toast.wiggle", "toast.error", "toast.warning", "toast.info", "toast.promise", "toast.custom", "toast.loading", "toast.dismiss"], "mappings": "AAA;qBCG;CDM;gBEC;CFM;eGC;CHE;cIC;CJM;gBKC;CLM;aMC;CNM;gBOC;CPU;eQC;CRO;gBSC;CTM;gBUC;CVE"}}, "type": "js/module"}]}