{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 98}, "end": {"line": 5, "column": 93, "index": 191}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 27, "column": 0, "index": 585}, "end": {"line": 29, "column": 32, "index": 694}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/ViewConfigIgnore", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 27, "column": 0, "index": 585}, "end": {"line": 29, "column": 32, "index": 694}}], "key": "IAMNY1s5722b4GYH12DgGSx1R70=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var _require = require(_dependencyMap[3], \"react-native/Libraries/NativeComponent/ViewConfigIgnore\"),\n    ConditionallyIgnoredEventHandlers = _require.ConditionallyIgnoredEventHandlers;\n  var nativeComponentName = 'RNCSafeAreaProvider';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNCSafeAreaProvider\",\n    directEventTypes: {\n      topInsetsChange: {\n        registrationName: \"onInsetsChange\"\n      }\n    },\n    validAttributes: {\n      ...ConditionallyIgnoredEventHandlers({\n        onInsetsChange: true\n      })\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 26, "map": [[7, 2, 5, 0], [7, 6, 5, 0, "_codegenNativeComponent"], [7, 29, 5, 0], [7, 32, 5, 0, "_interopRequireDefault"], [7, 54, 5, 0], [7, 55, 5, 0, "require"], [7, 62, 5, 0], [7, 63, 5, 0, "_dependencyMap"], [7, 77, 5, 0], [8, 2, 27, 0], [8, 6, 27, 0, "NativeComponentRegistry"], [8, 29, 29, 32], [8, 32, 27, 0, "require"], [8, 39, 29, 32], [8, 40, 29, 32, "_dependencyMap"], [8, 54, 29, 32], [8, 123, 29, 31], [8, 124, 29, 32], [9, 2, 27, 0], [9, 6, 27, 0, "_require"], [9, 14, 27, 0], [9, 17, 27, 0, "require"], [9, 24, 29, 32], [9, 25, 29, 32, "_dependencyMap"], [9, 39, 29, 32], [9, 101, 29, 31], [9, 102, 29, 32], [10, 4, 27, 0, "ConditionallyIgnoredEventHandlers"], [10, 37, 29, 32], [10, 40, 29, 32, "_require"], [10, 48, 29, 32], [10, 49, 27, 0, "ConditionallyIgnoredEventHandlers"], [10, 82, 29, 32], [11, 2, 27, 0], [11, 6, 27, 0, "nativeComponentName"], [11, 25, 29, 32], [11, 28, 27, 0], [11, 49, 29, 32], [12, 2, 27, 0], [12, 6, 27, 0, "__INTERNAL_VIEW_CONFIG"], [12, 28, 29, 32], [12, 31, 29, 32, "exports"], [12, 38, 29, 32], [12, 39, 29, 32, "__INTERNAL_VIEW_CONFIG"], [12, 61, 29, 32], [12, 64, 27, 0], [13, 4, 27, 0, "uiViewClassName"], [13, 19, 29, 32], [13, 21, 27, 0], [13, 42, 29, 32], [14, 4, 27, 0, "directEventTypes"], [14, 20, 29, 32], [14, 22, 27, 0], [15, 6, 27, 0, "topInsetsChange"], [15, 21, 29, 32], [15, 23, 27, 0], [16, 8, 27, 0, "registrationName"], [16, 24, 29, 32], [16, 26, 27, 0], [17, 6, 29, 31], [18, 4, 29, 31], [18, 5, 29, 32], [19, 4, 27, 0, "validAttributes"], [19, 19, 29, 32], [19, 21, 27, 0], [20, 6, 27, 0], [20, 9, 27, 0, "ConditionallyIgnoredEventHandlers"], [20, 42, 29, 32], [20, 43, 27, 0], [21, 8, 27, 0, "onInsetsChange"], [21, 22, 29, 32], [21, 24, 27, 0], [22, 6, 29, 31], [23, 4, 29, 31], [24, 2, 29, 31], [24, 3, 29, 32], [25, 2, 29, 32], [25, 6, 29, 32, "_default"], [25, 14, 29, 32], [25, 17, 29, 32, "exports"], [25, 24, 29, 32], [25, 25, 29, 32, "default"], [25, 32, 29, 32], [25, 35, 27, 0, "NativeComponentRegistry"], [25, 58, 29, 32], [25, 59, 27, 0, "get"], [25, 62, 29, 32], [25, 63, 27, 0, "nativeComponentName"], [25, 82, 29, 32], [25, 84, 27, 0], [25, 90, 27, 0, "__INTERNAL_VIEW_CONFIG"], [25, 112, 29, 31], [25, 113, 29, 32], [26, 0, 29, 32], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}