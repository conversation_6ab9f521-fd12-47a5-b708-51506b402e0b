{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function processFontVariant(fontVariant) {\n    if (Array.isArray(fontVariant)) {\n      return fontVariant;\n    }\n    var match = fontVariant.split(' ').filter(Boolean);\n    return match;\n  }\n  var _default = exports.default = processFontVariant;\n});", "lineCount": 16, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 15, 0], [8, 11, 15, 9, "processFontVariant"], [8, 29, 15, 27, "processFontVariant"], [8, 30, 16, 2, "fontVariant"], [8, 41, 16, 53], [8, 43, 17, 34], [9, 4, 18, 2], [9, 8, 18, 6, "Array"], [9, 13, 18, 11], [9, 14, 18, 12, "isArray"], [9, 21, 18, 19], [9, 22, 18, 20, "fontVariant"], [9, 33, 18, 31], [9, 34, 18, 32], [9, 36, 18, 34], [10, 6, 19, 4], [10, 13, 19, 11, "fontVariant"], [10, 24, 19, 22], [11, 4, 20, 2], [12, 4, 23, 2], [12, 8, 23, 8, "match"], [12, 13, 23, 45], [12, 16, 23, 48, "fontVariant"], [12, 27, 23, 59], [12, 28, 24, 5, "split"], [12, 33, 24, 10], [12, 34, 24, 11], [12, 37, 24, 14], [12, 38, 24, 15], [12, 39, 25, 5, "filter"], [12, 45, 25, 11], [12, 46, 25, 12, "Boolean"], [12, 53, 25, 19], [12, 54, 25, 20], [13, 4, 27, 2], [13, 11, 27, 9, "match"], [13, 16, 27, 14], [14, 2, 28, 0], [15, 2, 28, 1], [15, 6, 28, 1, "_default"], [15, 14, 28, 1], [15, 17, 28, 1, "exports"], [15, 24, 28, 1], [15, 25, 28, 1, "default"], [15, 32, 28, 1], [15, 35, 30, 15, "processFontVariant"], [15, 53, 30, 33], [16, 0, 30, 33], [16, 3]], "functionMap": {"names": ["<global>", "processFontVariant"], "mappings": "AAA;ACc;CDa"}}, "type": "js/module"}]}