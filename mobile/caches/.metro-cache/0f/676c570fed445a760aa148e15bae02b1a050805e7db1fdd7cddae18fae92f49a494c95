{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeUIManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 48}}], "key": "/RwmOIWIg0JN0s++XHair+76/w4=", "exportNames": ["*"]}}, {"name": "nullthrows", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 36}}], "key": "epufkdgpKN0G543QKwfSBBl0bWM=", "exportNames": ["*"]}}, {"name": "../BatchedBridge/NativeModules", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 63}}], "key": "vsf1Ls4L++VD3yF8UTrETFprAwI=", "exportNames": ["*"]}}, {"name": "../Utilities/defineLazyObjectProperty", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 50}}], "key": "iAPGUMITE/2KH0DH4/f0/lVJtsQ=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 49}}], "key": "4a+BOpVYP2jviYQTOV6MRNF0tRc=", "exportNames": ["*"]}}, {"name": "./UIManagerProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 28}, "end": {"line": 21, "column": 60}}], "key": "Zp8Lf7vF8AiWk3QkGb5owtjbark=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeUIManager = _interopRequireDefault(require(_dependencyMap[1], \"./NativeUIManager\"));\n  var _nullthrows = _interopRequireDefault(require(_dependencyMap[2], \"nullthrows\"));\n  var NativeModules = require(_dependencyMap[3], \"../BatchedBridge/NativeModules\").default;\n  var defineLazyObjectProperty = require(_dependencyMap[4], \"../Utilities/defineLazyObjectProperty\").default;\n  var Platform = require(_dependencyMap[5], \"../Utilities/Platform\").default;\n  var UIManagerProperties = require(_dependencyMap[6], \"./UIManagerProperties\").default;\n  var viewManagerConfigs = {};\n  var triedLoadingConfig = new Set();\n  var NativeUIManagerConstants = {};\n  var isNativeUIManagerConstantsSet = false;\n  function getConstants() {\n    if (!isNativeUIManagerConstantsSet) {\n      NativeUIManagerConstants = _NativeUIManager.default.getConstants();\n      isNativeUIManagerConstantsSet = true;\n    }\n    return NativeUIManagerConstants;\n  }\n  function getViewManagerConfig(viewManagerName) {\n    if (viewManagerConfigs[viewManagerName] === undefined && _NativeUIManager.default.getConstantsForViewManager) {\n      try {\n        viewManagerConfigs[viewManagerName] = _NativeUIManager.default.getConstantsForViewManager(viewManagerName);\n      } catch (e) {\n        console.error(\"NativeUIManager.getConstantsForViewManager('\" + viewManagerName + \"') threw an exception.\", e);\n        viewManagerConfigs[viewManagerName] = null;\n      }\n    }\n    var config = viewManagerConfigs[viewManagerName];\n    if (config) {\n      return config;\n    }\n    if (!global.nativeCallSyncHook) {\n      return config;\n    }\n    if (_NativeUIManager.default.lazilyLoadView && !triedLoadingConfig.has(viewManagerName)) {\n      var result = (0, _nullthrows.default)(_NativeUIManager.default.lazilyLoadView)(viewManagerName);\n      triedLoadingConfig.add(viewManagerName);\n      if (result != null && result.viewConfig != null) {\n        getConstants()[viewManagerName] = result.viewConfig;\n        lazifyViewManagerConfig(viewManagerName);\n      }\n    }\n    return viewManagerConfigs[viewManagerName];\n  }\n  var UIManagerJS = {\n    ..._NativeUIManager.default,\n    createView(reactTag, viewName, rootTag, props) {\n      if (Platform.OS === 'ios' && viewManagerConfigs[viewName] === undefined) {\n        getViewManagerConfig(viewName);\n      }\n      _NativeUIManager.default.createView(reactTag, viewName, rootTag, props);\n    },\n    getConstants() {\n      return getConstants();\n    },\n    getViewManagerConfig(viewManagerName) {\n      return getViewManagerConfig(viewManagerName);\n    },\n    hasViewManagerConfig(viewManagerName) {\n      return getViewManagerConfig(viewManagerName) != null;\n    }\n  };\n  _NativeUIManager.default.getViewManagerConfig = UIManagerJS.getViewManagerConfig;\n  function lazifyViewManagerConfig(viewName) {\n    var viewConfig = getConstants()[viewName];\n    viewManagerConfigs[viewName] = viewConfig;\n    if (viewConfig.Manager) {\n      defineLazyObjectProperty(viewConfig, 'Constants', {\n        get: () => {\n          var viewManager = NativeModules[viewConfig.Manager];\n          var constants = {};\n          viewManager && Object.keys(viewManager).forEach(key => {\n            var value = viewManager[key];\n            if (typeof value !== 'function') {\n              constants[key] = value;\n            }\n          });\n          return constants;\n        }\n      });\n      defineLazyObjectProperty(viewConfig, 'Commands', {\n        get: () => {\n          var viewManager = NativeModules[viewConfig.Manager];\n          var commands = {};\n          var index = 0;\n          viewManager && Object.keys(viewManager).forEach(key => {\n            var value = viewManager[key];\n            if (typeof value === 'function') {\n              commands[key] = index++;\n            }\n          });\n          return commands;\n        }\n      });\n    }\n  }\n  if (Platform.OS === 'ios') {\n    Object.keys(getConstants()).forEach(viewName => {\n      lazifyViewManagerConfig(viewName);\n    });\n  } else if (getConstants().ViewManagerNames) {\n    _NativeUIManager.default.getConstants().ViewManagerNames.forEach(viewManagerName => {\n      defineLazyObjectProperty(_NativeUIManager.default, viewManagerName, {\n        get: () => (0, _nullthrows.default)(_NativeUIManager.default.getConstantsForViewManager)(viewManagerName)\n      });\n    });\n  }\n  if (!global.nativeCallSyncHook) {\n    Object.keys(getConstants()).forEach(viewManagerName => {\n      if (!UIManagerProperties.includes(viewManagerName)) {\n        if (!viewManagerConfigs[viewManagerName]) {\n          viewManagerConfigs[viewManagerName] = getConstants()[viewManagerName];\n        }\n        defineLazyObjectProperty(_NativeUIManager.default, viewManagerName, {\n          get: () => {\n            console.warn(`Accessing view manager configs directly off UIManager via UIManager['${viewManagerName}'] ` + `is no longer supported. Use UIManager.getViewManagerConfig('${viewManagerName}') instead.`);\n            return UIManagerJS.getViewManagerConfig(viewManagerName);\n          }\n        });\n      }\n    });\n  }\n  var _default = exports.default = UIManagerJS;\n});", "lineCount": 129, "map": [[7, 2, 14, 0], [7, 6, 14, 0, "_NativeUIManager"], [7, 22, 14, 0], [7, 25, 14, 0, "_interopRequireDefault"], [7, 47, 14, 0], [7, 48, 14, 0, "require"], [7, 55, 14, 0], [7, 56, 14, 0, "_dependencyMap"], [7, 70, 14, 0], [8, 2, 15, 0], [8, 6, 15, 0, "_nullthrows"], [8, 17, 15, 0], [8, 20, 15, 0, "_interopRequireDefault"], [8, 42, 15, 0], [8, 43, 15, 0, "require"], [8, 50, 15, 0], [8, 51, 15, 0, "_dependencyMap"], [8, 65, 15, 0], [9, 2, 17, 0], [9, 6, 17, 6, "NativeModules"], [9, 19, 17, 19], [9, 22, 17, 22, "require"], [9, 29, 17, 29], [9, 30, 17, 29, "_dependencyMap"], [9, 44, 17, 29], [9, 81, 17, 62], [9, 82, 17, 63], [9, 83, 17, 64, "default"], [9, 90, 17, 71], [10, 2, 18, 0], [10, 6, 18, 6, "defineLazyObjectProperty"], [10, 30, 18, 30], [10, 33, 19, 2, "require"], [10, 40, 19, 9], [10, 41, 19, 9, "_dependencyMap"], [10, 55, 19, 9], [10, 99, 19, 49], [10, 100, 19, 50], [10, 101, 19, 51, "default"], [10, 108, 19, 58], [11, 2, 20, 0], [11, 6, 20, 6, "Platform"], [11, 14, 20, 14], [11, 17, 20, 17, "require"], [11, 24, 20, 24], [11, 25, 20, 24, "_dependencyMap"], [11, 39, 20, 24], [11, 67, 20, 48], [11, 68, 20, 49], [11, 69, 20, 50, "default"], [11, 76, 20, 57], [12, 2, 21, 0], [12, 6, 21, 6, "UIManagerProperties"], [12, 25, 21, 25], [12, 28, 21, 28, "require"], [12, 35, 21, 35], [12, 36, 21, 35, "_dependencyMap"], [12, 50, 21, 35], [12, 78, 21, 59], [12, 79, 21, 60], [12, 80, 21, 61, "default"], [12, 87, 21, 68], [13, 2, 23, 0], [13, 6, 23, 6, "viewManagerConfigs"], [13, 24, 23, 48], [13, 27, 23, 51], [13, 28, 23, 52], [13, 29, 23, 53], [14, 2, 25, 0], [14, 6, 25, 6, "triedLoadingConfig"], [14, 24, 25, 24], [14, 27, 25, 27], [14, 31, 25, 31, "Set"], [14, 34, 25, 34], [14, 35, 25, 43], [14, 36, 25, 44], [15, 2, 27, 0], [15, 6, 27, 4, "NativeUIManagerConstants"], [15, 30, 27, 28], [15, 33, 27, 31], [15, 34, 27, 32], [15, 35, 27, 33], [16, 2, 28, 0], [16, 6, 28, 4, "isNativeUIManagerConstantsSet"], [16, 35, 28, 33], [16, 38, 28, 36], [16, 43, 28, 41], [17, 2, 29, 0], [17, 11, 29, 9, "getConstants"], [17, 23, 29, 21, "getConstants"], [17, 24, 29, 21], [17, 26, 29, 32], [18, 4, 30, 2], [18, 8, 30, 6], [18, 9, 30, 7, "isNativeUIManagerConstantsSet"], [18, 38, 30, 36], [18, 40, 30, 38], [19, 6, 31, 4, "NativeUIManagerConstants"], [19, 30, 31, 28], [19, 33, 31, 31, "NativeUIManager"], [19, 57, 31, 46], [19, 58, 31, 47, "getConstants"], [19, 70, 31, 59], [19, 71, 31, 60], [19, 72, 31, 61], [20, 6, 32, 4, "isNativeUIManagerConstantsSet"], [20, 35, 32, 33], [20, 38, 32, 36], [20, 42, 32, 40], [21, 4, 33, 2], [22, 4, 34, 2], [22, 11, 34, 9, "NativeUIManagerConstants"], [22, 35, 34, 33], [23, 2, 35, 0], [24, 2, 37, 0], [24, 11, 37, 9, "getViewManagerConfig"], [24, 31, 37, 29, "getViewManagerConfig"], [24, 32, 37, 30, "viewManagerName"], [24, 47, 37, 53], [24, 49, 37, 60], [25, 4, 38, 2], [25, 8, 39, 4, "viewManagerConfigs"], [25, 26, 39, 22], [25, 27, 39, 23, "viewManagerName"], [25, 42, 39, 38], [25, 43, 39, 39], [25, 48, 39, 44, "undefined"], [25, 57, 39, 53], [25, 61, 40, 4, "NativeUIManager"], [25, 85, 40, 19], [25, 86, 40, 20, "getConstantsForViewManager"], [25, 112, 40, 46], [25, 114, 41, 4], [26, 6, 42, 4], [26, 10, 42, 8], [27, 8, 43, 6, "viewManagerConfigs"], [27, 26, 43, 24], [27, 27, 43, 25, "viewManagerName"], [27, 42, 43, 40], [27, 43, 43, 41], [27, 46, 44, 8, "NativeUIManager"], [27, 70, 44, 23], [27, 71, 44, 24, "getConstantsForViewManager"], [27, 97, 44, 50], [27, 98, 44, 51, "viewManagerName"], [27, 113, 44, 66], [27, 114, 44, 67], [28, 6, 45, 4], [28, 7, 45, 5], [28, 8, 45, 6], [28, 15, 45, 13, "e"], [28, 16, 45, 14], [28, 18, 45, 16], [29, 8, 46, 6, "console"], [29, 15, 46, 13], [29, 16, 46, 14, "error"], [29, 21, 46, 19], [29, 22, 47, 8], [29, 68, 47, 54], [29, 71, 48, 10, "viewManagerName"], [29, 86, 48, 25], [29, 89, 49, 10], [29, 113, 49, 34], [29, 115, 50, 8, "e"], [29, 116, 51, 6], [29, 117, 51, 7], [30, 8, 52, 6, "viewManagerConfigs"], [30, 26, 52, 24], [30, 27, 52, 25, "viewManagerName"], [30, 42, 52, 40], [30, 43, 52, 41], [30, 46, 52, 44], [30, 50, 52, 48], [31, 6, 53, 4], [32, 4, 54, 2], [33, 4, 56, 2], [33, 8, 56, 8, "config"], [33, 14, 56, 14], [33, 17, 56, 17, "viewManagerConfigs"], [33, 35, 56, 35], [33, 36, 56, 36, "viewManagerName"], [33, 51, 56, 51], [33, 52, 56, 52], [34, 4, 57, 2], [34, 8, 57, 6, "config"], [34, 14, 57, 12], [34, 16, 57, 14], [35, 6, 58, 4], [35, 13, 58, 11, "config"], [35, 19, 58, 17], [36, 4, 59, 2], [37, 4, 63, 2], [37, 8, 63, 6], [37, 9, 63, 7, "global"], [37, 15, 63, 13], [37, 16, 63, 14, "nativeCallSyncHook"], [37, 34, 63, 32], [37, 36, 63, 34], [38, 6, 64, 4], [38, 13, 64, 11, "config"], [38, 19, 64, 17], [39, 4, 65, 2], [40, 4, 67, 2], [40, 8, 68, 4, "NativeUIManager"], [40, 32, 68, 19], [40, 33, 68, 20, "lazilyLoadView"], [40, 47, 68, 34], [40, 51, 69, 4], [40, 52, 69, 5, "triedLoadingConfig"], [40, 70, 69, 23], [40, 71, 69, 24, "has"], [40, 74, 69, 27], [40, 75, 69, 28, "viewManagerName"], [40, 90, 69, 43], [40, 91, 69, 44], [40, 93, 70, 4], [41, 6, 71, 4], [41, 10, 71, 10, "result"], [41, 16, 71, 16], [41, 19, 71, 19], [41, 23, 71, 19, "nullthrows"], [41, 42, 71, 29], [41, 44, 71, 30, "NativeUIManager"], [41, 68, 71, 45], [41, 69, 71, 46, "lazilyLoadView"], [41, 83, 71, 60], [41, 84, 71, 61], [41, 85, 71, 62, "viewManagerName"], [41, 100, 71, 77], [41, 101, 71, 78], [42, 6, 72, 4, "triedLoadingConfig"], [42, 24, 72, 22], [42, 25, 72, 23, "add"], [42, 28, 72, 26], [42, 29, 72, 27, "viewManagerName"], [42, 44, 72, 42], [42, 45, 72, 43], [43, 6, 73, 4], [43, 10, 73, 8, "result"], [43, 16, 73, 14], [43, 20, 73, 18], [43, 24, 73, 22], [43, 28, 73, 26, "result"], [43, 34, 73, 32], [43, 35, 73, 33, "viewConfig"], [43, 45, 73, 43], [43, 49, 73, 47], [43, 53, 73, 51], [43, 55, 73, 53], [44, 8, 74, 6, "getConstants"], [44, 20, 74, 18], [44, 21, 74, 19], [44, 22, 74, 20], [44, 23, 74, 21, "viewManagerName"], [44, 38, 74, 36], [44, 39, 74, 37], [44, 42, 74, 40, "result"], [44, 48, 74, 46], [44, 49, 74, 47, "viewConfig"], [44, 59, 74, 57], [45, 8, 75, 6, "lazifyViewManagerConfig"], [45, 31, 75, 29], [45, 32, 75, 30, "viewManagerName"], [45, 47, 75, 45], [45, 48, 75, 46], [46, 6, 76, 4], [47, 4, 77, 2], [48, 4, 79, 2], [48, 11, 79, 9, "viewManagerConfigs"], [48, 29, 79, 27], [48, 30, 79, 28, "viewManagerName"], [48, 45, 79, 43], [48, 46, 79, 44], [49, 2, 80, 0], [50, 2, 83, 0], [50, 6, 83, 6, "UIManagerJS"], [50, 17, 83, 39], [50, 20, 83, 42], [51, 4, 84, 2], [51, 7, 84, 5, "NativeUIManager"], [51, 31, 84, 20], [52, 4, 85, 2, "createView"], [52, 14, 85, 12, "createView"], [52, 15, 86, 4, "reactTag"], [52, 23, 86, 20], [52, 25, 87, 4, "viewName"], [52, 33, 87, 20], [52, 35, 88, 4, "rootTag"], [52, 42, 88, 20], [52, 44, 89, 4, "props"], [52, 49, 89, 17], [52, 51, 90, 10], [53, 6, 91, 4], [53, 10, 91, 8, "Platform"], [53, 18, 91, 16], [53, 19, 91, 17, "OS"], [53, 21, 91, 19], [53, 26, 91, 24], [53, 31, 91, 29], [53, 35, 91, 33, "viewManagerConfigs"], [53, 53, 91, 51], [53, 54, 91, 52, "viewName"], [53, 62, 91, 60], [53, 63, 91, 61], [53, 68, 91, 66, "undefined"], [53, 77, 91, 75], [53, 79, 91, 77], [54, 8, 94, 6, "getViewManagerConfig"], [54, 28, 94, 26], [54, 29, 94, 27, "viewName"], [54, 37, 94, 35], [54, 38, 94, 36], [55, 6, 95, 4], [56, 6, 97, 4, "NativeUIManager"], [56, 30, 97, 19], [56, 31, 97, 20, "createView"], [56, 41, 97, 30], [56, 42, 97, 31, "reactTag"], [56, 50, 97, 39], [56, 52, 97, 41, "viewName"], [56, 60, 97, 49], [56, 62, 97, 51, "rootTag"], [56, 69, 97, 58], [56, 71, 97, 60, "props"], [56, 76, 97, 65], [56, 77, 97, 66], [57, 4, 98, 2], [57, 5, 98, 3], [58, 4, 99, 2, "getConstants"], [58, 16, 99, 14, "getConstants"], [58, 17, 99, 14], [58, 19, 99, 25], [59, 6, 100, 4], [59, 13, 100, 11, "getConstants"], [59, 25, 100, 23], [59, 26, 100, 24], [59, 27, 100, 25], [60, 4, 101, 2], [60, 5, 101, 3], [61, 4, 102, 2, "getViewManagerConfig"], [61, 24, 102, 22, "getViewManagerConfig"], [61, 25, 102, 23, "viewManagerName"], [61, 40, 102, 46], [61, 42, 102, 53], [62, 6, 103, 4], [62, 13, 103, 11, "getViewManagerConfig"], [62, 33, 103, 31], [62, 34, 103, 32, "viewManagerName"], [62, 49, 103, 47], [62, 50, 103, 48], [63, 4, 104, 2], [63, 5, 104, 3], [64, 4, 105, 2, "hasViewManagerConfig"], [64, 24, 105, 22, "hasViewManagerConfig"], [64, 25, 105, 23, "viewManagerName"], [64, 40, 105, 46], [64, 42, 105, 57], [65, 6, 106, 4], [65, 13, 106, 11, "getViewManagerConfig"], [65, 33, 106, 31], [65, 34, 106, 32, "viewManagerName"], [65, 49, 106, 47], [65, 50, 106, 48], [65, 54, 106, 52], [65, 58, 106, 56], [66, 4, 107, 2], [67, 2, 108, 0], [67, 3, 108, 1], [68, 2, 115, 0, "NativeUIManager"], [68, 26, 115, 15], [68, 27, 115, 16, "getViewManagerConfig"], [68, 47, 115, 36], [68, 50, 115, 39, "UIManagerJS"], [68, 61, 115, 50], [68, 62, 115, 51, "getViewManagerConfig"], [68, 82, 115, 71], [69, 2, 117, 0], [69, 11, 117, 9, "lazifyViewManagerConfig"], [69, 34, 117, 32, "lazifyViewManagerConfig"], [69, 35, 117, 33, "viewName"], [69, 43, 117, 49], [69, 45, 117, 51], [70, 4, 118, 2], [70, 8, 118, 8, "viewConfig"], [70, 18, 118, 18], [70, 21, 118, 21, "getConstants"], [70, 33, 118, 33], [70, 34, 118, 34], [70, 35, 118, 35], [70, 36, 118, 36, "viewName"], [70, 44, 118, 44], [70, 45, 118, 45], [71, 4, 119, 2, "viewManagerConfigs"], [71, 22, 119, 20], [71, 23, 119, 21, "viewName"], [71, 31, 119, 29], [71, 32, 119, 30], [71, 35, 119, 33, "viewConfig"], [71, 45, 119, 43], [72, 4, 120, 2], [72, 8, 120, 6, "viewConfig"], [72, 18, 120, 16], [72, 19, 120, 17, "Manager"], [72, 26, 120, 24], [72, 28, 120, 26], [73, 6, 121, 4, "defineLazyObjectProperty"], [73, 30, 121, 28], [73, 31, 121, 29, "viewConfig"], [73, 41, 121, 39], [73, 43, 121, 41], [73, 54, 121, 52], [73, 56, 121, 54], [74, 8, 122, 6, "get"], [74, 11, 122, 9], [74, 13, 122, 11, "get"], [74, 14, 122, 11], [74, 19, 122, 17], [75, 10, 123, 8], [75, 14, 123, 14, "viewManager"], [75, 25, 123, 25], [75, 28, 123, 28, "NativeModules"], [75, 41, 123, 41], [75, 42, 123, 42, "viewConfig"], [75, 52, 123, 52], [75, 53, 123, 53, "Manager"], [75, 60, 123, 60], [75, 61, 123, 61], [76, 10, 124, 8], [76, 14, 124, 14, "constants"], [76, 23, 124, 42], [76, 26, 124, 45], [76, 27, 124, 46], [76, 28, 124, 47], [77, 10, 125, 8, "viewManager"], [77, 21, 125, 19], [77, 25, 126, 10, "Object"], [77, 31, 126, 16], [77, 32, 126, 17, "keys"], [77, 36, 126, 21], [77, 37, 126, 22, "viewManager"], [77, 48, 126, 33], [77, 49, 126, 34], [77, 50, 126, 35, "for<PERSON>ach"], [77, 57, 126, 42], [77, 58, 126, 43, "key"], [77, 61, 126, 46], [77, 65, 126, 50], [78, 12, 127, 12], [78, 16, 127, 18, "value"], [78, 21, 127, 23], [78, 24, 127, 26, "viewManager"], [78, 35, 127, 37], [78, 36, 127, 38, "key"], [78, 39, 127, 41], [78, 40, 127, 42], [79, 12, 128, 12], [79, 16, 128, 16], [79, 23, 128, 23, "value"], [79, 28, 128, 28], [79, 33, 128, 33], [79, 43, 128, 43], [79, 45, 128, 45], [80, 14, 129, 14, "constants"], [80, 23, 129, 23], [80, 24, 129, 24, "key"], [80, 27, 129, 27], [80, 28, 129, 28], [80, 31, 129, 31, "value"], [80, 36, 129, 36], [81, 12, 130, 12], [82, 10, 131, 10], [82, 11, 131, 11], [82, 12, 131, 12], [83, 10, 132, 8], [83, 17, 132, 15, "constants"], [83, 26, 132, 24], [84, 8, 133, 6], [85, 6, 134, 4], [85, 7, 134, 5], [85, 8, 134, 6], [86, 6, 135, 4, "defineLazyObjectProperty"], [86, 30, 135, 28], [86, 31, 135, 29, "viewConfig"], [86, 41, 135, 39], [86, 43, 135, 41], [86, 53, 135, 51], [86, 55, 135, 53], [87, 8, 136, 6, "get"], [87, 11, 136, 9], [87, 13, 136, 11, "get"], [87, 14, 136, 11], [87, 19, 136, 17], [88, 10, 137, 8], [88, 14, 137, 14, "viewManager"], [88, 25, 137, 25], [88, 28, 137, 28, "NativeModules"], [88, 41, 137, 41], [88, 42, 137, 42, "viewConfig"], [88, 52, 137, 52], [88, 53, 137, 53, "Manager"], [88, 60, 137, 60], [88, 61, 137, 61], [89, 10, 138, 8], [89, 14, 138, 14, "commands"], [89, 22, 138, 42], [89, 25, 138, 45], [89, 26, 138, 46], [89, 27, 138, 47], [90, 10, 139, 8], [90, 14, 139, 12, "index"], [90, 19, 139, 17], [90, 22, 139, 20], [90, 23, 139, 21], [91, 10, 140, 8, "viewManager"], [91, 21, 140, 19], [91, 25, 141, 10, "Object"], [91, 31, 141, 16], [91, 32, 141, 17, "keys"], [91, 36, 141, 21], [91, 37, 141, 22, "viewManager"], [91, 48, 141, 33], [91, 49, 141, 34], [91, 50, 141, 35, "for<PERSON>ach"], [91, 57, 141, 42], [91, 58, 141, 43, "key"], [91, 61, 141, 46], [91, 65, 141, 50], [92, 12, 142, 12], [92, 16, 142, 18, "value"], [92, 21, 142, 23], [92, 24, 142, 26, "viewManager"], [92, 35, 142, 37], [92, 36, 142, 38, "key"], [92, 39, 142, 41], [92, 40, 142, 42], [93, 12, 143, 12], [93, 16, 143, 16], [93, 23, 143, 23, "value"], [93, 28, 143, 28], [93, 33, 143, 33], [93, 43, 143, 43], [93, 45, 143, 45], [94, 14, 144, 14, "commands"], [94, 22, 144, 22], [94, 23, 144, 23, "key"], [94, 26, 144, 26], [94, 27, 144, 27], [94, 30, 144, 30, "index"], [94, 35, 144, 35], [94, 37, 144, 37], [95, 12, 145, 12], [96, 10, 146, 10], [96, 11, 146, 11], [96, 12, 146, 12], [97, 10, 147, 8], [97, 17, 147, 15, "commands"], [97, 25, 147, 23], [98, 8, 148, 6], [99, 6, 149, 4], [99, 7, 149, 5], [99, 8, 149, 6], [100, 4, 150, 2], [101, 2, 151, 0], [102, 2, 158, 0], [102, 6, 158, 4, "Platform"], [102, 14, 158, 12], [102, 15, 158, 13, "OS"], [102, 17, 158, 15], [102, 22, 158, 20], [102, 27, 158, 25], [102, 29, 158, 27], [103, 4, 159, 2, "Object"], [103, 10, 159, 8], [103, 11, 159, 9, "keys"], [103, 15, 159, 13], [103, 16, 159, 14, "getConstants"], [103, 28, 159, 26], [103, 29, 159, 27], [103, 30, 159, 28], [103, 31, 159, 29], [103, 32, 159, 30, "for<PERSON>ach"], [103, 39, 159, 37], [103, 40, 159, 38, "viewName"], [103, 48, 159, 46], [103, 52, 159, 50], [104, 6, 160, 4, "lazifyViewManagerConfig"], [104, 29, 160, 27], [104, 30, 160, 28, "viewName"], [104, 38, 160, 36], [104, 39, 160, 37], [105, 4, 161, 2], [105, 5, 161, 3], [105, 6, 161, 4], [106, 2, 162, 0], [106, 3, 162, 1], [106, 9, 162, 7], [106, 13, 162, 11, "getConstants"], [106, 25, 162, 23], [106, 26, 162, 24], [106, 27, 162, 25], [106, 28, 162, 26, "ViewManagerNames"], [106, 44, 162, 42], [106, 46, 162, 44], [107, 4, 163, 2, "NativeUIManager"], [107, 28, 163, 17], [107, 29, 163, 18, "getConstants"], [107, 41, 163, 30], [107, 42, 163, 31], [107, 43, 163, 32], [107, 44, 163, 33, "ViewManagerNames"], [107, 60, 163, 49], [107, 61, 163, 50, "for<PERSON>ach"], [107, 68, 163, 57], [107, 69, 163, 58, "viewManagerName"], [107, 84, 163, 73], [107, 88, 163, 77], [108, 6, 164, 4, "defineLazyObjectProperty"], [108, 30, 164, 28], [108, 31, 164, 29, "NativeUIManager"], [108, 55, 164, 44], [108, 57, 164, 46, "viewManagerName"], [108, 72, 164, 61], [108, 74, 164, 63], [109, 8, 165, 6, "get"], [109, 11, 165, 9], [109, 13, 165, 11, "get"], [109, 14, 165, 11], [109, 19, 166, 8], [109, 23, 166, 8, "nullthrows"], [109, 42, 166, 18], [109, 44, 166, 19, "NativeUIManager"], [109, 68, 166, 34], [109, 69, 166, 35, "getConstantsForViewManager"], [109, 95, 166, 61], [109, 96, 166, 62], [109, 97, 166, 63, "viewManagerName"], [109, 112, 166, 78], [110, 6, 167, 4], [110, 7, 167, 5], [110, 8, 167, 6], [111, 4, 168, 2], [111, 5, 168, 3], [111, 6, 168, 4], [112, 2, 169, 0], [113, 2, 171, 0], [113, 6, 171, 4], [113, 7, 171, 5, "global"], [113, 13, 171, 11], [113, 14, 171, 12, "nativeCallSyncHook"], [113, 32, 171, 30], [113, 34, 171, 32], [114, 4, 172, 2, "Object"], [114, 10, 172, 8], [114, 11, 172, 9, "keys"], [114, 15, 172, 13], [114, 16, 172, 14, "getConstants"], [114, 28, 172, 26], [114, 29, 172, 27], [114, 30, 172, 28], [114, 31, 172, 29], [114, 32, 172, 30, "for<PERSON>ach"], [114, 39, 172, 37], [114, 40, 172, 38, "viewManagerName"], [114, 55, 172, 53], [114, 59, 172, 57], [115, 6, 173, 4], [115, 10, 173, 8], [115, 11, 173, 9, "UIManagerProperties"], [115, 30, 173, 28], [115, 31, 173, 29, "includes"], [115, 39, 173, 37], [115, 40, 173, 38, "viewManagerName"], [115, 55, 173, 53], [115, 56, 173, 54], [115, 58, 173, 56], [116, 8, 174, 6], [116, 12, 174, 10], [116, 13, 174, 11, "viewManagerConfigs"], [116, 31, 174, 29], [116, 32, 174, 30, "viewManagerName"], [116, 47, 174, 45], [116, 48, 174, 46], [116, 50, 174, 48], [117, 10, 175, 8, "viewManagerConfigs"], [117, 28, 175, 26], [117, 29, 175, 27, "viewManagerName"], [117, 44, 175, 42], [117, 45, 175, 43], [117, 48, 175, 46, "getConstants"], [117, 60, 175, 58], [117, 61, 175, 59], [117, 62, 175, 60], [117, 63, 175, 61, "viewManagerName"], [117, 78, 175, 76], [117, 79, 175, 77], [118, 8, 176, 6], [119, 8, 177, 6, "defineLazyObjectProperty"], [119, 32, 177, 30], [119, 33, 177, 31, "NativeUIManager"], [119, 57, 177, 46], [119, 59, 177, 48, "viewManagerName"], [119, 74, 177, 63], [119, 76, 177, 65], [120, 10, 178, 8, "get"], [120, 13, 178, 11], [120, 15, 178, 13, "get"], [120, 16, 178, 13], [120, 21, 178, 19], [121, 12, 179, 10, "console"], [121, 19, 179, 17], [121, 20, 179, 18, "warn"], [121, 24, 179, 22], [121, 25, 180, 12], [121, 97, 180, 84, "viewManagerName"], [121, 112, 180, 99], [121, 117, 180, 104], [121, 120, 181, 14], [121, 183, 181, 77, "viewManagerName"], [121, 198, 181, 92], [121, 211, 182, 10], [121, 212, 182, 11], [122, 12, 184, 10], [122, 19, 184, 17, "UIManagerJS"], [122, 30, 184, 28], [122, 31, 184, 29, "getViewManagerConfig"], [122, 51, 184, 49], [122, 52, 184, 50, "viewManagerName"], [122, 67, 184, 65], [122, 68, 184, 66], [123, 10, 185, 8], [124, 8, 186, 6], [124, 9, 186, 7], [124, 10, 186, 8], [125, 6, 187, 4], [126, 4, 188, 2], [126, 5, 188, 3], [126, 6, 188, 4], [127, 2, 189, 0], [128, 2, 189, 1], [128, 6, 189, 1, "_default"], [128, 14, 189, 1], [128, 17, 189, 1, "exports"], [128, 24, 189, 1], [128, 25, 189, 1, "default"], [128, 32, 189, 1], [128, 35, 191, 15, "UIManagerJS"], [128, 46, 191, 26], [129, 0, 191, 26], [129, 3]], "functionMap": {"names": ["<global>", "getConstants", "getViewManagerConfig", "UIManagerJS.createView", "UIManagerJS.getConstants", "UIManagerJS.getViewManagerConfig", "UIManagerJS.hasViewManagerConfig", "lazifyViewManagerConfig", "defineLazyObjectProperty$argument_2.get", "Object.keys.forEach$argument_0", "NativeUIManager.getConstants.ViewManagerNames.forEach$argument_0"], "mappings": "AAA;AC4B;CDM;AEE;CF2C;EGK;GHa;EIC;GJE;EKC;GLE;EMC;GNE;AOU;WCK;2CCI;WDK;ODE;WCG;2CCK;WDK;ODE;CPG;sCSQ;GTE;0DUE;WFE;+EEC;GVE;sCSI;aDM;SCO;GTG"}}, "type": "js/module"}]}