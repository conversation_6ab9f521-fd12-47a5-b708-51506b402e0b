{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 65, "index": 110}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.nativeViewProps = exports.nativeViewHandlerName = exports.nativeViewGestureHandlerProps = exports.NativeViewGestureHandler = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  const nativeViewGestureHandlerProps = exports.nativeViewGestureHandlerProps = ['shouldActivateOnStart', 'disallowInterruption'];\n  const nativeViewProps = exports.nativeViewProps = [..._gestureHandlerCommon.baseGestureHandlerProps, ...nativeViewGestureHandlerProps];\n  const nativeViewHandlerName = exports.nativeViewHandlerName = 'NativeViewGestureHandler';\n  /**\n   * @deprecated NativeViewGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Native()` instead.\n   */\n\n  /**\n   * @deprecated NativeViewGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Native()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  const NativeViewGestureHandler = exports.NativeViewGestureHandler = (0, _createHandler.default)({\n    name: nativeViewHandlerName,\n    allowedProps: nativeViewProps,\n    config: {}\n  });\n});", "lineCount": 25, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_createHandler"], [7, 20, 1, 0], [7, 23, 1, 0, "_interopRequireDefault"], [7, 45, 1, 0], [7, 46, 1, 0, "require"], [7, 53, 1, 0], [7, 54, 1, 0, "_dependencyMap"], [7, 68, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 2, 0], [8, 30, 2, 0, "require"], [8, 37, 2, 0], [8, 38, 2, 0, "_dependencyMap"], [8, 52, 2, 0], [9, 2, 3, 7], [9, 8, 3, 13, "nativeViewGestureHandlerProps"], [9, 37, 3, 42], [9, 40, 3, 42, "exports"], [9, 47, 3, 42], [9, 48, 3, 42, "nativeViewGestureHandlerProps"], [9, 77, 3, 42], [9, 80, 3, 45], [9, 81, 3, 46], [9, 104, 3, 69], [9, 106, 3, 71], [9, 128, 3, 93], [9, 129, 3, 94], [10, 2, 4, 7], [10, 8, 4, 13, "nativeViewProps"], [10, 23, 4, 28], [10, 26, 4, 28, "exports"], [10, 33, 4, 28], [10, 34, 4, 28, "nativeViewProps"], [10, 49, 4, 28], [10, 52, 4, 31], [10, 53, 4, 32], [10, 56, 4, 35, "baseGestureHandlerProps"], [10, 101, 4, 58], [10, 103, 4, 60], [10, 106, 4, 63, "nativeViewGestureHandlerProps"], [10, 135, 4, 92], [10, 136, 4, 93], [11, 2, 5, 7], [11, 8, 5, 13, "nativeViewHandlerName"], [11, 29, 5, 34], [11, 32, 5, 34, "exports"], [11, 39, 5, 34], [11, 40, 5, 34, "nativeViewHandlerName"], [11, 61, 5, 34], [11, 64, 5, 37], [11, 90, 5, 63], [12, 2, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [16, 2, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 2, 13, 0], [20, 2, 14, 7], [20, 8, 14, 13, "NativeViewGestureHandler"], [20, 32, 14, 37], [20, 35, 14, 37, "exports"], [20, 42, 14, 37], [20, 43, 14, 37, "NativeViewGestureHandler"], [20, 67, 14, 37], [20, 70, 14, 40], [20, 74, 14, 40, "createHandler"], [20, 96, 14, 53], [20, 98, 14, 54], [21, 4, 15, 2, "name"], [21, 8, 15, 6], [21, 10, 15, 8, "nativeViewHandlerName"], [21, 31, 15, 29], [22, 4, 16, 2, "allowedProps"], [22, 16, 16, 14], [22, 18, 16, 16, "nativeViewProps"], [22, 33, 16, 31], [23, 4, 17, 2, "config"], [23, 10, 17, 8], [23, 12, 17, 10], [23, 13, 17, 11], [24, 2, 18, 0], [24, 3, 18, 1], [24, 4, 18, 2], [25, 0, 18, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}