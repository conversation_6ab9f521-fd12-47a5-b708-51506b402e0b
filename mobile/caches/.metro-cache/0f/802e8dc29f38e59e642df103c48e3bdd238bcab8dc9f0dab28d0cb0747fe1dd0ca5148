{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/readOnlyError", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "tW9vjPH6bEH6zBBMaoehmzXhDcQ=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "./EventTiming", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 42}}], "key": "Rer5zYKtklTjjy8E2Iy2V8Wu9H8=", "exportNames": ["*"]}}, {"name": "./internals/RawPerformanceEntry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 24, "column": 41}}], "key": "+OkmVSJYD3nZm2fYWtEVYeUP3sE=", "exportNames": ["*"]}}, {"name": "./internals/Utilities", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 62}}], "key": "TjNx4kPKXttVwQPaMsi6cYyHSX0=", "exportNames": ["*"]}}, {"name": "./MemoryInfo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 38}}], "key": "rMYYUfX6fuCxd3kB2OXLrWJYWlw=", "exportNames": ["*"]}}, {"name": "./ReactNativeStartupTiming", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 66}}], "key": "4QRKRIr6zf2YAst70KCVpL3zHvE=", "exportNames": ["*"]}}, {"name": "./specs/NativePerformance", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 58}}], "key": "6o1nOxWMCiRx5/kTKp0IyARkOoo=", "exportNames": ["*"]}}, {"name": "./UserTiming", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 65}}], "key": "8iyXwyHll+TEw55777ayfrradVQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _readOnlyError2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/readOnlyError\"));\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/slicedToArray\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/createClass\"));\n  var _EventTiming = require(_dependencyMap[5], \"./EventTiming\");\n  var _RawPerformanceEntry = require(_dependencyMap[6], \"./internals/RawPerformanceEntry\");\n  var _Utilities = require(_dependencyMap[7], \"./internals/Utilities\");\n  var _MemoryInfo = _interopRequireDefault(require(_dependencyMap[8], \"./MemoryInfo\"));\n  var _ReactNativeStartupTiming = _interopRequireDefault(require(_dependencyMap[9], \"./ReactNativeStartupTiming\"));\n  var _NativePerformance = _interopRequireDefault(require(_dependencyMap[10], \"./specs/NativePerformance\"));\n  var _UserTiming = require(_dependencyMap[11], \"./UserTiming\");\n  var getCurrentTimeStamp = _NativePerformance.default?.now ?? global.nativePerformanceNow ?? (() => Date.now());\n  var ENTRY_TYPES_AVAILABLE_FROM_TIMELINE = ['mark', 'measure'];\n  var Performance = exports.default = /*#__PURE__*/function () {\n    function Performance() {\n      (0, _classCallCheck2.default)(this, Performance);\n      this.eventCounts = new _EventTiming.EventCounts();\n    }\n    return (0, _createClass2.default)(Performance, [{\n      key: \"memory\",\n      get: function () {\n        if (_NativePerformance.default?.getSimpleMemoryInfo) {\n          var memoryInfo = _NativePerformance.default.getSimpleMemoryInfo();\n          if (memoryInfo.hasOwnProperty('hermes_heapSize')) {\n            var totalJSHeapSize = memoryInfo.hermes_heapSize,\n              usedJSHeapSize = memoryInfo.hermes_allocatedBytes;\n            return new _MemoryInfo.default({\n              jsHeapSizeLimit: null,\n              totalJSHeapSize,\n              usedJSHeapSize\n            });\n          } else {\n            return new _MemoryInfo.default();\n          }\n        }\n        return new _MemoryInfo.default();\n      }\n    }, {\n      key: \"rnStartupTiming\",\n      get: function () {\n        if (_NativePerformance.default?.getReactNativeStartupTiming) {\n          var _NativePerformance$ge = _NativePerformance.default.getReactNativeStartupTiming(),\n            startTime = _NativePerformance$ge.startTime,\n            endTime = _NativePerformance$ge.endTime,\n            initializeRuntimeStart = _NativePerformance$ge.initializeRuntimeStart,\n            initializeRuntimeEnd = _NativePerformance$ge.initializeRuntimeEnd,\n            executeJavaScriptBundleEntryPointStart = _NativePerformance$ge.executeJavaScriptBundleEntryPointStart,\n            executeJavaScriptBundleEntryPointEnd = _NativePerformance$ge.executeJavaScriptBundleEntryPointEnd;\n          return new _ReactNativeStartupTiming.default({\n            startTime,\n            endTime,\n            initializeRuntimeStart,\n            initializeRuntimeEnd,\n            executeJavaScriptBundleEntryPointStart,\n            executeJavaScriptBundleEntryPointEnd\n          });\n        }\n        return new _ReactNativeStartupTiming.default();\n      }\n    }, {\n      key: \"mark\",\n      value: function mark(markName, markOptions) {\n        var computedStartTime;\n        if (_NativePerformance.default?.markWithResult) {\n          computedStartTime = _NativePerformance.default.markWithResult(markName, markOptions?.startTime);\n        } else {\n          (0, _Utilities.warnNoNativePerformance)();\n          computedStartTime = performance.now();\n        }\n        return new _UserTiming.PerformanceMark(markName, {\n          startTime: computedStartTime,\n          detail: markOptions?.detail\n        });\n      }\n    }, {\n      key: \"clearMarks\",\n      value: function clearMarks(markName) {\n        if (!_NativePerformance.default?.clearMarks) {\n          (0, _Utilities.warnNoNativePerformance)();\n          return;\n        }\n        _NativePerformance.default.clearMarks(markName);\n      }\n    }, {\n      key: \"measure\",\n      value: function measure(measureName, startMarkOrOptions, endMark) {\n        var options;\n        var startMarkName,\n          endMarkName = endMark,\n          duration,\n          startTime = 0,\n          endTime = 0;\n        if (typeof startMarkOrOptions === 'string') {\n          startMarkName = startMarkOrOptions;\n          options = {};\n        } else if (startMarkOrOptions !== undefined) {\n          options = startMarkOrOptions;\n          if (endMark !== undefined) {\n            throw new TypeError(\"Performance.measure: Can't have both options and endMark\");\n          }\n          if (options.start === undefined && options.end === undefined) {\n            throw new TypeError('Performance.measure: Must have at least one of start/end specified in options');\n          }\n          if (options.start !== undefined && options.end !== undefined && options.duration !== undefined) {\n            throw new TypeError(\"Performance.measure: Can't have both start/end and duration explicitly in options\");\n          }\n          if (typeof options.start === 'number') {\n            startTime = options.start;\n          } else {\n            startMarkName = options.start;\n          }\n          if (typeof options.end === 'number') {\n            endTime = options.end;\n          } else {\n            endMarkName = options.end;\n          }\n          duration = options.duration ?? duration;\n        }\n        var computedStartTime = startTime;\n        var computedDuration = duration;\n        if (_NativePerformance.default?.measureWithResult) {\n          var _NativePerformance$me = _NativePerformance.default.measureWithResult(measureName, startTime, endTime, duration, startMarkName, endMarkName);\n          var _NativePerformance$me2 = (0, _slicedToArray2.default)(_NativePerformance$me, 2);\n          computedStartTime = _NativePerformance$me2[0];\n          computedDuration = _NativePerformance$me2[1];\n        } else {\n          (0, _Utilities.warnNoNativePerformance)();\n        }\n        var measure = new _UserTiming.PerformanceMeasure(measureName, {\n          startTime: computedStartTime,\n          duration: computedDuration ?? 0,\n          detail: options?.detail\n        });\n        return measure;\n      }\n    }, {\n      key: \"clearMeasures\",\n      value: function clearMeasures(measureName) {\n        if (!_NativePerformance.default?.clearMeasures) {\n          (0, _Utilities.warnNoNativePerformance)();\n          return;\n        }\n        _NativePerformance.default?.clearMeasures(measureName);\n      }\n    }, {\n      key: \"now\",\n      value: function now() {\n        return getCurrentTimeStamp();\n      }\n    }, {\n      key: \"getEntries\",\n      value: function getEntries() {\n        if (!_NativePerformance.default?.getEntries) {\n          (0, _Utilities.warnNoNativePerformance)();\n          return [];\n        }\n        return _NativePerformance.default.getEntries().map(_RawPerformanceEntry.rawToPerformanceEntry);\n      }\n    }, {\n      key: \"getEntriesByType\",\n      value: function getEntriesByType(entryType) {\n        if (entryType != null && !ENTRY_TYPES_AVAILABLE_FROM_TIMELINE.includes(entryType)) {\n          console.warn('Deprecated API for given entry type.');\n          return [];\n        }\n        if (!_NativePerformance.default?.getEntriesByType) {\n          (0, _Utilities.warnNoNativePerformance)();\n          return [];\n        }\n        return _NativePerformance.default.getEntriesByType((0, _RawPerformanceEntry.performanceEntryTypeToRaw)(entryType)).map(_RawPerformanceEntry.rawToPerformanceEntry);\n      }\n    }, {\n      key: \"getEntriesByName\",\n      value: function getEntriesByName(entryName, entryType) {\n        if (entryType != null && !ENTRY_TYPES_AVAILABLE_FROM_TIMELINE.includes(entryType)) {\n          console.warn('Deprecated API for given entry type.');\n          return [];\n        }\n        if (!_NativePerformance.default?.getEntriesByName) {\n          (0, _Utilities.warnNoNativePerformance)();\n          return [];\n        }\n        return _NativePerformance.default.getEntriesByName(entryName, entryType != null ? (0, _RawPerformanceEntry.performanceEntryTypeToRaw)(entryType) : undefined).map(_RawPerformanceEntry.rawToPerformanceEntry);\n      }\n    }]);\n  }();\n});", "lineCount": 193, "map": [[11, 2, 20, 0], [11, 6, 20, 0, "_EventTiming"], [11, 18, 20, 0], [11, 21, 20, 0, "require"], [11, 28, 20, 0], [11, 29, 20, 0, "_dependencyMap"], [11, 43, 20, 0], [12, 2, 21, 0], [12, 6, 21, 0, "_RawPerformanceEntry"], [12, 26, 21, 0], [12, 29, 21, 0, "require"], [12, 36, 21, 0], [12, 37, 21, 0, "_dependencyMap"], [12, 51, 21, 0], [13, 2, 25, 0], [13, 6, 25, 0, "_Utilities"], [13, 16, 25, 0], [13, 19, 25, 0, "require"], [13, 26, 25, 0], [13, 27, 25, 0, "_dependencyMap"], [13, 41, 25, 0], [14, 2, 26, 0], [14, 6, 26, 0, "_MemoryInfo"], [14, 17, 26, 0], [14, 20, 26, 0, "_interopRequireDefault"], [14, 42, 26, 0], [14, 43, 26, 0, "require"], [14, 50, 26, 0], [14, 51, 26, 0, "_dependencyMap"], [14, 65, 26, 0], [15, 2, 27, 0], [15, 6, 27, 0, "_ReactNativeStartupTiming"], [15, 31, 27, 0], [15, 34, 27, 0, "_interopRequireDefault"], [15, 56, 27, 0], [15, 57, 27, 0, "require"], [15, 64, 27, 0], [15, 65, 27, 0, "_dependencyMap"], [15, 79, 27, 0], [16, 2, 28, 0], [16, 6, 28, 0, "_NativePerformance"], [16, 24, 28, 0], [16, 27, 28, 0, "_interopRequireDefault"], [16, 49, 28, 0], [16, 50, 28, 0, "require"], [16, 57, 28, 0], [16, 58, 28, 0, "_dependencyMap"], [16, 72, 28, 0], [17, 2, 29, 0], [17, 6, 29, 0, "_UserTiming"], [17, 17, 29, 0], [17, 20, 29, 0, "require"], [17, 27, 29, 0], [17, 28, 29, 0, "_dependencyMap"], [17, 42, 29, 0], [18, 2, 36, 0], [18, 6, 36, 6, "getCurrentTimeStamp"], [18, 25, 36, 52], [18, 28, 37, 2, "NativePerformance"], [18, 54, 37, 19], [18, 56, 37, 21, "now"], [18, 59, 37, 24], [18, 63, 37, 28, "global"], [18, 69, 37, 34], [18, 70, 37, 35, "nativePerformanceNow"], [18, 90, 37, 55], [18, 95, 37, 60], [18, 101, 37, 66, "Date"], [18, 105, 37, 70], [18, 106, 37, 71, "now"], [18, 109, 37, 74], [18, 110, 37, 75], [18, 111, 37, 76], [18, 112, 37, 77], [19, 2, 46, 0], [19, 6, 46, 6, "ENTRY_TYPES_AVAILABLE_FROM_TIMELINE"], [19, 41, 46, 79], [19, 44, 47, 2], [19, 45, 47, 3], [19, 51, 47, 9], [19, 53, 47, 11], [19, 62, 47, 20], [19, 63, 47, 21], [20, 2, 47, 22], [20, 6, 54, 21, "Performance"], [20, 17, 54, 32], [20, 20, 54, 32, "exports"], [20, 27, 54, 32], [20, 28, 54, 32, "default"], [20, 35, 54, 32], [21, 4, 54, 32], [21, 13, 54, 32, "Performance"], [21, 25, 54, 32], [22, 6, 54, 32], [22, 10, 54, 32, "_classCallCheck2"], [22, 26, 54, 32], [22, 27, 54, 32, "default"], [22, 34, 54, 32], [22, 42, 54, 32, "Performance"], [22, 53, 54, 32], [23, 6, 54, 32], [23, 11, 55, 2, "eventCounts"], [23, 22, 55, 13], [23, 25, 55, 29], [23, 29, 55, 33, "EventCounts"], [23, 53, 55, 44], [23, 54, 55, 45], [23, 55, 55, 46], [24, 4, 55, 46], [25, 4, 55, 46], [25, 15, 55, 46, "_createClass2"], [25, 28, 55, 46], [25, 29, 55, 46, "default"], [25, 36, 55, 46], [25, 38, 55, 46, "Performance"], [25, 49, 55, 46], [26, 6, 55, 46, "key"], [26, 9, 55, 46], [27, 6, 55, 46, "get"], [27, 9, 55, 46], [27, 11, 58, 2], [27, 20, 58, 2, "get"], [27, 21, 58, 2], [27, 23, 58, 27], [28, 8, 59, 4], [28, 12, 59, 8, "NativePerformance"], [28, 38, 59, 25], [28, 40, 59, 27, "getSimpleMemoryInfo"], [28, 59, 59, 46], [28, 61, 59, 48], [29, 10, 63, 6], [29, 14, 63, 12, "memoryInfo"], [29, 24, 63, 22], [29, 27, 63, 25, "NativePerformance"], [29, 53, 63, 42], [29, 54, 63, 43, "getSimpleMemoryInfo"], [29, 73, 63, 62], [29, 74, 63, 63], [29, 75, 63, 64], [30, 10, 64, 6], [30, 14, 64, 10, "memoryInfo"], [30, 24, 64, 20], [30, 25, 64, 21, "hasOwnProperty"], [30, 39, 64, 35], [30, 40, 64, 36], [30, 57, 64, 53], [30, 58, 64, 54], [30, 60, 64, 56], [31, 12, 66, 8], [31, 16, 67, 27, "totalJSHeapSize"], [31, 31, 67, 42], [31, 34, 69, 12, "memoryInfo"], [31, 44, 69, 22], [31, 45, 67, 10, "hermes_heapSize"], [31, 60, 67, 25], [32, 14, 68, 33, "usedJSHeapSize"], [32, 28, 68, 47], [32, 31, 69, 12, "memoryInfo"], [32, 41, 69, 22], [32, 42, 68, 10, "hermes_allocatedBytes"], [32, 63, 68, 31], [33, 12, 71, 8], [33, 19, 71, 15], [33, 23, 71, 19, "MemoryInfo"], [33, 42, 71, 29], [33, 43, 71, 30], [34, 14, 72, 10, "jsHeapSizeLimit"], [34, 29, 72, 25], [34, 31, 72, 27], [34, 35, 72, 31], [35, 14, 73, 10, "totalJSHeapSize"], [35, 29, 73, 25], [36, 14, 74, 10, "usedJSHeapSize"], [37, 12, 75, 8], [37, 13, 75, 9], [37, 14, 75, 10], [38, 10, 76, 6], [38, 11, 76, 7], [38, 17, 76, 13], [39, 12, 78, 8], [39, 19, 78, 15], [39, 23, 78, 19, "MemoryInfo"], [39, 42, 78, 29], [39, 43, 78, 30], [39, 44, 78, 31], [40, 10, 79, 6], [41, 8, 80, 4], [42, 8, 82, 4], [42, 15, 82, 11], [42, 19, 82, 15, "MemoryInfo"], [42, 38, 82, 25], [42, 39, 82, 26], [42, 40, 82, 27], [43, 6, 83, 2], [44, 4, 83, 3], [45, 6, 83, 3, "key"], [45, 9, 83, 3], [46, 6, 83, 3, "get"], [46, 9, 83, 3], [46, 11, 86, 2], [46, 20, 86, 2, "get"], [46, 21, 86, 2], [46, 23, 86, 50], [47, 8, 87, 4], [47, 12, 87, 8, "NativePerformance"], [47, 38, 87, 25], [47, 40, 87, 27, "getReactNativeStartupTiming"], [47, 67, 87, 54], [47, 69, 87, 56], [48, 10, 88, 6], [48, 14, 88, 6, "_NativePerformance$ge"], [48, 35, 88, 6], [48, 38, 95, 10, "NativePerformance"], [48, 64, 95, 27], [48, 65, 95, 28, "getReactNativeStartupTiming"], [48, 92, 95, 55], [48, 93, 95, 56], [48, 94, 95, 57], [49, 12, 89, 8, "startTime"], [49, 21, 89, 17], [49, 24, 89, 17, "_NativePerformance$ge"], [49, 45, 89, 17], [49, 46, 89, 8, "startTime"], [49, 55, 89, 17], [50, 12, 90, 8, "endTime"], [50, 19, 90, 15], [50, 22, 90, 15, "_NativePerformance$ge"], [50, 43, 90, 15], [50, 44, 90, 8, "endTime"], [50, 51, 90, 15], [51, 12, 91, 8, "initializeRuntimeStart"], [51, 34, 91, 30], [51, 37, 91, 30, "_NativePerformance$ge"], [51, 58, 91, 30], [51, 59, 91, 8, "initializeRuntimeStart"], [51, 81, 91, 30], [52, 12, 92, 8, "initializeRuntimeEnd"], [52, 32, 92, 28], [52, 35, 92, 28, "_NativePerformance$ge"], [52, 56, 92, 28], [52, 57, 92, 8, "initializeRuntimeEnd"], [52, 77, 92, 28], [53, 12, 93, 8, "executeJavaScriptBundleEntryPointStart"], [53, 50, 93, 46], [53, 53, 93, 46, "_NativePerformance$ge"], [53, 74, 93, 46], [53, 75, 93, 8, "executeJavaScriptBundleEntryPointStart"], [53, 113, 93, 46], [54, 12, 94, 8, "executeJavaScriptBundleEntryPointEnd"], [54, 48, 94, 44], [54, 51, 94, 44, "_NativePerformance$ge"], [54, 72, 94, 44], [54, 73, 94, 8, "executeJavaScriptBundleEntryPointEnd"], [54, 109, 94, 44], [55, 10, 96, 6], [55, 17, 96, 13], [55, 21, 96, 17, "ReactNativeStartupTiming"], [55, 54, 96, 41], [55, 55, 96, 42], [56, 12, 97, 8, "startTime"], [56, 21, 97, 17], [57, 12, 98, 8, "endTime"], [57, 19, 98, 15], [58, 12, 99, 8, "initializeRuntimeStart"], [58, 34, 99, 30], [59, 12, 100, 8, "initializeRuntimeEnd"], [59, 32, 100, 28], [60, 12, 101, 8, "executeJavaScriptBundleEntryPointStart"], [60, 50, 101, 46], [61, 12, 102, 8, "executeJavaScriptBundleEntryPointEnd"], [62, 10, 103, 6], [62, 11, 103, 7], [62, 12, 103, 8], [63, 8, 104, 4], [64, 8, 105, 4], [64, 15, 105, 11], [64, 19, 105, 15, "ReactNativeStartupTiming"], [64, 52, 105, 39], [64, 53, 105, 40], [64, 54, 105, 41], [65, 6, 106, 2], [66, 4, 106, 3], [67, 6, 106, 3, "key"], [67, 9, 106, 3], [68, 6, 106, 3, "value"], [68, 11, 106, 3], [68, 13, 108, 2], [68, 22, 108, 2, "mark"], [68, 26, 108, 6, "mark"], [68, 27, 109, 4, "<PERSON><PERSON><PERSON>"], [68, 35, 109, 20], [68, 37, 110, 4, "markOptions"], [68, 48, 110, 40], [68, 50, 111, 21], [69, 8, 112, 4], [69, 12, 112, 8, "computedStartTime"], [69, 29, 112, 25], [70, 8, 113, 4], [70, 12, 113, 8, "NativePerformance"], [70, 38, 113, 25], [70, 40, 113, 27, "mark<PERSON>ith<PERSON><PERSON>ult"], [70, 54, 113, 41], [70, 56, 113, 43], [71, 10, 114, 6, "computedStartTime"], [71, 27, 114, 23], [71, 30, 114, 26, "NativePerformance"], [71, 56, 114, 43], [71, 57, 114, 44, "mark<PERSON>ith<PERSON><PERSON>ult"], [71, 71, 114, 58], [71, 72, 115, 8, "<PERSON><PERSON><PERSON>"], [71, 80, 115, 16], [71, 82, 116, 8, "markOptions"], [71, 93, 116, 19], [71, 95, 116, 21, "startTime"], [71, 104, 117, 6], [71, 105, 117, 7], [72, 8, 118, 4], [72, 9, 118, 5], [72, 15, 118, 11], [73, 10, 119, 6], [73, 14, 119, 6, "warnNoNativePerformance"], [73, 48, 119, 29], [73, 50, 119, 30], [73, 51, 119, 31], [74, 10, 120, 6, "computedStartTime"], [74, 27, 120, 23], [74, 30, 120, 26, "performance"], [74, 41, 120, 37], [74, 42, 120, 38, "now"], [74, 45, 120, 41], [74, 46, 120, 42], [74, 47, 120, 43], [75, 8, 121, 4], [76, 8, 123, 4], [76, 15, 123, 11], [76, 19, 123, 15, "PerformanceMark"], [76, 46, 123, 30], [76, 47, 123, 31, "<PERSON><PERSON><PERSON>"], [76, 55, 123, 39], [76, 57, 123, 41], [77, 10, 124, 6, "startTime"], [77, 19, 124, 15], [77, 21, 124, 17, "computedStartTime"], [77, 38, 124, 34], [78, 10, 125, 6, "detail"], [78, 16, 125, 12], [78, 18, 125, 14, "markOptions"], [78, 29, 125, 25], [78, 31, 125, 27, "detail"], [79, 8, 126, 4], [79, 9, 126, 5], [79, 10, 126, 6], [80, 6, 127, 2], [81, 4, 127, 3], [82, 6, 127, 3, "key"], [82, 9, 127, 3], [83, 6, 127, 3, "value"], [83, 11, 127, 3], [83, 13, 129, 2], [83, 22, 129, 2, "clearMarks"], [83, 32, 129, 12, "clearMarks"], [83, 33, 129, 13, "<PERSON><PERSON><PERSON>"], [83, 41, 129, 30], [83, 43, 129, 38], [84, 8, 130, 4], [84, 12, 130, 8], [84, 13, 130, 9, "NativePerformance"], [84, 39, 130, 26], [84, 41, 130, 28, "clearMarks"], [84, 51, 130, 38], [84, 53, 130, 40], [85, 10, 131, 6], [85, 14, 131, 6, "warnNoNativePerformance"], [85, 48, 131, 29], [85, 50, 131, 30], [85, 51, 131, 31], [86, 10, 132, 6], [87, 8, 133, 4], [88, 8, 135, 4, "NativePerformance"], [88, 34, 135, 21], [88, 35, 135, 22, "clearMarks"], [88, 45, 135, 32], [88, 46, 135, 33, "<PERSON><PERSON><PERSON>"], [88, 54, 135, 41], [88, 55, 135, 42], [89, 6, 136, 2], [90, 4, 136, 3], [91, 6, 136, 3, "key"], [91, 9, 136, 3], [92, 6, 136, 3, "value"], [92, 11, 136, 3], [92, 13, 138, 2], [92, 22, 138, 2, "measure"], [92, 29, 138, 9, "measure"], [92, 30, 139, 4, "measureName"], [92, 41, 139, 23], [92, 43, 140, 4, "startMarkOrOptions"], [92, 61, 140, 59], [92, 63, 141, 4, "endMark"], [92, 70, 141, 20], [92, 72, 142, 24], [93, 8, 143, 4], [93, 12, 143, 8, "options"], [93, 19, 143, 15], [94, 8, 144, 4], [94, 12, 144, 8, "startMarkName"], [94, 25, 144, 21], [95, 10, 145, 6, "endMarkName"], [95, 21, 145, 17], [95, 24, 145, 20, "endMark"], [95, 31, 145, 27], [96, 10, 146, 6, "duration"], [96, 18, 146, 14], [97, 10, 147, 6, "startTime"], [97, 19, 147, 15], [97, 22, 147, 18], [97, 23, 147, 19], [98, 10, 148, 6, "endTime"], [98, 17, 148, 13], [98, 20, 148, 16], [98, 21, 148, 17], [99, 8, 150, 4], [99, 12, 150, 8], [99, 19, 150, 15, "startMarkOrOptions"], [99, 37, 150, 33], [99, 42, 150, 38], [99, 50, 150, 46], [99, 52, 150, 48], [100, 10, 151, 6, "startMarkName"], [100, 23, 151, 19], [100, 26, 151, 22, "startMarkOrOptions"], [100, 44, 151, 40], [101, 10, 152, 6, "options"], [101, 17, 152, 13], [101, 20, 152, 16], [101, 21, 152, 17], [101, 22, 152, 18], [102, 8, 153, 4], [102, 9, 153, 5], [102, 15, 153, 11], [102, 19, 153, 15, "startMarkOrOptions"], [102, 37, 153, 33], [102, 42, 153, 38, "undefined"], [102, 51, 153, 47], [102, 53, 153, 49], [103, 10, 154, 6, "options"], [103, 17, 154, 13], [103, 20, 154, 16, "startMarkOrOptions"], [103, 38, 154, 34], [104, 10, 155, 6], [104, 14, 155, 10, "endMark"], [104, 21, 155, 17], [104, 26, 155, 22, "undefined"], [104, 35, 155, 31], [104, 37, 155, 33], [105, 12, 156, 8], [105, 18, 156, 14], [105, 22, 156, 18, "TypeError"], [105, 31, 156, 27], [105, 32, 157, 10], [105, 90, 158, 8], [105, 91, 158, 9], [106, 10, 159, 6], [107, 10, 160, 6], [107, 14, 160, 10, "options"], [107, 21, 160, 17], [107, 22, 160, 18, "start"], [107, 27, 160, 23], [107, 32, 160, 28, "undefined"], [107, 41, 160, 37], [107, 45, 160, 41, "options"], [107, 52, 160, 48], [107, 53, 160, 49, "end"], [107, 56, 160, 52], [107, 61, 160, 57, "undefined"], [107, 70, 160, 66], [107, 72, 160, 68], [108, 12, 161, 8], [108, 18, 161, 14], [108, 22, 161, 18, "TypeError"], [108, 31, 161, 27], [108, 32, 162, 10], [108, 111, 163, 8], [108, 112, 163, 9], [109, 10, 164, 6], [110, 10, 165, 6], [110, 14, 166, 8, "options"], [110, 21, 166, 15], [110, 22, 166, 16, "start"], [110, 27, 166, 21], [110, 32, 166, 26, "undefined"], [110, 41, 166, 35], [110, 45, 167, 8, "options"], [110, 52, 167, 15], [110, 53, 167, 16, "end"], [110, 56, 167, 19], [110, 61, 167, 24, "undefined"], [110, 70, 167, 33], [110, 74, 168, 8, "options"], [110, 81, 168, 15], [110, 82, 168, 16, "duration"], [110, 90, 168, 24], [110, 95, 168, 29, "undefined"], [110, 104, 168, 38], [110, 106, 169, 8], [111, 12, 170, 8], [111, 18, 170, 14], [111, 22, 170, 18, "TypeError"], [111, 31, 170, 27], [111, 32, 171, 10], [111, 115, 172, 8], [111, 116, 172, 9], [112, 10, 173, 6], [113, 10, 175, 6], [113, 14, 175, 10], [113, 21, 175, 17, "options"], [113, 28, 175, 24], [113, 29, 175, 25, "start"], [113, 34, 175, 30], [113, 39, 175, 35], [113, 47, 175, 43], [113, 49, 175, 45], [114, 12, 176, 8, "startTime"], [114, 21, 176, 17], [114, 24, 176, 20, "options"], [114, 31, 176, 27], [114, 32, 176, 28, "start"], [114, 37, 176, 33], [115, 10, 177, 6], [115, 11, 177, 7], [115, 17, 177, 13], [116, 12, 178, 8, "startMarkName"], [116, 25, 178, 21], [116, 28, 178, 24, "options"], [116, 35, 178, 31], [116, 36, 178, 32, "start"], [116, 41, 178, 37], [117, 10, 179, 6], [118, 10, 181, 6], [118, 14, 181, 10], [118, 21, 181, 17, "options"], [118, 28, 181, 24], [118, 29, 181, 25, "end"], [118, 32, 181, 28], [118, 37, 181, 33], [118, 45, 181, 41], [118, 47, 181, 43], [119, 12, 182, 8, "endTime"], [119, 19, 182, 15], [119, 22, 182, 18, "options"], [119, 29, 182, 25], [119, 30, 182, 26, "end"], [119, 33, 182, 29], [120, 10, 183, 6], [120, 11, 183, 7], [120, 17, 183, 13], [121, 12, 184, 8, "endMarkName"], [121, 23, 184, 19], [121, 26, 184, 22, "options"], [121, 33, 184, 29], [121, 34, 184, 30, "end"], [121, 37, 184, 33], [122, 10, 185, 6], [123, 10, 187, 6, "duration"], [123, 18, 187, 14], [123, 21, 187, 17, "options"], [123, 28, 187, 24], [123, 29, 187, 25, "duration"], [123, 37, 187, 33], [123, 41, 187, 37, "duration"], [123, 49, 187, 45], [124, 8, 188, 4], [125, 8, 190, 4], [125, 12, 190, 8, "computedStartTime"], [125, 29, 190, 25], [125, 32, 190, 28, "startTime"], [125, 41, 190, 37], [126, 8, 191, 4], [126, 12, 191, 8, "computedDuration"], [126, 28, 191, 24], [126, 31, 191, 27, "duration"], [126, 39, 191, 35], [127, 8, 193, 4], [127, 12, 193, 8, "NativePerformance"], [127, 38, 193, 25], [127, 40, 193, 27, "measureWithResult"], [127, 57, 193, 44], [127, 59, 193, 46], [128, 10, 193, 46], [128, 14, 193, 46, "_NativePerformance$me"], [128, 35, 193, 46], [128, 38, 195, 8, "NativePerformance"], [128, 64, 195, 25], [128, 65, 195, 26, "measureWithResult"], [128, 82, 195, 43], [128, 83, 196, 10, "measureName"], [128, 94, 196, 21], [128, 96, 197, 10, "startTime"], [128, 105, 197, 19], [128, 107, 198, 10, "endTime"], [128, 114, 198, 17], [128, 116, 199, 10, "duration"], [128, 124, 199, 18], [128, 126, 200, 10, "startMarkName"], [128, 139, 200, 23], [128, 141, 201, 10, "endMarkName"], [128, 152, 202, 8], [128, 153, 202, 9], [129, 10, 202, 9], [129, 14, 202, 9, "_NativePerformance$me2"], [129, 36, 202, 9], [129, 43, 202, 9, "_slicedToArray2"], [129, 58, 202, 9], [129, 59, 202, 9, "default"], [129, 66, 202, 9], [129, 68, 202, 9, "_NativePerformance$me"], [129, 89, 202, 9], [130, 10, 194, 7, "computedStartTime"], [130, 27, 194, 24], [130, 30, 194, 24, "_NativePerformance$me2"], [130, 52, 194, 24], [131, 10, 194, 26, "computedDuration"], [131, 26, 194, 42], [131, 29, 194, 42, "_NativePerformance$me2"], [131, 51, 194, 42], [132, 8, 203, 4], [132, 9, 203, 5], [132, 15, 203, 11], [133, 10, 204, 6], [133, 14, 204, 6, "warnNoNativePerformance"], [133, 48, 204, 29], [133, 50, 204, 30], [133, 51, 204, 31], [134, 8, 205, 4], [135, 8, 207, 4], [135, 12, 207, 10, "measure"], [135, 19, 207, 17], [135, 22, 207, 20], [135, 26, 207, 24, "PerformanceMeasure"], [135, 56, 207, 42], [135, 57, 207, 43, "measureName"], [135, 68, 207, 54], [135, 70, 207, 56], [136, 10, 208, 6, "startTime"], [136, 19, 208, 15], [136, 21, 208, 17, "computedStartTime"], [136, 38, 208, 34], [137, 10, 209, 6, "duration"], [137, 18, 209, 14], [137, 20, 209, 16, "computedDuration"], [137, 36, 209, 32], [137, 40, 209, 36], [137, 41, 209, 37], [138, 10, 210, 6, "detail"], [138, 16, 210, 12], [138, 18, 210, 14, "options"], [138, 25, 210, 21], [138, 27, 210, 23, "detail"], [139, 8, 211, 4], [139, 9, 211, 5], [139, 10, 211, 6], [140, 8, 213, 4], [140, 15, 213, 11, "measure"], [140, 22, 213, 18], [141, 6, 214, 2], [142, 4, 214, 3], [143, 6, 214, 3, "key"], [143, 9, 214, 3], [144, 6, 214, 3, "value"], [144, 11, 214, 3], [144, 13, 216, 2], [144, 22, 216, 2, "clearMeasures"], [144, 35, 216, 15, "clearMeasures"], [144, 36, 216, 16, "measureName"], [144, 47, 216, 36], [144, 49, 216, 44], [145, 8, 217, 4], [145, 12, 217, 8], [145, 13, 217, 9, "NativePerformance"], [145, 39, 217, 26], [145, 41, 217, 28, "clearMeasures"], [145, 54, 217, 41], [145, 56, 217, 43], [146, 10, 218, 6], [146, 14, 218, 6, "warnNoNativePerformance"], [146, 48, 218, 29], [146, 50, 218, 30], [146, 51, 218, 31], [147, 10, 219, 6], [148, 8, 220, 4], [149, 8, 222, 4, "NativePerformance"], [149, 34, 222, 21], [149, 36, 222, 23, "clearMeasures"], [149, 49, 222, 36], [149, 50, 222, 37, "measureName"], [149, 61, 222, 48], [149, 62, 222, 49], [150, 6, 223, 2], [151, 4, 223, 3], [152, 6, 223, 3, "key"], [152, 9, 223, 3], [153, 6, 223, 3, "value"], [153, 11, 223, 3], [153, 13, 229, 2], [153, 22, 229, 2, "now"], [153, 25, 229, 5, "now"], [153, 26, 229, 5], [153, 28, 229, 29], [154, 8, 230, 4], [154, 15, 230, 11, "getCurrentTimeStamp"], [154, 34, 230, 30], [154, 35, 230, 31], [154, 36, 230, 32], [155, 6, 231, 2], [156, 4, 231, 3], [157, 6, 231, 3, "key"], [157, 9, 231, 3], [158, 6, 231, 3, "value"], [158, 11, 231, 3], [158, 13, 238, 2], [158, 22, 238, 2, "getEntries"], [158, 32, 238, 12, "getEntries"], [158, 33, 238, 12], [158, 35, 238, 37], [159, 8, 239, 4], [159, 12, 239, 8], [159, 13, 239, 9, "NativePerformance"], [159, 39, 239, 26], [159, 41, 239, 28, "getEntries"], [159, 51, 239, 38], [159, 53, 239, 40], [160, 10, 240, 6], [160, 14, 240, 6, "warnNoNativePerformance"], [160, 48, 240, 29], [160, 50, 240, 30], [160, 51, 240, 31], [161, 10, 241, 6], [161, 17, 241, 13], [161, 19, 241, 15], [162, 8, 242, 4], [163, 8, 243, 4], [163, 15, 243, 11, "NativePerformance"], [163, 41, 243, 28], [163, 42, 243, 29, "getEntries"], [163, 52, 243, 39], [163, 53, 243, 40], [163, 54, 243, 41], [163, 55, 243, 42, "map"], [163, 58, 243, 45], [163, 59, 243, 46, "rawToPerformanceEntry"], [163, 101, 243, 67], [163, 102, 243, 68], [164, 6, 244, 2], [165, 4, 244, 3], [166, 6, 244, 3, "key"], [166, 9, 244, 3], [167, 6, 244, 3, "value"], [167, 11, 244, 3], [167, 13, 246, 2], [167, 22, 246, 2, "getEntriesByType"], [167, 38, 246, 18, "getEntriesByType"], [167, 39, 246, 19, "entryType"], [167, 48, 246, 50], [167, 50, 246, 74], [168, 8, 247, 4], [168, 12, 248, 6, "entryType"], [168, 21, 248, 15], [168, 25, 248, 19], [168, 29, 248, 23], [168, 33, 249, 6], [168, 34, 249, 7, "ENTRY_TYPES_AVAILABLE_FROM_TIMELINE"], [168, 69, 249, 42], [168, 70, 249, 43, "includes"], [168, 78, 249, 51], [168, 79, 249, 52, "entryType"], [168, 88, 249, 61], [168, 89, 249, 62], [168, 91, 250, 6], [169, 10, 251, 6, "console"], [169, 17, 251, 13], [169, 18, 251, 14, "warn"], [169, 22, 251, 18], [169, 23, 251, 19], [169, 61, 251, 57], [169, 62, 251, 58], [170, 10, 252, 6], [170, 17, 252, 13], [170, 19, 252, 15], [171, 8, 253, 4], [172, 8, 255, 4], [172, 12, 255, 8], [172, 13, 255, 9, "NativePerformance"], [172, 39, 255, 26], [172, 41, 255, 28, "getEntriesByType"], [172, 57, 255, 44], [172, 59, 255, 46], [173, 10, 256, 6], [173, 14, 256, 6, "warnNoNativePerformance"], [173, 48, 256, 29], [173, 50, 256, 30], [173, 51, 256, 31], [174, 10, 257, 6], [174, 17, 257, 13], [174, 19, 257, 15], [175, 8, 258, 4], [176, 8, 260, 4], [176, 15, 260, 11, "NativePerformance"], [176, 41, 260, 28], [176, 42, 260, 29, "getEntriesByType"], [176, 58, 260, 45], [176, 59, 261, 6], [176, 63, 261, 6, "performanceEntryTypeToRaw"], [176, 109, 261, 31], [176, 111, 261, 32, "entryType"], [176, 120, 261, 41], [176, 121, 262, 4], [176, 122, 262, 5], [176, 123, 262, 6, "map"], [176, 126, 262, 9], [176, 127, 262, 10, "rawToPerformanceEntry"], [176, 169, 262, 31], [176, 170, 262, 32], [177, 6, 263, 2], [178, 4, 263, 3], [179, 6, 263, 3, "key"], [179, 9, 263, 3], [180, 6, 263, 3, "value"], [180, 11, 263, 3], [180, 13, 265, 2], [180, 22, 265, 2, "getEntriesByName"], [180, 38, 265, 18, "getEntriesByName"], [180, 39, 266, 4, "entryName"], [180, 48, 266, 21], [180, 50, 267, 4, "entryType"], [180, 59, 267, 36], [180, 61, 268, 26], [181, 8, 269, 4], [181, 12, 270, 6, "entryType"], [181, 21, 270, 15], [181, 25, 270, 19], [181, 29, 270, 23], [181, 33, 271, 6], [181, 34, 271, 7, "ENTRY_TYPES_AVAILABLE_FROM_TIMELINE"], [181, 69, 271, 42], [181, 70, 271, 43, "includes"], [181, 78, 271, 51], [181, 79, 271, 52, "entryType"], [181, 88, 271, 61], [181, 89, 271, 62], [181, 91, 272, 6], [182, 10, 273, 6, "console"], [182, 17, 273, 13], [182, 18, 273, 14, "warn"], [182, 22, 273, 18], [182, 23, 273, 19], [182, 61, 273, 57], [182, 62, 273, 58], [183, 10, 274, 6], [183, 17, 274, 13], [183, 19, 274, 15], [184, 8, 275, 4], [185, 8, 277, 4], [185, 12, 277, 8], [185, 13, 277, 9, "NativePerformance"], [185, 39, 277, 26], [185, 41, 277, 28, "getEntriesByName"], [185, 57, 277, 44], [185, 59, 277, 46], [186, 10, 278, 6], [186, 14, 278, 6, "warnNoNativePerformance"], [186, 48, 278, 29], [186, 50, 278, 30], [186, 51, 278, 31], [187, 10, 279, 6], [187, 17, 279, 13], [187, 19, 279, 15], [188, 8, 280, 4], [189, 8, 282, 4], [189, 15, 282, 11, "NativePerformance"], [189, 41, 282, 28], [189, 42, 282, 29, "getEntriesByName"], [189, 58, 282, 45], [189, 59, 283, 6, "entryName"], [189, 68, 283, 15], [189, 70, 284, 6, "entryType"], [189, 79, 284, 15], [189, 83, 284, 19], [189, 87, 284, 23], [189, 90, 284, 26], [189, 94, 284, 26, "performanceEntryTypeToRaw"], [189, 140, 284, 51], [189, 142, 284, 52, "entryType"], [189, 151, 284, 61], [189, 152, 284, 62], [189, 155, 284, 65, "undefined"], [189, 164, 285, 4], [189, 165, 285, 5], [189, 166, 285, 6, "map"], [189, 169, 285, 9], [189, 170, 285, 10, "rawToPerformanceEntry"], [189, 212, 285, 31], [189, 213, 285, 32], [190, 6, 286, 2], [191, 4, 286, 3], [192, 2, 286, 3], [193, 0, 286, 3], [193, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "Performance", "get__memory", "get__rnStartupTiming", "mark", "clearMarks", "measure", "clearMeasures", "now", "getEntries", "getEntriesByType", "getEntriesByName"], "mappings": "AAA;4DCoC,gBD;eEiB;ECI;GDyB;EEG;GFoB;EGE;GHmB;EIE;GJO;EKE;GL4E;EME;GNO;EOM;GPE;EQO;GRM;ESE;GTiB;EUE;GVqB"}}, "type": "js/module"}]}