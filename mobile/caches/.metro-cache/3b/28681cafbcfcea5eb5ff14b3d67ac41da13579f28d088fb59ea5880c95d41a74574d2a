{"dependencies": [{"name": "react-native/Libraries/Core/Devtools/getDevServer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 23, "index": 228}, "end": {"line": 8, "column": 83, "index": 288}}], "key": "2vJNIFpjcVsKB9LvzVXfwn949Zg=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/WebSocket/WebSocket", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 20, "index": 618}, "end": {"line": 16, "column": 73, "index": 671}}], "key": "Wxd+tCkewnke0kjsPLWAdyWgV6E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /* eslint-env browser */\n\n  function createWebSocketConnection() {\n    var path = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '/message';\n    var getDevServer = require(_dependencyMap[0], \"react-native/Libraries/Core/Devtools/getDevServer\").default;\n    var devServer = getDevServer();\n    if (!devServer.bundleLoadedFromServer) {\n      throw new Error('Cannot create devtools websocket connections in embedded environments.');\n    }\n    var devServerUrl = new URL(devServer.url);\n    var serverScheme = devServerUrl.protocol === 'https:' ? 'wss' : 'ws';\n    var WebSocket = require(_dependencyMap[1], \"react-native/Libraries/WebSocket/WebSocket\").default;\n    return new WebSocket(`${serverScheme}://${devServerUrl.host}${path}`);\n  }\n  createWebSocketConnection().onmessage = message => {\n    var data = JSON.parse(String(message.data));\n    switch (data.method) {\n      case 'sendDevCommand':\n        switch (data.params.name) {\n          case 'rsc-reload':\n            if (data.params.platform && data.params.platform !== \"ios\") {\n              return;\n            }\n            if (!globalThis.__EXPO_RSC_RELOAD_LISTENERS__) {\n              // server function-only mode\n            } else {\n              globalThis.__EXPO_RSC_RELOAD_LISTENERS__?.forEach(l => l());\n            }\n            break;\n        }\n        break;\n      // NOTE: All other cases are handled in the native runtime.\n    }\n  };\n});", "lineCount": 36, "map": [[2, 2, 1, 0], [4, 2, 7, 0], [4, 11, 7, 9, "createWebSocketConnection"], [4, 36, 7, 34, "createWebSocketConnection"], [4, 37, 7, 34], [4, 39, 7, 73], [5, 4, 7, 73], [5, 8, 7, 35, "path"], [5, 12, 7, 47], [5, 15, 7, 47, "arguments"], [5, 24, 7, 47], [5, 25, 7, 47, "length"], [5, 31, 7, 47], [5, 39, 7, 47, "arguments"], [5, 48, 7, 47], [5, 56, 7, 47, "undefined"], [5, 65, 7, 47], [5, 68, 7, 47, "arguments"], [5, 77, 7, 47], [5, 83, 7, 50], [5, 93, 7, 60], [6, 4, 8, 2], [6, 8, 8, 8, "getDevServer"], [6, 20, 8, 20], [6, 23, 8, 23, "require"], [6, 30, 8, 30], [6, 31, 8, 30, "_dependencyMap"], [6, 45, 8, 30], [6, 101, 8, 82], [6, 102, 8, 83], [6, 103, 8, 84, "default"], [6, 110, 8, 91], [7, 4, 9, 2], [7, 8, 9, 8, "devServer"], [7, 17, 9, 17], [7, 20, 9, 20, "getDevServer"], [7, 32, 9, 32], [7, 33, 9, 33], [7, 34, 9, 34], [8, 4, 10, 2], [8, 8, 10, 6], [8, 9, 10, 7, "devServer"], [8, 18, 10, 16], [8, 19, 10, 17, "bundleLoadedFromServer"], [8, 41, 10, 39], [8, 43, 10, 41], [9, 6, 11, 4], [9, 12, 11, 10], [9, 16, 11, 14, "Error"], [9, 21, 11, 19], [9, 22, 11, 20], [9, 94, 11, 92], [9, 95, 11, 93], [10, 4, 12, 2], [11, 4, 14, 2], [11, 8, 14, 8, "devServerUrl"], [11, 20, 14, 20], [11, 23, 14, 23], [11, 27, 14, 27, "URL"], [11, 30, 14, 30], [11, 31, 14, 31, "devServer"], [11, 40, 14, 40], [11, 41, 14, 41, "url"], [11, 44, 14, 44], [11, 45, 14, 45], [12, 4, 15, 2], [12, 8, 15, 8, "serverScheme"], [12, 20, 15, 20], [12, 23, 15, 23, "devServerUrl"], [12, 35, 15, 35], [12, 36, 15, 36, "protocol"], [12, 44, 15, 44], [12, 49, 15, 49], [12, 57, 15, 57], [12, 60, 15, 60], [12, 65, 15, 65], [12, 68, 15, 68], [12, 72, 15, 72], [13, 4, 16, 2], [13, 8, 16, 8, "WebSocket"], [13, 17, 16, 17], [13, 20, 16, 20, "require"], [13, 27, 16, 27], [13, 28, 16, 27, "_dependencyMap"], [13, 42, 16, 27], [13, 91, 16, 72], [13, 92, 16, 73], [13, 93, 16, 74, "default"], [13, 100, 16, 81], [14, 4, 17, 2], [14, 11, 17, 9], [14, 15, 17, 13, "WebSocket"], [14, 24, 17, 22], [14, 25, 17, 23], [14, 28, 17, 26, "serverScheme"], [14, 40, 17, 38], [14, 46, 17, 44, "devServerUrl"], [14, 58, 17, 56], [14, 59, 17, 57, "host"], [14, 63, 17, 61], [14, 66, 17, 64, "path"], [14, 70, 17, 68], [14, 72, 17, 70], [14, 73, 17, 71], [15, 2, 18, 0], [16, 2, 20, 0, "createWebSocketConnection"], [16, 27, 20, 25], [16, 28, 20, 26], [16, 29, 20, 27], [16, 30, 20, 28, "onmessage"], [16, 39, 20, 37], [16, 42, 20, 41, "message"], [16, 49, 20, 48], [16, 53, 20, 53], [17, 4, 21, 2], [17, 8, 21, 8, "data"], [17, 12, 21, 12], [17, 15, 21, 15, "JSON"], [17, 19, 21, 19], [17, 20, 21, 20, "parse"], [17, 25, 21, 25], [17, 26, 21, 26, "String"], [17, 32, 21, 32], [17, 33, 21, 33, "message"], [17, 40, 21, 40], [17, 41, 21, 41, "data"], [17, 45, 21, 45], [17, 46, 21, 46], [17, 47, 21, 47], [18, 4, 22, 2], [18, 12, 22, 10, "data"], [18, 16, 22, 14], [18, 17, 22, 15, "method"], [18, 23, 22, 21], [19, 6, 23, 4], [19, 11, 23, 9], [19, 27, 23, 25], [20, 8, 24, 6], [20, 16, 24, 14, "data"], [20, 20, 24, 18], [20, 21, 24, 19, "params"], [20, 27, 24, 25], [20, 28, 24, 26, "name"], [20, 32, 24, 30], [21, 10, 25, 8], [21, 15, 25, 13], [21, 27, 25, 25], [22, 12, 26, 10], [22, 16, 26, 14, "data"], [22, 20, 26, 18], [22, 21, 26, 19, "params"], [22, 27, 26, 25], [22, 28, 26, 26, "platform"], [22, 36, 26, 34], [22, 40, 26, 38, "data"], [22, 44, 26, 42], [22, 45, 26, 43, "params"], [22, 51, 26, 49], [22, 52, 26, 50, "platform"], [22, 60, 26, 58], [22, 70, 26, 82], [22, 72, 26, 84], [23, 14, 27, 12], [24, 12, 28, 10], [25, 12, 29, 10], [25, 16, 29, 14], [25, 17, 29, 15, "globalThis"], [25, 27, 29, 25], [25, 28, 29, 26, "__EXPO_RSC_RELOAD_LISTENERS__"], [25, 57, 29, 55], [25, 59, 29, 57], [26, 14, 30, 12], [27, 12, 30, 12], [27, 13, 31, 11], [27, 19, 31, 17], [28, 14, 32, 12, "globalThis"], [28, 24, 32, 22], [28, 25, 32, 23, "__EXPO_RSC_RELOAD_LISTENERS__"], [28, 54, 32, 52], [28, 56, 32, 54, "for<PERSON>ach"], [28, 63, 32, 61], [28, 64, 32, 63, "l"], [28, 65, 32, 64], [28, 69, 32, 69, "l"], [28, 70, 32, 70], [28, 71, 32, 71], [28, 72, 32, 72], [28, 73, 32, 73], [29, 12, 33, 10], [30, 12, 34, 10], [31, 8, 35, 6], [32, 8, 36, 6], [33, 6, 37, 4], [34, 4, 38, 2], [35, 2, 39, 0], [35, 3, 39, 1], [36, 0, 39, 2], [36, 3]], "functionMap": {"names": ["<global>", "createWebSocketConnection", "createWebSocketConnection.onmessage", "globalThis.__EXPO_RSC_RELOAD_LISTENERS__.forEach$argument_0"], "mappings": "AAA;ACM;CDW;wCEE;8DCY,UD;CFO"}}, "type": "js/module"}]}