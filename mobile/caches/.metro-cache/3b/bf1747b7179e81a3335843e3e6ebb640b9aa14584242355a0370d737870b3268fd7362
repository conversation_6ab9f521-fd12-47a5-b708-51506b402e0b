{"dependencies": [{"name": "./fonts.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 35, "index": 50}}], "key": "zSL/287093Hzzhp1MI1N2DlHLNk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.DarkTheme = void 0;\n  var _fonts = require(_dependencyMap[0], \"./fonts.js\");\n  const DarkTheme = exports.DarkTheme = {\n    dark: true,\n    colors: {\n      primary: 'rgb(10, 132, 255)',\n      background: 'rgb(1, 1, 1)',\n      card: 'rgb(18, 18, 18)',\n      text: 'rgb(229, 229, 231)',\n      border: 'rgb(39, 39, 41)',\n      notification: 'rgb(255, 69, 58)'\n    },\n    fonts: _fonts.fonts\n  };\n});", "lineCount": 21, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "DarkTheme"], [7, 19, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_fonts"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 7], [9, 8, 4, 13, "DarkTheme"], [9, 17, 4, 22], [9, 20, 4, 22, "exports"], [9, 27, 4, 22], [9, 28, 4, 22, "DarkTheme"], [9, 37, 4, 22], [9, 40, 4, 25], [10, 4, 5, 2, "dark"], [10, 8, 5, 6], [10, 10, 5, 8], [10, 14, 5, 12], [11, 4, 6, 2, "colors"], [11, 10, 6, 8], [11, 12, 6, 10], [12, 6, 7, 4, "primary"], [12, 13, 7, 11], [12, 15, 7, 13], [12, 34, 7, 32], [13, 6, 8, 4, "background"], [13, 16, 8, 14], [13, 18, 8, 16], [13, 32, 8, 30], [14, 6, 9, 4, "card"], [14, 10, 9, 8], [14, 12, 9, 10], [14, 29, 9, 27], [15, 6, 10, 4, "text"], [15, 10, 10, 8], [15, 12, 10, 10], [15, 32, 10, 30], [16, 6, 11, 4, "border"], [16, 12, 11, 10], [16, 14, 11, 12], [16, 31, 11, 29], [17, 6, 12, 4, "notification"], [17, 18, 12, 16], [17, 20, 12, 18], [18, 4, 13, 2], [18, 5, 13, 3], [19, 4, 14, 2, "fonts"], [19, 9, 14, 7], [19, 11, 14, 2, "fonts"], [20, 2, 15, 0], [20, 3, 15, 1], [21, 0, 15, 2], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}