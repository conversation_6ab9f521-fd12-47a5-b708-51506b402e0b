{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  //\n\n  module.exports = function shallowEqual(objA, objB, compare, compareContext) {\n    var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n    if (ret !== void 0) {\n      return !!ret;\n    }\n    if (objA === objB) {\n      return true;\n    }\n    if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n      return false;\n    }\n    var keysA = Object.keys(objA);\n    var keysB = Object.keys(objB);\n    if (keysA.length !== keysB.length) {\n      return false;\n    }\n    var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n    // Test for A's keys different from B.\n    for (var idx = 0; idx < keysA.length; idx++) {\n      var key = keysA[idx];\n      if (!bHasOwnProperty(key)) {\n        return false;\n      }\n      var valueA = objA[key];\n      var valueB = objB[key];\n      ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n      if (ret === false || ret === void 0 && valueA !== valueB) {\n        return false;\n      }\n    }\n    return true;\n  };\n});", "lineCount": 37, "map": [[2, 2, 1, 0], [4, 2, 3, 0, "module"], [4, 8, 3, 6], [4, 9, 3, 7, "exports"], [4, 16, 3, 14], [4, 19, 3, 17], [4, 28, 3, 26, "shallowEqual"], [4, 40, 3, 38, "shallowEqual"], [4, 41, 3, 39, "objA"], [4, 45, 3, 43], [4, 47, 3, 45, "objB"], [4, 51, 3, 49], [4, 53, 3, 51, "compare"], [4, 60, 3, 58], [4, 62, 3, 60, "compareContext"], [4, 76, 3, 74], [4, 78, 3, 76], [5, 4, 4, 2], [5, 8, 4, 6, "ret"], [5, 11, 4, 9], [5, 14, 4, 12, "compare"], [5, 21, 4, 19], [5, 24, 4, 22, "compare"], [5, 31, 4, 29], [5, 32, 4, 30, "call"], [5, 36, 4, 34], [5, 37, 4, 35, "compareContext"], [5, 51, 4, 49], [5, 53, 4, 51, "objA"], [5, 57, 4, 55], [5, 59, 4, 57, "objB"], [5, 63, 4, 61], [5, 64, 4, 62], [5, 67, 4, 65], [5, 72, 4, 70], [5, 73, 4, 71], [6, 4, 6, 2], [6, 8, 6, 6, "ret"], [6, 11, 6, 9], [6, 16, 6, 14], [6, 21, 6, 19], [6, 22, 6, 20], [6, 24, 6, 22], [7, 6, 7, 4], [7, 13, 7, 11], [7, 14, 7, 12], [7, 15, 7, 13, "ret"], [7, 18, 7, 16], [8, 4, 8, 2], [9, 4, 10, 2], [9, 8, 10, 6, "objA"], [9, 12, 10, 10], [9, 17, 10, 15, "objB"], [9, 21, 10, 19], [9, 23, 10, 21], [10, 6, 11, 4], [10, 13, 11, 11], [10, 17, 11, 15], [11, 4, 12, 2], [12, 4, 14, 2], [12, 8, 14, 6], [12, 15, 14, 13, "objA"], [12, 19, 14, 17], [12, 24, 14, 22], [12, 32, 14, 30], [12, 36, 14, 34], [12, 37, 14, 35, "objA"], [12, 41, 14, 39], [12, 45, 14, 43], [12, 52, 14, 50, "objB"], [12, 56, 14, 54], [12, 61, 14, 59], [12, 69, 14, 67], [12, 73, 14, 71], [12, 74, 14, 72, "objB"], [12, 78, 14, 76], [12, 80, 14, 78], [13, 6, 15, 4], [13, 13, 15, 11], [13, 18, 15, 16], [14, 4, 16, 2], [15, 4, 18, 2], [15, 8, 18, 6, "keysA"], [15, 13, 18, 11], [15, 16, 18, 14, "Object"], [15, 22, 18, 20], [15, 23, 18, 21, "keys"], [15, 27, 18, 25], [15, 28, 18, 26, "objA"], [15, 32, 18, 30], [15, 33, 18, 31], [16, 4, 19, 2], [16, 8, 19, 6, "keysB"], [16, 13, 19, 11], [16, 16, 19, 14, "Object"], [16, 22, 19, 20], [16, 23, 19, 21, "keys"], [16, 27, 19, 25], [16, 28, 19, 26, "objB"], [16, 32, 19, 30], [16, 33, 19, 31], [17, 4, 21, 2], [17, 8, 21, 6, "keysA"], [17, 13, 21, 11], [17, 14, 21, 12, "length"], [17, 20, 21, 18], [17, 25, 21, 23, "keysB"], [17, 30, 21, 28], [17, 31, 21, 29, "length"], [17, 37, 21, 35], [17, 39, 21, 37], [18, 6, 22, 4], [18, 13, 22, 11], [18, 18, 22, 16], [19, 4, 23, 2], [20, 4, 25, 2], [20, 8, 25, 6, "bHasOwnProperty"], [20, 23, 25, 21], [20, 26, 25, 24, "Object"], [20, 32, 25, 30], [20, 33, 25, 31, "prototype"], [20, 42, 25, 40], [20, 43, 25, 41, "hasOwnProperty"], [20, 57, 25, 55], [20, 58, 25, 56, "bind"], [20, 62, 25, 60], [20, 63, 25, 61, "objB"], [20, 67, 25, 65], [20, 68, 25, 66], [22, 4, 27, 2], [23, 4, 28, 2], [23, 9, 28, 7], [23, 13, 28, 11, "idx"], [23, 16, 28, 14], [23, 19, 28, 17], [23, 20, 28, 18], [23, 22, 28, 20, "idx"], [23, 25, 28, 23], [23, 28, 28, 26, "keysA"], [23, 33, 28, 31], [23, 34, 28, 32, "length"], [23, 40, 28, 38], [23, 42, 28, 40, "idx"], [23, 45, 28, 43], [23, 47, 28, 45], [23, 49, 28, 47], [24, 6, 29, 4], [24, 10, 29, 8, "key"], [24, 13, 29, 11], [24, 16, 29, 14, "keysA"], [24, 21, 29, 19], [24, 22, 29, 20, "idx"], [24, 25, 29, 23], [24, 26, 29, 24], [25, 6, 31, 4], [25, 10, 31, 8], [25, 11, 31, 9, "bHasOwnProperty"], [25, 26, 31, 24], [25, 27, 31, 25, "key"], [25, 30, 31, 28], [25, 31, 31, 29], [25, 33, 31, 31], [26, 8, 32, 6], [26, 15, 32, 13], [26, 20, 32, 18], [27, 6, 33, 4], [28, 6, 35, 4], [28, 10, 35, 8, "valueA"], [28, 16, 35, 14], [28, 19, 35, 17, "objA"], [28, 23, 35, 21], [28, 24, 35, 22, "key"], [28, 27, 35, 25], [28, 28, 35, 26], [29, 6, 36, 4], [29, 10, 36, 8, "valueB"], [29, 16, 36, 14], [29, 19, 36, 17, "objB"], [29, 23, 36, 21], [29, 24, 36, 22, "key"], [29, 27, 36, 25], [29, 28, 36, 26], [30, 6, 38, 4, "ret"], [30, 9, 38, 7], [30, 12, 38, 10, "compare"], [30, 19, 38, 17], [30, 22, 38, 20, "compare"], [30, 29, 38, 27], [30, 30, 38, 28, "call"], [30, 34, 38, 32], [30, 35, 38, 33, "compareContext"], [30, 49, 38, 47], [30, 51, 38, 49, "valueA"], [30, 57, 38, 55], [30, 59, 38, 57, "valueB"], [30, 65, 38, 63], [30, 67, 38, 65, "key"], [30, 70, 38, 68], [30, 71, 38, 69], [30, 74, 38, 72], [30, 79, 38, 77], [30, 80, 38, 78], [31, 6, 40, 4], [31, 10, 40, 8, "ret"], [31, 13, 40, 11], [31, 18, 40, 16], [31, 23, 40, 21], [31, 27, 40, 26, "ret"], [31, 30, 40, 29], [31, 35, 40, 34], [31, 40, 40, 39], [31, 41, 40, 40], [31, 45, 40, 44, "valueA"], [31, 51, 40, 50], [31, 56, 40, 55, "valueB"], [31, 62, 40, 62], [31, 64, 40, 64], [32, 8, 41, 6], [32, 15, 41, 13], [32, 20, 41, 18], [33, 6, 42, 4], [34, 4, 43, 2], [35, 4, 45, 2], [35, 11, 45, 9], [35, 15, 45, 13], [36, 2, 46, 0], [36, 3, 46, 1], [37, 0, 46, 2], [37, 3]], "functionMap": {"names": ["<global>", "shallowEqual"], "mappings": "AAA;iBCE;CD2C"}}, "type": "js/module"}]}