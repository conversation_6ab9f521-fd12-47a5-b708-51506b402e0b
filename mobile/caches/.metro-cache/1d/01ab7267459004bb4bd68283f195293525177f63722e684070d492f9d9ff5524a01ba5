{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 73, "index": 120}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}, {"name": "./NavigationContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 121}, "end": {"line": 5, "column": 59, "index": 180}}], "key": "RM0XoJ1uy5+hqq85ZlLNt6FYuco=", "exportNames": ["*"]}}, {"name": "./NavigationRouteContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 181}, "end": {"line": 6, "column": 69, "index": 250}}], "key": "AWXnpGNA5UkH1qQUM7hLv2L9KzI=", "exportNames": ["*"]}}, {"name": "./SceneView.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 251}, "end": {"line": 7, "column": 43, "index": 294}}], "key": "FvyKlHSheioqPkFrfVVDYQbCJP4=", "exportNames": ["*"]}}, {"name": "./theming/ThemeContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 295}, "end": {"line": 8, "column": 57, "index": 352}}], "key": "qlk5yrcKdN2V0KhKMRE4Vd3Zk/8=", "exportNames": ["*"]}}, {"name": "./useNavigationCache.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 353}, "end": {"line": 9, "column": 61, "index": 414}}], "key": "BIFS8wPXcRN7UboMxphJ4rGKLe4=", "exportNames": ["*"]}}, {"name": "./useRouteCache.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 415}, "end": {"line": 10, "column": 51, "index": 466}}], "key": "7Wwgyr1YruLrWKgk0bZ/DNlglOg=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 467}, "end": {"line": 11, "column": 48, "index": 515}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDescriptors = useDescriptors;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[1], \"./NavigationBuilderContext.js\");\n  var _NavigationContext = require(_dependencyMap[2], \"./NavigationContext.js\");\n  var _NavigationRouteContext = require(_dependencyMap[3], \"./NavigationRouteContext.js\");\n  var _SceneView = require(_dependencyMap[4], \"./SceneView.js\");\n  var _ThemeContext = require(_dependencyMap[5], \"./theming/ThemeContext.js\");\n  var _useNavigationCache = require(_dependencyMap[6], \"./useNavigationCache.js\");\n  var _useRouteCache = require(_dependencyMap[7], \"./useRouteCache.js\");\n  var _jsxRuntime = require(_dependencyMap[8], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Hook to create descriptor objects for the child routes.\n   *\n   * A descriptor object provides 3 things:\n   * - Helper method to render a screen\n   * - Options specified by the screen for the navigator\n   * - Navigation object intended for the route\n   */\n  function useDescriptors({\n    state,\n    screens,\n    navigation,\n    screenOptions,\n    screenLayout,\n    onAction,\n    getState,\n    setState,\n    addListener,\n    addKeyedListener,\n    onRouteFocus,\n    router,\n    emitter\n  }) {\n    const theme = React.useContext(_ThemeContext.ThemeContext);\n    const [options, setOptions] = React.useState({});\n    const {\n      onDispatchAction,\n      onOptionsChange,\n      scheduleUpdate,\n      flushUpdates,\n      stackRef\n    } = React.useContext(_NavigationBuilderContext.NavigationBuilderContext);\n    const context = React.useMemo(() => ({\n      navigation,\n      onAction,\n      addListener,\n      addKeyedListener,\n      onRouteFocus,\n      onDispatchAction,\n      onOptionsChange,\n      scheduleUpdate,\n      flushUpdates,\n      stackRef\n    }), [navigation, onAction, addListener, addKeyedListener, onRouteFocus, onDispatchAction, onOptionsChange, scheduleUpdate, flushUpdates, stackRef]);\n    const {\n      base,\n      navigations\n    } = (0, _useNavigationCache.useNavigationCache)({\n      state,\n      getState,\n      navigation,\n      setOptions,\n      router,\n      emitter\n    });\n    const routes = (0, _useRouteCache.useRouteCache)(state.routes);\n    const getOptions = (route, navigation, overrides) => {\n      const config = screens[route.name];\n      const screen = config.props;\n      const optionsList = [\n      // The default `screenOptions` passed to the navigator\n      screenOptions,\n      // The `screenOptions` props passed to `Group` elements\n      ...(config.options ? config.options.filter(Boolean) : []),\n      // The `options` prop passed to `Screen` elements,\n      screen.options,\n      // The options set via `navigation.setOptions`\n      overrides];\n      return optionsList.reduce((acc, curr) => Object.assign(acc,\n      // @ts-expect-error: we check for function but TS still complains\n      typeof curr !== 'function' ? curr : curr({\n        route,\n        navigation,\n        theme\n      })), {});\n    };\n    const render = (route, navigation, customOptions, routeState) => {\n      const config = screens[route.name];\n      const screen = config.props;\n      const clearOptions = () => setOptions(o => {\n        if (route.key in o) {\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const {\n            [route.key]: _,\n            ...rest\n          } = o;\n          return rest;\n        }\n        return o;\n      });\n      const layout =\n      // The `layout` prop passed to `Screen` elements,\n      screen.layout ??\n      // The `screenLayout` props passed to `Group` elements\n      config.layout ??\n      // The default `screenLayout` passed to the navigator\n      screenLayout;\n      let element = /*#__PURE__*/(0, _jsxRuntime.jsx)(_SceneView.SceneView, {\n        navigation: navigation,\n        route: route,\n        screen: screen,\n        routeState: routeState,\n        getState: getState,\n        setState: setState,\n        options: customOptions,\n        clearOptions: clearOptions\n      });\n      if (layout != null) {\n        element = layout({\n          route,\n          navigation,\n          options: customOptions,\n          // @ts-expect-error: in practice `theme` will be defined\n          theme,\n          children: element\n        });\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationBuilderContext.NavigationBuilderContext.Provider, {\n        value: context,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationContext.NavigationContext.Provider, {\n          value: navigation,\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationRouteContext.NavigationRouteContext.Provider, {\n            value: route,\n            children: element\n          })\n        })\n      }, route.key);\n    };\n    const descriptors = routes.reduce((acc, route, i) => {\n      const navigation = navigations[route.key];\n      const customOptions = getOptions(route, navigation, options[route.key]);\n      const element = render(route, navigation, customOptions, state.routes[i].state);\n      acc[route.key] = {\n        route,\n        // @ts-expect-error: it's missing action helpers, fix later\n        navigation,\n        render() {\n          return element;\n        },\n        options: customOptions\n      };\n      return acc;\n    }, {});\n\n    /**\n     * Create a descriptor object for a route.\n     *\n     * @param route Route object for which the descriptor should be created\n     * @param placeholder Whether the descriptor should be a placeholder, e.g. for a route not yet in the state\n     * @returns Descriptor object\n     */\n    const describe = (route, placeholder) => {\n      if (!placeholder) {\n        if (!(route.key in descriptors)) {\n          throw new Error(`Couldn't find a route with the key ${route.key}.`);\n        }\n        return descriptors[route.key];\n      }\n      const navigation = base;\n      const customOptions = getOptions(route, navigation, {});\n      const element = render(route, navigation, customOptions, undefined);\n      return {\n        route,\n        navigation,\n        render() {\n          return element;\n        },\n        options: customOptions\n      };\n    };\n    return {\n      describe,\n      descriptors\n    };\n  }\n});", "lineCount": 193, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useDescriptors"], [7, 24, 1, 13], [7, 27, 1, 13, "useDescriptors"], [7, 41, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationBuilderContext"], [9, 31, 4, 0], [9, 34, 4, 0, "require"], [9, 41, 4, 0], [9, 42, 4, 0, "_dependencyMap"], [9, 56, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationContext"], [10, 24, 5, 0], [10, 27, 5, 0, "require"], [10, 34, 5, 0], [10, 35, 5, 0, "_dependencyMap"], [10, 49, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_NavigationRouteContext"], [11, 29, 6, 0], [11, 32, 6, 0, "require"], [11, 39, 6, 0], [11, 40, 6, 0, "_dependencyMap"], [11, 54, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_SceneView"], [12, 16, 7, 0], [12, 19, 7, 0, "require"], [12, 26, 7, 0], [12, 27, 7, 0, "_dependencyMap"], [12, 41, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_ThemeContext"], [13, 19, 8, 0], [13, 22, 8, 0, "require"], [13, 29, 8, 0], [13, 30, 8, 0, "_dependencyMap"], [13, 44, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_useNavigationCache"], [14, 25, 9, 0], [14, 28, 9, 0, "require"], [14, 35, 9, 0], [14, 36, 9, 0, "_dependencyMap"], [14, 50, 9, 0], [15, 2, 10, 0], [15, 6, 10, 0, "_useRouteCache"], [15, 20, 10, 0], [15, 23, 10, 0, "require"], [15, 30, 10, 0], [15, 31, 10, 0, "_dependencyMap"], [15, 45, 10, 0], [16, 2, 11, 0], [16, 6, 11, 0, "_jsxRuntime"], [16, 17, 11, 0], [16, 20, 11, 0, "require"], [16, 27, 11, 0], [16, 28, 11, 0, "_dependencyMap"], [16, 42, 11, 0], [17, 2, 11, 48], [17, 11, 11, 48, "_interopRequireWildcard"], [17, 35, 11, 48, "e"], [17, 36, 11, 48], [17, 38, 11, 48, "t"], [17, 39, 11, 48], [17, 68, 11, 48, "WeakMap"], [17, 75, 11, 48], [17, 81, 11, 48, "r"], [17, 82, 11, 48], [17, 89, 11, 48, "WeakMap"], [17, 96, 11, 48], [17, 100, 11, 48, "n"], [17, 101, 11, 48], [17, 108, 11, 48, "WeakMap"], [17, 115, 11, 48], [17, 127, 11, 48, "_interopRequireWildcard"], [17, 150, 11, 48], [17, 162, 11, 48, "_interopRequireWildcard"], [17, 163, 11, 48, "e"], [17, 164, 11, 48], [17, 166, 11, 48, "t"], [17, 167, 11, 48], [17, 176, 11, 48, "t"], [17, 177, 11, 48], [17, 181, 11, 48, "e"], [17, 182, 11, 48], [17, 186, 11, 48, "e"], [17, 187, 11, 48], [17, 188, 11, 48, "__esModule"], [17, 198, 11, 48], [17, 207, 11, 48, "e"], [17, 208, 11, 48], [17, 214, 11, 48, "o"], [17, 215, 11, 48], [17, 217, 11, 48, "i"], [17, 218, 11, 48], [17, 220, 11, 48, "f"], [17, 221, 11, 48], [17, 226, 11, 48, "__proto__"], [17, 235, 11, 48], [17, 243, 11, 48, "default"], [17, 250, 11, 48], [17, 252, 11, 48, "e"], [17, 253, 11, 48], [17, 270, 11, 48, "e"], [17, 271, 11, 48], [17, 294, 11, 48, "e"], [17, 295, 11, 48], [17, 320, 11, 48, "e"], [17, 321, 11, 48], [17, 330, 11, 48, "f"], [17, 331, 11, 48], [17, 337, 11, 48, "o"], [17, 338, 11, 48], [17, 341, 11, 48, "t"], [17, 342, 11, 48], [17, 345, 11, 48, "n"], [17, 346, 11, 48], [17, 349, 11, 48, "r"], [17, 350, 11, 48], [17, 358, 11, 48, "o"], [17, 359, 11, 48], [17, 360, 11, 48, "has"], [17, 363, 11, 48], [17, 364, 11, 48, "e"], [17, 365, 11, 48], [17, 375, 11, 48, "o"], [17, 376, 11, 48], [17, 377, 11, 48, "get"], [17, 380, 11, 48], [17, 381, 11, 48, "e"], [17, 382, 11, 48], [17, 385, 11, 48, "o"], [17, 386, 11, 48], [17, 387, 11, 48, "set"], [17, 390, 11, 48], [17, 391, 11, 48, "e"], [17, 392, 11, 48], [17, 394, 11, 48, "f"], [17, 395, 11, 48], [17, 411, 11, 48, "t"], [17, 412, 11, 48], [17, 416, 11, 48, "e"], [17, 417, 11, 48], [17, 433, 11, 48, "t"], [17, 434, 11, 48], [17, 441, 11, 48, "hasOwnProperty"], [17, 455, 11, 48], [17, 456, 11, 48, "call"], [17, 460, 11, 48], [17, 461, 11, 48, "e"], [17, 462, 11, 48], [17, 464, 11, 48, "t"], [17, 465, 11, 48], [17, 472, 11, 48, "i"], [17, 473, 11, 48], [17, 477, 11, 48, "o"], [17, 478, 11, 48], [17, 481, 11, 48, "Object"], [17, 487, 11, 48], [17, 488, 11, 48, "defineProperty"], [17, 502, 11, 48], [17, 507, 11, 48, "Object"], [17, 513, 11, 48], [17, 514, 11, 48, "getOwnPropertyDescriptor"], [17, 538, 11, 48], [17, 539, 11, 48, "e"], [17, 540, 11, 48], [17, 542, 11, 48, "t"], [17, 543, 11, 48], [17, 550, 11, 48, "i"], [17, 551, 11, 48], [17, 552, 11, 48, "get"], [17, 555, 11, 48], [17, 559, 11, 48, "i"], [17, 560, 11, 48], [17, 561, 11, 48, "set"], [17, 564, 11, 48], [17, 568, 11, 48, "o"], [17, 569, 11, 48], [17, 570, 11, 48, "f"], [17, 571, 11, 48], [17, 573, 11, 48, "t"], [17, 574, 11, 48], [17, 576, 11, 48, "i"], [17, 577, 11, 48], [17, 581, 11, 48, "f"], [17, 582, 11, 48], [17, 583, 11, 48, "t"], [17, 584, 11, 48], [17, 588, 11, 48, "e"], [17, 589, 11, 48], [17, 590, 11, 48, "t"], [17, 591, 11, 48], [17, 602, 11, 48, "f"], [17, 603, 11, 48], [17, 608, 11, 48, "e"], [17, 609, 11, 48], [17, 611, 11, 48, "t"], [17, 612, 11, 48], [18, 2, 12, 0], [19, 0, 13, 0], [20, 0, 14, 0], [21, 0, 15, 0], [22, 0, 16, 0], [23, 0, 17, 0], [24, 0, 18, 0], [25, 0, 19, 0], [26, 2, 20, 7], [26, 11, 20, 16, "useDescriptors"], [26, 25, 20, 30, "useDescriptors"], [26, 26, 20, 31], [27, 4, 21, 2, "state"], [27, 9, 21, 7], [28, 4, 22, 2, "screens"], [28, 11, 22, 9], [29, 4, 23, 2, "navigation"], [29, 14, 23, 12], [30, 4, 24, 2, "screenOptions"], [30, 17, 24, 15], [31, 4, 25, 2, "screenLayout"], [31, 16, 25, 14], [32, 4, 26, 2, "onAction"], [32, 12, 26, 10], [33, 4, 27, 2, "getState"], [33, 12, 27, 10], [34, 4, 28, 2, "setState"], [34, 12, 28, 10], [35, 4, 29, 2, "addListener"], [35, 15, 29, 13], [36, 4, 30, 2, "addKeyedListener"], [36, 20, 30, 18], [37, 4, 31, 2, "onRouteFocus"], [37, 16, 31, 14], [38, 4, 32, 2, "router"], [38, 10, 32, 8], [39, 4, 33, 2, "emitter"], [40, 2, 34, 0], [40, 3, 34, 1], [40, 5, 34, 3], [41, 4, 35, 2], [41, 10, 35, 8, "theme"], [41, 15, 35, 13], [41, 18, 35, 16, "React"], [41, 23, 35, 21], [41, 24, 35, 22, "useContext"], [41, 34, 35, 32], [41, 35, 35, 33, "ThemeContext"], [41, 61, 35, 45], [41, 62, 35, 46], [42, 4, 36, 2], [42, 10, 36, 8], [42, 11, 36, 9, "options"], [42, 18, 36, 16], [42, 20, 36, 18, "setOptions"], [42, 30, 36, 28], [42, 31, 36, 29], [42, 34, 36, 32, "React"], [42, 39, 36, 37], [42, 40, 36, 38, "useState"], [42, 48, 36, 46], [42, 49, 36, 47], [42, 50, 36, 48], [42, 51, 36, 49], [42, 52, 36, 50], [43, 4, 37, 2], [43, 10, 37, 8], [44, 6, 38, 4, "onDispatchAction"], [44, 22, 38, 20], [45, 6, 39, 4, "onOptionsChange"], [45, 21, 39, 19], [46, 6, 40, 4, "scheduleUpdate"], [46, 20, 40, 18], [47, 6, 41, 4, "flushUpdates"], [47, 18, 41, 16], [48, 6, 42, 4, "stackRef"], [49, 4, 43, 2], [49, 5, 43, 3], [49, 8, 43, 6, "React"], [49, 13, 43, 11], [49, 14, 43, 12, "useContext"], [49, 24, 43, 22], [49, 25, 43, 23, "NavigationBuilderContext"], [49, 75, 43, 47], [49, 76, 43, 48], [50, 4, 44, 2], [50, 10, 44, 8, "context"], [50, 17, 44, 15], [50, 20, 44, 18, "React"], [50, 25, 44, 23], [50, 26, 44, 24, "useMemo"], [50, 33, 44, 31], [50, 34, 44, 32], [50, 41, 44, 39], [51, 6, 45, 4, "navigation"], [51, 16, 45, 14], [52, 6, 46, 4, "onAction"], [52, 14, 46, 12], [53, 6, 47, 4, "addListener"], [53, 17, 47, 15], [54, 6, 48, 4, "addKeyedListener"], [54, 22, 48, 20], [55, 6, 49, 4, "onRouteFocus"], [55, 18, 49, 16], [56, 6, 50, 4, "onDispatchAction"], [56, 22, 50, 20], [57, 6, 51, 4, "onOptionsChange"], [57, 21, 51, 19], [58, 6, 52, 4, "scheduleUpdate"], [58, 20, 52, 18], [59, 6, 53, 4, "flushUpdates"], [59, 18, 53, 16], [60, 6, 54, 4, "stackRef"], [61, 4, 55, 2], [61, 5, 55, 3], [61, 6, 55, 4], [61, 8, 55, 6], [61, 9, 55, 7, "navigation"], [61, 19, 55, 17], [61, 21, 55, 19, "onAction"], [61, 29, 55, 27], [61, 31, 55, 29, "addListener"], [61, 42, 55, 40], [61, 44, 55, 42, "addKeyedListener"], [61, 60, 55, 58], [61, 62, 55, 60, "onRouteFocus"], [61, 74, 55, 72], [61, 76, 55, 74, "onDispatchAction"], [61, 92, 55, 90], [61, 94, 55, 92, "onOptionsChange"], [61, 109, 55, 107], [61, 111, 55, 109, "scheduleUpdate"], [61, 125, 55, 123], [61, 127, 55, 125, "flushUpdates"], [61, 139, 55, 137], [61, 141, 55, 139, "stackRef"], [61, 149, 55, 147], [61, 150, 55, 148], [61, 151, 55, 149], [62, 4, 56, 2], [62, 10, 56, 8], [63, 6, 57, 4, "base"], [63, 10, 57, 8], [64, 6, 58, 4, "navigations"], [65, 4, 59, 2], [65, 5, 59, 3], [65, 8, 59, 6], [65, 12, 59, 6, "useNavigationCache"], [65, 50, 59, 24], [65, 52, 59, 25], [66, 6, 60, 4, "state"], [66, 11, 60, 9], [67, 6, 61, 4, "getState"], [67, 14, 61, 12], [68, 6, 62, 4, "navigation"], [68, 16, 62, 14], [69, 6, 63, 4, "setOptions"], [69, 16, 63, 14], [70, 6, 64, 4, "router"], [70, 12, 64, 10], [71, 6, 65, 4, "emitter"], [72, 4, 66, 2], [72, 5, 66, 3], [72, 6, 66, 4], [73, 4, 67, 2], [73, 10, 67, 8, "routes"], [73, 16, 67, 14], [73, 19, 67, 17], [73, 23, 67, 17, "useRouteCache"], [73, 51, 67, 30], [73, 53, 67, 31, "state"], [73, 58, 67, 36], [73, 59, 67, 37, "routes"], [73, 65, 67, 43], [73, 66, 67, 44], [74, 4, 68, 2], [74, 10, 68, 8, "getOptions"], [74, 20, 68, 18], [74, 23, 68, 21, "getOptions"], [74, 24, 68, 22, "route"], [74, 29, 68, 27], [74, 31, 68, 29, "navigation"], [74, 41, 68, 39], [74, 43, 68, 41, "overrides"], [74, 52, 68, 50], [74, 57, 68, 55], [75, 6, 69, 4], [75, 12, 69, 10, "config"], [75, 18, 69, 16], [75, 21, 69, 19, "screens"], [75, 28, 69, 26], [75, 29, 69, 27, "route"], [75, 34, 69, 32], [75, 35, 69, 33, "name"], [75, 39, 69, 37], [75, 40, 69, 38], [76, 6, 70, 4], [76, 12, 70, 10, "screen"], [76, 18, 70, 16], [76, 21, 70, 19, "config"], [76, 27, 70, 25], [76, 28, 70, 26, "props"], [76, 33, 70, 31], [77, 6, 71, 4], [77, 12, 71, 10, "optionsList"], [77, 23, 71, 21], [77, 26, 71, 24], [78, 6, 72, 4], [79, 6, 73, 4, "screenOptions"], [79, 19, 73, 17], [80, 6, 74, 4], [81, 6, 75, 4], [81, 10, 75, 8, "config"], [81, 16, 75, 14], [81, 17, 75, 15, "options"], [81, 24, 75, 22], [81, 27, 75, 25, "config"], [81, 33, 75, 31], [81, 34, 75, 32, "options"], [81, 41, 75, 39], [81, 42, 75, 40, "filter"], [81, 48, 75, 46], [81, 49, 75, 47, "Boolean"], [81, 56, 75, 54], [81, 57, 75, 55], [81, 60, 75, 58], [81, 62, 75, 60], [81, 63, 75, 61], [82, 6, 76, 4], [83, 6, 77, 4, "screen"], [83, 12, 77, 10], [83, 13, 77, 11, "options"], [83, 20, 77, 18], [84, 6, 78, 4], [85, 6, 79, 4, "overrides"], [85, 15, 79, 13], [85, 16, 79, 14], [86, 6, 80, 4], [86, 13, 80, 11, "optionsList"], [86, 24, 80, 22], [86, 25, 80, 23, "reduce"], [86, 31, 80, 29], [86, 32, 80, 30], [86, 33, 80, 31, "acc"], [86, 36, 80, 34], [86, 38, 80, 36, "curr"], [86, 42, 80, 40], [86, 47, 80, 45, "Object"], [86, 53, 80, 51], [86, 54, 80, 52, "assign"], [86, 60, 80, 58], [86, 61, 80, 59, "acc"], [86, 64, 80, 62], [87, 6, 81, 4], [88, 6, 82, 4], [88, 13, 82, 11, "curr"], [88, 17, 82, 15], [88, 22, 82, 20], [88, 32, 82, 30], [88, 35, 82, 33, "curr"], [88, 39, 82, 37], [88, 42, 82, 40, "curr"], [88, 46, 82, 44], [88, 47, 82, 45], [89, 8, 83, 6, "route"], [89, 13, 83, 11], [90, 8, 84, 6, "navigation"], [90, 18, 84, 16], [91, 8, 85, 6, "theme"], [92, 6, 86, 4], [92, 7, 86, 5], [92, 8, 86, 6], [92, 9, 86, 7], [92, 11, 86, 9], [92, 12, 86, 10], [92, 13, 86, 11], [92, 14, 86, 12], [93, 4, 87, 2], [93, 5, 87, 3], [94, 4, 88, 2], [94, 10, 88, 8, "render"], [94, 16, 88, 14], [94, 19, 88, 17, "render"], [94, 20, 88, 18, "route"], [94, 25, 88, 23], [94, 27, 88, 25, "navigation"], [94, 37, 88, 35], [94, 39, 88, 37, "customOptions"], [94, 52, 88, 50], [94, 54, 88, 52, "routeState"], [94, 64, 88, 62], [94, 69, 88, 67], [95, 6, 89, 4], [95, 12, 89, 10, "config"], [95, 18, 89, 16], [95, 21, 89, 19, "screens"], [95, 28, 89, 26], [95, 29, 89, 27, "route"], [95, 34, 89, 32], [95, 35, 89, 33, "name"], [95, 39, 89, 37], [95, 40, 89, 38], [96, 6, 90, 4], [96, 12, 90, 10, "screen"], [96, 18, 90, 16], [96, 21, 90, 19, "config"], [96, 27, 90, 25], [96, 28, 90, 26, "props"], [96, 33, 90, 31], [97, 6, 91, 4], [97, 12, 91, 10, "clearOptions"], [97, 24, 91, 22], [97, 27, 91, 25, "clearOptions"], [97, 28, 91, 25], [97, 33, 91, 31, "setOptions"], [97, 43, 91, 41], [97, 44, 91, 42, "o"], [97, 45, 91, 43], [97, 49, 91, 47], [98, 8, 92, 6], [98, 12, 92, 10, "route"], [98, 17, 92, 15], [98, 18, 92, 16, "key"], [98, 21, 92, 19], [98, 25, 92, 23, "o"], [98, 26, 92, 24], [98, 28, 92, 26], [99, 10, 93, 8], [100, 10, 94, 8], [100, 16, 94, 14], [101, 12, 95, 10], [101, 13, 95, 11, "route"], [101, 18, 95, 16], [101, 19, 95, 17, "key"], [101, 22, 95, 20], [101, 25, 95, 23, "_"], [101, 26, 95, 24], [102, 12, 96, 10], [102, 15, 96, 13, "rest"], [103, 10, 97, 8], [103, 11, 97, 9], [103, 14, 97, 12, "o"], [103, 15, 97, 13], [104, 10, 98, 8], [104, 17, 98, 15, "rest"], [104, 21, 98, 19], [105, 8, 99, 6], [106, 8, 100, 6], [106, 15, 100, 13, "o"], [106, 16, 100, 14], [107, 6, 101, 4], [107, 7, 101, 5], [107, 8, 101, 6], [108, 6, 102, 4], [108, 12, 102, 10, "layout"], [108, 18, 102, 16], [109, 6, 103, 4], [110, 6, 104, 4, "screen"], [110, 12, 104, 10], [110, 13, 104, 11, "layout"], [110, 19, 104, 17], [111, 6, 105, 4], [112, 6, 106, 4, "config"], [112, 12, 106, 10], [112, 13, 106, 11, "layout"], [112, 19, 106, 17], [113, 6, 107, 4], [114, 6, 108, 4, "screenLayout"], [114, 18, 108, 16], [115, 6, 109, 4], [115, 10, 109, 8, "element"], [115, 17, 109, 15], [115, 20, 109, 18], [115, 33, 109, 31], [115, 37, 109, 31, "_jsx"], [115, 52, 109, 35], [115, 54, 109, 36, "SceneView"], [115, 74, 109, 45], [115, 76, 109, 47], [116, 8, 110, 6, "navigation"], [116, 18, 110, 16], [116, 20, 110, 18, "navigation"], [116, 30, 110, 28], [117, 8, 111, 6, "route"], [117, 13, 111, 11], [117, 15, 111, 13, "route"], [117, 20, 111, 18], [118, 8, 112, 6, "screen"], [118, 14, 112, 12], [118, 16, 112, 14, "screen"], [118, 22, 112, 20], [119, 8, 113, 6, "routeState"], [119, 18, 113, 16], [119, 20, 113, 18, "routeState"], [119, 30, 113, 28], [120, 8, 114, 6, "getState"], [120, 16, 114, 14], [120, 18, 114, 16, "getState"], [120, 26, 114, 24], [121, 8, 115, 6, "setState"], [121, 16, 115, 14], [121, 18, 115, 16, "setState"], [121, 26, 115, 24], [122, 8, 116, 6, "options"], [122, 15, 116, 13], [122, 17, 116, 15, "customOptions"], [122, 30, 116, 28], [123, 8, 117, 6, "clearOptions"], [123, 20, 117, 18], [123, 22, 117, 20, "clearOptions"], [124, 6, 118, 4], [124, 7, 118, 5], [124, 8, 118, 6], [125, 6, 119, 4], [125, 10, 119, 8, "layout"], [125, 16, 119, 14], [125, 20, 119, 18], [125, 24, 119, 22], [125, 26, 119, 24], [126, 8, 120, 6, "element"], [126, 15, 120, 13], [126, 18, 120, 16, "layout"], [126, 24, 120, 22], [126, 25, 120, 23], [127, 10, 121, 8, "route"], [127, 15, 121, 13], [128, 10, 122, 8, "navigation"], [128, 20, 122, 18], [129, 10, 123, 8, "options"], [129, 17, 123, 15], [129, 19, 123, 17, "customOptions"], [129, 32, 123, 30], [130, 10, 124, 8], [131, 10, 125, 8, "theme"], [131, 15, 125, 13], [132, 10, 126, 8, "children"], [132, 18, 126, 16], [132, 20, 126, 18, "element"], [133, 8, 127, 6], [133, 9, 127, 7], [133, 10, 127, 8], [134, 6, 128, 4], [135, 6, 129, 4], [135, 13, 129, 11], [135, 26, 129, 24], [135, 30, 129, 24, "_jsx"], [135, 45, 129, 28], [135, 47, 129, 29, "NavigationBuilderContext"], [135, 97, 129, 53], [135, 98, 129, 54, "Provider"], [135, 106, 129, 62], [135, 108, 129, 64], [136, 8, 130, 6, "value"], [136, 13, 130, 11], [136, 15, 130, 13, "context"], [136, 22, 130, 20], [137, 8, 131, 6, "children"], [137, 16, 131, 14], [137, 18, 131, 16], [137, 31, 131, 29], [137, 35, 131, 29, "_jsx"], [137, 50, 131, 33], [137, 52, 131, 34, "NavigationContext"], [137, 88, 131, 51], [137, 89, 131, 52, "Provider"], [137, 97, 131, 60], [137, 99, 131, 62], [138, 10, 132, 8, "value"], [138, 15, 132, 13], [138, 17, 132, 15, "navigation"], [138, 27, 132, 25], [139, 10, 133, 8, "children"], [139, 18, 133, 16], [139, 20, 133, 18], [139, 33, 133, 31], [139, 37, 133, 31, "_jsx"], [139, 52, 133, 35], [139, 54, 133, 36, "NavigationRouteContext"], [139, 100, 133, 58], [139, 101, 133, 59, "Provider"], [139, 109, 133, 67], [139, 111, 133, 69], [140, 12, 134, 10, "value"], [140, 17, 134, 15], [140, 19, 134, 17, "route"], [140, 24, 134, 22], [141, 12, 135, 10, "children"], [141, 20, 135, 18], [141, 22, 135, 20, "element"], [142, 10, 136, 8], [142, 11, 136, 9], [143, 8, 137, 6], [143, 9, 137, 7], [144, 6, 138, 4], [144, 7, 138, 5], [144, 9, 138, 7, "route"], [144, 14, 138, 12], [144, 15, 138, 13, "key"], [144, 18, 138, 16], [144, 19, 138, 17], [145, 4, 139, 2], [145, 5, 139, 3], [146, 4, 140, 2], [146, 10, 140, 8, "descriptors"], [146, 21, 140, 19], [146, 24, 140, 22, "routes"], [146, 30, 140, 28], [146, 31, 140, 29, "reduce"], [146, 37, 140, 35], [146, 38, 140, 36], [146, 39, 140, 37, "acc"], [146, 42, 140, 40], [146, 44, 140, 42, "route"], [146, 49, 140, 47], [146, 51, 140, 49, "i"], [146, 52, 140, 50], [146, 57, 140, 55], [147, 6, 141, 4], [147, 12, 141, 10, "navigation"], [147, 22, 141, 20], [147, 25, 141, 23, "navigations"], [147, 36, 141, 34], [147, 37, 141, 35, "route"], [147, 42, 141, 40], [147, 43, 141, 41, "key"], [147, 46, 141, 44], [147, 47, 141, 45], [148, 6, 142, 4], [148, 12, 142, 10, "customOptions"], [148, 25, 142, 23], [148, 28, 142, 26, "getOptions"], [148, 38, 142, 36], [148, 39, 142, 37, "route"], [148, 44, 142, 42], [148, 46, 142, 44, "navigation"], [148, 56, 142, 54], [148, 58, 142, 56, "options"], [148, 65, 142, 63], [148, 66, 142, 64, "route"], [148, 71, 142, 69], [148, 72, 142, 70, "key"], [148, 75, 142, 73], [148, 76, 142, 74], [148, 77, 142, 75], [149, 6, 143, 4], [149, 12, 143, 10, "element"], [149, 19, 143, 17], [149, 22, 143, 20, "render"], [149, 28, 143, 26], [149, 29, 143, 27, "route"], [149, 34, 143, 32], [149, 36, 143, 34, "navigation"], [149, 46, 143, 44], [149, 48, 143, 46, "customOptions"], [149, 61, 143, 59], [149, 63, 143, 61, "state"], [149, 68, 143, 66], [149, 69, 143, 67, "routes"], [149, 75, 143, 73], [149, 76, 143, 74, "i"], [149, 77, 143, 75], [149, 78, 143, 76], [149, 79, 143, 77, "state"], [149, 84, 143, 82], [149, 85, 143, 83], [150, 6, 144, 4, "acc"], [150, 9, 144, 7], [150, 10, 144, 8, "route"], [150, 15, 144, 13], [150, 16, 144, 14, "key"], [150, 19, 144, 17], [150, 20, 144, 18], [150, 23, 144, 21], [151, 8, 145, 6, "route"], [151, 13, 145, 11], [152, 8, 146, 6], [153, 8, 147, 6, "navigation"], [153, 18, 147, 16], [154, 8, 148, 6, "render"], [154, 14, 148, 12, "render"], [154, 15, 148, 12], [154, 17, 148, 15], [155, 10, 149, 8], [155, 17, 149, 15, "element"], [155, 24, 149, 22], [156, 8, 150, 6], [156, 9, 150, 7], [157, 8, 151, 6, "options"], [157, 15, 151, 13], [157, 17, 151, 15, "customOptions"], [158, 6, 152, 4], [158, 7, 152, 5], [159, 6, 153, 4], [159, 13, 153, 11, "acc"], [159, 16, 153, 14], [160, 4, 154, 2], [160, 5, 154, 3], [160, 7, 154, 5], [160, 8, 154, 6], [160, 9, 154, 7], [160, 10, 154, 8], [162, 4, 156, 2], [163, 0, 157, 0], [164, 0, 158, 0], [165, 0, 159, 0], [166, 0, 160, 0], [167, 0, 161, 0], [168, 0, 162, 0], [169, 4, 163, 2], [169, 10, 163, 8, "describe"], [169, 18, 163, 16], [169, 21, 163, 19, "describe"], [169, 22, 163, 20, "route"], [169, 27, 163, 25], [169, 29, 163, 27, "placeholder"], [169, 40, 163, 38], [169, 45, 163, 43], [170, 6, 164, 4], [170, 10, 164, 8], [170, 11, 164, 9, "placeholder"], [170, 22, 164, 20], [170, 24, 164, 22], [171, 8, 165, 6], [171, 12, 165, 10], [171, 14, 165, 12, "route"], [171, 19, 165, 17], [171, 20, 165, 18, "key"], [171, 23, 165, 21], [171, 27, 165, 25, "descriptors"], [171, 38, 165, 36], [171, 39, 165, 37], [171, 41, 165, 39], [172, 10, 166, 8], [172, 16, 166, 14], [172, 20, 166, 18, "Error"], [172, 25, 166, 23], [172, 26, 166, 24], [172, 64, 166, 62, "route"], [172, 69, 166, 67], [172, 70, 166, 68, "key"], [172, 73, 166, 71], [172, 76, 166, 74], [172, 77, 166, 75], [173, 8, 167, 6], [174, 8, 168, 6], [174, 15, 168, 13, "descriptors"], [174, 26, 168, 24], [174, 27, 168, 25, "route"], [174, 32, 168, 30], [174, 33, 168, 31, "key"], [174, 36, 168, 34], [174, 37, 168, 35], [175, 6, 169, 4], [176, 6, 170, 4], [176, 12, 170, 10, "navigation"], [176, 22, 170, 20], [176, 25, 170, 23, "base"], [176, 29, 170, 27], [177, 6, 171, 4], [177, 12, 171, 10, "customOptions"], [177, 25, 171, 23], [177, 28, 171, 26, "getOptions"], [177, 38, 171, 36], [177, 39, 171, 37, "route"], [177, 44, 171, 42], [177, 46, 171, 44, "navigation"], [177, 56, 171, 54], [177, 58, 171, 56], [177, 59, 171, 57], [177, 60, 171, 58], [177, 61, 171, 59], [178, 6, 172, 4], [178, 12, 172, 10, "element"], [178, 19, 172, 17], [178, 22, 172, 20, "render"], [178, 28, 172, 26], [178, 29, 172, 27, "route"], [178, 34, 172, 32], [178, 36, 172, 34, "navigation"], [178, 46, 172, 44], [178, 48, 172, 46, "customOptions"], [178, 61, 172, 59], [178, 63, 172, 61, "undefined"], [178, 72, 172, 70], [178, 73, 172, 71], [179, 6, 173, 4], [179, 13, 173, 11], [180, 8, 174, 6, "route"], [180, 13, 174, 11], [181, 8, 175, 6, "navigation"], [181, 18, 175, 16], [182, 8, 176, 6, "render"], [182, 14, 176, 12, "render"], [182, 15, 176, 12], [182, 17, 176, 15], [183, 10, 177, 8], [183, 17, 177, 15, "element"], [183, 24, 177, 22], [184, 8, 178, 6], [184, 9, 178, 7], [185, 8, 179, 6, "options"], [185, 15, 179, 13], [185, 17, 179, 15, "customOptions"], [186, 6, 180, 4], [186, 7, 180, 5], [187, 4, 181, 2], [187, 5, 181, 3], [188, 4, 182, 2], [188, 11, 182, 9], [189, 6, 183, 4, "describe"], [189, 14, 183, 12], [190, 6, 184, 4, "descriptors"], [191, 4, 185, 2], [191, 5, 185, 3], [192, 2, 186, 0], [193, 0, 186, 1], [193, 3]], "functionMap": {"names": ["<global>", "useDescriptors", "React.useMemo$argument_0", "getOptions", "optionsList.reduce$argument_0", "render", "clearOptions", "setOptions$argument_0", "routes.reduce$argument_0", "acc.route.key.render", "describe"], "mappings": "AAA;OCmB;gCCwB;IDW;qBEa;8BCY;ODM;GFC;iBIC;yBCG,iBC;KDU,CD;GJsC;oCOC;MCQ;ODE;GPI;mBSS;MLa;OKE;GTG;CDK"}}, "type": "js/module"}]}