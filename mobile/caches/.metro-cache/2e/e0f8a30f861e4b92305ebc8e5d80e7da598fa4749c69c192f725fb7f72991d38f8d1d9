{"dependencies": [{"name": "hyphenate-style-name", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 26, "index": 147}, "end": {"line": 8, "column": 57, "index": 178}}], "key": "gns51hvwZV1lCo6loDE4vPDhwcs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports[\"default\"] = hyphenateProperty;\n  var _hyphenateStyleName = require(_dependencyMap[0], \"hyphenate-style-name\");\n  var _hyphenateStyleName2 = _interopRequireDefault(_hyphenateStyleName);\n  function _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n      \"default\": obj\n    };\n  }\n  function hyphenateProperty(property) {\n    return (0, _hyphenateStyleName2[\"default\"])(property);\n  }\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8], [7, 19, 6, 17], [7, 20, 6, 18], [7, 23, 6, 21, "hyphenateProperty"], [7, 40, 6, 38], [8, 2, 8, 0], [8, 6, 8, 4, "_hyphenateStyleName"], [8, 25, 8, 23], [8, 28, 8, 26, "require"], [8, 35, 8, 33], [8, 36, 8, 33, "_dependencyMap"], [8, 50, 8, 33], [8, 77, 8, 56], [8, 78, 8, 57], [9, 2, 10, 0], [9, 6, 10, 4, "_hyphenateStyleName2"], [9, 26, 10, 24], [9, 29, 10, 27, "_interopRequireDefault"], [9, 51, 10, 49], [9, 52, 10, 50, "_hyphenateStyleName"], [9, 71, 10, 69], [9, 72, 10, 70], [10, 2, 12, 0], [10, 11, 12, 9, "_interopRequireDefault"], [10, 33, 12, 31, "_interopRequireDefault"], [10, 34, 12, 32, "obj"], [10, 37, 12, 35], [10, 39, 12, 37], [11, 4, 12, 39], [11, 11, 12, 46, "obj"], [11, 14, 12, 49], [11, 18, 12, 53, "obj"], [11, 21, 12, 56], [11, 22, 12, 57, "__esModule"], [11, 32, 12, 67], [11, 35, 12, 70, "obj"], [11, 38, 12, 73], [11, 41, 12, 76], [12, 6, 12, 78], [12, 15, 12, 87], [12, 17, 12, 89, "obj"], [13, 4, 12, 93], [13, 5, 12, 94], [14, 2, 12, 96], [15, 2, 14, 0], [15, 11, 14, 9, "hyphenateProperty"], [15, 28, 14, 26, "hyphenateProperty"], [15, 29, 14, 27, "property"], [15, 37, 14, 35], [15, 39, 14, 37], [16, 4, 15, 2], [16, 11, 15, 9], [16, 12, 15, 10], [16, 13, 15, 11], [16, 15, 15, 13, "_hyphenateStyleName2"], [16, 35, 15, 33], [16, 36, 15, 34], [16, 45, 15, 43], [16, 46, 15, 44], [16, 48, 15, 46, "property"], [16, 56, 15, 54], [16, 57, 15, 55], [17, 2, 16, 0], [18, 0, 16, 1], [18, 3]], "functionMap": {"names": ["<global>", "_interopRequireDefault", "hyphenateProperty"], "mappings": "AAA;ACW,iGD;AEE"}}, "type": "js/module"}]}