{"dependencies": [{"name": "./AnimatedExports", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 35}, "end": {"line": 16, "column": 63}}], "key": "O1kc6O7JZXbnEY45dzbhMyHbR1E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var Animated = require(_dependencyMap[0], \"./AnimatedExports\").default;\n  var _default = exports.default = Animated;\n});", "lineCount": 8, "map": [[6, 2, 16, 0], [6, 6, 16, 6, "Animated"], [6, 14, 16, 31], [6, 17, 16, 35, "require"], [6, 24, 16, 42], [6, 25, 16, 42, "_dependencyMap"], [6, 39, 16, 42], [6, 63, 16, 62], [6, 64, 16, 63], [6, 65, 17, 3, "default"], [6, 72, 17, 10], [7, 2, 17, 11], [7, 6, 17, 11, "_default"], [7, 14, 17, 11], [7, 17, 17, 11, "exports"], [7, 24, 17, 11], [7, 25, 17, 11, "default"], [7, 32, 17, 11], [7, 35, 19, 15, "Animated"], [7, 43, 19, 23], [8, 0, 19, 23], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}