{"dependencies": [{"name": "./TabsClient", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 37, "index": 276}, "end": {"line": 7, "column": 60, "index": 299}}], "key": "g+q/Ef6XbPG8bWN/s7u5GEXoTuk=", "exportNames": ["*"]}}, {"name": "../views/Screen", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 17, "index": 356}, "end": {"line": 9, "column": 43, "index": 382}}], "key": "CvVDy33sFAANwe0yWc+ZvurKuCc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Tabs = void 0;\n  const TabsClient_1 = __importDefault(require(_dependencyMap[0], \"./TabsClient\"));\n  exports.Tabs = TabsClient_1.default;\n  const Screen_1 = require(_dependencyMap[1], \"../views/Screen\");\n  TabsClient_1.default.Screen = Screen_1.Screen;\n  exports.default = TabsClient_1.default;\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__importDefault"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__importDefault"], [4, 52, 2, 51], [4, 56, 2, 56], [4, 66, 2, 66, "mod"], [4, 69, 2, 69], [4, 71, 2, 71], [5, 4, 3, 4], [5, 11, 3, 12, "mod"], [5, 14, 3, 15], [5, 18, 3, 19, "mod"], [5, 21, 3, 22], [5, 22, 3, 23, "__esModule"], [5, 32, 3, 33], [5, 35, 3, 37, "mod"], [5, 38, 3, 40], [5, 41, 3, 43], [6, 6, 3, 45], [6, 15, 3, 54], [6, 17, 3, 56, "mod"], [7, 4, 3, 60], [7, 5, 3, 61], [8, 2, 4, 0], [8, 3, 4, 1], [9, 2, 5, 0, "Object"], [9, 8, 5, 6], [9, 9, 5, 7, "defineProperty"], [9, 23, 5, 21], [9, 24, 5, 22, "exports"], [9, 31, 5, 29], [9, 33, 5, 31], [9, 45, 5, 43], [9, 47, 5, 45], [10, 4, 5, 47, "value"], [10, 9, 5, 52], [10, 11, 5, 54], [11, 2, 5, 59], [11, 3, 5, 60], [11, 4, 5, 61], [12, 2, 6, 0, "exports"], [12, 9, 6, 7], [12, 10, 6, 8, "Tabs"], [12, 14, 6, 12], [12, 17, 6, 15], [12, 22, 6, 20], [12, 23, 6, 21], [13, 2, 7, 0], [13, 8, 7, 6, "TabsClient_1"], [13, 20, 7, 18], [13, 23, 7, 21, "__importDefault"], [13, 38, 7, 36], [13, 39, 7, 37, "require"], [13, 46, 7, 44], [13, 47, 7, 44, "_dependencyMap"], [13, 61, 7, 44], [13, 80, 7, 59], [13, 81, 7, 60], [13, 82, 7, 61], [14, 2, 8, 0, "exports"], [14, 9, 8, 7], [14, 10, 8, 8, "Tabs"], [14, 14, 8, 12], [14, 17, 8, 15, "TabsClient_1"], [14, 29, 8, 27], [14, 30, 8, 28, "default"], [14, 37, 8, 35], [15, 2, 9, 0], [15, 8, 9, 6, "Screen_1"], [15, 16, 9, 14], [15, 19, 9, 17, "require"], [15, 26, 9, 24], [15, 27, 9, 24, "_dependencyMap"], [15, 41, 9, 24], [15, 63, 9, 42], [15, 64, 9, 43], [16, 2, 10, 0, "TabsClient_1"], [16, 14, 10, 12], [16, 15, 10, 13, "default"], [16, 22, 10, 20], [16, 23, 10, 21, "Screen"], [16, 29, 10, 27], [16, 32, 10, 30, "Screen_1"], [16, 40, 10, 38], [16, 41, 10, 39, "Screen"], [16, 47, 10, 45], [17, 2, 11, 0, "exports"], [17, 9, 11, 7], [17, 10, 11, 8, "default"], [17, 17, 11, 15], [17, 20, 11, 18, "TabsClient_1"], [17, 32, 11, 30], [17, 33, 11, 31, "default"], [17, 40, 11, 38], [18, 0, 11, 39], [18, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;wDCC;CDE"}}, "type": "js/module"}]}