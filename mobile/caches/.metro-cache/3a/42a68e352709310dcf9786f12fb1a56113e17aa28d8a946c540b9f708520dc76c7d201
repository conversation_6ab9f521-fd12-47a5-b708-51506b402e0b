{"dependencies": [{"name": "../../src/private/specs_DEPRECATED/modules/NativeNetworkingIOS", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 79}}], "key": "gSIzN7s693gJwfO5b0bzEYzJcX0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {};\n  exports.default = void 0;\n  var _NativeNetworkingIOS = _interopRequireWildcard(require(_dependencyMap[0], \"../../src/private/specs_DEPRECATED/modules/NativeNetworkingIOS\"));\n  Object.keys(_NativeNetworkingIOS).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _NativeNetworkingIOS[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _NativeNetworkingIOS[key];\n      }\n    });\n  });\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _default = exports.default = _NativeNetworkingIOS.default;\n});", "lineCount": 21, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeNetworkingIOS"], [7, 26, 11, 0], [7, 29, 11, 0, "_interopRequireWildcard"], [7, 52, 11, 0], [7, 53, 11, 0, "require"], [7, 60, 11, 0], [7, 61, 11, 0, "_dependencyMap"], [7, 75, 11, 0], [8, 2, 11, 0, "Object"], [8, 8, 11, 0], [8, 9, 11, 0, "keys"], [8, 13, 11, 0], [8, 14, 11, 0, "_NativeNetworkingIOS"], [8, 34, 11, 0], [8, 36, 11, 0, "for<PERSON>ach"], [8, 43, 11, 0], [8, 54, 11, 0, "key"], [8, 57, 11, 0], [9, 4, 11, 0], [9, 8, 11, 0, "key"], [9, 11, 11, 0], [9, 29, 11, 0, "key"], [9, 32, 11, 0], [10, 4, 11, 0], [10, 8, 11, 0, "Object"], [10, 14, 11, 0], [10, 15, 11, 0, "prototype"], [10, 24, 11, 0], [10, 25, 11, 0, "hasOwnProperty"], [10, 39, 11, 0], [10, 40, 11, 0, "call"], [10, 44, 11, 0], [10, 45, 11, 0, "_exportNames"], [10, 57, 11, 0], [10, 59, 11, 0, "key"], [10, 62, 11, 0], [11, 4, 11, 0], [11, 8, 11, 0, "key"], [11, 11, 11, 0], [11, 15, 11, 0, "exports"], [11, 22, 11, 0], [11, 26, 11, 0, "exports"], [11, 33, 11, 0], [11, 34, 11, 0, "key"], [11, 37, 11, 0], [11, 43, 11, 0, "_NativeNetworkingIOS"], [11, 63, 11, 0], [11, 64, 11, 0, "key"], [11, 67, 11, 0], [12, 4, 11, 0, "Object"], [12, 10, 11, 0], [12, 11, 11, 0, "defineProperty"], [12, 25, 11, 0], [12, 26, 11, 0, "exports"], [12, 33, 11, 0], [12, 35, 11, 0, "key"], [12, 38, 11, 0], [13, 6, 11, 0, "enumerable"], [13, 16, 11, 0], [14, 6, 11, 0, "get"], [14, 9, 11, 0], [14, 20, 11, 0, "get"], [14, 21, 11, 0], [15, 8, 11, 0], [15, 15, 11, 0, "_NativeNetworkingIOS"], [15, 35, 11, 0], [15, 36, 11, 0, "key"], [15, 39, 11, 0], [16, 6, 11, 0], [17, 4, 11, 0], [18, 2, 11, 0], [19, 2, 11, 79], [19, 11, 11, 79, "_interopRequireWildcard"], [19, 35, 11, 79, "e"], [19, 36, 11, 79], [19, 38, 11, 79, "t"], [19, 39, 11, 79], [19, 68, 11, 79, "WeakMap"], [19, 75, 11, 79], [19, 81, 11, 79, "r"], [19, 82, 11, 79], [19, 89, 11, 79, "WeakMap"], [19, 96, 11, 79], [19, 100, 11, 79, "n"], [19, 101, 11, 79], [19, 108, 11, 79, "WeakMap"], [19, 115, 11, 79], [19, 127, 11, 79, "_interopRequireWildcard"], [19, 150, 11, 79], [19, 162, 11, 79, "_interopRequireWildcard"], [19, 163, 11, 79, "e"], [19, 164, 11, 79], [19, 166, 11, 79, "t"], [19, 167, 11, 79], [19, 176, 11, 79, "t"], [19, 177, 11, 79], [19, 181, 11, 79, "e"], [19, 182, 11, 79], [19, 186, 11, 79, "e"], [19, 187, 11, 79], [19, 188, 11, 79, "__esModule"], [19, 198, 11, 79], [19, 207, 11, 79, "e"], [19, 208, 11, 79], [19, 214, 11, 79, "o"], [19, 215, 11, 79], [19, 217, 11, 79, "i"], [19, 218, 11, 79], [19, 220, 11, 79, "f"], [19, 221, 11, 79], [19, 226, 11, 79, "__proto__"], [19, 235, 11, 79], [19, 243, 11, 79, "default"], [19, 250, 11, 79], [19, 252, 11, 79, "e"], [19, 253, 11, 79], [19, 270, 11, 79, "e"], [19, 271, 11, 79], [19, 294, 11, 79, "e"], [19, 295, 11, 79], [19, 320, 11, 79, "e"], [19, 321, 11, 79], [19, 330, 11, 79, "f"], [19, 331, 11, 79], [19, 337, 11, 79, "o"], [19, 338, 11, 79], [19, 341, 11, 79, "t"], [19, 342, 11, 79], [19, 345, 11, 79, "n"], [19, 346, 11, 79], [19, 349, 11, 79, "r"], [19, 350, 11, 79], [19, 358, 11, 79, "o"], [19, 359, 11, 79], [19, 360, 11, 79, "has"], [19, 363, 11, 79], [19, 364, 11, 79, "e"], [19, 365, 11, 79], [19, 375, 11, 79, "o"], [19, 376, 11, 79], [19, 377, 11, 79, "get"], [19, 380, 11, 79], [19, 381, 11, 79, "e"], [19, 382, 11, 79], [19, 385, 11, 79, "o"], [19, 386, 11, 79], [19, 387, 11, 79, "set"], [19, 390, 11, 79], [19, 391, 11, 79, "e"], [19, 392, 11, 79], [19, 394, 11, 79, "f"], [19, 395, 11, 79], [19, 409, 11, 79, "_t"], [19, 411, 11, 79], [19, 415, 11, 79, "e"], [19, 416, 11, 79], [19, 432, 11, 79, "_t"], [19, 434, 11, 79], [19, 441, 11, 79, "hasOwnProperty"], [19, 455, 11, 79], [19, 456, 11, 79, "call"], [19, 460, 11, 79], [19, 461, 11, 79, "e"], [19, 462, 11, 79], [19, 464, 11, 79, "_t"], [19, 466, 11, 79], [19, 473, 11, 79, "i"], [19, 474, 11, 79], [19, 478, 11, 79, "o"], [19, 479, 11, 79], [19, 482, 11, 79, "Object"], [19, 488, 11, 79], [19, 489, 11, 79, "defineProperty"], [19, 503, 11, 79], [19, 508, 11, 79, "Object"], [19, 514, 11, 79], [19, 515, 11, 79, "getOwnPropertyDescriptor"], [19, 539, 11, 79], [19, 540, 11, 79, "e"], [19, 541, 11, 79], [19, 543, 11, 79, "_t"], [19, 545, 11, 79], [19, 552, 11, 79, "i"], [19, 553, 11, 79], [19, 554, 11, 79, "get"], [19, 557, 11, 79], [19, 561, 11, 79, "i"], [19, 562, 11, 79], [19, 563, 11, 79, "set"], [19, 566, 11, 79], [19, 570, 11, 79, "o"], [19, 571, 11, 79], [19, 572, 11, 79, "f"], [19, 573, 11, 79], [19, 575, 11, 79, "_t"], [19, 577, 11, 79], [19, 579, 11, 79, "i"], [19, 580, 11, 79], [19, 584, 11, 79, "f"], [19, 585, 11, 79], [19, 586, 11, 79, "_t"], [19, 588, 11, 79], [19, 592, 11, 79, "e"], [19, 593, 11, 79], [19, 594, 11, 79, "_t"], [19, 596, 11, 79], [19, 607, 11, 79, "f"], [19, 608, 11, 79], [19, 613, 11, 79, "e"], [19, 614, 11, 79], [19, 616, 11, 79, "t"], [19, 617, 11, 79], [20, 2, 11, 79], [20, 6, 11, 79, "_default"], [20, 14, 11, 79], [20, 17, 11, 79, "exports"], [20, 24, 11, 79], [20, 25, 11, 79, "default"], [20, 32, 11, 79], [20, 35, 13, 15, "NativeNetworkingIOS"], [20, 63, 13, 34], [21, 0, 13, 34], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}