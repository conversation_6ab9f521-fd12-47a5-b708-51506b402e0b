{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = ErrorToastContainer;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[2], \"react-native-css-interop/jsx-runtime\");\n  function ErrorToastContainer(_ref) {\n    var children = _ref.children;\n    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {\n      children: children\n    });\n  }\n});", "lineCount": 15, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 1, 26], [8, 6, 1, 26, "_jsxRuntime"], [8, 17, 1, 26], [8, 20, 1, 26, "require"], [8, 27, 1, 26], [8, 28, 1, 26, "_dependencyMap"], [8, 42, 1, 26], [9, 2, 3, 15], [9, 11, 3, 24, "ErrorToastContainer"], [9, 30, 3, 43, "ErrorToastContainer"], [9, 31, 3, 43, "_ref"], [9, 35, 3, 43], [9, 37, 7, 23], [10, 4, 7, 23], [10, 8, 4, 2, "children"], [10, 16, 4, 10], [10, 19, 4, 10, "_ref"], [10, 23, 4, 10], [10, 24, 4, 2, "children"], [10, 32, 4, 10], [11, 4, 8, 2], [11, 11, 8, 9], [11, 15, 8, 9, "_jsxRuntime"], [11, 26, 8, 9], [11, 27, 8, 9, "jsx"], [11, 30, 8, 9], [11, 32, 8, 9, "_jsxRuntime"], [11, 43, 8, 9], [11, 44, 8, 9, "Fragment"], [11, 52, 8, 9], [12, 6, 8, 9, "children"], [12, 14, 8, 9], [12, 16, 8, 12, "children"], [13, 4, 8, 20], [13, 5, 8, 23], [13, 6, 8, 24], [14, 2, 9, 0], [15, 0, 9, 1], [15, 3]], "functionMap": {"names": ["<global>", "ErrorToastContainer"], "mappings": "AAA;eCE;CDM"}}, "type": "js/module"}]}