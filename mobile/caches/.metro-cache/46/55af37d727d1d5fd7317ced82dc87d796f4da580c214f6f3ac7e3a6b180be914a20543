{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2Fnode_modules%2Fexpo-router%2Fassets\",\n    \"width\": 96,\n    \"height\": 88,\n    \"scales\": [1],\n    \"hash\": \"0fb94560a2bb19eb54892e497fb29c67\",\n    \"name\": \"logotype\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"0fb94560a2bb19eb54892e497fb29c67\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 90, 1, 110], [5, 4, 1, 111], [5, 11, 1, 118], [5, 13, 1, 119], [5, 15, 1, 121], [6, 4, 1, 122], [6, 12, 1, 130], [6, 14, 1, 131], [6, 16, 1, 133], [7, 4, 1, 134], [7, 12, 1, 142], [7, 14, 1, 143], [7, 15, 1, 144], [7, 16, 1, 145], [7, 17, 1, 146], [8, 4, 1, 147], [8, 10, 1, 153], [8, 12, 1, 154], [8, 46, 1, 188], [9, 4, 1, 189], [9, 10, 1, 195], [9, 12, 1, 196], [9, 22, 1, 206], [10, 4, 1, 207], [10, 10, 1, 213], [10, 12, 1, 214], [10, 17, 1, 219], [11, 4, 1, 220], [11, 16, 1, 232], [11, 18, 1, 233], [11, 19, 1, 234], [11, 53, 1, 268], [12, 2, 1, 269], [12, 3, 1, 270], [13, 0, 1, 270], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}