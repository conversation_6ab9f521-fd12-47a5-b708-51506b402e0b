{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getUseOfValueInStyleWarning = getUseOfValueInStyleWarning;\n  function getUseOfValueInStyleWarning() {\n    return \"It looks like you might be using shared value's .value inside reanimated inline style. \" + 'If you want a component to update when shared value changes you should use the shared value' + ' directly instead of its current state represented by `.value`. See documentation here: ' + 'https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary/#animations-in-inline-styling';\n  }\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getUseOfValueInStyleWarning"], [7, 37, 1, 13], [7, 40, 1, 13, "getUseOfValueInStyleWarning"], [7, 67, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "getUseOfValueInStyleWarning"], [8, 38, 3, 43, "getUseOfValueInStyleWarning"], [8, 39, 3, 43], [8, 41, 3, 46], [9, 4, 4, 2], [9, 11, 4, 9], [9, 100, 4, 98], [9, 103, 4, 101], [9, 196, 4, 194], [9, 199, 4, 197], [9, 289, 4, 287], [9, 292, 4, 290], [9, 401, 4, 399], [10, 2, 5, 0], [11, 0, 5, 1], [11, 3]], "functionMap": {"names": ["<global>", "getUseOfValueInStyleWarning"], "mappings": "AAA;OCE;CDE"}}, "type": "js/module"}]}