{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../canUseDom", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 214}, "end": {"line": 12, "column": 37, "index": 251}}], "key": "NAgv5vx4h/J1uxoLSWxqZG/Z81Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.addEventListener = addEventListener;\n  var _canUseDom = _interopRequireDefault(require(_dependencyMap[1], \"../canUseDom\"));\n  var emptyFunction = () => {};\n  function supportsPassiveEvents() {\n    var supported = false;\n    // Check if browser supports event with passive listeners\n    // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n    if (_canUseDom.default) {\n      try {\n        var options = {};\n        Object.defineProperty(options, 'passive', {\n          get() {\n            supported = true;\n            return false;\n          }\n        });\n        window.addEventListener('test', null, options);\n        window.removeEventListener('test', null, options);\n      } catch (e) {}\n    }\n    return supported;\n  }\n  var canUsePassiveEvents = supportsPassiveEvents();\n  function getOptions(options) {\n    if (options == null) {\n      return false;\n    }\n    return canUsePassiveEvents ? options : Boolean(options.capture);\n  }\n\n  /**\n   * Shim generic API compatibility with ReactDOM's synthetic events, without needing the\n   * large amount of code ReactDOM uses to do this. Ideally we wouldn't use a synthetic\n   * event wrapper at all.\n   */\n  function isPropagationStopped() {\n    return this.cancelBubble;\n  }\n  function isDefaultPrevented() {\n    return this.defaultPrevented;\n  }\n  function normalizeEvent(event) {\n    event.nativeEvent = event;\n    event.persist = emptyFunction;\n    event.isDefaultPrevented = isDefaultPrevented;\n    event.isPropagationStopped = isPropagationStopped;\n    return event;\n  }\n\n  /**\n   *\n   */\n  function addEventListener(target, type, listener, options) {\n    var opts = getOptions(options);\n    var compatListener = e => listener(normalizeEvent(e));\n    target.addEventListener(type, compatListener, opts);\n    return function removeEventListener() {\n      if (target != null) {\n        target.removeEventListener(type, compatListener, opts);\n      }\n    };\n  }\n});", "lineCount": 79, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [11, 2, 10, 0], [11, 14, 10, 12], [13, 2, 10, 13], [13, 6, 10, 13, "_interopRequireDefault"], [13, 28, 10, 13], [13, 31, 10, 13, "require"], [13, 38, 10, 13], [13, 39, 10, 13, "_dependencyMap"], [13, 53, 10, 13], [14, 2, 10, 13, "Object"], [14, 8, 10, 13], [14, 9, 10, 13, "defineProperty"], [14, 23, 10, 13], [14, 24, 10, 13, "exports"], [14, 31, 10, 13], [15, 4, 10, 13, "value"], [15, 9, 10, 13], [16, 2, 10, 13], [17, 2, 10, 13, "exports"], [17, 9, 10, 13], [17, 10, 10, 13, "addEventListener"], [17, 26, 10, 13], [17, 29, 10, 13, "addEventListener"], [17, 45, 10, 13], [18, 2, 12, 0], [18, 6, 12, 0, "_canUseDom"], [18, 16, 12, 0], [18, 19, 12, 0, "_interopRequireDefault"], [18, 41, 12, 0], [18, 42, 12, 0, "require"], [18, 49, 12, 0], [18, 50, 12, 0, "_dependencyMap"], [18, 64, 12, 0], [19, 2, 13, 0], [19, 6, 13, 4, "emptyFunction"], [19, 19, 13, 17], [19, 22, 13, 20, "emptyFunction"], [19, 23, 13, 20], [19, 28, 13, 26], [19, 29, 13, 27], [19, 30, 13, 28], [20, 2, 14, 0], [20, 11, 14, 9, "supportsPassiveEvents"], [20, 32, 14, 30, "supportsPassiveEvents"], [20, 33, 14, 30], [20, 35, 14, 33], [21, 4, 15, 2], [21, 8, 15, 6, "supported"], [21, 17, 15, 15], [21, 20, 15, 18], [21, 25, 15, 23], [22, 4, 16, 2], [23, 4, 17, 2], [24, 4, 18, 2], [24, 8, 18, 6, "canUseDOM"], [24, 26, 18, 15], [24, 28, 18, 17], [25, 6, 19, 4], [25, 10, 19, 8], [26, 8, 20, 6], [26, 12, 20, 10, "options"], [26, 19, 20, 17], [26, 22, 20, 20], [26, 23, 20, 21], [26, 24, 20, 22], [27, 8, 21, 6, "Object"], [27, 14, 21, 12], [27, 15, 21, 13, "defineProperty"], [27, 29, 21, 27], [27, 30, 21, 28, "options"], [27, 37, 21, 35], [27, 39, 21, 37], [27, 48, 21, 46], [27, 50, 21, 48], [28, 10, 22, 8, "get"], [28, 13, 22, 11, "get"], [28, 14, 22, 11], [28, 16, 22, 14], [29, 12, 23, 10, "supported"], [29, 21, 23, 19], [29, 24, 23, 22], [29, 28, 23, 26], [30, 12, 24, 10], [30, 19, 24, 17], [30, 24, 24, 22], [31, 10, 25, 8], [32, 8, 26, 6], [32, 9, 26, 7], [32, 10, 26, 8], [33, 8, 27, 6, "window"], [33, 14, 27, 12], [33, 15, 27, 13, "addEventListener"], [33, 31, 27, 29], [33, 32, 27, 30], [33, 38, 27, 36], [33, 40, 27, 38], [33, 44, 27, 42], [33, 46, 27, 44, "options"], [33, 53, 27, 51], [33, 54, 27, 52], [34, 8, 28, 6, "window"], [34, 14, 28, 12], [34, 15, 28, 13, "removeEventListener"], [34, 34, 28, 32], [34, 35, 28, 33], [34, 41, 28, 39], [34, 43, 28, 41], [34, 47, 28, 45], [34, 49, 28, 47, "options"], [34, 56, 28, 54], [34, 57, 28, 55], [35, 6, 29, 4], [35, 7, 29, 5], [35, 8, 29, 6], [35, 15, 29, 13, "e"], [35, 16, 29, 14], [35, 18, 29, 16], [35, 19, 29, 17], [36, 4, 30, 2], [37, 4, 31, 2], [37, 11, 31, 9, "supported"], [37, 20, 31, 18], [38, 2, 32, 0], [39, 2, 33, 0], [39, 6, 33, 4, "canUsePassiveEvents"], [39, 25, 33, 23], [39, 28, 33, 26, "supportsPassiveEvents"], [39, 49, 33, 47], [39, 50, 33, 48], [39, 51, 33, 49], [40, 2, 34, 0], [40, 11, 34, 9, "getOptions"], [40, 21, 34, 19, "getOptions"], [40, 22, 34, 20, "options"], [40, 29, 34, 27], [40, 31, 34, 29], [41, 4, 35, 2], [41, 8, 35, 6, "options"], [41, 15, 35, 13], [41, 19, 35, 17], [41, 23, 35, 21], [41, 25, 35, 23], [42, 6, 36, 4], [42, 13, 36, 11], [42, 18, 36, 16], [43, 4, 37, 2], [44, 4, 38, 2], [44, 11, 38, 9, "canUsePassiveEvents"], [44, 30, 38, 28], [44, 33, 38, 31, "options"], [44, 40, 38, 38], [44, 43, 38, 41, "Boolean"], [44, 50, 38, 48], [44, 51, 38, 49, "options"], [44, 58, 38, 56], [44, 59, 38, 57, "capture"], [44, 66, 38, 64], [44, 67, 38, 65], [45, 2, 39, 0], [47, 2, 41, 0], [48, 0, 42, 0], [49, 0, 43, 0], [50, 0, 44, 0], [51, 0, 45, 0], [52, 2, 46, 0], [52, 11, 46, 9, "isPropagationStopped"], [52, 31, 46, 29, "isPropagationStopped"], [52, 32, 46, 29], [52, 34, 46, 32], [53, 4, 47, 2], [53, 11, 47, 9], [53, 15, 47, 13], [53, 16, 47, 14, "cancelBubble"], [53, 28, 47, 26], [54, 2, 48, 0], [55, 2, 49, 0], [55, 11, 49, 9, "isDefaultPrevented"], [55, 29, 49, 27, "isDefaultPrevented"], [55, 30, 49, 27], [55, 32, 49, 30], [56, 4, 50, 2], [56, 11, 50, 9], [56, 15, 50, 13], [56, 16, 50, 14, "defaultPrevented"], [56, 32, 50, 30], [57, 2, 51, 0], [58, 2, 52, 0], [58, 11, 52, 9, "normalizeEvent"], [58, 25, 52, 23, "normalizeEvent"], [58, 26, 52, 24, "event"], [58, 31, 52, 29], [58, 33, 52, 31], [59, 4, 53, 2, "event"], [59, 9, 53, 7], [59, 10, 53, 8, "nativeEvent"], [59, 21, 53, 19], [59, 24, 53, 22, "event"], [59, 29, 53, 27], [60, 4, 54, 2, "event"], [60, 9, 54, 7], [60, 10, 54, 8, "persist"], [60, 17, 54, 15], [60, 20, 54, 18, "emptyFunction"], [60, 33, 54, 31], [61, 4, 55, 2, "event"], [61, 9, 55, 7], [61, 10, 55, 8, "isDefaultPrevented"], [61, 28, 55, 26], [61, 31, 55, 29, "isDefaultPrevented"], [61, 49, 55, 47], [62, 4, 56, 2, "event"], [62, 9, 56, 7], [62, 10, 56, 8, "isPropagationStopped"], [62, 30, 56, 28], [62, 33, 56, 31, "isPropagationStopped"], [62, 53, 56, 51], [63, 4, 57, 2], [63, 11, 57, 9, "event"], [63, 16, 57, 14], [64, 2, 58, 0], [66, 2, 60, 0], [67, 0, 61, 0], [68, 0, 62, 0], [69, 2, 63, 7], [69, 11, 63, 16, "addEventListener"], [69, 27, 63, 32, "addEventListener"], [69, 28, 63, 33, "target"], [69, 34, 63, 39], [69, 36, 63, 41, "type"], [69, 40, 63, 45], [69, 42, 63, 47, "listener"], [69, 50, 63, 55], [69, 52, 63, 57, "options"], [69, 59, 63, 64], [69, 61, 63, 66], [70, 4, 64, 2], [70, 8, 64, 6, "opts"], [70, 12, 64, 10], [70, 15, 64, 13, "getOptions"], [70, 25, 64, 23], [70, 26, 64, 24, "options"], [70, 33, 64, 31], [70, 34, 64, 32], [71, 4, 65, 2], [71, 8, 65, 6, "compatListener"], [71, 22, 65, 20], [71, 25, 65, 23, "e"], [71, 26, 65, 24], [71, 30, 65, 28, "listener"], [71, 38, 65, 36], [71, 39, 65, 37, "normalizeEvent"], [71, 53, 65, 51], [71, 54, 65, 52, "e"], [71, 55, 65, 53], [71, 56, 65, 54], [71, 57, 65, 55], [72, 4, 66, 2, "target"], [72, 10, 66, 8], [72, 11, 66, 9, "addEventListener"], [72, 27, 66, 25], [72, 28, 66, 26, "type"], [72, 32, 66, 30], [72, 34, 66, 32, "compatListener"], [72, 48, 66, 46], [72, 50, 66, 48, "opts"], [72, 54, 66, 52], [72, 55, 66, 53], [73, 4, 67, 2], [73, 11, 67, 9], [73, 20, 67, 18, "removeEventListener"], [73, 39, 67, 37, "removeEventListener"], [73, 40, 67, 37], [73, 42, 67, 40], [74, 6, 68, 4], [74, 10, 68, 8, "target"], [74, 16, 68, 14], [74, 20, 68, 18], [74, 24, 68, 22], [74, 26, 68, 24], [75, 8, 69, 6, "target"], [75, 14, 69, 12], [75, 15, 69, 13, "removeEventListener"], [75, 34, 69, 32], [75, 35, 69, 33, "type"], [75, 39, 69, 37], [75, 41, 69, 39, "compatListener"], [75, 55, 69, 53], [75, 57, 69, 55, "opts"], [75, 61, 69, 59], [75, 62, 69, 60], [76, 6, 70, 4], [77, 4, 71, 2], [77, 5, 71, 3], [78, 2, 72, 0], [79, 0, 72, 1], [79, 3]], "functionMap": {"names": ["<global>", "emptyFunction", "supportsPassiveEvents", "Object.defineProperty$argument_2.get", "getOptions", "isPropagationStopped", "isDefaultPrevented", "normalizeEvent", "addEventListener", "compatListener", "removeEventListener"], "mappings": "AAA;oBCY,QD;AEC;QCQ;SDG;CFO;AIE;CJK;AKO;CLE;AMC;CNE;AOC;CPM;OQK;uBCE,gCD;SEE;GFI"}}, "type": "js/module"}]}