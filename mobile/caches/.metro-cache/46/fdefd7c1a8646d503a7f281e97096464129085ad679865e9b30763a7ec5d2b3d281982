{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.downloadAsync = downloadAsync;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _expoModulesCore = require(_dependencyMap[2], \"expo-modules-core\");\n  var AssetModule = (0, _expoModulesCore.requireNativeModule)('ExpoAsset');\n  /**\n   * Downloads the asset from the given URL to a local cache and returns the local URL of the cached\n   * file.\n   *\n   * If there is already a locally cached file and its MD5 hash matches the given `md5Hash` parameter,\n   * if present, the remote asset is not downloaded. The `hash` property is included in Metro's asset\n   * metadata objects when this module's `hashAssetFiles` plugin is used, which is the typical way the\n   * `md5Hash` parameter of this function is provided.\n   */\n  function downloadAsync(_x, _x2, _x3) {\n    return _downloadAsync.apply(this, arguments);\n  }\n  function _downloadAsync() {\n    _downloadAsync = (0, _asyncToGenerator2.default)(function* (url, md5Hash, type) {\n      return AssetModule.downloadAsync(url, md5Hash, type);\n    });\n    return _downloadAsync.apply(this, arguments);\n  }\n});", "lineCount": 28, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_expoModulesCore"], [8, 22, 1, 0], [8, 25, 1, 0, "require"], [8, 32, 1, 0], [8, 33, 1, 0, "_dependencyMap"], [8, 47, 1, 0], [9, 2, 2, 0], [9, 6, 2, 6, "AssetModule"], [9, 17, 2, 17], [9, 20, 2, 20], [9, 24, 2, 20, "requireNativeModule"], [9, 60, 2, 39], [9, 62, 2, 40], [9, 73, 2, 51], [9, 74, 2, 52], [10, 2, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [17, 0, 10, 0], [18, 0, 11, 0], [19, 2, 3, 0], [19, 11, 12, 22, "downloadAsync"], [19, 24, 12, 35, "downloadAsync"], [19, 25, 12, 35, "_x"], [19, 27, 12, 35], [19, 29, 12, 35, "_x2"], [19, 32, 12, 35], [19, 34, 12, 35, "_x3"], [19, 37, 12, 35], [20, 4, 12, 35], [20, 11, 12, 35, "_downloadAsync"], [20, 25, 12, 35], [20, 26, 12, 35, "apply"], [20, 31, 12, 35], [20, 38, 12, 35, "arguments"], [20, 47, 12, 35], [21, 2, 12, 35], [22, 2, 12, 35], [22, 11, 12, 35, "_downloadAsync"], [22, 26, 12, 35], [23, 4, 12, 35, "_downloadAsync"], [23, 18, 12, 35], [23, 25, 12, 35, "_asyncToGenerator2"], [23, 43, 12, 35], [23, 44, 12, 35, "default"], [23, 51, 12, 35], [23, 53, 12, 7], [23, 64, 12, 36, "url"], [23, 67, 12, 39], [23, 69, 12, 41, "md5Hash"], [23, 76, 12, 48], [23, 78, 12, 50, "type"], [23, 82, 12, 54], [23, 84, 12, 56], [24, 6, 13, 4], [24, 13, 13, 11, "AssetModule"], [24, 24, 13, 22], [24, 25, 13, 23, "downloadAsync"], [24, 38, 13, 36], [24, 39, 13, 37, "url"], [24, 42, 13, 40], [24, 44, 13, 42, "md5Hash"], [24, 51, 13, 49], [24, 53, 13, 51, "type"], [24, 57, 13, 55], [24, 58, 13, 56], [25, 4, 14, 0], [25, 5, 14, 1], [26, 4, 14, 1], [26, 11, 14, 1, "_downloadAsync"], [26, 25, 14, 1], [26, 26, 14, 1, "apply"], [26, 31, 14, 1], [26, 38, 14, 1, "arguments"], [26, 47, 14, 1], [27, 2, 14, 1], [28, 0, 14, 1], [28, 3]], "functionMap": {"names": ["<global>", "downloadAsync"], "mappings": "AAA;OCW;CDE"}}, "type": "js/module"}]}