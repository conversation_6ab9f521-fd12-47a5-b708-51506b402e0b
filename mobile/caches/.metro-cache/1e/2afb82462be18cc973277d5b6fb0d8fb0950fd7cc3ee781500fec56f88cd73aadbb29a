{"dependencies": [{"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 49, "index": 260}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ForceTouchGesture = void 0;\n  var _gesture = require(_dependencyMap[0], \"./gesture\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const _worklet_6555184671956_init_data = {\n    code: \"function changeEventCalculator_reactNativeGestureHandler_forceTouchGestureJs1(current,previous){let changePayload;if(previous===undefined){changePayload={forceChange:current.force};}else{changePayload={forceChange:current.force-previous.force};}return{...current,...changePayload};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/forceTouchGesture.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_reactNativeGestureHandler_forceTouchGestureJs1\\\",\\\"current\\\",\\\"previous\\\",\\\"changePayload\\\",\\\"undefined\\\",\\\"forceChange\\\",\\\"force\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/forceTouchGesture.js\\\"],\\\"mappings\\\":\\\"AAIA,SAAAA,oEAAkDA,CAAAC,OAAA,CAAAC,QAAA,EAGhD,GAAI,CAAAC,aAAa,CAEjB,GAAID,QAAQ,GAAKE,SAAS,CAAE,CAC1BD,aAAa,CAAG,CACdE,WAAW,CAAEJ,OAAO,CAACK,KACvB,CAAC,CACH,CAAC,IAAM,CACLH,aAAa,CAAG,CACdE,WAAW,CAAEJ,OAAO,CAACK,KAAK,CAAGJ,QAAQ,CAACI,KACxC,CAAC,CACH,CAEA,MAAO,CAAE,GAAGL,OAAO,CACjB,GAAGE,aACL,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const changeEventCalculator = function () {\n    const _e = [new global.Error(), 1, -27];\n    const changeEventCalculator = function (current, previous) {\n      let changePayload;\n      if (previous === undefined) {\n        changePayload = {\n          forceChange: current.force\n        };\n      } else {\n        changePayload = {\n          forceChange: current.force - previous.force\n        };\n      }\n      return {\n        ...current,\n        ...changePayload\n      };\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 6555184671956;\n    changeEventCalculator.__initData = _worklet_6555184671956_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  class ForceTouchGesture extends _gesture.ContinousBaseGesture {\n    constructor() {\n      super();\n      _defineProperty(this, \"config\", {});\n      this.handlerName = 'ForceTouchGestureHandler';\n    }\n    /**\n     * A minimal pressure that is required before gesture can activate.\n     * Should be a value from range [0.0, 1.0]. Default is 0.2.\n     * @param force\n     */\n\n    minForce(force) {\n      this.config.minForce = force;\n      return this;\n    }\n    /**\n     * A maximal pressure that could be applied for gesture.\n     * If the pressure is greater, gesture fails. Should be a value from range [0.0, 1.0].\n     * @param force\n     */\n\n    maxForce(force) {\n      this.config.maxForce = force;\n      return this;\n    }\n    /**\n     * Value defining if haptic feedback has to be performed on activation.\n     * @param value\n     */\n\n    feedbackOnActivation(value) {\n      this.config.feedbackOnActivation = value;\n      return this;\n    }\n    onChange(callback) {\n      // @ts-ignore TS being overprotective, ForceTouchGestureHandlerEventPayload is Record\n      this.handlers.changeEventCalculator = changeEventCalculator;\n      return super.onChange(callback);\n    }\n  }\n  exports.ForceTouchGesture = ForceTouchGesture;\n});", "lineCount": 92, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_gesture"], [6, 14, 3, 0], [6, 17, 3, 0, "require"], [6, 24, 3, 0], [6, 25, 3, 0, "_dependencyMap"], [6, 39, 3, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "obj"], [7, 30, 1, 28], [7, 32, 1, 30, "key"], [7, 35, 1, 33], [7, 37, 1, 35, "value"], [7, 42, 1, 40], [7, 44, 1, 42], [8, 4, 1, 44], [8, 8, 1, 48, "key"], [8, 11, 1, 51], [8, 15, 1, 55, "obj"], [8, 18, 1, 58], [8, 20, 1, 60], [9, 6, 1, 62, "Object"], [9, 12, 1, 68], [9, 13, 1, 69, "defineProperty"], [9, 27, 1, 83], [9, 28, 1, 84, "obj"], [9, 31, 1, 87], [9, 33, 1, 89, "key"], [9, 36, 1, 92], [9, 38, 1, 94], [10, 8, 1, 96, "value"], [10, 13, 1, 101], [10, 15, 1, 103, "value"], [10, 20, 1, 108], [11, 8, 1, 110, "enumerable"], [11, 18, 1, 120], [11, 20, 1, 122], [11, 24, 1, 126], [12, 8, 1, 128, "configurable"], [12, 20, 1, 140], [12, 22, 1, 142], [12, 26, 1, 146], [13, 8, 1, 148, "writable"], [13, 16, 1, 156], [13, 18, 1, 158], [14, 6, 1, 163], [14, 7, 1, 164], [14, 8, 1, 165], [15, 4, 1, 167], [15, 5, 1, 168], [15, 11, 1, 174], [16, 6, 1, 176, "obj"], [16, 9, 1, 179], [16, 10, 1, 180, "key"], [16, 13, 1, 183], [16, 14, 1, 184], [16, 17, 1, 187, "value"], [16, 22, 1, 192], [17, 4, 1, 194], [18, 4, 1, 196], [18, 11, 1, 203, "obj"], [18, 14, 1, 206], [19, 2, 1, 208], [20, 2, 1, 209], [20, 8, 1, 209, "_worklet_6555184671956_init_data"], [20, 40, 1, 209], [21, 4, 1, 209, "code"], [21, 8, 1, 209], [22, 4, 1, 209, "location"], [22, 12, 1, 209], [23, 4, 1, 209, "sourceMap"], [23, 13, 1, 209], [24, 4, 1, 209, "version"], [24, 11, 1, 209], [25, 2, 1, 209], [26, 2, 1, 209], [26, 8, 1, 209, "changeEventCalculator"], [26, 29, 1, 209], [26, 32, 5, 0], [27, 4, 5, 0], [27, 10, 5, 0, "_e"], [27, 12, 5, 0], [27, 20, 5, 0, "global"], [27, 26, 5, 0], [27, 27, 5, 0, "Error"], [27, 32, 5, 0], [28, 4, 5, 0], [28, 10, 5, 0, "changeEventCalculator"], [28, 31, 5, 0], [28, 43, 5, 0, "changeEventCalculator"], [28, 44, 5, 31, "current"], [28, 51, 5, 38], [28, 53, 5, 40, "previous"], [28, 61, 5, 48], [28, 63, 5, 50], [29, 6, 8, 2], [29, 10, 8, 6, "changePayload"], [29, 23, 8, 19], [30, 6, 10, 2], [30, 10, 10, 6, "previous"], [30, 18, 10, 14], [30, 23, 10, 19, "undefined"], [30, 32, 10, 28], [30, 34, 10, 30], [31, 8, 11, 4, "changePayload"], [31, 21, 11, 17], [31, 24, 11, 20], [32, 10, 12, 6, "forceChange"], [32, 21, 12, 17], [32, 23, 12, 19, "current"], [32, 30, 12, 26], [32, 31, 12, 27, "force"], [33, 8, 13, 4], [33, 9, 13, 5], [34, 6, 14, 2], [34, 7, 14, 3], [34, 13, 14, 9], [35, 8, 15, 4, "changePayload"], [35, 21, 15, 17], [35, 24, 15, 20], [36, 10, 16, 6, "forceChange"], [36, 21, 16, 17], [36, 23, 16, 19, "current"], [36, 30, 16, 26], [36, 31, 16, 27, "force"], [36, 36, 16, 32], [36, 39, 16, 35, "previous"], [36, 47, 16, 43], [36, 48, 16, 44, "force"], [37, 8, 17, 4], [37, 9, 17, 5], [38, 6, 18, 2], [39, 6, 20, 2], [39, 13, 20, 9], [40, 8, 20, 11], [40, 11, 20, 14, "current"], [40, 18, 20, 21], [41, 8, 21, 4], [41, 11, 21, 7, "changePayload"], [42, 6, 22, 2], [42, 7, 22, 3], [43, 4, 23, 0], [43, 5, 23, 1], [44, 4, 23, 1, "changeEventCalculator"], [44, 25, 23, 1], [44, 26, 23, 1, "__closure"], [44, 35, 23, 1], [45, 4, 23, 1, "changeEventCalculator"], [45, 25, 23, 1], [45, 26, 23, 1, "__workletHash"], [45, 39, 23, 1], [46, 4, 23, 1, "changeEventCalculator"], [46, 25, 23, 1], [46, 26, 23, 1, "__initData"], [46, 36, 23, 1], [46, 39, 23, 1, "_worklet_6555184671956_init_data"], [46, 71, 23, 1], [47, 4, 23, 1, "changeEventCalculator"], [47, 25, 23, 1], [47, 26, 23, 1, "__stackDetails"], [47, 40, 23, 1], [47, 43, 23, 1, "_e"], [47, 45, 23, 1], [48, 4, 23, 1], [48, 11, 23, 1, "changeEventCalculator"], [48, 32, 23, 1], [49, 2, 23, 1], [49, 3, 5, 0], [50, 2, 25, 7], [50, 8, 25, 13, "ForceTouchGesture"], [50, 25, 25, 30], [50, 34, 25, 39, "ContinousBaseGesture"], [50, 63, 25, 59], [50, 64, 25, 60], [51, 4, 26, 2, "constructor"], [51, 15, 26, 13, "constructor"], [51, 16, 26, 13], [51, 18, 26, 16], [52, 6, 27, 4], [52, 11, 27, 9], [52, 12, 27, 10], [52, 13, 27, 11], [53, 6, 29, 4, "_defineProperty"], [53, 21, 29, 19], [53, 22, 29, 20], [53, 26, 29, 24], [53, 28, 29, 26], [53, 36, 29, 34], [53, 38, 29, 36], [53, 39, 29, 37], [53, 40, 29, 38], [53, 41, 29, 39], [54, 6, 31, 4], [54, 10, 31, 8], [54, 11, 31, 9, "handler<PERSON>ame"], [54, 22, 31, 20], [54, 25, 31, 23], [54, 51, 31, 49], [55, 4, 32, 2], [56, 4, 33, 2], [57, 0, 34, 0], [58, 0, 35, 0], [59, 0, 36, 0], [60, 0, 37, 0], [62, 4, 40, 2, "minForce"], [62, 12, 40, 10, "minForce"], [62, 13, 40, 11, "force"], [62, 18, 40, 16], [62, 20, 40, 18], [63, 6, 41, 4], [63, 10, 41, 8], [63, 11, 41, 9, "config"], [63, 17, 41, 15], [63, 18, 41, 16, "minForce"], [63, 26, 41, 24], [63, 29, 41, 27, "force"], [63, 34, 41, 32], [64, 6, 42, 4], [64, 13, 42, 11], [64, 17, 42, 15], [65, 4, 43, 2], [66, 4, 44, 2], [67, 0, 45, 0], [68, 0, 46, 0], [69, 0, 47, 0], [70, 0, 48, 0], [72, 4, 51, 2, "max<PERSON><PERSON>ce"], [72, 12, 51, 10, "max<PERSON><PERSON>ce"], [72, 13, 51, 11, "force"], [72, 18, 51, 16], [72, 20, 51, 18], [73, 6, 52, 4], [73, 10, 52, 8], [73, 11, 52, 9, "config"], [73, 17, 52, 15], [73, 18, 52, 16, "max<PERSON><PERSON>ce"], [73, 26, 52, 24], [73, 29, 52, 27, "force"], [73, 34, 52, 32], [74, 6, 53, 4], [74, 13, 53, 11], [74, 17, 53, 15], [75, 4, 54, 2], [76, 4, 55, 2], [77, 0, 56, 0], [78, 0, 57, 0], [79, 0, 58, 0], [81, 4, 61, 2, "feedbackOnActivation"], [81, 24, 61, 22, "feedbackOnActivation"], [81, 25, 61, 23, "value"], [81, 30, 61, 28], [81, 32, 61, 30], [82, 6, 62, 4], [82, 10, 62, 8], [82, 11, 62, 9, "config"], [82, 17, 62, 15], [82, 18, 62, 16, "feedbackOnActivation"], [82, 38, 62, 36], [82, 41, 62, 39, "value"], [82, 46, 62, 44], [83, 6, 63, 4], [83, 13, 63, 11], [83, 17, 63, 15], [84, 4, 64, 2], [85, 4, 66, 2, "onChange"], [85, 12, 66, 10, "onChange"], [85, 13, 66, 11, "callback"], [85, 21, 66, 19], [85, 23, 66, 21], [86, 6, 67, 4], [87, 6, 68, 4], [87, 10, 68, 8], [87, 11, 68, 9, "handlers"], [87, 19, 68, 17], [87, 20, 68, 18, "changeEventCalculator"], [87, 41, 68, 39], [87, 44, 68, 42, "changeEventCalculator"], [87, 65, 68, 63], [88, 6, 69, 4], [88, 13, 69, 11], [88, 18, 69, 16], [88, 19, 69, 17, "onChange"], [88, 27, 69, 25], [88, 28, 69, 26, "callback"], [88, 36, 69, 34], [88, 37, 69, 35], [89, 4, 70, 2], [90, 2, 72, 0], [91, 2, 72, 1, "exports"], [91, 9, 72, 1], [91, 10, 72, 1, "ForceTouchGesture"], [91, 27, 72, 1], [91, 30, 72, 1, "ForceTouchGesture"], [91, 47, 72, 1], [92, 0, 72, 1], [92, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "changeEventCalculator", "ForceTouchGesture", "ForceTouchGesture#constructor", "ForceTouchGesture#minForce", "ForceTouchGesture#maxForce", "ForceTouchGesture#feedbackOnActivation", "ForceTouchGesture#onChange"], "mappings": "AAA,iNC;ACI;CDkB;OEE;ECC;GDM;EEQ;GFG;EGQ;GHG;EIO;GJG;EKE;GLI;CFE"}}, "type": "js/module"}]}