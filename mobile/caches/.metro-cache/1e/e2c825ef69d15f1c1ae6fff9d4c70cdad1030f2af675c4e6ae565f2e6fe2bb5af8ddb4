{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../../utils/ArrayLikeUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 19, "column": 36}}], "key": "UNCoikk/SuoXbwGrCmksIC85q/Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createNodeList = createNodeList;\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _ArrayLikeUtils = require(_dependencyMap[5], \"../../utils/ArrayLikeUtils\");\n  var _length = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"length\");\n  var NodeList = exports.default = /*#__PURE__*/function () {\n    function NodeList(elements) {\n      (0, _classCallCheck2.default)(this, NodeList);\n      Object.defineProperty(this, _length, {\n        writable: true,\n        value: void 0\n      });\n      for (var i = 0; i < elements.length; i++) {\n        Object.defineProperty(this, i, {\n          value: elements[i],\n          writable: false\n        });\n      }\n      (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length] = elements.length;\n    }\n    return (0, _createClass2.default)(NodeList, [{\n      key: \"length\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length];\n      }\n    }, {\n      key: \"item\",\n      value: function item(index) {\n        if (index < 0 || index >= (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length]) {\n          return null;\n        }\n        var arrayLike = this;\n        return arrayLike[index];\n      }\n    }, {\n      key: \"entries\",\n      value: function entries() {\n        return (0, _ArrayLikeUtils.createEntriesIterator)(this);\n      }\n    }, {\n      key: \"forEach\",\n      value: function forEach(callbackFn, thisArg) {\n        var arrayLike = this;\n        for (var _index = 0; _index < (0, _classPrivateFieldLooseBase2.default)(this, _length)[_length]; _index++) {\n          if (thisArg == null) {\n            callbackFn(arrayLike[_index], _index, this);\n          } else {\n            callbackFn.call(thisArg, arrayLike[_index], _index, this);\n          }\n        }\n      }\n    }, {\n      key: \"keys\",\n      value: function keys() {\n        return (0, _ArrayLikeUtils.createKeyIterator)(this);\n      }\n    }, {\n      key: \"values\",\n      value: function values() {\n        return (0, _ArrayLikeUtils.createValueIterator)(this);\n      }\n    }, {\n      key: Symbol.iterator,\n      value: function () {\n        return (0, _ArrayLikeUtils.createValueIterator)(this);\n      }\n    }]);\n  }();\n  function createNodeList(elements) {\n    return new NodeList(elements);\n  }\n});", "lineCount": 80, "map": [[12, 2, 15, 0], [12, 6, 15, 0, "_ArrayLikeUtils"], [12, 21, 15, 0], [12, 24, 15, 0, "require"], [12, 31, 15, 0], [12, 32, 15, 0, "_dependencyMap"], [12, 46, 15, 0], [13, 2, 19, 36], [13, 6, 19, 36, "_length"], [13, 13, 19, 36], [13, 33, 19, 36, "_classPrivateFieldLooseKey2"], [13, 60, 19, 36], [13, 61, 19, 36, "default"], [13, 68, 19, 36], [14, 2, 19, 36], [14, 6, 25, 21, "NodeList"], [14, 14, 25, 29], [14, 17, 25, 29, "exports"], [14, 24, 25, 29], [14, 25, 25, 29, "default"], [14, 32, 25, 29], [15, 4, 34, 2], [15, 13, 34, 2, "NodeList"], [15, 22, 34, 14, "elements"], [15, 30, 34, 41], [15, 32, 34, 43], [16, 6, 34, 43], [16, 10, 34, 43, "_classCallCheck2"], [16, 26, 34, 43], [16, 27, 34, 43, "default"], [16, 34, 34, 43], [16, 42, 34, 43, "NodeList"], [16, 50, 34, 43], [17, 6, 34, 43, "Object"], [17, 12, 34, 43], [17, 13, 34, 43, "defineProperty"], [17, 27, 34, 43], [17, 34, 34, 43, "_length"], [17, 41, 34, 43], [18, 8, 34, 43, "writable"], [18, 16, 34, 43], [19, 8, 34, 43, "value"], [19, 13, 34, 43], [20, 6, 34, 43], [21, 6, 35, 4], [21, 11, 35, 9], [21, 15, 35, 13, "i"], [21, 16, 35, 14], [21, 19, 35, 17], [21, 20, 35, 18], [21, 22, 35, 20, "i"], [21, 23, 35, 21], [21, 26, 35, 24, "elements"], [21, 34, 35, 32], [21, 35, 35, 33, "length"], [21, 41, 35, 39], [21, 43, 35, 41, "i"], [21, 44, 35, 42], [21, 46, 35, 44], [21, 48, 35, 46], [22, 8, 36, 6, "Object"], [22, 14, 36, 12], [22, 15, 36, 13, "defineProperty"], [22, 29, 36, 27], [22, 30, 36, 28], [22, 34, 36, 32], [22, 36, 36, 34, "i"], [22, 37, 36, 35], [22, 39, 36, 37], [23, 10, 37, 8, "value"], [23, 15, 37, 13], [23, 17, 37, 15, "elements"], [23, 25, 37, 23], [23, 26, 37, 24, "i"], [23, 27, 37, 25], [23, 28, 37, 26], [24, 10, 38, 8, "writable"], [24, 18, 38, 16], [24, 20, 38, 18], [25, 8, 39, 6], [25, 9, 39, 7], [25, 10, 39, 8], [26, 6, 40, 4], [27, 6, 41, 4], [27, 10, 41, 4, "_classPrivateFieldLooseBase2"], [27, 38, 41, 4], [27, 39, 41, 4, "default"], [27, 46, 41, 4], [27, 52, 41, 8], [27, 54, 41, 8, "_length"], [27, 61, 41, 8], [27, 63, 41, 8, "_length"], [27, 70, 41, 8], [27, 74, 41, 19, "elements"], [27, 82, 41, 27], [27, 83, 41, 28, "length"], [27, 89, 41, 34], [28, 4, 42, 2], [29, 4, 42, 3], [29, 15, 42, 3, "_createClass2"], [29, 28, 42, 3], [29, 29, 42, 3, "default"], [29, 36, 42, 3], [29, 38, 42, 3, "NodeList"], [29, 46, 42, 3], [30, 6, 42, 3, "key"], [30, 9, 42, 3], [31, 6, 42, 3, "get"], [31, 9, 42, 3], [31, 11, 44, 2], [31, 20, 44, 2, "get"], [31, 21, 44, 2], [31, 23, 44, 23], [32, 8, 45, 4], [32, 19, 45, 4, "_classPrivateFieldLooseBase2"], [32, 47, 45, 4], [32, 48, 45, 4, "default"], [32, 55, 45, 4], [32, 57, 45, 11], [32, 61, 45, 15], [32, 63, 45, 15, "_length"], [32, 70, 45, 15], [32, 72, 45, 15, "_length"], [32, 79, 45, 15], [33, 6, 46, 2], [34, 4, 46, 3], [35, 6, 46, 3, "key"], [35, 9, 46, 3], [36, 6, 46, 3, "value"], [36, 11, 46, 3], [36, 13, 48, 2], [36, 22, 48, 2, "item"], [36, 26, 48, 6, "item"], [36, 27, 48, 7, "index"], [36, 32, 48, 20], [36, 34, 48, 32], [37, 8, 49, 4], [37, 12, 49, 8, "index"], [37, 17, 49, 13], [37, 20, 49, 16], [37, 21, 49, 17], [37, 25, 49, 21, "index"], [37, 30, 49, 26], [37, 38, 49, 26, "_classPrivateFieldLooseBase2"], [37, 66, 49, 26], [37, 67, 49, 26, "default"], [37, 74, 49, 26], [37, 76, 49, 30], [37, 80, 49, 34], [37, 82, 49, 34, "_length"], [37, 89, 49, 34], [37, 91, 49, 34, "_length"], [37, 98, 49, 34], [37, 99, 49, 42], [37, 101, 49, 44], [38, 10, 50, 6], [38, 17, 50, 13], [38, 21, 50, 17], [39, 8, 51, 4], [40, 8, 56, 4], [40, 12, 56, 10, "arrayLike"], [40, 21, 56, 33], [40, 24, 56, 36], [40, 28, 56, 40], [41, 8, 57, 4], [41, 15, 57, 11, "arrayLike"], [41, 24, 57, 20], [41, 25, 57, 21, "index"], [41, 30, 57, 26], [41, 31, 57, 27], [42, 6, 58, 2], [43, 4, 58, 3], [44, 6, 58, 3, "key"], [44, 9, 58, 3], [45, 6, 58, 3, "value"], [45, 11, 58, 3], [45, 13, 60, 2], [45, 22, 60, 2, "entries"], [45, 29, 60, 9, "entries"], [45, 30, 60, 9], [45, 32, 60, 35], [46, 8, 61, 4], [46, 15, 61, 11], [46, 19, 61, 11, "createEntriesIterator"], [46, 56, 61, 32], [46, 58, 61, 33], [46, 62, 61, 37], [46, 63, 61, 38], [47, 6, 62, 2], [48, 4, 62, 3], [49, 6, 62, 3, "key"], [49, 9, 62, 3], [50, 6, 62, 3, "value"], [50, 11, 62, 3], [50, 13, 64, 2], [50, 22, 64, 2, "for<PERSON>ach"], [50, 29, 64, 9, "for<PERSON>ach"], [50, 30, 65, 4, "callbackFn"], [50, 40, 65, 70], [50, 42, 66, 4, "thisArg"], [50, 49, 66, 22], [50, 51, 67, 10], [51, 8, 71, 4], [51, 12, 71, 10, "arrayLike"], [51, 21, 71, 33], [51, 24, 71, 36], [51, 28, 71, 40], [52, 8, 73, 4], [52, 13, 73, 9], [52, 17, 73, 13, "index"], [52, 23, 73, 18], [52, 26, 73, 21], [52, 27, 73, 22], [52, 29, 73, 24, "index"], [52, 35, 73, 29], [52, 42, 73, 29, "_classPrivateFieldLooseBase2"], [52, 70, 73, 29], [52, 71, 73, 29, "default"], [52, 78, 73, 29], [52, 80, 73, 32], [52, 84, 73, 36], [52, 86, 73, 36, "_length"], [52, 93, 73, 36], [52, 95, 73, 36, "_length"], [52, 102, 73, 36], [52, 103, 73, 44], [52, 105, 73, 46, "index"], [52, 111, 73, 51], [52, 113, 73, 53], [52, 115, 73, 55], [53, 10, 74, 6], [53, 14, 74, 10, "thisArg"], [53, 21, 74, 17], [53, 25, 74, 21], [53, 29, 74, 25], [53, 31, 74, 27], [54, 12, 75, 8, "callbackFn"], [54, 22, 75, 18], [54, 23, 75, 19, "arrayLike"], [54, 32, 75, 28], [54, 33, 75, 29, "index"], [54, 39, 75, 34], [54, 40, 75, 35], [54, 42, 75, 37, "index"], [54, 48, 75, 42], [54, 50, 75, 44], [54, 54, 75, 48], [54, 55, 75, 49], [55, 10, 76, 6], [55, 11, 76, 7], [55, 17, 76, 13], [56, 12, 77, 8, "callbackFn"], [56, 22, 77, 18], [56, 23, 77, 19, "call"], [56, 27, 77, 23], [56, 28, 77, 24, "thisArg"], [56, 35, 77, 31], [56, 37, 77, 33, "arrayLike"], [56, 46, 77, 42], [56, 47, 77, 43, "index"], [56, 53, 77, 48], [56, 54, 77, 49], [56, 56, 77, 51, "index"], [56, 62, 77, 56], [56, 64, 77, 58], [56, 68, 77, 62], [56, 69, 77, 63], [57, 10, 78, 6], [58, 8, 79, 4], [59, 6, 80, 2], [60, 4, 80, 3], [61, 6, 80, 3, "key"], [61, 9, 80, 3], [62, 6, 80, 3, "value"], [62, 11, 80, 3], [62, 13, 82, 2], [62, 22, 82, 2, "keys"], [62, 26, 82, 6, "keys"], [62, 27, 82, 6], [62, 29, 82, 27], [63, 8, 83, 4], [63, 15, 83, 11], [63, 19, 83, 11, "createKeyIterator"], [63, 52, 83, 28], [63, 54, 83, 29], [63, 58, 83, 33], [63, 59, 83, 34], [64, 6, 84, 2], [65, 4, 84, 3], [66, 6, 84, 3, "key"], [66, 9, 84, 3], [67, 6, 84, 3, "value"], [67, 11, 84, 3], [67, 13, 86, 2], [67, 22, 86, 2, "values"], [67, 28, 86, 8, "values"], [67, 29, 86, 8], [67, 31, 86, 24], [68, 8, 87, 4], [68, 15, 87, 11], [68, 19, 87, 11, "createValueIterator"], [68, 54, 87, 30], [68, 56, 87, 31], [68, 60, 87, 35], [68, 61, 87, 36], [69, 6, 88, 2], [70, 4, 88, 3], [71, 6, 88, 3, "key"], [71, 9, 88, 3], [71, 11, 91, 3, "Symbol"], [71, 17, 91, 9], [71, 18, 91, 10, "iterator"], [71, 26, 91, 18], [72, 6, 91, 18, "value"], [72, 11, 91, 18], [72, 13, 91, 2], [72, 22, 91, 2, "value"], [72, 23, 91, 2], [72, 25, 91, 35], [73, 8, 92, 4], [73, 15, 92, 11], [73, 19, 92, 11, "createValueIterator"], [73, 54, 92, 30], [73, 56, 92, 31], [73, 60, 92, 35], [73, 61, 92, 36], [74, 6, 93, 2], [75, 4, 93, 3], [76, 2, 93, 3], [77, 2, 102, 7], [77, 11, 102, 16, "createNodeList"], [77, 25, 102, 30, "createNodeList"], [77, 26, 102, 34, "elements"], [77, 34, 102, 61], [77, 36, 102, 76], [78, 4, 103, 2], [78, 11, 103, 9], [78, 15, 103, 13, "NodeList"], [78, 23, 103, 21], [78, 24, 103, 22, "elements"], [78, 32, 103, 30], [78, 33, 103, 31], [79, 2, 104, 0], [80, 0, 104, 1], [80, 3]], "functionMap": {"names": ["<global>", "NodeList", "constructor", "get__length", "item", "entries", "for<PERSON>ach", "keys", "values", "@@iterator", "createNodeList"], "mappings": "AAA;eCwB;ECS;GDQ;EEE;GFE;EGE;GHU;EIE;GJE;EKE;GLgB;EME;GNE;EOE;GPE;EQG;GRE;CDC;OUQ"}}, "type": "js/module"}]}