{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  // Forked so we can access without importing any React Native code in Node.js environments.\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.findFocusedRoute = findFocusedRoute;\n  function findFocusedRoute(state) {\n    var current = state;\n    while (current?.routes[current.index ?? 0].state != null) {\n      current = current.routes[current.index ?? 0].state;\n    }\n    var route = current?.routes[current?.index ?? 0];\n    return route;\n  }\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [5, 2, 3, 0, "Object"], [5, 8, 3, 6], [5, 9, 3, 7, "defineProperty"], [5, 23, 3, 21], [5, 24, 3, 22, "exports"], [5, 31, 3, 29], [5, 33, 3, 31], [5, 45, 3, 43], [5, 47, 3, 45], [6, 4, 3, 47, "value"], [6, 9, 3, 52], [6, 11, 3, 54], [7, 2, 3, 59], [7, 3, 3, 60], [7, 4, 3, 61], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "findFocusedRoute"], [8, 26, 4, 24], [8, 29, 4, 27, "findFocusedRoute"], [8, 45, 4, 43], [9, 2, 5, 0], [9, 11, 5, 9, "findFocusedRoute"], [9, 27, 5, 25, "findFocusedRoute"], [9, 28, 5, 26, "state"], [9, 33, 5, 31], [9, 35, 5, 33], [10, 4, 6, 4], [10, 8, 6, 8, "current"], [10, 15, 6, 15], [10, 18, 6, 18, "state"], [10, 23, 6, 23], [11, 4, 7, 4], [11, 11, 7, 11, "current"], [11, 18, 7, 18], [11, 20, 7, 20, "routes"], [11, 26, 7, 26], [11, 27, 7, 27, "current"], [11, 34, 7, 34], [11, 35, 7, 35, "index"], [11, 40, 7, 40], [11, 44, 7, 44], [11, 45, 7, 45], [11, 46, 7, 46], [11, 47, 7, 47, "state"], [11, 52, 7, 52], [11, 56, 7, 56], [11, 60, 7, 60], [11, 62, 7, 62], [12, 6, 8, 8, "current"], [12, 13, 8, 15], [12, 16, 8, 18, "current"], [12, 23, 8, 25], [12, 24, 8, 26, "routes"], [12, 30, 8, 32], [12, 31, 8, 33, "current"], [12, 38, 8, 40], [12, 39, 8, 41, "index"], [12, 44, 8, 46], [12, 48, 8, 50], [12, 49, 8, 51], [12, 50, 8, 52], [12, 51, 8, 53, "state"], [12, 56, 8, 58], [13, 4, 9, 4], [14, 4, 10, 4], [14, 8, 10, 10, "route"], [14, 13, 10, 15], [14, 16, 10, 18, "current"], [14, 23, 10, 25], [14, 25, 10, 27, "routes"], [14, 31, 10, 33], [14, 32, 10, 34, "current"], [14, 39, 10, 41], [14, 41, 10, 43, "index"], [14, 46, 10, 48], [14, 50, 10, 52], [14, 51, 10, 53], [14, 52, 10, 54], [15, 4, 11, 4], [15, 11, 11, 11, "route"], [15, 16, 11, 16], [16, 2, 12, 0], [17, 0, 12, 1], [17, 3]], "functionMap": {"names": ["<global>", "findFocusedRoute"], "mappings": "AAA;ACI;CDO"}}, "type": "js/module"}]}