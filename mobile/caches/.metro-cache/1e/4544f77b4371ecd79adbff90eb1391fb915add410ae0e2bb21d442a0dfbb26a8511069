{"dependencies": [{"name": "../ConfigHelper.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "gbxqekPN9YwWzwODJteGByBgHo4=", "exportNames": ["*"]}}, {"name": "./styleUpdater.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 47, "index": 115}}], "key": "gE9MWcLES+XgUfY8ydNusVgaUwc=", "exportNames": ["*"]}}, {"name": "./swipeSimulator.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 116}, "end": {"line": 5, "column": 56, "index": 172}}], "key": "9tXh6xSN6A5yOGEDZqszK3bMiDo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.startScreenTransition = exports.finishScreenTransition = void 0;\n  var _ConfigHelper = require(_dependencyMap[0], \"../ConfigHelper.js\");\n  var _styleUpdater = require(_dependencyMap[1], \"./styleUpdater.js\");\n  var _swipeSimulator = require(_dependencyMap[2], \"./swipeSimulator.js\");\n  (0, _ConfigHelper.configureProps)();\n  const _worklet_15010796722453_init_data = {\n    code: \"function startScreenTransition_reactNativeReanimated_animationManagerJs1(screenTransitionConfig){const{applyStyle}=this.__closure;const{stackTag:stackTag,sharedEvent:sharedEvent}=screenTransitionConfig;sharedEvent.addListener(stackTag,function(){applyStyle(screenTransitionConfig,sharedEvent.value);});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/animationManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"startScreenTransition_reactNativeReanimated_animationManagerJs1\\\",\\\"screenTransitionConfig\\\",\\\"applyStyle\\\",\\\"__closure\\\",\\\"stackTag\\\",\\\"sharedEvent\\\",\\\"addListener\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/animationManager.js\\\"],\\\"mappings\\\":\\\"AAMO,SAAAA,+DAAuDA,CAAAC,sBAAA,QAAAC,UAAA,OAAAC,SAAA,CAG5D,KAAM,CACJC,QAAQ,CAARA,QAAQ,CACRC,WAAA,CAAAA,WACF,CAAC,CAAGJ,sBAAsB,CAC1BI,WAAW,CAACC,WAAW,CAACF,QAAQ,CAAE,UAAM,CACtCF,UAAU,CAACD,sBAAsB,CAAEI,WAAW,CAACE,KAAK,CAAC,CACvD,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const startScreenTransition = exports.startScreenTransition = function () {\n    const _e = [new global.Error(), -2, -27];\n    const startScreenTransition = function (screenTransitionConfig) {\n      const {\n        stackTag,\n        sharedEvent\n      } = screenTransitionConfig;\n      sharedEvent.addListener(stackTag, () => {\n        (0, _styleUpdater.applyStyle)(screenTransitionConfig, sharedEvent.value);\n      });\n    };\n    startScreenTransition.__closure = {\n      applyStyle: _styleUpdater.applyStyle\n    };\n    startScreenTransition.__workletHash = 15010796722453;\n    startScreenTransition.__initData = _worklet_15010796722453_init_data;\n    startScreenTransition.__stackDetails = _e;\n    return startScreenTransition;\n  }();\n  const _worklet_13448177000651_init_data = {\n    code: \"function getLockAxis_reactNativeReanimated_animationManagerJs2(goBackGesture){if(['swipeRight','swipeLeft','horizontalSwipe'].includes(goBackGesture)){return'x';}else if(['swipeUp','swipeDown','verticalSwipe'].includes(goBackGesture)){return'y';}return undefined;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/animationManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getLockAxis_reactNativeReanimated_animationManagerJs2\\\",\\\"goBackGesture\\\",\\\"includes\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/animationManager.js\\\"],\\\"mappings\\\":\\\"AAiBA,SAAAA,qDAAoCA,CAAAC,aAAA,EAGlC,GAAI,CAAC,YAAY,CAAE,WAAW,CAAE,iBAAiB,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,CAAE,CAC1E,MAAO,GAAG,CACZ,CAAC,IAAM,IAAI,CAAC,SAAS,CAAE,WAAW,CAAE,eAAe,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,CAAE,CAC5E,MAAO,GAAG,CACZ,CACA,MAAO,CAAAE,SAAS,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const getLockAxis = function () {\n    const _e = [new global.Error(), 1, -27];\n    const getLockAxis = function (goBackGesture) {\n      if (['swipeRight', 'swipeLeft', 'horizontalSwipe'].includes(goBackGesture)) {\n        return 'x';\n      } else if (['swipeUp', 'swipeDown', 'verticalSwipe'].includes(goBackGesture)) {\n        return 'y';\n      }\n      return undefined;\n    };\n    getLockAxis.__closure = {};\n    getLockAxis.__workletHash = 13448177000651;\n    getLockAxis.__initData = _worklet_13448177000651_init_data;\n    getLockAxis.__stackDetails = _e;\n    return getLockAxis;\n  }();\n  const _worklet_3004477788376_init_data = {\n    code: \"function finishScreenTransition_reactNativeReanimated_animationManagerJs3(screenTransitionConfig){const{getLockAxis,getSwipeSimulator}=this.__closure;const{stackTag:stackTag,sharedEvent:sharedEvent,goBackGesture:goBackGesture}=screenTransitionConfig;sharedEvent.removeListener(stackTag);const lockAxis=getLockAxis(goBackGesture);const step=getSwipeSimulator(sharedEvent.value,screenTransitionConfig,lockAxis);step();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/animationManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"finishScreenTransition_reactNativeReanimated_animationManagerJs3\\\",\\\"screenTransitionConfig\\\",\\\"getLockAxis\\\",\\\"getSwipeSimulator\\\",\\\"__closure\\\",\\\"stackTag\\\",\\\"sharedEvent\\\",\\\"goBackGesture\\\",\\\"removeListener\\\",\\\"lockAxis\\\",\\\"step\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/animationManager.js\\\"],\\\"mappings\\\":\\\"AA2BO,SAAAA,gEAAwDA,CAAAC,sBAAA,QAAAC,WAAA,CAAAC,iBAAA,OAAAC,SAAA,CAG7D,KAAM,CACJC,QAAQ,CAARA,QAAQ,CACRC,WAAW,CAAXA,WAAW,CACXC,aAAA,CAAAA,aACF,CAAC,CAAGN,sBAAsB,CAC1BK,WAAW,CAACE,cAAc,CAACH,QAAQ,CAAC,CACpC,KAAM,CAAAI,QAAQ,CAAGP,WAAW,CAACK,aAAa,CAAC,CAC3C,KAAM,CAAAG,IAAI,CAAGP,iBAAiB,CAACG,WAAW,CAACK,KAAK,CAAEV,sBAAsB,CAAEQ,QAAQ,CAAC,CACnFC,IAAI,CAAC,CAAC,CACR\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const finishScreenTransition = exports.finishScreenTransition = function () {\n    const _e = [new global.Error(), -3, -27];\n    const finishScreenTransition = function (screenTransitionConfig) {\n      const {\n        stackTag,\n        sharedEvent,\n        goBackGesture\n      } = screenTransitionConfig;\n      sharedEvent.removeListener(stackTag);\n      const lockAxis = getLockAxis(goBackGesture);\n      const step = (0, _swipeSimulator.getSwipeSimulator)(sharedEvent.value, screenTransitionConfig, lockAxis);\n      step();\n    };\n    finishScreenTransition.__closure = {\n      getLockAxis,\n      getSwipeSimulator: _swipeSimulator.getSwipeSimulator\n    };\n    finishScreenTransition.__workletHash = 3004477788376;\n    finishScreenTransition.__initData = _worklet_3004477788376_init_data;\n    finishScreenTransition.__stackDetails = _e;\n    return finishScreenTransition;\n  }();\n});", "lineCount": 87, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "startScreenTransition"], [7, 31, 1, 13], [7, 34, 1, 13, "exports"], [7, 41, 1, 13], [7, 42, 1, 13, "finishScreenTransition"], [7, 64, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_ConfigHelper"], [8, 19, 3, 0], [8, 22, 3, 0, "require"], [8, 29, 3, 0], [8, 30, 3, 0, "_dependencyMap"], [8, 44, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_styleUpdater"], [9, 19, 4, 0], [9, 22, 4, 0, "require"], [9, 29, 4, 0], [9, 30, 4, 0, "_dependencyMap"], [9, 44, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_swipeSimulator"], [10, 21, 5, 0], [10, 24, 5, 0, "require"], [10, 31, 5, 0], [10, 32, 5, 0, "_dependencyMap"], [10, 46, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "configureProps"], [11, 34, 6, 14], [11, 36, 6, 15], [11, 37, 6, 16], [12, 2, 6, 17], [12, 8, 6, 17, "_worklet_15010796722453_init_data"], [12, 41, 6, 17], [13, 4, 6, 17, "code"], [13, 8, 6, 17], [14, 4, 6, 17, "location"], [14, 12, 6, 17], [15, 4, 6, 17, "sourceMap"], [15, 13, 6, 17], [16, 4, 6, 17, "version"], [16, 11, 6, 17], [17, 2, 6, 17], [18, 2, 6, 17], [18, 8, 6, 17, "startScreenTransition"], [18, 29, 6, 17], [18, 32, 6, 17, "exports"], [18, 39, 6, 17], [18, 40, 6, 17, "startScreenTransition"], [18, 61, 6, 17], [18, 64, 7, 7], [19, 4, 7, 7], [19, 10, 7, 7, "_e"], [19, 12, 7, 7], [19, 20, 7, 7, "global"], [19, 26, 7, 7], [19, 27, 7, 7, "Error"], [19, 32, 7, 7], [20, 4, 7, 7], [20, 10, 7, 7, "startScreenTransition"], [20, 31, 7, 7], [20, 43, 7, 7, "startScreenTransition"], [20, 44, 7, 38, "screenTransitionConfig"], [20, 66, 7, 60], [20, 68, 7, 62], [21, 6, 10, 2], [21, 12, 10, 8], [22, 8, 11, 4, "stackTag"], [22, 16, 11, 12], [23, 8, 12, 4, "sharedEvent"], [24, 6, 13, 2], [24, 7, 13, 3], [24, 10, 13, 6, "screenTransitionConfig"], [24, 32, 13, 28], [25, 6, 14, 2, "sharedEvent"], [25, 17, 14, 13], [25, 18, 14, 14, "addListener"], [25, 29, 14, 25], [25, 30, 14, 26, "stackTag"], [25, 38, 14, 34], [25, 40, 14, 36], [25, 46, 14, 42], [26, 8, 15, 4], [26, 12, 15, 4, "applyStyle"], [26, 36, 15, 14], [26, 38, 15, 15, "screenTransitionConfig"], [26, 60, 15, 37], [26, 62, 15, 39, "sharedEvent"], [26, 73, 15, 50], [26, 74, 15, 51, "value"], [26, 79, 15, 56], [26, 80, 15, 57], [27, 6, 16, 2], [27, 7, 16, 3], [27, 8, 16, 4], [28, 4, 17, 0], [28, 5, 17, 1], [29, 4, 17, 1, "startScreenTransition"], [29, 25, 17, 1], [29, 26, 17, 1, "__closure"], [29, 35, 17, 1], [30, 6, 17, 1, "applyStyle"], [30, 16, 17, 1], [30, 18, 15, 4, "applyStyle"], [31, 4, 15, 14], [32, 4, 15, 14, "startScreenTransition"], [32, 25, 15, 14], [32, 26, 15, 14, "__workletHash"], [32, 39, 15, 14], [33, 4, 15, 14, "startScreenTransition"], [33, 25, 15, 14], [33, 26, 15, 14, "__initData"], [33, 36, 15, 14], [33, 39, 15, 14, "_worklet_15010796722453_init_data"], [33, 72, 15, 14], [34, 4, 15, 14, "startScreenTransition"], [34, 25, 15, 14], [34, 26, 15, 14, "__stackDetails"], [34, 40, 15, 14], [34, 43, 15, 14, "_e"], [34, 45, 15, 14], [35, 4, 15, 14], [35, 11, 15, 14, "startScreenTransition"], [35, 32, 15, 14], [36, 2, 15, 14], [36, 3, 7, 7], [37, 2, 7, 7], [37, 8, 7, 7, "_worklet_13448177000651_init_data"], [37, 41, 7, 7], [38, 4, 7, 7, "code"], [38, 8, 7, 7], [39, 4, 7, 7, "location"], [39, 12, 7, 7], [40, 4, 7, 7, "sourceMap"], [40, 13, 7, 7], [41, 4, 7, 7, "version"], [41, 11, 7, 7], [42, 2, 7, 7], [43, 2, 7, 7], [43, 8, 7, 7, "getLockAxis"], [43, 19, 7, 7], [43, 22, 18, 0], [44, 4, 18, 0], [44, 10, 18, 0, "_e"], [44, 12, 18, 0], [44, 20, 18, 0, "global"], [44, 26, 18, 0], [44, 27, 18, 0, "Error"], [44, 32, 18, 0], [45, 4, 18, 0], [45, 10, 18, 0, "getLockAxis"], [45, 21, 18, 0], [45, 33, 18, 0, "getLockAxis"], [45, 34, 18, 21, "goBackGesture"], [45, 47, 18, 34], [45, 49, 18, 36], [46, 6, 21, 2], [46, 10, 21, 6], [46, 11, 21, 7], [46, 23, 21, 19], [46, 25, 21, 21], [46, 36, 21, 32], [46, 38, 21, 34], [46, 55, 21, 51], [46, 56, 21, 52], [46, 57, 21, 53, "includes"], [46, 65, 21, 61], [46, 66, 21, 62, "goBackGesture"], [46, 79, 21, 75], [46, 80, 21, 76], [46, 82, 21, 78], [47, 8, 22, 4], [47, 15, 22, 11], [47, 18, 22, 14], [48, 6, 23, 2], [48, 7, 23, 3], [48, 13, 23, 9], [48, 17, 23, 13], [48, 18, 23, 14], [48, 27, 23, 23], [48, 29, 23, 25], [48, 40, 23, 36], [48, 42, 23, 38], [48, 57, 23, 53], [48, 58, 23, 54], [48, 59, 23, 55, "includes"], [48, 67, 23, 63], [48, 68, 23, 64, "goBackGesture"], [48, 81, 23, 77], [48, 82, 23, 78], [48, 84, 23, 80], [49, 8, 24, 4], [49, 15, 24, 11], [49, 18, 24, 14], [50, 6, 25, 2], [51, 6, 26, 2], [51, 13, 26, 9, "undefined"], [51, 22, 26, 18], [52, 4, 27, 0], [52, 5, 27, 1], [53, 4, 27, 1, "getLockAxis"], [53, 15, 27, 1], [53, 16, 27, 1, "__closure"], [53, 25, 27, 1], [54, 4, 27, 1, "getLockAxis"], [54, 15, 27, 1], [54, 16, 27, 1, "__workletHash"], [54, 29, 27, 1], [55, 4, 27, 1, "getLockAxis"], [55, 15, 27, 1], [55, 16, 27, 1, "__initData"], [55, 26, 27, 1], [55, 29, 27, 1, "_worklet_13448177000651_init_data"], [55, 62, 27, 1], [56, 4, 27, 1, "getLockAxis"], [56, 15, 27, 1], [56, 16, 27, 1, "__stackDetails"], [56, 30, 27, 1], [56, 33, 27, 1, "_e"], [56, 35, 27, 1], [57, 4, 27, 1], [57, 11, 27, 1, "getLockAxis"], [57, 22, 27, 1], [58, 2, 27, 1], [58, 3, 18, 0], [59, 2, 18, 0], [59, 8, 18, 0, "_worklet_3004477788376_init_data"], [59, 40, 18, 0], [60, 4, 18, 0, "code"], [60, 8, 18, 0], [61, 4, 18, 0, "location"], [61, 12, 18, 0], [62, 4, 18, 0, "sourceMap"], [62, 13, 18, 0], [63, 4, 18, 0, "version"], [63, 11, 18, 0], [64, 2, 18, 0], [65, 2, 18, 0], [65, 8, 18, 0, "finishScreenTransition"], [65, 30, 18, 0], [65, 33, 18, 0, "exports"], [65, 40, 18, 0], [65, 41, 18, 0, "finishScreenTransition"], [65, 63, 18, 0], [65, 66, 28, 7], [66, 4, 28, 7], [66, 10, 28, 7, "_e"], [66, 12, 28, 7], [66, 20, 28, 7, "global"], [66, 26, 28, 7], [66, 27, 28, 7, "Error"], [66, 32, 28, 7], [67, 4, 28, 7], [67, 10, 28, 7, "finishScreenTransition"], [67, 32, 28, 7], [67, 44, 28, 7, "finishScreenTransition"], [67, 45, 28, 39, "screenTransitionConfig"], [67, 67, 28, 61], [67, 69, 28, 63], [68, 6, 31, 2], [68, 12, 31, 8], [69, 8, 32, 4, "stackTag"], [69, 16, 32, 12], [70, 8, 33, 4, "sharedEvent"], [70, 19, 33, 15], [71, 8, 34, 4, "goBackGesture"], [72, 6, 35, 2], [72, 7, 35, 3], [72, 10, 35, 6, "screenTransitionConfig"], [72, 32, 35, 28], [73, 6, 36, 2, "sharedEvent"], [73, 17, 36, 13], [73, 18, 36, 14, "removeListener"], [73, 32, 36, 28], [73, 33, 36, 29, "stackTag"], [73, 41, 36, 37], [73, 42, 36, 38], [74, 6, 37, 2], [74, 12, 37, 8, "lockAxis"], [74, 20, 37, 16], [74, 23, 37, 19, "getLockAxis"], [74, 34, 37, 30], [74, 35, 37, 31, "goBackGesture"], [74, 48, 37, 44], [74, 49, 37, 45], [75, 6, 38, 2], [75, 12, 38, 8, "step"], [75, 16, 38, 12], [75, 19, 38, 15], [75, 23, 38, 15, "getSwipeSimulator"], [75, 56, 38, 32], [75, 58, 38, 33, "sharedEvent"], [75, 69, 38, 44], [75, 70, 38, 45, "value"], [75, 75, 38, 50], [75, 77, 38, 52, "screenTransitionConfig"], [75, 99, 38, 74], [75, 101, 38, 76, "lockAxis"], [75, 109, 38, 84], [75, 110, 38, 85], [76, 6, 39, 2, "step"], [76, 10, 39, 6], [76, 11, 39, 7], [76, 12, 39, 8], [77, 4, 40, 0], [77, 5, 40, 1], [78, 4, 40, 1, "finishScreenTransition"], [78, 26, 40, 1], [78, 27, 40, 1, "__closure"], [78, 36, 40, 1], [79, 6, 40, 1, "getLockAxis"], [79, 17, 40, 1], [80, 6, 40, 1, "getSwipeSimulator"], [80, 23, 40, 1], [80, 25, 38, 15, "getSwipeSimulator"], [81, 4, 38, 32], [82, 4, 38, 32, "finishScreenTransition"], [82, 26, 38, 32], [82, 27, 38, 32, "__workletHash"], [82, 40, 38, 32], [83, 4, 38, 32, "finishScreenTransition"], [83, 26, 38, 32], [83, 27, 38, 32, "__initData"], [83, 37, 38, 32], [83, 40, 38, 32, "_worklet_3004477788376_init_data"], [83, 72, 38, 32], [84, 4, 38, 32, "finishScreenTransition"], [84, 26, 38, 32], [84, 27, 38, 32, "__stackDetails"], [84, 41, 38, 32], [84, 44, 38, 32, "_e"], [84, 46, 38, 32], [85, 4, 38, 32], [85, 11, 38, 32, "finishScreenTransition"], [85, 33, 38, 32], [86, 2, 38, 32], [86, 3, 28, 7], [87, 0, 28, 7], [87, 3]], "functionMap": {"names": ["<global>", "startScreenTransition", "sharedEvent.addListener$argument_1", "getLockAxis", "finishScreenTransition"], "mappings": "AAA;OCM;oCCO;GDE;CDC;AGC;CHS;OIC;CJY"}}, "type": "js/module"}]}