{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "fbjs/lib/invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 235}, "end": {"line": 11, "column": 43, "index": 278}}], "key": "bGUa+dDG2WEhPiIlobT3urS95UE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _invariant = _interopRequireDefault(require(_dependencyMap[1], \"fbjs/lib/invariant\"));\n  /**\n   * Copyright (c) Nicolas <PERSON>.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  class Share {\n    static share(content, options) {\n      if (options === void 0) {\n        options = {};\n      }\n      (0, _invariant.default)(typeof content === 'object' && content !== null, 'Content to share must be a valid object');\n      (0, _invariant.default)(typeof content.url === 'string' || typeof content.message === 'string', 'At least one of URL and message is required');\n      (0, _invariant.default)(typeof options === 'object' && options !== null, 'Options must be a valid object');\n      (0, _invariant.default)(!content.title || typeof content.title === 'string', 'Invalid title: title should be a string.');\n      if (window.navigator.share !== undefined) {\n        return window.navigator.share({\n          title: content.title,\n          text: content.message,\n          url: content.url\n        });\n      } else {\n        return Promise.reject(new Error('Share is not supported in this browser'));\n      }\n    }\n\n    /**\n     * The content was successfully shared.\n     */\n    static get sharedAction() {\n      return 'sharedAction';\n    }\n\n    /**\n     * The dialog has been dismissed.\n     * @platform ios\n     */\n    static get dismissedAction() {\n      return 'dismissedAction';\n    }\n  }\n  var _default = exports.default = Share;\n});", "lineCount": 54, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_invariant"], [7, 16, 11, 0], [7, 19, 11, 0, "_interopRequireDefault"], [7, 41, 11, 0], [7, 42, 11, 0, "require"], [7, 49, 11, 0], [7, 50, 11, 0, "_dependencyMap"], [7, 64, 11, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [18, 2, 12, 0], [18, 8, 12, 6, "Share"], [18, 13, 12, 11], [18, 14, 12, 12], [19, 4, 13, 2], [19, 11, 13, 9, "share"], [19, 16, 13, 14, "share"], [19, 17, 13, 15, "content"], [19, 24, 13, 22], [19, 26, 13, 24, "options"], [19, 33, 13, 31], [19, 35, 13, 33], [20, 6, 14, 4], [20, 10, 14, 8, "options"], [20, 17, 14, 15], [20, 22, 14, 20], [20, 27, 14, 25], [20, 28, 14, 26], [20, 30, 14, 28], [21, 8, 15, 6, "options"], [21, 15, 15, 13], [21, 18, 15, 16], [21, 19, 15, 17], [21, 20, 15, 18], [22, 6, 16, 4], [23, 6, 17, 4], [23, 10, 17, 4, "invariant"], [23, 28, 17, 13], [23, 30, 17, 14], [23, 37, 17, 21, "content"], [23, 44, 17, 28], [23, 49, 17, 33], [23, 57, 17, 41], [23, 61, 17, 45, "content"], [23, 68, 17, 52], [23, 73, 17, 57], [23, 77, 17, 61], [23, 79, 17, 63], [23, 120, 17, 104], [23, 121, 17, 105], [24, 6, 18, 4], [24, 10, 18, 4, "invariant"], [24, 28, 18, 13], [24, 30, 18, 14], [24, 37, 18, 21, "content"], [24, 44, 18, 28], [24, 45, 18, 29, "url"], [24, 48, 18, 32], [24, 53, 18, 37], [24, 61, 18, 45], [24, 65, 18, 49], [24, 72, 18, 56, "content"], [24, 79, 18, 63], [24, 80, 18, 64, "message"], [24, 87, 18, 71], [24, 92, 18, 76], [24, 100, 18, 84], [24, 102, 18, 86], [24, 147, 18, 131], [24, 148, 18, 132], [25, 6, 19, 4], [25, 10, 19, 4, "invariant"], [25, 28, 19, 13], [25, 30, 19, 14], [25, 37, 19, 21, "options"], [25, 44, 19, 28], [25, 49, 19, 33], [25, 57, 19, 41], [25, 61, 19, 45, "options"], [25, 68, 19, 52], [25, 73, 19, 57], [25, 77, 19, 61], [25, 79, 19, 63], [25, 111, 19, 95], [25, 112, 19, 96], [26, 6, 20, 4], [26, 10, 20, 4, "invariant"], [26, 28, 20, 13], [26, 30, 20, 14], [26, 31, 20, 15, "content"], [26, 38, 20, 22], [26, 39, 20, 23, "title"], [26, 44, 20, 28], [26, 48, 20, 32], [26, 55, 20, 39, "content"], [26, 62, 20, 46], [26, 63, 20, 47, "title"], [26, 68, 20, 52], [26, 73, 20, 57], [26, 81, 20, 65], [26, 83, 20, 67], [26, 125, 20, 109], [26, 126, 20, 110], [27, 6, 21, 4], [27, 10, 21, 8, "window"], [27, 16, 21, 14], [27, 17, 21, 15, "navigator"], [27, 26, 21, 24], [27, 27, 21, 25, "share"], [27, 32, 21, 30], [27, 37, 21, 35, "undefined"], [27, 46, 21, 44], [27, 48, 21, 46], [28, 8, 22, 6], [28, 15, 22, 13, "window"], [28, 21, 22, 19], [28, 22, 22, 20, "navigator"], [28, 31, 22, 29], [28, 32, 22, 30, "share"], [28, 37, 22, 35], [28, 38, 22, 36], [29, 10, 23, 8, "title"], [29, 15, 23, 13], [29, 17, 23, 15, "content"], [29, 24, 23, 22], [29, 25, 23, 23, "title"], [29, 30, 23, 28], [30, 10, 24, 8, "text"], [30, 14, 24, 12], [30, 16, 24, 14, "content"], [30, 23, 24, 21], [30, 24, 24, 22, "message"], [30, 31, 24, 29], [31, 10, 25, 8, "url"], [31, 13, 25, 11], [31, 15, 25, 13, "content"], [31, 22, 25, 20], [31, 23, 25, 21, "url"], [32, 8, 26, 6], [32, 9, 26, 7], [32, 10, 26, 8], [33, 6, 27, 4], [33, 7, 27, 5], [33, 13, 27, 11], [34, 8, 28, 6], [34, 15, 28, 13, "Promise"], [34, 22, 28, 20], [34, 23, 28, 21, "reject"], [34, 29, 28, 27], [34, 30, 28, 28], [34, 34, 28, 32, "Error"], [34, 39, 28, 37], [34, 40, 28, 38], [34, 80, 28, 78], [34, 81, 28, 79], [34, 82, 28, 80], [35, 6, 29, 4], [36, 4, 30, 2], [38, 4, 32, 2], [39, 0, 33, 0], [40, 0, 34, 0], [41, 4, 35, 2], [41, 15, 35, 13, "sharedAction"], [41, 27, 35, 25, "sharedAction"], [41, 28, 35, 25], [41, 30, 35, 28], [42, 6, 36, 4], [42, 13, 36, 11], [42, 27, 36, 25], [43, 4, 37, 2], [45, 4, 39, 2], [46, 0, 40, 0], [47, 0, 41, 0], [48, 0, 42, 0], [49, 4, 43, 2], [49, 15, 43, 13, "dismissedAction"], [49, 30, 43, 28, "dismissedAction"], [49, 31, 43, 28], [49, 33, 43, 31], [50, 6, 44, 4], [50, 13, 44, 11], [50, 30, 44, 28], [51, 4, 45, 2], [52, 2, 46, 0], [53, 2, 46, 1], [53, 6, 46, 1, "_default"], [53, 14, 46, 1], [53, 17, 46, 1, "exports"], [53, 24, 46, 1], [53, 25, 46, 1, "default"], [53, 32, 46, 1], [53, 35, 47, 15, "Share"], [53, 40, 47, 20], [54, 0, 47, 20], [54, 3]], "functionMap": {"names": ["<global>", "Share", "Share.share", "Share.get__sharedAction", "Share.get__dismissedAction"], "mappings": "AAA;ACW;ECC;GDiB;EEK;GFE;EGM;GHE;CDC"}}, "type": "js/module"}]}