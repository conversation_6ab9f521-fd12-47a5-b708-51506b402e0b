{"dependencies": [{"name": "./getRoutesCore", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 24, "index": 259}, "end": {"line": 6, "column": 50, "index": 285}}, {"start": {"line": 86, "column": 22, "index": 3522}, "end": {"line": 86, "column": 48, "index": 3548}}], "key": "gOO2v0sl1XvYI6QH2o9IVEEjbmQ=", "exportNames": ["*"]}}, {"name": "./views/Navigator", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 27, "column": 33, "index": 1275}, "end": {"line": 27, "column": 61, "index": 1303}}], "key": "PBpeZlMTHxnI1L+/mUlv77sLyo4=", "exportNames": ["*"]}}, {"name": "./views/Sitemap", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 41, "column": 59, "index": 1867}, "end": {"line": 41, "column": 85, "index": 1893}}], "key": "G8ud5EPcJ8MF2dMhl1o+nfNHuTs=", "exportNames": ["*"]}}, {"name": "./views/Unmatched", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 56, "column": 42, "index": 2473}, "end": {"line": 56, "column": 70, "index": 2501}}], "key": "i1x8xpZw0K+kAMz1gUdMaXw0H74=", "exportNames": ["*"]}}, {"name": "./getRoutesRedirects", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 71, "column": 31, "index": 3112}, "end": {"line": 71, "column": 62, "index": 3143}}], "key": "E6xJvmMQ8Tg9oU66GfkFALUrP4w=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getIgnoreList = exports.extrapolateGroups = exports.generateDynamic = void 0;\n  exports.getRoutes = getRoutes;\n  exports.getExactRoutes = getExactRoutes;\n  const getRoutesCore_1 = require(_dependencyMap[0], \"./getRoutesCore\");\n  /**\n   * Given a Metro context module, return an array of nested routes.\n   *\n   * This is a two step process:\n   *  1. Convert the RequireContext keys (file paths) into a directory tree.\n   *      - This should extrapolate array syntax into multiple routes\n   *      - Routes are given a specificity score\n   *  2. Flatten the directory tree into routes\n   *      - Routes in directories without _layout files are hoisted to the nearest _layout\n   *      - The name of the route is relative to the nearest _layout\n   *      - If multiple routes have the same name, the most specific route is used\n   */\n  function getRoutes(contextModule, options = {}) {\n    return (0, getRoutesCore_1.getRoutes)(contextModule, {\n      getSystemRoute({\n        route,\n        type\n      }, defaults) {\n        if (route === '' && type === 'layout') {\n          // Root layout when no layout is defined.\n          return {\n            type: 'layout',\n            loadRoute: () => ({\n              default: require(_dependencyMap[1], \"./views/Navigator\").DefaultNavigator\n            }),\n            // Generate a fake file name for the directory\n            contextKey: 'expo-router/build/views/Navigator.js',\n            route: '',\n            generated: true,\n            dynamic: null,\n            children: []\n          };\n        } else if (route === '_sitemap' && type === 'route') {\n          return {\n            loadRoute() {\n              const {\n                Sitemap,\n                getNavOptions\n              } = require(_dependencyMap[2], \"./views/Sitemap\");\n              return {\n                default: Sitemap,\n                getNavOptions\n              };\n            },\n            route: '_sitemap',\n            type: 'route',\n            contextKey: 'expo-router/build/views/Sitemap.js',\n            generated: true,\n            internal: true,\n            dynamic: null,\n            children: []\n          };\n        } else if (route === '+not-found' && type === 'route') {\n          return {\n            loadRoute() {\n              return {\n                default: require(_dependencyMap[3], \"./views/Unmatched\").Unmatched\n              };\n            },\n            type: 'route',\n            route: '+not-found',\n            contextKey: 'expo-router/build/views/Unmatched.js',\n            generated: true,\n            internal: true,\n            dynamic: [{\n              name: '+not-found',\n              deep: true,\n              notFound: true\n            }],\n            children: []\n          };\n        } else if ((type === 'redirect' || type === 'rewrite') && defaults) {\n          return {\n            ...defaults,\n            loadRoute() {\n              return require(_dependencyMap[4], \"./getRoutesRedirects\").getRedirectModule(route);\n            }\n          };\n        }\n        throw new Error(`Unknown system route: ${route} and type: ${type}`);\n      },\n      ...options\n    });\n  }\n  function getExactRoutes(contextModule, options = {}) {\n    return getRoutes(contextModule, {\n      ...options,\n      skipGenerated: true\n    });\n  }\n  var getRoutesCore_2 = require(_dependencyMap[0], \"./getRoutesCore\");\n  Object.defineProperty(exports, \"generateDynamic\", {\n    enumerable: true,\n    get: function () {\n      return getRoutesCore_2.generateDynamic;\n    }\n  });\n  Object.defineProperty(exports, \"extrapolateGroups\", {\n    enumerable: true,\n    get: function () {\n      return getRoutesCore_2.extrapolateGroups;\n    }\n  });\n  Object.defineProperty(exports, \"getIgnoreList\", {\n    enumerable: true,\n    get: function () {\n      return getRoutesCore_2.getIgnoreList;\n    }\n  });\n});", "lineCount": 120, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "getIgnoreList"], [7, 23, 3, 21], [7, 26, 3, 24, "exports"], [7, 33, 3, 31], [7, 34, 3, 32, "extrapolateGroups"], [7, 51, 3, 49], [7, 54, 3, 52, "exports"], [7, 61, 3, 59], [7, 62, 3, 60, "generateDynamic"], [7, 77, 3, 75], [7, 80, 3, 78], [7, 85, 3, 83], [7, 86, 3, 84], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "getRoutes"], [8, 19, 4, 17], [8, 22, 4, 20, "getRoutes"], [8, 31, 4, 29], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "getExactRoutes"], [9, 24, 5, 22], [9, 27, 5, 25, "getExactRoutes"], [9, 41, 5, 39], [10, 2, 6, 0], [10, 8, 6, 6, "getRoutesCore_1"], [10, 23, 6, 21], [10, 26, 6, 24, "require"], [10, 33, 6, 31], [10, 34, 6, 31, "_dependencyMap"], [10, 48, 6, 31], [10, 70, 6, 49], [10, 71, 6, 50], [11, 2, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 2, 19, 0], [23, 11, 19, 9, "getRoutes"], [23, 20, 19, 18, "getRoutes"], [23, 21, 19, 19, "contextModule"], [23, 34, 19, 32], [23, 36, 19, 34, "options"], [23, 43, 19, 41], [23, 46, 19, 44], [23, 47, 19, 45], [23, 48, 19, 46], [23, 50, 19, 48], [24, 4, 20, 4], [24, 11, 20, 11], [24, 12, 20, 12], [24, 13, 20, 13], [24, 15, 20, 15, "getRoutesCore_1"], [24, 30, 20, 30], [24, 31, 20, 31, "getRoutes"], [24, 40, 20, 40], [24, 42, 20, 42, "contextModule"], [24, 55, 20, 55], [24, 57, 20, 57], [25, 6, 21, 8, "getSystemRoute"], [25, 20, 21, 22, "getSystemRoute"], [25, 21, 21, 23], [26, 8, 21, 25, "route"], [26, 13, 21, 30], [27, 8, 21, 32, "type"], [28, 6, 21, 37], [28, 7, 21, 38], [28, 9, 21, 40, "defaults"], [28, 17, 21, 48], [28, 19, 21, 50], [29, 8, 22, 12], [29, 12, 22, 16, "route"], [29, 17, 22, 21], [29, 22, 22, 26], [29, 24, 22, 28], [29, 28, 22, 32, "type"], [29, 32, 22, 36], [29, 37, 22, 41], [29, 45, 22, 49], [29, 47, 22, 51], [30, 10, 23, 16], [31, 10, 24, 16], [31, 17, 24, 23], [32, 12, 25, 20, "type"], [32, 16, 25, 24], [32, 18, 25, 26], [32, 26, 25, 34], [33, 12, 26, 20, "loadRoute"], [33, 21, 26, 29], [33, 23, 26, 31, "loadRoute"], [33, 24, 26, 31], [33, 30, 26, 38], [34, 14, 27, 24, "default"], [34, 21, 27, 31], [34, 23, 27, 33, "require"], [34, 30, 27, 40], [34, 31, 27, 40, "_dependencyMap"], [34, 45, 27, 40], [34, 69, 27, 60], [34, 70, 27, 61], [34, 71, 28, 29, "DefaultNavigator"], [35, 12, 29, 20], [35, 13, 29, 21], [35, 14, 29, 22], [36, 12, 30, 20], [37, 12, 31, 20, "<PERSON><PERSON>ey"], [37, 22, 31, 30], [37, 24, 31, 32], [37, 62, 31, 70], [38, 12, 32, 20, "route"], [38, 17, 32, 25], [38, 19, 32, 27], [38, 21, 32, 29], [39, 12, 33, 20, "generated"], [39, 21, 33, 29], [39, 23, 33, 31], [39, 27, 33, 35], [40, 12, 34, 20, "dynamic"], [40, 19, 34, 27], [40, 21, 34, 29], [40, 25, 34, 33], [41, 12, 35, 20, "children"], [41, 20, 35, 28], [41, 22, 35, 30], [42, 10, 36, 16], [42, 11, 36, 17], [43, 8, 37, 12], [43, 9, 37, 13], [43, 15, 38, 17], [43, 19, 38, 21, "route"], [43, 24, 38, 26], [43, 29, 38, 31], [43, 39, 38, 41], [43, 43, 38, 45, "type"], [43, 47, 38, 49], [43, 52, 38, 54], [43, 59, 38, 61], [43, 61, 38, 63], [44, 10, 39, 16], [44, 17, 39, 23], [45, 12, 40, 20, "loadRoute"], [45, 21, 40, 29, "loadRoute"], [45, 22, 40, 29], [45, 24, 40, 32], [46, 14, 41, 24], [46, 20, 41, 30], [47, 16, 41, 32, "Sitemap"], [47, 23, 41, 39], [48, 16, 41, 41, "getNavOptions"], [49, 14, 41, 55], [49, 15, 41, 56], [49, 18, 41, 59, "require"], [49, 25, 41, 66], [49, 26, 41, 66, "_dependencyMap"], [49, 40, 41, 66], [49, 62, 41, 84], [49, 63, 41, 85], [50, 14, 42, 24], [50, 21, 42, 31], [51, 16, 42, 33, "default"], [51, 23, 42, 40], [51, 25, 42, 42, "Sitemap"], [51, 32, 42, 49], [52, 16, 42, 51, "getNavOptions"], [53, 14, 42, 65], [53, 15, 42, 66], [54, 12, 43, 20], [54, 13, 43, 21], [55, 12, 44, 20, "route"], [55, 17, 44, 25], [55, 19, 44, 27], [55, 29, 44, 37], [56, 12, 45, 20, "type"], [56, 16, 45, 24], [56, 18, 45, 26], [56, 25, 45, 33], [57, 12, 46, 20, "<PERSON><PERSON>ey"], [57, 22, 46, 30], [57, 24, 46, 32], [57, 60, 46, 68], [58, 12, 47, 20, "generated"], [58, 21, 47, 29], [58, 23, 47, 31], [58, 27, 47, 35], [59, 12, 48, 20, "internal"], [59, 20, 48, 28], [59, 22, 48, 30], [59, 26, 48, 34], [60, 12, 49, 20, "dynamic"], [60, 19, 49, 27], [60, 21, 49, 29], [60, 25, 49, 33], [61, 12, 50, 20, "children"], [61, 20, 50, 28], [61, 22, 50, 30], [62, 10, 51, 16], [62, 11, 51, 17], [63, 8, 52, 12], [63, 9, 52, 13], [63, 15, 53, 17], [63, 19, 53, 21, "route"], [63, 24, 53, 26], [63, 29, 53, 31], [63, 41, 53, 43], [63, 45, 53, 47, "type"], [63, 49, 53, 51], [63, 54, 53, 56], [63, 61, 53, 63], [63, 63, 53, 65], [64, 10, 54, 16], [64, 17, 54, 23], [65, 12, 55, 20, "loadRoute"], [65, 21, 55, 29, "loadRoute"], [65, 22, 55, 29], [65, 24, 55, 32], [66, 14, 56, 24], [66, 21, 56, 31], [67, 16, 56, 33, "default"], [67, 23, 56, 40], [67, 25, 56, 42, "require"], [67, 32, 56, 49], [67, 33, 56, 49, "_dependencyMap"], [67, 47, 56, 49], [67, 71, 56, 69], [67, 72, 56, 70], [67, 73, 56, 71, "Unmatched"], [68, 14, 56, 81], [68, 15, 56, 82], [69, 12, 57, 20], [69, 13, 57, 21], [70, 12, 58, 20, "type"], [70, 16, 58, 24], [70, 18, 58, 26], [70, 25, 58, 33], [71, 12, 59, 20, "route"], [71, 17, 59, 25], [71, 19, 59, 27], [71, 31, 59, 39], [72, 12, 60, 20, "<PERSON><PERSON>ey"], [72, 22, 60, 30], [72, 24, 60, 32], [72, 62, 60, 70], [73, 12, 61, 20, "generated"], [73, 21, 61, 29], [73, 23, 61, 31], [73, 27, 61, 35], [74, 12, 62, 20, "internal"], [74, 20, 62, 28], [74, 22, 62, 30], [74, 26, 62, 34], [75, 12, 63, 20, "dynamic"], [75, 19, 63, 27], [75, 21, 63, 29], [75, 22, 63, 30], [76, 14, 63, 32, "name"], [76, 18, 63, 36], [76, 20, 63, 38], [76, 32, 63, 50], [77, 14, 63, 52, "deep"], [77, 18, 63, 56], [77, 20, 63, 58], [77, 24, 63, 62], [78, 14, 63, 64, "notFound"], [78, 22, 63, 72], [78, 24, 63, 74], [79, 12, 63, 79], [79, 13, 63, 80], [79, 14, 63, 81], [80, 12, 64, 20, "children"], [80, 20, 64, 28], [80, 22, 64, 30], [81, 10, 65, 16], [81, 11, 65, 17], [82, 8, 66, 12], [82, 9, 66, 13], [82, 15, 67, 17], [82, 19, 67, 21], [82, 20, 67, 22, "type"], [82, 24, 67, 26], [82, 29, 67, 31], [82, 39, 67, 41], [82, 43, 67, 45, "type"], [82, 47, 67, 49], [82, 52, 67, 54], [82, 61, 67, 63], [82, 66, 67, 68, "defaults"], [82, 74, 67, 76], [82, 76, 67, 78], [83, 10, 68, 16], [83, 17, 68, 23], [84, 12, 69, 20], [84, 15, 69, 23, "defaults"], [84, 23, 69, 31], [85, 12, 70, 20, "loadRoute"], [85, 21, 70, 29, "loadRoute"], [85, 22, 70, 29], [85, 24, 70, 32], [86, 14, 71, 24], [86, 21, 71, 31, "require"], [86, 28, 71, 38], [86, 29, 71, 38, "_dependencyMap"], [86, 43, 71, 38], [86, 70, 71, 61], [86, 71, 71, 62], [86, 72, 71, 63, "getRedirectModule"], [86, 89, 71, 80], [86, 90, 71, 81, "route"], [86, 95, 71, 86], [86, 96, 71, 87], [87, 12, 72, 20], [88, 10, 73, 16], [88, 11, 73, 17], [89, 8, 74, 12], [90, 8, 75, 12], [90, 14, 75, 18], [90, 18, 75, 22, "Error"], [90, 23, 75, 27], [90, 24, 75, 28], [90, 49, 75, 53, "route"], [90, 54, 75, 58], [90, 68, 75, 72, "type"], [90, 72, 75, 76], [90, 74, 75, 78], [90, 75, 75, 79], [91, 6, 76, 8], [91, 7, 76, 9], [92, 6, 77, 8], [92, 9, 77, 11, "options"], [93, 4, 78, 4], [93, 5, 78, 5], [93, 6, 78, 6], [94, 2, 79, 0], [95, 2, 80, 0], [95, 11, 80, 9, "getExactRoutes"], [95, 25, 80, 23, "getExactRoutes"], [95, 26, 80, 24, "contextModule"], [95, 39, 80, 37], [95, 41, 80, 39, "options"], [95, 48, 80, 46], [95, 51, 80, 49], [95, 52, 80, 50], [95, 53, 80, 51], [95, 55, 80, 53], [96, 4, 81, 4], [96, 11, 81, 11, "getRoutes"], [96, 20, 81, 20], [96, 21, 81, 21, "contextModule"], [96, 34, 81, 34], [96, 36, 81, 36], [97, 6, 82, 8], [97, 9, 82, 11, "options"], [97, 16, 82, 18], [98, 6, 83, 8, "skipGenerated"], [98, 19, 83, 21], [98, 21, 83, 23], [99, 4, 84, 4], [99, 5, 84, 5], [99, 6, 84, 6], [100, 2, 85, 0], [101, 2, 86, 0], [101, 6, 86, 4, "getRoutesCore_2"], [101, 21, 86, 19], [101, 24, 86, 22, "require"], [101, 31, 86, 29], [101, 32, 86, 29, "_dependencyMap"], [101, 46, 86, 29], [101, 68, 86, 47], [101, 69, 86, 48], [102, 2, 87, 0, "Object"], [102, 8, 87, 6], [102, 9, 87, 7, "defineProperty"], [102, 23, 87, 21], [102, 24, 87, 22, "exports"], [102, 31, 87, 29], [102, 33, 87, 31], [102, 50, 87, 48], [102, 52, 87, 50], [103, 4, 87, 52, "enumerable"], [103, 14, 87, 62], [103, 16, 87, 64], [103, 20, 87, 68], [104, 4, 87, 70, "get"], [104, 7, 87, 73], [104, 9, 87, 75], [104, 18, 87, 75, "get"], [104, 19, 87, 75], [104, 21, 87, 87], [105, 6, 87, 89], [105, 13, 87, 96, "getRoutesCore_2"], [105, 28, 87, 111], [105, 29, 87, 112, "generateDynamic"], [105, 44, 87, 127], [106, 4, 87, 129], [107, 2, 87, 131], [107, 3, 87, 132], [107, 4, 87, 133], [108, 2, 88, 0, "Object"], [108, 8, 88, 6], [108, 9, 88, 7, "defineProperty"], [108, 23, 88, 21], [108, 24, 88, 22, "exports"], [108, 31, 88, 29], [108, 33, 88, 31], [108, 52, 88, 50], [108, 54, 88, 52], [109, 4, 88, 54, "enumerable"], [109, 14, 88, 64], [109, 16, 88, 66], [109, 20, 88, 70], [110, 4, 88, 72, "get"], [110, 7, 88, 75], [110, 9, 88, 77], [110, 18, 88, 77, "get"], [110, 19, 88, 77], [110, 21, 88, 89], [111, 6, 88, 91], [111, 13, 88, 98, "getRoutesCore_2"], [111, 28, 88, 113], [111, 29, 88, 114, "extrapolateGroups"], [111, 46, 88, 131], [112, 4, 88, 133], [113, 2, 88, 135], [113, 3, 88, 136], [113, 4, 88, 137], [114, 2, 89, 0, "Object"], [114, 8, 89, 6], [114, 9, 89, 7, "defineProperty"], [114, 23, 89, 21], [114, 24, 89, 22, "exports"], [114, 31, 89, 29], [114, 33, 89, 31], [114, 48, 89, 46], [114, 50, 89, 48], [115, 4, 89, 50, "enumerable"], [115, 14, 89, 60], [115, 16, 89, 62], [115, 20, 89, 66], [116, 4, 89, 68, "get"], [116, 7, 89, 71], [116, 9, 89, 73], [116, 18, 89, 73, "get"], [116, 19, 89, 73], [116, 21, 89, 85], [117, 6, 89, 87], [117, 13, 89, 94, "getRoutesCore_2"], [117, 28, 89, 109], [117, 29, 89, 110, "getIgnoreList"], [117, 42, 89, 123], [118, 4, 89, 125], [119, 2, 89, 127], [119, 3, 89, 128], [119, 4, 89, 129], [120, 0, 89, 130], [120, 3]], "functionMap": {"names": ["<global>", "getRoutes", "getSystemRoute", "loadRoute", "getExactRoutes", "Object.defineProperty$argument_2.get"], "mappings": "AAA;ACkB;QCE;+BCK;sBDG;oBCW;qBDG;oBCY;qBDE;oBCa;qBDE;SDI;CDG;AIC;CJK;2EKE,uDL;6EKC,yDL;yEKC,qDL"}}, "type": "js/module"}]}