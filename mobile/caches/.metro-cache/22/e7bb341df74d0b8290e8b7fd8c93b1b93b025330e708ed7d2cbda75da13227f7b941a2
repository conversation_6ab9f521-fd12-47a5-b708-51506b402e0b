{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 17, "index": 271}, "end": {"line": 7, "column": 52, "index": 306}}], "key": "uE+cRVNnMKkS9OYKR5fpRqPul5s=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 32, "index": 340}, "end": {"line": 8, "column": 48, "index": 356}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/index", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "55efhPHw3gz2FoQtoN2yI1VuhbM=", "exportNames": ["*"]}}, {"name": "use-latest-callback", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 46, "index": 453}, "end": {"line": 10, "column": 76, "index": 483}}], "key": "Pp42meoAsoBb9zFxGL4kkNu1jlQ=", "exportNames": ["*"]}}, {"name": "./useBackButton", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 24, "index": 510}, "end": {"line": 11, "column": 50, "index": 536}}], "key": "YmhII9Ytv9kFHu+pbu0LnaRB2V4=", "exportNames": ["*"]}}, {"name": "./useDocumentTitle", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 27, "index": 565}, "end": {"line": 12, "column": 56, "index": 594}}], "key": "BeC954vwiAJsRBJSvl79qfL3bnM=", "exportNames": ["*"]}}, {"name": "./useLinking", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 21, "index": 617}, "end": {"line": 13, "column": 44, "index": 640}}], "key": "tsrFVTWF4wbQufWa35aBIaOycS0=", "exportNames": ["*"]}}, {"name": "./useThenable", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 22, "index": 664}, "end": {"line": 14, "column": 46, "index": 688}}], "key": "i4iWk4ipI7VWnnvd/1N9IPV3M9I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/fork/NavigationContainer.js\";\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NavigationContainer = void 0;\n  const native_1 = require(_dependencyMap[1], \"@react-navigation/native\");\n  const react_1 = __importDefault(require(_dependencyMap[2], \"react\"));\n  const react_native_1 = require(_dependencyMap[3], \"react-native-web/dist/index\");\n  const use_latest_callback_1 = __importDefault(require(_dependencyMap[4], \"use-latest-callback\"));\n  const useBackButton_1 = require(_dependencyMap[5], \"./useBackButton\");\n  const useDocumentTitle_1 = require(_dependencyMap[6], \"./useDocumentTitle\");\n  const useLinking_1 = require(_dependencyMap[7], \"./useLinking\");\n  const useThenable_1 = require(_dependencyMap[8], \"./useThenable\");\n  globalThis.REACT_NAVIGATION_DEVTOOLS = new WeakMap();\n  /**\n   * Container component which holds the navigation state designed for React Native apps.\n   * This should be rendered at the root wrapping the whole app.\n   *\n   * @param props.initialState Initial state object for the navigation tree. When deep link handling is enabled, this will override deep links when specified. Make sure that you don't specify an `initialState` when there's a deep link (`Linking.getInitialURL()`).\n   * @param props.onReady Callback which is called after the navigation tree mounts.\n   * @param props.onStateChange Callback which is called with the latest navigation state when it changes.\n   * @param props.onUnhandledAction Callback which is called when an action is not handled.\n   * @param props.direction Text direction of the components. Defaults to `'ltr'`.\n   * @param props.theme Theme object for the UI elements.\n   * @param props.linking Options for deep linking. Deep link handling is enabled when this prop is provided, unless `linking.enabled` is `false`.\n   * @param props.fallback Fallback component to render until we have finished getting initial state when linking is enabled. Defaults to `null`.\n   * @param props.documentTitle Options to configure the document title on Web. Updating document title is handled by default unless `documentTitle.enabled` is `false`.\n   * @param props.children Child elements to render the content.\n   * @param props.ref Ref object which refers to the navigation object containing helper methods.\n   */\n  function NavigationContainerInner({\n    direction = react_native_1.I18nManager.getConstants().isRTL ? 'rtl' : 'ltr',\n    theme = native_1.DefaultTheme,\n    linking,\n    fallback = null,\n    documentTitle,\n    onReady,\n    onStateChange,\n    ...rest\n  }, ref) {\n    const isLinkingEnabled = linking ? linking.enabled !== false : false;\n    if (linking?.config) {\n      (0, native_1.validatePathConfig)(linking.config);\n    }\n    const refContainer = react_1.default.useRef(null);\n    (0, useBackButton_1.useBackButton)(refContainer);\n    (0, useDocumentTitle_1.useDocumentTitle)(refContainer, documentTitle);\n    const [lastUnhandledLink, setLastUnhandledLink] = react_1.default.useState();\n    const {\n      getInitialState\n    } = (0, useLinking_1.useLinking)(refContainer, {\n      enabled: isLinkingEnabled,\n      prefixes: [],\n      ...linking\n    }, setLastUnhandledLink);\n    const linkingContext = react_1.default.useMemo(() => ({\n      options: linking\n    }), [linking]);\n    const unhandledLinkingContext = react_1.default.useMemo(() => ({\n      lastUnhandledLink,\n      setLastUnhandledLink\n    }), [lastUnhandledLink, setLastUnhandledLink]);\n    const onReadyForLinkingHandling = (0, use_latest_callback_1.default)(() => {\n      // If the screen path matches lastUnhandledLink, we do not track it\n      const path = refContainer.current?.getCurrentRoute()?.path;\n      setLastUnhandledLink(previousLastUnhandledLink => {\n        if (previousLastUnhandledLink === path) {\n          return undefined;\n        }\n        return previousLastUnhandledLink;\n      });\n      onReady?.();\n    });\n    const onStateChangeForLinkingHandling = (0, use_latest_callback_1.default)(state => {\n      // If the screen path matches lastUnhandledLink, we do not track it\n      const path = refContainer.current?.getCurrentRoute()?.path;\n      setLastUnhandledLink(previousLastUnhandledLink => {\n        if (previousLastUnhandledLink === path) {\n          return undefined;\n        }\n        return previousLastUnhandledLink;\n      });\n      onStateChange?.(state);\n    });\n    // Add additional linking related info to the ref\n    // This will be used by the devtools\n    react_1.default.useEffect(() => {\n      if (refContainer.current) {\n        REACT_NAVIGATION_DEVTOOLS.set(refContainer.current, {\n          get linking() {\n            return {\n              ...linking,\n              enabled: isLinkingEnabled,\n              prefixes: linking?.prefixes ?? [],\n              getStateFromPath: linking?.getStateFromPath ?? native_1.getStateFromPath,\n              getPathFromState: linking?.getPathFromState ?? native_1.getPathFromState,\n              getActionFromState: linking?.getActionFromState ?? native_1.getActionFromState\n            };\n          }\n        });\n      }\n    });\n    const [isResolved, initialState] = (0, useThenable_1.useThenable)(getInitialState);\n    react_1.default.useImperativeHandle(ref, () => refContainer.current);\n    const isLinkingReady = rest.initialState != null || !isLinkingEnabled || isResolved;\n    if (!isLinkingReady) {\n      // This is temporary until we have Suspense for data-fetching\n      // Then the fallback will be handled by a parent `Suspense` component\n      return _reactNativeCssInteropJsxRuntime.jsx(native_1.ThemeProvider, {\n        value: theme,\n        children: fallback\n      });\n    }\n    return _reactNativeCssInteropJsxRuntime.jsx(native_1.LocaleDirContext.Provider, {\n      value: direction,\n      children: _reactNativeCssInteropJsxRuntime.jsx(native_1.UNSTABLE_UnhandledLinkingContext.Provider, {\n        value: unhandledLinkingContext,\n        children: _reactNativeCssInteropJsxRuntime.jsx(native_1.LinkingContext.Provider, {\n          value: linkingContext,\n          children: _reactNativeCssInteropJsxRuntime.jsx(native_1.BaseNavigationContainer, {\n            ...rest,\n            theme: theme,\n            onReady: onReadyForLinkingHandling,\n            onStateChange: onStateChangeForLinkingHandling,\n            initialState: rest.initialState == null ? initialState : rest.initialState,\n            ref: refContainer\n          })\n        })\n      })\n    });\n  }\n  exports.NavigationContainer = react_1.default.forwardRef(NavigationContainerInner);\n});", "lineCount": 142, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_reactNativeCssInteropJsxRuntime"], [4, 38, 1, 13], [4, 41, 1, 13, "require"], [4, 48, 1, 13], [4, 49, 1, 13, "_dependencyMap"], [4, 63, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_jsxFileName"], [5, 18, 1, 13], [6, 2, 2, 0], [6, 6, 2, 4, "__importDefault"], [6, 21, 2, 19], [6, 24, 2, 23], [6, 28, 2, 27], [6, 32, 2, 31], [6, 36, 2, 35], [6, 37, 2, 36, "__importDefault"], [6, 52, 2, 51], [6, 56, 2, 56], [6, 66, 2, 66, "mod"], [6, 69, 2, 69], [6, 71, 2, 71], [7, 4, 3, 4], [7, 11, 3, 12, "mod"], [7, 14, 3, 15], [7, 18, 3, 19, "mod"], [7, 21, 3, 22], [7, 22, 3, 23, "__esModule"], [7, 32, 3, 33], [7, 35, 3, 37, "mod"], [7, 38, 3, 40], [7, 41, 3, 43], [8, 6, 3, 45], [8, 15, 3, 54], [8, 17, 3, 56, "mod"], [9, 4, 3, 60], [9, 5, 3, 61], [10, 2, 4, 0], [10, 3, 4, 1], [11, 2, 5, 0, "Object"], [11, 8, 5, 6], [11, 9, 5, 7, "defineProperty"], [11, 23, 5, 21], [11, 24, 5, 22, "exports"], [11, 31, 5, 29], [11, 33, 5, 31], [11, 45, 5, 43], [11, 47, 5, 45], [12, 4, 5, 47, "value"], [12, 9, 5, 52], [12, 11, 5, 54], [13, 2, 5, 59], [13, 3, 5, 60], [13, 4, 5, 61], [14, 2, 6, 0, "exports"], [14, 9, 6, 7], [14, 10, 6, 8, "NavigationContainer"], [14, 29, 6, 27], [14, 32, 6, 30], [14, 37, 6, 35], [14, 38, 6, 36], [15, 2, 7, 0], [15, 8, 7, 6, "native_1"], [15, 16, 7, 14], [15, 19, 7, 17, "require"], [15, 26, 7, 24], [15, 27, 7, 24, "_dependencyMap"], [15, 41, 7, 24], [15, 72, 7, 51], [15, 73, 7, 52], [16, 2, 8, 0], [16, 8, 8, 6, "react_1"], [16, 15, 8, 13], [16, 18, 8, 16, "__importDefault"], [16, 33, 8, 31], [16, 34, 8, 32, "require"], [16, 41, 8, 39], [16, 42, 8, 39, "_dependencyMap"], [16, 56, 8, 39], [16, 68, 8, 47], [16, 69, 8, 48], [16, 70, 8, 49], [17, 2, 8, 50], [17, 8, 8, 50, "react_native_1"], [17, 22, 8, 50], [17, 25, 8, 50, "require"], [17, 32, 8, 50], [17, 33, 8, 50, "_dependencyMap"], [17, 47, 8, 50], [18, 2, 10, 0], [18, 8, 10, 6, "use_latest_callback_1"], [18, 29, 10, 27], [18, 32, 10, 30, "__importDefault"], [18, 47, 10, 45], [18, 48, 10, 46, "require"], [18, 55, 10, 53], [18, 56, 10, 53, "_dependencyMap"], [18, 70, 10, 53], [18, 96, 10, 75], [18, 97, 10, 76], [18, 98, 10, 77], [19, 2, 11, 0], [19, 8, 11, 6, "useBackButton_1"], [19, 23, 11, 21], [19, 26, 11, 24, "require"], [19, 33, 11, 31], [19, 34, 11, 31, "_dependencyMap"], [19, 48, 11, 31], [19, 70, 11, 49], [19, 71, 11, 50], [20, 2, 12, 0], [20, 8, 12, 6, "useDocumentTitle_1"], [20, 26, 12, 24], [20, 29, 12, 27, "require"], [20, 36, 12, 34], [20, 37, 12, 34, "_dependencyMap"], [20, 51, 12, 34], [20, 76, 12, 55], [20, 77, 12, 56], [21, 2, 13, 0], [21, 8, 13, 6, "useLinking_1"], [21, 20, 13, 18], [21, 23, 13, 21, "require"], [21, 30, 13, 28], [21, 31, 13, 28, "_dependencyMap"], [21, 45, 13, 28], [21, 64, 13, 43], [21, 65, 13, 44], [22, 2, 14, 0], [22, 8, 14, 6, "useThenable_1"], [22, 21, 14, 19], [22, 24, 14, 22, "require"], [22, 31, 14, 29], [22, 32, 14, 29, "_dependencyMap"], [22, 46, 14, 29], [22, 66, 14, 45], [22, 67, 14, 46], [23, 2, 15, 0, "globalThis"], [23, 12, 15, 10], [23, 13, 15, 11, "REACT_NAVIGATION_DEVTOOLS"], [23, 38, 15, 36], [23, 41, 15, 39], [23, 45, 15, 43, "WeakMap"], [23, 52, 15, 50], [23, 53, 15, 51], [23, 54, 15, 52], [24, 2, 16, 0], [25, 0, 17, 0], [26, 0, 18, 0], [27, 0, 19, 0], [28, 0, 20, 0], [29, 0, 21, 0], [30, 0, 22, 0], [31, 0, 23, 0], [32, 0, 24, 0], [33, 0, 25, 0], [34, 0, 26, 0], [35, 0, 27, 0], [36, 0, 28, 0], [37, 0, 29, 0], [38, 0, 30, 0], [39, 0, 31, 0], [40, 2, 32, 0], [40, 11, 32, 9, "NavigationContainerInner"], [40, 35, 32, 33, "NavigationContainerInner"], [40, 36, 32, 34], [41, 4, 32, 36, "direction"], [41, 13, 32, 45], [41, 16, 32, 48, "react_native_1"], [41, 30, 32, 62], [41, 31, 32, 63, "I18nManager"], [41, 42, 32, 74], [41, 43, 32, 75, "getConstants"], [41, 55, 32, 87], [41, 56, 32, 88], [41, 57, 32, 89], [41, 58, 32, 90, "isRTL"], [41, 63, 32, 95], [41, 66, 32, 98], [41, 71, 32, 103], [41, 74, 32, 106], [41, 79, 32, 111], [42, 4, 32, 113, "theme"], [42, 9, 32, 118], [42, 12, 32, 121, "native_1"], [42, 20, 32, 129], [42, 21, 32, 130, "DefaultTheme"], [42, 33, 32, 142], [43, 4, 32, 144, "linking"], [43, 11, 32, 151], [44, 4, 32, 153, "fallback"], [44, 12, 32, 161], [44, 15, 32, 164], [44, 19, 32, 168], [45, 4, 32, 170, "documentTitle"], [45, 17, 32, 183], [46, 4, 32, 185, "onReady"], [46, 11, 32, 192], [47, 4, 32, 194, "onStateChange"], [47, 17, 32, 207], [48, 4, 32, 209], [48, 7, 32, 212, "rest"], [49, 2, 32, 217], [49, 3, 32, 218], [49, 5, 32, 220, "ref"], [49, 8, 32, 223], [49, 10, 32, 225], [50, 4, 33, 4], [50, 10, 33, 10, "isLinkingEnabled"], [50, 26, 33, 26], [50, 29, 33, 29, "linking"], [50, 36, 33, 36], [50, 39, 33, 39, "linking"], [50, 46, 33, 46], [50, 47, 33, 47, "enabled"], [50, 54, 33, 54], [50, 59, 33, 59], [50, 64, 33, 64], [50, 67, 33, 67], [50, 72, 33, 72], [51, 4, 34, 4], [51, 8, 34, 8, "linking"], [51, 15, 34, 15], [51, 17, 34, 17, "config"], [51, 23, 34, 23], [51, 25, 34, 25], [52, 6, 35, 8], [52, 7, 35, 9], [52, 8, 35, 10], [52, 10, 35, 12, "native_1"], [52, 18, 35, 20], [52, 19, 35, 21, "validatePathConfig"], [52, 37, 35, 39], [52, 39, 35, 41, "linking"], [52, 46, 35, 48], [52, 47, 35, 49, "config"], [52, 53, 35, 55], [52, 54, 35, 56], [53, 4, 36, 4], [54, 4, 37, 4], [54, 10, 37, 10, "ref<PERSON><PERSON><PERSON>"], [54, 22, 37, 22], [54, 25, 37, 25, "react_1"], [54, 32, 37, 32], [54, 33, 37, 33, "default"], [54, 40, 37, 40], [54, 41, 37, 41, "useRef"], [54, 47, 37, 47], [54, 48, 37, 48], [54, 52, 37, 52], [54, 53, 37, 53], [55, 4, 38, 4], [55, 5, 38, 5], [55, 6, 38, 6], [55, 8, 38, 8, "useBackButton_1"], [55, 23, 38, 23], [55, 24, 38, 24, "useBackButton"], [55, 37, 38, 37], [55, 39, 38, 39, "ref<PERSON><PERSON><PERSON>"], [55, 51, 38, 51], [55, 52, 38, 52], [56, 4, 39, 4], [56, 5, 39, 5], [56, 6, 39, 6], [56, 8, 39, 8, "useDocumentTitle_1"], [56, 26, 39, 26], [56, 27, 39, 27, "useDocumentTitle"], [56, 43, 39, 43], [56, 45, 39, 45, "ref<PERSON><PERSON><PERSON>"], [56, 57, 39, 57], [56, 59, 39, 59, "documentTitle"], [56, 72, 39, 72], [56, 73, 39, 73], [57, 4, 40, 4], [57, 10, 40, 10], [57, 11, 40, 11, "lastUnhandledLink"], [57, 28, 40, 28], [57, 30, 40, 30, "setLastUnhandledLink"], [57, 50, 40, 50], [57, 51, 40, 51], [57, 54, 40, 54, "react_1"], [57, 61, 40, 61], [57, 62, 40, 62, "default"], [57, 69, 40, 69], [57, 70, 40, 70, "useState"], [57, 78, 40, 78], [57, 79, 40, 79], [57, 80, 40, 80], [58, 4, 41, 4], [58, 10, 41, 10], [59, 6, 41, 12, "getInitialState"], [60, 4, 41, 28], [60, 5, 41, 29], [60, 8, 41, 32], [60, 9, 41, 33], [60, 10, 41, 34], [60, 12, 41, 36, "useLinking_1"], [60, 24, 41, 48], [60, 25, 41, 49, "useLinking"], [60, 35, 41, 59], [60, 37, 41, 61, "ref<PERSON><PERSON><PERSON>"], [60, 49, 41, 73], [60, 51, 41, 75], [61, 6, 42, 8, "enabled"], [61, 13, 42, 15], [61, 15, 42, 17, "isLinkingEnabled"], [61, 31, 42, 33], [62, 6, 43, 8, "prefixes"], [62, 14, 43, 16], [62, 16, 43, 18], [62, 18, 43, 20], [63, 6, 44, 8], [63, 9, 44, 11, "linking"], [64, 4, 45, 4], [64, 5, 45, 5], [64, 7, 45, 7, "setLastUnhandledLink"], [64, 27, 45, 27], [64, 28, 45, 28], [65, 4, 46, 4], [65, 10, 46, 10, "linkingContext"], [65, 24, 46, 24], [65, 27, 46, 27, "react_1"], [65, 34, 46, 34], [65, 35, 46, 35, "default"], [65, 42, 46, 42], [65, 43, 46, 43, "useMemo"], [65, 50, 46, 50], [65, 51, 46, 51], [65, 58, 46, 58], [66, 6, 46, 60, "options"], [66, 13, 46, 67], [66, 15, 46, 69, "linking"], [67, 4, 46, 77], [67, 5, 46, 78], [67, 6, 46, 79], [67, 8, 46, 81], [67, 9, 46, 82, "linking"], [67, 16, 46, 89], [67, 17, 46, 90], [67, 18, 46, 91], [68, 4, 47, 4], [68, 10, 47, 10, "unhandledLinkingContext"], [68, 33, 47, 33], [68, 36, 47, 36, "react_1"], [68, 43, 47, 43], [68, 44, 47, 44, "default"], [68, 51, 47, 51], [68, 52, 47, 52, "useMemo"], [68, 59, 47, 59], [68, 60, 47, 60], [68, 67, 47, 67], [69, 6, 47, 69, "lastUnhandledLink"], [69, 23, 47, 86], [70, 6, 47, 88, "setLastUnhandledLink"], [71, 4, 47, 109], [71, 5, 47, 110], [71, 6, 47, 111], [71, 8, 47, 113], [71, 9, 47, 114, "lastUnhandledLink"], [71, 26, 47, 131], [71, 28, 47, 133, "setLastUnhandledLink"], [71, 48, 47, 153], [71, 49, 47, 154], [71, 50, 47, 155], [72, 4, 48, 4], [72, 10, 48, 10, "onReadyForLinkingHandling"], [72, 35, 48, 35], [72, 38, 48, 38], [72, 39, 48, 39], [72, 40, 48, 40], [72, 42, 48, 42, "use_latest_callback_1"], [72, 63, 48, 63], [72, 64, 48, 64, "default"], [72, 71, 48, 71], [72, 73, 48, 73], [72, 79, 48, 79], [73, 6, 49, 8], [74, 6, 50, 8], [74, 12, 50, 14, "path"], [74, 16, 50, 18], [74, 19, 50, 21, "ref<PERSON><PERSON><PERSON>"], [74, 31, 50, 33], [74, 32, 50, 34, "current"], [74, 39, 50, 41], [74, 41, 50, 43, "getCurrentRoute"], [74, 56, 50, 58], [74, 57, 50, 59], [74, 58, 50, 60], [74, 60, 50, 62, "path"], [74, 64, 50, 66], [75, 6, 51, 8, "setLastUnhandledLink"], [75, 26, 51, 28], [75, 27, 51, 30, "previousLastUnhandledLink"], [75, 52, 51, 55], [75, 56, 51, 60], [76, 8, 52, 12], [76, 12, 52, 16, "previousLastUnhandledLink"], [76, 37, 52, 41], [76, 42, 52, 46, "path"], [76, 46, 52, 50], [76, 48, 52, 52], [77, 10, 53, 16], [77, 17, 53, 23, "undefined"], [77, 26, 53, 32], [78, 8, 54, 12], [79, 8, 55, 12], [79, 15, 55, 19, "previousLastUnhandledLink"], [79, 40, 55, 44], [80, 6, 56, 8], [80, 7, 56, 9], [80, 8, 56, 10], [81, 6, 57, 8, "onReady"], [81, 13, 57, 15], [81, 16, 57, 18], [81, 17, 57, 19], [82, 4, 58, 4], [82, 5, 58, 5], [82, 6, 58, 6], [83, 4, 59, 4], [83, 10, 59, 10, "onStateChangeForLinkingHandling"], [83, 41, 59, 41], [83, 44, 59, 44], [83, 45, 59, 45], [83, 46, 59, 46], [83, 48, 59, 48, "use_latest_callback_1"], [83, 69, 59, 69], [83, 70, 59, 70, "default"], [83, 77, 59, 77], [83, 79, 59, 80, "state"], [83, 84, 59, 85], [83, 88, 59, 90], [84, 6, 60, 8], [85, 6, 61, 8], [85, 12, 61, 14, "path"], [85, 16, 61, 18], [85, 19, 61, 21, "ref<PERSON><PERSON><PERSON>"], [85, 31, 61, 33], [85, 32, 61, 34, "current"], [85, 39, 61, 41], [85, 41, 61, 43, "getCurrentRoute"], [85, 56, 61, 58], [85, 57, 61, 59], [85, 58, 61, 60], [85, 60, 61, 62, "path"], [85, 64, 61, 66], [86, 6, 62, 8, "setLastUnhandledLink"], [86, 26, 62, 28], [86, 27, 62, 30, "previousLastUnhandledLink"], [86, 52, 62, 55], [86, 56, 62, 60], [87, 8, 63, 12], [87, 12, 63, 16, "previousLastUnhandledLink"], [87, 37, 63, 41], [87, 42, 63, 46, "path"], [87, 46, 63, 50], [87, 48, 63, 52], [88, 10, 64, 16], [88, 17, 64, 23, "undefined"], [88, 26, 64, 32], [89, 8, 65, 12], [90, 8, 66, 12], [90, 15, 66, 19, "previousLastUnhandledLink"], [90, 40, 66, 44], [91, 6, 67, 8], [91, 7, 67, 9], [91, 8, 67, 10], [92, 6, 68, 8, "onStateChange"], [92, 19, 68, 21], [92, 22, 68, 24, "state"], [92, 27, 68, 29], [92, 28, 68, 30], [93, 4, 69, 4], [93, 5, 69, 5], [93, 6, 69, 6], [94, 4, 70, 4], [95, 4, 71, 4], [96, 4, 72, 4, "react_1"], [96, 11, 72, 11], [96, 12, 72, 12, "default"], [96, 19, 72, 19], [96, 20, 72, 20, "useEffect"], [96, 29, 72, 29], [96, 30, 72, 30], [96, 36, 72, 36], [97, 6, 73, 8], [97, 10, 73, 12, "ref<PERSON><PERSON><PERSON>"], [97, 22, 73, 24], [97, 23, 73, 25, "current"], [97, 30, 73, 32], [97, 32, 73, 34], [98, 8, 74, 12, "REACT_NAVIGATION_DEVTOOLS"], [98, 33, 74, 37], [98, 34, 74, 38, "set"], [98, 37, 74, 41], [98, 38, 74, 42, "ref<PERSON><PERSON><PERSON>"], [98, 50, 74, 54], [98, 51, 74, 55, "current"], [98, 58, 74, 62], [98, 60, 74, 64], [99, 10, 75, 16], [99, 14, 75, 20, "linking"], [99, 21, 75, 27, "linking"], [99, 22, 75, 27], [99, 24, 75, 30], [100, 12, 76, 20], [100, 19, 76, 27], [101, 14, 77, 24], [101, 17, 77, 27, "linking"], [101, 24, 77, 34], [102, 14, 78, 24, "enabled"], [102, 21, 78, 31], [102, 23, 78, 33, "isLinkingEnabled"], [102, 39, 78, 49], [103, 14, 79, 24, "prefixes"], [103, 22, 79, 32], [103, 24, 79, 34, "linking"], [103, 31, 79, 41], [103, 33, 79, 43, "prefixes"], [103, 41, 79, 51], [103, 45, 79, 55], [103, 47, 79, 57], [104, 14, 80, 24, "getStateFromPath"], [104, 30, 80, 40], [104, 32, 80, 42, "linking"], [104, 39, 80, 49], [104, 41, 80, 51, "getStateFromPath"], [104, 57, 80, 67], [104, 61, 80, 71, "native_1"], [104, 69, 80, 79], [104, 70, 80, 80, "getStateFromPath"], [104, 86, 80, 96], [105, 14, 81, 24, "getPathFromState"], [105, 30, 81, 40], [105, 32, 81, 42, "linking"], [105, 39, 81, 49], [105, 41, 81, 51, "getPathFromState"], [105, 57, 81, 67], [105, 61, 81, 71, "native_1"], [105, 69, 81, 79], [105, 70, 81, 80, "getPathFromState"], [105, 86, 81, 96], [106, 14, 82, 24, "getActionFromState"], [106, 32, 82, 42], [106, 34, 82, 44, "linking"], [106, 41, 82, 51], [106, 43, 82, 53, "getActionFromState"], [106, 61, 82, 71], [106, 65, 82, 75, "native_1"], [106, 73, 82, 83], [106, 74, 82, 84, "getActionFromState"], [107, 12, 83, 20], [107, 13, 83, 21], [108, 10, 84, 16], [109, 8, 85, 12], [109, 9, 85, 13], [109, 10, 85, 14], [110, 6, 86, 8], [111, 4, 87, 4], [111, 5, 87, 5], [111, 6, 87, 6], [112, 4, 88, 4], [112, 10, 88, 10], [112, 11, 88, 11, "isResolved"], [112, 21, 88, 21], [112, 23, 88, 23, "initialState"], [112, 35, 88, 35], [112, 36, 88, 36], [112, 39, 88, 39], [112, 40, 88, 40], [112, 41, 88, 41], [112, 43, 88, 43, "useThenable_1"], [112, 56, 88, 56], [112, 57, 88, 57, "useThenable"], [112, 68, 88, 68], [112, 70, 88, 70, "getInitialState"], [112, 85, 88, 85], [112, 86, 88, 86], [113, 4, 89, 4, "react_1"], [113, 11, 89, 11], [113, 12, 89, 12, "default"], [113, 19, 89, 19], [113, 20, 89, 20, "useImperativeHandle"], [113, 39, 89, 39], [113, 40, 89, 40, "ref"], [113, 43, 89, 43], [113, 45, 89, 45], [113, 51, 89, 51, "ref<PERSON><PERSON><PERSON>"], [113, 63, 89, 63], [113, 64, 89, 64, "current"], [113, 71, 89, 71], [113, 72, 89, 72], [114, 4, 90, 4], [114, 10, 90, 10, "isLinkingReady"], [114, 24, 90, 24], [114, 27, 90, 27, "rest"], [114, 31, 90, 31], [114, 32, 90, 32, "initialState"], [114, 44, 90, 44], [114, 48, 90, 48], [114, 52, 90, 52], [114, 56, 90, 56], [114, 57, 90, 57, "isLinkingEnabled"], [114, 73, 90, 73], [114, 77, 90, 77, "isResolved"], [114, 87, 90, 87], [115, 4, 91, 4], [115, 8, 91, 8], [115, 9, 91, 9, "isLinkingReady"], [115, 23, 91, 23], [115, 25, 91, 25], [116, 6, 92, 8], [117, 6, 93, 8], [118, 6, 94, 8], [118, 13, 94, 15, "_reactNativeCssInteropJsxRuntime"], [118, 45, 94, 15], [118, 46, 94, 15, "jsx"], [118, 49, 94, 15], [118, 50, 94, 16, "native_1"], [118, 58, 94, 24], [118, 59, 94, 25, "ThemeProvider"], [118, 72, 94, 38], [119, 8, 94, 39, "value"], [119, 13, 94, 44], [119, 15, 94, 46, "theme"], [119, 20, 94, 52], [120, 8, 94, 52, "children"], [120, 16, 94, 52], [120, 18, 94, 54, "fallback"], [121, 6, 94, 62], [121, 7, 94, 87], [121, 8, 94, 88], [122, 4, 95, 4], [123, 4, 96, 4], [123, 11, 96, 12, "_reactNativeCssInteropJsxRuntime"], [123, 43, 96, 12], [123, 44, 96, 12, "jsx"], [123, 47, 96, 12], [123, 48, 96, 13, "native_1"], [123, 56, 96, 21], [123, 57, 96, 22, "LocaleDirContext"], [123, 73, 96, 38], [123, 74, 96, 39, "Provider"], [123, 82, 96, 47], [124, 6, 96, 48, "value"], [124, 11, 96, 53], [124, 13, 96, 55, "direction"], [124, 22, 96, 65], [125, 6, 96, 65, "children"], [125, 14, 96, 65], [125, 16, 97, 6, "_reactNativeCssInteropJsxRuntime"], [125, 48, 97, 6], [125, 49, 97, 6, "jsx"], [125, 52, 97, 6], [125, 53, 97, 7, "native_1"], [125, 61, 97, 15], [125, 62, 97, 16, "UNSTABLE_UnhandledLinkingContext"], [125, 94, 97, 48], [125, 95, 97, 49, "Provider"], [125, 103, 97, 57], [126, 8, 97, 58, "value"], [126, 13, 97, 63], [126, 15, 97, 65, "unhandledLinkingContext"], [126, 38, 97, 89], [127, 8, 97, 89, "children"], [127, 16, 97, 89], [127, 18, 98, 8, "_reactNativeCssInteropJsxRuntime"], [127, 50, 98, 8], [127, 51, 98, 8, "jsx"], [127, 54, 98, 8], [127, 55, 98, 9, "native_1"], [127, 63, 98, 17], [127, 64, 98, 18, "LinkingContext"], [127, 78, 98, 32], [127, 79, 98, 33, "Provider"], [127, 87, 98, 41], [128, 10, 98, 42, "value"], [128, 15, 98, 47], [128, 17, 98, 49, "linkingContext"], [128, 31, 98, 64], [129, 10, 98, 64, "children"], [129, 18, 98, 64], [129, 20, 99, 10, "_reactNativeCssInteropJsxRuntime"], [129, 52, 99, 10], [129, 53, 99, 10, "jsx"], [129, 56, 99, 10], [129, 57, 99, 11, "native_1"], [129, 65, 99, 19], [129, 66, 99, 20, "BaseNavigationContainer"], [129, 89, 99, 43], [130, 12, 99, 43], [130, 15, 99, 48, "rest"], [130, 19, 99, 52], [131, 12, 99, 54, "theme"], [131, 17, 99, 59], [131, 19, 99, 61, "theme"], [131, 24, 99, 67], [132, 12, 99, 68, "onReady"], [132, 19, 99, 75], [132, 21, 99, 77, "onReadyForLinkingHandling"], [132, 46, 99, 103], [133, 12, 99, 104, "onStateChange"], [133, 25, 99, 117], [133, 27, 99, 119, "onStateChangeForLinkingHandling"], [133, 58, 99, 151], [134, 12, 99, 152, "initialState"], [134, 24, 99, 164], [134, 26, 99, 166, "rest"], [134, 30, 99, 170], [134, 31, 99, 171, "initialState"], [134, 43, 99, 183], [134, 47, 99, 187], [134, 51, 99, 191], [134, 54, 99, 194, "initialState"], [134, 66, 99, 206], [134, 69, 99, 209, "rest"], [134, 73, 99, 213], [134, 74, 99, 214, "initialState"], [134, 86, 99, 227], [135, 12, 99, 228, "ref"], [135, 15, 99, 231], [135, 17, 99, 233, "ref<PERSON><PERSON><PERSON>"], [136, 10, 99, 246], [136, 11, 99, 247], [137, 8, 99, 248], [137, 9, 100, 42], [138, 6, 100, 43], [138, 7, 101, 58], [139, 4, 101, 59], [139, 5, 102, 40], [139, 6, 102, 41], [140, 2, 103, 0], [141, 2, 104, 0, "exports"], [141, 9, 104, 7], [141, 10, 104, 8, "NavigationContainer"], [141, 29, 104, 27], [141, 32, 104, 30, "react_1"], [141, 39, 104, 37], [141, 40, 104, 38, "default"], [141, 47, 104, 45], [141, 48, 104, 46, "forwardRef"], [141, 58, 104, 56], [141, 59, 104, 57, "NavigationContainerInner"], [141, 83, 104, 81], [141, 84, 104, 82], [142, 0, 104, 83], [142, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "NavigationContainerInner", "react_1._default.useMemo$argument_0", "setLastUnhandledLink$argument_0", "react_1._default.useEffect$argument_0", "REACT_NAVIGATION_DEVTOOLS.set$argument_1.get__linking", "react_1._default.useImperativeHandle$argument_1"], "mappings": "AAA;wDCC;CDE;AE4B;mDCc,4BD;4DCC,mDD;yEDC;6BGG;SHK;KCE;+EDC;6BGG;SHK;KCE;8BGG;gBCG;iBDS;KHG;6CKE,0BL;CFc"}}, "type": "js/module"}]}