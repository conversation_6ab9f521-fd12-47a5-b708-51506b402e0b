{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../src/private/featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 98}}], "key": "fdTx5edELD8GYD7vaakWfKKte1Y=", "exportNames": ["*"]}}, {"name": "../vendor/emitter/EventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 58}}], "key": "FSJ8dBSGMPKDnoV5nqp600Oqgzc=", "exportNames": ["*"]}}, {"name": "../BatchedBridge/BatchedBridge", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 63}}], "key": "RVO988+Zv/6E/rt0hBQ7t8lVl4o=", "exportNames": ["*"]}}, {"name": "../Utilities/infoLog", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 16}, "end": {"line": 18, "column": 47}}], "key": "JOQiliPDxvxpS4qCtoh0JoUHvB4=", "exportNames": ["*"]}}, {"name": "./TaskQueue", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 18}, "end": {"line": 19, "column": 40}}], "key": "JdVwdRfYYIkBEefq5ZvEtZ/aHHY=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 20, "column": 18}, "end": {"line": 20, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}, {"name": "./InteractionManagerStub", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 223, "column": 6}, "end": {"line": 223, "column": 41}}], "key": "6Axl/FjQxNlPydjZVdzFgWLPoUg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[1], \"../../src/private/featureflags/ReactNativeFeatureFlags\"));\n  var _EventEmitter = _interopRequireDefault(require(_dependencyMap[2], \"../vendor/emitter/EventEmitter\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var BatchedBridge = require(_dependencyMap[3], \"../BatchedBridge/BatchedBridge\").default;\n  var infoLog = require(_dependencyMap[4], \"../Utilities/infoLog\").default;\n  var TaskQueue = require(_dependencyMap[5], \"./TaskQueue\").default;\n  var invariant = require(_dependencyMap[6], \"invariant\");\n  var _emitter = new _EventEmitter.default();\n  var DEBUG_DELAY = 0;\n  var DEBUG = false;\n  var InteractionManagerImpl = {\n    Events: {\n      interactionStart: 'interactionStart',\n      interactionComplete: 'interactionComplete'\n    },\n    runAfterInteractions(task) {\n      var tasks = [];\n      var promise = new Promise(resolve => {\n        _scheduleUpdate();\n        if (task) {\n          tasks.push(task);\n        }\n        tasks.push({\n          run: resolve,\n          name: 'resolve ' + (task && task.name || '?')\n        });\n        _taskQueue.enqueueTasks(tasks);\n      });\n      return {\n        then: promise.then.bind(promise),\n        cancel: function () {\n          _taskQueue.cancelTasks(tasks);\n        }\n      };\n    },\n    createInteractionHandle() {\n      DEBUG && infoLog('InteractionManager: create interaction handle');\n      _scheduleUpdate();\n      var handle = ++_inc;\n      _addInteractionSet.add(handle);\n      return handle;\n    },\n    clearInteractionHandle(handle) {\n      DEBUG && infoLog('InteractionManager: clear interaction handle');\n      invariant(!!handle, 'InteractionManager: Must provide a handle to clear.');\n      _scheduleUpdate();\n      _addInteractionSet.delete(handle);\n      _deleteInteractionSet.add(handle);\n    },\n    addListener: _emitter.addListener.bind(_emitter),\n    setDeadline(deadline) {\n      _deadline = deadline;\n    }\n  };\n  var _interactionSet = new Set();\n  var _addInteractionSet = new Set();\n  var _deleteInteractionSet = new Set();\n  var _taskQueue = new TaskQueue({\n    onMoreTasks: _scheduleUpdate\n  });\n  var _nextUpdateHandle = 0;\n  var _inc = 0;\n  var _deadline = -1;\n  function _scheduleUpdate() {\n    if (!_nextUpdateHandle) {\n      if (_deadline > 0) {\n        _nextUpdateHandle = setTimeout(_processUpdate, 0 + DEBUG_DELAY);\n      } else {\n        _nextUpdateHandle = setImmediate(_processUpdate);\n      }\n    }\n  }\n  function _processUpdate() {\n    _nextUpdateHandle = 0;\n    var interactionCount = _interactionSet.size;\n    _addInteractionSet.forEach(handle => _interactionSet.add(handle));\n    _deleteInteractionSet.forEach(handle => _interactionSet.delete(handle));\n    var nextInteractionCount = _interactionSet.size;\n    if (interactionCount !== 0 && nextInteractionCount === 0) {\n      _emitter.emit(InteractionManager.Events.interactionComplete);\n    } else if (interactionCount === 0 && nextInteractionCount !== 0) {\n      _emitter.emit(InteractionManager.Events.interactionStart);\n    }\n    if (nextInteractionCount === 0) {\n      while (_taskQueue.hasTasksToProcess()) {\n        _taskQueue.processNext();\n        if (_deadline > 0 && BatchedBridge.getEventLoopRunningTime() >= _deadline) {\n          _scheduleUpdate();\n          break;\n        }\n      }\n    }\n    _addInteractionSet.clear();\n    _deleteInteractionSet.clear();\n  }\n  var InteractionManager = ReactNativeFeatureFlags.disableInteractionManager() ? require(_dependencyMap[7], \"./InteractionManagerStub\").default : InteractionManagerImpl;\n  var _default = exports.default = InteractionManager;\n});", "lineCount": 104, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "ReactNativeFeatureFlags"], [7, 29, 13, 0], [7, 32, 13, 0, "_interopRequireWildcard"], [7, 55, 13, 0], [7, 56, 13, 0, "require"], [7, 63, 13, 0], [7, 64, 13, 0, "_dependencyMap"], [7, 78, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "_EventEmitter"], [8, 19, 14, 0], [8, 22, 14, 0, "_interopRequireDefault"], [8, 44, 14, 0], [8, 45, 14, 0, "require"], [8, 52, 14, 0], [8, 53, 14, 0, "_dependencyMap"], [8, 67, 14, 0], [9, 2, 14, 58], [9, 11, 14, 58, "_interopRequireWildcard"], [9, 35, 14, 58, "e"], [9, 36, 14, 58], [9, 38, 14, 58, "t"], [9, 39, 14, 58], [9, 68, 14, 58, "WeakMap"], [9, 75, 14, 58], [9, 81, 14, 58, "r"], [9, 82, 14, 58], [9, 89, 14, 58, "WeakMap"], [9, 96, 14, 58], [9, 100, 14, 58, "n"], [9, 101, 14, 58], [9, 108, 14, 58, "WeakMap"], [9, 115, 14, 58], [9, 127, 14, 58, "_interopRequireWildcard"], [9, 150, 14, 58], [9, 162, 14, 58, "_interopRequireWildcard"], [9, 163, 14, 58, "e"], [9, 164, 14, 58], [9, 166, 14, 58, "t"], [9, 167, 14, 58], [9, 176, 14, 58, "t"], [9, 177, 14, 58], [9, 181, 14, 58, "e"], [9, 182, 14, 58], [9, 186, 14, 58, "e"], [9, 187, 14, 58], [9, 188, 14, 58, "__esModule"], [9, 198, 14, 58], [9, 207, 14, 58, "e"], [9, 208, 14, 58], [9, 214, 14, 58, "o"], [9, 215, 14, 58], [9, 217, 14, 58, "i"], [9, 218, 14, 58], [9, 220, 14, 58, "f"], [9, 221, 14, 58], [9, 226, 14, 58, "__proto__"], [9, 235, 14, 58], [9, 243, 14, 58, "default"], [9, 250, 14, 58], [9, 252, 14, 58, "e"], [9, 253, 14, 58], [9, 270, 14, 58, "e"], [9, 271, 14, 58], [9, 294, 14, 58, "e"], [9, 295, 14, 58], [9, 320, 14, 58, "e"], [9, 321, 14, 58], [9, 330, 14, 58, "f"], [9, 331, 14, 58], [9, 337, 14, 58, "o"], [9, 338, 14, 58], [9, 341, 14, 58, "t"], [9, 342, 14, 58], [9, 345, 14, 58, "n"], [9, 346, 14, 58], [9, 349, 14, 58, "r"], [9, 350, 14, 58], [9, 358, 14, 58, "o"], [9, 359, 14, 58], [9, 360, 14, 58, "has"], [9, 363, 14, 58], [9, 364, 14, 58, "e"], [9, 365, 14, 58], [9, 375, 14, 58, "o"], [9, 376, 14, 58], [9, 377, 14, 58, "get"], [9, 380, 14, 58], [9, 381, 14, 58, "e"], [9, 382, 14, 58], [9, 385, 14, 58, "o"], [9, 386, 14, 58], [9, 387, 14, 58, "set"], [9, 390, 14, 58], [9, 391, 14, 58, "e"], [9, 392, 14, 58], [9, 394, 14, 58, "f"], [9, 395, 14, 58], [9, 409, 14, 58, "_t"], [9, 411, 14, 58], [9, 415, 14, 58, "e"], [9, 416, 14, 58], [9, 432, 14, 58, "_t"], [9, 434, 14, 58], [9, 441, 14, 58, "hasOwnProperty"], [9, 455, 14, 58], [9, 456, 14, 58, "call"], [9, 460, 14, 58], [9, 461, 14, 58, "e"], [9, 462, 14, 58], [9, 464, 14, 58, "_t"], [9, 466, 14, 58], [9, 473, 14, 58, "i"], [9, 474, 14, 58], [9, 478, 14, 58, "o"], [9, 479, 14, 58], [9, 482, 14, 58, "Object"], [9, 488, 14, 58], [9, 489, 14, 58, "defineProperty"], [9, 503, 14, 58], [9, 508, 14, 58, "Object"], [9, 514, 14, 58], [9, 515, 14, 58, "getOwnPropertyDescriptor"], [9, 539, 14, 58], [9, 540, 14, 58, "e"], [9, 541, 14, 58], [9, 543, 14, 58, "_t"], [9, 545, 14, 58], [9, 552, 14, 58, "i"], [9, 553, 14, 58], [9, 554, 14, 58, "get"], [9, 557, 14, 58], [9, 561, 14, 58, "i"], [9, 562, 14, 58], [9, 563, 14, 58, "set"], [9, 566, 14, 58], [9, 570, 14, 58, "o"], [9, 571, 14, 58], [9, 572, 14, 58, "f"], [9, 573, 14, 58], [9, 575, 14, 58, "_t"], [9, 577, 14, 58], [9, 579, 14, 58, "i"], [9, 580, 14, 58], [9, 584, 14, 58, "f"], [9, 585, 14, 58], [9, 586, 14, 58, "_t"], [9, 588, 14, 58], [9, 592, 14, 58, "e"], [9, 593, 14, 58], [9, 594, 14, 58, "_t"], [9, 596, 14, 58], [9, 607, 14, 58, "f"], [9, 608, 14, 58], [9, 613, 14, 58, "e"], [9, 614, 14, 58], [9, 616, 14, 58, "t"], [9, 617, 14, 58], [10, 2, 17, 0], [10, 6, 17, 6, "BatchedBridge"], [10, 19, 17, 19], [10, 22, 17, 22, "require"], [10, 29, 17, 29], [10, 30, 17, 29, "_dependencyMap"], [10, 44, 17, 29], [10, 81, 17, 62], [10, 82, 17, 63], [10, 83, 17, 64, "default"], [10, 90, 17, 71], [11, 2, 18, 0], [11, 6, 18, 6, "infoLog"], [11, 13, 18, 13], [11, 16, 18, 16, "require"], [11, 23, 18, 23], [11, 24, 18, 23, "_dependencyMap"], [11, 38, 18, 23], [11, 65, 18, 46], [11, 66, 18, 47], [11, 67, 18, 48, "default"], [11, 74, 18, 55], [12, 2, 19, 0], [12, 6, 19, 6, "TaskQueue"], [12, 15, 19, 15], [12, 18, 19, 18, "require"], [12, 25, 19, 25], [12, 26, 19, 25, "_dependencyMap"], [12, 40, 19, 25], [12, 58, 19, 39], [12, 59, 19, 40], [12, 60, 19, 41, "default"], [12, 67, 19, 48], [13, 2, 20, 0], [13, 6, 20, 6, "invariant"], [13, 15, 20, 15], [13, 18, 20, 18, "require"], [13, 25, 20, 25], [13, 26, 20, 25, "_dependencyMap"], [13, 40, 20, 25], [13, 56, 20, 37], [13, 57, 20, 38], [14, 2, 26, 0], [14, 6, 26, 6, "_emitter"], [14, 14, 26, 14], [14, 17, 26, 17], [14, 21, 26, 21, "EventEmitter"], [14, 42, 26, 33], [14, 43, 29, 3], [14, 44, 29, 4], [15, 2, 31, 0], [15, 6, 31, 6, "DEBUG_DELAY"], [15, 17, 31, 20], [15, 20, 31, 23], [15, 21, 31, 24], [16, 2, 32, 0], [16, 6, 32, 6, "DEBUG"], [16, 11, 32, 18], [16, 14, 32, 21], [16, 19, 32, 26], [17, 2, 83, 0], [17, 6, 83, 6, "InteractionManagerImpl"], [17, 28, 83, 28], [17, 31, 83, 31], [18, 4, 84, 2, "Events"], [18, 10, 84, 8], [18, 12, 84, 10], [19, 6, 85, 4, "interactionStart"], [19, 22, 85, 20], [19, 24, 85, 22], [19, 42, 85, 40], [20, 6, 86, 4, "interactionComplete"], [20, 25, 86, 23], [20, 27, 86, 25], [21, 4, 87, 2], [21, 5, 87, 3], [22, 4, 93, 2, "runAfterInteractions"], [22, 24, 93, 22, "runAfterInteractions"], [22, 25, 93, 23, "task"], [22, 29, 93, 34], [22, 31, 100, 4], [23, 6, 101, 4], [23, 10, 101, 10, "tasks"], [23, 15, 101, 28], [23, 18, 101, 31], [23, 20, 101, 33], [24, 6, 102, 4], [24, 10, 102, 10, "promise"], [24, 17, 102, 17], [24, 20, 102, 20], [24, 24, 102, 24, "Promise"], [24, 31, 102, 31], [24, 32, 102, 33, "resolve"], [24, 39, 102, 52], [24, 43, 102, 57], [25, 8, 103, 6, "_scheduleUpdate"], [25, 23, 103, 21], [25, 24, 103, 22], [25, 25, 103, 23], [26, 8, 104, 6], [26, 12, 104, 10, "task"], [26, 16, 104, 14], [26, 18, 104, 16], [27, 10, 105, 8, "tasks"], [27, 15, 105, 13], [27, 16, 105, 14, "push"], [27, 20, 105, 18], [27, 21, 105, 19, "task"], [27, 25, 105, 23], [27, 26, 105, 24], [28, 8, 106, 6], [29, 8, 107, 6, "tasks"], [29, 13, 107, 11], [29, 14, 107, 12, "push"], [29, 18, 107, 16], [29, 19, 107, 17], [30, 10, 108, 8, "run"], [30, 13, 108, 11], [30, 15, 108, 13, "resolve"], [30, 22, 108, 20], [31, 10, 109, 8, "name"], [31, 14, 109, 12], [31, 16, 109, 14], [31, 26, 109, 24], [31, 30, 109, 29, "task"], [31, 34, 109, 33], [31, 38, 109, 37, "task"], [31, 42, 109, 41], [31, 43, 109, 42, "name"], [31, 47, 109, 46], [31, 51, 109, 51], [31, 54, 109, 54], [32, 8, 110, 6], [32, 9, 110, 7], [32, 10, 110, 8], [33, 8, 111, 6, "_taskQueue"], [33, 18, 111, 16], [33, 19, 111, 17, "enqueueTasks"], [33, 31, 111, 29], [33, 32, 111, 30, "tasks"], [33, 37, 111, 35], [33, 38, 111, 36], [34, 6, 112, 4], [34, 7, 112, 5], [34, 8, 112, 6], [35, 6, 113, 4], [35, 13, 113, 11], [36, 8, 115, 6, "then"], [36, 12, 115, 10], [36, 14, 115, 12, "promise"], [36, 21, 115, 19], [36, 22, 115, 20, "then"], [36, 26, 115, 24], [36, 27, 115, 25, "bind"], [36, 31, 115, 29], [36, 32, 115, 30, "promise"], [36, 39, 115, 37], [36, 40, 115, 38], [37, 8, 116, 6, "cancel"], [37, 14, 116, 12], [37, 16, 116, 14], [37, 25, 116, 14, "cancel"], [37, 26, 116, 14], [37, 28, 116, 26], [38, 10, 117, 8, "_taskQueue"], [38, 20, 117, 18], [38, 21, 117, 19, "cancelTasks"], [38, 32, 117, 30], [38, 33, 117, 31, "tasks"], [38, 38, 117, 36], [38, 39, 117, 37], [39, 8, 118, 6], [40, 6, 119, 4], [40, 7, 119, 5], [41, 4, 120, 2], [41, 5, 120, 3], [42, 4, 125, 2, "createInteractionHandle"], [42, 27, 125, 25, "createInteractionHandle"], [42, 28, 125, 25], [42, 30, 125, 36], [43, 6, 126, 4, "DEBUG"], [43, 11, 126, 9], [43, 15, 126, 13, "infoLog"], [43, 22, 126, 20], [43, 23, 126, 21], [43, 70, 126, 68], [43, 71, 126, 69], [44, 6, 127, 4, "_scheduleUpdate"], [44, 21, 127, 19], [44, 22, 127, 20], [44, 23, 127, 21], [45, 6, 128, 4], [45, 10, 128, 10, "handle"], [45, 16, 128, 16], [45, 19, 128, 19], [45, 21, 128, 21, "_inc"], [45, 25, 128, 25], [46, 6, 129, 4, "_addInteractionSet"], [46, 24, 129, 22], [46, 25, 129, 23, "add"], [46, 28, 129, 26], [46, 29, 129, 27, "handle"], [46, 35, 129, 33], [46, 36, 129, 34], [47, 6, 130, 4], [47, 13, 130, 11, "handle"], [47, 19, 130, 17], [48, 4, 131, 2], [48, 5, 131, 3], [49, 4, 136, 2, "clearInteractionHandle"], [49, 26, 136, 24, "clearInteractionHandle"], [49, 27, 136, 25, "handle"], [49, 33, 136, 39], [49, 35, 136, 41], [50, 6, 137, 4, "DEBUG"], [50, 11, 137, 9], [50, 15, 137, 13, "infoLog"], [50, 22, 137, 20], [50, 23, 137, 21], [50, 69, 137, 67], [50, 70, 137, 68], [51, 6, 138, 4, "invariant"], [51, 15, 138, 13], [51, 16, 138, 14], [51, 17, 138, 15], [51, 18, 138, 16, "handle"], [51, 24, 138, 22], [51, 26, 138, 24], [51, 79, 138, 77], [51, 80, 138, 78], [52, 6, 139, 4, "_scheduleUpdate"], [52, 21, 139, 19], [52, 22, 139, 20], [52, 23, 139, 21], [53, 6, 140, 4, "_addInteractionSet"], [53, 24, 140, 22], [53, 25, 140, 23, "delete"], [53, 31, 140, 29], [53, 32, 140, 30, "handle"], [53, 38, 140, 36], [53, 39, 140, 37], [54, 6, 141, 4, "_deleteInteractionSet"], [54, 27, 141, 25], [54, 28, 141, 26, "add"], [54, 31, 141, 29], [54, 32, 141, 30, "handle"], [54, 38, 141, 36], [54, 39, 141, 37], [55, 4, 142, 2], [55, 5, 142, 3], [56, 4, 146, 2, "addListener"], [56, 15, 146, 13], [56, 17, 146, 15, "_emitter"], [56, 25, 146, 23], [56, 26, 146, 24, "addListener"], [56, 37, 146, 35], [56, 38, 146, 36, "bind"], [56, 42, 146, 40], [56, 43, 146, 41, "_emitter"], [56, 51, 146, 49], [56, 52, 151, 24], [57, 4, 158, 2, "setDeadline"], [57, 15, 158, 13, "setDeadline"], [57, 16, 158, 14, "deadline"], [57, 24, 158, 30], [57, 26, 158, 32], [58, 6, 159, 4, "_deadline"], [58, 15, 159, 13], [58, 18, 159, 16, "deadline"], [58, 26, 159, 24], [59, 4, 160, 2], [60, 2, 161, 0], [60, 3, 161, 1], [61, 2, 163, 0], [61, 6, 163, 6, "_interactionSet"], [61, 21, 163, 21], [61, 24, 163, 24], [61, 28, 163, 28, "Set"], [61, 31, 163, 31], [61, 32, 163, 49], [61, 33, 163, 50], [62, 2, 164, 0], [62, 6, 164, 6, "_addInteractionSet"], [62, 24, 164, 24], [62, 27, 164, 27], [62, 31, 164, 31, "Set"], [62, 34, 164, 34], [62, 35, 164, 52], [62, 36, 164, 53], [63, 2, 165, 0], [63, 6, 165, 6, "_deleteInteractionSet"], [63, 27, 165, 27], [63, 30, 165, 30], [63, 34, 165, 34, "Set"], [63, 37, 165, 37], [63, 38, 165, 46], [63, 39, 165, 47], [64, 2, 166, 0], [64, 6, 166, 6, "_taskQueue"], [64, 16, 166, 16], [64, 19, 166, 19], [64, 23, 166, 23, "TaskQueue"], [64, 32, 166, 32], [64, 33, 166, 33], [65, 4, 166, 34, "onMoreTasks"], [65, 15, 166, 45], [65, 17, 166, 47, "_scheduleUpdate"], [66, 2, 166, 62], [66, 3, 166, 63], [66, 4, 166, 64], [67, 2, 167, 0], [67, 6, 167, 4, "_nextUpdate<PERSON><PERSON>le"], [67, 23, 167, 45], [67, 26, 167, 48], [67, 27, 167, 49], [68, 2, 168, 0], [68, 6, 168, 4, "_inc"], [68, 10, 168, 8], [68, 13, 168, 11], [68, 14, 168, 12], [69, 2, 169, 0], [69, 6, 169, 4, "_deadline"], [69, 15, 169, 13], [69, 18, 169, 16], [69, 19, 169, 17], [69, 20, 169, 18], [70, 2, 174, 0], [70, 11, 174, 9, "_scheduleUpdate"], [70, 26, 174, 24, "_scheduleUpdate"], [70, 27, 174, 24], [70, 29, 174, 27], [71, 4, 175, 2], [71, 8, 175, 6], [71, 9, 175, 7, "_nextUpdate<PERSON><PERSON>le"], [71, 26, 175, 24], [71, 28, 175, 26], [72, 6, 176, 4], [72, 10, 176, 8, "_deadline"], [72, 19, 176, 17], [72, 22, 176, 20], [72, 23, 176, 21], [72, 25, 176, 23], [73, 8, 177, 6, "_nextUpdate<PERSON><PERSON>le"], [73, 25, 177, 23], [73, 28, 177, 26, "setTimeout"], [73, 38, 177, 36], [73, 39, 177, 37, "_processUpdate"], [73, 53, 177, 51], [73, 55, 177, 53], [73, 56, 177, 54], [73, 59, 177, 57, "DEBUG_DELAY"], [73, 70, 177, 68], [73, 71, 177, 69], [74, 6, 178, 4], [74, 7, 178, 5], [74, 13, 178, 11], [75, 8, 179, 6, "_nextUpdate<PERSON><PERSON>le"], [75, 25, 179, 23], [75, 28, 179, 26, "setImmediate"], [75, 40, 179, 38], [75, 41, 179, 39, "_processUpdate"], [75, 55, 179, 53], [75, 56, 179, 54], [76, 6, 180, 4], [77, 4, 181, 2], [78, 2, 182, 0], [79, 2, 187, 0], [79, 11, 187, 9, "_processUpdate"], [79, 25, 187, 23, "_processUpdate"], [79, 26, 187, 23], [79, 28, 187, 26], [80, 4, 188, 2, "_nextUpdate<PERSON><PERSON>le"], [80, 21, 188, 19], [80, 24, 188, 22], [80, 25, 188, 23], [81, 4, 190, 2], [81, 8, 190, 8, "interactionCount"], [81, 24, 190, 24], [81, 27, 190, 27, "_interactionSet"], [81, 42, 190, 42], [81, 43, 190, 43, "size"], [81, 47, 190, 47], [82, 4, 191, 2, "_addInteractionSet"], [82, 22, 191, 20], [82, 23, 191, 21, "for<PERSON>ach"], [82, 30, 191, 28], [82, 31, 191, 29, "handle"], [82, 37, 191, 35], [82, 41, 191, 39, "_interactionSet"], [82, 56, 191, 54], [82, 57, 191, 55, "add"], [82, 60, 191, 58], [82, 61, 191, 59, "handle"], [82, 67, 191, 65], [82, 68, 191, 66], [82, 69, 191, 67], [83, 4, 192, 2, "_deleteInteractionSet"], [83, 25, 192, 23], [83, 26, 192, 24, "for<PERSON>ach"], [83, 33, 192, 31], [83, 34, 192, 32, "handle"], [83, 40, 192, 38], [83, 44, 192, 42, "_interactionSet"], [83, 59, 192, 57], [83, 60, 192, 58, "delete"], [83, 66, 192, 64], [83, 67, 192, 65, "handle"], [83, 73, 192, 71], [83, 74, 192, 72], [83, 75, 192, 73], [84, 4, 193, 2], [84, 8, 193, 8, "nextInteractionCount"], [84, 28, 193, 28], [84, 31, 193, 31, "_interactionSet"], [84, 46, 193, 46], [84, 47, 193, 47, "size"], [84, 51, 193, 51], [85, 4, 195, 2], [85, 8, 195, 6, "interactionCount"], [85, 24, 195, 22], [85, 29, 195, 27], [85, 30, 195, 28], [85, 34, 195, 32, "nextInteractionCount"], [85, 54, 195, 52], [85, 59, 195, 57], [85, 60, 195, 58], [85, 62, 195, 60], [86, 6, 197, 4, "_emitter"], [86, 14, 197, 12], [86, 15, 197, 13, "emit"], [86, 19, 197, 17], [86, 20, 197, 18, "InteractionManager"], [86, 38, 197, 36], [86, 39, 197, 37, "Events"], [86, 45, 197, 43], [86, 46, 197, 44, "interactionComplete"], [86, 65, 197, 63], [86, 66, 197, 64], [87, 4, 198, 2], [87, 5, 198, 3], [87, 11, 198, 9], [87, 15, 198, 13, "interactionCount"], [87, 31, 198, 29], [87, 36, 198, 34], [87, 37, 198, 35], [87, 41, 198, 39, "nextInteractionCount"], [87, 61, 198, 59], [87, 66, 198, 64], [87, 67, 198, 65], [87, 69, 198, 67], [88, 6, 200, 4, "_emitter"], [88, 14, 200, 12], [88, 15, 200, 13, "emit"], [88, 19, 200, 17], [88, 20, 200, 18, "InteractionManager"], [88, 38, 200, 36], [88, 39, 200, 37, "Events"], [88, 45, 200, 43], [88, 46, 200, 44, "interactionStart"], [88, 62, 200, 60], [88, 63, 200, 61], [89, 4, 201, 2], [90, 4, 204, 2], [90, 8, 204, 6, "nextInteractionCount"], [90, 28, 204, 26], [90, 33, 204, 31], [90, 34, 204, 32], [90, 36, 204, 34], [91, 6, 205, 4], [91, 13, 205, 11, "_taskQueue"], [91, 23, 205, 21], [91, 24, 205, 22, "hasTasksToProcess"], [91, 41, 205, 39], [91, 42, 205, 40], [91, 43, 205, 41], [91, 45, 205, 43], [92, 8, 206, 6, "_taskQueue"], [92, 18, 206, 16], [92, 19, 206, 17, "processNext"], [92, 30, 206, 28], [92, 31, 206, 29], [92, 32, 206, 30], [93, 8, 207, 6], [93, 12, 208, 8, "_deadline"], [93, 21, 208, 17], [93, 24, 208, 20], [93, 25, 208, 21], [93, 29, 209, 8, "BatchedBridge"], [93, 42, 209, 21], [93, 43, 209, 22, "getEventLoopRunningTime"], [93, 66, 209, 45], [93, 67, 209, 46], [93, 68, 209, 47], [93, 72, 209, 51, "_deadline"], [93, 81, 209, 60], [93, 83, 210, 8], [94, 10, 212, 8, "_scheduleUpdate"], [94, 25, 212, 23], [94, 26, 212, 24], [94, 27, 212, 25], [95, 10, 213, 8], [96, 8, 214, 6], [97, 6, 215, 4], [98, 4, 216, 2], [99, 4, 217, 2, "_addInteractionSet"], [99, 22, 217, 20], [99, 23, 217, 21, "clear"], [99, 28, 217, 26], [99, 29, 217, 27], [99, 30, 217, 28], [100, 4, 218, 2, "_deleteInteractionSet"], [100, 25, 218, 23], [100, 26, 218, 24, "clear"], [100, 31, 218, 29], [100, 32, 218, 30], [100, 33, 218, 31], [101, 2, 219, 0], [102, 2, 221, 0], [102, 6, 221, 6, "InteractionManager"], [102, 24, 221, 24], [102, 27, 222, 2, "ReactNativeFeatureFlags"], [102, 50, 222, 25], [102, 51, 222, 26, "disableInteractionManager"], [102, 76, 222, 51], [102, 77, 222, 52], [102, 78, 222, 53], [102, 81, 223, 6, "require"], [102, 88, 223, 13], [102, 89, 223, 13, "_dependencyMap"], [102, 103, 223, 13], [102, 134, 223, 40], [102, 135, 223, 41], [102, 136, 223, 42, "default"], [102, 143, 223, 49], [102, 146, 224, 6, "InteractionManagerImpl"], [102, 168, 225, 34], [103, 2, 225, 35], [103, 6, 225, 35, "_default"], [103, 14, 225, 35], [103, 17, 225, 35, "exports"], [103, 24, 225, 35], [103, 25, 225, 35, "default"], [103, 32, 225, 35], [103, 35, 227, 15, "InteractionManager"], [103, 53, 227, 33], [104, 0, 227, 33], [104, 3]], "functionMap": {"names": ["<global>", "InteractionManagerImpl.runAfterInteractions", "Promise$argument_0", "cancel", "InteractionManagerImpl.createInteractionHandle", "InteractionManagerImpl.clearInteractionHandle", "InteractionManagerImpl.setDeadline", "_scheduleUpdate", "_processUpdate", "_addInteractionSet.forEach$argument_0", "_deleteInteractionSet.forEach$argument_0"], "mappings": "AAA;EC4F;gCCS;KDU;cEI;OFE;GDE;EIK;GJM;EKK;GLM;EMgB;GNE;AOc;CPQ;AQK;6BCI,qCD;gCEC,wCF;CR2B"}}, "type": "js/module"}]}