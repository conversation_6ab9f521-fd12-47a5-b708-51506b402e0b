{"dependencies": [{"name": "react-native/Libraries/ReactPrivate/ReactNativePrivateInterface", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 110}}], "key": "ghLJ0yC3uDApQa4xAeq8CTgvgps=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ReactNativePrivateInterface = require(_dependencyMap[0], \"react-native/Libraries/ReactPrivate/ReactNativePrivateInterface\");\n  var register = _ReactNativePrivateInterface.ReactNativeViewConfigRegistry.register;\n  var createReactNativeComponentClass = function (name, callback) {\n    return register(name, callback);\n  };\n  var _default = exports.default = createReactNativeComponentClass;\n});", "lineCount": 14, "map": [[2, 2, 13, 0], [2, 14, 13, 12], [4, 2, 13, 13, "Object"], [4, 8, 13, 13], [4, 9, 13, 13, "defineProperty"], [4, 23, 13, 13], [4, 24, 13, 13, "exports"], [4, 31, 13, 13], [5, 4, 13, 13, "value"], [5, 9, 13, 13], [6, 2, 13, 13], [7, 2, 13, 13, "exports"], [7, 9, 13, 13], [7, 10, 13, 13, "default"], [7, 17, 13, 13], [8, 2, 15, 0], [8, 6, 15, 0, "_ReactNativePrivateInterface"], [8, 34, 15, 0], [8, 37, 15, 0, "require"], [8, 44, 15, 0], [8, 45, 15, 0, "_dependencyMap"], [8, 59, 15, 0], [9, 2, 18, 0], [9, 6, 18, 7, "register"], [9, 14, 18, 15], [9, 17, 18, 19, "ReactNativeViewConfigRegistry"], [9, 75, 18, 48], [9, 76, 18, 7, "register"], [9, 84, 18, 15], [10, 2, 28, 0], [10, 6, 28, 6, "createReactNativeComponentClass"], [10, 37, 28, 37], [10, 40, 28, 40], [10, 49, 28, 40, "createReactNativeComponentClass"], [10, 50, 29, 2, "name"], [10, 54, 29, 14], [10, 56, 30, 2, "callback"], [10, 64, 30, 28], [10, 66, 31, 10], [11, 4, 32, 2], [11, 11, 32, 9, "register"], [11, 19, 32, 17], [11, 20, 32, 18, "name"], [11, 24, 32, 22], [11, 26, 32, 24, "callback"], [11, 34, 32, 32], [11, 35, 32, 33], [12, 2, 33, 0], [12, 3, 33, 1], [13, 2, 33, 2], [13, 6, 33, 2, "_default"], [13, 14, 33, 2], [13, 17, 33, 2, "exports"], [13, 24, 33, 2], [13, 25, 33, 2, "default"], [13, 32, 33, 2], [13, 35, 35, 15, "createReactNativeComponentClass"], [13, 66, 35, 46], [14, 0, 35, 46], [14, 3]], "functionMap": {"names": ["<global>", "createReactNativeComponentClass"], "mappings": "AAA;wCC2B;CDK"}}, "type": "js/module"}]}