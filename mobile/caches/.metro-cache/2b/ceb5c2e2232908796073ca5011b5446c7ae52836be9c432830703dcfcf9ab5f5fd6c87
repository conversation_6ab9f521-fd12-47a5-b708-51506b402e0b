{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/routers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 58, "index": 73}}], "key": "TumjUqgKkj40CL5/as2VxzLfO54=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 74}, "end": {"line": 4, "column": 31, "index": 105}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 106}, "end": {"line": 5, "column": 73, "index": 179}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useNavigationCache = useNavigationCache;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _routers = require(_dependencyMap[2], \"@react-navigation/routers\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[4], \"./NavigationBuilderContext.js\");\n  var _excluded = [\"emit\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook to cache navigation objects for each screen in the navigator.\n   * It's important to cache them to make sure navigation objects don't change between renders.\n   * This lets us apply optimizations like `React.memo` to minimize re-rendering screens.\n   */\n  function useNavigationCache(_ref) {\n    var state = _ref.state,\n      getState = _ref.getState,\n      navigation = _ref.navigation,\n      setOptions = _ref.setOptions,\n      router = _ref.router,\n      emitter = _ref.emitter;\n    var _React$useContext = React.useContext(_NavigationBuilderContext.NavigationBuilderContext),\n      stackRef = _React$useContext.stackRef;\n    var base = React.useMemo(() => {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      var emit = navigation.emit,\n        rest = (0, _objectWithoutProperties2.default)(navigation, _excluded);\n      var actions = {\n        ...router.actionCreators,\n        ..._routers.CommonActions\n      };\n      var dispatch = () => {\n        throw new Error('Actions cannot be dispatched from a placeholder screen.');\n      };\n      var helpers = Object.keys(actions).reduce((acc, name) => {\n        acc[name] = dispatch;\n        return acc;\n      }, {});\n      return {\n        ...rest,\n        ...helpers,\n        addListener: () => {\n          // Event listeners are not supported for placeholder screens\n\n          return () => {\n            // Empty function\n          };\n        },\n        removeListener: () => {\n          // Event listeners are not supported for placeholder screens\n        },\n        dispatch,\n        getParent: id => {\n          if (id !== undefined && id === rest.getId()) {\n            return base;\n          }\n          return rest.getParent(id);\n        },\n        setOptions: () => {\n          throw new Error('Options cannot be set from a placeholder screen.');\n        },\n        isFocused: () => false\n      };\n    }, [navigation, router.actionCreators]);\n\n    // Cache object which holds navigation objects for each screen\n    // We use `React.useMemo` instead of `React.useRef` coz we want to invalidate it when deps change\n    // In reality, these deps will rarely change, if ever\n    var cache = React.useMemo(() => ({\n      current: {}\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [base, getState, navigation, setOptions, emitter]);\n    cache.current = state.routes.reduce((acc, route) => {\n      var previous = cache.current[route.key];\n      if (previous) {\n        // If a cached navigation object already exists, reuse it\n        acc[route.key] = previous;\n      } else {\n        var dispatch = thunk => {\n          var action = typeof thunk === 'function' ? thunk(getState()) : thunk;\n          if (action != null) {\n            navigation.dispatch({\n              source: route.key,\n              ...action\n            });\n          }\n        };\n        var withStack = callback => {\n          var isStackSet = false;\n          try {\n            if (process.env.NODE_ENV !== 'production' && stackRef && !stackRef.current) {\n              // Capture the stack trace for devtools\n              stackRef.current = new Error().stack;\n              isStackSet = true;\n            }\n            callback();\n          } finally {\n            if (isStackSet && stackRef) {\n              stackRef.current = undefined;\n            }\n          }\n        };\n        var actions = {\n          ...router.actionCreators,\n          ..._routers.CommonActions\n        };\n        var helpers = Object.keys(actions).reduce((acc, name) => {\n          acc[name] = function () {\n            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n              args[_key] = arguments[_key];\n            }\n            return withStack(() =>\n            // @ts-expect-error: name is a valid key, but TypeScript is dumb\n            dispatch(actions[name](...args)));\n          };\n          return acc;\n        }, {});\n        acc[route.key] = {\n          ...base,\n          ...helpers,\n          // FIXME: too much work to fix the types for now\n          ...emitter.create(route.key),\n          dispatch: thunk => withStack(() => dispatch(thunk)),\n          getParent: id => {\n            if (id !== undefined && id === base.getId()) {\n              // If the passed id is the same as the current navigation id,\n              // we return the cached navigation object for the relevant route\n              return acc[route.key];\n            }\n            return base.getParent(id);\n          },\n          setOptions: options => {\n            setOptions(o => ({\n              ...o,\n              [route.key]: {\n                ...o[route.key],\n                ...options\n              }\n            }));\n          },\n          isFocused: () => {\n            var state = base.getState();\n            if (state.routes[state.index].key !== route.key) {\n              return false;\n            }\n\n            // If the current screen is focused, we also need to check if parent navigator is focused\n            // This makes sure that we return the focus state in the whole tree, not just this navigator\n            return navigation ? navigation.isFocused() : true;\n          }\n        };\n      }\n      return acc;\n    }, {});\n    return {\n      base,\n      navigations: cache.current\n    };\n  }\n});", "lineCount": 166, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useNavigationCache"], [8, 28, 1, 13], [8, 31, 1, 13, "useNavigationCache"], [8, 49, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_routers"], [10, 14, 3, 0], [10, 17, 3, 0, "require"], [10, 24, 3, 0], [10, 25, 3, 0, "_dependencyMap"], [10, 39, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "React"], [11, 11, 4, 0], [11, 14, 4, 0, "_interopRequireWildcard"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_NavigationBuilderContext"], [12, 31, 5, 0], [12, 34, 5, 0, "require"], [12, 41, 5, 0], [12, 42, 5, 0, "_dependencyMap"], [12, 56, 5, 0], [13, 2, 5, 73], [13, 6, 5, 73, "_excluded"], [13, 15, 5, 73], [14, 2, 5, 73], [14, 11, 5, 73, "_interopRequireWildcard"], [14, 35, 5, 73, "e"], [14, 36, 5, 73], [14, 38, 5, 73, "t"], [14, 39, 5, 73], [14, 68, 5, 73, "WeakMap"], [14, 75, 5, 73], [14, 81, 5, 73, "r"], [14, 82, 5, 73], [14, 89, 5, 73, "WeakMap"], [14, 96, 5, 73], [14, 100, 5, 73, "n"], [14, 101, 5, 73], [14, 108, 5, 73, "WeakMap"], [14, 115, 5, 73], [14, 127, 5, 73, "_interopRequireWildcard"], [14, 150, 5, 73], [14, 162, 5, 73, "_interopRequireWildcard"], [14, 163, 5, 73, "e"], [14, 164, 5, 73], [14, 166, 5, 73, "t"], [14, 167, 5, 73], [14, 176, 5, 73, "t"], [14, 177, 5, 73], [14, 181, 5, 73, "e"], [14, 182, 5, 73], [14, 186, 5, 73, "e"], [14, 187, 5, 73], [14, 188, 5, 73, "__esModule"], [14, 198, 5, 73], [14, 207, 5, 73, "e"], [14, 208, 5, 73], [14, 214, 5, 73, "o"], [14, 215, 5, 73], [14, 217, 5, 73, "i"], [14, 218, 5, 73], [14, 220, 5, 73, "f"], [14, 221, 5, 73], [14, 226, 5, 73, "__proto__"], [14, 235, 5, 73], [14, 243, 5, 73, "default"], [14, 250, 5, 73], [14, 252, 5, 73, "e"], [14, 253, 5, 73], [14, 270, 5, 73, "e"], [14, 271, 5, 73], [14, 294, 5, 73, "e"], [14, 295, 5, 73], [14, 320, 5, 73, "e"], [14, 321, 5, 73], [14, 330, 5, 73, "f"], [14, 331, 5, 73], [14, 337, 5, 73, "o"], [14, 338, 5, 73], [14, 341, 5, 73, "t"], [14, 342, 5, 73], [14, 345, 5, 73, "n"], [14, 346, 5, 73], [14, 349, 5, 73, "r"], [14, 350, 5, 73], [14, 358, 5, 73, "o"], [14, 359, 5, 73], [14, 360, 5, 73, "has"], [14, 363, 5, 73], [14, 364, 5, 73, "e"], [14, 365, 5, 73], [14, 375, 5, 73, "o"], [14, 376, 5, 73], [14, 377, 5, 73, "get"], [14, 380, 5, 73], [14, 381, 5, 73, "e"], [14, 382, 5, 73], [14, 385, 5, 73, "o"], [14, 386, 5, 73], [14, 387, 5, 73, "set"], [14, 390, 5, 73], [14, 391, 5, 73, "e"], [14, 392, 5, 73], [14, 394, 5, 73, "f"], [14, 395, 5, 73], [14, 409, 5, 73, "_t"], [14, 411, 5, 73], [14, 415, 5, 73, "e"], [14, 416, 5, 73], [14, 432, 5, 73, "_t"], [14, 434, 5, 73], [14, 441, 5, 73, "hasOwnProperty"], [14, 455, 5, 73], [14, 456, 5, 73, "call"], [14, 460, 5, 73], [14, 461, 5, 73, "e"], [14, 462, 5, 73], [14, 464, 5, 73, "_t"], [14, 466, 5, 73], [14, 473, 5, 73, "i"], [14, 474, 5, 73], [14, 478, 5, 73, "o"], [14, 479, 5, 73], [14, 482, 5, 73, "Object"], [14, 488, 5, 73], [14, 489, 5, 73, "defineProperty"], [14, 503, 5, 73], [14, 508, 5, 73, "Object"], [14, 514, 5, 73], [14, 515, 5, 73, "getOwnPropertyDescriptor"], [14, 539, 5, 73], [14, 540, 5, 73, "e"], [14, 541, 5, 73], [14, 543, 5, 73, "_t"], [14, 545, 5, 73], [14, 552, 5, 73, "i"], [14, 553, 5, 73], [14, 554, 5, 73, "get"], [14, 557, 5, 73], [14, 561, 5, 73, "i"], [14, 562, 5, 73], [14, 563, 5, 73, "set"], [14, 566, 5, 73], [14, 570, 5, 73, "o"], [14, 571, 5, 73], [14, 572, 5, 73, "f"], [14, 573, 5, 73], [14, 575, 5, 73, "_t"], [14, 577, 5, 73], [14, 579, 5, 73, "i"], [14, 580, 5, 73], [14, 584, 5, 73, "f"], [14, 585, 5, 73], [14, 586, 5, 73, "_t"], [14, 588, 5, 73], [14, 592, 5, 73, "e"], [14, 593, 5, 73], [14, 594, 5, 73, "_t"], [14, 596, 5, 73], [14, 607, 5, 73, "f"], [14, 608, 5, 73], [14, 613, 5, 73, "e"], [14, 614, 5, 73], [14, 616, 5, 73, "t"], [14, 617, 5, 73], [15, 2, 6, 0], [16, 0, 7, 0], [17, 0, 8, 0], [18, 0, 9, 0], [19, 0, 10, 0], [20, 2, 11, 7], [20, 11, 11, 16, "useNavigationCache"], [20, 29, 11, 34, "useNavigationCache"], [20, 30, 11, 34, "_ref"], [20, 34, 11, 34], [20, 36, 18, 3], [21, 4, 18, 3], [21, 8, 12, 2, "state"], [21, 13, 12, 7], [21, 16, 12, 7, "_ref"], [21, 20, 12, 7], [21, 21, 12, 2, "state"], [21, 26, 12, 7], [22, 6, 13, 2, "getState"], [22, 14, 13, 10], [22, 17, 13, 10, "_ref"], [22, 21, 13, 10], [22, 22, 13, 2, "getState"], [22, 30, 13, 10], [23, 6, 14, 2, "navigation"], [23, 16, 14, 12], [23, 19, 14, 12, "_ref"], [23, 23, 14, 12], [23, 24, 14, 2, "navigation"], [23, 34, 14, 12], [24, 6, 15, 2, "setOptions"], [24, 16, 15, 12], [24, 19, 15, 12, "_ref"], [24, 23, 15, 12], [24, 24, 15, 2, "setOptions"], [24, 34, 15, 12], [25, 6, 16, 2, "router"], [25, 12, 16, 8], [25, 15, 16, 8, "_ref"], [25, 19, 16, 8], [25, 20, 16, 2, "router"], [25, 26, 16, 8], [26, 6, 17, 2, "emitter"], [26, 13, 17, 9], [26, 16, 17, 9, "_ref"], [26, 20, 17, 9], [26, 21, 17, 2, "emitter"], [26, 28, 17, 9], [27, 4, 19, 2], [27, 8, 19, 2, "_React$useContext"], [27, 25, 19, 2], [27, 28, 21, 6, "React"], [27, 33, 21, 11], [27, 34, 21, 12, "useContext"], [27, 44, 21, 22], [27, 45, 21, 23, "NavigationBuilderContext"], [27, 95, 21, 47], [27, 96, 21, 48], [28, 6, 20, 4, "stackRef"], [28, 14, 20, 12], [28, 17, 20, 12, "_React$useContext"], [28, 34, 20, 12], [28, 35, 20, 4, "stackRef"], [28, 43, 20, 12], [29, 4, 22, 2], [29, 8, 22, 8, "base"], [29, 12, 22, 12], [29, 15, 22, 15, "React"], [29, 20, 22, 20], [29, 21, 22, 21, "useMemo"], [29, 28, 22, 28], [29, 29, 22, 29], [29, 35, 22, 35], [30, 6, 23, 4], [31, 6, 24, 4], [31, 10, 25, 6, "emit"], [31, 14, 25, 10], [31, 17, 27, 8, "navigation"], [31, 27, 27, 18], [31, 28, 25, 6, "emit"], [31, 32, 25, 10], [32, 8, 26, 9, "rest"], [32, 12, 26, 13], [32, 19, 26, 13, "_objectWithoutProperties2"], [32, 44, 26, 13], [32, 45, 26, 13, "default"], [32, 52, 26, 13], [32, 54, 27, 8, "navigation"], [32, 64, 27, 18], [32, 66, 27, 18, "_excluded"], [32, 75, 27, 18], [33, 6, 28, 4], [33, 10, 28, 10, "actions"], [33, 17, 28, 17], [33, 20, 28, 20], [34, 8, 29, 6], [34, 11, 29, 9, "router"], [34, 17, 29, 15], [34, 18, 29, 16, "actionCreators"], [34, 32, 29, 30], [35, 8, 30, 6], [35, 11, 30, 9, "CommonActions"], [36, 6, 31, 4], [36, 7, 31, 5], [37, 6, 32, 4], [37, 10, 32, 10, "dispatch"], [37, 18, 32, 18], [37, 21, 32, 21, "dispatch"], [37, 22, 32, 21], [37, 27, 32, 27], [38, 8, 33, 6], [38, 14, 33, 12], [38, 18, 33, 16, "Error"], [38, 23, 33, 21], [38, 24, 33, 22], [38, 81, 33, 79], [38, 82, 33, 80], [39, 6, 34, 4], [39, 7, 34, 5], [40, 6, 35, 4], [40, 10, 35, 10, "helpers"], [40, 17, 35, 17], [40, 20, 35, 20, "Object"], [40, 26, 35, 26], [40, 27, 35, 27, "keys"], [40, 31, 35, 31], [40, 32, 35, 32, "actions"], [40, 39, 35, 39], [40, 40, 35, 40], [40, 41, 35, 41, "reduce"], [40, 47, 35, 47], [40, 48, 35, 48], [40, 49, 35, 49, "acc"], [40, 52, 35, 52], [40, 54, 35, 54, "name"], [40, 58, 35, 58], [40, 63, 35, 63], [41, 8, 36, 6, "acc"], [41, 11, 36, 9], [41, 12, 36, 10, "name"], [41, 16, 36, 14], [41, 17, 36, 15], [41, 20, 36, 18, "dispatch"], [41, 28, 36, 26], [42, 8, 37, 6], [42, 15, 37, 13, "acc"], [42, 18, 37, 16], [43, 6, 38, 4], [43, 7, 38, 5], [43, 9, 38, 7], [43, 10, 38, 8], [43, 11, 38, 9], [43, 12, 38, 10], [44, 6, 39, 4], [44, 13, 39, 11], [45, 8, 40, 6], [45, 11, 40, 9, "rest"], [45, 15, 40, 13], [46, 8, 41, 6], [46, 11, 41, 9, "helpers"], [46, 18, 41, 16], [47, 8, 42, 6, "addListener"], [47, 19, 42, 17], [47, 21, 42, 19, "addListener"], [47, 22, 42, 19], [47, 27, 42, 25], [48, 10, 43, 8], [50, 10, 45, 8], [50, 17, 45, 15], [50, 23, 45, 21], [51, 12, 46, 10], [52, 10, 46, 10], [52, 11, 47, 9], [53, 8, 48, 6], [53, 9, 48, 7], [54, 8, 49, 6, "removeListener"], [54, 22, 49, 20], [54, 24, 49, 22, "removeListener"], [54, 25, 49, 22], [54, 30, 49, 28], [55, 10, 50, 8], [56, 8, 50, 8], [56, 9, 51, 7], [57, 8, 52, 6, "dispatch"], [57, 16, 52, 14], [58, 8, 53, 6, "getParent"], [58, 17, 53, 15], [58, 19, 53, 17, "id"], [58, 21, 53, 19], [58, 25, 53, 23], [59, 10, 54, 8], [59, 14, 54, 12, "id"], [59, 16, 54, 14], [59, 21, 54, 19, "undefined"], [59, 30, 54, 28], [59, 34, 54, 32, "id"], [59, 36, 54, 34], [59, 41, 54, 39, "rest"], [59, 45, 54, 43], [59, 46, 54, 44, "getId"], [59, 51, 54, 49], [59, 52, 54, 50], [59, 53, 54, 51], [59, 55, 54, 53], [60, 12, 55, 10], [60, 19, 55, 17, "base"], [60, 23, 55, 21], [61, 10, 56, 8], [62, 10, 57, 8], [62, 17, 57, 15, "rest"], [62, 21, 57, 19], [62, 22, 57, 20, "getParent"], [62, 31, 57, 29], [62, 32, 57, 30, "id"], [62, 34, 57, 32], [62, 35, 57, 33], [63, 8, 58, 6], [63, 9, 58, 7], [64, 8, 59, 6, "setOptions"], [64, 18, 59, 16], [64, 20, 59, 18, "setOptions"], [64, 21, 59, 18], [64, 26, 59, 24], [65, 10, 60, 8], [65, 16, 60, 14], [65, 20, 60, 18, "Error"], [65, 25, 60, 23], [65, 26, 60, 24], [65, 76, 60, 74], [65, 77, 60, 75], [66, 8, 61, 6], [66, 9, 61, 7], [67, 8, 62, 6, "isFocused"], [67, 17, 62, 15], [67, 19, 62, 17, "isFocused"], [67, 20, 62, 17], [67, 25, 62, 23], [68, 6, 63, 4], [68, 7, 63, 5], [69, 4, 64, 2], [69, 5, 64, 3], [69, 7, 64, 5], [69, 8, 64, 6, "navigation"], [69, 18, 64, 16], [69, 20, 64, 18, "router"], [69, 26, 64, 24], [69, 27, 64, 25, "actionCreators"], [69, 41, 64, 39], [69, 42, 64, 40], [69, 43, 64, 41], [71, 4, 66, 2], [72, 4, 67, 2], [73, 4, 68, 2], [74, 4, 69, 2], [74, 8, 69, 8, "cache"], [74, 13, 69, 13], [74, 16, 69, 16, "React"], [74, 21, 69, 21], [74, 22, 69, 22, "useMemo"], [74, 29, 69, 29], [74, 30, 69, 30], [74, 37, 69, 37], [75, 6, 70, 4, "current"], [75, 13, 70, 11], [75, 15, 70, 13], [75, 16, 70, 14], [76, 4, 71, 2], [76, 5, 71, 3], [76, 6, 71, 4], [77, 4, 72, 2], [78, 4, 73, 2], [78, 5, 73, 3, "base"], [78, 9, 73, 7], [78, 11, 73, 9, "getState"], [78, 19, 73, 17], [78, 21, 73, 19, "navigation"], [78, 31, 73, 29], [78, 33, 73, 31, "setOptions"], [78, 43, 73, 41], [78, 45, 73, 43, "emitter"], [78, 52, 73, 50], [78, 53, 73, 51], [78, 54, 73, 52], [79, 4, 74, 2, "cache"], [79, 9, 74, 7], [79, 10, 74, 8, "current"], [79, 17, 74, 15], [79, 20, 74, 18, "state"], [79, 25, 74, 23], [79, 26, 74, 24, "routes"], [79, 32, 74, 30], [79, 33, 74, 31, "reduce"], [79, 39, 74, 37], [79, 40, 74, 38], [79, 41, 74, 39, "acc"], [79, 44, 74, 42], [79, 46, 74, 44, "route"], [79, 51, 74, 49], [79, 56, 74, 54], [80, 6, 75, 4], [80, 10, 75, 10, "previous"], [80, 18, 75, 18], [80, 21, 75, 21, "cache"], [80, 26, 75, 26], [80, 27, 75, 27, "current"], [80, 34, 75, 34], [80, 35, 75, 35, "route"], [80, 40, 75, 40], [80, 41, 75, 41, "key"], [80, 44, 75, 44], [80, 45, 75, 45], [81, 6, 76, 4], [81, 10, 76, 8, "previous"], [81, 18, 76, 16], [81, 20, 76, 18], [82, 8, 77, 6], [83, 8, 78, 6, "acc"], [83, 11, 78, 9], [83, 12, 78, 10, "route"], [83, 17, 78, 15], [83, 18, 78, 16, "key"], [83, 21, 78, 19], [83, 22, 78, 20], [83, 25, 78, 23, "previous"], [83, 33, 78, 31], [84, 6, 79, 4], [84, 7, 79, 5], [84, 13, 79, 11], [85, 8, 80, 6], [85, 12, 80, 12, "dispatch"], [85, 20, 80, 20], [85, 23, 80, 23, "thunk"], [85, 28, 80, 28], [85, 32, 80, 32], [86, 10, 81, 8], [86, 14, 81, 14, "action"], [86, 20, 81, 20], [86, 23, 81, 23], [86, 30, 81, 30, "thunk"], [86, 35, 81, 35], [86, 40, 81, 40], [86, 50, 81, 50], [86, 53, 81, 53, "thunk"], [86, 58, 81, 58], [86, 59, 81, 59, "getState"], [86, 67, 81, 67], [86, 68, 81, 68], [86, 69, 81, 69], [86, 70, 81, 70], [86, 73, 81, 73, "thunk"], [86, 78, 81, 78], [87, 10, 82, 8], [87, 14, 82, 12, "action"], [87, 20, 82, 18], [87, 24, 82, 22], [87, 28, 82, 26], [87, 30, 82, 28], [88, 12, 83, 10, "navigation"], [88, 22, 83, 20], [88, 23, 83, 21, "dispatch"], [88, 31, 83, 29], [88, 32, 83, 30], [89, 14, 84, 12, "source"], [89, 20, 84, 18], [89, 22, 84, 20, "route"], [89, 27, 84, 25], [89, 28, 84, 26, "key"], [89, 31, 84, 29], [90, 14, 85, 12], [90, 17, 85, 15, "action"], [91, 12, 86, 10], [91, 13, 86, 11], [91, 14, 86, 12], [92, 10, 87, 8], [93, 8, 88, 6], [93, 9, 88, 7], [94, 8, 89, 6], [94, 12, 89, 12, "withStack"], [94, 21, 89, 21], [94, 24, 89, 24, "callback"], [94, 32, 89, 32], [94, 36, 89, 36], [95, 10, 90, 8], [95, 14, 90, 12, "isStackSet"], [95, 24, 90, 22], [95, 27, 90, 25], [95, 32, 90, 30], [96, 10, 91, 8], [96, 14, 91, 12], [97, 12, 92, 10], [97, 16, 92, 14, "process"], [97, 23, 92, 21], [97, 24, 92, 22, "env"], [97, 27, 92, 25], [97, 28, 92, 26, "NODE_ENV"], [97, 36, 92, 34], [97, 41, 92, 39], [97, 53, 92, 51], [97, 57, 92, 55, "stackRef"], [97, 65, 92, 63], [97, 69, 92, 67], [97, 70, 92, 68, "stackRef"], [97, 78, 92, 76], [97, 79, 92, 77, "current"], [97, 86, 92, 84], [97, 88, 92, 86], [98, 14, 93, 12], [99, 14, 94, 12, "stackRef"], [99, 22, 94, 20], [99, 23, 94, 21, "current"], [99, 30, 94, 28], [99, 33, 94, 31], [99, 37, 94, 35, "Error"], [99, 42, 94, 40], [99, 43, 94, 41], [99, 44, 94, 42], [99, 45, 94, 43, "stack"], [99, 50, 94, 48], [100, 14, 95, 12, "isStackSet"], [100, 24, 95, 22], [100, 27, 95, 25], [100, 31, 95, 29], [101, 12, 96, 10], [102, 12, 97, 10, "callback"], [102, 20, 97, 18], [102, 21, 97, 19], [102, 22, 97, 20], [103, 10, 98, 8], [103, 11, 98, 9], [103, 20, 98, 18], [104, 12, 99, 10], [104, 16, 99, 14, "isStackSet"], [104, 26, 99, 24], [104, 30, 99, 28, "stackRef"], [104, 38, 99, 36], [104, 40, 99, 38], [105, 14, 100, 12, "stackRef"], [105, 22, 100, 20], [105, 23, 100, 21, "current"], [105, 30, 100, 28], [105, 33, 100, 31, "undefined"], [105, 42, 100, 40], [106, 12, 101, 10], [107, 10, 102, 8], [108, 8, 103, 6], [108, 9, 103, 7], [109, 8, 104, 6], [109, 12, 104, 12, "actions"], [109, 19, 104, 19], [109, 22, 104, 22], [110, 10, 105, 8], [110, 13, 105, 11, "router"], [110, 19, 105, 17], [110, 20, 105, 18, "actionCreators"], [110, 34, 105, 32], [111, 10, 106, 8], [111, 13, 106, 11, "CommonActions"], [112, 8, 107, 6], [112, 9, 107, 7], [113, 8, 108, 6], [113, 12, 108, 12, "helpers"], [113, 19, 108, 19], [113, 22, 108, 22, "Object"], [113, 28, 108, 28], [113, 29, 108, 29, "keys"], [113, 33, 108, 33], [113, 34, 108, 34, "actions"], [113, 41, 108, 41], [113, 42, 108, 42], [113, 43, 108, 43, "reduce"], [113, 49, 108, 49], [113, 50, 108, 50], [113, 51, 108, 51, "acc"], [113, 54, 108, 54], [113, 56, 108, 56, "name"], [113, 60, 108, 60], [113, 65, 108, 65], [114, 10, 109, 8, "acc"], [114, 13, 109, 11], [114, 14, 109, 12, "name"], [114, 18, 109, 16], [114, 19, 109, 17], [114, 22, 109, 20], [115, 12, 109, 20], [115, 21, 109, 20, "_len"], [115, 25, 109, 20], [115, 28, 109, 20, "arguments"], [115, 37, 109, 20], [115, 38, 109, 20, "length"], [115, 44, 109, 20], [115, 46, 109, 24, "args"], [115, 50, 109, 28], [115, 57, 109, 28, "Array"], [115, 62, 109, 28], [115, 63, 109, 28, "_len"], [115, 67, 109, 28], [115, 70, 109, 28, "_key"], [115, 74, 109, 28], [115, 80, 109, 28, "_key"], [115, 84, 109, 28], [115, 87, 109, 28, "_len"], [115, 91, 109, 28], [115, 93, 109, 28, "_key"], [115, 97, 109, 28], [116, 14, 109, 24, "args"], [116, 18, 109, 28], [116, 19, 109, 28, "_key"], [116, 23, 109, 28], [116, 27, 109, 28, "arguments"], [116, 36, 109, 28], [116, 37, 109, 28, "_key"], [116, 41, 109, 28], [117, 12, 109, 28], [118, 12, 109, 28], [118, 19, 109, 33, "withStack"], [118, 28, 109, 42], [118, 29, 109, 43], [119, 12, 110, 8], [120, 12, 111, 8, "dispatch"], [120, 20, 111, 16], [120, 21, 111, 17, "actions"], [120, 28, 111, 24], [120, 29, 111, 25, "name"], [120, 33, 111, 29], [120, 34, 111, 30], [120, 35, 111, 31], [120, 38, 111, 34, "args"], [120, 42, 111, 38], [120, 43, 111, 39], [120, 44, 111, 40], [120, 45, 111, 41], [121, 10, 111, 41], [122, 10, 112, 8], [122, 17, 112, 15, "acc"], [122, 20, 112, 18], [123, 8, 113, 6], [123, 9, 113, 7], [123, 11, 113, 9], [123, 12, 113, 10], [123, 13, 113, 11], [123, 14, 113, 12], [124, 8, 114, 6, "acc"], [124, 11, 114, 9], [124, 12, 114, 10, "route"], [124, 17, 114, 15], [124, 18, 114, 16, "key"], [124, 21, 114, 19], [124, 22, 114, 20], [124, 25, 114, 23], [125, 10, 115, 8], [125, 13, 115, 11, "base"], [125, 17, 115, 15], [126, 10, 116, 8], [126, 13, 116, 11, "helpers"], [126, 20, 116, 18], [127, 10, 117, 8], [128, 10, 118, 8], [128, 13, 118, 11, "emitter"], [128, 20, 118, 18], [128, 21, 118, 19, "create"], [128, 27, 118, 25], [128, 28, 118, 26, "route"], [128, 33, 118, 31], [128, 34, 118, 32, "key"], [128, 37, 118, 35], [128, 38, 118, 36], [129, 10, 119, 8, "dispatch"], [129, 18, 119, 16], [129, 20, 119, 18, "thunk"], [129, 25, 119, 23], [129, 29, 119, 27, "withStack"], [129, 38, 119, 36], [129, 39, 119, 37], [129, 45, 119, 43, "dispatch"], [129, 53, 119, 51], [129, 54, 119, 52, "thunk"], [129, 59, 119, 57], [129, 60, 119, 58], [129, 61, 119, 59], [130, 10, 120, 8, "getParent"], [130, 19, 120, 17], [130, 21, 120, 19, "id"], [130, 23, 120, 21], [130, 27, 120, 25], [131, 12, 121, 10], [131, 16, 121, 14, "id"], [131, 18, 121, 16], [131, 23, 121, 21, "undefined"], [131, 32, 121, 30], [131, 36, 121, 34, "id"], [131, 38, 121, 36], [131, 43, 121, 41, "base"], [131, 47, 121, 45], [131, 48, 121, 46, "getId"], [131, 53, 121, 51], [131, 54, 121, 52], [131, 55, 121, 53], [131, 57, 121, 55], [132, 14, 122, 12], [133, 14, 123, 12], [134, 14, 124, 12], [134, 21, 124, 19, "acc"], [134, 24, 124, 22], [134, 25, 124, 23, "route"], [134, 30, 124, 28], [134, 31, 124, 29, "key"], [134, 34, 124, 32], [134, 35, 124, 33], [135, 12, 125, 10], [136, 12, 126, 10], [136, 19, 126, 17, "base"], [136, 23, 126, 21], [136, 24, 126, 22, "getParent"], [136, 33, 126, 31], [136, 34, 126, 32, "id"], [136, 36, 126, 34], [136, 37, 126, 35], [137, 10, 127, 8], [137, 11, 127, 9], [138, 10, 128, 8, "setOptions"], [138, 20, 128, 18], [138, 22, 128, 20, "options"], [138, 29, 128, 27], [138, 33, 128, 31], [139, 12, 129, 10, "setOptions"], [139, 22, 129, 20], [139, 23, 129, 21, "o"], [139, 24, 129, 22], [139, 29, 129, 27], [140, 14, 130, 12], [140, 17, 130, 15, "o"], [140, 18, 130, 16], [141, 14, 131, 12], [141, 15, 131, 13, "route"], [141, 20, 131, 18], [141, 21, 131, 19, "key"], [141, 24, 131, 22], [141, 27, 131, 25], [142, 16, 132, 14], [142, 19, 132, 17, "o"], [142, 20, 132, 18], [142, 21, 132, 19, "route"], [142, 26, 132, 24], [142, 27, 132, 25, "key"], [142, 30, 132, 28], [142, 31, 132, 29], [143, 16, 133, 14], [143, 19, 133, 17, "options"], [144, 14, 134, 12], [145, 12, 135, 10], [145, 13, 135, 11], [145, 14, 135, 12], [145, 15, 135, 13], [146, 10, 136, 8], [146, 11, 136, 9], [147, 10, 137, 8, "isFocused"], [147, 19, 137, 17], [147, 21, 137, 19, "isFocused"], [147, 22, 137, 19], [147, 27, 137, 25], [148, 12, 138, 10], [148, 16, 138, 16, "state"], [148, 21, 138, 21], [148, 24, 138, 24, "base"], [148, 28, 138, 28], [148, 29, 138, 29, "getState"], [148, 37, 138, 37], [148, 38, 138, 38], [148, 39, 138, 39], [149, 12, 139, 10], [149, 16, 139, 14, "state"], [149, 21, 139, 19], [149, 22, 139, 20, "routes"], [149, 28, 139, 26], [149, 29, 139, 27, "state"], [149, 34, 139, 32], [149, 35, 139, 33, "index"], [149, 40, 139, 38], [149, 41, 139, 39], [149, 42, 139, 40, "key"], [149, 45, 139, 43], [149, 50, 139, 48, "route"], [149, 55, 139, 53], [149, 56, 139, 54, "key"], [149, 59, 139, 57], [149, 61, 139, 59], [150, 14, 140, 12], [150, 21, 140, 19], [150, 26, 140, 24], [151, 12, 141, 10], [153, 12, 143, 10], [154, 12, 144, 10], [155, 12, 145, 10], [155, 19, 145, 17, "navigation"], [155, 29, 145, 27], [155, 32, 145, 30, "navigation"], [155, 42, 145, 40], [155, 43, 145, 41, "isFocused"], [155, 52, 145, 50], [155, 53, 145, 51], [155, 54, 145, 52], [155, 57, 145, 55], [155, 61, 145, 59], [156, 10, 146, 8], [157, 8, 147, 6], [157, 9, 147, 7], [158, 6, 148, 4], [159, 6, 149, 4], [159, 13, 149, 11, "acc"], [159, 16, 149, 14], [160, 4, 150, 2], [160, 5, 150, 3], [160, 7, 150, 5], [160, 8, 150, 6], [160, 9, 150, 7], [160, 10, 150, 8], [161, 4, 151, 2], [161, 11, 151, 9], [162, 6, 152, 4, "base"], [162, 10, 152, 8], [163, 6, 153, 4, "navigations"], [163, 17, 153, 15], [163, 19, 153, 17, "cache"], [163, 24, 153, 22], [163, 25, 153, 23, "current"], [164, 4, 154, 2], [164, 5, 154, 3], [165, 2, 155, 0], [166, 0, 155, 1], [166, 3]], "functionMap": {"names": ["<global>", "useNavigationCache", "React.useMemo$argument_0", "dispatch", "Object.keys.reduce$argument_0", "addListener", "<anonymous>", "removeListener", "getParent", "setOptions", "isFocused", "state.routes.reduce$argument_0", "withStack", "acc.name", "withStack$argument_0", "acc.route.key.dispatch", "acc.route.key.getParent", "acc.route.key.setOptions", "setOptions$argument_0", "acc.route.key.isFocused"], "mappings": "AAA;OCU;6BCW;qBCU;KDE;gDEC;KFG;mBGI;eCG;SDE;OHC;sBKC;OLE;iBME;ONK;kBOC;OPE;iBQC,WR;GDE;8BCK;IDE;sCUG;uBRM;OQQ;wBCC;ODc;kDPK;oBSC,uBC;wCDE,CT;OOE;kBIM,mBD,qBC,CJ;mBKC;SLO;oBMC;qBCC;YDM;SNC;mBQC;SRS;GVI;CDK"}}, "type": "js/module"}]}