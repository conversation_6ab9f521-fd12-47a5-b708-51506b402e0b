{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 16, "index": 423}, "end": {"line": 11, "column": 32, "index": 439}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 23, "index": 464}, "end": {"line": 12, "column": 46, "index": 487}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "./href", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 15, "index": 504}, "end": {"line": 13, "column": 32, "index": 521}}], "key": "diDc5FkIhsiwR6PP+lddt/DVx50=", "exportNames": ["*"]}}, {"name": "./useLinkToPathProps", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 45, "index": 568}, "end": {"line": 14, "column": 76, "index": 599}}], "key": "q9bfXhW9ulBNkDWUmJU3mtaUQwM=", "exportNames": ["*"]}}, {"name": "../hooks", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 16, "index": 618}, "end": {"line": 15, "column": 35, "index": 637}}], "key": "v2Yuhjr6LxgVylgzuY6yqo/x8nY=", "exportNames": ["*"]}}, {"name": "../useFocusEffect", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 25, "index": 664}, "end": {"line": 16, "column": 53, "index": 692}}], "key": "Xl2T8spXebYhHf2E93kxkRRgEms=", "exportNames": ["*"]}}, {"name": "./useLinkHooks", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 23, "index": 717}, "end": {"line": 17, "column": 48, "index": 742}}], "key": "GiCaESewjcnmecsnO8l3M+MaIME=", "exportNames": ["*"]}}, {"name": "../Prefetch", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 19, "index": 763}, "end": {"line": 18, "column": 41, "index": 785}}], "key": "wsXl5cCco1mMKiwoLK6VVWKcTak=", "exportNames": ["*"]}}, {"name": "../ui/Slot", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 15, "index": 802}, "end": {"line": 19, "column": 36, "index": 823}}], "key": "UzQIkkkxiT6jmbAtPauTkCiHlGg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _objectWithoutProperties = require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\");\n  var _excluded = [\"href\", \"replace\", \"push\", \"dismissTo\", \"relativeToDirectory\", \"asChild\", \"rel\", \"target\", \"download\", \"withAnchor\", \"dangerouslySingular\", \"prefetch\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/link/Link.js\";\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Link = void 0;\n  exports.Redirect = Redirect;\n  // Fork of @react-navigation/native Link.tsx with `href` and `replace` support added and\n  // `to` / `action` support removed.\n  var react_1 = require(_dependencyMap[2], \"react\");\n  var react_native_1 = require(_dependencyMap[3], \"react-native\");\n  var href_1 = require(_dependencyMap[4], \"./href\");\n  var useLinkToPathProps_1 = __importDefault(require(_dependencyMap[5], \"./useLinkToPathProps\"));\n  var hooks_1 = require(_dependencyMap[6], \"../hooks\");\n  var useFocusEffect_1 = require(_dependencyMap[7], \"../useFocusEffect\");\n  var useLinkHooks_1 = require(_dependencyMap[8], \"./useLinkHooks\");\n  var Prefetch_1 = require(_dependencyMap[9], \"../Prefetch\");\n  var Slot_1 = require(_dependencyMap[10], \"../ui/Slot\");\n  /**\n   * Redirects to the `href` as soon as the component is mounted.\n   *\n   * @example\n   * ```tsx\n   * import { View, Text } from 'react-native';\n   * import { Redirect } from 'expo-router';\n   *\n   * export default function Page() {\n   *  const { user } = useAuth();\n   *\n   *  if (!user) {\n   *    return <Redirect href=\"/login\" />;\n   *  }\n   *\n   *  return (\n   *    <View>\n   *      <Text>Welcome Back!</Text>\n   *    </View>\n   *  );\n   * }\n   * ```\n   */\n  function Redirect(_ref) {\n    var href = _ref.href,\n      relativeToDirectory = _ref.relativeToDirectory,\n      withAnchor = _ref.withAnchor;\n    var router = (0, hooks_1.useRouter)();\n    (0, useFocusEffect_1.useFocusEffect)(() => {\n      try {\n        router.replace(href, {\n          relativeToDirectory,\n          withAnchor\n        });\n      } catch (error) {\n        console.error(error);\n      }\n    });\n    return null;\n  }\n  /**\n   * Component that renders a link using [`href`](#href) to another route.\n   * By default, it accepts children and wraps them in a `<Text>` component.\n   *\n   * Uses an anchor tag (`<a>`) on web and performs a client-side navigation to preserve\n   * the state of the website and navigate faster. The web-only attributes such as `target`,\n   * `rel`, and `download` are supported and passed to the anchor tag on web. See\n   * [`WebAnchorProps`](#webanchorprops) for more details.\n   *\n   * > **Note**: Client-side navigation works with both single-page apps,\n   * and [static-rendering](/router/reference/static-rendering/).\n   *\n   * @example\n   * ```tsx\n   * import { Link } from 'expo-router';\n   * import { View } from 'react-native';\n   *\n   * export default function Route() {\n   *  return (\n   *   <View>\n   *    <Link href=\"/about\">About</Link>\n   *   </View>\n   *  );\n   *}\n   * ```\n   */\n  exports.Link = (0, react_1.forwardRef)(ExpoRouterLink);\n  exports.Link.resolveHref = href_1.resolveHref;\n  function ExpoRouterLink(_ref2, ref) {\n    var href = _ref2.href,\n      replace = _ref2.replace,\n      push = _ref2.push,\n      dismissTo = _ref2.dismissTo,\n      relativeToDirectory = _ref2.relativeToDirectory,\n      asChild = _ref2.asChild,\n      rel = _ref2.rel,\n      target = _ref2.target,\n      download = _ref2.download,\n      withAnchor = _ref2.withAnchor,\n      singular = _ref2.dangerouslySingular,\n      prefetch = _ref2.prefetch,\n      rest = _objectWithoutProperties(_ref2, _excluded);\n    // Mutate the style prop to add the className on web.\n    var style = (0, useLinkHooks_1.useInteropClassName)(rest);\n    // If not passing asChild, we need to forward the props to the anchor tag using React Native Web's `hrefAttrs`.\n    var hrefAttrs = (0, useLinkHooks_1.useHrefAttrs)({\n      asChild,\n      rel,\n      target,\n      download\n    });\n    var resolvedHref = (0, react_1.useMemo)(() => {\n      if (href == null) {\n        throw new Error('Link: href is required');\n      }\n      return (0, href_1.resolveHref)(href);\n    }, [href]);\n    var event;\n    if (push) event = 'PUSH';\n    if (replace) event = 'REPLACE';\n    if (dismissTo) event = 'POP_TO';\n    var props = (0, useLinkToPathProps_1.default)({\n      href: resolvedHref,\n      event,\n      relativeToDirectory,\n      withAnchor,\n      dangerouslySingular: singular\n    });\n    var onPress = e => {\n      if ('onPress' in rest) {\n        rest.onPress?.(e);\n      }\n      props.onPress(e);\n    };\n    var Component = asChild ? Slot_1.Slot : react_native_1.Text;\n    // Avoid using createElement directly, favoring JSX, to allow tools like NativeWind to perform custom JSX handling on native.\n    var element = _reactNativeCssInteropJsxRuntime.jsx(Component, {\n      ref: ref,\n      ...props,\n      ...hrefAttrs,\n      ...rest,\n      style: style,\n      ...react_native_1.Platform.select({\n        web: {\n          onClick: onPress\n        },\n        default: {\n          onPress\n        }\n      })\n    });\n    return prefetch ? _reactNativeCssInteropJsxRuntime.jsxs(_reactNativeCssInteropJsxRuntime.Fragment, {\n      children: [_reactNativeCssInteropJsxRuntime.jsx(Prefetch_1.Prefetch, {\n        href: href\n      }), element]\n    }) : element;\n  }\n});", "lineCount": 167, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_reactNativeCssInteropJsxRuntime"], [5, 38, 2, 13], [5, 41, 2, 13, "require"], [5, 48, 2, 13], [5, 49, 2, 13, "_dependencyMap"], [5, 63, 2, 13], [6, 2, 2, 13], [6, 6, 2, 13, "_objectWithoutProperties"], [6, 30, 2, 13], [6, 33, 2, 13, "require"], [6, 40, 2, 13], [6, 41, 2, 13, "_dependencyMap"], [6, 55, 2, 13], [7, 2, 2, 13], [7, 6, 2, 13, "_excluded"], [7, 15, 2, 13], [8, 2, 2, 13], [8, 6, 2, 13, "_jsxFileName"], [8, 18, 2, 13], [9, 2, 3, 0], [9, 6, 3, 4, "__importDefault"], [9, 21, 3, 19], [9, 24, 3, 23], [9, 28, 3, 27], [9, 32, 3, 31], [9, 36, 3, 35], [9, 37, 3, 36, "__importDefault"], [9, 52, 3, 51], [9, 56, 3, 56], [9, 66, 3, 66, "mod"], [9, 69, 3, 69], [9, 71, 3, 71], [10, 4, 4, 4], [10, 11, 4, 12, "mod"], [10, 14, 4, 15], [10, 18, 4, 19, "mod"], [10, 21, 4, 22], [10, 22, 4, 23, "__esModule"], [10, 32, 4, 33], [10, 35, 4, 37, "mod"], [10, 38, 4, 40], [10, 41, 4, 43], [11, 6, 4, 45], [11, 15, 4, 54], [11, 17, 4, 56, "mod"], [12, 4, 4, 60], [12, 5, 4, 61], [13, 2, 5, 0], [13, 3, 5, 1], [14, 2, 6, 0, "Object"], [14, 8, 6, 6], [14, 9, 6, 7, "defineProperty"], [14, 23, 6, 21], [14, 24, 6, 22, "exports"], [14, 31, 6, 29], [14, 33, 6, 31], [14, 45, 6, 43], [14, 47, 6, 45], [15, 4, 6, 47, "value"], [15, 9, 6, 52], [15, 11, 6, 54], [16, 2, 6, 59], [16, 3, 6, 60], [16, 4, 6, 61], [17, 2, 7, 0, "exports"], [17, 9, 7, 7], [17, 10, 7, 8, "Link"], [17, 14, 7, 12], [17, 17, 7, 15], [17, 22, 7, 20], [17, 23, 7, 21], [18, 2, 8, 0, "exports"], [18, 9, 8, 7], [18, 10, 8, 8, "Redirect"], [18, 18, 8, 16], [18, 21, 8, 19, "Redirect"], [18, 29, 8, 27], [19, 2, 9, 0], [20, 2, 10, 0], [21, 2, 11, 0], [21, 6, 11, 6, "react_1"], [21, 13, 11, 13], [21, 16, 11, 16, "require"], [21, 23, 11, 23], [21, 24, 11, 23, "_dependencyMap"], [21, 38, 11, 23], [21, 50, 11, 31], [21, 51, 11, 32], [22, 2, 12, 0], [22, 6, 12, 6, "react_native_1"], [22, 20, 12, 20], [22, 23, 12, 23, "require"], [22, 30, 12, 30], [22, 31, 12, 30, "_dependencyMap"], [22, 45, 12, 30], [22, 64, 12, 45], [22, 65, 12, 46], [23, 2, 13, 0], [23, 6, 13, 6, "href_1"], [23, 12, 13, 12], [23, 15, 13, 15, "require"], [23, 22, 13, 22], [23, 23, 13, 22, "_dependencyMap"], [23, 37, 13, 22], [23, 50, 13, 31], [23, 51, 13, 32], [24, 2, 14, 0], [24, 6, 14, 6, "useLinkToPathProps_1"], [24, 26, 14, 26], [24, 29, 14, 29, "__importDefault"], [24, 44, 14, 44], [24, 45, 14, 45, "require"], [24, 52, 14, 52], [24, 53, 14, 52, "_dependencyMap"], [24, 67, 14, 52], [24, 94, 14, 75], [24, 95, 14, 76], [24, 96, 14, 77], [25, 2, 15, 0], [25, 6, 15, 6, "hooks_1"], [25, 13, 15, 13], [25, 16, 15, 16, "require"], [25, 23, 15, 23], [25, 24, 15, 23, "_dependencyMap"], [25, 38, 15, 23], [25, 53, 15, 34], [25, 54, 15, 35], [26, 2, 16, 0], [26, 6, 16, 6, "useFocusEffect_1"], [26, 22, 16, 22], [26, 25, 16, 25, "require"], [26, 32, 16, 32], [26, 33, 16, 32, "_dependencyMap"], [26, 47, 16, 32], [26, 71, 16, 52], [26, 72, 16, 53], [27, 2, 17, 0], [27, 6, 17, 6, "useLinkHooks_1"], [27, 20, 17, 20], [27, 23, 17, 23, "require"], [27, 30, 17, 30], [27, 31, 17, 30, "_dependencyMap"], [27, 45, 17, 30], [27, 66, 17, 47], [27, 67, 17, 48], [28, 2, 18, 0], [28, 6, 18, 6, "Prefetch_1"], [28, 16, 18, 16], [28, 19, 18, 19, "require"], [28, 26, 18, 26], [28, 27, 18, 26, "_dependencyMap"], [28, 41, 18, 26], [28, 59, 18, 40], [28, 60, 18, 41], [29, 2, 19, 0], [29, 6, 19, 6, "Slot_1"], [29, 12, 19, 12], [29, 15, 19, 15, "require"], [29, 22, 19, 22], [29, 23, 19, 22, "_dependencyMap"], [29, 37, 19, 22], [29, 55, 19, 35], [29, 56, 19, 36], [30, 2, 20, 0], [31, 0, 21, 0], [32, 0, 22, 0], [33, 0, 23, 0], [34, 0, 24, 0], [35, 0, 25, 0], [36, 0, 26, 0], [37, 0, 27, 0], [38, 0, 28, 0], [39, 0, 29, 0], [40, 0, 30, 0], [41, 0, 31, 0], [42, 0, 32, 0], [43, 0, 33, 0], [44, 0, 34, 0], [45, 0, 35, 0], [46, 0, 36, 0], [47, 0, 37, 0], [48, 0, 38, 0], [49, 0, 39, 0], [50, 0, 40, 0], [51, 0, 41, 0], [52, 0, 42, 0], [53, 2, 43, 0], [53, 11, 43, 9, "Redirect"], [53, 19, 43, 17, "Redirect"], [53, 20, 43, 17, "_ref"], [53, 24, 43, 17], [53, 26, 43, 61], [54, 4, 43, 61], [54, 8, 43, 20, "href"], [54, 12, 43, 24], [54, 15, 43, 24, "_ref"], [54, 19, 43, 24], [54, 20, 43, 20, "href"], [54, 24, 43, 24], [55, 6, 43, 26, "relativeToDirectory"], [55, 25, 43, 45], [55, 28, 43, 45, "_ref"], [55, 32, 43, 45], [55, 33, 43, 26, "relativeToDirectory"], [55, 52, 43, 45], [56, 6, 43, 47, "with<PERSON>nchor"], [56, 16, 43, 57], [56, 19, 43, 57, "_ref"], [56, 23, 43, 57], [56, 24, 43, 47, "with<PERSON>nchor"], [56, 34, 43, 57], [57, 4, 44, 4], [57, 8, 44, 10, "router"], [57, 14, 44, 16], [57, 17, 44, 19], [57, 18, 44, 20], [57, 19, 44, 21], [57, 21, 44, 23, "hooks_1"], [57, 28, 44, 30], [57, 29, 44, 31, "useRouter"], [57, 38, 44, 40], [57, 40, 44, 42], [57, 41, 44, 43], [58, 4, 45, 4], [58, 5, 45, 5], [58, 6, 45, 6], [58, 8, 45, 8, "useFocusEffect_1"], [58, 24, 45, 24], [58, 25, 45, 25, "useFocusEffect"], [58, 39, 45, 39], [58, 41, 45, 41], [58, 47, 45, 47], [59, 6, 46, 8], [59, 10, 46, 12], [60, 8, 47, 12, "router"], [60, 14, 47, 18], [60, 15, 47, 19, "replace"], [60, 22, 47, 26], [60, 23, 47, 27, "href"], [60, 27, 47, 31], [60, 29, 47, 33], [61, 10, 47, 35, "relativeToDirectory"], [61, 29, 47, 54], [62, 10, 47, 56, "with<PERSON>nchor"], [63, 8, 47, 67], [63, 9, 47, 68], [63, 10, 47, 69], [64, 6, 48, 8], [64, 7, 48, 9], [64, 8, 49, 8], [64, 15, 49, 15, "error"], [64, 20, 49, 20], [64, 22, 49, 22], [65, 8, 50, 12, "console"], [65, 15, 50, 19], [65, 16, 50, 20, "error"], [65, 21, 50, 25], [65, 22, 50, 26, "error"], [65, 27, 50, 31], [65, 28, 50, 32], [66, 6, 51, 8], [67, 4, 52, 4], [67, 5, 52, 5], [67, 6, 52, 6], [68, 4, 53, 4], [68, 11, 53, 11], [68, 15, 53, 15], [69, 2, 54, 0], [70, 2, 55, 0], [71, 0, 56, 0], [72, 0, 57, 0], [73, 0, 58, 0], [74, 0, 59, 0], [75, 0, 60, 0], [76, 0, 61, 0], [77, 0, 62, 0], [78, 0, 63, 0], [79, 0, 64, 0], [80, 0, 65, 0], [81, 0, 66, 0], [82, 0, 67, 0], [83, 0, 68, 0], [84, 0, 69, 0], [85, 0, 70, 0], [86, 0, 71, 0], [87, 0, 72, 0], [88, 0, 73, 0], [89, 0, 74, 0], [90, 0, 75, 0], [91, 0, 76, 0], [92, 0, 77, 0], [93, 0, 78, 0], [94, 0, 79, 0], [95, 0, 80, 0], [96, 2, 81, 0, "exports"], [96, 9, 81, 7], [96, 10, 81, 8, "Link"], [96, 14, 81, 12], [96, 17, 81, 15], [96, 18, 81, 16], [96, 19, 81, 17], [96, 21, 81, 19, "react_1"], [96, 28, 81, 26], [96, 29, 81, 27, "forwardRef"], [96, 39, 81, 37], [96, 41, 81, 39, "ExpoRouterLink"], [96, 55, 81, 53], [96, 56, 81, 54], [97, 2, 82, 0, "exports"], [97, 9, 82, 7], [97, 10, 82, 8, "Link"], [97, 14, 82, 12], [97, 15, 82, 13, "resolveHref"], [97, 26, 82, 24], [97, 29, 82, 27, "href_1"], [97, 35, 82, 33], [97, 36, 82, 34, "resolveHref"], [97, 47, 82, 45], [98, 2, 83, 0], [98, 11, 83, 9, "ExpoRouterLink"], [98, 25, 83, 23, "ExpoRouterLink"], [98, 26, 83, 23, "_ref2"], [98, 31, 83, 23], [98, 33, 85, 117, "ref"], [98, 36, 85, 120], [98, 38, 85, 122], [99, 4, 85, 122], [99, 8, 83, 26, "href"], [99, 12, 83, 30], [99, 15, 83, 30, "_ref2"], [99, 20, 83, 30], [99, 21, 83, 26, "href"], [99, 25, 83, 30], [100, 6, 83, 32, "replace"], [100, 13, 83, 39], [100, 16, 83, 39, "_ref2"], [100, 21, 83, 39], [100, 22, 83, 32, "replace"], [100, 29, 83, 39], [101, 6, 83, 41, "push"], [101, 10, 83, 45], [101, 13, 83, 45, "_ref2"], [101, 18, 83, 45], [101, 19, 83, 41, "push"], [101, 23, 83, 45], [102, 6, 83, 47, "dismissTo"], [102, 15, 83, 56], [102, 18, 83, 56, "_ref2"], [102, 23, 83, 56], [102, 24, 83, 47, "dismissTo"], [102, 33, 83, 56], [103, 6, 85, 0, "relativeToDirectory"], [103, 25, 85, 19], [103, 28, 85, 19, "_ref2"], [103, 33, 85, 19], [103, 34, 85, 0, "relativeToDirectory"], [103, 53, 85, 19], [104, 6, 85, 21, "<PERSON><PERSON><PERSON><PERSON>"], [104, 13, 85, 28], [104, 16, 85, 28, "_ref2"], [104, 21, 85, 28], [104, 22, 85, 21, "<PERSON><PERSON><PERSON><PERSON>"], [104, 29, 85, 28], [105, 6, 85, 30, "rel"], [105, 9, 85, 33], [105, 12, 85, 33, "_ref2"], [105, 17, 85, 33], [105, 18, 85, 30, "rel"], [105, 21, 85, 33], [106, 6, 85, 35, "target"], [106, 12, 85, 41], [106, 15, 85, 41, "_ref2"], [106, 20, 85, 41], [106, 21, 85, 35, "target"], [106, 27, 85, 41], [107, 6, 85, 43, "download"], [107, 14, 85, 51], [107, 17, 85, 51, "_ref2"], [107, 22, 85, 51], [107, 23, 85, 43, "download"], [107, 31, 85, 51], [108, 6, 85, 53, "with<PERSON>nchor"], [108, 16, 85, 63], [108, 19, 85, 63, "_ref2"], [108, 24, 85, 63], [108, 25, 85, 53, "with<PERSON>nchor"], [108, 35, 85, 63], [109, 6, 85, 86, "singular"], [109, 14, 85, 94], [109, 17, 85, 94, "_ref2"], [109, 22, 85, 94], [109, 23, 85, 65, "dangerouslySingular"], [109, 42, 85, 84], [110, 6, 85, 96, "prefetch"], [110, 14, 85, 104], [110, 17, 85, 104, "_ref2"], [110, 22, 85, 104], [110, 23, 85, 96, "prefetch"], [110, 31, 85, 104], [111, 6, 85, 109, "rest"], [111, 10, 85, 113], [111, 13, 85, 113, "_objectWithoutProperties"], [111, 37, 85, 113], [111, 38, 85, 113, "_ref2"], [111, 43, 85, 113], [111, 45, 85, 113, "_excluded"], [111, 54, 85, 113], [112, 4, 86, 4], [113, 4, 87, 4], [113, 8, 87, 10, "style"], [113, 13, 87, 15], [113, 16, 87, 18], [113, 17, 87, 19], [113, 18, 87, 20], [113, 20, 87, 22, "useLinkHooks_1"], [113, 34, 87, 36], [113, 35, 87, 37, "useInteropClassName"], [113, 54, 87, 56], [113, 56, 87, 58, "rest"], [113, 60, 87, 62], [113, 61, 87, 63], [114, 4, 88, 4], [115, 4, 89, 4], [115, 8, 89, 10, "hrefAttrs"], [115, 17, 89, 19], [115, 20, 89, 22], [115, 21, 89, 23], [115, 22, 89, 24], [115, 24, 89, 26, "useLinkHooks_1"], [115, 38, 89, 40], [115, 39, 89, 41, "useHrefAttrs"], [115, 51, 89, 53], [115, 53, 89, 55], [116, 6, 89, 57, "<PERSON><PERSON><PERSON><PERSON>"], [116, 13, 89, 64], [117, 6, 89, 66, "rel"], [117, 9, 89, 69], [118, 6, 89, 71, "target"], [118, 12, 89, 77], [119, 6, 89, 79, "download"], [120, 4, 89, 88], [120, 5, 89, 89], [120, 6, 89, 90], [121, 4, 90, 4], [121, 8, 90, 10, "resolvedHref"], [121, 20, 90, 22], [121, 23, 90, 25], [121, 24, 90, 26], [121, 25, 90, 27], [121, 27, 90, 29, "react_1"], [121, 34, 90, 36], [121, 35, 90, 37, "useMemo"], [121, 42, 90, 44], [121, 44, 90, 46], [121, 50, 90, 52], [122, 6, 91, 8], [122, 10, 91, 12, "href"], [122, 14, 91, 16], [122, 18, 91, 20], [122, 22, 91, 24], [122, 24, 91, 26], [123, 8, 92, 12], [123, 14, 92, 18], [123, 18, 92, 22, "Error"], [123, 23, 92, 27], [123, 24, 92, 28], [123, 48, 92, 52], [123, 49, 92, 53], [124, 6, 93, 8], [125, 6, 94, 8], [125, 13, 94, 15], [125, 14, 94, 16], [125, 15, 94, 17], [125, 17, 94, 19, "href_1"], [125, 23, 94, 25], [125, 24, 94, 26, "resolveHref"], [125, 35, 94, 37], [125, 37, 94, 39, "href"], [125, 41, 94, 43], [125, 42, 94, 44], [126, 4, 95, 4], [126, 5, 95, 5], [126, 7, 95, 7], [126, 8, 95, 8, "href"], [126, 12, 95, 12], [126, 13, 95, 13], [126, 14, 95, 14], [127, 4, 96, 4], [127, 8, 96, 8, "event"], [127, 13, 96, 13], [128, 4, 97, 4], [128, 8, 97, 8, "push"], [128, 12, 97, 12], [128, 14, 98, 8, "event"], [128, 19, 98, 13], [128, 22, 98, 16], [128, 28, 98, 22], [129, 4, 99, 4], [129, 8, 99, 8, "replace"], [129, 15, 99, 15], [129, 17, 100, 8, "event"], [129, 22, 100, 13], [129, 25, 100, 16], [129, 34, 100, 25], [130, 4, 101, 4], [130, 8, 101, 8, "dismissTo"], [130, 17, 101, 17], [130, 19, 102, 8, "event"], [130, 24, 102, 13], [130, 27, 102, 16], [130, 35, 102, 24], [131, 4, 103, 4], [131, 8, 103, 10, "props"], [131, 13, 103, 15], [131, 16, 103, 18], [131, 17, 103, 19], [131, 18, 103, 20], [131, 20, 103, 22, "useLinkToPathProps_1"], [131, 40, 103, 42], [131, 41, 103, 43, "default"], [131, 48, 103, 50], [131, 50, 103, 52], [132, 6, 104, 8, "href"], [132, 10, 104, 12], [132, 12, 104, 14, "resolvedHref"], [132, 24, 104, 26], [133, 6, 105, 8, "event"], [133, 11, 105, 13], [134, 6, 106, 8, "relativeToDirectory"], [134, 25, 106, 27], [135, 6, 107, 8, "with<PERSON>nchor"], [135, 16, 107, 18], [136, 6, 108, 8, "dangerouslySingular"], [136, 25, 108, 27], [136, 27, 108, 29, "singular"], [137, 4, 109, 4], [137, 5, 109, 5], [137, 6, 109, 6], [138, 4, 110, 4], [138, 8, 110, 10, "onPress"], [138, 15, 110, 17], [138, 18, 110, 21, "e"], [138, 19, 110, 22], [138, 23, 110, 27], [139, 6, 111, 8], [139, 10, 111, 12], [139, 19, 111, 21], [139, 23, 111, 25, "rest"], [139, 27, 111, 29], [139, 29, 111, 31], [140, 8, 112, 12, "rest"], [140, 12, 112, 16], [140, 13, 112, 17, "onPress"], [140, 20, 112, 24], [140, 23, 112, 27, "e"], [140, 24, 112, 28], [140, 25, 112, 29], [141, 6, 113, 8], [142, 6, 114, 8, "props"], [142, 11, 114, 13], [142, 12, 114, 14, "onPress"], [142, 19, 114, 21], [142, 20, 114, 22, "e"], [142, 21, 114, 23], [142, 22, 114, 24], [143, 4, 115, 4], [143, 5, 115, 5], [144, 4, 116, 4], [144, 8, 116, 10, "Component"], [144, 17, 116, 19], [144, 20, 116, 22, "<PERSON><PERSON><PERSON><PERSON>"], [144, 27, 116, 29], [144, 30, 116, 32, "Slot_1"], [144, 36, 116, 38], [144, 37, 116, 39, "Slot"], [144, 41, 116, 43], [144, 44, 116, 46, "react_native_1"], [144, 58, 116, 60], [144, 59, 116, 61, "Text"], [144, 63, 116, 65], [145, 4, 117, 4], [146, 4, 118, 4], [146, 8, 118, 10, "element"], [146, 15, 118, 17], [146, 18, 118, 21, "_reactNativeCssInteropJsxRuntime"], [146, 50, 118, 21], [146, 51, 118, 21, "jsx"], [146, 54, 118, 21], [146, 55, 118, 22, "Component"], [146, 64, 118, 31], [147, 6, 118, 32, "ref"], [147, 9, 118, 35], [147, 11, 118, 37, "ref"], [147, 14, 118, 41], [148, 6, 118, 41], [148, 9, 118, 46, "props"], [148, 14, 118, 51], [149, 6, 118, 51], [149, 9, 118, 57, "hrefAttrs"], [149, 18, 118, 66], [150, 6, 118, 66], [150, 9, 118, 72, "rest"], [150, 13, 118, 76], [151, 6, 118, 78, "style"], [151, 11, 118, 83], [151, 13, 118, 85, "style"], [151, 18, 118, 91], [152, 6, 118, 91], [152, 9, 118, 96, "react_native_1"], [152, 23, 118, 110], [152, 24, 118, 111, "Platform"], [152, 32, 118, 119], [152, 33, 118, 120, "select"], [152, 39, 118, 126], [152, 40, 118, 127], [153, 8, 119, 8, "web"], [153, 11, 119, 11], [153, 13, 119, 13], [154, 10, 120, 12, "onClick"], [154, 17, 120, 19], [154, 19, 120, 21, "onPress"], [155, 8, 121, 8], [155, 9, 121, 9], [156, 8, 122, 8, "default"], [156, 15, 122, 15], [156, 17, 122, 17], [157, 10, 122, 19, "onPress"], [158, 8, 122, 27], [159, 6, 123, 4], [159, 7, 123, 5], [160, 4, 123, 6], [160, 5, 123, 8], [160, 6, 123, 10], [161, 4, 124, 4], [161, 11, 124, 11, "prefetch"], [161, 19, 124, 19], [161, 22, 124, 23, "_reactNativeCssInteropJsxRuntime"], [161, 54, 124, 23], [161, 55, 124, 23, "jsxs"], [161, 59, 124, 23], [161, 60, 124, 23, "_reactNativeCssInteropJsxRuntime"], [161, 92, 124, 23], [161, 93, 124, 23, "Fragment"], [161, 101, 124, 23], [162, 6, 124, 23, "children"], [162, 14, 124, 23], [162, 17, 125, 6, "_reactNativeCssInteropJsxRuntime"], [162, 49, 125, 6], [162, 50, 125, 6, "jsx"], [162, 53, 125, 6], [162, 54, 125, 7, "Prefetch_1"], [162, 64, 125, 17], [162, 65, 125, 18, "Prefetch"], [162, 73, 125, 26], [163, 8, 125, 27, "href"], [163, 12, 125, 31], [163, 14, 125, 33, "href"], [164, 6, 125, 38], [164, 7, 125, 39], [164, 8, 125, 40], [164, 10, 126, 7, "element"], [164, 17, 126, 14], [165, 4, 126, 14], [165, 5, 127, 6], [165, 6, 127, 7], [165, 9, 127, 12, "element"], [165, 16, 127, 20], [166, 2, 128, 0], [167, 0, 128, 1], [167, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "Redirect", "ExpoRouterLink", "onPress"], "mappings": "AAA;wDCE;CDE;AEsC;yCDE;KCO;CFE;AG6B;8CFO;KEK;oBCe;KDK;CHa"}}, "type": "js/module"}]}