{"dependencies": [{"name": "@/utils/useAuth", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 42, "index": 42}}], "key": "HHge/F+kNWTBLu5mZhqDJclSs98=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 43}, "end": {"line": 2, "column": 36, "index": 79}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "expo-splash-screen", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 80}, "end": {"line": 3, "column": 51, "index": 131}}], "key": "a2FJfa13Ea2ozNvpuFf5VyHsS1c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 132}, "end": {"line": 4, "column": 34, "index": 166}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../__create/SharedErrorBoundary", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 167}, "end": {"line": 5, "column": 95, "index": 262}}], "key": "5iEwes3lWMT8wrYchOZ6hxHe4EA=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = RootLayout;\n  var _useAuth2 = require(_dependencyMap[0], \"@/utils/useAuth\");\n  var _expoRouter = require(_dependencyMap[1], \"expo-router\");\n  var SplashScreen = _interopRequireWildcard(require(_dependencyMap[2], \"expo-splash-screen\"));\n  var _react = require(_dependencyMap[3], \"react\");\n  var _SharedErrorBoundary = require(_dependencyMap[4], \"../../__create/SharedErrorBoundary\");\n  var _jsxRuntime = require(_dependencyMap[5], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/src/app/_layout.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  SplashScreen.preventAutoHideAsync();\n  function RootLayout() {\n    _s();\n    var _useAuth = (0, _useAuth2.useAuth)(),\n      initiate = _useAuth.initiate,\n      isReady = _useAuth.isReady;\n    (0, _react.useEffect)(() => {\n      initiate();\n    }, [initiate]);\n    (0, _react.useEffect)(() => {\n      if (isReady) {\n        SplashScreen.hideAsync();\n      }\n    }, [isReady]);\n    return (0, _jsxRuntime.jsx)(_SharedErrorBoundary.ErrorBoundaryWrapper, {\n      children: (0, _jsxRuntime.jsxs)(_expoRouter.Stack, {\n        screenOptions: {\n          headerShown: false\n        },\n        children: [(0, _jsxRuntime.jsx)(_expoRouter.Stack.Screen, {\n          name: \"index\"\n        }), (0, _jsxRuntime.jsx)(_expoRouter.Stack.Screen, {\n          name: \"auth\",\n          options: {\n            presentation: 'modal',\n            headerShown: false\n          }\n        })]\n      })\n    });\n  }\n  _s(RootLayout, \"PnvQr3TDvatEzGVB8PXu7spubTg=\", false, function () {\n    return [_useAuth2.useAuth];\n  });\n  _c = RootLayout;\n  var _c;\n  $RefreshReg$(_c, \"RootLayout\");\n});", "lineCount": 52, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_useAuth2"], [6, 15, 1, 0], [6, 18, 1, 0, "require"], [6, 25, 1, 0], [6, 26, 1, 0, "_dependencyMap"], [6, 40, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_expoRouter"], [7, 17, 2, 0], [7, 20, 2, 0, "require"], [7, 27, 2, 0], [7, 28, 2, 0, "_dependencyMap"], [7, 42, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "SplashScreen"], [8, 18, 3, 0], [8, 21, 3, 0, "_interopRequireWildcard"], [8, 44, 3, 0], [8, 45, 3, 0, "require"], [8, 52, 3, 0], [8, 53, 3, 0, "_dependencyMap"], [8, 67, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_react"], [9, 12, 4, 0], [9, 15, 4, 0, "require"], [9, 22, 4, 0], [9, 23, 4, 0, "_dependencyMap"], [9, 37, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_SharedErrorBoundary"], [10, 26, 5, 0], [10, 29, 5, 0, "require"], [10, 36, 5, 0], [10, 37, 5, 0, "_dependencyMap"], [10, 51, 5, 0], [11, 2, 5, 95], [11, 6, 5, 95, "_jsxRuntime"], [11, 17, 5, 95], [11, 20, 5, 95, "require"], [11, 27, 5, 95], [11, 28, 5, 95, "_dependencyMap"], [11, 42, 5, 95], [12, 2, 5, 95], [12, 6, 5, 95, "_jsxFileName"], [12, 18, 5, 95], [13, 4, 5, 95, "_s"], [13, 6, 5, 95], [13, 9, 5, 95, "$RefreshSig$"], [13, 21, 5, 95], [14, 2, 5, 95], [14, 11, 5, 95, "_interopRequireWildcard"], [14, 35, 5, 95, "e"], [14, 36, 5, 95], [14, 38, 5, 95, "t"], [14, 39, 5, 95], [14, 68, 5, 95, "WeakMap"], [14, 75, 5, 95], [14, 81, 5, 95, "r"], [14, 82, 5, 95], [14, 89, 5, 95, "WeakMap"], [14, 96, 5, 95], [14, 100, 5, 95, "n"], [14, 101, 5, 95], [14, 108, 5, 95, "WeakMap"], [14, 115, 5, 95], [14, 127, 5, 95, "_interopRequireWildcard"], [14, 150, 5, 95], [14, 162, 5, 95, "_interopRequireWildcard"], [14, 163, 5, 95, "e"], [14, 164, 5, 95], [14, 166, 5, 95, "t"], [14, 167, 5, 95], [14, 176, 5, 95, "t"], [14, 177, 5, 95], [14, 181, 5, 95, "e"], [14, 182, 5, 95], [14, 186, 5, 95, "e"], [14, 187, 5, 95], [14, 188, 5, 95, "__esModule"], [14, 198, 5, 95], [14, 207, 5, 95, "e"], [14, 208, 5, 95], [14, 214, 5, 95, "o"], [14, 215, 5, 95], [14, 217, 5, 95, "i"], [14, 218, 5, 95], [14, 220, 5, 95, "f"], [14, 221, 5, 95], [14, 226, 5, 95, "__proto__"], [14, 235, 5, 95], [14, 243, 5, 95, "default"], [14, 250, 5, 95], [14, 252, 5, 95, "e"], [14, 253, 5, 95], [14, 270, 5, 95, "e"], [14, 271, 5, 95], [14, 294, 5, 95, "e"], [14, 295, 5, 95], [14, 320, 5, 95, "e"], [14, 321, 5, 95], [14, 330, 5, 95, "f"], [14, 331, 5, 95], [14, 337, 5, 95, "o"], [14, 338, 5, 95], [14, 341, 5, 95, "t"], [14, 342, 5, 95], [14, 345, 5, 95, "n"], [14, 346, 5, 95], [14, 349, 5, 95, "r"], [14, 350, 5, 95], [14, 358, 5, 95, "o"], [14, 359, 5, 95], [14, 360, 5, 95, "has"], [14, 363, 5, 95], [14, 364, 5, 95, "e"], [14, 365, 5, 95], [14, 375, 5, 95, "o"], [14, 376, 5, 95], [14, 377, 5, 95, "get"], [14, 380, 5, 95], [14, 381, 5, 95, "e"], [14, 382, 5, 95], [14, 385, 5, 95, "o"], [14, 386, 5, 95], [14, 387, 5, 95, "set"], [14, 390, 5, 95], [14, 391, 5, 95, "e"], [14, 392, 5, 95], [14, 394, 5, 95, "f"], [14, 395, 5, 95], [14, 409, 5, 95, "_t"], [14, 411, 5, 95], [14, 415, 5, 95, "e"], [14, 416, 5, 95], [14, 432, 5, 95, "_t"], [14, 434, 5, 95], [14, 441, 5, 95, "hasOwnProperty"], [14, 455, 5, 95], [14, 456, 5, 95, "call"], [14, 460, 5, 95], [14, 461, 5, 95, "e"], [14, 462, 5, 95], [14, 464, 5, 95, "_t"], [14, 466, 5, 95], [14, 473, 5, 95, "i"], [14, 474, 5, 95], [14, 478, 5, 95, "o"], [14, 479, 5, 95], [14, 482, 5, 95, "Object"], [14, 488, 5, 95], [14, 489, 5, 95, "defineProperty"], [14, 503, 5, 95], [14, 508, 5, 95, "Object"], [14, 514, 5, 95], [14, 515, 5, 95, "getOwnPropertyDescriptor"], [14, 539, 5, 95], [14, 540, 5, 95, "e"], [14, 541, 5, 95], [14, 543, 5, 95, "_t"], [14, 545, 5, 95], [14, 552, 5, 95, "i"], [14, 553, 5, 95], [14, 554, 5, 95, "get"], [14, 557, 5, 95], [14, 561, 5, 95, "i"], [14, 562, 5, 95], [14, 563, 5, 95, "set"], [14, 566, 5, 95], [14, 570, 5, 95, "o"], [14, 571, 5, 95], [14, 572, 5, 95, "f"], [14, 573, 5, 95], [14, 575, 5, 95, "_t"], [14, 577, 5, 95], [14, 579, 5, 95, "i"], [14, 580, 5, 95], [14, 584, 5, 95, "f"], [14, 585, 5, 95], [14, 586, 5, 95, "_t"], [14, 588, 5, 95], [14, 592, 5, 95, "e"], [14, 593, 5, 95], [14, 594, 5, 95, "_t"], [14, 596, 5, 95], [14, 607, 5, 95, "f"], [14, 608, 5, 95], [14, 613, 5, 95, "e"], [14, 614, 5, 95], [14, 616, 5, 95, "t"], [14, 617, 5, 95], [15, 2, 7, 0, "SplashScreen"], [15, 14, 7, 12], [15, 15, 7, 13, "preventAutoHideAsync"], [15, 35, 7, 33], [15, 36, 7, 34], [15, 37, 7, 35], [16, 2, 9, 15], [16, 11, 9, 24, "RootLayout"], [16, 21, 9, 34, "RootLayout"], [16, 22, 9, 34], [16, 24, 9, 37], [17, 4, 9, 37, "_s"], [17, 6, 9, 37], [18, 4, 10, 2], [18, 8, 10, 2, "_useAuth"], [18, 16, 10, 2], [18, 19, 10, 32], [18, 23, 10, 32, "useAuth"], [18, 40, 10, 39], [18, 42, 10, 40], [18, 43, 10, 41], [19, 6, 10, 10, "initiate"], [19, 14, 10, 18], [19, 17, 10, 18, "_useAuth"], [19, 25, 10, 18], [19, 26, 10, 10, "initiate"], [19, 34, 10, 18], [20, 6, 10, 20, "isReady"], [20, 13, 10, 27], [20, 16, 10, 27, "_useAuth"], [20, 24, 10, 27], [20, 25, 10, 20, "isReady"], [20, 32, 10, 27], [21, 4, 12, 2], [21, 8, 12, 2, "useEffect"], [21, 24, 12, 11], [21, 26, 12, 12], [21, 32, 12, 18], [22, 6, 13, 4, "initiate"], [22, 14, 13, 12], [22, 15, 13, 13], [22, 16, 13, 14], [23, 4, 14, 2], [23, 5, 14, 3], [23, 7, 14, 5], [23, 8, 14, 6, "initiate"], [23, 16, 14, 14], [23, 17, 14, 15], [23, 18, 14, 16], [24, 4, 16, 2], [24, 8, 16, 2, "useEffect"], [24, 24, 16, 11], [24, 26, 16, 12], [24, 32, 16, 18], [25, 6, 17, 4], [25, 10, 17, 8, "isReady"], [25, 17, 17, 15], [25, 19, 17, 17], [26, 8, 18, 6, "SplashScreen"], [26, 20, 18, 18], [26, 21, 18, 19, "<PERSON><PERSON><PERSON>"], [26, 30, 18, 28], [26, 31, 18, 29], [26, 32, 18, 30], [27, 6, 19, 4], [28, 4, 20, 2], [28, 5, 20, 3], [28, 7, 20, 5], [28, 8, 20, 6, "isReady"], [28, 15, 20, 13], [28, 16, 20, 14], [28, 17, 20, 15], [29, 4, 22, 2], [29, 11, 23, 4], [29, 15, 23, 4, "_jsxRuntime"], [29, 26, 23, 4], [29, 27, 23, 4, "jsx"], [29, 30, 23, 4], [29, 32, 23, 5, "_SharedErrorBoundary"], [29, 52, 23, 5], [29, 53, 23, 5, "ErrorBoundaryWrapper"], [29, 73, 23, 25], [30, 6, 23, 25, "children"], [30, 14, 23, 25], [30, 16, 24, 6], [30, 20, 24, 6, "_jsxRuntime"], [30, 31, 24, 6], [30, 32, 24, 6, "jsxs"], [30, 36, 24, 6], [30, 38, 24, 7, "_expoRouter"], [30, 49, 24, 7], [30, 50, 24, 7, "<PERSON><PERSON>"], [30, 55, 24, 12], [31, 8, 24, 13, "screenOptions"], [31, 21, 24, 26], [31, 23, 24, 28], [32, 10, 24, 30, "headerShown"], [32, 21, 24, 41], [32, 23, 24, 43], [33, 8, 24, 49], [33, 9, 24, 51], [34, 8, 24, 51, "children"], [34, 16, 24, 51], [34, 19, 25, 8], [34, 23, 25, 8, "_jsxRuntime"], [34, 34, 25, 8], [34, 35, 25, 8, "jsx"], [34, 38, 25, 8], [34, 40, 25, 9, "_expoRouter"], [34, 51, 25, 9], [34, 52, 25, 9, "<PERSON><PERSON>"], [34, 57, 25, 14], [34, 58, 25, 15, "Screen"], [34, 64, 25, 21], [35, 10, 25, 22, "name"], [35, 14, 25, 26], [35, 16, 25, 27], [36, 8, 25, 34], [36, 9, 25, 36], [36, 10, 25, 37], [36, 12, 26, 8], [36, 16, 26, 8, "_jsxRuntime"], [36, 27, 26, 8], [36, 28, 26, 8, "jsx"], [36, 31, 26, 8], [36, 33, 26, 9, "_expoRouter"], [36, 44, 26, 9], [36, 45, 26, 9, "<PERSON><PERSON>"], [36, 50, 26, 14], [36, 51, 26, 15, "Screen"], [36, 57, 26, 21], [37, 10, 26, 22, "name"], [37, 14, 26, 26], [37, 16, 26, 27], [37, 22, 26, 33], [38, 10, 26, 34, "options"], [38, 17, 26, 41], [38, 19, 26, 43], [39, 12, 26, 45, "presentation"], [39, 24, 26, 57], [39, 26, 26, 59], [39, 33, 26, 66], [40, 12, 26, 68, "headerShown"], [40, 23, 26, 79], [40, 25, 26, 81], [41, 10, 26, 87], [42, 8, 26, 89], [42, 9, 26, 91], [42, 10, 26, 92], [43, 6, 26, 92], [43, 7, 27, 13], [44, 4, 27, 14], [44, 5, 28, 26], [44, 6, 28, 27], [45, 2, 30, 0], [46, 2, 30, 1, "_s"], [46, 4, 30, 1], [46, 5, 9, 24, "RootLayout"], [46, 15, 9, 34], [47, 4, 9, 34], [47, 12, 10, 32, "useAuth"], [47, 29, 10, 39], [48, 2, 10, 39], [49, 2, 10, 39, "_c"], [49, 4, 10, 39], [49, 7, 9, 24, "RootLayout"], [49, 17, 9, 34], [50, 2, 9, 34], [50, 6, 9, 34, "_c"], [50, 8, 9, 34], [51, 2, 9, 34, "$RefreshReg$"], [51, 14, 9, 34], [51, 15, 9, 34, "_c"], [51, 17, 9, 34], [52, 0, 9, 34], [52, 3]], "functionMap": {"names": ["<global>", "RootLayout", "useEffect$argument_0"], "mappings": "AAA;eCQ;YCG;GDE;YCE;GDI;CDU"}}, "type": "js/module"}]}