{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./TransitionProgressContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 33}, "end": {"line": 3, "column": 68, "index": 101}}], "key": "YgFkpQMOm7qkv4gM/LY2PvjMxwM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = useTransitionProgress;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _TransitionProgressContext = _interopRequireDefault(require(_dependencyMap[2], \"./TransitionProgressContext\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useTransitionProgress() {\n    var progress = React.useContext(_TransitionProgressContext.default);\n    if (progress === undefined) {\n      throw new Error(\"Couldn't find values for transition progress. Are you inside a screen in Native Stack?\");\n    }\n    return progress;\n  }\n});", "lineCount": 17, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "React"], [7, 11, 1, 0], [7, 14, 1, 0, "_interopRequireWildcard"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_TransitionProgressContext"], [8, 32, 3, 0], [8, 35, 3, 0, "_interopRequireDefault"], [8, 57, 3, 0], [8, 58, 3, 0, "require"], [8, 65, 3, 0], [8, 66, 3, 0, "_dependencyMap"], [8, 80, 3, 0], [9, 2, 3, 68], [9, 11, 3, 68, "_interopRequireWildcard"], [9, 35, 3, 68, "e"], [9, 36, 3, 68], [9, 38, 3, 68, "t"], [9, 39, 3, 68], [9, 68, 3, 68, "WeakMap"], [9, 75, 3, 68], [9, 81, 3, 68, "r"], [9, 82, 3, 68], [9, 89, 3, 68, "WeakMap"], [9, 96, 3, 68], [9, 100, 3, 68, "n"], [9, 101, 3, 68], [9, 108, 3, 68, "WeakMap"], [9, 115, 3, 68], [9, 127, 3, 68, "_interopRequireWildcard"], [9, 150, 3, 68], [9, 162, 3, 68, "_interopRequireWildcard"], [9, 163, 3, 68, "e"], [9, 164, 3, 68], [9, 166, 3, 68, "t"], [9, 167, 3, 68], [9, 176, 3, 68, "t"], [9, 177, 3, 68], [9, 181, 3, 68, "e"], [9, 182, 3, 68], [9, 186, 3, 68, "e"], [9, 187, 3, 68], [9, 188, 3, 68, "__esModule"], [9, 198, 3, 68], [9, 207, 3, 68, "e"], [9, 208, 3, 68], [9, 214, 3, 68, "o"], [9, 215, 3, 68], [9, 217, 3, 68, "i"], [9, 218, 3, 68], [9, 220, 3, 68, "f"], [9, 221, 3, 68], [9, 226, 3, 68, "__proto__"], [9, 235, 3, 68], [9, 243, 3, 68, "default"], [9, 250, 3, 68], [9, 252, 3, 68, "e"], [9, 253, 3, 68], [9, 270, 3, 68, "e"], [9, 271, 3, 68], [9, 294, 3, 68, "e"], [9, 295, 3, 68], [9, 320, 3, 68, "e"], [9, 321, 3, 68], [9, 330, 3, 68, "f"], [9, 331, 3, 68], [9, 337, 3, 68, "o"], [9, 338, 3, 68], [9, 341, 3, 68, "t"], [9, 342, 3, 68], [9, 345, 3, 68, "n"], [9, 346, 3, 68], [9, 349, 3, 68, "r"], [9, 350, 3, 68], [9, 358, 3, 68, "o"], [9, 359, 3, 68], [9, 360, 3, 68, "has"], [9, 363, 3, 68], [9, 364, 3, 68, "e"], [9, 365, 3, 68], [9, 375, 3, 68, "o"], [9, 376, 3, 68], [9, 377, 3, 68, "get"], [9, 380, 3, 68], [9, 381, 3, 68, "e"], [9, 382, 3, 68], [9, 385, 3, 68, "o"], [9, 386, 3, 68], [9, 387, 3, 68, "set"], [9, 390, 3, 68], [9, 391, 3, 68, "e"], [9, 392, 3, 68], [9, 394, 3, 68, "f"], [9, 395, 3, 68], [9, 409, 3, 68, "_t"], [9, 411, 3, 68], [9, 415, 3, 68, "e"], [9, 416, 3, 68], [9, 432, 3, 68, "_t"], [9, 434, 3, 68], [9, 441, 3, 68, "hasOwnProperty"], [9, 455, 3, 68], [9, 456, 3, 68, "call"], [9, 460, 3, 68], [9, 461, 3, 68, "e"], [9, 462, 3, 68], [9, 464, 3, 68, "_t"], [9, 466, 3, 68], [9, 473, 3, 68, "i"], [9, 474, 3, 68], [9, 478, 3, 68, "o"], [9, 479, 3, 68], [9, 482, 3, 68, "Object"], [9, 488, 3, 68], [9, 489, 3, 68, "defineProperty"], [9, 503, 3, 68], [9, 508, 3, 68, "Object"], [9, 514, 3, 68], [9, 515, 3, 68, "getOwnPropertyDescriptor"], [9, 539, 3, 68], [9, 540, 3, 68, "e"], [9, 541, 3, 68], [9, 543, 3, 68, "_t"], [9, 545, 3, 68], [9, 552, 3, 68, "i"], [9, 553, 3, 68], [9, 554, 3, 68, "get"], [9, 557, 3, 68], [9, 561, 3, 68, "i"], [9, 562, 3, 68], [9, 563, 3, 68, "set"], [9, 566, 3, 68], [9, 570, 3, 68, "o"], [9, 571, 3, 68], [9, 572, 3, 68, "f"], [9, 573, 3, 68], [9, 575, 3, 68, "_t"], [9, 577, 3, 68], [9, 579, 3, 68, "i"], [9, 580, 3, 68], [9, 584, 3, 68, "f"], [9, 585, 3, 68], [9, 586, 3, 68, "_t"], [9, 588, 3, 68], [9, 592, 3, 68, "e"], [9, 593, 3, 68], [9, 594, 3, 68, "_t"], [9, 596, 3, 68], [9, 607, 3, 68, "f"], [9, 608, 3, 68], [9, 613, 3, 68, "e"], [9, 614, 3, 68], [9, 616, 3, 68, "t"], [9, 617, 3, 68], [10, 2, 5, 15], [10, 11, 5, 24, "useTransitionProgress"], [10, 32, 5, 45, "useTransitionProgress"], [10, 33, 5, 45], [10, 35, 5, 48], [11, 4, 6, 2], [11, 8, 6, 8, "progress"], [11, 16, 6, 16], [11, 19, 6, 19, "React"], [11, 24, 6, 24], [11, 25, 6, 25, "useContext"], [11, 35, 6, 35], [11, 36, 6, 36, "TransitionProgressContext"], [11, 70, 6, 61], [11, 71, 6, 62], [12, 4, 8, 2], [12, 8, 8, 6, "progress"], [12, 16, 8, 14], [12, 21, 8, 19, "undefined"], [12, 30, 8, 28], [12, 32, 8, 30], [13, 6, 9, 4], [13, 12, 9, 10], [13, 16, 9, 14, "Error"], [13, 21, 9, 19], [13, 22, 10, 6], [13, 110, 11, 4], [13, 111, 11, 5], [14, 4, 12, 2], [15, 4, 14, 2], [15, 11, 14, 9, "progress"], [15, 19, 14, 17], [16, 2, 15, 0], [17, 0, 15, 1], [17, 3]], "functionMap": {"names": ["<global>", "useTransitionProgress"], "mappings": "AAA;eCI;CDU"}}, "type": "js/module"}]}