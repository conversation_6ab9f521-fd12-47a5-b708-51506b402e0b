{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "./Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 19}, "end": {"line": 13, "column": 42}}], "key": "kNMfvxuuydQy2ZTEl52oO+/Llac=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var Dimensions = require(_dependencyMap[3], \"./Dimensions\").default;\n  var PixelRatio = /*#__PURE__*/function () {\n    function PixelRatio() {\n      (0, _classCallCheck2.default)(this, PixelRatio);\n    }\n    return (0, _createClass2.default)(PixelRatio, null, [{\n      key: \"get\",\n      value: function get() {\n        return Dimensions.get('window').scale;\n      }\n    }, {\n      key: \"getFontScale\",\n      value: function getFontScale() {\n        return Dimensions.get('window').fontScale || PixelRatio.get();\n      }\n    }, {\n      key: \"getPixelSizeForLayoutSize\",\n      value: function getPixelSizeForLayoutSize(layoutSize) {\n        return Math.round(layoutSize * PixelRatio.get());\n      }\n    }, {\n      key: \"roundToNearestPixel\",\n      value: function roundToNearestPixel(layoutSize) {\n        var ratio = PixelRatio.get();\n        return Math.round(layoutSize * ratio) / ratio;\n      }\n    }, {\n      key: \"startDetecting\",\n      value: function startDetecting() {}\n    }]);\n  }();\n  var _default = exports.default = PixelRatio;\n});", "lineCount": 43, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 13, 0], [11, 6, 13, 6, "Dimensions"], [11, 16, 13, 16], [11, 19, 13, 19, "require"], [11, 26, 13, 26], [11, 27, 13, 26, "_dependencyMap"], [11, 41, 13, 26], [11, 60, 13, 41], [11, 61, 13, 42], [11, 62, 13, 43, "default"], [11, 69, 13, 50], [12, 2, 13, 51], [12, 6, 59, 6, "PixelRatio"], [12, 16, 59, 16], [13, 4, 59, 16], [13, 13, 59, 16, "PixelRatio"], [13, 24, 59, 16], [14, 6, 59, 16], [14, 10, 59, 16, "_classCallCheck2"], [14, 26, 59, 16], [14, 27, 59, 16, "default"], [14, 34, 59, 16], [14, 42, 59, 16, "PixelRatio"], [14, 52, 59, 16], [15, 4, 59, 16], [16, 4, 59, 16], [16, 15, 59, 16, "_createClass2"], [16, 28, 59, 16], [16, 29, 59, 16, "default"], [16, 36, 59, 16], [16, 38, 59, 16, "PixelRatio"], [16, 48, 59, 16], [17, 6, 59, 16, "key"], [17, 9, 59, 16], [18, 6, 59, 16, "value"], [18, 11, 59, 16], [18, 13, 84, 2], [18, 22, 84, 9, "get"], [18, 25, 84, 12, "get"], [18, 26, 84, 12], [18, 28, 84, 23], [19, 8, 85, 4], [19, 15, 85, 11, "Dimensions"], [19, 25, 85, 21], [19, 26, 85, 22, "get"], [19, 29, 85, 25], [19, 30, 85, 26], [19, 38, 85, 34], [19, 39, 85, 35], [19, 40, 85, 36, "scale"], [19, 45, 85, 41], [20, 6, 86, 2], [21, 4, 86, 3], [22, 6, 86, 3, "key"], [22, 9, 86, 3], [23, 6, 86, 3, "value"], [23, 11, 86, 3], [23, 13, 99, 2], [23, 22, 99, 9, "getFontScale"], [23, 34, 99, 21, "getFontScale"], [23, 35, 99, 21], [23, 37, 99, 32], [24, 8, 100, 4], [24, 15, 100, 11, "Dimensions"], [24, 25, 100, 21], [24, 26, 100, 22, "get"], [24, 29, 100, 25], [24, 30, 100, 26], [24, 38, 100, 34], [24, 39, 100, 35], [24, 40, 100, 36, "fontScale"], [24, 49, 100, 45], [24, 53, 100, 49, "PixelRatio"], [24, 63, 100, 59], [24, 64, 100, 60, "get"], [24, 67, 100, 63], [24, 68, 100, 64], [24, 69, 100, 65], [25, 6, 101, 2], [26, 4, 101, 3], [27, 6, 101, 3, "key"], [27, 9, 101, 3], [28, 6, 101, 3, "value"], [28, 11, 101, 3], [28, 13, 108, 2], [28, 22, 108, 9, "getPixelSizeForLayoutSize"], [28, 47, 108, 34, "getPixelSizeForLayoutSize"], [28, 48, 108, 35, "layoutSize"], [28, 58, 108, 53], [28, 60, 108, 63], [29, 8, 109, 4], [29, 15, 109, 11, "Math"], [29, 19, 109, 15], [29, 20, 109, 16, "round"], [29, 25, 109, 21], [29, 26, 109, 22, "layoutSize"], [29, 36, 109, 32], [29, 39, 109, 35, "PixelRatio"], [29, 49, 109, 45], [29, 50, 109, 46, "get"], [29, 53, 109, 49], [29, 54, 109, 50], [29, 55, 109, 51], [29, 56, 109, 52], [30, 6, 110, 2], [31, 4, 110, 3], [32, 6, 110, 3, "key"], [32, 9, 110, 3], [33, 6, 110, 3, "value"], [33, 11, 110, 3], [33, 13, 118, 2], [33, 22, 118, 9, "roundToNearestPixel"], [33, 41, 118, 28, "roundToNearestPixel"], [33, 42, 118, 29, "layoutSize"], [33, 52, 118, 47], [33, 54, 118, 57], [34, 8, 119, 4], [34, 12, 119, 10, "ratio"], [34, 17, 119, 15], [34, 20, 119, 18, "PixelRatio"], [34, 30, 119, 28], [34, 31, 119, 29, "get"], [34, 34, 119, 32], [34, 35, 119, 33], [34, 36, 119, 34], [35, 8, 120, 4], [35, 15, 120, 11, "Math"], [35, 19, 120, 15], [35, 20, 120, 16, "round"], [35, 25, 120, 21], [35, 26, 120, 22, "layoutSize"], [35, 36, 120, 32], [35, 39, 120, 35, "ratio"], [35, 44, 120, 40], [35, 45, 120, 41], [35, 48, 120, 44, "ratio"], [35, 53, 120, 49], [36, 6, 121, 2], [37, 4, 121, 3], [38, 6, 121, 3, "key"], [38, 9, 121, 3], [39, 6, 121, 3, "value"], [39, 11, 121, 3], [39, 13, 124, 2], [39, 22, 124, 9, "startDetecting"], [39, 36, 124, 23, "startDetecting"], [39, 37, 124, 23], [39, 39, 124, 26], [39, 40, 124, 27], [40, 4, 124, 28], [41, 2, 124, 28], [42, 2, 124, 28], [42, 6, 124, 28, "_default"], [42, 14, 124, 28], [42, 17, 124, 28, "exports"], [42, 24, 124, 28], [42, 25, 124, 28, "default"], [42, 32, 124, 28], [42, 35, 127, 15, "PixelRatio"], [42, 45, 127, 25], [43, 0, 127, 25], [43, 3]], "functionMap": {"names": ["<global>", "PixelRatio", "get", "getFontScale", "getPixelSizeForLayoutSize", "roundToNearestPixel", "startDetecting"], "mappings": "AAA;AC0D;ECyB;GDE;EEa;GFE;EGO;GHE;EIQ;GJG;EKG,0BL;CDC"}}, "type": "js/module"}]}