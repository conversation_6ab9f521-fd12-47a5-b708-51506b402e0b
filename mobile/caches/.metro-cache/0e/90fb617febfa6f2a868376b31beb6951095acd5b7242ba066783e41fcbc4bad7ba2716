{"dependencies": [{"name": "expo-linking", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 40, "column": 29, "index": 1630}, "end": {"line": 40, "column": 52, "index": 1653}}], "key": "F3IRuZxT1cyHB74rJR7WrB3Q6GA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/index", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "55efhPHw3gz2FoQtoN2yI1VuhbM=", "exportNames": ["*"]}}, {"name": "../fork/extractPathFromURL", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 42, "column": 29, "index": 1733}, "end": {"line": 42, "column": 66, "index": 1770}}], "key": "pUFbwQoQZlov/XwfhUdJl+NeEyQ=", "exportNames": ["*"]}}, {"name": "../fork/getPathFromState", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 43, "column": 27, "index": 1799}, "end": {"line": 43, "column": 62, "index": 1834}}], "key": "bbl8pYP1ohlaNy5xlw9jy/lZMmQ=", "exportNames": ["*"]}}, {"name": "../fork/getStateFromPath", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 45, "column": 27, "index": 2003}, "end": {"line": 45, "column": 62, "index": 2038}}], "key": "RO3aOY3sN2Er2u7UZCH0KWb+jbA=", "exportNames": ["*"]}}, {"name": "../fork/useLinking", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 47, "column": 21, "index": 2201}, "end": {"line": 47, "column": 50, "index": 2230}}], "key": "2q5pbZsBc45NbQNFlIwJDYHkJak=", "exportNames": ["*"]}}, {"name": "../getRoutesRedirects", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 48, "column": 29, "index": 2261}, "end": {"line": 48, "column": 61, "index": 2293}}], "key": "yKsaMnGrawCxqUEp+aBM/Yxeh5A=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = {\n        enumerable: true,\n        get: function () {\n          return m[k];\n        }\n      };\n    }\n    Object.defineProperty(o, k2, desc);\n  } : function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n  });\n  var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n    Object.defineProperty(o, \"default\", {\n      enumerable: true,\n      value: v\n    });\n  } : function (o, v) {\n    o[\"default\"] = v;\n  });\n  var __importStar = this && this.__importStar || function () {\n    var ownKeys = function (o) {\n      ownKeys = Object.getOwnPropertyNames || function (o) {\n        var ar = [];\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n        return ar;\n      };\n      return ownKeys(o);\n    };\n    return function (mod) {\n      if (mod && mod.__esModule) return mod;\n      var result = {};\n      if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n      __setModuleDefault(result, mod);\n      return result;\n    };\n  }();\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getPathFromState = exports.getStateFromPath = void 0;\n  exports.getInitialURL = getInitialURL;\n  exports.getRootURL = getRootURL;\n  exports.subscribe = subscribe;\n  const Linking = __importStar(require(_dependencyMap[0], \"expo-linking\"));\n  const react_native_1 = require(_dependencyMap[1], \"react-native-web/dist/index\");\n  const extractPathFromURL_1 = require(_dependencyMap[2], \"../fork/extractPathFromURL\");\n  const getPathFromState_1 = require(_dependencyMap[3], \"../fork/getPathFromState\");\n  Object.defineProperty(exports, \"getPathFromState\", {\n    enumerable: true,\n    get: function () {\n      return getPathFromState_1.getPathFromState;\n    }\n  });\n  const getStateFromPath_1 = require(_dependencyMap[4], \"../fork/getStateFromPath\");\n  Object.defineProperty(exports, \"getStateFromPath\", {\n    enumerable: true,\n    get: function () {\n      return getStateFromPath_1.getStateFromPath;\n    }\n  });\n  const useLinking_1 = require(_dependencyMap[5], \"../fork/useLinking\");\n  const getRoutesRedirects_1 = require(_dependencyMap[6], \"../getRoutesRedirects\");\n  const isExpoGo = typeof expo !== 'undefined' && globalThis.expo?.modules?.ExpoGo;\n  // A custom getInitialURL is used on native to ensure the app always starts at\n  // the root path if it's launched from something other than a deep link.\n  // This helps keep the native functionality working like the web functionality.\n  // For example, if you had a root navigator where the first screen was `/settings` and the second was `/index`\n  // then `/index` would be used on web and `/settings` would be used on native.\n  function getInitialURL() {\n    if (typeof window === 'undefined') {\n      return '';\n    }\n    if (react_native_1.Platform.OS === 'web' && window.location?.href) {\n      return window.location.href;\n    }\n    if (react_native_1.Platform.OS === 'ios') {\n      // Use the new Expo API for iOS. This has better support for App Clips and handoff.\n      const url = Linking.getLinkingURL();\n      return parseExpoGoUrlFromListener(url) ??\n      // The path will be nullish in bare apps when the app is launched from the home screen.\n      // TODO(EvanBacon): define some policy around notifications.\n      getRootURL();\n    }\n    // TODO: Figure out if expo-linking on Android has full interop with the React Native implementation.\n    return Promise.resolve((0, useLinking_1.getInitialURLWithTimeout)()).then(url => parseExpoGoUrlFromListener(url) ??\n    // The path will be nullish in bare apps when the app is launched from the home screen.\n    // TODO(EvanBacon): define some policy around notifications.\n    getRootURL());\n  }\n  let _rootURL;\n  function getRootURL() {\n    if (_rootURL === undefined) {\n      _rootURL = Linking.createURL('/');\n      if (isExpoGo) {\n        _rootURL = (0, extractPathFromURL_1.parsePathFromExpoGoLink)(_rootURL);\n      }\n    }\n    return _rootURL;\n  }\n  // Expo Go is weird and requires the root path to be `/--/`\n  function parseExpoGoUrlFromListener(url) {\n    if (!url || !isExpoGo) {\n      return url;\n    }\n    const {\n      pathname,\n      queryString\n    } = (0, extractPathFromURL_1.parsePathAndParamsFromExpoGoLink)(url);\n    // If the URL is defined (default in Expo Go dev apps) and the URL has no path:\n    // `exp://*************:19000/` then use the default `exp://*************:19000/--/`\n    if (!pathname || pathname === '/') {\n      return getRootURL() + queryString;\n    }\n    return url;\n  }\n  function subscribe(nativeLinking, redirects) {\n    return listener => {\n      let callback;\n      const legacySubscription = nativeLinking?.legacy_subscribe?.(listener);\n      if (isExpoGo) {\n        // This extra work is only done in the Expo Go app.\n        callback = async ({\n          url\n        }) => {\n          let href = parseExpoGoUrlFromListener(url);\n          href = (0, getRoutesRedirects_1.applyRedirects)(href, redirects);\n          if (href && nativeLinking?.redirectSystemPath) {\n            href = await nativeLinking.redirectSystemPath({\n              path: href,\n              initial: false\n            });\n          }\n          if (href) {\n            listener(href);\n          }\n        };\n      } else {\n        callback = async ({\n          url\n        }) => {\n          let href = (0, getRoutesRedirects_1.applyRedirects)(url, redirects);\n          if (href && nativeLinking?.redirectSystemPath) {\n            href = await nativeLinking.redirectSystemPath({\n              path: href,\n              initial: false\n            });\n          }\n          if (href) {\n            listener(href);\n          }\n        };\n      }\n      const subscription = Linking.addEventListener('url', callback);\n      return () => {\n        // https://github.com/facebook/react-native/commit/6d1aca806cee86ad76de771ed3a1cc62982ebcd7\n        subscription?.remove?.();\n        legacySubscription?.();\n      };\n    };\n  }\n});", "lineCount": 169, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__createBinding"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__createBinding"], [4, 52, 2, 51], [4, 57, 2, 57, "Object"], [4, 63, 2, 63], [4, 64, 2, 64, "create"], [4, 70, 2, 70], [4, 73, 2, 74], [4, 83, 2, 83, "o"], [4, 84, 2, 84], [4, 86, 2, 86, "m"], [4, 87, 2, 87], [4, 89, 2, 89, "k"], [4, 90, 2, 90], [4, 92, 2, 92, "k2"], [4, 94, 2, 94], [4, 96, 2, 96], [5, 4, 3, 4], [5, 8, 3, 8, "k2"], [5, 10, 3, 10], [5, 15, 3, 15, "undefined"], [5, 24, 3, 24], [5, 26, 3, 26, "k2"], [5, 28, 3, 28], [5, 31, 3, 31, "k"], [5, 32, 3, 32], [6, 4, 4, 4], [6, 8, 4, 8, "desc"], [6, 12, 4, 12], [6, 15, 4, 15, "Object"], [6, 21, 4, 21], [6, 22, 4, 22, "getOwnPropertyDescriptor"], [6, 46, 4, 46], [6, 47, 4, 47, "m"], [6, 48, 4, 48], [6, 50, 4, 50, "k"], [6, 51, 4, 51], [6, 52, 4, 52], [7, 4, 5, 4], [7, 8, 5, 8], [7, 9, 5, 9, "desc"], [7, 13, 5, 13], [7, 18, 5, 18], [7, 23, 5, 23], [7, 27, 5, 27, "desc"], [7, 31, 5, 31], [7, 34, 5, 34], [7, 35, 5, 35, "m"], [7, 36, 5, 36], [7, 37, 5, 37, "__esModule"], [7, 47, 5, 47], [7, 50, 5, 50, "desc"], [7, 54, 5, 54], [7, 55, 5, 55, "writable"], [7, 63, 5, 63], [7, 67, 5, 67, "desc"], [7, 71, 5, 71], [7, 72, 5, 72, "configurable"], [7, 84, 5, 84], [7, 85, 5, 85], [7, 87, 5, 87], [8, 6, 6, 6, "desc"], [8, 10, 6, 10], [8, 13, 6, 13], [9, 8, 6, 15, "enumerable"], [9, 18, 6, 25], [9, 20, 6, 27], [9, 24, 6, 31], [10, 8, 6, 33, "get"], [10, 11, 6, 36], [10, 13, 6, 38], [10, 22, 6, 38, "get"], [10, 23, 6, 38], [10, 25, 6, 49], [11, 10, 6, 51], [11, 17, 6, 58, "m"], [11, 18, 6, 59], [11, 19, 6, 60, "k"], [11, 20, 6, 61], [11, 21, 6, 62], [12, 8, 6, 64], [13, 6, 6, 66], [13, 7, 6, 67], [14, 4, 7, 4], [15, 4, 8, 4, "Object"], [15, 10, 8, 10], [15, 11, 8, 11, "defineProperty"], [15, 25, 8, 25], [15, 26, 8, 26, "o"], [15, 27, 8, 27], [15, 29, 8, 29, "k2"], [15, 31, 8, 31], [15, 33, 8, 33, "desc"], [15, 37, 8, 37], [15, 38, 8, 38], [16, 2, 9, 0], [16, 3, 9, 1], [16, 6, 9, 6], [16, 16, 9, 15, "o"], [16, 17, 9, 16], [16, 19, 9, 18, "m"], [16, 20, 9, 19], [16, 22, 9, 21, "k"], [16, 23, 9, 22], [16, 25, 9, 24, "k2"], [16, 27, 9, 26], [16, 29, 9, 28], [17, 4, 10, 4], [17, 8, 10, 8, "k2"], [17, 10, 10, 10], [17, 15, 10, 15, "undefined"], [17, 24, 10, 24], [17, 26, 10, 26, "k2"], [17, 28, 10, 28], [17, 31, 10, 31, "k"], [17, 32, 10, 32], [18, 4, 11, 4, "o"], [18, 5, 11, 5], [18, 6, 11, 6, "k2"], [18, 8, 11, 8], [18, 9, 11, 9], [18, 12, 11, 12, "m"], [18, 13, 11, 13], [18, 14, 11, 14, "k"], [18, 15, 11, 15], [18, 16, 11, 16], [19, 2, 12, 0], [19, 3, 12, 2], [19, 4, 12, 3], [20, 2, 13, 0], [20, 6, 13, 4, "__setModuleDefault"], [20, 24, 13, 22], [20, 27, 13, 26], [20, 31, 13, 30], [20, 35, 13, 34], [20, 39, 13, 38], [20, 40, 13, 39, "__setModuleDefault"], [20, 58, 13, 57], [20, 63, 13, 63, "Object"], [20, 69, 13, 69], [20, 70, 13, 70, "create"], [20, 76, 13, 76], [20, 79, 13, 80], [20, 89, 13, 89, "o"], [20, 90, 13, 90], [20, 92, 13, 92, "v"], [20, 93, 13, 93], [20, 95, 13, 95], [21, 4, 14, 4, "Object"], [21, 10, 14, 10], [21, 11, 14, 11, "defineProperty"], [21, 25, 14, 25], [21, 26, 14, 26, "o"], [21, 27, 14, 27], [21, 29, 14, 29], [21, 38, 14, 38], [21, 40, 14, 40], [22, 6, 14, 42, "enumerable"], [22, 16, 14, 52], [22, 18, 14, 54], [22, 22, 14, 58], [23, 6, 14, 60, "value"], [23, 11, 14, 65], [23, 13, 14, 67, "v"], [24, 4, 14, 69], [24, 5, 14, 70], [24, 6, 14, 71], [25, 2, 15, 0], [25, 3, 15, 1], [25, 6, 15, 5], [25, 16, 15, 14, "o"], [25, 17, 15, 15], [25, 19, 15, 17, "v"], [25, 20, 15, 18], [25, 22, 15, 20], [26, 4, 16, 4, "o"], [26, 5, 16, 5], [26, 6, 16, 6], [26, 15, 16, 15], [26, 16, 16, 16], [26, 19, 16, 19, "v"], [26, 20, 16, 20], [27, 2, 17, 0], [27, 3, 17, 1], [27, 4, 17, 2], [28, 2, 18, 0], [28, 6, 18, 4, "__importStar"], [28, 18, 18, 16], [28, 21, 18, 20], [28, 25, 18, 24], [28, 29, 18, 28], [28, 33, 18, 32], [28, 34, 18, 33, "__importStar"], [28, 46, 18, 45], [28, 50, 18, 51], [28, 62, 18, 63], [29, 4, 19, 4], [29, 8, 19, 8, "ownKeys"], [29, 15, 19, 15], [29, 18, 19, 18], [29, 27, 19, 18, "ownKeys"], [29, 28, 19, 27, "o"], [29, 29, 19, 28], [29, 31, 19, 30], [30, 6, 20, 8, "ownKeys"], [30, 13, 20, 15], [30, 16, 20, 18, "Object"], [30, 22, 20, 24], [30, 23, 20, 25, "getOwnPropertyNames"], [30, 42, 20, 44], [30, 46, 20, 48], [30, 56, 20, 58, "o"], [30, 57, 20, 59], [30, 59, 20, 61], [31, 8, 21, 12], [31, 12, 21, 16, "ar"], [31, 14, 21, 18], [31, 17, 21, 21], [31, 19, 21, 23], [32, 8, 22, 12], [32, 13, 22, 17], [32, 17, 22, 21, "k"], [32, 18, 22, 22], [32, 22, 22, 26, "o"], [32, 23, 22, 27], [32, 25, 22, 29], [32, 29, 22, 33, "Object"], [32, 35, 22, 39], [32, 36, 22, 40, "prototype"], [32, 45, 22, 49], [32, 46, 22, 50, "hasOwnProperty"], [32, 60, 22, 64], [32, 61, 22, 65, "call"], [32, 65, 22, 69], [32, 66, 22, 70, "o"], [32, 67, 22, 71], [32, 69, 22, 73, "k"], [32, 70, 22, 74], [32, 71, 22, 75], [32, 73, 22, 77, "ar"], [32, 75, 22, 79], [32, 76, 22, 80, "ar"], [32, 78, 22, 82], [32, 79, 22, 83, "length"], [32, 85, 22, 89], [32, 86, 22, 90], [32, 89, 22, 93, "k"], [32, 90, 22, 94], [33, 8, 23, 12], [33, 15, 23, 19, "ar"], [33, 17, 23, 21], [34, 6, 24, 8], [34, 7, 24, 9], [35, 6, 25, 8], [35, 13, 25, 15, "ownKeys"], [35, 20, 25, 22], [35, 21, 25, 23, "o"], [35, 22, 25, 24], [35, 23, 25, 25], [36, 4, 26, 4], [36, 5, 26, 5], [37, 4, 27, 4], [37, 11, 27, 11], [37, 21, 27, 21, "mod"], [37, 24, 27, 24], [37, 26, 27, 26], [38, 6, 28, 8], [38, 10, 28, 12, "mod"], [38, 13, 28, 15], [38, 17, 28, 19, "mod"], [38, 20, 28, 22], [38, 21, 28, 23, "__esModule"], [38, 31, 28, 33], [38, 33, 28, 35], [38, 40, 28, 42, "mod"], [38, 43, 28, 45], [39, 6, 29, 8], [39, 10, 29, 12, "result"], [39, 16, 29, 18], [39, 19, 29, 21], [39, 20, 29, 22], [39, 21, 29, 23], [40, 6, 30, 8], [40, 10, 30, 12, "mod"], [40, 13, 30, 15], [40, 17, 30, 19], [40, 21, 30, 23], [40, 23, 30, 25], [40, 28, 30, 30], [40, 32, 30, 34, "k"], [40, 33, 30, 35], [40, 36, 30, 38, "ownKeys"], [40, 43, 30, 45], [40, 44, 30, 46, "mod"], [40, 47, 30, 49], [40, 48, 30, 50], [40, 50, 30, 52, "i"], [40, 51, 30, 53], [40, 54, 30, 56], [40, 55, 30, 57], [40, 57, 30, 59, "i"], [40, 58, 30, 60], [40, 61, 30, 63, "k"], [40, 62, 30, 64], [40, 63, 30, 65, "length"], [40, 69, 30, 71], [40, 71, 30, 73, "i"], [40, 72, 30, 74], [40, 74, 30, 76], [40, 76, 30, 78], [40, 80, 30, 82, "k"], [40, 81, 30, 83], [40, 82, 30, 84, "i"], [40, 83, 30, 85], [40, 84, 30, 86], [40, 89, 30, 91], [40, 98, 30, 100], [40, 100, 30, 102, "__createBinding"], [40, 115, 30, 117], [40, 116, 30, 118, "result"], [40, 122, 30, 124], [40, 124, 30, 126, "mod"], [40, 127, 30, 129], [40, 129, 30, 131, "k"], [40, 130, 30, 132], [40, 131, 30, 133, "i"], [40, 132, 30, 134], [40, 133, 30, 135], [40, 134, 30, 136], [41, 6, 31, 8, "__setModuleDefault"], [41, 24, 31, 26], [41, 25, 31, 27, "result"], [41, 31, 31, 33], [41, 33, 31, 35, "mod"], [41, 36, 31, 38], [41, 37, 31, 39], [42, 6, 32, 8], [42, 13, 32, 15, "result"], [42, 19, 32, 21], [43, 4, 33, 4], [43, 5, 33, 5], [44, 2, 34, 0], [44, 3, 34, 1], [44, 4, 34, 3], [44, 5, 34, 4], [45, 2, 35, 0, "Object"], [45, 8, 35, 6], [45, 9, 35, 7, "defineProperty"], [45, 23, 35, 21], [45, 24, 35, 22, "exports"], [45, 31, 35, 29], [45, 33, 35, 31], [45, 45, 35, 43], [45, 47, 35, 45], [46, 4, 35, 47, "value"], [46, 9, 35, 52], [46, 11, 35, 54], [47, 2, 35, 59], [47, 3, 35, 60], [47, 4, 35, 61], [48, 2, 36, 0, "exports"], [48, 9, 36, 7], [48, 10, 36, 8, "getPathFromState"], [48, 26, 36, 24], [48, 29, 36, 27, "exports"], [48, 36, 36, 34], [48, 37, 36, 35, "getStateFromPath"], [48, 53, 36, 51], [48, 56, 36, 54], [48, 61, 36, 59], [48, 62, 36, 60], [49, 2, 37, 0, "exports"], [49, 9, 37, 7], [49, 10, 37, 8, "getInitialURL"], [49, 23, 37, 21], [49, 26, 37, 24, "getInitialURL"], [49, 39, 37, 37], [50, 2, 38, 0, "exports"], [50, 9, 38, 7], [50, 10, 38, 8, "getRootURL"], [50, 20, 38, 18], [50, 23, 38, 21, "getRootURL"], [50, 33, 38, 31], [51, 2, 39, 0, "exports"], [51, 9, 39, 7], [51, 10, 39, 8, "subscribe"], [51, 19, 39, 17], [51, 22, 39, 20, "subscribe"], [51, 31, 39, 29], [52, 2, 40, 0], [52, 8, 40, 6, "Linking"], [52, 15, 40, 13], [52, 18, 40, 16, "__importStar"], [52, 30, 40, 28], [52, 31, 40, 29, "require"], [52, 38, 40, 36], [52, 39, 40, 36, "_dependencyMap"], [52, 53, 40, 36], [52, 72, 40, 51], [52, 73, 40, 52], [52, 74, 40, 53], [53, 2, 40, 54], [53, 8, 40, 54, "react_native_1"], [53, 22, 40, 54], [53, 25, 40, 54, "require"], [53, 32, 40, 54], [53, 33, 40, 54, "_dependencyMap"], [53, 47, 40, 54], [54, 2, 42, 0], [54, 8, 42, 6, "extractPathFromURL_1"], [54, 28, 42, 26], [54, 31, 42, 29, "require"], [54, 38, 42, 36], [54, 39, 42, 36, "_dependencyMap"], [54, 53, 42, 36], [54, 86, 42, 65], [54, 87, 42, 66], [55, 2, 43, 0], [55, 8, 43, 6, "getPathFromState_1"], [55, 26, 43, 24], [55, 29, 43, 27, "require"], [55, 36, 43, 34], [55, 37, 43, 34, "_dependencyMap"], [55, 51, 43, 34], [55, 82, 43, 61], [55, 83, 43, 62], [56, 2, 44, 0, "Object"], [56, 8, 44, 6], [56, 9, 44, 7, "defineProperty"], [56, 23, 44, 21], [56, 24, 44, 22, "exports"], [56, 31, 44, 29], [56, 33, 44, 31], [56, 51, 44, 49], [56, 53, 44, 51], [57, 4, 44, 53, "enumerable"], [57, 14, 44, 63], [57, 16, 44, 65], [57, 20, 44, 69], [58, 4, 44, 71, "get"], [58, 7, 44, 74], [58, 9, 44, 76], [58, 18, 44, 76, "get"], [58, 19, 44, 76], [58, 21, 44, 88], [59, 6, 44, 90], [59, 13, 44, 97, "getPathFromState_1"], [59, 31, 44, 115], [59, 32, 44, 116, "getPathFromState"], [59, 48, 44, 132], [60, 4, 44, 134], [61, 2, 44, 136], [61, 3, 44, 137], [61, 4, 44, 138], [62, 2, 45, 0], [62, 8, 45, 6, "getStateFromPath_1"], [62, 26, 45, 24], [62, 29, 45, 27, "require"], [62, 36, 45, 34], [62, 37, 45, 34, "_dependencyMap"], [62, 51, 45, 34], [62, 82, 45, 61], [62, 83, 45, 62], [63, 2, 46, 0, "Object"], [63, 8, 46, 6], [63, 9, 46, 7, "defineProperty"], [63, 23, 46, 21], [63, 24, 46, 22, "exports"], [63, 31, 46, 29], [63, 33, 46, 31], [63, 51, 46, 49], [63, 53, 46, 51], [64, 4, 46, 53, "enumerable"], [64, 14, 46, 63], [64, 16, 46, 65], [64, 20, 46, 69], [65, 4, 46, 71, "get"], [65, 7, 46, 74], [65, 9, 46, 76], [65, 18, 46, 76, "get"], [65, 19, 46, 76], [65, 21, 46, 88], [66, 6, 46, 90], [66, 13, 46, 97, "getStateFromPath_1"], [66, 31, 46, 115], [66, 32, 46, 116, "getStateFromPath"], [66, 48, 46, 132], [67, 4, 46, 134], [68, 2, 46, 136], [68, 3, 46, 137], [68, 4, 46, 138], [69, 2, 47, 0], [69, 8, 47, 6, "useLinking_1"], [69, 20, 47, 18], [69, 23, 47, 21, "require"], [69, 30, 47, 28], [69, 31, 47, 28, "_dependencyMap"], [69, 45, 47, 28], [69, 70, 47, 49], [69, 71, 47, 50], [70, 2, 48, 0], [70, 8, 48, 6, "getRoutesRedirects_1"], [70, 28, 48, 26], [70, 31, 48, 29, "require"], [70, 38, 48, 36], [70, 39, 48, 36, "_dependencyMap"], [70, 53, 48, 36], [70, 81, 48, 60], [70, 82, 48, 61], [71, 2, 49, 0], [71, 8, 49, 6, "isExpoGo"], [71, 16, 49, 14], [71, 19, 49, 17], [71, 26, 49, 24, "expo"], [71, 30, 49, 28], [71, 35, 49, 33], [71, 46, 49, 44], [71, 50, 49, 48, "globalThis"], [71, 60, 49, 58], [71, 61, 49, 59, "expo"], [71, 65, 49, 63], [71, 67, 49, 65, "modules"], [71, 74, 49, 72], [71, 76, 49, 74, "ExpoGo"], [71, 82, 49, 80], [72, 2, 50, 0], [73, 2, 51, 0], [74, 2, 52, 0], [75, 2, 53, 0], [76, 2, 54, 0], [77, 2, 55, 0], [77, 11, 55, 9, "getInitialURL"], [77, 24, 55, 22, "getInitialURL"], [77, 25, 55, 22], [77, 27, 55, 25], [78, 4, 56, 4], [78, 8, 56, 8], [78, 15, 56, 15, "window"], [78, 21, 56, 21], [78, 26, 56, 26], [78, 37, 56, 37], [78, 39, 56, 39], [79, 6, 57, 8], [79, 13, 57, 15], [79, 15, 57, 17], [80, 4, 58, 4], [81, 4, 59, 4], [81, 8, 59, 8, "react_native_1"], [81, 22, 59, 22], [81, 23, 59, 23, "Platform"], [81, 31, 59, 31], [81, 32, 59, 32, "OS"], [81, 34, 59, 34], [81, 39, 59, 39], [81, 44, 59, 44], [81, 48, 59, 48, "window"], [81, 54, 59, 54], [81, 55, 59, 55, "location"], [81, 63, 59, 63], [81, 65, 59, 65, "href"], [81, 69, 59, 69], [81, 71, 59, 71], [82, 6, 60, 8], [82, 13, 60, 15, "window"], [82, 19, 60, 21], [82, 20, 60, 22, "location"], [82, 28, 60, 30], [82, 29, 60, 31, "href"], [82, 33, 60, 35], [83, 4, 61, 4], [84, 4, 62, 4], [84, 8, 62, 8, "react_native_1"], [84, 22, 62, 22], [84, 23, 62, 23, "Platform"], [84, 31, 62, 31], [84, 32, 62, 32, "OS"], [84, 34, 62, 34], [84, 39, 62, 39], [84, 44, 62, 44], [84, 46, 62, 46], [85, 6, 63, 8], [86, 6, 64, 8], [86, 12, 64, 14, "url"], [86, 15, 64, 17], [86, 18, 64, 20, "Linking"], [86, 25, 64, 27], [86, 26, 64, 28, "getLinkingURL"], [86, 39, 64, 41], [86, 40, 64, 42], [86, 41, 64, 43], [87, 6, 65, 8], [87, 13, 65, 16, "parseExpoGoUrlFromListener"], [87, 39, 65, 42], [87, 40, 65, 43, "url"], [87, 43, 65, 46], [87, 44, 65, 47], [88, 6, 66, 12], [89, 6, 67, 12], [90, 6, 68, 12, "getRootURL"], [90, 16, 68, 22], [90, 17, 68, 23], [90, 18, 68, 24], [91, 4, 69, 4], [92, 4, 70, 4], [93, 4, 71, 4], [93, 11, 71, 11, "Promise"], [93, 18, 71, 18], [93, 19, 71, 19, "resolve"], [93, 26, 71, 26], [93, 27, 71, 27], [93, 28, 71, 28], [93, 29, 71, 29], [93, 31, 71, 31, "useLinking_1"], [93, 43, 71, 43], [93, 44, 71, 44, "getInitialURLWithTimeout"], [93, 68, 71, 68], [93, 70, 71, 70], [93, 71, 71, 71], [93, 72, 71, 72], [93, 73, 71, 73, "then"], [93, 77, 71, 77], [93, 78, 71, 79, "url"], [93, 81, 71, 82], [93, 85, 71, 87, "parseExpoGoUrlFromListener"], [93, 111, 71, 113], [93, 112, 71, 114, "url"], [93, 115, 71, 117], [93, 116, 71, 118], [94, 4, 72, 8], [95, 4, 73, 8], [96, 4, 74, 8, "getRootURL"], [96, 14, 74, 18], [96, 15, 74, 19], [96, 16, 74, 20], [96, 17, 74, 21], [97, 2, 75, 0], [98, 2, 76, 0], [98, 6, 76, 4, "_rootURL"], [98, 14, 76, 12], [99, 2, 77, 0], [99, 11, 77, 9, "getRootURL"], [99, 21, 77, 19, "getRootURL"], [99, 22, 77, 19], [99, 24, 77, 22], [100, 4, 78, 4], [100, 8, 78, 8, "_rootURL"], [100, 16, 78, 16], [100, 21, 78, 21, "undefined"], [100, 30, 78, 30], [100, 32, 78, 32], [101, 6, 79, 8, "_rootURL"], [101, 14, 79, 16], [101, 17, 79, 19, "Linking"], [101, 24, 79, 26], [101, 25, 79, 27, "createURL"], [101, 34, 79, 36], [101, 35, 79, 37], [101, 38, 79, 40], [101, 39, 79, 41], [102, 6, 80, 8], [102, 10, 80, 12, "isExpoGo"], [102, 18, 80, 20], [102, 20, 80, 22], [103, 8, 81, 12, "_rootURL"], [103, 16, 81, 20], [103, 19, 81, 23], [103, 20, 81, 24], [103, 21, 81, 25], [103, 23, 81, 27, "extractPathFromURL_1"], [103, 43, 81, 47], [103, 44, 81, 48, "parsePathFromExpoGoLink"], [103, 67, 81, 71], [103, 69, 81, 73, "_rootURL"], [103, 77, 81, 81], [103, 78, 81, 82], [104, 6, 82, 8], [105, 4, 83, 4], [106, 4, 84, 4], [106, 11, 84, 11, "_rootURL"], [106, 19, 84, 19], [107, 2, 85, 0], [108, 2, 86, 0], [109, 2, 87, 0], [109, 11, 87, 9, "parseExpoGoUrlFromListener"], [109, 37, 87, 35, "parseExpoGoUrlFromListener"], [109, 38, 87, 36, "url"], [109, 41, 87, 39], [109, 43, 87, 41], [110, 4, 88, 4], [110, 8, 88, 8], [110, 9, 88, 9, "url"], [110, 12, 88, 12], [110, 16, 88, 16], [110, 17, 88, 17, "isExpoGo"], [110, 25, 88, 25], [110, 27, 88, 27], [111, 6, 89, 8], [111, 13, 89, 15, "url"], [111, 16, 89, 18], [112, 4, 90, 4], [113, 4, 91, 4], [113, 10, 91, 10], [114, 6, 91, 12, "pathname"], [114, 14, 91, 20], [115, 6, 91, 22, "queryString"], [116, 4, 91, 34], [116, 5, 91, 35], [116, 8, 91, 38], [116, 9, 91, 39], [116, 10, 91, 40], [116, 12, 91, 42, "extractPathFromURL_1"], [116, 32, 91, 62], [116, 33, 91, 63, "parsePathAndParamsFromExpoGoLink"], [116, 65, 91, 95], [116, 67, 91, 97, "url"], [116, 70, 91, 100], [116, 71, 91, 101], [117, 4, 92, 4], [118, 4, 93, 4], [119, 4, 94, 4], [119, 8, 94, 8], [119, 9, 94, 9, "pathname"], [119, 17, 94, 17], [119, 21, 94, 21, "pathname"], [119, 29, 94, 29], [119, 34, 94, 34], [119, 37, 94, 37], [119, 39, 94, 39], [120, 6, 95, 8], [120, 13, 95, 16, "getRootURL"], [120, 23, 95, 26], [120, 24, 95, 27], [120, 25, 95, 28], [120, 28, 95, 31, "queryString"], [120, 39, 95, 42], [121, 4, 96, 4], [122, 4, 97, 4], [122, 11, 97, 11, "url"], [122, 14, 97, 14], [123, 2, 98, 0], [124, 2, 99, 0], [124, 11, 99, 9, "subscribe"], [124, 20, 99, 18, "subscribe"], [124, 21, 99, 19, "nativeLinking"], [124, 34, 99, 32], [124, 36, 99, 34, "redirects"], [124, 45, 99, 43], [124, 47, 99, 45], [125, 4, 100, 4], [125, 11, 100, 12, "listener"], [125, 19, 100, 20], [125, 23, 100, 25], [126, 6, 101, 8], [126, 10, 101, 12, "callback"], [126, 18, 101, 20], [127, 6, 102, 8], [127, 12, 102, 14, "legacySubscription"], [127, 30, 102, 32], [127, 33, 102, 35, "nativeLinking"], [127, 46, 102, 48], [127, 48, 102, 50, "legacy_subscribe"], [127, 64, 102, 66], [127, 67, 102, 69, "listener"], [127, 75, 102, 77], [127, 76, 102, 78], [128, 6, 103, 8], [128, 10, 103, 12, "isExpoGo"], [128, 18, 103, 20], [128, 20, 103, 22], [129, 8, 104, 12], [130, 8, 105, 12, "callback"], [130, 16, 105, 20], [130, 19, 105, 23], [130, 25, 105, 23, "callback"], [130, 26, 105, 30], [131, 10, 105, 32, "url"], [132, 8, 105, 36], [132, 9, 105, 37], [132, 14, 105, 42], [133, 10, 106, 16], [133, 14, 106, 20, "href"], [133, 18, 106, 24], [133, 21, 106, 27, "parseExpoGoUrlFromListener"], [133, 47, 106, 53], [133, 48, 106, 54, "url"], [133, 51, 106, 57], [133, 52, 106, 58], [134, 10, 107, 16, "href"], [134, 14, 107, 20], [134, 17, 107, 23], [134, 18, 107, 24], [134, 19, 107, 25], [134, 21, 107, 27, "getRoutesRedirects_1"], [134, 41, 107, 47], [134, 42, 107, 48, "applyRedirects"], [134, 56, 107, 62], [134, 58, 107, 64, "href"], [134, 62, 107, 68], [134, 64, 107, 70, "redirects"], [134, 73, 107, 79], [134, 74, 107, 80], [135, 10, 108, 16], [135, 14, 108, 20, "href"], [135, 18, 108, 24], [135, 22, 108, 28, "nativeLinking"], [135, 35, 108, 41], [135, 37, 108, 43, "redirectSystemPath"], [135, 55, 108, 61], [135, 57, 108, 63], [136, 12, 109, 20, "href"], [136, 16, 109, 24], [136, 19, 109, 27], [136, 25, 109, 33, "nativeLinking"], [136, 38, 109, 46], [136, 39, 109, 47, "redirectSystemPath"], [136, 57, 109, 65], [136, 58, 109, 66], [137, 14, 109, 68, "path"], [137, 18, 109, 72], [137, 20, 109, 74, "href"], [137, 24, 109, 78], [138, 14, 109, 80, "initial"], [138, 21, 109, 87], [138, 23, 109, 89], [139, 12, 109, 95], [139, 13, 109, 96], [139, 14, 109, 97], [140, 10, 110, 16], [141, 10, 111, 16], [141, 14, 111, 20, "href"], [141, 18, 111, 24], [141, 20, 111, 26], [142, 12, 112, 20, "listener"], [142, 20, 112, 28], [142, 21, 112, 29, "href"], [142, 25, 112, 33], [142, 26, 112, 34], [143, 10, 113, 16], [144, 8, 114, 12], [144, 9, 114, 13], [145, 6, 115, 8], [145, 7, 115, 9], [145, 13, 116, 13], [146, 8, 117, 12, "callback"], [146, 16, 117, 20], [146, 19, 117, 23], [146, 25, 117, 23, "callback"], [146, 26, 117, 30], [147, 10, 117, 32, "url"], [148, 8, 117, 36], [148, 9, 117, 37], [148, 14, 117, 42], [149, 10, 118, 16], [149, 14, 118, 20, "href"], [149, 18, 118, 24], [149, 21, 118, 27], [149, 22, 118, 28], [149, 23, 118, 29], [149, 25, 118, 31, "getRoutesRedirects_1"], [149, 45, 118, 51], [149, 46, 118, 52, "applyRedirects"], [149, 60, 118, 66], [149, 62, 118, 68, "url"], [149, 65, 118, 71], [149, 67, 118, 73, "redirects"], [149, 76, 118, 82], [149, 77, 118, 83], [150, 10, 119, 16], [150, 14, 119, 20, "href"], [150, 18, 119, 24], [150, 22, 119, 28, "nativeLinking"], [150, 35, 119, 41], [150, 37, 119, 43, "redirectSystemPath"], [150, 55, 119, 61], [150, 57, 119, 63], [151, 12, 120, 20, "href"], [151, 16, 120, 24], [151, 19, 120, 27], [151, 25, 120, 33, "nativeLinking"], [151, 38, 120, 46], [151, 39, 120, 47, "redirectSystemPath"], [151, 57, 120, 65], [151, 58, 120, 66], [152, 14, 120, 68, "path"], [152, 18, 120, 72], [152, 20, 120, 74, "href"], [152, 24, 120, 78], [153, 14, 120, 80, "initial"], [153, 21, 120, 87], [153, 23, 120, 89], [154, 12, 120, 95], [154, 13, 120, 96], [154, 14, 120, 97], [155, 10, 121, 16], [156, 10, 122, 16], [156, 14, 122, 20, "href"], [156, 18, 122, 24], [156, 20, 122, 26], [157, 12, 123, 20, "listener"], [157, 20, 123, 28], [157, 21, 123, 29, "href"], [157, 25, 123, 33], [157, 26, 123, 34], [158, 10, 124, 16], [159, 8, 125, 12], [159, 9, 125, 13], [160, 6, 126, 8], [161, 6, 127, 8], [161, 12, 127, 14, "subscription"], [161, 24, 127, 26], [161, 27, 127, 29, "Linking"], [161, 34, 127, 36], [161, 35, 127, 37, "addEventListener"], [161, 51, 127, 53], [161, 52, 127, 54], [161, 57, 127, 59], [161, 59, 127, 61, "callback"], [161, 67, 127, 69], [161, 68, 127, 70], [162, 6, 128, 8], [162, 13, 128, 15], [162, 19, 128, 21], [163, 8, 129, 12], [164, 8, 130, 12, "subscription"], [164, 20, 130, 24], [164, 22, 130, 26, "remove"], [164, 28, 130, 32], [164, 31, 130, 35], [164, 32, 130, 36], [165, 8, 131, 12, "legacySubscription"], [165, 26, 131, 30], [165, 29, 131, 33], [165, 30, 131, 34], [166, 6, 132, 8], [166, 7, 132, 9], [167, 4, 133, 4], [167, 5, 133, 5], [168, 2, 134, 0], [169, 0, 134, 1], [169, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "desc.get", "ownKeys", "Object.defineProperty$argument_2.get", "getInitialURL", "Promise.resolve.then$argument_0", "getRootURL", "parseExpoGoUrlFromListener", "subscribe", "callback"], "mappings": "AAA;0ECC;sCCI,2BD;CDG,KC;CDG;gFCC;CDE,IC;CDE;mDCC;kBEC;gDFC;SEI;KFE;CDQ;4EIU,2DJ;4EIE,2DJ;AKS;8ECgB;oBDG;CLC;AOE;CPQ;AQE;CRW;ASC;WRC;uBSK;aTS;uBSG;aTQ;KQQ;CTC"}}, "type": "js/module"}]}