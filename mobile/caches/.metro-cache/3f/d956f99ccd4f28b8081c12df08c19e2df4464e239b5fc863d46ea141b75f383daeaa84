{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createIconSetFromFontAwesome5", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 67, "index": 81}}], "key": "dMhvN/7lLF3/XUo2gwyoJrHKfVY=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free.json", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 82}, "end": {"line": 3, "column": 90, "index": 172}}], "key": "wdkUCSPsKvOhXA1dqqfqX2XWQKg=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free_meta.json", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 173}, "end": {"line": 4, "column": 95, "index": 268}}], "key": "F6AX/IPK0oJ2IswY8BNUN6gYUfA=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 13, "index": 300}, "end": {"line": 6, "column": 89, "index": 376}}, {"start": {"line": 7, "column": 11, "index": 389}, "end": {"line": 7, "column": 87, "index": 465}}], "key": "Mkp0zaODDHyzCthL+U4bOzLp61M=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 11, "index": 478}, "end": {"line": 8, "column": 85, "index": 552}}], "key": "unY2+z/96bYW4ZgqeTHEQNrmIXE=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 11, "index": 565}, "end": {"line": 9, "column": 86, "index": 640}}], "key": "0Og6VF0bIOVwpC7Dsz6s7TmIqNI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use client\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.FA5Style = void 0;\n  var _createIconSetFromFontAwesome = require(_dependencyMap[1], \"./createIconSetFromFontAwesome5\");\n  var _FontAwesome5Free = _interopRequireDefault(require(_dependencyMap[2], \"./vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free.json\"));\n  var _FontAwesome5Free_meta = _interopRequireDefault(require(_dependencyMap[3], \"./vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free_meta.json\"));\n  const fontMap = {\n    Regular: require(_dependencyMap[4], \"./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf\"),\n    Light: require(_dependencyMap[4], \"./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf\"),\n    Solid: require(_dependencyMap[5], \"./vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf\"),\n    Brand: require(_dependencyMap[6], \"./vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf\")\n  };\n  const FA5Style = exports.FA5Style = {\n    regular: 'regular',\n    light: 'light',\n    solid: 'solid',\n    brand: 'brand'\n  };\n  const iconSet = (0, _createIconSetFromFontAwesome.createFA5iconSet)(_FontAwesome5Free.default, _FontAwesome5Free_meta.default, fontMap, false);\n  var _default = exports.default = iconSet;\n});", "lineCount": 26, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "FA5Style"], [8, 36, 1, 13], [9, 2, 2, 0], [9, 6, 2, 0, "_createIconSetFromFontAwesome"], [9, 35, 2, 0], [9, 38, 2, 0, "require"], [9, 45, 2, 0], [9, 46, 2, 0, "_dependencyMap"], [9, 60, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_FontAwesome5Free"], [10, 23, 3, 0], [10, 26, 3, 0, "_interopRequireDefault"], [10, 48, 3, 0], [10, 49, 3, 0, "require"], [10, 56, 3, 0], [10, 57, 3, 0, "_dependencyMap"], [10, 71, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_FontAwesome5Free_meta"], [11, 28, 4, 0], [11, 31, 4, 0, "_interopRequireDefault"], [11, 53, 4, 0], [11, 54, 4, 0, "require"], [11, 61, 4, 0], [11, 62, 4, 0, "_dependencyMap"], [11, 76, 4, 0], [12, 2, 5, 0], [12, 8, 5, 6, "fontMap"], [12, 15, 5, 13], [12, 18, 5, 16], [13, 4, 6, 4, "Regular"], [13, 11, 6, 11], [13, 13, 6, 13, "require"], [13, 20, 6, 20], [13, 21, 6, 20, "_dependencyMap"], [13, 35, 6, 20], [13, 107, 6, 88], [13, 108, 6, 89], [14, 4, 7, 4, "Light"], [14, 9, 7, 9], [14, 11, 7, 11, "require"], [14, 18, 7, 18], [14, 19, 7, 18, "_dependencyMap"], [14, 33, 7, 18], [14, 105, 7, 86], [14, 106, 7, 87], [15, 4, 8, 4, "Solid"], [15, 9, 8, 9], [15, 11, 8, 11, "require"], [15, 18, 8, 18], [15, 19, 8, 18, "_dependencyMap"], [15, 33, 8, 18], [15, 103, 8, 84], [15, 104, 8, 85], [16, 4, 9, 4, "Brand"], [16, 9, 9, 9], [16, 11, 9, 11, "require"], [16, 18, 9, 18], [16, 19, 9, 18, "_dependencyMap"], [16, 33, 9, 18], [16, 104, 9, 85], [17, 2, 10, 0], [17, 3, 10, 1], [18, 2, 11, 7], [18, 8, 11, 13, "FA5Style"], [18, 16, 11, 21], [18, 19, 11, 21, "exports"], [18, 26, 11, 21], [18, 27, 11, 21, "FA5Style"], [18, 35, 11, 21], [18, 38, 11, 24], [19, 4, 12, 4, "regular"], [19, 11, 12, 11], [19, 13, 12, 13], [19, 22, 12, 22], [20, 4, 13, 4, "light"], [20, 9, 13, 9], [20, 11, 13, 11], [20, 18, 13, 18], [21, 4, 14, 4, "solid"], [21, 9, 14, 9], [21, 11, 14, 11], [21, 18, 14, 18], [22, 4, 15, 4, "brand"], [22, 9, 15, 9], [22, 11, 15, 11], [23, 2, 16, 0], [23, 3, 16, 1], [24, 2, 17, 0], [24, 8, 17, 6, "iconSet"], [24, 15, 17, 13], [24, 18, 17, 16], [24, 22, 17, 16, "createFA5iconSet"], [24, 68, 17, 32], [24, 70, 17, 33, "glyphMap"], [24, 95, 17, 41], [24, 97, 17, 43, "metadata"], [24, 127, 17, 51], [24, 129, 17, 53, "fontMap"], [24, 136, 17, 60], [24, 138, 17, 62], [24, 143, 17, 67], [24, 144, 17, 68], [25, 2, 17, 69], [25, 6, 17, 69, "_default"], [25, 14, 17, 69], [25, 17, 17, 69, "exports"], [25, 24, 17, 69], [25, 25, 17, 69, "default"], [25, 32, 17, 69], [25, 35, 18, 15, "iconSet"], [25, 42, 18, 22], [26, 0, 18, 22], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}