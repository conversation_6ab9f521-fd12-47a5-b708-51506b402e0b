{"dependencies": [{"name": "../../NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 89}}], "key": "TuB5rvhhYFP7S1O2+poQUZyTlqI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var NativeComponentRegistry = _interopRequireWildcard(require(_dependencyMap[0], \"../../NativeComponent/NativeComponentRegistry\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var LayoutConformanceNativeComponent = NativeComponentRegistry.get('LayoutConformance', () => ({\n    uiViewClassName: 'LayoutConformance',\n    validAttributes: {\n      mode: true\n    }\n  }));\n  var _default = exports.default = LayoutConformanceNativeComponent;\n});", "lineCount": 15, "map": [[6, 2, 14, 0], [6, 6, 14, 0, "NativeComponentRegistry"], [6, 29, 14, 0], [6, 32, 14, 0, "_interopRequireWildcard"], [6, 55, 14, 0], [6, 56, 14, 0, "require"], [6, 63, 14, 0], [6, 64, 14, 0, "_dependencyMap"], [6, 78, 14, 0], [7, 2, 14, 89], [7, 11, 14, 89, "_interopRequireWildcard"], [7, 35, 14, 89, "e"], [7, 36, 14, 89], [7, 38, 14, 89, "t"], [7, 39, 14, 89], [7, 68, 14, 89, "WeakMap"], [7, 75, 14, 89], [7, 81, 14, 89, "r"], [7, 82, 14, 89], [7, 89, 14, 89, "WeakMap"], [7, 96, 14, 89], [7, 100, 14, 89, "n"], [7, 101, 14, 89], [7, 108, 14, 89, "WeakMap"], [7, 115, 14, 89], [7, 127, 14, 89, "_interopRequireWildcard"], [7, 150, 14, 89], [7, 162, 14, 89, "_interopRequireWildcard"], [7, 163, 14, 89, "e"], [7, 164, 14, 89], [7, 166, 14, 89, "t"], [7, 167, 14, 89], [7, 176, 14, 89, "t"], [7, 177, 14, 89], [7, 181, 14, 89, "e"], [7, 182, 14, 89], [7, 186, 14, 89, "e"], [7, 187, 14, 89], [7, 188, 14, 89, "__esModule"], [7, 198, 14, 89], [7, 207, 14, 89, "e"], [7, 208, 14, 89], [7, 214, 14, 89, "o"], [7, 215, 14, 89], [7, 217, 14, 89, "i"], [7, 218, 14, 89], [7, 220, 14, 89, "f"], [7, 221, 14, 89], [7, 226, 14, 89, "__proto__"], [7, 235, 14, 89], [7, 243, 14, 89, "default"], [7, 250, 14, 89], [7, 252, 14, 89, "e"], [7, 253, 14, 89], [7, 270, 14, 89, "e"], [7, 271, 14, 89], [7, 294, 14, 89, "e"], [7, 295, 14, 89], [7, 320, 14, 89, "e"], [7, 321, 14, 89], [7, 330, 14, 89, "f"], [7, 331, 14, 89], [7, 337, 14, 89, "o"], [7, 338, 14, 89], [7, 341, 14, 89, "t"], [7, 342, 14, 89], [7, 345, 14, 89, "n"], [7, 346, 14, 89], [7, 349, 14, 89, "r"], [7, 350, 14, 89], [7, 358, 14, 89, "o"], [7, 359, 14, 89], [7, 360, 14, 89, "has"], [7, 363, 14, 89], [7, 364, 14, 89, "e"], [7, 365, 14, 89], [7, 375, 14, 89, "o"], [7, 376, 14, 89], [7, 377, 14, 89, "get"], [7, 380, 14, 89], [7, 381, 14, 89, "e"], [7, 382, 14, 89], [7, 385, 14, 89, "o"], [7, 386, 14, 89], [7, 387, 14, 89, "set"], [7, 390, 14, 89], [7, 391, 14, 89, "e"], [7, 392, 14, 89], [7, 394, 14, 89, "f"], [7, 395, 14, 89], [7, 409, 14, 89, "_t"], [7, 411, 14, 89], [7, 415, 14, 89, "e"], [7, 416, 14, 89], [7, 432, 14, 89, "_t"], [7, 434, 14, 89], [7, 441, 14, 89, "hasOwnProperty"], [7, 455, 14, 89], [7, 456, 14, 89, "call"], [7, 460, 14, 89], [7, 461, 14, 89, "e"], [7, 462, 14, 89], [7, 464, 14, 89, "_t"], [7, 466, 14, 89], [7, 473, 14, 89, "i"], [7, 474, 14, 89], [7, 478, 14, 89, "o"], [7, 479, 14, 89], [7, 482, 14, 89, "Object"], [7, 488, 14, 89], [7, 489, 14, 89, "defineProperty"], [7, 503, 14, 89], [7, 508, 14, 89, "Object"], [7, 514, 14, 89], [7, 515, 14, 89, "getOwnPropertyDescriptor"], [7, 539, 14, 89], [7, 540, 14, 89, "e"], [7, 541, 14, 89], [7, 543, 14, 89, "_t"], [7, 545, 14, 89], [7, 552, 14, 89, "i"], [7, 553, 14, 89], [7, 554, 14, 89, "get"], [7, 557, 14, 89], [7, 561, 14, 89, "i"], [7, 562, 14, 89], [7, 563, 14, 89, "set"], [7, 566, 14, 89], [7, 570, 14, 89, "o"], [7, 571, 14, 89], [7, 572, 14, 89, "f"], [7, 573, 14, 89], [7, 575, 14, 89, "_t"], [7, 577, 14, 89], [7, 579, 14, 89, "i"], [7, 580, 14, 89], [7, 584, 14, 89, "f"], [7, 585, 14, 89], [7, 586, 14, 89, "_t"], [7, 588, 14, 89], [7, 592, 14, 89, "e"], [7, 593, 14, 89], [7, 594, 14, 89, "_t"], [7, 596, 14, 89], [7, 607, 14, 89, "f"], [7, 608, 14, 89], [7, 613, 14, 89, "e"], [7, 614, 14, 89], [7, 616, 14, 89, "t"], [7, 617, 14, 89], [8, 2, 21, 0], [8, 6, 21, 6, "LayoutConformanceNativeComponent"], [8, 38, 21, 60], [8, 41, 22, 2, "NativeComponentRegistry"], [8, 64, 22, 25], [8, 65, 22, 26, "get"], [8, 68, 22, 29], [8, 69, 22, 37], [8, 88, 22, 56], [8, 90, 22, 58], [8, 97, 22, 65], [9, 4, 23, 4, "uiViewClassName"], [9, 19, 23, 19], [9, 21, 23, 21], [9, 40, 23, 40], [10, 4, 24, 4, "validAttributes"], [10, 19, 24, 19], [10, 21, 24, 21], [11, 6, 25, 6, "mode"], [11, 10, 25, 10], [11, 12, 25, 12], [12, 4, 26, 4], [13, 2, 27, 2], [13, 3, 27, 3], [13, 4, 27, 4], [13, 5, 27, 5], [14, 2, 27, 6], [14, 6, 27, 6, "_default"], [14, 14, 27, 6], [14, 17, 27, 6, "exports"], [14, 24, 27, 6], [14, 25, 27, 6, "default"], [14, 32, 27, 6], [14, 35, 29, 15, "LayoutConformanceNativeComponent"], [14, 67, 29, 47], [15, 0, 29, 47], [15, 3]], "functionMap": {"names": ["<global>", "NativeComponentRegistry.get$argument_1"], "mappings": "AAA;0DCqB;IDK"}}, "type": "js/module"}]}