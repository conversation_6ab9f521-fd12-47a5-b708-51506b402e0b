{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SaveLayerFlag = exports.ClipOp = void 0;\n  let ClipOp = exports.ClipOp = /*#__PURE__*/function (ClipOp) {\n    ClipOp[ClipOp[\"Difference\"] = 0] = \"Difference\";\n    ClipOp[ClipOp[\"Intersect\"] = 1] = \"Intersect\";\n    return ClipOp;\n  }({});\n  let SaveLayerFlag = exports.SaveLayerFlag = /*#__PURE__*/function (SaveLayerFlag) {\n    SaveLayerFlag[SaveLayerFlag[\"SaveLayerInitWithPrevious\"] = 4] = \"SaveLayerInitWithPrevious\";\n    SaveLayerFlag[SaveLayerFlag[\"SaveLayerF16ColorType\"] = 16] = \"SaveLayerF16ColorType\";\n    return SaveLayerFlag;\n  }({});\n});", "lineCount": 16, "map": [[6, 2, 1, 7], [6, 6, 1, 11, "ClipOp"], [6, 12, 1, 17], [6, 15, 1, 17, "exports"], [6, 22, 1, 17], [6, 23, 1, 17, "ClipOp"], [6, 29, 1, 17], [6, 32, 1, 20], [6, 45, 1, 33], [6, 55, 1, 43, "ClipOp"], [6, 61, 1, 49], [6, 63, 1, 51], [7, 4, 2, 2, "ClipOp"], [7, 10, 2, 8], [7, 11, 2, 9, "ClipOp"], [7, 17, 2, 15], [7, 18, 2, 16], [7, 30, 2, 28], [7, 31, 2, 29], [7, 34, 2, 32], [7, 35, 2, 33], [7, 36, 2, 34], [7, 39, 2, 37], [7, 51, 2, 49], [8, 4, 3, 2, "ClipOp"], [8, 10, 3, 8], [8, 11, 3, 9, "ClipOp"], [8, 17, 3, 15], [8, 18, 3, 16], [8, 29, 3, 27], [8, 30, 3, 28], [8, 33, 3, 31], [8, 34, 3, 32], [8, 35, 3, 33], [8, 38, 3, 36], [8, 49, 3, 47], [9, 4, 4, 2], [9, 11, 4, 9, "ClipOp"], [9, 17, 4, 15], [10, 2, 5, 0], [10, 3, 5, 1], [10, 4, 5, 2], [10, 5, 5, 3], [10, 6, 5, 4], [10, 7, 5, 5], [11, 2, 6, 7], [11, 6, 6, 11, "SaveLayerFlag"], [11, 19, 6, 24], [11, 22, 6, 24, "exports"], [11, 29, 6, 24], [11, 30, 6, 24, "SaveLayerFlag"], [11, 43, 6, 24], [11, 46, 6, 27], [11, 59, 6, 40], [11, 69, 6, 50, "SaveLayerFlag"], [11, 82, 6, 63], [11, 84, 6, 65], [12, 4, 7, 2, "SaveLayerFlag"], [12, 17, 7, 15], [12, 18, 7, 16, "SaveLayerFlag"], [12, 31, 7, 29], [12, 32, 7, 30], [12, 59, 7, 57], [12, 60, 7, 58], [12, 63, 7, 61], [12, 64, 7, 62], [12, 65, 7, 63], [12, 68, 7, 66], [12, 95, 7, 93], [13, 4, 8, 2, "SaveLayerFlag"], [13, 17, 8, 15], [13, 18, 8, 16, "SaveLayerFlag"], [13, 31, 8, 29], [13, 32, 8, 30], [13, 55, 8, 53], [13, 56, 8, 54], [13, 59, 8, 57], [13, 61, 8, 59], [13, 62, 8, 60], [13, 65, 8, 63], [13, 88, 8, 86], [14, 4, 9, 2], [14, 11, 9, 9, "SaveLayerFlag"], [14, 24, 9, 22], [15, 2, 10, 0], [15, 3, 10, 1], [15, 4, 10, 2], [15, 5, 10, 3], [15, 6, 10, 4], [15, 7, 10, 5], [16, 0, 10, 6], [16, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA,iCC;CDI;wCCC;CDI"}}, "type": "js/module"}]}