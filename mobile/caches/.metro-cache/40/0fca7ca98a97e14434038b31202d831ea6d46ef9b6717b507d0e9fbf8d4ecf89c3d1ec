{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createErrorHandler = createErrorHandler;\n  exports.disableErrorHandling = disableErrorHandling;\n  // Similar interface to the one used in expo modules.\n\n  var isErrorHandlingEnabled = true;\n  var developmentBuildMessage = `If you're trying to use a module that is not supported in Expo Go, you need to create a development build of your app. See https://docs.expo.dev/development/introduction/ for more info.`;\n  function customizeUnavailableMessage(error) {\n    error.message += '\\n\\n' + developmentBuildMessage;\n  }\n  function customizeModuleIsMissingMessage(error) {\n    error.message = `Your JavaScript code tried to access a native module that doesn't exist. \n\n${developmentBuildMessage}`;\n  }\n  function customizeError(error) {\n    if ('code' in error && error.code === 'ERR_UNAVAILABLE') {\n      customizeUnavailableMessage(error);\n    } else if (error.message.includes('Native module cannot be null') ||\n    // RN 0.64 and below message\n    error.message.includes('`new NativeEventEmitter()` requires a non-null argument.') // RN 0.65+ message\n    ) {\n      customizeModuleIsMissingMessage(error);\n    }\n  }\n  function errorHandler(originalHandler, error, isFatal) {\n    if (error instanceof Error) {\n      customizeError(error);\n    }\n    originalHandler(error, isFatal);\n  }\n  function createErrorHandler(originalHandler) {\n    return (error, isFatal) => {\n      if (isErrorHandlingEnabled) {\n        errorHandler(originalHandler, error, isFatal);\n        return;\n      }\n      originalHandler(error, isFatal);\n    };\n  }\n  function disableErrorHandling() {\n    isErrorHandlingEnabled = false;\n  }\n});", "lineCount": 47, "map": [[7, 2, 3, 0], [9, 2, 6, 0], [9, 6, 6, 4, "isErrorHandlingEnabled"], [9, 28, 6, 26], [9, 31, 6, 29], [9, 35, 6, 33], [10, 2, 8, 0], [10, 6, 8, 6, "developmentBuildMessage"], [10, 29, 8, 29], [10, 32, 8, 32], [10, 219, 8, 219], [11, 2, 10, 0], [11, 11, 10, 9, "customizeUnavailableMessage"], [11, 38, 10, 36, "customizeUnavailableMessage"], [11, 39, 10, 37, "error"], [11, 44, 10, 54], [11, 46, 10, 56], [12, 4, 11, 2, "error"], [12, 9, 11, 7], [12, 10, 11, 8, "message"], [12, 17, 11, 15], [12, 21, 11, 19], [12, 27, 11, 25], [12, 30, 11, 28, "developmentBuildMessage"], [12, 53, 11, 51], [13, 2, 12, 0], [14, 2, 14, 0], [14, 11, 14, 9, "customizeModuleIsMissingMessage"], [14, 42, 14, 40, "customizeModuleIsMissingMessage"], [14, 43, 14, 41, "error"], [14, 48, 14, 53], [14, 50, 14, 55], [15, 4, 15, 2, "error"], [15, 9, 15, 7], [15, 10, 15, 8, "message"], [15, 17, 15, 15], [15, 20, 15, 18], [16, 0, 16, 0], [17, 0, 17, 0], [17, 2, 17, 2, "developmentBuildMessage"], [17, 25, 17, 25], [17, 27, 17, 27], [18, 2, 18, 0], [19, 2, 20, 0], [19, 11, 20, 9, "customizeError"], [19, 25, 20, 23, "customizeError"], [19, 26, 20, 24, "error"], [19, 31, 20, 49], [19, 33, 20, 51], [20, 4, 21, 2], [20, 8, 21, 6], [20, 14, 21, 12], [20, 18, 21, 16, "error"], [20, 23, 21, 21], [20, 27, 21, 25, "error"], [20, 32, 21, 30], [20, 33, 21, 31, "code"], [20, 37, 21, 35], [20, 42, 21, 40], [20, 59, 21, 57], [20, 61, 21, 59], [21, 6, 22, 4, "customizeUnavailableMessage"], [21, 33, 22, 31], [21, 34, 22, 32, "error"], [21, 39, 22, 37], [21, 40, 22, 38], [22, 4, 23, 2], [22, 5, 23, 3], [22, 11, 23, 9], [22, 15, 24, 4, "error"], [22, 20, 24, 9], [22, 21, 24, 10, "message"], [22, 28, 24, 17], [22, 29, 24, 18, "includes"], [22, 37, 24, 26], [22, 38, 24, 27], [22, 68, 24, 57], [22, 69, 24, 58], [23, 4, 24, 62], [24, 4, 25, 4, "error"], [24, 9, 25, 9], [24, 10, 25, 10, "message"], [24, 17, 25, 17], [24, 18, 25, 18, "includes"], [24, 26, 25, 26], [24, 27, 25, 27], [24, 85, 25, 85], [24, 86, 25, 86], [24, 87, 25, 87], [25, 4, 25, 87], [25, 6, 26, 4], [26, 6, 27, 4, "customizeModuleIsMissingMessage"], [26, 37, 27, 35], [26, 38, 27, 36, "error"], [26, 43, 27, 41], [26, 44, 27, 42], [27, 4, 28, 2], [28, 2, 29, 0], [29, 2, 31, 0], [29, 11, 31, 9, "<PERSON><PERSON><PERSON><PERSON>"], [29, 23, 31, 21, "<PERSON><PERSON><PERSON><PERSON>"], [29, 24, 31, 22, "<PERSON><PERSON><PERSON><PERSON>"], [29, 39, 31, 59], [29, 41, 31, 61, "error"], [29, 46, 31, 71], [29, 48, 31, 73, "isFatal"], [29, 55, 31, 90], [29, 57, 31, 98], [30, 4, 32, 2], [30, 8, 32, 6, "error"], [30, 13, 32, 11], [30, 25, 32, 23, "Error"], [30, 30, 32, 28], [30, 32, 32, 30], [31, 6, 33, 4, "customizeError"], [31, 20, 33, 18], [31, 21, 33, 19, "error"], [31, 26, 33, 24], [31, 27, 33, 25], [32, 4, 34, 2], [33, 4, 35, 2, "<PERSON><PERSON><PERSON><PERSON>"], [33, 19, 35, 17], [33, 20, 35, 18, "error"], [33, 25, 35, 23], [33, 27, 35, 25, "isFatal"], [33, 34, 35, 32], [33, 35, 35, 33], [34, 2, 36, 0], [35, 2, 38, 7], [35, 11, 38, 16, "createErrorHandler"], [35, 29, 38, 34, "createErrorHandler"], [35, 30, 38, 35, "<PERSON><PERSON><PERSON><PERSON>"], [35, 45, 38, 72], [35, 47, 38, 96], [36, 4, 39, 2], [36, 11, 39, 9], [36, 12, 39, 10, "error"], [36, 17, 39, 15], [36, 19, 39, 17, "isFatal"], [36, 26, 39, 24], [36, 31, 39, 29], [37, 6, 40, 4], [37, 10, 40, 8, "isErrorHandlingEnabled"], [37, 32, 40, 30], [37, 34, 40, 32], [38, 8, 41, 6, "<PERSON><PERSON><PERSON><PERSON>"], [38, 20, 41, 18], [38, 21, 41, 19, "<PERSON><PERSON><PERSON><PERSON>"], [38, 36, 41, 34], [38, 38, 41, 36, "error"], [38, 43, 41, 41], [38, 45, 41, 43, "isFatal"], [38, 52, 41, 50], [38, 53, 41, 51], [39, 8, 42, 6], [40, 6, 43, 4], [41, 6, 45, 4, "<PERSON><PERSON><PERSON><PERSON>"], [41, 21, 45, 19], [41, 22, 45, 20, "error"], [41, 27, 45, 25], [41, 29, 45, 27, "isFatal"], [41, 36, 45, 34], [41, 37, 45, 35], [42, 4, 46, 2], [42, 5, 46, 3], [43, 2, 47, 0], [44, 2, 49, 7], [44, 11, 49, 16, "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [44, 31, 49, 36, "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [44, 32, 49, 36], [44, 34, 49, 39], [45, 4, 50, 2, "isErrorHandlingEnabled"], [45, 26, 50, 24], [45, 29, 50, 27], [45, 34, 50, 32], [46, 2, 51, 0], [47, 0, 51, 1], [47, 3]], "functionMap": {"names": ["<global>", "customizeUnavailableMessage", "customizeModuleIsMissingMessage", "customizeError", "<PERSON><PERSON><PERSON><PERSON>", "createErrorHandler", "<anonymous>", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;ACS;CDE;AEE;CFI;AGE;CHS;AIE;CJK;OKE;SCC;GDO;CLC;OOE;CPE"}}, "type": "js/module"}]}