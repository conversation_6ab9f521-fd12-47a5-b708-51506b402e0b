{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../Dimensions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 225}, "end": {"line": 13, "column": 39, "index": 264}}], "key": "EbYpQpVroIaqKOn2gPUtrpyDwfw=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 265}, "end": {"line": 14, "column": 44, "index": 309}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * @format\n   * \n   */\n\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = useWindowDimensions;\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[1], \"../Dimensions\"));\n  var _react = require(_dependencyMap[2], \"react\");\n  function useWindowDimensions() {\n    var _useState = (0, _react.useState)(() => _Dimensions.default.get('window')),\n      dims = _useState[0],\n      setDims = _useState[1];\n    (0, _react.useEffect)(() => {\n      function handleChange(_ref) {\n        var window = _ref.window;\n        if (window != null) {\n          setDims(window);\n        }\n      }\n      _Dimensions.default.addEventListener('change', handleChange);\n      // We might have missed an update between calling `get` in render and\n      // `addEventListener` in this handler, so we set it here. If there was\n      // no change, React will filter out this update as a no-op.\n      setDims(_Dimensions.default.get('window'));\n      return () => {\n        _Dimensions.default.removeEventListener('change', handleChange);\n      };\n    }, []);\n    return dims;\n  }\n});", "lineCount": 43, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 11, 13], [14, 6, 11, 13, "_interopRequireDefault"], [14, 28, 11, 13], [14, 31, 11, 13, "require"], [14, 38, 11, 13], [14, 39, 11, 13, "_dependencyMap"], [14, 53, 11, 13], [15, 2, 11, 13, "Object"], [15, 8, 11, 13], [15, 9, 11, 13, "defineProperty"], [15, 23, 11, 13], [15, 24, 11, 13, "exports"], [15, 31, 11, 13], [16, 4, 11, 13, "value"], [16, 9, 11, 13], [17, 2, 11, 13], [18, 2, 11, 13, "exports"], [18, 9, 11, 13], [18, 10, 11, 13, "default"], [18, 17, 11, 13], [18, 20, 11, 13, "useWindowDimensions"], [18, 39, 11, 13], [19, 2, 13, 0], [19, 6, 13, 0, "_Dimensions"], [19, 17, 13, 0], [19, 20, 13, 0, "_interopRequireDefault"], [19, 42, 13, 0], [19, 43, 13, 0, "require"], [19, 50, 13, 0], [19, 51, 13, 0, "_dependencyMap"], [19, 65, 13, 0], [20, 2, 14, 0], [20, 6, 14, 0, "_react"], [20, 12, 14, 0], [20, 15, 14, 0, "require"], [20, 22, 14, 0], [20, 23, 14, 0, "_dependencyMap"], [20, 37, 14, 0], [21, 2, 15, 15], [21, 11, 15, 24, "useWindowDimensions"], [21, 30, 15, 43, "useWindowDimensions"], [21, 31, 15, 43], [21, 33, 15, 46], [22, 4, 16, 2], [22, 8, 16, 6, "_useState"], [22, 17, 16, 15], [22, 20, 16, 18], [22, 24, 16, 18, "useState"], [22, 39, 16, 26], [22, 41, 16, 27], [22, 47, 16, 33, "Dimensions"], [22, 66, 16, 43], [22, 67, 16, 44, "get"], [22, 70, 16, 47], [22, 71, 16, 48], [22, 79, 16, 56], [22, 80, 16, 57], [22, 81, 16, 58], [23, 6, 17, 4, "dims"], [23, 10, 17, 8], [23, 13, 17, 11, "_useState"], [23, 22, 17, 20], [23, 23, 17, 21], [23, 24, 17, 22], [23, 25, 17, 23], [24, 6, 18, 4, "setDims"], [24, 13, 18, 11], [24, 16, 18, 14, "_useState"], [24, 25, 18, 23], [24, 26, 18, 24], [24, 27, 18, 25], [24, 28, 18, 26], [25, 4, 19, 2], [25, 8, 19, 2, "useEffect"], [25, 24, 19, 11], [25, 26, 19, 12], [25, 32, 19, 18], [26, 6, 20, 4], [26, 15, 20, 13, "handleChange"], [26, 27, 20, 25, "handleChange"], [26, 28, 20, 26, "_ref"], [26, 32, 20, 30], [26, 34, 20, 32], [27, 8, 21, 6], [27, 12, 21, 10, "window"], [27, 18, 21, 16], [27, 21, 21, 19, "_ref"], [27, 25, 21, 23], [27, 26, 21, 24, "window"], [27, 32, 21, 30], [28, 8, 22, 6], [28, 12, 22, 10, "window"], [28, 18, 22, 16], [28, 22, 22, 20], [28, 26, 22, 24], [28, 28, 22, 26], [29, 10, 23, 8, "setDims"], [29, 17, 23, 15], [29, 18, 23, 16, "window"], [29, 24, 23, 22], [29, 25, 23, 23], [30, 8, 24, 6], [31, 6, 25, 4], [32, 6, 26, 4, "Dimensions"], [32, 25, 26, 14], [32, 26, 26, 15, "addEventListener"], [32, 42, 26, 31], [32, 43, 26, 32], [32, 51, 26, 40], [32, 53, 26, 42, "handleChange"], [32, 65, 26, 54], [32, 66, 26, 55], [33, 6, 27, 4], [34, 6, 28, 4], [35, 6, 29, 4], [36, 6, 30, 4, "setDims"], [36, 13, 30, 11], [36, 14, 30, 12, "Dimensions"], [36, 33, 30, 22], [36, 34, 30, 23, "get"], [36, 37, 30, 26], [36, 38, 30, 27], [36, 46, 30, 35], [36, 47, 30, 36], [36, 48, 30, 37], [37, 6, 31, 4], [37, 13, 31, 11], [37, 19, 31, 17], [38, 8, 32, 6, "Dimensions"], [38, 27, 32, 16], [38, 28, 32, 17, "removeEventListener"], [38, 47, 32, 36], [38, 48, 32, 37], [38, 56, 32, 45], [38, 58, 32, 47, "handleChange"], [38, 70, 32, 59], [38, 71, 32, 60], [39, 6, 33, 4], [39, 7, 33, 5], [40, 4, 34, 2], [40, 5, 34, 3], [40, 7, 34, 5], [40, 9, 34, 7], [40, 10, 34, 8], [41, 4, 35, 2], [41, 11, 35, 9, "dims"], [41, 15, 35, 13], [42, 2, 36, 0], [43, 0, 36, 1], [43, 3]], "functionMap": {"names": ["<global>", "useWindowDimensions", "useState$argument_0", "useEffect$argument_0", "handleChange", "<anonymous>"], "mappings": "AAA;eCc;2BCC,8BD;YEG;ICC;KDK;WEM;KFE;GFC"}}, "type": "js/module"}]}