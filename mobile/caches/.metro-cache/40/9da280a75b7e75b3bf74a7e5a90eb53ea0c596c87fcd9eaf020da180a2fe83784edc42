{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "./DiscreteGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 62, "index": 310}}], "key": "dpOMqAtzFjjUQychI3TYNsYmXQE=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 311}, "end": {"line": 5, "column": 32, "index": 343}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _DiscreteGestureHandler = _interopRequireDefault(require(_dependencyMap[2], \"./DiscreteGestureHandler\"));\n  var _utils = require(_dependencyMap[3], \"./utils\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class TapGestureHandler extends _DiscreteGestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"_shouldFireEndEvent\", null);\n      _defineProperty(this, \"_timer\", void 0);\n      _defineProperty(this, \"_multiTapTimer\", void 0);\n      _defineProperty(this, \"onSuccessfulTap\", ev => {\n        if (this._getPendingGestures().length) {\n          this._shouldFireEndEvent = ev;\n          return;\n        }\n        if (ev.eventType === _hammerjs.default.INPUT_END) {\n          this.sendEvent({\n            ...ev,\n            eventType: _hammerjs.default.INPUT_MOVE\n          });\n        } // When handler gets activated it will turn into State.END immediately.\n\n        this.sendEvent({\n          ...ev,\n          isFinal: true\n        });\n        this.onGestureEnded(ev);\n      });\n    }\n\n    // TODO unused?\n    get name() {\n      return 'tap';\n    }\n    get NativeGestureClass() {\n      return _hammerjs.default.Tap;\n    }\n    get maxDelayMs() {\n      // @ts-ignore TODO(TS) trace down config\n      return (0, _utils.isnan)(this.config.maxDelayMs) ? 300 : this.config.maxDelayMs;\n    }\n    simulateCancelEvent(inputData) {\n      if (this.isGestureRunning) {\n        this.cancelEvent(inputData);\n      }\n    }\n    onGestureActivated(ev) {\n      if (this.isGestureRunning) {\n        this.onSuccessfulTap(ev);\n      }\n    }\n    onRawEvent(ev) {\n      super.onRawEvent(ev); // Attempt to create a touch-down event by checking if a valid tap hasn't started yet, then validating the input.\n\n      if (!this.hasGestureFailed && !this.isGestureRunning &&\n      // Prevent multi-pointer events from misfiring.\n      !ev.isFinal) {\n        // Tap Gesture start event\n        const gesture = this.hammer.get(this.name); // @ts-ignore TODO(TS) trace down config\n\n        if (gesture.options.enable(gesture, ev)) {\n          clearTimeout(this._multiTapTimer);\n          this.onStart(ev);\n          this.sendEvent(ev);\n        }\n      }\n      if (ev.isFinal && ev.maxPointers > 1) {\n        setTimeout(() => {\n          // Handle case where one finger presses slightly\n          // after the first finger on a multi-tap event\n          if (this.isGestureRunning) {\n            this.cancelEvent(ev);\n          }\n        });\n      }\n      if (this.hasGestureFailed) {\n        return;\n      } // Hammer doesn't send a `cancel` event for taps.\n      // Manually fail the event.\n\n      if (ev.isFinal) {\n        // Handle case where one finger presses slightly\n        // after the first finger on a multi-tap event\n        if (ev.maxPointers > 1) {\n          setTimeout(() => {\n            if (this.isGestureRunning) {\n              this.cancelEvent(ev);\n            }\n          });\n        } // Clear last timer\n\n        clearTimeout(this._timer); // Create time out for multi-taps.\n\n        this._timer = setTimeout(() => {\n          this.hasGestureFailed = true;\n          this.cancelEvent(ev);\n        }, this.maxDelayMs);\n      } else if (!this.hasGestureFailed && !this.isGestureRunning) {\n        // Tap Gesture start event\n        const gesture = this.hammer.get(this.name); // @ts-ignore TODO(TS) trace down config\n\n        if (gesture.options.enable(gesture, ev)) {\n          clearTimeout(this._multiTapTimer);\n          this.onStart(ev);\n          this.sendEvent(ev);\n        }\n      }\n    }\n    getHammerConfig() {\n      return {\n        ...super.getHammerConfig(),\n        event: this.name,\n        // @ts-ignore TODO(TS) trace down config\n        taps: (0, _utils.isnan)(this.config.numberOfTaps) ? 1 : this.config.numberOfTaps,\n        interval: this.maxDelayMs,\n        time:\n        // @ts-ignore TODO(TS) trace down config\n        (0, _utils.isnan)(this.config.maxDurationMs) || this.config.maxDurationMs == null ? 250 :\n        // @ts-ignore TODO(TS) trace down config\n        this.config.maxDurationMs\n      };\n    }\n    updateGestureConfig({\n      shouldCancelWhenOutside = true,\n      maxDeltaX = Number.NaN,\n      maxDeltaY = Number.NaN,\n      numberOfTaps = 1,\n      minDurationMs = 525,\n      maxDelayMs = Number.NaN,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars -- TODO possibly forgotten to use in updateGestureConfig?\n      maxDurationMs = Number.NaN,\n      maxDist = 2,\n      minPointers = 1,\n      maxPointers = 1,\n      ...props\n    }) {\n      return super.updateGestureConfig({\n        shouldCancelWhenOutside,\n        numberOfTaps,\n        maxDeltaX,\n        maxDeltaY,\n        minDurationMs,\n        maxDelayMs,\n        maxDist,\n        minPointers,\n        maxPointers,\n        ...props\n      });\n    }\n    onGestureEnded(...props) {\n      clearTimeout(this._timer); // @ts-ignore TODO(TS) check how onGestureEnded works\n\n      super.onGestureEnded(...props);\n    }\n    onWaitingEnded(_gesture) {\n      if (this._shouldFireEndEvent) {\n        this.onSuccessfulTap(this._shouldFireEndEvent);\n        this._shouldFireEndEvent = null;\n      }\n    }\n  }\n  var _default = exports.default = TapGestureHandler;\n});", "lineCount": 181, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_hammerjs"], [7, 15, 3, 0], [7, 18, 3, 0, "_interopRequireDefault"], [7, 40, 3, 0], [7, 41, 3, 0, "require"], [7, 48, 3, 0], [7, 49, 3, 0, "_dependencyMap"], [7, 63, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_DiscreteGestureHandler"], [8, 29, 4, 0], [8, 32, 4, 0, "_interopRequireDefault"], [8, 54, 4, 0], [8, 55, 4, 0, "require"], [8, 62, 4, 0], [8, 63, 4, 0, "_dependencyMap"], [8, 77, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_utils"], [9, 12, 5, 0], [9, 15, 5, 0, "require"], [9, 22, 5, 0], [9, 23, 5, 0, "_dependencyMap"], [9, 37, 5, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "obj"], [10, 30, 1, 28], [10, 32, 1, 30, "key"], [10, 35, 1, 33], [10, 37, 1, 35, "value"], [10, 42, 1, 40], [10, 44, 1, 42], [11, 4, 1, 44], [11, 8, 1, 48, "key"], [11, 11, 1, 51], [11, 15, 1, 55, "obj"], [11, 18, 1, 58], [11, 20, 1, 60], [12, 6, 1, 62, "Object"], [12, 12, 1, 68], [12, 13, 1, 69, "defineProperty"], [12, 27, 1, 83], [12, 28, 1, 84, "obj"], [12, 31, 1, 87], [12, 33, 1, 89, "key"], [12, 36, 1, 92], [12, 38, 1, 94], [13, 8, 1, 96, "value"], [13, 13, 1, 101], [13, 15, 1, 103, "value"], [13, 20, 1, 108], [14, 8, 1, 110, "enumerable"], [14, 18, 1, 120], [14, 20, 1, 122], [14, 24, 1, 126], [15, 8, 1, 128, "configurable"], [15, 20, 1, 140], [15, 22, 1, 142], [15, 26, 1, 146], [16, 8, 1, 148, "writable"], [16, 16, 1, 156], [16, 18, 1, 158], [17, 6, 1, 163], [17, 7, 1, 164], [17, 8, 1, 165], [18, 4, 1, 167], [18, 5, 1, 168], [18, 11, 1, 174], [19, 6, 1, 176, "obj"], [19, 9, 1, 179], [19, 10, 1, 180, "key"], [19, 13, 1, 183], [19, 14, 1, 184], [19, 17, 1, 187, "value"], [19, 22, 1, 192], [20, 4, 1, 194], [21, 4, 1, 196], [21, 11, 1, 203, "obj"], [21, 14, 1, 206], [22, 2, 1, 208], [23, 2, 7, 0], [23, 8, 7, 6, "TapGestureHandler"], [23, 25, 7, 23], [23, 34, 7, 32, "DiscreteGestureHandler"], [23, 65, 7, 54], [23, 66, 7, 55], [24, 4, 8, 2, "constructor"], [24, 15, 8, 13, "constructor"], [24, 16, 8, 14], [24, 19, 8, 17, "args"], [24, 23, 8, 21], [24, 25, 8, 23], [25, 6, 9, 4], [25, 11, 9, 9], [25, 12, 9, 10], [25, 15, 9, 13, "args"], [25, 19, 9, 17], [25, 20, 9, 18], [26, 6, 11, 4, "_defineProperty"], [26, 21, 11, 19], [26, 22, 11, 20], [26, 26, 11, 24], [26, 28, 11, 26], [26, 49, 11, 47], [26, 51, 11, 49], [26, 55, 11, 53], [26, 56, 11, 54], [27, 6, 13, 4, "_defineProperty"], [27, 21, 13, 19], [27, 22, 13, 20], [27, 26, 13, 24], [27, 28, 13, 26], [27, 36, 13, 34], [27, 38, 13, 36], [27, 43, 13, 41], [27, 44, 13, 42], [27, 45, 13, 43], [28, 6, 15, 4, "_defineProperty"], [28, 21, 15, 19], [28, 22, 15, 20], [28, 26, 15, 24], [28, 28, 15, 26], [28, 44, 15, 42], [28, 46, 15, 44], [28, 51, 15, 49], [28, 52, 15, 50], [28, 53, 15, 51], [29, 6, 17, 4, "_defineProperty"], [29, 21, 17, 19], [29, 22, 17, 20], [29, 26, 17, 24], [29, 28, 17, 26], [29, 45, 17, 43], [29, 47, 17, 45, "ev"], [29, 49, 17, 47], [29, 53, 17, 51], [30, 8, 18, 6], [30, 12, 18, 10], [30, 16, 18, 14], [30, 17, 18, 15, "_getPendingGestures"], [30, 36, 18, 34], [30, 37, 18, 35], [30, 38, 18, 36], [30, 39, 18, 37, "length"], [30, 45, 18, 43], [30, 47, 18, 45], [31, 10, 19, 8], [31, 14, 19, 12], [31, 15, 19, 13, "_shouldFireEndEvent"], [31, 34, 19, 32], [31, 37, 19, 35, "ev"], [31, 39, 19, 37], [32, 10, 20, 8], [33, 8, 21, 6], [34, 8, 23, 6], [34, 12, 23, 10, "ev"], [34, 14, 23, 12], [34, 15, 23, 13, "eventType"], [34, 24, 23, 22], [34, 29, 23, 27, "Hammer"], [34, 46, 23, 33], [34, 47, 23, 34, "INPUT_END"], [34, 56, 23, 43], [34, 58, 23, 45], [35, 10, 24, 8], [35, 14, 24, 12], [35, 15, 24, 13, "sendEvent"], [35, 24, 24, 22], [35, 25, 24, 23], [36, 12, 24, 25], [36, 15, 24, 28, "ev"], [36, 17, 24, 30], [37, 12, 25, 10, "eventType"], [37, 21, 25, 19], [37, 23, 25, 21, "Hammer"], [37, 40, 25, 27], [37, 41, 25, 28, "INPUT_MOVE"], [38, 10, 26, 8], [38, 11, 26, 9], [38, 12, 26, 10], [39, 8, 27, 6], [39, 9, 27, 7], [39, 10, 27, 8], [41, 8, 30, 6], [41, 12, 30, 10], [41, 13, 30, 11, "sendEvent"], [41, 22, 30, 20], [41, 23, 30, 21], [42, 10, 30, 23], [42, 13, 30, 26, "ev"], [42, 15, 30, 28], [43, 10, 31, 8, "isFinal"], [43, 17, 31, 15], [43, 19, 31, 17], [44, 8, 32, 6], [44, 9, 32, 7], [44, 10, 32, 8], [45, 8, 33, 6], [45, 12, 33, 10], [45, 13, 33, 11, "onGestureEnded"], [45, 27, 33, 25], [45, 28, 33, 26, "ev"], [45, 30, 33, 28], [45, 31, 33, 29], [46, 6, 34, 4], [46, 7, 34, 5], [46, 8, 34, 6], [47, 4, 35, 2], [49, 4, 37, 2], [50, 4, 38, 2], [50, 8, 38, 6, "name"], [50, 12, 38, 10, "name"], [50, 13, 38, 10], [50, 15, 38, 13], [51, 6, 39, 4], [51, 13, 39, 11], [51, 18, 39, 16], [52, 4, 40, 2], [53, 4, 42, 2], [53, 8, 42, 6, "NativeGestureClass"], [53, 26, 42, 24, "NativeGestureClass"], [53, 27, 42, 24], [53, 29, 42, 27], [54, 6, 43, 4], [54, 13, 43, 11, "Hammer"], [54, 30, 43, 17], [54, 31, 43, 18, "Tap"], [54, 34, 43, 21], [55, 4, 44, 2], [56, 4, 46, 2], [56, 8, 46, 6, "max<PERSON>elay<PERSON>"], [56, 18, 46, 16, "max<PERSON>elay<PERSON>"], [56, 19, 46, 16], [56, 21, 46, 19], [57, 6, 47, 4], [58, 6, 48, 4], [58, 13, 48, 11], [58, 17, 48, 11, "isnan"], [58, 29, 48, 16], [58, 31, 48, 17], [58, 35, 48, 21], [58, 36, 48, 22, "config"], [58, 42, 48, 28], [58, 43, 48, 29, "max<PERSON>elay<PERSON>"], [58, 53, 48, 39], [58, 54, 48, 40], [58, 57, 48, 43], [58, 60, 48, 46], [58, 63, 48, 49], [58, 67, 48, 53], [58, 68, 48, 54, "config"], [58, 74, 48, 60], [58, 75, 48, 61, "max<PERSON>elay<PERSON>"], [58, 85, 48, 71], [59, 4, 49, 2], [60, 4, 51, 2, "simulateCancelEvent"], [60, 23, 51, 21, "simulateCancelEvent"], [60, 24, 51, 22, "inputData"], [60, 33, 51, 31], [60, 35, 51, 33], [61, 6, 52, 4], [61, 10, 52, 8], [61, 14, 52, 12], [61, 15, 52, 13, "isGestureRunning"], [61, 31, 52, 29], [61, 33, 52, 31], [62, 8, 53, 6], [62, 12, 53, 10], [62, 13, 53, 11, "cancelEvent"], [62, 24, 53, 22], [62, 25, 53, 23, "inputData"], [62, 34, 53, 32], [62, 35, 53, 33], [63, 6, 54, 4], [64, 4, 55, 2], [65, 4, 57, 2, "onGestureActivated"], [65, 22, 57, 20, "onGestureActivated"], [65, 23, 57, 21, "ev"], [65, 25, 57, 23], [65, 27, 57, 25], [66, 6, 58, 4], [66, 10, 58, 8], [66, 14, 58, 12], [66, 15, 58, 13, "isGestureRunning"], [66, 31, 58, 29], [66, 33, 58, 31], [67, 8, 59, 6], [67, 12, 59, 10], [67, 13, 59, 11, "onSuccessfulTap"], [67, 28, 59, 26], [67, 29, 59, 27, "ev"], [67, 31, 59, 29], [67, 32, 59, 30], [68, 6, 60, 4], [69, 4, 61, 2], [70, 4, 63, 2, "onRawEvent"], [70, 14, 63, 12, "onRawEvent"], [70, 15, 63, 13, "ev"], [70, 17, 63, 15], [70, 19, 63, 17], [71, 6, 64, 4], [71, 11, 64, 9], [71, 12, 64, 10, "onRawEvent"], [71, 22, 64, 20], [71, 23, 64, 21, "ev"], [71, 25, 64, 23], [71, 26, 64, 24], [71, 27, 64, 25], [71, 28, 64, 26], [73, 6, 66, 4], [73, 10, 66, 8], [73, 11, 66, 9], [73, 15, 66, 13], [73, 16, 66, 14, "hasGestureFailed"], [73, 32, 66, 30], [73, 36, 66, 34], [73, 37, 66, 35], [73, 41, 66, 39], [73, 42, 66, 40, "isGestureRunning"], [73, 58, 66, 56], [74, 6, 66, 60], [75, 6, 67, 4], [75, 7, 67, 5, "ev"], [75, 9, 67, 7], [75, 10, 67, 8, "isFinal"], [75, 17, 67, 15], [75, 19, 67, 17], [76, 8, 68, 6], [77, 8, 69, 6], [77, 14, 69, 12, "gesture"], [77, 21, 69, 19], [77, 24, 69, 22], [77, 28, 69, 26], [77, 29, 69, 27, "hammer"], [77, 35, 69, 33], [77, 36, 69, 34, "get"], [77, 39, 69, 37], [77, 40, 69, 38], [77, 44, 69, 42], [77, 45, 69, 43, "name"], [77, 49, 69, 47], [77, 50, 69, 48], [77, 51, 69, 49], [77, 52, 69, 50], [79, 8, 71, 6], [79, 12, 71, 10, "gesture"], [79, 19, 71, 17], [79, 20, 71, 18, "options"], [79, 27, 71, 25], [79, 28, 71, 26, "enable"], [79, 34, 71, 32], [79, 35, 71, 33, "gesture"], [79, 42, 71, 40], [79, 44, 71, 42, "ev"], [79, 46, 71, 44], [79, 47, 71, 45], [79, 49, 71, 47], [80, 10, 72, 8, "clearTimeout"], [80, 22, 72, 20], [80, 23, 72, 21], [80, 27, 72, 25], [80, 28, 72, 26, "_multiTapTimer"], [80, 42, 72, 40], [80, 43, 72, 41], [81, 10, 73, 8], [81, 14, 73, 12], [81, 15, 73, 13, "onStart"], [81, 22, 73, 20], [81, 23, 73, 21, "ev"], [81, 25, 73, 23], [81, 26, 73, 24], [82, 10, 74, 8], [82, 14, 74, 12], [82, 15, 74, 13, "sendEvent"], [82, 24, 74, 22], [82, 25, 74, 23, "ev"], [82, 27, 74, 25], [82, 28, 74, 26], [83, 8, 75, 6], [84, 6, 76, 4], [85, 6, 78, 4], [85, 10, 78, 8, "ev"], [85, 12, 78, 10], [85, 13, 78, 11, "isFinal"], [85, 20, 78, 18], [85, 24, 78, 22, "ev"], [85, 26, 78, 24], [85, 27, 78, 25, "maxPointers"], [85, 38, 78, 36], [85, 41, 78, 39], [85, 42, 78, 40], [85, 44, 78, 42], [86, 8, 79, 6, "setTimeout"], [86, 18, 79, 16], [86, 19, 79, 17], [86, 25, 79, 23], [87, 10, 80, 8], [88, 10, 81, 8], [89, 10, 82, 8], [89, 14, 82, 12], [89, 18, 82, 16], [89, 19, 82, 17, "isGestureRunning"], [89, 35, 82, 33], [89, 37, 82, 35], [90, 12, 83, 10], [90, 16, 83, 14], [90, 17, 83, 15, "cancelEvent"], [90, 28, 83, 26], [90, 29, 83, 27, "ev"], [90, 31, 83, 29], [90, 32, 83, 30], [91, 10, 84, 8], [92, 8, 85, 6], [92, 9, 85, 7], [92, 10, 85, 8], [93, 6, 86, 4], [94, 6, 88, 4], [94, 10, 88, 8], [94, 14, 88, 12], [94, 15, 88, 13, "hasGestureFailed"], [94, 31, 88, 29], [94, 33, 88, 31], [95, 8, 89, 6], [96, 6, 90, 4], [96, 7, 90, 5], [96, 8, 90, 6], [97, 6, 91, 4], [99, 6, 94, 4], [99, 10, 94, 8, "ev"], [99, 12, 94, 10], [99, 13, 94, 11, "isFinal"], [99, 20, 94, 18], [99, 22, 94, 20], [100, 8, 95, 6], [101, 8, 96, 6], [102, 8, 97, 6], [102, 12, 97, 10, "ev"], [102, 14, 97, 12], [102, 15, 97, 13, "maxPointers"], [102, 26, 97, 24], [102, 29, 97, 27], [102, 30, 97, 28], [102, 32, 97, 30], [103, 10, 98, 8, "setTimeout"], [103, 20, 98, 18], [103, 21, 98, 19], [103, 27, 98, 25], [104, 12, 99, 10], [104, 16, 99, 14], [104, 20, 99, 18], [104, 21, 99, 19, "isGestureRunning"], [104, 37, 99, 35], [104, 39, 99, 37], [105, 14, 100, 12], [105, 18, 100, 16], [105, 19, 100, 17, "cancelEvent"], [105, 30, 100, 28], [105, 31, 100, 29, "ev"], [105, 33, 100, 31], [105, 34, 100, 32], [106, 12, 101, 10], [107, 10, 102, 8], [107, 11, 102, 9], [107, 12, 102, 10], [108, 8, 103, 6], [108, 9, 103, 7], [108, 10, 103, 8], [110, 8, 106, 6, "clearTimeout"], [110, 20, 106, 18], [110, 21, 106, 19], [110, 25, 106, 23], [110, 26, 106, 24, "_timer"], [110, 32, 106, 30], [110, 33, 106, 31], [110, 34, 106, 32], [110, 35, 106, 33], [112, 8, 108, 6], [112, 12, 108, 10], [112, 13, 108, 11, "_timer"], [112, 19, 108, 17], [112, 22, 108, 20, "setTimeout"], [112, 32, 108, 30], [112, 33, 108, 31], [112, 39, 108, 37], [113, 10, 109, 8], [113, 14, 109, 12], [113, 15, 109, 13, "hasGestureFailed"], [113, 31, 109, 29], [113, 34, 109, 32], [113, 38, 109, 36], [114, 10, 110, 8], [114, 14, 110, 12], [114, 15, 110, 13, "cancelEvent"], [114, 26, 110, 24], [114, 27, 110, 25, "ev"], [114, 29, 110, 27], [114, 30, 110, 28], [115, 8, 111, 6], [115, 9, 111, 7], [115, 11, 111, 9], [115, 15, 111, 13], [115, 16, 111, 14, "max<PERSON>elay<PERSON>"], [115, 26, 111, 24], [115, 27, 111, 25], [116, 6, 112, 4], [116, 7, 112, 5], [116, 13, 112, 11], [116, 17, 112, 15], [116, 18, 112, 16], [116, 22, 112, 20], [116, 23, 112, 21, "hasGestureFailed"], [116, 39, 112, 37], [116, 43, 112, 41], [116, 44, 112, 42], [116, 48, 112, 46], [116, 49, 112, 47, "isGestureRunning"], [116, 65, 112, 63], [116, 67, 112, 65], [117, 8, 113, 6], [118, 8, 114, 6], [118, 14, 114, 12, "gesture"], [118, 21, 114, 19], [118, 24, 114, 22], [118, 28, 114, 26], [118, 29, 114, 27, "hammer"], [118, 35, 114, 33], [118, 36, 114, 34, "get"], [118, 39, 114, 37], [118, 40, 114, 38], [118, 44, 114, 42], [118, 45, 114, 43, "name"], [118, 49, 114, 47], [118, 50, 114, 48], [118, 51, 114, 49], [118, 52, 114, 50], [120, 8, 116, 6], [120, 12, 116, 10, "gesture"], [120, 19, 116, 17], [120, 20, 116, 18, "options"], [120, 27, 116, 25], [120, 28, 116, 26, "enable"], [120, 34, 116, 32], [120, 35, 116, 33, "gesture"], [120, 42, 116, 40], [120, 44, 116, 42, "ev"], [120, 46, 116, 44], [120, 47, 116, 45], [120, 49, 116, 47], [121, 10, 117, 8, "clearTimeout"], [121, 22, 117, 20], [121, 23, 117, 21], [121, 27, 117, 25], [121, 28, 117, 26, "_multiTapTimer"], [121, 42, 117, 40], [121, 43, 117, 41], [122, 10, 118, 8], [122, 14, 118, 12], [122, 15, 118, 13, "onStart"], [122, 22, 118, 20], [122, 23, 118, 21, "ev"], [122, 25, 118, 23], [122, 26, 118, 24], [123, 10, 119, 8], [123, 14, 119, 12], [123, 15, 119, 13, "sendEvent"], [123, 24, 119, 22], [123, 25, 119, 23, "ev"], [123, 27, 119, 25], [123, 28, 119, 26], [124, 8, 120, 6], [125, 6, 121, 4], [126, 4, 122, 2], [127, 4, 124, 2, "getHammerConfig"], [127, 19, 124, 17, "getHammerConfig"], [127, 20, 124, 17], [127, 22, 124, 20], [128, 6, 125, 4], [128, 13, 125, 11], [129, 8, 125, 13], [129, 11, 125, 16], [129, 16, 125, 21], [129, 17, 125, 22, "getHammerConfig"], [129, 32, 125, 37], [129, 33, 125, 38], [129, 34, 125, 39], [130, 8, 126, 6, "event"], [130, 13, 126, 11], [130, 15, 126, 13], [130, 19, 126, 17], [130, 20, 126, 18, "name"], [130, 24, 126, 22], [131, 8, 127, 6], [132, 8, 128, 6, "taps"], [132, 12, 128, 10], [132, 14, 128, 12], [132, 18, 128, 12, "isnan"], [132, 30, 128, 17], [132, 32, 128, 18], [132, 36, 128, 22], [132, 37, 128, 23, "config"], [132, 43, 128, 29], [132, 44, 128, 30, "numberOfTaps"], [132, 56, 128, 42], [132, 57, 128, 43], [132, 60, 128, 46], [132, 61, 128, 47], [132, 64, 128, 50], [132, 68, 128, 54], [132, 69, 128, 55, "config"], [132, 75, 128, 61], [132, 76, 128, 62, "numberOfTaps"], [132, 88, 128, 74], [133, 8, 129, 6, "interval"], [133, 16, 129, 14], [133, 18, 129, 16], [133, 22, 129, 20], [133, 23, 129, 21, "max<PERSON>elay<PERSON>"], [133, 33, 129, 31], [134, 8, 130, 6, "time"], [134, 12, 130, 10], [135, 8, 130, 12], [136, 8, 131, 6], [136, 12, 131, 6, "isnan"], [136, 24, 131, 11], [136, 26, 131, 12], [136, 30, 131, 16], [136, 31, 131, 17, "config"], [136, 37, 131, 23], [136, 38, 131, 24, "maxDurationMs"], [136, 51, 131, 37], [136, 52, 131, 38], [136, 56, 131, 42], [136, 60, 131, 46], [136, 61, 131, 47, "config"], [136, 67, 131, 53], [136, 68, 131, 54, "maxDurationMs"], [136, 81, 131, 67], [136, 85, 131, 71], [136, 89, 131, 75], [136, 92, 131, 78], [136, 95, 131, 81], [137, 8, 131, 84], [138, 8, 132, 6], [138, 12, 132, 10], [138, 13, 132, 11, "config"], [138, 19, 132, 17], [138, 20, 132, 18, "maxDurationMs"], [139, 6, 133, 4], [139, 7, 133, 5], [140, 4, 134, 2], [141, 4, 136, 2, "updateGestureConfig"], [141, 23, 136, 21, "updateGestureConfig"], [141, 24, 136, 22], [142, 6, 137, 4, "shouldCancelWhenOutside"], [142, 29, 137, 27], [142, 32, 137, 30], [142, 36, 137, 34], [143, 6, 138, 4, "maxDeltaX"], [143, 15, 138, 13], [143, 18, 138, 16, "Number"], [143, 24, 138, 22], [143, 25, 138, 23, "NaN"], [143, 28, 138, 26], [144, 6, 139, 4, "maxDeltaY"], [144, 15, 139, 13], [144, 18, 139, 16, "Number"], [144, 24, 139, 22], [144, 25, 139, 23, "NaN"], [144, 28, 139, 26], [145, 6, 140, 4, "numberOfTaps"], [145, 18, 140, 16], [145, 21, 140, 19], [145, 22, 140, 20], [146, 6, 141, 4, "minDurationMs"], [146, 19, 141, 17], [146, 22, 141, 20], [146, 25, 141, 23], [147, 6, 142, 4, "max<PERSON>elay<PERSON>"], [147, 16, 142, 14], [147, 19, 142, 17, "Number"], [147, 25, 142, 23], [147, 26, 142, 24, "NaN"], [147, 29, 142, 27], [148, 6, 143, 4], [149, 6, 144, 4, "maxDurationMs"], [149, 19, 144, 17], [149, 22, 144, 20, "Number"], [149, 28, 144, 26], [149, 29, 144, 27, "NaN"], [149, 32, 144, 30], [150, 6, 145, 4, "maxDist"], [150, 13, 145, 11], [150, 16, 145, 14], [150, 17, 145, 15], [151, 6, 146, 4, "minPointers"], [151, 17, 146, 15], [151, 20, 146, 18], [151, 21, 146, 19], [152, 6, 147, 4, "maxPointers"], [152, 17, 147, 15], [152, 20, 147, 18], [152, 21, 147, 19], [153, 6, 148, 4], [153, 9, 148, 7, "props"], [154, 4, 149, 2], [154, 5, 149, 3], [154, 7, 149, 5], [155, 6, 150, 4], [155, 13, 150, 11], [155, 18, 150, 16], [155, 19, 150, 17, "updateGestureConfig"], [155, 38, 150, 36], [155, 39, 150, 37], [156, 8, 151, 6, "shouldCancelWhenOutside"], [156, 31, 151, 29], [157, 8, 152, 6, "numberOfTaps"], [157, 20, 152, 18], [158, 8, 153, 6, "maxDeltaX"], [158, 17, 153, 15], [159, 8, 154, 6, "maxDeltaY"], [159, 17, 154, 15], [160, 8, 155, 6, "minDurationMs"], [160, 21, 155, 19], [161, 8, 156, 6, "max<PERSON>elay<PERSON>"], [161, 18, 156, 16], [162, 8, 157, 6, "maxDist"], [162, 15, 157, 13], [163, 8, 158, 6, "minPointers"], [163, 19, 158, 17], [164, 8, 159, 6, "maxPointers"], [164, 19, 159, 17], [165, 8, 160, 6], [165, 11, 160, 9, "props"], [166, 6, 161, 4], [166, 7, 161, 5], [166, 8, 161, 6], [167, 4, 162, 2], [168, 4, 164, 2, "onGestureEnded"], [168, 18, 164, 16, "onGestureEnded"], [168, 19, 164, 17], [168, 22, 164, 20, "props"], [168, 27, 164, 25], [168, 29, 164, 27], [169, 6, 165, 4, "clearTimeout"], [169, 18, 165, 16], [169, 19, 165, 17], [169, 23, 165, 21], [169, 24, 165, 22, "_timer"], [169, 30, 165, 28], [169, 31, 165, 29], [169, 32, 165, 30], [169, 33, 165, 31], [171, 6, 167, 4], [171, 11, 167, 9], [171, 12, 167, 10, "onGestureEnded"], [171, 26, 167, 24], [171, 27, 167, 25], [171, 30, 167, 28, "props"], [171, 35, 167, 33], [171, 36, 167, 34], [172, 4, 168, 2], [173, 4, 170, 2, "onWaitingEnded"], [173, 18, 170, 16, "onWaitingEnded"], [173, 19, 170, 17, "_gesture"], [173, 27, 170, 25], [173, 29, 170, 27], [174, 6, 171, 4], [174, 10, 171, 8], [174, 14, 171, 12], [174, 15, 171, 13, "_shouldFireEndEvent"], [174, 34, 171, 32], [174, 36, 171, 34], [175, 8, 172, 6], [175, 12, 172, 10], [175, 13, 172, 11, "onSuccessfulTap"], [175, 28, 172, 26], [175, 29, 172, 27], [175, 33, 172, 31], [175, 34, 172, 32, "_shouldFireEndEvent"], [175, 53, 172, 51], [175, 54, 172, 52], [176, 8, 173, 6], [176, 12, 173, 10], [176, 13, 173, 11, "_shouldFireEndEvent"], [176, 32, 173, 30], [176, 35, 173, 33], [176, 39, 173, 37], [177, 6, 174, 4], [178, 4, 175, 2], [179, 2, 177, 0], [180, 2, 177, 1], [180, 6, 177, 1, "_default"], [180, 14, 177, 1], [180, 17, 177, 1, "exports"], [180, 24, 177, 1], [180, 25, 177, 1, "default"], [180, 32, 177, 1], [180, 35, 179, 15, "TapGestureHandler"], [180, 52, 179, 32], [181, 0, 179, 32], [181, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "TapGestureHandler", "constructor", "_defineProperty$argument_2", "get__name", "get__NativeGestureClass", "get__max<PERSON><PERSON><PERSON><PERSON>", "simulateCancelEvent", "onGestureActivated", "onRawEvent", "setTimeout$argument_0", "getHammerConfig", "updateGestureConfig", "onGestureEnded", "onWaitingEnded"], "mappings": "AAA,iNC;ACM;ECC;6CCS;KDiB;GDC;EGG;GHE;EIE;GJE;EKE;GLG;EME;GNI;EOE;GPI;EQE;iBCgB;ODM;mBCa;SDI;+BCM;ODG;GRW;EUE;GVU;EWE;GX0B;EYE;GZI;EaE;GbK;CDE"}}, "type": "js/module"}]}