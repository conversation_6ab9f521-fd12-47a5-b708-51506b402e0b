{"dependencies": [{"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 40, "index": 251}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FlingGesture = void 0;\n  var _gesture = require(_dependencyMap[0], \"./gesture\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class FlingGesture extends _gesture.BaseGesture {\n    constructor() {\n      super();\n      _defineProperty(this, \"config\", {});\n      this.handlerName = 'FlingGestureHandler';\n    }\n    /**\n     * Determine exact number of points required to handle the fling gesture.\n     * @param pointers\n     */\n\n    numberOfPointers(pointers) {\n      this.config.numberOfPointers = pointers;\n      return this;\n    }\n    /**\n     * Expressed allowed direction of movement.\n     * Expected values are exported as constants in the Directions object.\n     * Arguments can be combined using `|` operator. Default value is set to `MouseButton.LEFT`.\n     * @param direction\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/fling-gesture/#directionvalue-directions\n     */\n\n    direction(direction) {\n      this.config.direction = direction;\n      return this;\n    }\n  }\n  exports.FlingGesture = FlingGesture;\n});", "lineCount": 49, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_gesture"], [6, 14, 3, 0], [6, 17, 3, 0, "require"], [6, 24, 3, 0], [6, 25, 3, 0, "_dependencyMap"], [6, 39, 3, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "obj"], [7, 30, 1, 28], [7, 32, 1, 30, "key"], [7, 35, 1, 33], [7, 37, 1, 35, "value"], [7, 42, 1, 40], [7, 44, 1, 42], [8, 4, 1, 44], [8, 8, 1, 48, "key"], [8, 11, 1, 51], [8, 15, 1, 55, "obj"], [8, 18, 1, 58], [8, 20, 1, 60], [9, 6, 1, 62, "Object"], [9, 12, 1, 68], [9, 13, 1, 69, "defineProperty"], [9, 27, 1, 83], [9, 28, 1, 84, "obj"], [9, 31, 1, 87], [9, 33, 1, 89, "key"], [9, 36, 1, 92], [9, 38, 1, 94], [10, 8, 1, 96, "value"], [10, 13, 1, 101], [10, 15, 1, 103, "value"], [10, 20, 1, 108], [11, 8, 1, 110, "enumerable"], [11, 18, 1, 120], [11, 20, 1, 122], [11, 24, 1, 126], [12, 8, 1, 128, "configurable"], [12, 20, 1, 140], [12, 22, 1, 142], [12, 26, 1, 146], [13, 8, 1, 148, "writable"], [13, 16, 1, 156], [13, 18, 1, 158], [14, 6, 1, 163], [14, 7, 1, 164], [14, 8, 1, 165], [15, 4, 1, 167], [15, 5, 1, 168], [15, 11, 1, 174], [16, 6, 1, 176, "obj"], [16, 9, 1, 179], [16, 10, 1, 180, "key"], [16, 13, 1, 183], [16, 14, 1, 184], [16, 17, 1, 187, "value"], [16, 22, 1, 192], [17, 4, 1, 194], [18, 4, 1, 196], [18, 11, 1, 203, "obj"], [18, 14, 1, 206], [19, 2, 1, 208], [20, 2, 4, 7], [20, 8, 4, 13, "FlingGesture"], [20, 20, 4, 25], [20, 29, 4, 34, "BaseGesture"], [20, 49, 4, 45], [20, 50, 4, 46], [21, 4, 5, 2, "constructor"], [21, 15, 5, 13, "constructor"], [21, 16, 5, 13], [21, 18, 5, 16], [22, 6, 6, 4], [22, 11, 6, 9], [22, 12, 6, 10], [22, 13, 6, 11], [23, 6, 8, 4, "_defineProperty"], [23, 21, 8, 19], [23, 22, 8, 20], [23, 26, 8, 24], [23, 28, 8, 26], [23, 36, 8, 34], [23, 38, 8, 36], [23, 39, 8, 37], [23, 40, 8, 38], [23, 41, 8, 39], [24, 6, 10, 4], [24, 10, 10, 8], [24, 11, 10, 9, "handler<PERSON>ame"], [24, 22, 10, 20], [24, 25, 10, 23], [24, 46, 10, 44], [25, 4, 11, 2], [26, 4, 12, 2], [27, 0, 13, 0], [28, 0, 14, 0], [29, 0, 15, 0], [31, 4, 18, 2, "numberOfPointers"], [31, 20, 18, 18, "numberOfPointers"], [31, 21, 18, 19, "pointers"], [31, 29, 18, 27], [31, 31, 18, 29], [32, 6, 19, 4], [32, 10, 19, 8], [32, 11, 19, 9, "config"], [32, 17, 19, 15], [32, 18, 19, 16, "numberOfPointers"], [32, 34, 19, 32], [32, 37, 19, 35, "pointers"], [32, 45, 19, 43], [33, 6, 20, 4], [33, 13, 20, 11], [33, 17, 20, 15], [34, 4, 21, 2], [35, 4, 22, 2], [36, 0, 23, 0], [37, 0, 24, 0], [38, 0, 25, 0], [39, 0, 26, 0], [40, 0, 27, 0], [41, 0, 28, 0], [43, 4, 31, 2, "direction"], [43, 13, 31, 11, "direction"], [43, 14, 31, 12, "direction"], [43, 23, 31, 21], [43, 25, 31, 23], [44, 6, 32, 4], [44, 10, 32, 8], [44, 11, 32, 9, "config"], [44, 17, 32, 15], [44, 18, 32, 16, "direction"], [44, 27, 32, 25], [44, 30, 32, 28, "direction"], [44, 39, 32, 37], [45, 6, 33, 4], [45, 13, 33, 11], [45, 17, 33, 15], [46, 4, 34, 2], [47, 2, 36, 0], [48, 2, 36, 1, "exports"], [48, 9, 36, 1], [48, 10, 36, 1, "FlingGesture"], [48, 22, 36, 1], [48, 25, 36, 1, "FlingGesture"], [48, 37, 36, 1], [49, 0, 36, 1], [49, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "FlingGesture", "FlingGesture#constructor", "FlingGesture#numberOfPointers", "FlingGesture#direction"], "mappings": "AAA,iNC;OCG;ECC;GDM;EEO;GFG;EGU;GHG;CDE"}}, "type": "js/module"}]}