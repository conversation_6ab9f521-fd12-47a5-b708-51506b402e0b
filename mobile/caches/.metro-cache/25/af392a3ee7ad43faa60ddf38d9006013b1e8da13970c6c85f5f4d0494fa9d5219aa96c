{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./DOMRectReadOnly", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 68}}], "key": "w2/y1uifffC4HxGPQqZC/tCvnbg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _DOMRectReadOnly2 = _interopRequireDefault(require(_dependencyMap[6], \"./DOMRectReadOnly\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var DOMRect = exports.default = /*#__PURE__*/function (_DOMRectReadOnly) {\n    function DOMRect() {\n      (0, _classCallCheck2.default)(this, DOMRect);\n      return _callSuper(this, DOMRect, arguments);\n    }\n    (0, _inherits2.default)(DOMRect, _DOMRectReadOnly);\n    return (0, _createClass2.default)(DOMRect, [{\n      key: \"x\",\n      get: function () {\n        return this.__getInternalX();\n      },\n      set: function (x) {\n        this.__setInternalX(x);\n      }\n    }, {\n      key: \"y\",\n      get: function () {\n        return this.__getInternalY();\n      },\n      set: function (y) {\n        this.__setInternalY(y);\n      }\n    }, {\n      key: \"width\",\n      get: function () {\n        return this.__getInternalWidth();\n      },\n      set: function (width) {\n        this.__setInternalWidth(width);\n      }\n    }, {\n      key: \"height\",\n      get: function () {\n        return this.__getInternalHeight();\n      },\n      set: function (height) {\n        this.__setInternalHeight(height);\n      }\n    }], [{\n      key: \"fromRect\",\n      value: function fromRect(rect) {\n        if (!rect) {\n          return new DOMRect();\n        }\n        return new DOMRect(rect.x, rect.y, rect.width, rect.height);\n      }\n    }]);\n  }(_DOMRectReadOnly2.default);\n});", "lineCount": 63, "map": [[12, 2, 17, 0], [12, 6, 17, 0, "_DOMRectReadOnly2"], [12, 23, 17, 0], [12, 26, 17, 0, "_interopRequireDefault"], [12, 48, 17, 0], [12, 49, 17, 0, "require"], [12, 56, 17, 0], [12, 57, 17, 0, "_dependencyMap"], [12, 71, 17, 0], [13, 2, 17, 68], [13, 11, 17, 68, "_callSuper"], [13, 22, 17, 68, "t"], [13, 23, 17, 68], [13, 25, 17, 68, "o"], [13, 26, 17, 68], [13, 28, 17, 68, "e"], [13, 29, 17, 68], [13, 40, 17, 68, "o"], [13, 41, 17, 68], [13, 48, 17, 68, "_getPrototypeOf2"], [13, 64, 17, 68], [13, 65, 17, 68, "default"], [13, 72, 17, 68], [13, 74, 17, 68, "o"], [13, 75, 17, 68], [13, 82, 17, 68, "_possibleConstructorReturn2"], [13, 109, 17, 68], [13, 110, 17, 68, "default"], [13, 117, 17, 68], [13, 119, 17, 68, "t"], [13, 120, 17, 68], [13, 122, 17, 68, "_isNativeReflectConstruct"], [13, 147, 17, 68], [13, 152, 17, 68, "Reflect"], [13, 159, 17, 68], [13, 160, 17, 68, "construct"], [13, 169, 17, 68], [13, 170, 17, 68, "o"], [13, 171, 17, 68], [13, 173, 17, 68, "e"], [13, 174, 17, 68], [13, 186, 17, 68, "_getPrototypeOf2"], [13, 202, 17, 68], [13, 203, 17, 68, "default"], [13, 210, 17, 68], [13, 212, 17, 68, "t"], [13, 213, 17, 68], [13, 215, 17, 68, "constructor"], [13, 226, 17, 68], [13, 230, 17, 68, "o"], [13, 231, 17, 68], [13, 232, 17, 68, "apply"], [13, 237, 17, 68], [13, 238, 17, 68, "t"], [13, 239, 17, 68], [13, 241, 17, 68, "e"], [13, 242, 17, 68], [14, 2, 17, 68], [14, 11, 17, 68, "_isNativeReflectConstruct"], [14, 37, 17, 68], [14, 51, 17, 68, "t"], [14, 52, 17, 68], [14, 56, 17, 68, "Boolean"], [14, 63, 17, 68], [14, 64, 17, 68, "prototype"], [14, 73, 17, 68], [14, 74, 17, 68, "valueOf"], [14, 81, 17, 68], [14, 82, 17, 68, "call"], [14, 86, 17, 68], [14, 87, 17, 68, "Reflect"], [14, 94, 17, 68], [14, 95, 17, 68, "construct"], [14, 104, 17, 68], [14, 105, 17, 68, "Boolean"], [14, 112, 17, 68], [14, 145, 17, 68, "t"], [14, 146, 17, 68], [14, 159, 17, 68, "_isNativeReflectConstruct"], [14, 184, 17, 68], [14, 196, 17, 68, "_isNativeReflectConstruct"], [14, 197, 17, 68], [14, 210, 17, 68, "t"], [14, 211, 17, 68], [15, 2, 17, 68], [15, 6, 27, 21, "DOMRect"], [15, 13, 27, 28], [15, 16, 27, 28, "exports"], [15, 23, 27, 28], [15, 24, 27, 28, "default"], [15, 31, 27, 28], [15, 57, 27, 28, "_DOMRectReadOnly"], [15, 73, 27, 28], [16, 4, 27, 28], [16, 13, 27, 28, "DOMRect"], [16, 21, 27, 28], [17, 6, 27, 28], [17, 10, 27, 28, "_classCallCheck2"], [17, 26, 27, 28], [17, 27, 27, 28, "default"], [17, 34, 27, 28], [17, 42, 27, 28, "DOMRect"], [17, 49, 27, 28], [18, 6, 27, 28], [18, 13, 27, 28, "_callSuper"], [18, 23, 27, 28], [18, 30, 27, 28, "DOMRect"], [18, 37, 27, 28], [18, 39, 27, 28, "arguments"], [18, 48, 27, 28], [19, 4, 27, 28], [20, 4, 27, 28], [20, 8, 27, 28, "_inherits2"], [20, 18, 27, 28], [20, 19, 27, 28, "default"], [20, 26, 27, 28], [20, 28, 27, 28, "DOMRect"], [20, 35, 27, 28], [20, 37, 27, 28, "_DOMRectReadOnly"], [20, 53, 27, 28], [21, 4, 27, 28], [21, 15, 27, 28, "_createClass2"], [21, 28, 27, 28], [21, 29, 27, 28, "default"], [21, 36, 27, 28], [21, 38, 27, 28, "DOMRect"], [21, 45, 27, 28], [22, 6, 27, 28, "key"], [22, 9, 27, 28], [23, 6, 27, 28, "get"], [23, 9, 27, 28], [23, 11, 31, 2], [23, 20, 31, 2, "get"], [23, 21, 31, 2], [23, 23, 31, 18], [24, 8, 32, 4], [24, 15, 32, 11], [24, 19, 32, 15], [24, 20, 32, 16, "__getInternalX"], [24, 34, 32, 30], [24, 35, 32, 31], [24, 36, 32, 32], [25, 6, 33, 2], [25, 7, 33, 3], [26, 6, 33, 3, "set"], [26, 9, 33, 3], [26, 11, 35, 2], [26, 20, 35, 2, "set"], [26, 21, 35, 8, "x"], [26, 22, 35, 18], [26, 24, 35, 20], [27, 8, 36, 4], [27, 12, 36, 8], [27, 13, 36, 9, "__setInternalX"], [27, 27, 36, 23], [27, 28, 36, 24, "x"], [27, 29, 36, 25], [27, 30, 36, 26], [28, 6, 37, 2], [29, 4, 37, 3], [30, 6, 37, 3, "key"], [30, 9, 37, 3], [31, 6, 37, 3, "get"], [31, 9, 37, 3], [31, 11, 42, 2], [31, 20, 42, 2, "get"], [31, 21, 42, 2], [31, 23, 42, 18], [32, 8, 43, 4], [32, 15, 43, 11], [32, 19, 43, 15], [32, 20, 43, 16, "__getInternalY"], [32, 34, 43, 30], [32, 35, 43, 31], [32, 36, 43, 32], [33, 6, 44, 2], [33, 7, 44, 3], [34, 6, 44, 3, "set"], [34, 9, 44, 3], [34, 11, 46, 2], [34, 20, 46, 2, "set"], [34, 21, 46, 8, "y"], [34, 22, 46, 18], [34, 24, 46, 20], [35, 8, 47, 4], [35, 12, 47, 8], [35, 13, 47, 9, "__setInternalY"], [35, 27, 47, 23], [35, 28, 47, 24, "y"], [35, 29, 47, 25], [35, 30, 47, 26], [36, 6, 48, 2], [37, 4, 48, 3], [38, 6, 48, 3, "key"], [38, 9, 48, 3], [39, 6, 48, 3, "get"], [39, 9, 48, 3], [39, 11, 53, 2], [39, 20, 53, 2, "get"], [39, 21, 53, 2], [39, 23, 53, 22], [40, 8, 54, 4], [40, 15, 54, 11], [40, 19, 54, 15], [40, 20, 54, 16, "__getInternalWidth"], [40, 38, 54, 34], [40, 39, 54, 35], [40, 40, 54, 36], [41, 6, 55, 2], [41, 7, 55, 3], [42, 6, 55, 3, "set"], [42, 9, 55, 3], [42, 11, 57, 2], [42, 20, 57, 2, "set"], [42, 21, 57, 12, "width"], [42, 26, 57, 26], [42, 28, 57, 28], [43, 8, 58, 4], [43, 12, 58, 8], [43, 13, 58, 9, "__setInternalWidth"], [43, 31, 58, 27], [43, 32, 58, 28, "width"], [43, 37, 58, 33], [43, 38, 58, 34], [44, 6, 59, 2], [45, 4, 59, 3], [46, 6, 59, 3, "key"], [46, 9, 59, 3], [47, 6, 59, 3, "get"], [47, 9, 59, 3], [47, 11, 64, 2], [47, 20, 64, 2, "get"], [47, 21, 64, 2], [47, 23, 64, 23], [48, 8, 65, 4], [48, 15, 65, 11], [48, 19, 65, 15], [48, 20, 65, 16, "__getInternalHeight"], [48, 39, 65, 35], [48, 40, 65, 36], [48, 41, 65, 37], [49, 6, 66, 2], [49, 7, 66, 3], [50, 6, 66, 3, "set"], [50, 9, 66, 3], [50, 11, 68, 2], [50, 20, 68, 2, "set"], [50, 21, 68, 13, "height"], [50, 27, 68, 28], [50, 29, 68, 30], [51, 8, 69, 4], [51, 12, 69, 8], [51, 13, 69, 9, "__setInternalHeight"], [51, 32, 69, 28], [51, 33, 69, 29, "height"], [51, 39, 69, 35], [51, 40, 69, 36], [52, 6, 70, 2], [53, 4, 70, 3], [54, 6, 70, 3, "key"], [54, 9, 70, 3], [55, 6, 70, 3, "value"], [55, 11, 70, 3], [55, 13, 75, 2], [55, 22, 75, 9, "fromRect"], [55, 30, 75, 17, "fromRect"], [55, 31, 75, 18, "rect"], [55, 35, 75, 37], [55, 37, 75, 48], [56, 8, 76, 4], [56, 12, 76, 8], [56, 13, 76, 9, "rect"], [56, 17, 76, 13], [56, 19, 76, 15], [57, 10, 77, 6], [57, 17, 77, 13], [57, 21, 77, 17, "DOMRect"], [57, 28, 77, 24], [57, 29, 77, 25], [57, 30, 77, 26], [58, 8, 78, 4], [59, 8, 80, 4], [59, 15, 80, 11], [59, 19, 80, 15, "DOMRect"], [59, 26, 80, 22], [59, 27, 80, 23, "rect"], [59, 31, 80, 27], [59, 32, 80, 28, "x"], [59, 33, 80, 29], [59, 35, 80, 31, "rect"], [59, 39, 80, 35], [59, 40, 80, 36, "y"], [59, 41, 80, 37], [59, 43, 80, 39, "rect"], [59, 47, 80, 43], [59, 48, 80, 44, "width"], [59, 53, 80, 49], [59, 55, 80, 51, "rect"], [59, 59, 80, 55], [59, 60, 80, 56, "height"], [59, 66, 80, 62], [59, 67, 80, 63], [60, 6, 81, 2], [61, 4, 81, 3], [62, 2, 81, 3], [62, 4, 27, 37, "DOMRectReadOnly"], [62, 29, 27, 52], [63, 0, 27, 52], [63, 3]], "functionMap": {"names": ["<global>", "DOMRect", "get__x", "set__x", "get__y", "set__y", "get__width", "set__width", "get__height", "set__height", "fromRect"], "mappings": "AAA;eC0B;ECI;GDE;EEE;GFE;EGK;GHE;EIE;GJE;EKK;GLE;EME;GNE;EOK;GPE;EQE;GRE;ESK;GTM"}}, "type": "js/module"}]}