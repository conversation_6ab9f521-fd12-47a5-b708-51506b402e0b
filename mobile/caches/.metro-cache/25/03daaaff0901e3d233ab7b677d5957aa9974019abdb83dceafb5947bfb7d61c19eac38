{"dependencies": [{"name": "whatwg-fetch", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 23}}], "key": "P64/yKB0111O6dCR+gTr9kSWky0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.fetch = exports.Response = exports.Request = exports.Headers = void 0;\n  require(_dependencyMap[0], \"whatwg-fetch\");\n  var fetch = exports.fetch = global.fetch;\n  var Headers = exports.Headers = global.Headers;\n  var Request = exports.Request = global.Request;\n  var Response = exports.Response = global.Response;\n});", "lineCount": 13, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "fetch"], [7, 15, 11, 13], [7, 18, 11, 13, "exports"], [7, 25, 11, 13], [7, 26, 11, 13, "Response"], [7, 34, 11, 13], [7, 37, 11, 13, "exports"], [7, 44, 11, 13], [7, 45, 11, 13, "Request"], [7, 52, 11, 13], [7, 55, 11, 13, "exports"], [7, 62, 11, 13], [7, 63, 11, 13, "Headers"], [7, 70, 11, 13], [8, 2, 15, 0, "require"], [8, 9, 15, 7], [8, 10, 15, 7, "_dependencyMap"], [8, 24, 15, 7], [8, 43, 15, 22], [8, 44, 15, 23], [9, 2, 17, 7], [9, 6, 17, 13, "fetch"], [9, 11, 17, 18], [9, 14, 17, 18, "exports"], [9, 21, 17, 18], [9, 22, 17, 18, "fetch"], [9, 27, 17, 18], [9, 30, 17, 21, "global"], [9, 36, 17, 27], [9, 37, 17, 28, "fetch"], [9, 42, 17, 33], [10, 2, 18, 7], [10, 6, 18, 13, "Headers"], [10, 13, 18, 20], [10, 16, 18, 20, "exports"], [10, 23, 18, 20], [10, 24, 18, 20, "Headers"], [10, 31, 18, 20], [10, 34, 18, 23, "global"], [10, 40, 18, 29], [10, 41, 18, 30, "Headers"], [10, 48, 18, 37], [11, 2, 19, 7], [11, 6, 19, 13, "Request"], [11, 13, 19, 20], [11, 16, 19, 20, "exports"], [11, 23, 19, 20], [11, 24, 19, 20, "Request"], [11, 31, 19, 20], [11, 34, 19, 23, "global"], [11, 40, 19, 29], [11, 41, 19, 30, "Request"], [11, 48, 19, 37], [12, 2, 20, 7], [12, 6, 20, 13, "Response"], [12, 14, 20, 21], [12, 17, 20, 21, "exports"], [12, 24, 20, 21], [12, 25, 20, 21, "Response"], [12, 33, 20, 21], [12, 36, 20, 24, "global"], [12, 42, 20, 30], [12, 43, 20, 31, "Response"], [12, 51, 20, 39], [13, 0, 20, 40], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}