{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "j9sUgJL2drnBoAedJuo4/l2ILqw=", "exportNames": ["*"]}}, {"name": "./gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 65, "index": 110}}], "key": "M3YJtGPnWOlAL/cGsCkMRGpSLhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PanGestureHandler = void 0;\n  exports.managePanProps = managePanProps;\n  exports.panHandlerName = exports.panGestureHandlerProps = exports.panGestureHandlerCustomNativeProps = void 0;\n  var _createHandler = _interopRequireDefault(require(_dependencyMap[1], \"./createHandler\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"./gestureHandlerCommon\");\n  const panGestureHandlerProps = exports.panGestureHandlerProps = ['activeOffsetY', 'activeOffsetX', 'failOffsetY', 'failOffsetX', 'minDist', 'minVelocity', 'minVelocityX', 'minVelocityY', 'minPointers', 'maxPointers', 'avgTouches', 'enableTrackpadTwoFingerGesture', 'activateAfterLongPress'];\n  const panGestureHandlerCustomNativeProps = exports.panGestureHandlerCustomNativeProps = ['activeOffsetYStart', 'activeOffsetYEnd', 'activeOffsetXStart', 'activeOffsetXEnd', 'failOffsetYStart', 'failOffsetYEnd', 'failOffsetXStart', 'failOffsetXEnd'];\n  const panHandlerName = exports.panHandlerName = 'PanGestureHandler';\n  /**\n   * @deprecated PanGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pan()` instead.\n   */\n\n  /**\n   * @deprecated PanGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pan()` instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\n  const PanGestureHandler = exports.PanGestureHandler = (0, _createHandler.default)({\n    name: panHandlerName,\n    allowedProps: [..._gestureHandlerCommon.baseGestureHandlerProps, ...panGestureHandlerProps],\n    config: {},\n    transformProps: managePanProps,\n    customNativeProps: panGestureHandlerCustomNativeProps\n  });\n  function validatePanGestureHandlerProps(props) {\n    if (Array.isArray(props.activeOffsetX) && (props.activeOffsetX[0] > 0 || props.activeOffsetX[1] < 0)) {\n      throw new Error(`First element of activeOffsetX should be negative, a the second one should be positive`);\n    }\n    if (Array.isArray(props.activeOffsetY) && (props.activeOffsetY[0] > 0 || props.activeOffsetY[1] < 0)) {\n      throw new Error(`First element of activeOffsetY should be negative, a the second one should be positive`);\n    }\n    if (Array.isArray(props.failOffsetX) && (props.failOffsetX[0] > 0 || props.failOffsetX[1] < 0)) {\n      throw new Error(`First element of failOffsetX should be negative, a the second one should be positive`);\n    }\n    if (Array.isArray(props.failOffsetY) && (props.failOffsetY[0] > 0 || props.failOffsetY[1] < 0)) {\n      throw new Error(`First element of failOffsetY should be negative, a the second one should be positive`);\n    }\n    if (props.minDist && (props.failOffsetX || props.failOffsetY)) {\n      throw new Error(`It is not supported to use minDist with failOffsetX or failOffsetY, use activeOffsetX and activeOffsetY instead`);\n    }\n    if (props.minDist && (props.activeOffsetX || props.activeOffsetY)) {\n      throw new Error(`It is not supported to use minDist with activeOffsetX or activeOffsetY`);\n    }\n  }\n  function transformPanGestureHandlerProps(props) {\n    const res = {\n      ...props\n    };\n    if (props.activeOffsetX !== undefined) {\n      delete res.activeOffsetX;\n      if (Array.isArray(props.activeOffsetX)) {\n        res.activeOffsetXStart = props.activeOffsetX[0];\n        res.activeOffsetXEnd = props.activeOffsetX[1];\n      } else if (props.activeOffsetX < 0) {\n        res.activeOffsetXStart = props.activeOffsetX;\n      } else {\n        res.activeOffsetXEnd = props.activeOffsetX;\n      }\n    }\n    if (props.activeOffsetY !== undefined) {\n      delete res.activeOffsetY;\n      if (Array.isArray(props.activeOffsetY)) {\n        res.activeOffsetYStart = props.activeOffsetY[0];\n        res.activeOffsetYEnd = props.activeOffsetY[1];\n      } else if (props.activeOffsetY < 0) {\n        res.activeOffsetYStart = props.activeOffsetY;\n      } else {\n        res.activeOffsetYEnd = props.activeOffsetY;\n      }\n    }\n    if (props.failOffsetX !== undefined) {\n      delete res.failOffsetX;\n      if (Array.isArray(props.failOffsetX)) {\n        res.failOffsetXStart = props.failOffsetX[0];\n        res.failOffsetXEnd = props.failOffsetX[1];\n      } else if (props.failOffsetX < 0) {\n        res.failOffsetXStart = props.failOffsetX;\n      } else {\n        res.failOffsetXEnd = props.failOffsetX;\n      }\n    }\n    if (props.failOffsetY !== undefined) {\n      delete res.failOffsetY;\n      if (Array.isArray(props.failOffsetY)) {\n        res.failOffsetYStart = props.failOffsetY[0];\n        res.failOffsetYEnd = props.failOffsetY[1];\n      } else if (props.failOffsetY < 0) {\n        res.failOffsetYStart = props.failOffsetY;\n      } else {\n        res.failOffsetYEnd = props.failOffsetY;\n      }\n    }\n    return res;\n  }\n  function managePanProps(props) {\n    if (__DEV__) {\n      validatePanGestureHandlerProps(props);\n    }\n    return transformPanGestureHandlerProps(props);\n  }\n});", "lineCount": 105, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_createHandler"], [9, 20, 1, 0], [9, 23, 1, 0, "_interopRequireDefault"], [9, 45, 1, 0], [9, 46, 1, 0, "require"], [9, 53, 1, 0], [9, 54, 1, 0, "_dependencyMap"], [9, 68, 1, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 27, 2, 0], [10, 30, 2, 0, "require"], [10, 37, 2, 0], [10, 38, 2, 0, "_dependencyMap"], [10, 52, 2, 0], [11, 2, 3, 7], [11, 8, 3, 13, "panGestureHandlerProps"], [11, 30, 3, 35], [11, 33, 3, 35, "exports"], [11, 40, 3, 35], [11, 41, 3, 35, "panGestureHandlerProps"], [11, 63, 3, 35], [11, 66, 3, 38], [11, 67, 3, 39], [11, 82, 3, 54], [11, 84, 3, 56], [11, 99, 3, 71], [11, 101, 3, 73], [11, 114, 3, 86], [11, 116, 3, 88], [11, 129, 3, 101], [11, 131, 3, 103], [11, 140, 3, 112], [11, 142, 3, 114], [11, 155, 3, 127], [11, 157, 3, 129], [11, 171, 3, 143], [11, 173, 3, 145], [11, 187, 3, 159], [11, 189, 3, 161], [11, 202, 3, 174], [11, 204, 3, 176], [11, 217, 3, 189], [11, 219, 3, 191], [11, 231, 3, 203], [11, 233, 3, 205], [11, 265, 3, 237], [11, 267, 3, 239], [11, 291, 3, 263], [11, 292, 3, 264], [12, 2, 4, 7], [12, 8, 4, 13, "panGestureHandlerCustomNativeProps"], [12, 42, 4, 47], [12, 45, 4, 47, "exports"], [12, 52, 4, 47], [12, 53, 4, 47, "panGestureHandlerCustomNativeProps"], [12, 87, 4, 47], [12, 90, 4, 50], [12, 91, 4, 51], [12, 111, 4, 71], [12, 113, 4, 73], [12, 131, 4, 91], [12, 133, 4, 93], [12, 153, 4, 113], [12, 155, 4, 115], [12, 173, 4, 133], [12, 175, 4, 135], [12, 193, 4, 153], [12, 195, 4, 155], [12, 211, 4, 171], [12, 213, 4, 173], [12, 231, 4, 191], [12, 233, 4, 193], [12, 249, 4, 209], [12, 250, 4, 210], [13, 2, 5, 7], [13, 8, 5, 13, "panHandlerName"], [13, 22, 5, 27], [13, 25, 5, 27, "exports"], [13, 32, 5, 27], [13, 33, 5, 27, "panHandlerName"], [13, 47, 5, 27], [13, 50, 5, 30], [13, 69, 5, 49], [14, 2, 6, 0], [15, 0, 7, 0], [16, 0, 8, 0], [18, 2, 10, 0], [19, 0, 11, 0], [20, 0, 12, 0], [21, 2, 13, 0], [22, 2, 14, 7], [22, 8, 14, 13, "PanGestureHandler"], [22, 25, 14, 30], [22, 28, 14, 30, "exports"], [22, 35, 14, 30], [22, 36, 14, 30, "PanGestureHandler"], [22, 53, 14, 30], [22, 56, 14, 33], [22, 60, 14, 33, "createHandler"], [22, 82, 14, 46], [22, 84, 14, 47], [23, 4, 15, 2, "name"], [23, 8, 15, 6], [23, 10, 15, 8, "panHandlerName"], [23, 24, 15, 22], [24, 4, 16, 2, "allowedProps"], [24, 16, 16, 14], [24, 18, 16, 16], [24, 19, 16, 17], [24, 22, 16, 20, "baseGestureHandlerProps"], [24, 67, 16, 43], [24, 69, 16, 45], [24, 72, 16, 48, "panGestureHandlerProps"], [24, 94, 16, 70], [24, 95, 16, 71], [25, 4, 17, 2, "config"], [25, 10, 17, 8], [25, 12, 17, 10], [25, 13, 17, 11], [25, 14, 17, 12], [26, 4, 18, 2, "transformProps"], [26, 18, 18, 16], [26, 20, 18, 18, "managePanProps"], [26, 34, 18, 32], [27, 4, 19, 2, "customNativeProps"], [27, 21, 19, 19], [27, 23, 19, 21, "panGestureHandlerCustomNativeProps"], [28, 2, 20, 0], [28, 3, 20, 1], [28, 4, 20, 2], [29, 2, 22, 0], [29, 11, 22, 9, "validatePanGestureHandlerProps"], [29, 41, 22, 39, "validatePanGestureHandlerProps"], [29, 42, 22, 40, "props"], [29, 47, 22, 45], [29, 49, 22, 47], [30, 4, 23, 2], [30, 8, 23, 6, "Array"], [30, 13, 23, 11], [30, 14, 23, 12, "isArray"], [30, 21, 23, 19], [30, 22, 23, 20, "props"], [30, 27, 23, 25], [30, 28, 23, 26, "activeOffsetX"], [30, 41, 23, 39], [30, 42, 23, 40], [30, 47, 23, 45, "props"], [30, 52, 23, 50], [30, 53, 23, 51, "activeOffsetX"], [30, 66, 23, 64], [30, 67, 23, 65], [30, 68, 23, 66], [30, 69, 23, 67], [30, 72, 23, 70], [30, 73, 23, 71], [30, 77, 23, 75, "props"], [30, 82, 23, 80], [30, 83, 23, 81, "activeOffsetX"], [30, 96, 23, 94], [30, 97, 23, 95], [30, 98, 23, 96], [30, 99, 23, 97], [30, 102, 23, 100], [30, 103, 23, 101], [30, 104, 23, 102], [30, 106, 23, 104], [31, 6, 24, 4], [31, 12, 24, 10], [31, 16, 24, 14, "Error"], [31, 21, 24, 19], [31, 22, 24, 20], [31, 110, 24, 108], [31, 111, 24, 109], [32, 4, 25, 2], [33, 4, 27, 2], [33, 8, 27, 6, "Array"], [33, 13, 27, 11], [33, 14, 27, 12, "isArray"], [33, 21, 27, 19], [33, 22, 27, 20, "props"], [33, 27, 27, 25], [33, 28, 27, 26, "activeOffsetY"], [33, 41, 27, 39], [33, 42, 27, 40], [33, 47, 27, 45, "props"], [33, 52, 27, 50], [33, 53, 27, 51, "activeOffsetY"], [33, 66, 27, 64], [33, 67, 27, 65], [33, 68, 27, 66], [33, 69, 27, 67], [33, 72, 27, 70], [33, 73, 27, 71], [33, 77, 27, 75, "props"], [33, 82, 27, 80], [33, 83, 27, 81, "activeOffsetY"], [33, 96, 27, 94], [33, 97, 27, 95], [33, 98, 27, 96], [33, 99, 27, 97], [33, 102, 27, 100], [33, 103, 27, 101], [33, 104, 27, 102], [33, 106, 27, 104], [34, 6, 28, 4], [34, 12, 28, 10], [34, 16, 28, 14, "Error"], [34, 21, 28, 19], [34, 22, 28, 20], [34, 110, 28, 108], [34, 111, 28, 109], [35, 4, 29, 2], [36, 4, 31, 2], [36, 8, 31, 6, "Array"], [36, 13, 31, 11], [36, 14, 31, 12, "isArray"], [36, 21, 31, 19], [36, 22, 31, 20, "props"], [36, 27, 31, 25], [36, 28, 31, 26, "failOffsetX"], [36, 39, 31, 37], [36, 40, 31, 38], [36, 45, 31, 43, "props"], [36, 50, 31, 48], [36, 51, 31, 49, "failOffsetX"], [36, 62, 31, 60], [36, 63, 31, 61], [36, 64, 31, 62], [36, 65, 31, 63], [36, 68, 31, 66], [36, 69, 31, 67], [36, 73, 31, 71, "props"], [36, 78, 31, 76], [36, 79, 31, 77, "failOffsetX"], [36, 90, 31, 88], [36, 91, 31, 89], [36, 92, 31, 90], [36, 93, 31, 91], [36, 96, 31, 94], [36, 97, 31, 95], [36, 98, 31, 96], [36, 100, 31, 98], [37, 6, 32, 4], [37, 12, 32, 10], [37, 16, 32, 14, "Error"], [37, 21, 32, 19], [37, 22, 32, 20], [37, 108, 32, 106], [37, 109, 32, 107], [38, 4, 33, 2], [39, 4, 35, 2], [39, 8, 35, 6, "Array"], [39, 13, 35, 11], [39, 14, 35, 12, "isArray"], [39, 21, 35, 19], [39, 22, 35, 20, "props"], [39, 27, 35, 25], [39, 28, 35, 26, "failOffsetY"], [39, 39, 35, 37], [39, 40, 35, 38], [39, 45, 35, 43, "props"], [39, 50, 35, 48], [39, 51, 35, 49, "failOffsetY"], [39, 62, 35, 60], [39, 63, 35, 61], [39, 64, 35, 62], [39, 65, 35, 63], [39, 68, 35, 66], [39, 69, 35, 67], [39, 73, 35, 71, "props"], [39, 78, 35, 76], [39, 79, 35, 77, "failOffsetY"], [39, 90, 35, 88], [39, 91, 35, 89], [39, 92, 35, 90], [39, 93, 35, 91], [39, 96, 35, 94], [39, 97, 35, 95], [39, 98, 35, 96], [39, 100, 35, 98], [40, 6, 36, 4], [40, 12, 36, 10], [40, 16, 36, 14, "Error"], [40, 21, 36, 19], [40, 22, 36, 20], [40, 108, 36, 106], [40, 109, 36, 107], [41, 4, 37, 2], [42, 4, 39, 2], [42, 8, 39, 6, "props"], [42, 13, 39, 11], [42, 14, 39, 12, "minDist"], [42, 21, 39, 19], [42, 26, 39, 24, "props"], [42, 31, 39, 29], [42, 32, 39, 30, "failOffsetX"], [42, 43, 39, 41], [42, 47, 39, 45, "props"], [42, 52, 39, 50], [42, 53, 39, 51, "failOffsetY"], [42, 64, 39, 62], [42, 65, 39, 63], [42, 67, 39, 65], [43, 6, 40, 4], [43, 12, 40, 10], [43, 16, 40, 14, "Error"], [43, 21, 40, 19], [43, 22, 40, 20], [43, 135, 40, 133], [43, 136, 40, 134], [44, 4, 41, 2], [45, 4, 43, 2], [45, 8, 43, 6, "props"], [45, 13, 43, 11], [45, 14, 43, 12, "minDist"], [45, 21, 43, 19], [45, 26, 43, 24, "props"], [45, 31, 43, 29], [45, 32, 43, 30, "activeOffsetX"], [45, 45, 43, 43], [45, 49, 43, 47, "props"], [45, 54, 43, 52], [45, 55, 43, 53, "activeOffsetY"], [45, 68, 43, 66], [45, 69, 43, 67], [45, 71, 43, 69], [46, 6, 44, 4], [46, 12, 44, 10], [46, 16, 44, 14, "Error"], [46, 21, 44, 19], [46, 22, 44, 20], [46, 94, 44, 92], [46, 95, 44, 93], [47, 4, 45, 2], [48, 2, 46, 0], [49, 2, 48, 0], [49, 11, 48, 9, "transformPanGestureHandlerProps"], [49, 42, 48, 40, "transformPanGestureHandlerProps"], [49, 43, 48, 41, "props"], [49, 48, 48, 46], [49, 50, 48, 48], [50, 4, 49, 2], [50, 10, 49, 8, "res"], [50, 13, 49, 11], [50, 16, 49, 14], [51, 6, 49, 16], [51, 9, 49, 19, "props"], [52, 4, 50, 2], [52, 5, 50, 3], [53, 4, 52, 2], [53, 8, 52, 6, "props"], [53, 13, 52, 11], [53, 14, 52, 12, "activeOffsetX"], [53, 27, 52, 25], [53, 32, 52, 30, "undefined"], [53, 41, 52, 39], [53, 43, 52, 41], [54, 6, 53, 4], [54, 13, 53, 11, "res"], [54, 16, 53, 14], [54, 17, 53, 15, "activeOffsetX"], [54, 30, 53, 28], [55, 6, 55, 4], [55, 10, 55, 8, "Array"], [55, 15, 55, 13], [55, 16, 55, 14, "isArray"], [55, 23, 55, 21], [55, 24, 55, 22, "props"], [55, 29, 55, 27], [55, 30, 55, 28, "activeOffsetX"], [55, 43, 55, 41], [55, 44, 55, 42], [55, 46, 55, 44], [56, 8, 56, 6, "res"], [56, 11, 56, 9], [56, 12, 56, 10, "activeOffsetXStart"], [56, 30, 56, 28], [56, 33, 56, 31, "props"], [56, 38, 56, 36], [56, 39, 56, 37, "activeOffsetX"], [56, 52, 56, 50], [56, 53, 56, 51], [56, 54, 56, 52], [56, 55, 56, 53], [57, 8, 57, 6, "res"], [57, 11, 57, 9], [57, 12, 57, 10, "activeOffsetXEnd"], [57, 28, 57, 26], [57, 31, 57, 29, "props"], [57, 36, 57, 34], [57, 37, 57, 35, "activeOffsetX"], [57, 50, 57, 48], [57, 51, 57, 49], [57, 52, 57, 50], [57, 53, 57, 51], [58, 6, 58, 4], [58, 7, 58, 5], [58, 13, 58, 11], [58, 17, 58, 15, "props"], [58, 22, 58, 20], [58, 23, 58, 21, "activeOffsetX"], [58, 36, 58, 34], [58, 39, 58, 37], [58, 40, 58, 38], [58, 42, 58, 40], [59, 8, 59, 6, "res"], [59, 11, 59, 9], [59, 12, 59, 10, "activeOffsetXStart"], [59, 30, 59, 28], [59, 33, 59, 31, "props"], [59, 38, 59, 36], [59, 39, 59, 37, "activeOffsetX"], [59, 52, 59, 50], [60, 6, 60, 4], [60, 7, 60, 5], [60, 13, 60, 11], [61, 8, 61, 6, "res"], [61, 11, 61, 9], [61, 12, 61, 10, "activeOffsetXEnd"], [61, 28, 61, 26], [61, 31, 61, 29, "props"], [61, 36, 61, 34], [61, 37, 61, 35, "activeOffsetX"], [61, 50, 61, 48], [62, 6, 62, 4], [63, 4, 63, 2], [64, 4, 65, 2], [64, 8, 65, 6, "props"], [64, 13, 65, 11], [64, 14, 65, 12, "activeOffsetY"], [64, 27, 65, 25], [64, 32, 65, 30, "undefined"], [64, 41, 65, 39], [64, 43, 65, 41], [65, 6, 66, 4], [65, 13, 66, 11, "res"], [65, 16, 66, 14], [65, 17, 66, 15, "activeOffsetY"], [65, 30, 66, 28], [66, 6, 68, 4], [66, 10, 68, 8, "Array"], [66, 15, 68, 13], [66, 16, 68, 14, "isArray"], [66, 23, 68, 21], [66, 24, 68, 22, "props"], [66, 29, 68, 27], [66, 30, 68, 28, "activeOffsetY"], [66, 43, 68, 41], [66, 44, 68, 42], [66, 46, 68, 44], [67, 8, 69, 6, "res"], [67, 11, 69, 9], [67, 12, 69, 10, "activeOffsetYStart"], [67, 30, 69, 28], [67, 33, 69, 31, "props"], [67, 38, 69, 36], [67, 39, 69, 37, "activeOffsetY"], [67, 52, 69, 50], [67, 53, 69, 51], [67, 54, 69, 52], [67, 55, 69, 53], [68, 8, 70, 6, "res"], [68, 11, 70, 9], [68, 12, 70, 10, "activeOffsetYEnd"], [68, 28, 70, 26], [68, 31, 70, 29, "props"], [68, 36, 70, 34], [68, 37, 70, 35, "activeOffsetY"], [68, 50, 70, 48], [68, 51, 70, 49], [68, 52, 70, 50], [68, 53, 70, 51], [69, 6, 71, 4], [69, 7, 71, 5], [69, 13, 71, 11], [69, 17, 71, 15, "props"], [69, 22, 71, 20], [69, 23, 71, 21, "activeOffsetY"], [69, 36, 71, 34], [69, 39, 71, 37], [69, 40, 71, 38], [69, 42, 71, 40], [70, 8, 72, 6, "res"], [70, 11, 72, 9], [70, 12, 72, 10, "activeOffsetYStart"], [70, 30, 72, 28], [70, 33, 72, 31, "props"], [70, 38, 72, 36], [70, 39, 72, 37, "activeOffsetY"], [70, 52, 72, 50], [71, 6, 73, 4], [71, 7, 73, 5], [71, 13, 73, 11], [72, 8, 74, 6, "res"], [72, 11, 74, 9], [72, 12, 74, 10, "activeOffsetYEnd"], [72, 28, 74, 26], [72, 31, 74, 29, "props"], [72, 36, 74, 34], [72, 37, 74, 35, "activeOffsetY"], [72, 50, 74, 48], [73, 6, 75, 4], [74, 4, 76, 2], [75, 4, 78, 2], [75, 8, 78, 6, "props"], [75, 13, 78, 11], [75, 14, 78, 12, "failOffsetX"], [75, 25, 78, 23], [75, 30, 78, 28, "undefined"], [75, 39, 78, 37], [75, 41, 78, 39], [76, 6, 79, 4], [76, 13, 79, 11, "res"], [76, 16, 79, 14], [76, 17, 79, 15, "failOffsetX"], [76, 28, 79, 26], [77, 6, 81, 4], [77, 10, 81, 8, "Array"], [77, 15, 81, 13], [77, 16, 81, 14, "isArray"], [77, 23, 81, 21], [77, 24, 81, 22, "props"], [77, 29, 81, 27], [77, 30, 81, 28, "failOffsetX"], [77, 41, 81, 39], [77, 42, 81, 40], [77, 44, 81, 42], [78, 8, 82, 6, "res"], [78, 11, 82, 9], [78, 12, 82, 10, "failOffsetXStart"], [78, 28, 82, 26], [78, 31, 82, 29, "props"], [78, 36, 82, 34], [78, 37, 82, 35, "failOffsetX"], [78, 48, 82, 46], [78, 49, 82, 47], [78, 50, 82, 48], [78, 51, 82, 49], [79, 8, 83, 6, "res"], [79, 11, 83, 9], [79, 12, 83, 10, "failOffsetXEnd"], [79, 26, 83, 24], [79, 29, 83, 27, "props"], [79, 34, 83, 32], [79, 35, 83, 33, "failOffsetX"], [79, 46, 83, 44], [79, 47, 83, 45], [79, 48, 83, 46], [79, 49, 83, 47], [80, 6, 84, 4], [80, 7, 84, 5], [80, 13, 84, 11], [80, 17, 84, 15, "props"], [80, 22, 84, 20], [80, 23, 84, 21, "failOffsetX"], [80, 34, 84, 32], [80, 37, 84, 35], [80, 38, 84, 36], [80, 40, 84, 38], [81, 8, 85, 6, "res"], [81, 11, 85, 9], [81, 12, 85, 10, "failOffsetXStart"], [81, 28, 85, 26], [81, 31, 85, 29, "props"], [81, 36, 85, 34], [81, 37, 85, 35, "failOffsetX"], [81, 48, 85, 46], [82, 6, 86, 4], [82, 7, 86, 5], [82, 13, 86, 11], [83, 8, 87, 6, "res"], [83, 11, 87, 9], [83, 12, 87, 10, "failOffsetXEnd"], [83, 26, 87, 24], [83, 29, 87, 27, "props"], [83, 34, 87, 32], [83, 35, 87, 33, "failOffsetX"], [83, 46, 87, 44], [84, 6, 88, 4], [85, 4, 89, 2], [86, 4, 91, 2], [86, 8, 91, 6, "props"], [86, 13, 91, 11], [86, 14, 91, 12, "failOffsetY"], [86, 25, 91, 23], [86, 30, 91, 28, "undefined"], [86, 39, 91, 37], [86, 41, 91, 39], [87, 6, 92, 4], [87, 13, 92, 11, "res"], [87, 16, 92, 14], [87, 17, 92, 15, "failOffsetY"], [87, 28, 92, 26], [88, 6, 94, 4], [88, 10, 94, 8, "Array"], [88, 15, 94, 13], [88, 16, 94, 14, "isArray"], [88, 23, 94, 21], [88, 24, 94, 22, "props"], [88, 29, 94, 27], [88, 30, 94, 28, "failOffsetY"], [88, 41, 94, 39], [88, 42, 94, 40], [88, 44, 94, 42], [89, 8, 95, 6, "res"], [89, 11, 95, 9], [89, 12, 95, 10, "failOffsetYStart"], [89, 28, 95, 26], [89, 31, 95, 29, "props"], [89, 36, 95, 34], [89, 37, 95, 35, "failOffsetY"], [89, 48, 95, 46], [89, 49, 95, 47], [89, 50, 95, 48], [89, 51, 95, 49], [90, 8, 96, 6, "res"], [90, 11, 96, 9], [90, 12, 96, 10, "failOffsetYEnd"], [90, 26, 96, 24], [90, 29, 96, 27, "props"], [90, 34, 96, 32], [90, 35, 96, 33, "failOffsetY"], [90, 46, 96, 44], [90, 47, 96, 45], [90, 48, 96, 46], [90, 49, 96, 47], [91, 6, 97, 4], [91, 7, 97, 5], [91, 13, 97, 11], [91, 17, 97, 15, "props"], [91, 22, 97, 20], [91, 23, 97, 21, "failOffsetY"], [91, 34, 97, 32], [91, 37, 97, 35], [91, 38, 97, 36], [91, 40, 97, 38], [92, 8, 98, 6, "res"], [92, 11, 98, 9], [92, 12, 98, 10, "failOffsetYStart"], [92, 28, 98, 26], [92, 31, 98, 29, "props"], [92, 36, 98, 34], [92, 37, 98, 35, "failOffsetY"], [92, 48, 98, 46], [93, 6, 99, 4], [93, 7, 99, 5], [93, 13, 99, 11], [94, 8, 100, 6, "res"], [94, 11, 100, 9], [94, 12, 100, 10, "failOffsetYEnd"], [94, 26, 100, 24], [94, 29, 100, 27, "props"], [94, 34, 100, 32], [94, 35, 100, 33, "failOffsetY"], [94, 46, 100, 44], [95, 6, 101, 4], [96, 4, 102, 2], [97, 4, 104, 2], [97, 11, 104, 9, "res"], [97, 14, 104, 12], [98, 2, 105, 0], [99, 2, 107, 7], [99, 11, 107, 16, "managePanProps"], [99, 25, 107, 30, "managePanProps"], [99, 26, 107, 31, "props"], [99, 31, 107, 36], [99, 33, 107, 38], [100, 4, 108, 2], [100, 8, 108, 6, "__DEV__"], [100, 15, 108, 13], [100, 17, 108, 15], [101, 6, 109, 4, "validatePanGestureHandlerProps"], [101, 36, 109, 34], [101, 37, 109, 35, "props"], [101, 42, 109, 40], [101, 43, 109, 41], [102, 4, 110, 2], [103, 4, 112, 2], [103, 11, 112, 9, "transformPanGestureHandlerProps"], [103, 42, 112, 40], [103, 43, 112, 41, "props"], [103, 48, 112, 46], [103, 49, 112, 47], [104, 2, 113, 0], [105, 0, 113, 1], [105, 3]], "functionMap": {"names": ["<global>", "validatePanGestureHandlerProps", "transformPanGestureHandlerProps", "managePanProps"], "mappings": "AAA;ACqB;CDwB;AEE;CFyD;OGE;CHM"}}, "type": "js/module"}]}