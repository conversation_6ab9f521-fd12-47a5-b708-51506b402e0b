{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.resolveHref = void 0;\n  exports.resolveHrefStringWithSegments = resolveHrefStringWithSegments;\n  /** Resolve an href object into a fully qualified, relative href. */\n  const resolveHref = href => {\n    if (typeof href === 'string') {\n      return (0, exports.resolveHref)({\n        pathname: href\n      });\n    }\n    const path = href.pathname ?? '';\n    if (!href?.params) {\n      return path;\n    }\n    const {\n      pathname,\n      params\n    } = createQualifiedPathname(path, {\n      ...href.params\n    });\n    const paramsString = createQueryParams(params);\n    return pathname + (paramsString ? `?${paramsString}` : '');\n  };\n  exports.resolveHref = resolveHref;\n  function resolveHrefStringWithSegments(href, {\n    segments = [],\n    params = {}\n  } = {}, {\n    relativeToDirectory\n  } = {}) {\n    if (href.startsWith('.')) {\n      // Resolve base path by merging the current segments with the params\n      let base = segments?.map(segment => {\n        if (!segment.startsWith('[')) return segment;\n        if (segment.startsWith('[...')) {\n          segment = segment.slice(4, -1);\n          const param = params[segment];\n          if (Array.isArray(param)) {\n            return param.join('/');\n          } else {\n            return param?.split(',')?.join('/') ?? '';\n          }\n        } else {\n          segment = segment.slice(1, -1);\n          return params[segment];\n        }\n      }).filter(Boolean).join('/') ?? '/';\n      if (relativeToDirectory) {\n        base = `${base}/`;\n      }\n      const url = new URL(href, `http://hostname/${base}`);\n      href = `${url.pathname}${url.search}`;\n    }\n    return href;\n  }\n  function createQualifiedPathname(pathname, params) {\n    for (const [key, value = ''] of Object.entries(params)) {\n      const dynamicKey = `[${key}]`;\n      const deepDynamicKey = `[...${key}]`;\n      if (pathname.includes(dynamicKey)) {\n        pathname = pathname.replace(dynamicKey, encodeParam(value));\n      } else if (pathname.includes(deepDynamicKey)) {\n        pathname = pathname.replace(deepDynamicKey, encodeParam(value));\n      } else {\n        continue;\n      }\n      delete params[key];\n    }\n    return {\n      pathname,\n      params\n    };\n  }\n  function encodeParam(param) {\n    if (Array.isArray(param)) {\n      return param.map(p => encodeParam(p)).join('/');\n    }\n    return encodeURIComponent(param.toString());\n  }\n  function createQueryParams(params) {\n    return Object.entries(params)\n    // Allow nullish params\n    .filter(([, value]) => value != null).map(([key, value]) => `${key}=${encodeURIComponent(value.toString())}`).join('&');\n  }\n});", "lineCount": 90, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "resolveHref"], [7, 21, 3, 19], [7, 24, 3, 22], [7, 29, 3, 27], [7, 30, 3, 28], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "resolveHrefStringWithSegments"], [8, 39, 4, 37], [8, 42, 4, 40, "resolveHrefStringWithSegments"], [8, 71, 4, 69], [9, 2, 5, 0], [10, 2, 6, 0], [10, 8, 6, 6, "resolveHref"], [10, 19, 6, 17], [10, 22, 6, 21, "href"], [10, 26, 6, 25], [10, 30, 6, 30], [11, 4, 7, 4], [11, 8, 7, 8], [11, 15, 7, 15, "href"], [11, 19, 7, 19], [11, 24, 7, 24], [11, 32, 7, 32], [11, 34, 7, 34], [12, 6, 8, 8], [12, 13, 8, 15], [12, 14, 8, 16], [12, 15, 8, 17], [12, 17, 8, 19, "exports"], [12, 24, 8, 26], [12, 25, 8, 27, "resolveHref"], [12, 36, 8, 38], [12, 38, 8, 40], [13, 8, 8, 42, "pathname"], [13, 16, 8, 50], [13, 18, 8, 52, "href"], [14, 6, 8, 57], [14, 7, 8, 58], [14, 8, 8, 59], [15, 4, 9, 4], [16, 4, 10, 4], [16, 10, 10, 10, "path"], [16, 14, 10, 14], [16, 17, 10, 17, "href"], [16, 21, 10, 21], [16, 22, 10, 22, "pathname"], [16, 30, 10, 30], [16, 34, 10, 34], [16, 36, 10, 36], [17, 4, 11, 4], [17, 8, 11, 8], [17, 9, 11, 9, "href"], [17, 13, 11, 13], [17, 15, 11, 15, "params"], [17, 21, 11, 21], [17, 23, 11, 23], [18, 6, 12, 8], [18, 13, 12, 15, "path"], [18, 17, 12, 19], [19, 4, 13, 4], [20, 4, 14, 4], [20, 10, 14, 10], [21, 6, 14, 12, "pathname"], [21, 14, 14, 20], [22, 6, 14, 22, "params"], [23, 4, 14, 29], [23, 5, 14, 30], [23, 8, 14, 33, "createQualifiedPathname"], [23, 31, 14, 56], [23, 32, 14, 57, "path"], [23, 36, 14, 61], [23, 38, 14, 63], [24, 6, 15, 8], [24, 9, 15, 11, "href"], [24, 13, 15, 15], [24, 14, 15, 16, "params"], [25, 4, 16, 4], [25, 5, 16, 5], [25, 6, 16, 6], [26, 4, 17, 4], [26, 10, 17, 10, "paramsString"], [26, 22, 17, 22], [26, 25, 17, 25, "createQueryParams"], [26, 42, 17, 42], [26, 43, 17, 43, "params"], [26, 49, 17, 49], [26, 50, 17, 50], [27, 4, 18, 4], [27, 11, 18, 11, "pathname"], [27, 19, 18, 19], [27, 23, 18, 23, "paramsString"], [27, 35, 18, 35], [27, 38, 18, 38], [27, 42, 18, 42, "paramsString"], [27, 54, 18, 54], [27, 56, 18, 56], [27, 59, 18, 59], [27, 61, 18, 61], [27, 62, 18, 62], [28, 2, 19, 0], [28, 3, 19, 1], [29, 2, 20, 0, "exports"], [29, 9, 20, 7], [29, 10, 20, 8, "resolveHref"], [29, 21, 20, 19], [29, 24, 20, 22, "resolveHref"], [29, 35, 20, 33], [30, 2, 21, 0], [30, 11, 21, 9, "resolveHrefStringWithSegments"], [30, 40, 21, 38, "resolveHrefStringWithSegments"], [30, 41, 21, 39, "href"], [30, 45, 21, 43], [30, 47, 21, 45], [31, 4, 21, 47, "segments"], [31, 12, 21, 55], [31, 15, 21, 58], [31, 17, 21, 60], [32, 4, 21, 62, "params"], [32, 10, 21, 68], [32, 13, 21, 71], [32, 14, 21, 72], [33, 2, 21, 74], [33, 3, 21, 75], [33, 6, 21, 78], [33, 7, 21, 79], [33, 8, 21, 80], [33, 10, 21, 82], [34, 4, 21, 84, "relativeToDirectory"], [35, 2, 21, 104], [35, 3, 21, 105], [35, 6, 21, 108], [35, 7, 21, 109], [35, 8, 21, 110], [35, 10, 21, 112], [36, 4, 22, 4], [36, 8, 22, 8, "href"], [36, 12, 22, 12], [36, 13, 22, 13, "startsWith"], [36, 23, 22, 23], [36, 24, 22, 24], [36, 27, 22, 27], [36, 28, 22, 28], [36, 30, 22, 30], [37, 6, 23, 8], [38, 6, 24, 8], [38, 10, 24, 12, "base"], [38, 14, 24, 16], [38, 17, 24, 19, "segments"], [38, 25, 24, 27], [38, 27, 25, 14, "map"], [38, 30, 25, 17], [38, 31, 25, 19, "segment"], [38, 38, 25, 26], [38, 42, 25, 31], [39, 8, 26, 12], [39, 12, 26, 16], [39, 13, 26, 17, "segment"], [39, 20, 26, 24], [39, 21, 26, 25, "startsWith"], [39, 31, 26, 35], [39, 32, 26, 36], [39, 35, 26, 39], [39, 36, 26, 40], [39, 38, 27, 16], [39, 45, 27, 23, "segment"], [39, 52, 27, 30], [40, 8, 28, 12], [40, 12, 28, 16, "segment"], [40, 19, 28, 23], [40, 20, 28, 24, "startsWith"], [40, 30, 28, 34], [40, 31, 28, 35], [40, 37, 28, 41], [40, 38, 28, 42], [40, 40, 28, 44], [41, 10, 29, 16, "segment"], [41, 17, 29, 23], [41, 20, 29, 26, "segment"], [41, 27, 29, 33], [41, 28, 29, 34, "slice"], [41, 33, 29, 39], [41, 34, 29, 40], [41, 35, 29, 41], [41, 37, 29, 43], [41, 38, 29, 44], [41, 39, 29, 45], [41, 40, 29, 46], [42, 10, 30, 16], [42, 16, 30, 22, "param"], [42, 21, 30, 27], [42, 24, 30, 30, "params"], [42, 30, 30, 36], [42, 31, 30, 37, "segment"], [42, 38, 30, 44], [42, 39, 30, 45], [43, 10, 31, 16], [43, 14, 31, 20, "Array"], [43, 19, 31, 25], [43, 20, 31, 26, "isArray"], [43, 27, 31, 33], [43, 28, 31, 34, "param"], [43, 33, 31, 39], [43, 34, 31, 40], [43, 36, 31, 42], [44, 12, 32, 20], [44, 19, 32, 27, "param"], [44, 24, 32, 32], [44, 25, 32, 33, "join"], [44, 29, 32, 37], [44, 30, 32, 38], [44, 33, 32, 41], [44, 34, 32, 42], [45, 10, 33, 16], [45, 11, 33, 17], [45, 17, 34, 21], [46, 12, 35, 20], [46, 19, 35, 27, "param"], [46, 24, 35, 32], [46, 26, 35, 34, "split"], [46, 31, 35, 39], [46, 32, 35, 40], [46, 35, 35, 43], [46, 36, 35, 44], [46, 38, 35, 46, "join"], [46, 42, 35, 50], [46, 43, 35, 51], [46, 46, 35, 54], [46, 47, 35, 55], [46, 51, 35, 59], [46, 53, 35, 61], [47, 10, 36, 16], [48, 8, 37, 12], [48, 9, 37, 13], [48, 15, 38, 17], [49, 10, 39, 16, "segment"], [49, 17, 39, 23], [49, 20, 39, 26, "segment"], [49, 27, 39, 33], [49, 28, 39, 34, "slice"], [49, 33, 39, 39], [49, 34, 39, 40], [49, 35, 39, 41], [49, 37, 39, 43], [49, 38, 39, 44], [49, 39, 39, 45], [49, 40, 39, 46], [50, 10, 40, 16], [50, 17, 40, 23, "params"], [50, 23, 40, 29], [50, 24, 40, 30, "segment"], [50, 31, 40, 37], [50, 32, 40, 38], [51, 8, 41, 12], [52, 6, 42, 8], [52, 7, 42, 9], [52, 8, 42, 10], [52, 9, 43, 13, "filter"], [52, 15, 43, 19], [52, 16, 43, 20, "Boolean"], [52, 23, 43, 27], [52, 24, 43, 28], [52, 25, 44, 13, "join"], [52, 29, 44, 17], [52, 30, 44, 18], [52, 33, 44, 21], [52, 34, 44, 22], [52, 38, 44, 26], [52, 41, 44, 29], [53, 6, 45, 8], [53, 10, 45, 12, "relativeToDirectory"], [53, 29, 45, 31], [53, 31, 45, 33], [54, 8, 46, 12, "base"], [54, 12, 46, 16], [54, 15, 46, 19], [54, 18, 46, 22, "base"], [54, 22, 46, 26], [54, 25, 46, 29], [55, 6, 47, 8], [56, 6, 48, 8], [56, 12, 48, 14, "url"], [56, 15, 48, 17], [56, 18, 48, 20], [56, 22, 48, 24, "URL"], [56, 25, 48, 27], [56, 26, 48, 28, "href"], [56, 30, 48, 32], [56, 32, 48, 34], [56, 51, 48, 53, "base"], [56, 55, 48, 57], [56, 57, 48, 59], [56, 58, 48, 60], [57, 6, 49, 8, "href"], [57, 10, 49, 12], [57, 13, 49, 15], [57, 16, 49, 18, "url"], [57, 19, 49, 21], [57, 20, 49, 22, "pathname"], [57, 28, 49, 30], [57, 31, 49, 33, "url"], [57, 34, 49, 36], [57, 35, 49, 37, "search"], [57, 41, 49, 43], [57, 43, 49, 45], [58, 4, 50, 4], [59, 4, 51, 4], [59, 11, 51, 11, "href"], [59, 15, 51, 15], [60, 2, 52, 0], [61, 2, 53, 0], [61, 11, 53, 9, "createQualifiedPathname"], [61, 34, 53, 32, "createQualifiedPathname"], [61, 35, 53, 33, "pathname"], [61, 43, 53, 41], [61, 45, 53, 43, "params"], [61, 51, 53, 49], [61, 53, 53, 51], [62, 4, 54, 4], [62, 9, 54, 9], [62, 15, 54, 15], [62, 16, 54, 16, "key"], [62, 19, 54, 19], [62, 21, 54, 21, "value"], [62, 26, 54, 26], [62, 29, 54, 29], [62, 31, 54, 31], [62, 32, 54, 32], [62, 36, 54, 36, "Object"], [62, 42, 54, 42], [62, 43, 54, 43, "entries"], [62, 50, 54, 50], [62, 51, 54, 51, "params"], [62, 57, 54, 57], [62, 58, 54, 58], [62, 60, 54, 60], [63, 6, 55, 8], [63, 12, 55, 14, "<PERSON><PERSON><PERSON>"], [63, 22, 55, 24], [63, 25, 55, 27], [63, 29, 55, 31, "key"], [63, 32, 55, 34], [63, 35, 55, 37], [64, 6, 56, 8], [64, 12, 56, 14, "deepDynamicKey"], [64, 26, 56, 28], [64, 29, 56, 31], [64, 36, 56, 38, "key"], [64, 39, 56, 41], [64, 42, 56, 44], [65, 6, 57, 8], [65, 10, 57, 12, "pathname"], [65, 18, 57, 20], [65, 19, 57, 21, "includes"], [65, 27, 57, 29], [65, 28, 57, 30, "<PERSON><PERSON><PERSON>"], [65, 38, 57, 40], [65, 39, 57, 41], [65, 41, 57, 43], [66, 8, 58, 12, "pathname"], [66, 16, 58, 20], [66, 19, 58, 23, "pathname"], [66, 27, 58, 31], [66, 28, 58, 32, "replace"], [66, 35, 58, 39], [66, 36, 58, 40, "<PERSON><PERSON><PERSON>"], [66, 46, 58, 50], [66, 48, 58, 52, "encodeParam"], [66, 59, 58, 63], [66, 60, 58, 64, "value"], [66, 65, 58, 69], [66, 66, 58, 70], [66, 67, 58, 71], [67, 6, 59, 8], [67, 7, 59, 9], [67, 13, 60, 13], [67, 17, 60, 17, "pathname"], [67, 25, 60, 25], [67, 26, 60, 26, "includes"], [67, 34, 60, 34], [67, 35, 60, 35, "deepDynamicKey"], [67, 49, 60, 49], [67, 50, 60, 50], [67, 52, 60, 52], [68, 8, 61, 12, "pathname"], [68, 16, 61, 20], [68, 19, 61, 23, "pathname"], [68, 27, 61, 31], [68, 28, 61, 32, "replace"], [68, 35, 61, 39], [68, 36, 61, 40, "deepDynamicKey"], [68, 50, 61, 54], [68, 52, 61, 56, "encodeParam"], [68, 63, 61, 67], [68, 64, 61, 68, "value"], [68, 69, 61, 73], [68, 70, 61, 74], [68, 71, 61, 75], [69, 6, 62, 8], [69, 7, 62, 9], [69, 13, 63, 13], [70, 8, 64, 12], [71, 6, 65, 8], [72, 6, 66, 8], [72, 13, 66, 15, "params"], [72, 19, 66, 21], [72, 20, 66, 22, "key"], [72, 23, 66, 25], [72, 24, 66, 26], [73, 4, 67, 4], [74, 4, 68, 4], [74, 11, 68, 11], [75, 6, 68, 13, "pathname"], [75, 14, 68, 21], [76, 6, 68, 23, "params"], [77, 4, 68, 30], [77, 5, 68, 31], [78, 2, 69, 0], [79, 2, 70, 0], [79, 11, 70, 9, "encodeParam"], [79, 22, 70, 20, "encodeParam"], [79, 23, 70, 21, "param"], [79, 28, 70, 26], [79, 30, 70, 28], [80, 4, 71, 4], [80, 8, 71, 8, "Array"], [80, 13, 71, 13], [80, 14, 71, 14, "isArray"], [80, 21, 71, 21], [80, 22, 71, 22, "param"], [80, 27, 71, 27], [80, 28, 71, 28], [80, 30, 71, 30], [81, 6, 72, 8], [81, 13, 72, 15, "param"], [81, 18, 72, 20], [81, 19, 72, 21, "map"], [81, 22, 72, 24], [81, 23, 72, 26, "p"], [81, 24, 72, 27], [81, 28, 72, 32, "encodeParam"], [81, 39, 72, 43], [81, 40, 72, 44, "p"], [81, 41, 72, 45], [81, 42, 72, 46], [81, 43, 72, 47], [81, 44, 72, 48, "join"], [81, 48, 72, 52], [81, 49, 72, 53], [81, 52, 72, 56], [81, 53, 72, 57], [82, 4, 73, 4], [83, 4, 74, 4], [83, 11, 74, 11, "encodeURIComponent"], [83, 29, 74, 29], [83, 30, 74, 30, "param"], [83, 35, 74, 35], [83, 36, 74, 36, "toString"], [83, 44, 74, 44], [83, 45, 74, 45], [83, 46, 74, 46], [83, 47, 74, 47], [84, 2, 75, 0], [85, 2, 76, 0], [85, 11, 76, 9, "createQueryParams"], [85, 28, 76, 26, "createQueryParams"], [85, 29, 76, 27, "params"], [85, 35, 76, 33], [85, 37, 76, 35], [86, 4, 77, 4], [86, 11, 77, 12, "Object"], [86, 17, 77, 18], [86, 18, 77, 19, "entries"], [86, 25, 77, 26], [86, 26, 77, 27, "params"], [86, 32, 77, 33], [87, 4, 78, 8], [88, 4, 78, 8], [88, 5, 79, 9, "filter"], [88, 11, 79, 15], [88, 12, 79, 16], [88, 13, 79, 17], [88, 16, 79, 20, "value"], [88, 21, 79, 25], [88, 22, 79, 26], [88, 27, 79, 31, "value"], [88, 32, 79, 36], [88, 36, 79, 40], [88, 40, 79, 44], [88, 41, 79, 45], [88, 42, 80, 9, "map"], [88, 45, 80, 12], [88, 46, 80, 13], [88, 47, 80, 14], [88, 48, 80, 15, "key"], [88, 51, 80, 18], [88, 53, 80, 20, "value"], [88, 58, 80, 25], [88, 59, 80, 26], [88, 64, 80, 31], [88, 67, 80, 34, "key"], [88, 70, 80, 37], [88, 74, 80, 41, "encodeURIComponent"], [88, 92, 80, 59], [88, 93, 80, 60, "value"], [88, 98, 80, 65], [88, 99, 80, 66, "toString"], [88, 107, 80, 74], [88, 108, 80, 75], [88, 109, 80, 76], [88, 110, 80, 77], [88, 112, 80, 79], [88, 113, 80, 80], [88, 114, 81, 9, "join"], [88, 118, 81, 13], [88, 119, 81, 14], [88, 122, 81, 17], [88, 123, 81, 18], [89, 2, 82, 0], [90, 0, 82, 1], [90, 3]], "functionMap": {"names": ["<global>", "resolveHref", "resolveHrefStringWithSegments", "segments.map$argument_0", "createQualifiedPathname", "encodeParam", "param.map$argument_0", "createQueryParams", "Object.entries.filter$argument_0", "Object.entries.filter.map$argument_0"], "mappings": "AAA;oBCK;CDa;AEE;kBCI;SDiB;CFU;AIC;CJgB;AKC;yBCE,qBD;CLG;AOC;gBCG,4BD;aEC,kEF;CPE"}}, "type": "js/module"}]}