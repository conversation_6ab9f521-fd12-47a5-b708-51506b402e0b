{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/Keyboard/Keyboard", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 58}}], "key": "6tXZZE0lIGRoPAqR2m0a+9nrnuw=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../Data/LogBoxData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 49}}], "key": "A5Z1ymCQl9OoAWyeygTXLGmRNuU=", "exportNames": ["*"]}}, {"name": "../Data/LogBoxLog", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 59}}], "key": "G7ZBvdkqrR7u14zDBnWaTI9YPCM=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorBody", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 56}}], "key": "SvvYiXM+BNtR4B9zLZHDx6YoksE=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorFooter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 60}}], "key": "cicEPmUWEE7y+GsljcPNo3Wc9Ok=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorHeader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 60}}], "key": "tR1UoYjw/8muSX+DrzqV51vn+BA=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxInspector;\n  var _Keyboard = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/Keyboard/Keyboard\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"../../StyleSheet/StyleSheet\"));\n  var LogBoxData = _interopRequireWildcard(require(_dependencyMap[4], \"../Data/LogBoxData\"));\n  var _LogBoxLog = _interopRequireDefault(require(_dependencyMap[5], \"../Data/LogBoxLog\"));\n  var _LogBoxInspectorBody = _interopRequireDefault(require(_dependencyMap[6], \"./LogBoxInspectorBody\"));\n  var _LogBoxInspectorFooter = _interopRequireDefault(require(_dependencyMap[7], \"./LogBoxInspectorFooter\"));\n  var _LogBoxInspectorHeader = _interopRequireDefault(require(_dependencyMap[8], \"./LogBoxInspectorHeader\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[9], \"./LogBoxStyle\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[10], \"react\"));\n  var React = _react;\n  var _jsxRuntime = require(_dependencyMap[11], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/UI/LogBoxInspector.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxInspector(props) {\n    var logs = props.logs,\n      selectedIndex = props.selectedIndex;\n    var log = logs[selectedIndex];\n    (0, _react.useEffect)(() => {\n      if (log) {\n        LogBoxData.symbolicateLogNow(log);\n      }\n    }, [log]);\n    (0, _react.useEffect)(() => {\n      if (logs.length > 1) {\n        var selected = selectedIndex;\n        var lastIndex = logs.length - 1;\n        var prevIndex = selected - 1 < 0 ? lastIndex : selected - 1;\n        var nextIndex = selected + 1 > lastIndex ? 0 : selected + 1;\n        LogBoxData.symbolicateLogLazy(logs[prevIndex]);\n        LogBoxData.symbolicateLogLazy(logs[nextIndex]);\n      }\n    }, [logs, selectedIndex]);\n    (0, _react.useEffect)(() => {\n      _Keyboard.default.dismiss();\n    }, []);\n    function _handleRetry() {\n      LogBoxData.retrySymbolicateLogNow(log);\n    }\n    if (log == null) {\n      return null;\n    }\n    return (0, _jsxRuntime.jsxs)(_View.default, {\n      id: \"logbox_inspector\",\n      style: styles.root,\n      children: [(0, _jsxRuntime.jsx)(_LogBoxInspectorHeader.default, {\n        onSelectIndex: props.onChangeSelectedIndex,\n        selectedIndex: selectedIndex,\n        total: logs.length,\n        level: log.level\n      }), (0, _jsxRuntime.jsx)(_LogBoxInspectorBody.default, {\n        log: log,\n        onRetry: _handleRetry\n      }), (0, _jsxRuntime.jsx)(_LogBoxInspectorFooter.default, {\n        onDismiss: props.onDismiss,\n        onMinimize: props.onMinimize,\n        level: log.level\n      })]\n    });\n  }\n  var styles = _StyleSheet.default.create({\n    root: {\n      flex: 1,\n      backgroundColor: LogBoxStyle.getTextColor()\n    }\n  });\n});", "lineCount": 73, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_Keyboard"], [7, 15, 11, 0], [7, 18, 11, 0, "_interopRequireDefault"], [7, 40, 11, 0], [7, 41, 11, 0, "require"], [7, 48, 11, 0], [7, 49, 11, 0, "_dependencyMap"], [7, 63, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_View"], [8, 11, 12, 0], [8, 14, 12, 0, "_interopRequireDefault"], [8, 36, 12, 0], [8, 37, 12, 0, "require"], [8, 44, 12, 0], [8, 45, 12, 0, "_dependencyMap"], [8, 59, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_StyleSheet"], [9, 17, 13, 0], [9, 20, 13, 0, "_interopRequireDefault"], [9, 42, 13, 0], [9, 43, 13, 0, "require"], [9, 50, 13, 0], [9, 51, 13, 0, "_dependencyMap"], [9, 65, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "LogBoxData"], [10, 16, 14, 0], [10, 19, 14, 0, "_interopRequireWildcard"], [10, 42, 14, 0], [10, 43, 14, 0, "require"], [10, 50, 14, 0], [10, 51, 14, 0, "_dependencyMap"], [10, 65, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_LogBoxLog"], [11, 16, 15, 0], [11, 19, 15, 0, "_interopRequireDefault"], [11, 41, 15, 0], [11, 42, 15, 0, "require"], [11, 49, 15, 0], [11, 50, 15, 0, "_dependencyMap"], [11, 64, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "_LogBoxInspectorBody"], [12, 26, 16, 0], [12, 29, 16, 0, "_interopRequireDefault"], [12, 51, 16, 0], [12, 52, 16, 0, "require"], [12, 59, 16, 0], [12, 60, 16, 0, "_dependencyMap"], [12, 74, 16, 0], [13, 2, 17, 0], [13, 6, 17, 0, "_LogBoxInspectorFooter"], [13, 28, 17, 0], [13, 31, 17, 0, "_interopRequireDefault"], [13, 53, 17, 0], [13, 54, 17, 0, "require"], [13, 61, 17, 0], [13, 62, 17, 0, "_dependencyMap"], [13, 76, 17, 0], [14, 2, 18, 0], [14, 6, 18, 0, "_LogBoxInspectorHeader"], [14, 28, 18, 0], [14, 31, 18, 0, "_interopRequireDefault"], [14, 53, 18, 0], [14, 54, 18, 0, "require"], [14, 61, 18, 0], [14, 62, 18, 0, "_dependencyMap"], [14, 76, 18, 0], [15, 2, 19, 0], [15, 6, 19, 0, "LogBoxStyle"], [15, 17, 19, 0], [15, 20, 19, 0, "_interopRequireWildcard"], [15, 43, 19, 0], [15, 44, 19, 0, "require"], [15, 51, 19, 0], [15, 52, 19, 0, "_dependencyMap"], [15, 66, 19, 0], [16, 2, 20, 0], [16, 6, 20, 0, "_react"], [16, 12, 20, 0], [16, 15, 20, 0, "_interopRequireWildcard"], [16, 38, 20, 0], [16, 39, 20, 0, "require"], [16, 46, 20, 0], [16, 47, 20, 0, "_dependencyMap"], [16, 61, 20, 0], [17, 2, 20, 31], [17, 6, 20, 31, "React"], [17, 11, 20, 31], [17, 14, 20, 31, "_react"], [17, 20, 20, 31], [18, 2, 20, 31], [18, 6, 20, 31, "_jsxRuntime"], [18, 17, 20, 31], [18, 20, 20, 31, "require"], [18, 27, 20, 31], [18, 28, 20, 31, "_dependencyMap"], [18, 42, 20, 31], [19, 2, 20, 31], [19, 6, 20, 31, "_jsxFileName"], [19, 18, 20, 31], [20, 2, 20, 31], [20, 11, 20, 31, "_interopRequireWildcard"], [20, 35, 20, 31, "e"], [20, 36, 20, 31], [20, 38, 20, 31, "t"], [20, 39, 20, 31], [20, 68, 20, 31, "WeakMap"], [20, 75, 20, 31], [20, 81, 20, 31, "r"], [20, 82, 20, 31], [20, 89, 20, 31, "WeakMap"], [20, 96, 20, 31], [20, 100, 20, 31, "n"], [20, 101, 20, 31], [20, 108, 20, 31, "WeakMap"], [20, 115, 20, 31], [20, 127, 20, 31, "_interopRequireWildcard"], [20, 150, 20, 31], [20, 162, 20, 31, "_interopRequireWildcard"], [20, 163, 20, 31, "e"], [20, 164, 20, 31], [20, 166, 20, 31, "t"], [20, 167, 20, 31], [20, 176, 20, 31, "t"], [20, 177, 20, 31], [20, 181, 20, 31, "e"], [20, 182, 20, 31], [20, 186, 20, 31, "e"], [20, 187, 20, 31], [20, 188, 20, 31, "__esModule"], [20, 198, 20, 31], [20, 207, 20, 31, "e"], [20, 208, 20, 31], [20, 214, 20, 31, "o"], [20, 215, 20, 31], [20, 217, 20, 31, "i"], [20, 218, 20, 31], [20, 220, 20, 31, "f"], [20, 221, 20, 31], [20, 226, 20, 31, "__proto__"], [20, 235, 20, 31], [20, 243, 20, 31, "default"], [20, 250, 20, 31], [20, 252, 20, 31, "e"], [20, 253, 20, 31], [20, 270, 20, 31, "e"], [20, 271, 20, 31], [20, 294, 20, 31, "e"], [20, 295, 20, 31], [20, 320, 20, 31, "e"], [20, 321, 20, 31], [20, 330, 20, 31, "f"], [20, 331, 20, 31], [20, 337, 20, 31, "o"], [20, 338, 20, 31], [20, 341, 20, 31, "t"], [20, 342, 20, 31], [20, 345, 20, 31, "n"], [20, 346, 20, 31], [20, 349, 20, 31, "r"], [20, 350, 20, 31], [20, 358, 20, 31, "o"], [20, 359, 20, 31], [20, 360, 20, 31, "has"], [20, 363, 20, 31], [20, 364, 20, 31, "e"], [20, 365, 20, 31], [20, 375, 20, 31, "o"], [20, 376, 20, 31], [20, 377, 20, 31, "get"], [20, 380, 20, 31], [20, 381, 20, 31, "e"], [20, 382, 20, 31], [20, 385, 20, 31, "o"], [20, 386, 20, 31], [20, 387, 20, 31, "set"], [20, 390, 20, 31], [20, 391, 20, 31, "e"], [20, 392, 20, 31], [20, 394, 20, 31, "f"], [20, 395, 20, 31], [20, 409, 20, 31, "_t"], [20, 411, 20, 31], [20, 415, 20, 31, "e"], [20, 416, 20, 31], [20, 432, 20, 31, "_t"], [20, 434, 20, 31], [20, 441, 20, 31, "hasOwnProperty"], [20, 455, 20, 31], [20, 456, 20, 31, "call"], [20, 460, 20, 31], [20, 461, 20, 31, "e"], [20, 462, 20, 31], [20, 464, 20, 31, "_t"], [20, 466, 20, 31], [20, 473, 20, 31, "i"], [20, 474, 20, 31], [20, 478, 20, 31, "o"], [20, 479, 20, 31], [20, 482, 20, 31, "Object"], [20, 488, 20, 31], [20, 489, 20, 31, "defineProperty"], [20, 503, 20, 31], [20, 508, 20, 31, "Object"], [20, 514, 20, 31], [20, 515, 20, 31, "getOwnPropertyDescriptor"], [20, 539, 20, 31], [20, 540, 20, 31, "e"], [20, 541, 20, 31], [20, 543, 20, 31, "_t"], [20, 545, 20, 31], [20, 552, 20, 31, "i"], [20, 553, 20, 31], [20, 554, 20, 31, "get"], [20, 557, 20, 31], [20, 561, 20, 31, "i"], [20, 562, 20, 31], [20, 563, 20, 31, "set"], [20, 566, 20, 31], [20, 570, 20, 31, "o"], [20, 571, 20, 31], [20, 572, 20, 31, "f"], [20, 573, 20, 31], [20, 575, 20, 31, "_t"], [20, 577, 20, 31], [20, 579, 20, 31, "i"], [20, 580, 20, 31], [20, 584, 20, 31, "f"], [20, 585, 20, 31], [20, 586, 20, 31, "_t"], [20, 588, 20, 31], [20, 592, 20, 31, "e"], [20, 593, 20, 31], [20, 594, 20, 31, "_t"], [20, 596, 20, 31], [20, 607, 20, 31, "f"], [20, 608, 20, 31], [20, 613, 20, 31, "e"], [20, 614, 20, 31], [20, 616, 20, 31, "t"], [20, 617, 20, 31], [21, 2, 32, 15], [21, 11, 32, 24, "LogBoxInspector"], [21, 26, 32, 39, "LogBoxInspector"], [21, 27, 32, 40, "props"], [21, 32, 32, 52], [21, 34, 32, 66], [22, 4, 33, 2], [22, 8, 33, 9, "logs"], [22, 12, 33, 13], [22, 15, 33, 32, "props"], [22, 20, 33, 37], [22, 21, 33, 9, "logs"], [22, 25, 33, 13], [23, 6, 33, 15, "selectedIndex"], [23, 19, 33, 28], [23, 22, 33, 32, "props"], [23, 27, 33, 37], [23, 28, 33, 15, "selectedIndex"], [23, 41, 33, 28], [24, 4, 34, 2], [24, 8, 34, 6, "log"], [24, 11, 34, 9], [24, 14, 34, 12, "logs"], [24, 18, 34, 16], [24, 19, 34, 17, "selectedIndex"], [24, 32, 34, 30], [24, 33, 34, 31], [25, 4, 36, 2], [25, 8, 36, 2, "useEffect"], [25, 24, 36, 11], [25, 26, 36, 12], [25, 32, 36, 18], [26, 6, 37, 4], [26, 10, 37, 8, "log"], [26, 13, 37, 11], [26, 15, 37, 13], [27, 8, 38, 6, "LogBoxData"], [27, 18, 38, 16], [27, 19, 38, 17, "symbolicateLogNow"], [27, 36, 38, 34], [27, 37, 38, 35, "log"], [27, 40, 38, 38], [27, 41, 38, 39], [28, 6, 39, 4], [29, 4, 40, 2], [29, 5, 40, 3], [29, 7, 40, 5], [29, 8, 40, 6, "log"], [29, 11, 40, 9], [29, 12, 40, 10], [29, 13, 40, 11], [30, 4, 42, 2], [30, 8, 42, 2, "useEffect"], [30, 24, 42, 11], [30, 26, 42, 12], [30, 32, 42, 18], [31, 6, 44, 4], [31, 10, 44, 8, "logs"], [31, 14, 44, 12], [31, 15, 44, 13, "length"], [31, 21, 44, 19], [31, 24, 44, 22], [31, 25, 44, 23], [31, 27, 44, 25], [32, 8, 45, 6], [32, 12, 45, 12, "selected"], [32, 20, 45, 20], [32, 23, 45, 23, "selectedIndex"], [32, 36, 45, 36], [33, 8, 46, 6], [33, 12, 46, 12, "lastIndex"], [33, 21, 46, 21], [33, 24, 46, 24, "logs"], [33, 28, 46, 28], [33, 29, 46, 29, "length"], [33, 35, 46, 35], [33, 38, 46, 38], [33, 39, 46, 39], [34, 8, 47, 6], [34, 12, 47, 12, "prevIndex"], [34, 21, 47, 21], [34, 24, 47, 24, "selected"], [34, 32, 47, 32], [34, 35, 47, 35], [34, 36, 47, 36], [34, 39, 47, 39], [34, 40, 47, 40], [34, 43, 47, 43, "lastIndex"], [34, 52, 47, 52], [34, 55, 47, 55, "selected"], [34, 63, 47, 63], [34, 66, 47, 66], [34, 67, 47, 67], [35, 8, 48, 6], [35, 12, 48, 12, "nextIndex"], [35, 21, 48, 21], [35, 24, 48, 24, "selected"], [35, 32, 48, 32], [35, 35, 48, 35], [35, 36, 48, 36], [35, 39, 48, 39, "lastIndex"], [35, 48, 48, 48], [35, 51, 48, 51], [35, 52, 48, 52], [35, 55, 48, 55, "selected"], [35, 63, 48, 63], [35, 66, 48, 66], [35, 67, 48, 67], [36, 8, 49, 6, "LogBoxData"], [36, 18, 49, 16], [36, 19, 49, 17, "symbolicateLogLazy"], [36, 37, 49, 35], [36, 38, 49, 36, "logs"], [36, 42, 49, 40], [36, 43, 49, 41, "prevIndex"], [36, 52, 49, 50], [36, 53, 49, 51], [36, 54, 49, 52], [37, 8, 50, 6, "LogBoxData"], [37, 18, 50, 16], [37, 19, 50, 17, "symbolicateLogLazy"], [37, 37, 50, 35], [37, 38, 50, 36, "logs"], [37, 42, 50, 40], [37, 43, 50, 41, "nextIndex"], [37, 52, 50, 50], [37, 53, 50, 51], [37, 54, 50, 52], [38, 6, 51, 4], [39, 4, 52, 2], [39, 5, 52, 3], [39, 7, 52, 5], [39, 8, 52, 6, "logs"], [39, 12, 52, 10], [39, 14, 52, 12, "selectedIndex"], [39, 27, 52, 25], [39, 28, 52, 26], [39, 29, 52, 27], [40, 4, 54, 2], [40, 8, 54, 2, "useEffect"], [40, 24, 54, 11], [40, 26, 54, 12], [40, 32, 54, 18], [41, 6, 55, 4, "Keyboard"], [41, 23, 55, 12], [41, 24, 55, 13, "dismiss"], [41, 31, 55, 20], [41, 32, 55, 21], [41, 33, 55, 22], [42, 4, 56, 2], [42, 5, 56, 3], [42, 7, 56, 5], [42, 9, 56, 7], [42, 10, 56, 8], [43, 4, 58, 2], [43, 13, 58, 11, "_handleRetry"], [43, 25, 58, 23, "_handleRetry"], [43, 26, 58, 23], [43, 28, 58, 26], [44, 6, 59, 4, "LogBoxData"], [44, 16, 59, 14], [44, 17, 59, 15, "retrySymbolicateLogNow"], [44, 39, 59, 37], [44, 40, 59, 38, "log"], [44, 43, 59, 41], [44, 44, 59, 42], [45, 4, 60, 2], [46, 4, 62, 2], [46, 8, 62, 6, "log"], [46, 11, 62, 9], [46, 15, 62, 13], [46, 19, 62, 17], [46, 21, 62, 19], [47, 6, 63, 4], [47, 13, 63, 11], [47, 17, 63, 15], [48, 4, 64, 2], [49, 4, 66, 2], [49, 11, 67, 4], [49, 15, 67, 4, "_jsxRuntime"], [49, 26, 67, 4], [49, 27, 67, 4, "jsxs"], [49, 31, 67, 4], [49, 33, 67, 5, "_View"], [49, 38, 67, 5], [49, 39, 67, 5, "default"], [49, 46, 67, 9], [50, 6, 67, 10, "id"], [50, 8, 67, 12], [50, 10, 67, 13], [50, 28, 67, 31], [51, 6, 67, 32, "style"], [51, 11, 67, 37], [51, 13, 67, 39, "styles"], [51, 19, 67, 45], [51, 20, 67, 46, "root"], [51, 24, 67, 51], [52, 6, 67, 51, "children"], [52, 14, 67, 51], [52, 17, 68, 6], [52, 21, 68, 6, "_jsxRuntime"], [52, 32, 68, 6], [52, 33, 68, 6, "jsx"], [52, 36, 68, 6], [52, 38, 68, 7, "_LogBoxInspectorHeader"], [52, 60, 68, 7], [52, 61, 68, 7, "default"], [52, 68, 68, 28], [53, 8, 69, 8, "onSelectIndex"], [53, 21, 69, 21], [53, 23, 69, 23, "props"], [53, 28, 69, 28], [53, 29, 69, 29, "onChangeSelectedIndex"], [53, 50, 69, 51], [54, 8, 70, 8, "selectedIndex"], [54, 21, 70, 21], [54, 23, 70, 23, "selectedIndex"], [54, 36, 70, 37], [55, 8, 71, 8, "total"], [55, 13, 71, 13], [55, 15, 71, 15, "logs"], [55, 19, 71, 19], [55, 20, 71, 20, "length"], [55, 26, 71, 27], [56, 8, 72, 8, "level"], [56, 13, 72, 13], [56, 15, 72, 15, "log"], [56, 18, 72, 18], [56, 19, 72, 19, "level"], [57, 6, 72, 25], [57, 7, 73, 7], [57, 8, 73, 8], [57, 10, 74, 6], [57, 14, 74, 6, "_jsxRuntime"], [57, 25, 74, 6], [57, 26, 74, 6, "jsx"], [57, 29, 74, 6], [57, 31, 74, 7, "_LogBoxInspectorBody"], [57, 51, 74, 7], [57, 52, 74, 7, "default"], [57, 59, 74, 26], [58, 8, 74, 27, "log"], [58, 11, 74, 30], [58, 13, 74, 32, "log"], [58, 16, 74, 36], [59, 8, 74, 37, "onRetry"], [59, 15, 74, 44], [59, 17, 74, 46, "_handleRetry"], [60, 6, 74, 59], [60, 7, 74, 61], [60, 8, 74, 62], [60, 10, 75, 6], [60, 14, 75, 6, "_jsxRuntime"], [60, 25, 75, 6], [60, 26, 75, 6, "jsx"], [60, 29, 75, 6], [60, 31, 75, 7, "_LogBoxInspectorFooter"], [60, 53, 75, 7], [60, 54, 75, 7, "default"], [60, 61, 75, 28], [61, 8, 76, 8, "on<PERSON><PERSON><PERSON>"], [61, 17, 76, 17], [61, 19, 76, 19, "props"], [61, 24, 76, 24], [61, 25, 76, 25, "on<PERSON><PERSON><PERSON>"], [61, 34, 76, 35], [62, 8, 77, 8, "onMinimize"], [62, 18, 77, 18], [62, 20, 77, 20, "props"], [62, 25, 77, 25], [62, 26, 77, 26, "onMinimize"], [62, 36, 77, 37], [63, 8, 78, 8, "level"], [63, 13, 78, 13], [63, 15, 78, 15, "log"], [63, 18, 78, 18], [63, 19, 78, 19, "level"], [64, 6, 78, 25], [64, 7, 79, 7], [64, 8, 79, 8], [65, 4, 79, 8], [65, 5, 80, 10], [65, 6, 80, 11], [66, 2, 82, 0], [67, 2, 84, 0], [67, 6, 84, 6, "styles"], [67, 12, 84, 12], [67, 15, 84, 15, "StyleSheet"], [67, 34, 84, 25], [67, 35, 84, 26, "create"], [67, 41, 84, 32], [67, 42, 84, 33], [68, 4, 85, 2, "root"], [68, 8, 85, 6], [68, 10, 85, 8], [69, 6, 86, 4, "flex"], [69, 10, 86, 8], [69, 12, 86, 10], [69, 13, 86, 11], [70, 6, 87, 4, "backgroundColor"], [70, 21, 87, 19], [70, 23, 87, 21, "LogBoxStyle"], [70, 34, 87, 32], [70, 35, 87, 33, "getTextColor"], [70, 47, 87, 45], [70, 48, 87, 46], [71, 4, 88, 2], [72, 2, 89, 0], [72, 3, 89, 1], [72, 4, 89, 2], [73, 0, 89, 3], [73, 3]], "functionMap": {"names": ["<global>", "LogBoxInspector", "useEffect$argument_0", "_handleRetry"], "mappings": "AAA;eC+B;YCI;GDI;YCE;GDU;YCE;GDE;EEE;GFE;CDsB"}}, "type": "js/module"}]}