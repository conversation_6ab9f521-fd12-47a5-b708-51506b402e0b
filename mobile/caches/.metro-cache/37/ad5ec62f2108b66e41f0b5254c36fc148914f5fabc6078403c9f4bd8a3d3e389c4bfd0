{"dependencies": [{"name": "./dispatchCommand", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "ox+VJbjgxK+JO2iVuZgRrFKJTho=", "exportNames": ["*"]}}, {"name": "./getRelativeCoords.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 59, "index": 127}}], "key": "qSXOTB9cgk30vTHVRZpih6xeJ5o=", "exportNames": ["*"]}}, {"name": "./measure", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 128}, "end": {"line": 5, "column": 36, "index": 164}}], "key": "B5IHqoArBd4o1xEAyGBYJRJiEtY=", "exportNames": ["*"]}}, {"name": "./scrollTo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 165}, "end": {"line": 6, "column": 38, "index": 203}}], "key": "pLEhQoHmZYU1q+++iu+FbfY0xWw=", "exportNames": ["*"]}}, {"name": "./setGestureState", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 204}, "end": {"line": 7, "column": 52, "index": 256}}], "key": "YiFnKrctRaBiwSJlwKEWfMoGz7g=", "exportNames": ["*"]}}, {"name": "./setNativeProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 257}, "end": {"line": 8, "column": 50, "index": 307}}], "key": "snEsaHMAwv8/oD2AVGKv3Lq+65Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"dispatchCommand\", {\n    enumerable: true,\n    get: function () {\n      return _dispatchCommand.dispatchCommand;\n    }\n  });\n  Object.defineProperty(exports, \"getRelativeCoords\", {\n    enumerable: true,\n    get: function () {\n      return _getRelativeCoords.getRelativeCoords;\n    }\n  });\n  Object.defineProperty(exports, \"measure\", {\n    enumerable: true,\n    get: function () {\n      return _measure.measure;\n    }\n  });\n  Object.defineProperty(exports, \"scrollTo\", {\n    enumerable: true,\n    get: function () {\n      return _scrollTo.scrollTo;\n    }\n  });\n  Object.defineProperty(exports, \"setGestureState\", {\n    enumerable: true,\n    get: function () {\n      return _setGestureState.setGestureState;\n    }\n  });\n  Object.defineProperty(exports, \"setNativeProps\", {\n    enumerable: true,\n    get: function () {\n      return _setNativeProps.setNativeProps;\n    }\n  });\n  var _dispatchCommand = require(_dependencyMap[0], \"./dispatchCommand\");\n  var _getRelativeCoords = require(_dependencyMap[1], \"./getRelativeCoords.js\");\n  var _measure = require(_dependencyMap[2], \"./measure\");\n  var _scrollTo = require(_dependencyMap[3], \"./scrollTo\");\n  var _setGestureState = require(_dependencyMap[4], \"./setGestureState\");\n  var _setNativeProps = require(_dependencyMap[5], \"./setNativeProps\");\n});", "lineCount": 49, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_dispatchCommand"], [10, 29, 1, 13], [10, 30, 1, 13, "dispatchCommand"], [10, 45, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 1, 13, "Object"], [13, 8, 1, 13], [13, 9, 1, 13, "defineProperty"], [13, 23, 1, 13], [13, 24, 1, 13, "exports"], [13, 31, 1, 13], [14, 4, 1, 13, "enumerable"], [14, 14, 1, 13], [15, 4, 1, 13, "get"], [15, 7, 1, 13], [15, 18, 1, 13, "get"], [15, 19, 1, 13], [16, 6, 1, 13], [16, 13, 1, 13, "_getRelativeCoords"], [16, 31, 1, 13], [16, 32, 1, 13, "getRelativeCoords"], [16, 49, 1, 13], [17, 4, 1, 13], [18, 2, 1, 13], [19, 2, 1, 13, "Object"], [19, 8, 1, 13], [19, 9, 1, 13, "defineProperty"], [19, 23, 1, 13], [19, 24, 1, 13, "exports"], [19, 31, 1, 13], [20, 4, 1, 13, "enumerable"], [20, 14, 1, 13], [21, 4, 1, 13, "get"], [21, 7, 1, 13], [21, 18, 1, 13, "get"], [21, 19, 1, 13], [22, 6, 1, 13], [22, 13, 1, 13, "_measure"], [22, 21, 1, 13], [22, 22, 1, 13, "measure"], [22, 29, 1, 13], [23, 4, 1, 13], [24, 2, 1, 13], [25, 2, 1, 13, "Object"], [25, 8, 1, 13], [25, 9, 1, 13, "defineProperty"], [25, 23, 1, 13], [25, 24, 1, 13, "exports"], [25, 31, 1, 13], [26, 4, 1, 13, "enumerable"], [26, 14, 1, 13], [27, 4, 1, 13, "get"], [27, 7, 1, 13], [27, 18, 1, 13, "get"], [27, 19, 1, 13], [28, 6, 1, 13], [28, 13, 1, 13, "_scrollTo"], [28, 22, 1, 13], [28, 23, 1, 13, "scrollTo"], [28, 31, 1, 13], [29, 4, 1, 13], [30, 2, 1, 13], [31, 2, 1, 13, "Object"], [31, 8, 1, 13], [31, 9, 1, 13, "defineProperty"], [31, 23, 1, 13], [31, 24, 1, 13, "exports"], [31, 31, 1, 13], [32, 4, 1, 13, "enumerable"], [32, 14, 1, 13], [33, 4, 1, 13, "get"], [33, 7, 1, 13], [33, 18, 1, 13, "get"], [33, 19, 1, 13], [34, 6, 1, 13], [34, 13, 1, 13, "_setGestureState"], [34, 29, 1, 13], [34, 30, 1, 13, "setGestureState"], [34, 45, 1, 13], [35, 4, 1, 13], [36, 2, 1, 13], [37, 2, 1, 13, "Object"], [37, 8, 1, 13], [37, 9, 1, 13, "defineProperty"], [37, 23, 1, 13], [37, 24, 1, 13, "exports"], [37, 31, 1, 13], [38, 4, 1, 13, "enumerable"], [38, 14, 1, 13], [39, 4, 1, 13, "get"], [39, 7, 1, 13], [39, 18, 1, 13, "get"], [39, 19, 1, 13], [40, 6, 1, 13], [40, 13, 1, 13, "_setNativeProps"], [40, 28, 1, 13], [40, 29, 1, 13, "setNativeProps"], [40, 43, 1, 13], [41, 4, 1, 13], [42, 2, 1, 13], [43, 2, 3, 0], [43, 6, 3, 0, "_dispatchCommand"], [43, 22, 3, 0], [43, 25, 3, 0, "require"], [43, 32, 3, 0], [43, 33, 3, 0, "_dependencyMap"], [43, 47, 3, 0], [44, 2, 4, 0], [44, 6, 4, 0, "_getRelativeCoords"], [44, 24, 4, 0], [44, 27, 4, 0, "require"], [44, 34, 4, 0], [44, 35, 4, 0, "_dependencyMap"], [44, 49, 4, 0], [45, 2, 5, 0], [45, 6, 5, 0, "_measure"], [45, 14, 5, 0], [45, 17, 5, 0, "require"], [45, 24, 5, 0], [45, 25, 5, 0, "_dependencyMap"], [45, 39, 5, 0], [46, 2, 6, 0], [46, 6, 6, 0, "_scrollTo"], [46, 15, 6, 0], [46, 18, 6, 0, "require"], [46, 25, 6, 0], [46, 26, 6, 0, "_dependencyMap"], [46, 40, 6, 0], [47, 2, 7, 0], [47, 6, 7, 0, "_setGestureState"], [47, 22, 7, 0], [47, 25, 7, 0, "require"], [47, 32, 7, 0], [47, 33, 7, 0, "_dependencyMap"], [47, 47, 7, 0], [48, 2, 8, 0], [48, 6, 8, 0, "_setNativeProps"], [48, 21, 8, 0], [48, 24, 8, 0, "require"], [48, 31, 8, 0], [48, 32, 8, 0, "_dependencyMap"], [48, 46, 8, 0], [49, 0, 8, 50], [49, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}