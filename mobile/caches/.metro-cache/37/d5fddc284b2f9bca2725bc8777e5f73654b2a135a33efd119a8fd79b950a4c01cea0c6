{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./fetch", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 35, "index": 35}}], "key": "530RtYCmKsE3te4bFHMv0Zip4Vc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  var _fetch = _interopRequireDefault(require(_dependencyMap[1], \"./fetch\"));\n  // @ts-ignore\n  global.fetch = _fetch.default;\n});", "lineCount": 6, "map": [[3, 2, 1, 0], [3, 6, 1, 0, "_fetch"], [3, 12, 1, 0], [3, 15, 1, 0, "_interopRequireDefault"], [3, 37, 1, 0], [3, 38, 1, 0, "require"], [3, 45, 1, 0], [3, 46, 1, 0, "_dependencyMap"], [3, 60, 1, 0], [4, 2, 2, 0], [5, 2, 3, 0, "global"], [5, 8, 3, 6], [5, 9, 3, 7, "fetch"], [5, 14, 3, 12], [5, 17, 3, 15, "updatedFetch"], [5, 31, 3, 27], [6, 0, 3, 28], [6, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}