{"dependencies": [{"name": "./core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 73, "index": 88}}], "key": "mJVVi7YU3vDVLm6ZethtbJGh1KY=", "exportNames": ["*"]}}, {"name": "./PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 89}, "end": {"line": 4, "column": 54, "index": 143}}], "key": "6AA7RQghlqlrd3hVWNoLh/rI420=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WorkletEventHandler = void 0;\n  var _core = require(_dependencyMap[0], \"./core.js\");\n  var _PlatformChecker = require(_dependencyMap[1], \"./PlatformChecker.js\");\n  const SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  // In JS implementation (e.g. for web) we don't use Reanimated's\n  // event emitter, therefore we have to handle here\n  // the event that came from React Native and convert it.\n  function jsListener(eventName, handler) {\n    return evt => {\n      handler({\n        ...evt.nativeEvent,\n        eventName\n      });\n    };\n  }\n  class WorkletEventHandlerNative {\n    #viewTags;\n    #registrations; // keys are viewTags, values are arrays of registration ID's for each viewTag\n    constructor(worklet, eventNames) {\n      this.worklet = worklet;\n      this.eventNames = eventNames;\n      this.#viewTags = new Set();\n      this.#registrations = new Map();\n    }\n    updateEventHandler(newWorklet, newEvents) {\n      // Update worklet and event names\n      this.worklet = newWorklet;\n      this.eventNames = newEvents;\n\n      // Detach all events\n      this.#registrations.forEach(registrationIDs => {\n        registrationIDs.forEach(id => (0, _core.unregisterEventHandler)(id));\n        // No need to remove registrationIDs from map, since it gets overwritten when attaching\n      });\n\n      // Attach new events with new worklet\n      Array.from(this.#viewTags).forEach(tag => {\n        const newRegistrations = this.eventNames.map(eventName => (0, _core.registerEventHandler)(this.worklet, eventName, tag));\n        this.#registrations.set(tag, newRegistrations);\n      });\n    }\n    registerForEvents(viewTag, fallbackEventName) {\n      this.#viewTags.add(viewTag);\n      const newRegistrations = this.eventNames.map(eventName => (0, _core.registerEventHandler)(this.worklet, eventName, viewTag));\n      this.#registrations.set(viewTag, newRegistrations);\n      if (this.eventNames.length === 0 && fallbackEventName) {\n        const newRegistration = (0, _core.registerEventHandler)(this.worklet, fallbackEventName, viewTag);\n        this.#registrations.set(viewTag, [newRegistration]);\n      }\n    }\n    unregisterFromEvents(viewTag) {\n      this.#viewTags.delete(viewTag);\n      this.#registrations.get(viewTag)?.forEach(id => {\n        (0, _core.unregisterEventHandler)(id);\n      });\n      this.#registrations.delete(viewTag);\n    }\n  }\n  class WorkletEventHandlerWeb {\n    constructor(worklet, eventNames = []) {\n      this.worklet = worklet;\n      this.eventNames = eventNames;\n      this.listeners = {};\n      this.setupWebListeners();\n    }\n    setupWebListeners() {\n      this.listeners = {};\n      this.eventNames.forEach(eventName => {\n        this.listeners[eventName] = jsListener(eventName, this.worklet);\n      });\n    }\n    updateEventHandler(newWorklet, newEvents) {\n      // Update worklet and event names\n      this.worklet = newWorklet;\n      this.eventNames = newEvents;\n      this.setupWebListeners();\n    }\n    registerForEvents(_viewTag, _fallbackEventName) {\n      // noop\n    }\n    unregisterFromEvents(_viewTag) {\n      // noop\n    }\n  }\n  const WorkletEventHandler = exports.WorkletEventHandler = SHOULD_BE_USE_WEB ? WorkletEventHandlerWeb : WorkletEventHandlerNative;\n});", "lineCount": 92, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "WorkletEventHandler"], [7, 29, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_PlatformChecker"], [9, 22, 4, 0], [9, 25, 4, 0, "require"], [9, 32, 4, 0], [9, 33, 4, 0, "_dependencyMap"], [9, 47, 4, 0], [10, 2, 5, 0], [10, 8, 5, 6, "SHOULD_BE_USE_WEB"], [10, 25, 5, 23], [10, 28, 5, 26], [10, 32, 5, 26, "shouldBeUseWeb"], [10, 63, 5, 40], [10, 65, 5, 41], [10, 66, 5, 42], [11, 2, 6, 0], [12, 2, 7, 0], [13, 2, 8, 0], [14, 2, 9, 0], [14, 11, 9, 9, "jsListener"], [14, 21, 9, 19, "jsListener"], [14, 22, 9, 20, "eventName"], [14, 31, 9, 29], [14, 33, 9, 31, "handler"], [14, 40, 9, 38], [14, 42, 9, 40], [15, 4, 10, 2], [15, 11, 10, 9, "evt"], [15, 14, 10, 12], [15, 18, 10, 16], [16, 6, 11, 4, "handler"], [16, 13, 11, 11], [16, 14, 11, 12], [17, 8, 12, 6], [17, 11, 12, 9, "evt"], [17, 14, 12, 12], [17, 15, 12, 13, "nativeEvent"], [17, 26, 12, 24], [18, 8, 13, 6, "eventName"], [19, 6, 14, 4], [19, 7, 14, 5], [19, 8, 14, 6], [20, 4, 15, 2], [20, 5, 15, 3], [21, 2, 16, 0], [22, 2, 17, 0], [22, 8, 17, 6, "WorkletEventHandlerNative"], [22, 33, 17, 31], [22, 34, 17, 32], [23, 4, 18, 2], [23, 5, 18, 3, "viewTags"], [23, 13, 18, 11], [24, 4, 19, 2], [24, 5, 19, 3, "registrations"], [24, 18, 19, 16], [24, 19, 19, 17], [24, 20, 19, 18], [25, 4, 20, 2, "constructor"], [25, 15, 20, 13, "constructor"], [25, 16, 20, 14, "worklet"], [25, 23, 20, 21], [25, 25, 20, 23, "eventNames"], [25, 35, 20, 33], [25, 37, 20, 35], [26, 6, 21, 4], [26, 10, 21, 8], [26, 11, 21, 9, "worklet"], [26, 18, 21, 16], [26, 21, 21, 19, "worklet"], [26, 28, 21, 26], [27, 6, 22, 4], [27, 10, 22, 8], [27, 11, 22, 9, "eventNames"], [27, 21, 22, 19], [27, 24, 22, 22, "eventNames"], [27, 34, 22, 32], [28, 6, 23, 4], [28, 10, 23, 8], [28, 11, 23, 9], [28, 12, 23, 10, "viewTags"], [28, 20, 23, 18], [28, 23, 23, 21], [28, 27, 23, 25, "Set"], [28, 30, 23, 28], [28, 31, 23, 29], [28, 32, 23, 30], [29, 6, 24, 4], [29, 10, 24, 8], [29, 11, 24, 9], [29, 12, 24, 10, "registrations"], [29, 25, 24, 23], [29, 28, 24, 26], [29, 32, 24, 30, "Map"], [29, 35, 24, 33], [29, 36, 24, 34], [29, 37, 24, 35], [30, 4, 25, 2], [31, 4, 26, 2, "updateEventHandler"], [31, 22, 26, 20, "updateEventHandler"], [31, 23, 26, 21, "newWorklet"], [31, 33, 26, 31], [31, 35, 26, 33, "newEvents"], [31, 44, 26, 42], [31, 46, 26, 44], [32, 6, 27, 4], [33, 6, 28, 4], [33, 10, 28, 8], [33, 11, 28, 9, "worklet"], [33, 18, 28, 16], [33, 21, 28, 19, "newWorklet"], [33, 31, 28, 29], [34, 6, 29, 4], [34, 10, 29, 8], [34, 11, 29, 9, "eventNames"], [34, 21, 29, 19], [34, 24, 29, 22, "newEvents"], [34, 33, 29, 31], [36, 6, 31, 4], [37, 6, 32, 4], [37, 10, 32, 8], [37, 11, 32, 9], [37, 12, 32, 10, "registrations"], [37, 25, 32, 23], [37, 26, 32, 24, "for<PERSON>ach"], [37, 33, 32, 31], [37, 34, 32, 32, "registrationIDs"], [37, 49, 32, 47], [37, 53, 32, 51], [38, 8, 33, 6, "registrationIDs"], [38, 23, 33, 21], [38, 24, 33, 22, "for<PERSON>ach"], [38, 31, 33, 29], [38, 32, 33, 30, "id"], [38, 34, 33, 32], [38, 38, 33, 36], [38, 42, 33, 36, "unregisterEventHandler"], [38, 70, 33, 58], [38, 72, 33, 59, "id"], [38, 74, 33, 61], [38, 75, 33, 62], [38, 76, 33, 63], [39, 8, 34, 6], [40, 6, 35, 4], [40, 7, 35, 5], [40, 8, 35, 6], [42, 6, 37, 4], [43, 6, 38, 4, "Array"], [43, 11, 38, 9], [43, 12, 38, 10, "from"], [43, 16, 38, 14], [43, 17, 38, 15], [43, 21, 38, 19], [43, 22, 38, 20], [43, 23, 38, 21, "viewTags"], [43, 31, 38, 29], [43, 32, 38, 30], [43, 33, 38, 31, "for<PERSON>ach"], [43, 40, 38, 38], [43, 41, 38, 39, "tag"], [43, 44, 38, 42], [43, 48, 38, 46], [44, 8, 39, 6], [44, 14, 39, 12, "newRegistrations"], [44, 30, 39, 28], [44, 33, 39, 31], [44, 37, 39, 35], [44, 38, 39, 36, "eventNames"], [44, 48, 39, 46], [44, 49, 39, 47, "map"], [44, 52, 39, 50], [44, 53, 39, 51, "eventName"], [44, 62, 39, 60], [44, 66, 39, 64], [44, 70, 39, 64, "registerEventHandler"], [44, 96, 39, 84], [44, 98, 39, 85], [44, 102, 39, 89], [44, 103, 39, 90, "worklet"], [44, 110, 39, 97], [44, 112, 39, 99, "eventName"], [44, 121, 39, 108], [44, 123, 39, 110, "tag"], [44, 126, 39, 113], [44, 127, 39, 114], [44, 128, 39, 115], [45, 8, 40, 6], [45, 12, 40, 10], [45, 13, 40, 11], [45, 14, 40, 12, "registrations"], [45, 27, 40, 25], [45, 28, 40, 26, "set"], [45, 31, 40, 29], [45, 32, 40, 30, "tag"], [45, 35, 40, 33], [45, 37, 40, 35, "newRegistrations"], [45, 53, 40, 51], [45, 54, 40, 52], [46, 6, 41, 4], [46, 7, 41, 5], [46, 8, 41, 6], [47, 4, 42, 2], [48, 4, 43, 2, "registerForEvents"], [48, 21, 43, 19, "registerForEvents"], [48, 22, 43, 20, "viewTag"], [48, 29, 43, 27], [48, 31, 43, 29, "fallbackEventName"], [48, 48, 43, 46], [48, 50, 43, 48], [49, 6, 44, 4], [49, 10, 44, 8], [49, 11, 44, 9], [49, 12, 44, 10, "viewTags"], [49, 20, 44, 18], [49, 21, 44, 19, "add"], [49, 24, 44, 22], [49, 25, 44, 23, "viewTag"], [49, 32, 44, 30], [49, 33, 44, 31], [50, 6, 45, 4], [50, 12, 45, 10, "newRegistrations"], [50, 28, 45, 26], [50, 31, 45, 29], [50, 35, 45, 33], [50, 36, 45, 34, "eventNames"], [50, 46, 45, 44], [50, 47, 45, 45, "map"], [50, 50, 45, 48], [50, 51, 45, 49, "eventName"], [50, 60, 45, 58], [50, 64, 45, 62], [50, 68, 45, 62, "registerEventHandler"], [50, 94, 45, 82], [50, 96, 45, 83], [50, 100, 45, 87], [50, 101, 45, 88, "worklet"], [50, 108, 45, 95], [50, 110, 45, 97, "eventName"], [50, 119, 45, 106], [50, 121, 45, 108, "viewTag"], [50, 128, 45, 115], [50, 129, 45, 116], [50, 130, 45, 117], [51, 6, 46, 4], [51, 10, 46, 8], [51, 11, 46, 9], [51, 12, 46, 10, "registrations"], [51, 25, 46, 23], [51, 26, 46, 24, "set"], [51, 29, 46, 27], [51, 30, 46, 28, "viewTag"], [51, 37, 46, 35], [51, 39, 46, 37, "newRegistrations"], [51, 55, 46, 53], [51, 56, 46, 54], [52, 6, 47, 4], [52, 10, 47, 8], [52, 14, 47, 12], [52, 15, 47, 13, "eventNames"], [52, 25, 47, 23], [52, 26, 47, 24, "length"], [52, 32, 47, 30], [52, 37, 47, 35], [52, 38, 47, 36], [52, 42, 47, 40, "fallbackEventName"], [52, 59, 47, 57], [52, 61, 47, 59], [53, 8, 48, 6], [53, 14, 48, 12, "newRegistration"], [53, 29, 48, 27], [53, 32, 48, 30], [53, 36, 48, 30, "registerEventHandler"], [53, 62, 48, 50], [53, 64, 48, 51], [53, 68, 48, 55], [53, 69, 48, 56, "worklet"], [53, 76, 48, 63], [53, 78, 48, 65, "fallbackEventName"], [53, 95, 48, 82], [53, 97, 48, 84, "viewTag"], [53, 104, 48, 91], [53, 105, 48, 92], [54, 8, 49, 6], [54, 12, 49, 10], [54, 13, 49, 11], [54, 14, 49, 12, "registrations"], [54, 27, 49, 25], [54, 28, 49, 26, "set"], [54, 31, 49, 29], [54, 32, 49, 30, "viewTag"], [54, 39, 49, 37], [54, 41, 49, 39], [54, 42, 49, 40, "newRegistration"], [54, 57, 49, 55], [54, 58, 49, 56], [54, 59, 49, 57], [55, 6, 50, 4], [56, 4, 51, 2], [57, 4, 52, 2, "unregisterFromEvents"], [57, 24, 52, 22, "unregisterFromEvents"], [57, 25, 52, 23, "viewTag"], [57, 32, 52, 30], [57, 34, 52, 32], [58, 6, 53, 4], [58, 10, 53, 8], [58, 11, 53, 9], [58, 12, 53, 10, "viewTags"], [58, 20, 53, 18], [58, 21, 53, 19, "delete"], [58, 27, 53, 25], [58, 28, 53, 26, "viewTag"], [58, 35, 53, 33], [58, 36, 53, 34], [59, 6, 54, 4], [59, 10, 54, 8], [59, 11, 54, 9], [59, 12, 54, 10, "registrations"], [59, 25, 54, 23], [59, 26, 54, 24, "get"], [59, 29, 54, 27], [59, 30, 54, 28, "viewTag"], [59, 37, 54, 35], [59, 38, 54, 36], [59, 40, 54, 38, "for<PERSON>ach"], [59, 47, 54, 45], [59, 48, 54, 46, "id"], [59, 50, 54, 48], [59, 54, 54, 52], [60, 8, 55, 6], [60, 12, 55, 6, "unregisterEventHandler"], [60, 40, 55, 28], [60, 42, 55, 29, "id"], [60, 44, 55, 31], [60, 45, 55, 32], [61, 6, 56, 4], [61, 7, 56, 5], [61, 8, 56, 6], [62, 6, 57, 4], [62, 10, 57, 8], [62, 11, 57, 9], [62, 12, 57, 10, "registrations"], [62, 25, 57, 23], [62, 26, 57, 24, "delete"], [62, 32, 57, 30], [62, 33, 57, 31, "viewTag"], [62, 40, 57, 38], [62, 41, 57, 39], [63, 4, 58, 2], [64, 2, 59, 0], [65, 2, 60, 0], [65, 8, 60, 6, "WorkletEventHandlerWeb"], [65, 30, 60, 28], [65, 31, 60, 29], [66, 4, 61, 2, "constructor"], [66, 15, 61, 13, "constructor"], [66, 16, 61, 14, "worklet"], [66, 23, 61, 21], [66, 25, 61, 23, "eventNames"], [66, 35, 61, 33], [66, 38, 61, 36], [66, 40, 61, 38], [66, 42, 61, 40], [67, 6, 62, 4], [67, 10, 62, 8], [67, 11, 62, 9, "worklet"], [67, 18, 62, 16], [67, 21, 62, 19, "worklet"], [67, 28, 62, 26], [68, 6, 63, 4], [68, 10, 63, 8], [68, 11, 63, 9, "eventNames"], [68, 21, 63, 19], [68, 24, 63, 22, "eventNames"], [68, 34, 63, 32], [69, 6, 64, 4], [69, 10, 64, 8], [69, 11, 64, 9, "listeners"], [69, 20, 64, 18], [69, 23, 64, 21], [69, 24, 64, 22], [69, 25, 64, 23], [70, 6, 65, 4], [70, 10, 65, 8], [70, 11, 65, 9, "setupWebListeners"], [70, 28, 65, 26], [70, 29, 65, 27], [70, 30, 65, 28], [71, 4, 66, 2], [72, 4, 67, 2, "setupWebListeners"], [72, 21, 67, 19, "setupWebListeners"], [72, 22, 67, 19], [72, 24, 67, 22], [73, 6, 68, 4], [73, 10, 68, 8], [73, 11, 68, 9, "listeners"], [73, 20, 68, 18], [73, 23, 68, 21], [73, 24, 68, 22], [73, 25, 68, 23], [74, 6, 69, 4], [74, 10, 69, 8], [74, 11, 69, 9, "eventNames"], [74, 21, 69, 19], [74, 22, 69, 20, "for<PERSON>ach"], [74, 29, 69, 27], [74, 30, 69, 28, "eventName"], [74, 39, 69, 37], [74, 43, 69, 41], [75, 8, 70, 6], [75, 12, 70, 10], [75, 13, 70, 11, "listeners"], [75, 22, 70, 20], [75, 23, 70, 21, "eventName"], [75, 32, 70, 30], [75, 33, 70, 31], [75, 36, 70, 34, "jsListener"], [75, 46, 70, 44], [75, 47, 70, 45, "eventName"], [75, 56, 70, 54], [75, 58, 70, 56], [75, 62, 70, 60], [75, 63, 70, 61, "worklet"], [75, 70, 70, 68], [75, 71, 70, 69], [76, 6, 71, 4], [76, 7, 71, 5], [76, 8, 71, 6], [77, 4, 72, 2], [78, 4, 73, 2, "updateEventHandler"], [78, 22, 73, 20, "updateEventHandler"], [78, 23, 73, 21, "newWorklet"], [78, 33, 73, 31], [78, 35, 73, 33, "newEvents"], [78, 44, 73, 42], [78, 46, 73, 44], [79, 6, 74, 4], [80, 6, 75, 4], [80, 10, 75, 8], [80, 11, 75, 9, "worklet"], [80, 18, 75, 16], [80, 21, 75, 19, "newWorklet"], [80, 31, 75, 29], [81, 6, 76, 4], [81, 10, 76, 8], [81, 11, 76, 9, "eventNames"], [81, 21, 76, 19], [81, 24, 76, 22, "newEvents"], [81, 33, 76, 31], [82, 6, 77, 4], [82, 10, 77, 8], [82, 11, 77, 9, "setupWebListeners"], [82, 28, 77, 26], [82, 29, 77, 27], [82, 30, 77, 28], [83, 4, 78, 2], [84, 4, 79, 2, "registerForEvents"], [84, 21, 79, 19, "registerForEvents"], [84, 22, 79, 20, "_viewTag"], [84, 30, 79, 28], [84, 32, 79, 30, "_fallbackEventName"], [84, 50, 79, 48], [84, 52, 79, 50], [85, 6, 80, 4], [86, 4, 80, 4], [87, 4, 82, 2, "unregisterFromEvents"], [87, 24, 82, 22, "unregisterFromEvents"], [87, 25, 82, 23, "_viewTag"], [87, 33, 82, 31], [87, 35, 82, 33], [88, 6, 83, 4], [89, 4, 83, 4], [90, 2, 85, 0], [91, 2, 86, 7], [91, 8, 86, 13, "WorkletEventHandler"], [91, 27, 86, 32], [91, 30, 86, 32, "exports"], [91, 37, 86, 32], [91, 38, 86, 32, "WorkletEventHandler"], [91, 57, 86, 32], [91, 60, 86, 35, "SHOULD_BE_USE_WEB"], [91, 77, 86, 52], [91, 80, 86, 55, "WorkletEventHandlerWeb"], [91, 102, 86, 77], [91, 105, 86, 80, "WorkletEventHandlerNative"], [91, 130, 86, 105], [92, 0, 86, 106], [92, 3]], "functionMap": {"names": ["<global>", "jsListener", "<anonymous>", "WorkletEventHandlerNative", "WorkletEventHandlerNative#constructor", "WorkletEventHandlerNative#updateEventHandler", "forEach$argument_0", "registrationIDs.forEach$argument_0", "Array.from.forEach$argument_0", "eventNames.map$argument_0", "WorkletEventHandlerNative#registerForEvents", "WorkletEventHandlerNative#unregisterFromEvents", "get.forEach$argument_0", "WorkletEventHandlerWeb", "WorkletEventHandlerWeb#constructor", "WorkletEventHandlerWeb#setupWebListeners", "eventNames.forEach$argument_0", "WorkletEventHandlerWeb#updateEventHandler", "WorkletEventHandlerWeb#registerForEvents", "WorkletEventHandlerWeb#unregisterFromEvents"], "mappings": "AAA;ACQ;SCC;GDK;CDC;AGC;ECG;GDK;EEC;gCCM;8BCC,gCD;KDE;uCGG;mDCC,+DD;KHE;GFC;EOC;iDDE,mEC;GPM;EQC;8CCE;KDE;GRE;CHC;AaC;ECC;GDK;EEC;4BCE;KDE;GFC;EIC;GJK;EKC;GLE;EMC;GNE;CbC"}}, "type": "js/module"}]}