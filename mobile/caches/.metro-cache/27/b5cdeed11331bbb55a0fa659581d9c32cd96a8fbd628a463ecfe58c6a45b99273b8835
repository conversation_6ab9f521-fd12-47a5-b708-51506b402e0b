{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.coerceDisplayMode = coerceDisplayMode;\n  exports.default = void 0;\n  var DisplayMode = Object.freeze({\n    VISIBLE: 1,\n    SUSPENDED: 2,\n    HIDDEN: 3\n  });\n  function coerceDisplayMode(value) {\n    switch (value) {\n      case DisplayMode.SUSPENDED:\n        return DisplayMode.SUSPENDED;\n      case DisplayMode.HIDDEN:\n        return DisplayMode.HIDDEN;\n      default:\n        return DisplayMode.VISIBLE;\n    }\n  }\n  var _default = exports.default = DisplayMode;\n});", "lineCount": 23, "map": [[7, 2, 15, 0], [7, 6, 15, 6, "DisplayMode"], [7, 17, 15, 47], [7, 20, 15, 50, "Object"], [7, 26, 15, 56], [7, 27, 15, 57, "freeze"], [7, 33, 15, 63], [7, 34, 15, 64], [8, 4, 16, 2, "VISIBLE"], [8, 11, 16, 9], [8, 13, 16, 11], [8, 14, 16, 12], [9, 4, 17, 2, "SUSPENDED"], [9, 13, 17, 11], [9, 15, 17, 13], [9, 16, 17, 14], [10, 4, 18, 2, "HIDDEN"], [10, 10, 18, 8], [10, 12, 18, 10], [11, 2, 19, 0], [11, 3, 19, 1], [11, 4, 19, 2], [12, 2, 21, 7], [12, 11, 21, 16, "coerceDisplayMode"], [12, 28, 21, 33, "coerceDisplayMode"], [12, 29, 21, 34, "value"], [12, 34, 21, 48], [12, 36, 21, 67], [13, 4, 22, 2], [13, 12, 22, 10, "value"], [13, 17, 22, 15], [14, 6, 23, 4], [14, 11, 23, 9, "DisplayMode"], [14, 22, 23, 20], [14, 23, 23, 21, "SUSPENDED"], [14, 32, 23, 30], [15, 8, 24, 6], [15, 15, 24, 13, "DisplayMode"], [15, 26, 24, 24], [15, 27, 24, 25, "SUSPENDED"], [15, 36, 24, 34], [16, 6, 25, 4], [16, 11, 25, 9, "DisplayMode"], [16, 22, 25, 20], [16, 23, 25, 21, "HIDDEN"], [16, 29, 25, 27], [17, 8, 26, 6], [17, 15, 26, 13, "DisplayMode"], [17, 26, 26, 24], [17, 27, 26, 25, "HIDDEN"], [17, 33, 26, 31], [18, 6, 27, 4], [19, 8, 28, 6], [19, 15, 28, 13, "DisplayMode"], [19, 26, 28, 24], [19, 27, 28, 25, "VISIBLE"], [19, 34, 28, 32], [20, 4, 29, 2], [21, 2, 30, 0], [22, 2, 30, 1], [22, 6, 30, 1, "_default"], [22, 14, 30, 1], [22, 17, 30, 1, "exports"], [22, 24, 30, 1], [22, 25, 30, 1, "default"], [22, 32, 30, 1], [22, 35, 32, 15, "DisplayMode"], [22, 46, 32, 26], [23, 0, 32, 26], [23, 3]], "functionMap": {"names": ["<global>", "coerceDisplayMode"], "mappings": "AAA;OCoB;CDS"}}, "type": "js/module"}]}