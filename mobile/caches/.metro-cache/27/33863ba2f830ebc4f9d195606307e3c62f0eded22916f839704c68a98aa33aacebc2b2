{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getDefaultSidebarWidth = void 0;\n  var APPROX_APP_BAR_HEIGHT = 56;\n  var DEFAULT_DRAWER_WIDTH = 360;\n  var getDefaultSidebarWidth = _ref => {\n    var width = _ref.width;\n    /**\n     * Default sidebar width is 360dp\n     * On screens smaller than 320dp, ideally the drawer would collapse to a tab bar\n     * https://m3.material.io/components/navigation-drawer/specs\n     */\n    if (width - APPROX_APP_BAR_HEIGHT <= 360) {\n      return width - APPROX_APP_BAR_HEIGHT;\n    }\n    return DEFAULT_DRAWER_WIDTH;\n  };\n  exports.getDefaultSidebarWidth = getDefaultSidebarWidth;\n});", "lineCount": 23, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getDefaultSidebarWidth"], [7, 32, 1, 13], [8, 2, 3, 0], [8, 6, 3, 6, "APPROX_APP_BAR_HEIGHT"], [8, 27, 3, 27], [8, 30, 3, 30], [8, 32, 3, 32], [9, 2, 4, 0], [9, 6, 4, 6, "DEFAULT_DRAWER_WIDTH"], [9, 26, 4, 26], [9, 29, 4, 29], [9, 32, 4, 32], [10, 2, 5, 7], [10, 6, 5, 13, "getDefaultSidebarWidth"], [10, 28, 5, 35], [10, 31, 5, 38, "_ref"], [10, 35, 5, 38], [10, 39, 7, 6], [11, 4, 7, 6], [11, 8, 6, 2, "width"], [11, 13, 6, 7], [11, 16, 6, 7, "_ref"], [11, 20, 6, 7], [11, 21, 6, 2, "width"], [11, 26, 6, 7], [12, 4, 8, 2], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 4, 13, 2], [17, 8, 13, 6, "width"], [17, 13, 13, 11], [17, 16, 13, 14, "APPROX_APP_BAR_HEIGHT"], [17, 37, 13, 35], [17, 41, 13, 39], [17, 44, 13, 42], [17, 46, 13, 44], [18, 6, 14, 4], [18, 13, 14, 11, "width"], [18, 18, 14, 16], [18, 21, 14, 19, "APPROX_APP_BAR_HEIGHT"], [18, 42, 14, 40], [19, 4, 15, 2], [20, 4, 16, 2], [20, 11, 16, 9, "DEFAULT_DRAWER_WIDTH"], [20, 31, 16, 29], [21, 2, 17, 0], [21, 3, 17, 1], [22, 2, 17, 2, "exports"], [22, 9, 17, 2], [22, 10, 17, 2, "getDefaultSidebarWidth"], [22, 32, 17, 2], [22, 35, 17, 2, "getDefaultSidebarWidth"], [22, 57, 17, 2], [23, 0, 17, 2], [23, 3]], "functionMap": {"names": ["<global>", "getDefaultSidebarWidth"], "mappings": "AAA;sCCI;CDY"}}, "type": "js/module"}]}