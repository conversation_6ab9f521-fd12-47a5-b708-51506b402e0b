{"dependencies": [{"name": "../../..", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 22, "index": 22}, "end": {"line": 1, "column": 68, "index": 68}}], "key": "wL8jGab/nAbOQ3QMY0RfnotQwFs=", "exportNames": ["*"], "contextParams": {"recursive": false, "filter": {"pattern": "^\\.\\/\\.env", "flags": ""}, "mode": "sync"}}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.env = void 0;\n  var dotEnvModules = require(_dependencyMap[0], \"../../..\");\n  var env = exports.env = !dotEnvModules.keys().length ? process.env : {\n    ...process.env,\n    ...['.env', '.env.development', '.env.local', '.env.development.local'].reduce((acc, file) => {\n      return {\n        ...acc,\n        ...(dotEnvModules(file)?.default ?? {})\n      };\n    }, {})\n  };\n});", "lineCount": 16, "map": [[6, 2, 1, 0], [6, 6, 1, 6, "dotEnvModules"], [6, 19, 1, 19], [6, 22, 1, 22, "require"], [6, 29, 1, 22], [6, 30, 1, 22, "_dependencyMap"], [6, 44, 1, 22], [6, 59, 1, 67], [6, 60, 1, 68], [7, 2, 3, 11], [7, 6, 3, 17, "env"], [7, 9, 3, 20], [7, 12, 3, 20, "exports"], [7, 19, 3, 20], [7, 20, 3, 20, "env"], [7, 23, 3, 20], [7, 26, 3, 23], [7, 27, 3, 24, "dotEnvModules"], [7, 40, 3, 37], [7, 41, 3, 38, "keys"], [7, 45, 3, 42], [7, 46, 3, 43], [7, 47, 3, 44], [7, 48, 3, 45, "length"], [7, 54, 3, 51], [7, 57, 3, 54, "process"], [7, 64, 3, 61], [7, 65, 3, 62, "env"], [7, 68, 3, 65], [7, 71, 3, 68], [8, 4, 3, 70], [8, 7, 3, 73, "process"], [8, 14, 3, 80], [8, 15, 3, 81, "env"], [8, 18, 3, 84], [9, 4, 3, 86], [9, 7, 3, 89], [9, 8, 3, 90], [9, 14, 3, 96], [9, 16, 3, 98], [9, 34, 3, 116], [9, 36, 3, 118], [9, 48, 3, 130], [9, 50, 3, 132], [9, 74, 3, 156], [9, 75, 3, 157], [9, 76, 3, 158, "reduce"], [9, 82, 3, 164], [9, 83, 3, 165], [9, 84, 3, 166, "acc"], [9, 87, 3, 169], [9, 89, 3, 171, "file"], [9, 93, 3, 175], [9, 98, 3, 180], [10, 6, 4, 6], [10, 13, 4, 13], [11, 8, 4, 15], [11, 11, 4, 18, "acc"], [11, 14, 4, 21], [12, 8, 4, 23], [12, 12, 4, 27, "dotEnvModules"], [12, 25, 4, 40], [12, 26, 4, 41, "file"], [12, 30, 4, 45], [12, 31, 4, 46], [12, 33, 4, 48, "default"], [12, 40, 4, 55], [12, 44, 4, 59], [12, 45, 4, 60], [12, 46, 4, 61], [13, 6, 4, 63], [13, 7, 4, 64], [14, 4, 5, 4], [14, 5, 5, 5], [14, 7, 5, 7], [14, 8, 5, 8], [14, 9, 5, 9], [15, 2, 5, 11], [15, 3, 5, 12], [16, 0, 5, 13], [16, 3]], "functionMap": {"names": ["<global>", "reduce$argument_0"], "mappings": "AAA;qKCE;KDE"}}, "type": "js/module"}]}