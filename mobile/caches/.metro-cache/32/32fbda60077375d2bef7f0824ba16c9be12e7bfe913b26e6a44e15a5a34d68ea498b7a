{"dependencies": [{"name": "./location/install", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 174}, "end": {"line": 8, "column": 28, "index": 202}}], "key": "XMeAUDI4b46BeNkZ23zAP7ftu8Y=", "exportNames": ["*"]}}, {"name": "./effects", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 254}, "end": {"line": 10, "column": 19, "index": 273}}], "key": "YKOg2zKbgaMjlKRRxfCzMu4nW0k=", "exportNames": ["*"]}}, {"name": "./async-require", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 301}, "end": {"line": 12, "column": 25, "index": 326}}], "key": "PeNRWexqbbNYxkpckBcXNC2Zzv0=", "exportNames": ["*"]}}, {"name": "@expo/metro-runtime/rsc/runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 328}, "end": {"line": 14, "column": 41, "index": 369}}], "key": "pkmIaud+eyyr54g5TgPBq62IZ5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  require(_dependencyMap[0], \"./location/install\");\n  require(_dependencyMap[1], \"./effects\");\n  require(_dependencyMap[2], \"./async-require\");\n  require(_dependencyMap[3], \"@expo/metro-runtime/rsc/runtime\");\n});", "lineCount": 6, "map": [[2, 2, 8, 0, "require"], [2, 9, 8, 0], [2, 10, 8, 0, "_dependencyMap"], [2, 24, 8, 0], [3, 2, 10, 0, "require"], [3, 9, 10, 0], [3, 10, 10, 0, "_dependencyMap"], [3, 24, 10, 0], [4, 2, 12, 0, "require"], [4, 9, 12, 0], [4, 10, 12, 0, "_dependencyMap"], [4, 24, 12, 0], [5, 2, 14, 0, "require"], [5, 9, 14, 0], [5, 10, 14, 0, "_dependencyMap"], [5, 24, 14, 0], [6, 0, 14, 41], [6, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}