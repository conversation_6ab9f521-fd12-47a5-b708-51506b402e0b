{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 61}, "end": {"line": 3, "column": 42, "index": 103}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../animation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 105}, "end": {"line": 5, "column": 49, "index": 154}}], "key": "a6n75g9KQy+KnMEjz15YzADQ7Hw=", "exportNames": ["*"]}}, {"name": "../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 376}, "end": {"line": 17, "column": 51, "index": 427}}], "key": "dQSfS57Pf/C96+Vvd1rktbJJov4=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 428}, "end": {"line": 18, "column": 65, "index": 493}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}, {"name": "../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 571}, "end": {"line": 20, "column": 44, "index": 615}}], "key": "ioSJ9iLOtXMo2uBjbVE14/NC9RQ=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 616}, "end": {"line": 21, "column": 60, "index": 676}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../processBoxShadow", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 677}, "end": {"line": 22, "column": 55, "index": 732}}], "key": "5U9DQyIl4l2fB1WnbYtSzF+WrRQ=", "exportNames": ["*"]}}, {"name": "../updateProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 733}, "end": {"line": 23, "column": 69, "index": 802}}], "key": "9+ynmbkEG/f9MdXTtZSVB+/M8dQ=", "exportNames": ["*"]}}, {"name": "../ViewDescriptorsSet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 868}, "end": {"line": 25, "column": 63, "index": 931}}], "key": "s50EVKb9uOc42WX2uciUIObJnp4=", "exportNames": ["*"]}}, {"name": "./useSharedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 1068}, "end": {"line": 33, "column": 50, "index": 1118}}], "key": "6yldmc0IldDX63zJLZukWRMfHng=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 1119}, "end": {"line": 39, "column": 17, "index": 1222}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedStyle = useAnimatedStyle;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _animation = require(_dependencyMap[1], \"../animation\");\n  var _commonTypes = require(_dependencyMap[2], \"../commonTypes\");\n  var _core = require(_dependencyMap[3], \"../core\");\n  var _errors = require(_dependencyMap[4], \"../errors\");\n  var _PlatformChecker = require(_dependencyMap[5], \"../PlatformChecker\");\n  var _processBoxShadow = require(_dependencyMap[6], \"../processBoxShadow\");\n  var _updateProps = require(_dependencyMap[7], \"../updateProps\");\n  var _ViewDescriptorsSet = require(_dependencyMap[8], \"../ViewDescriptorsSet\");\n  var _useSharedValue = require(_dependencyMap[9], \"./useSharedValue\");\n  var _utils = require(_dependencyMap[10], \"./utils\");\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  var _worklet_6358524786384_init_data = {\n    code: \"function prepareAnimation_reactNativeReanimated_useAnimatedStyleTs1(frameTimestamp,animatedProp,lastAnimation,lastValue){const prepareAnimation_reactNativeReanimated_useAnimatedStyleTs1=this._recur;if(Array.isArray(animatedProp)){animatedProp.forEach(function(prop,index){prepareAnimation_reactNativeReanimated_useAnimatedStyleTs1(frameTimestamp,prop,lastAnimation&&lastAnimation[index],lastValue&&lastValue[index]);});}if(typeof animatedProp==='object'&&animatedProp.onFrame){const animation=animatedProp;let value=animation.current;if(lastValue!==undefined&&lastValue!==null){if(typeof lastValue==='object'){if(lastValue.value!==undefined){value=lastValue.value;}else if(lastValue.onFrame!==undefined){if((lastAnimation===null||lastAnimation===void 0?void 0:lastAnimation.current)!==undefined){value=lastAnimation.current;}else if((lastValue===null||lastValue===void 0?void 0:lastValue.current)!==undefined){value=lastValue.current;}}}else{value=lastValue;}}animation.callStart=function(timestamp){animation.onStart(animation,value,timestamp,lastAnimation);};animation.callStart(frameTimestamp);animation.callStart=null;}else if(typeof animatedProp==='object'){Object.keys(animatedProp).forEach(function(key){return prepareAnimation_reactNativeReanimated_useAnimatedStyleTs1(frameTimestamp,animatedProp[key],lastAnimation&&lastAnimation[key],lastValue&&lastValue[key]);});}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"prepareAnimation_reactNativeReanimated_useAnimatedStyleTs1\\\",\\\"frameTimestamp\\\",\\\"animatedProp\\\",\\\"lastAnimation\\\",\\\"lastValue\\\",\\\"_recur\\\",\\\"Array\\\",\\\"isArray\\\",\\\"forEach\\\",\\\"prop\\\",\\\"index\\\",\\\"onFrame\\\",\\\"animation\\\",\\\"value\\\",\\\"current\\\",\\\"undefined\\\",\\\"callStart\\\",\\\"timestamp\\\",\\\"onStart\\\",\\\"Object\\\",\\\"keys\\\",\\\"key\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\\\"],\\\"mappings\\\":\\\"AA0DA,SAAAA,0DAGEA,CAAAC,cAED,CAAOC,YAAA,CAAAC,aAAA,CAAAC,SAAA,QAAAJ,0DAAA,MAAAK,MAAA,CAEN,GAAIC,KAAK,CAACC,OAAO,CAACL,YAAY,CAAC,CAAE,CAC/BA,YAAY,CAACM,OAAO,CAAC,SAACC,IAAI,CAAEC,KAAK,CAAK,CACpCV,0DAGmB,CAAAC,cAAc,CAAKQ,IACpC,CAAAN,aAAa,EAAAA,aACd,CAAAO,KAAA,EAAAN,SAAA,EAAAA,SAAA,CAAAM,KAAA,GACH,CAAC,CAAC,CAEJ,CACA,GAAI,MAAO,CAAAR,YAAY,GAAK,QAAQ,EAAIA,YAAY,CAACS,OAAO,CAAE,CAC5D,KAAM,CAAAC,SAAS,CAAGV,YAAY,CAE9B,GAAI,CAAAW,KAAK,CAAGD,SAAS,CAACE,OAAO,CAC7B,GAAIV,SAAS,GAAKW,SAAS,EAAIX,SAAS,GAAK,IAAI,CAAE,CACjD,GAAI,MAAO,CAAAA,SAAS,GAAK,QAAQ,CAAE,CACjC,GAAIA,SAAS,CAACS,KAAK,GAAKE,SAAS,CAAE,CAEjCF,KAAK,CAAGT,SAAS,CAACS,KAAK,CACzB,CAAC,IAAM,IAAIT,SAAS,CAACO,OAAO,GAAKI,SAAS,CAAE,CAC1C,GAAI,CAAAZ,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEW,OAAO,IAAKC,SAAS,CAAE,CAExCF,KAAK,CAAGV,aAAa,CAACW,OAAO,CAC/B,CAAC,IAAM,IAAI,CAAAV,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEU,OAAO,IAAKC,SAAS,CAAE,CAE3CF,KAAK,CAAGT,SAAS,CAACU,OAAO,CAC3B,CACF,CACF,CAAC,IAAM,CAELD,KAAK,CAAGT,SAAS,CACnB,CACF,CAEAQ,SAAS,CAACI,SAAS,CAAG,SAACC,SAAoB,CAAK,CAC9CL,SAAS,CAACM,OAAO,CAACN,SAAS,CAAEC,KAAK,CAAEI,SAAS,CAAEd,aAAa,CAAC,CAC/D,CAAC,CACDS,SAAS,CAACI,SAAS,CAACf,cAAc,CAAC,CACnCW,SAAS,CAACI,SAAS,CAAG,IAAI,CAC5B,CAAC,IAAM,IAAI,MAAO,CAAAd,YAAY,GAAK,QAAQ,CAAE,CAE3CiB,MAAM,CAACC,IAAI,CAAClB,YAAY,CAAC,CAACM,OAAO,CAAE,SAAAa,GAAG,QACpC,CAAArB,0DAGE,CAAAC,cAAiB,CAAAC,YAAkB,CAAAmB,GACnC,EAAAlB,aAAa,EAASA,aAE1B,CAAAkB,GAAA,EAAAjB,SAAA,EAAAA,SAAA,CAAAiB,GAAA,KAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var prepareAnimation = function () {\n    var _e = [new global.Error(), 1, -27];\n    var prepareAnimation = function (frameTimestamp, animatedProp, lastAnimation, lastValue) {\n      if (Array.isArray(animatedProp)) {\n        animatedProp.forEach((prop, index) => {\n          prepareAnimation(frameTimestamp, prop, lastAnimation && lastAnimation[index], lastValue && lastValue[index]);\n        });\n        // return animatedProp;\n      }\n      if (typeof animatedProp === 'object' && animatedProp.onFrame) {\n        var animation = animatedProp;\n        var value = animation.current;\n        if (lastValue !== undefined && lastValue !== null) {\n          if (typeof lastValue === 'object') {\n            if (lastValue.value !== undefined) {\n              // previously it was a shared value\n              value = lastValue.value;\n            } else if (lastValue.onFrame !== undefined) {\n              if (lastAnimation?.current !== undefined) {\n                // it was an animation before, copy its state\n                value = lastAnimation.current;\n              } else if (lastValue?.current !== undefined) {\n                // it was initialized\n                value = lastValue.current;\n              }\n            }\n          } else {\n            // previously it was a plain value, just set it as starting point\n            value = lastValue;\n          }\n        }\n        animation.callStart = timestamp => {\n          animation.onStart(animation, value, timestamp, lastAnimation);\n        };\n        animation.callStart(frameTimestamp);\n        animation.callStart = null;\n      } else if (typeof animatedProp === 'object') {\n        // it is an object\n        Object.keys(animatedProp).forEach(key => prepareAnimation(frameTimestamp, animatedProp[key], lastAnimation && lastAnimation[key], lastValue && lastValue[key]));\n      }\n    };\n    prepareAnimation.__closure = {};\n    prepareAnimation.__workletHash = 6358524786384;\n    prepareAnimation.__initData = _worklet_6358524786384_init_data;\n    prepareAnimation.__stackDetails = _e;\n    return prepareAnimation;\n  }();\n  var _worklet_11724700371151_init_data = {\n    code: \"function runAnimations_reactNativeReanimated_useAnimatedStyleTs2(animation,timestamp,key,result,animationsActive,forceCopyAnimation){const runAnimations_reactNativeReanimated_useAnimatedStyleTs2=this._recur;if(!animationsActive.value){return true;}if(Array.isArray(animation)){result[key]=[];let allFinished=true;forceCopyAnimation=key==='boxShadow';animation.forEach(function(entry,index){if(!runAnimations_reactNativeReanimated_useAnimatedStyleTs2(entry,timestamp,index,result[key],animationsActive,forceCopyAnimation)){allFinished=false;}});return allFinished;}else if(typeof animation==='object'&&animation.onFrame){let finished=true;if(!animation.finished){if(animation.callStart){animation.callStart(timestamp);animation.callStart=null;}finished=animation.onFrame(animation,timestamp);animation.timestamp=timestamp;if(finished){animation.finished=true;animation.callback&&animation.callback(true);}}if(forceCopyAnimation){result[key]={...animation.current};}else{result[key]=animation.current;}return finished;}else if(typeof animation==='object'){result[key]={};let allFinished=true;Object.keys(animation).forEach(function(k){if(!runAnimations_reactNativeReanimated_useAnimatedStyleTs2(animation[k],timestamp,k,result[key],animationsActive,forceCopyAnimation)){allFinished=false;}});return allFinished;}else{result[key]=animation;return true;}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runAnimations_reactNativeReanimated_useAnimatedStyleTs2\\\",\\\"animation\\\",\\\"timestamp\\\",\\\"key\\\",\\\"result\\\",\\\"animationsActive\\\",\\\"forceCopyAnimation\\\",\\\"_recur\\\",\\\"value\\\",\\\"Array\\\",\\\"isArray\\\",\\\"allFinished\\\",\\\"forEach\\\",\\\"entry\\\",\\\"index\\\",\\\"onFrame\\\",\\\"finished\\\",\\\"callStart\\\",\\\"callback\\\",\\\"current\\\",\\\"Object\\\",\\\"keys\\\",\\\"k\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\\\"],\\\"mappings\\\":\\\"AAsHA,SAAAA,uDAKEA,CAAAC,SAAA,CAAsCC,SACtC,CAAAC,GAAA,CAAAC,MACS,CAAAC,gBAAA,CAAAC,kBAAA,QAAAN,uDAAA,MAAAO,MAAA,CAET,GAAI,CAACF,gBAAgB,CAACG,KAAK,CAAE,CAC3B,MAAO,KAAI,CACb,CACA,GAAIC,KAAK,CAACC,OAAO,CAACT,SAAS,CAAC,CAAE,CAC5BG,MAAM,CAACD,GAAG,CAAC,CAAG,EAAE,CAChB,GAAI,CAAAQ,WAAW,CAAG,IAAI,CACtBL,kBAAkB,CAAGH,GAAG,GAAK,WAAW,CACxCF,SAAS,CAACW,OAAO,CAAC,SAACC,KAAK,CAAEC,KAAK,CAAK,CAClC,GACE,CAACd,uDAKC,CAAAa,KAAA,CAAAX,SACA,CAAAY,KAAA,CAAAV,MAAA,CACFD,GACA,EAAAE,gBAAA,CAAAC,kBAAA,GACAK,WAAW,CAAG,KAAK,CACrB,CACF,CAAC,CAAC,CACF,MAAO,CAAAA,WAAW,CACpB,CAAC,IAAM,IAAI,MAAO,CAAAV,SAAS,GAAK,QAAQ,EAAIA,SAAS,CAACc,OAAO,CAAE,CAC7D,GAAI,CAAAC,QAAQ,CAAG,IAAI,CACnB,GAAI,CAACf,SAAS,CAACe,QAAQ,CAAE,CACvB,GAAIf,SAAS,CAACgB,SAAS,CAAE,CACvBhB,SAAS,CAACgB,SAAS,CAACf,SAAS,CAAC,CAC9BD,SAAS,CAACgB,SAAS,CAAG,IAAI,CAC5B,CACAD,QAAQ,CAAGf,SAAS,CAACc,OAAO,CAACd,SAAS,CAAEC,SAAS,CAAC,CAClDD,SAAS,CAACC,SAAS,CAAGA,SAAS,CAC/B,GAAIc,QAAQ,CAAE,CACZf,SAAS,CAACe,QAAQ,CAAG,IAAI,CACzBf,SAAS,CAACiB,QAAQ,EAAIjB,SAAS,CAACiB,QAAQ,CAAC,IAAmB,CAAC,CAC/D,CACF,CAMA,GAAIZ,kBAAkB,CAAE,CACtBF,MAAM,CAACD,GAAG,CAAC,CAAG,CAAE,GAAGF,SAAS,CAACkB,OAAQ,CAAC,CACxC,CAAC,IAAM,CACLf,MAAM,CAACD,GAAG,CAAC,CAAGF,SAAS,CAACkB,OAAO,CACjC,CACA,MAAO,CAAAH,QAAQ,CACjB,CAAC,IAAM,IAAI,MAAO,CAAAf,SAAS,GAAK,QAAQ,CAAE,CACxCG,MAAM,CAACD,GAAG,CAAC,CAAG,CAAC,CAAC,CAChB,GAAI,CAAAQ,WAAW,CAAG,IAAI,CACtBS,MAAM,CAACC,IAAI,CAACpB,SAAS,CAAC,CAACW,OAAO,CAAE,SAAAU,CAAC,CAAK,CACpC,GACE,CAACtB,uDAKC,CAAAC,SAAA,CAAAqB,CAAA,EAAApB,SACA,CAAAoB,CAAA,CAAAlB,MAAA,CACFD,GACA,EAAAE,gBAAA,CAAAC,kBAAA,GACAK,WAAW,CAAG,KAAK,CACrB,CACF,CAAC,CAAC,CACF,MAAO,CAAAA,WAAW,CACpB,CAAC,IAAM,CACLP,MAAM,CAACD,GAAG,CAAC,CAAGF,SAAS,CACvB,MAAO,KAAI,CACb,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var runAnimations = function () {\n    var _e = [new global.Error(), 1, -27];\n    var runAnimations = function (animation, timestamp, key, result, animationsActive, forceCopyAnimation) {\n      if (!animationsActive.value) {\n        return true;\n      }\n      if (Array.isArray(animation)) {\n        result[key] = [];\n        var allFinished = true;\n        forceCopyAnimation = key === 'boxShadow';\n        animation.forEach((entry, index) => {\n          if (!runAnimations(entry, timestamp, index, result[key], animationsActive, forceCopyAnimation)) {\n            allFinished = false;\n          }\n        });\n        return allFinished;\n      } else if (typeof animation === 'object' && animation.onFrame) {\n        var finished = true;\n        if (!animation.finished) {\n          if (animation.callStart) {\n            animation.callStart(timestamp);\n            animation.callStart = null;\n          }\n          finished = animation.onFrame(animation, timestamp);\n          animation.timestamp = timestamp;\n          if (finished) {\n            animation.finished = true;\n            animation.callback && animation.callback(true /* finished */);\n          }\n        }\n        /*\n         * If `animation.current` is a boxShadow object, spread its properties into a new object\n         * to avoid modifying the original reference. This ensures when `newValues` has a nested color prop, it stays unparsed\n         * in rgba format, allowing the animation to run correctly.\n         */\n        if (forceCopyAnimation) {\n          result[key] = {\n            ...animation.current\n          };\n        } else {\n          result[key] = animation.current;\n        }\n        return finished;\n      } else if (typeof animation === 'object') {\n        result[key] = {};\n        var _allFinished = true;\n        Object.keys(animation).forEach(k => {\n          if (!runAnimations(animation[k], timestamp, k, result[key], animationsActive, forceCopyAnimation)) {\n            _allFinished = false;\n          }\n        });\n        return _allFinished;\n      } else {\n        result[key] = animation;\n        return true;\n      }\n    };\n    runAnimations.__closure = {};\n    runAnimations.__workletHash = 11724700371151;\n    runAnimations.__initData = _worklet_11724700371151_init_data;\n    runAnimations.__stackDetails = _e;\n    return runAnimations;\n  }();\n  var _worklet_6395962061635_init_data = {\n    code: \"function styleUpdater_reactNativeReanimated_useAnimatedStyleTs3(viewDescriptors,updater,state,animationsActive,isAnimatedProps=false){const{SHOULD_BE_USE_WEB,processBoxShadow,isAnimated,prepareAnimation,runAnimations,updateProps,shallowEqual}=this.__closure;var _state$animations,_updater;const animations=(_state$animations=state.animations)!==null&&_state$animations!==void 0?_state$animations:{};const newValues=(_updater=updater())!==null&&_updater!==void 0?_updater:{};const oldValues=state.last;const nonAnimatedNewValues={};let hasAnimations=false;let frameTimestamp;let hasNonAnimatedValues=false;if(!SHOULD_BE_USE_WEB&&newValues.boxShadow){processBoxShadow(newValues);}for(const key in newValues){const value=newValues[key];if(isAnimated(value)){frameTimestamp=global.__frameTimestamp||global._getAnimationTimestamp();prepareAnimation(frameTimestamp,value,animations[key],oldValues[key]);animations[key]=value;hasAnimations=true;}else{hasNonAnimatedValues=true;nonAnimatedNewValues[key]=value;delete animations[key];}}if(hasAnimations){const frame=function(timestamp){const{animations:animations,last:last,isAnimationCancelled:isAnimationCancelled}=state;if(isAnimationCancelled){state.isAnimationRunning=false;return;}const updates={};let allFinished=true;for(const propName in animations){const finished=runAnimations(animations[propName],timestamp,propName,updates,animationsActive);if(finished){if(Array.isArray(updates[propName])){updates[propName].forEach(function(obj){for(const prop in obj){last[propName][prop]=obj[prop];}});}else{last[propName]=updates[propName];}delete animations[propName];}else{allFinished=false;}}if(updates){updateProps(viewDescriptors,updates);}if(!allFinished){requestAnimationFrame(frame);}else{state.isAnimationRunning=false;}};state.animations=animations;if(!state.isAnimationRunning){state.isAnimationCancelled=false;state.isAnimationRunning=true;frame(frameTimestamp);}if(hasNonAnimatedValues){updateProps(viewDescriptors,nonAnimatedNewValues);}}else{state.isAnimationCancelled=true;state.animations=[];if(!shallowEqual(oldValues,newValues)){updateProps(viewDescriptors,newValues,isAnimatedProps);}}state.last=newValues;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"styleUpdater_reactNativeReanimated_useAnimatedStyleTs3\\\",\\\"viewDescriptors\\\",\\\"updater\\\",\\\"state\\\",\\\"animationsActive\\\",\\\"isAnimatedProps\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"processBoxShadow\\\",\\\"isAnimated\\\",\\\"prepareAnimation\\\",\\\"runAnimations\\\",\\\"updateProps\\\",\\\"shallowEqual\\\",\\\"__closure\\\",\\\"_state$animations\\\",\\\"_updater\\\",\\\"animations\\\",\\\"newValues\\\",\\\"oldValues\\\",\\\"last\\\",\\\"nonAnimatedNewValues\\\",\\\"hasAnimations\\\",\\\"frameTimestamp\\\",\\\"hasNonAnimatedValues\\\",\\\"boxShadow\\\",\\\"key\\\",\\\"value\\\",\\\"global\\\",\\\"__frameTimestamp\\\",\\\"_getAnimationTimestamp\\\",\\\"frame\\\",\\\"timestamp\\\",\\\"isAnimationCancelled\\\",\\\"isAnimationRunning\\\",\\\"updates\\\",\\\"allFinished\\\",\\\"propName\\\",\\\"finished\\\",\\\"Array\\\",\\\"isArray\\\",\\\"forEach\\\",\\\"obj\\\",\\\"prop\\\",\\\"requestAnimationFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\\\"],\\\"mappings\\\":\\\"AAsMA,SAAAA,sDAIEA,CAAAC,eACA,CAAAC,OAAA,CAAeC,KAAG,CACnBC,gBAAO,CAAAC,eAAA,cAAAC,iBAAA,CAAAC,gBAAA,CAAAC,UAAA,CAAAC,gBAAA,CAAAC,aAAA,CAAAC,WAAA,CAAAC,YAAA,OAAAC,SAAA,KAAAC,iBAAA,CAAAC,QAAA,CAEN,KAAM,CAAAC,UAAU,EAAAF,iBAAA,CAAGX,KAAK,CAACa,UAAU,UAAAF,iBAAA,UAAAA,iBAAA,CAAI,CAAC,CAAC,CACzC,KAAM,CAAAG,SAAS,EAAAF,QAAA,CAAGb,OAAO,CAAC,CAAC,UAAAa,QAAA,UAAAA,QAAA,CAAI,CAAC,CAAC,CACjC,KAAM,CAAAG,SAAS,CAAGf,KAAK,CAACgB,IAAI,CAC5B,KAAM,CAAAC,oBAAgC,CAAG,CAAC,CAAC,CAE3C,GAAI,CAAAC,aAAa,CAAG,KAAK,CACzB,GAAI,CAAAC,cAAkC,CACtC,GAAI,CAAAC,oBAAoB,CAAG,KAAK,CAChC,GAAI,CAACjB,iBAAiB,EAAIW,SAAS,CAACO,SAAS,CAAE,CAC7CjB,gBAAgB,CAACU,SAAS,CAAC,CAC7B,CACA,IAAK,KAAM,CAAAQ,GAAG,GAAI,CAAAR,SAAS,CAAE,CAC3B,KAAM,CAAAS,KAAK,CAAGT,SAAS,CAACQ,GAAG,CAAC,CAC5B,GAAIjB,UAAU,CAACkB,KAAK,CAAC,CAAE,CACrBJ,cAAc,CACZK,MAAM,CAACC,gBAAgB,EAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC,CAC5DpB,gBAAgB,CAACa,cAAc,CAAEI,KAAK,CAAEV,UAAU,CAACS,GAAG,CAAC,CAAEP,SAAS,CAACO,GAAG,CAAC,CAAC,CACxET,UAAU,CAACS,GAAG,CAAC,CAAGC,KAAK,CACvBL,aAAa,CAAG,IAAI,CACtB,CAAC,IAAM,CACLE,oBAAoB,CAAG,IAAI,CAC3BH,oBAAoB,CAACK,GAAG,CAAC,CAAGC,KAAK,CACjC,MAAO,CAAAV,UAAU,CAACS,GAAG,CAAC,CACxB,CACF,CAEA,GAAIJ,aAAa,CAAE,CACjB,KAAM,CAAAS,KAAK,CAAG,QAAAA,CAACC,SAAoB,CAAK,CAEtC,KAAM,CAAEf,UAAU,CAAVA,UAAU,CAAEG,IAAI,CAAJA,IAAI,CAAEa,oBAAA,CAAAA,oBAAqB,CAAC,CAAG7B,KAAK,CACxD,GAAI6B,oBAAoB,CAAE,CACxB7B,KAAK,CAAC8B,kBAAkB,CAAG,KAAK,CAChC,OACF,CAEA,KAAM,CAAAC,OAA2B,CAAG,CAAC,CAAC,CACtC,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtB,IAAK,KAAM,CAAAC,QAAQ,GAAI,CAAApB,UAAU,CAAE,CACjC,KAAM,CAAAqB,QAAQ,CAAG3B,aAAa,CAC5BM,UAAU,CAACoB,QAAQ,CAAC,CACpBL,SAAS,CACTK,QAAQ,CACRF,OAAO,CACP9B,gBACF,CAAC,CACD,GAAIiC,QAAQ,CAAE,CAOZ,GAAIC,KAAK,CAACC,OAAO,CAACL,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAE,CACpCF,OAAO,CAACE,QAAQ,CAAC,CAACI,OAAO,CAAC,SAACC,GAAe,CAAK,CAC7C,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAAD,GAAG,CAAE,CACtBtB,IAAI,CAACiB,QAAQ,CAAC,CAACM,IAAI,CAAC,CAAGD,GAAG,CAACC,IAAI,CAAC,CAClC,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLvB,IAAI,CAACiB,QAAQ,CAAC,CAAGF,OAAO,CAACE,QAAQ,CAAC,CACpC,CACA,MAAO,CAAApB,UAAU,CAACoB,QAAQ,CAAC,CAC7B,CAAC,IAAM,CACLD,WAAW,CAAG,KAAK,CACrB,CACF,CAEA,GAAID,OAAO,CAAE,CACXvB,WAAW,CAACV,eAAe,CAAEiC,OAAO,CAAC,CACvC,CAEA,GAAI,CAACC,WAAW,CAAE,CAChBQ,qBAAqB,CAACb,KAAK,CAAC,CAC9B,CAAC,IAAM,CACL3B,KAAK,CAAC8B,kBAAkB,CAAG,KAAK,CAClC,CACF,CAAC,CAED9B,KAAK,CAACa,UAAU,CAAGA,UAAU,CAC7B,GAAI,CAACb,KAAK,CAAC8B,kBAAkB,CAAE,CAC7B9B,KAAK,CAAC6B,oBAAoB,CAAG,KAAK,CAClC7B,KAAK,CAAC8B,kBAAkB,CAAG,IAAI,CAC/BH,KAAK,CAACR,cAAe,CAAC,CACxB,CAEA,GAAIC,oBAAoB,CAAE,CACxBZ,WAAW,CAACV,eAAe,CAAEmB,oBAAoB,CAAC,CACpD,CACF,CAAC,IAAM,CACLjB,KAAK,CAAC6B,oBAAoB,CAAG,IAAI,CACjC7B,KAAK,CAACa,UAAU,CAAG,EAAE,CAErB,GAAI,CAACJ,YAAY,CAACM,SAAS,CAAED,SAAS,CAAC,CAAE,CACvCN,WAAW,CAACV,eAAe,CAAEgB,SAAS,CAAEZ,eAAe,CAAC,CAC1D,CACF,CACAF,KAAK,CAACgB,IAAI,CAAGF,SAAS,CACxB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var styleUpdater = function () {\n    var _e = [new global.Error(), -8, -27];\n    var styleUpdater = function (viewDescriptors, updater, state, animationsActive) {\n      var isAnimatedProps = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n      var animations = state.animations ?? {};\n      var newValues = updater() ?? {};\n      var oldValues = state.last;\n      var nonAnimatedNewValues = {};\n      var hasAnimations = false;\n      var frameTimestamp;\n      var hasNonAnimatedValues = false;\n      if (!SHOULD_BE_USE_WEB && newValues.boxShadow) {\n        (0, _processBoxShadow.processBoxShadow)(newValues);\n      }\n      for (var key in newValues) {\n        var value = newValues[key];\n        if ((0, _utils.isAnimated)(value)) {\n          frameTimestamp = global.__frameTimestamp || global._getAnimationTimestamp();\n          prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);\n          animations[key] = value;\n          hasAnimations = true;\n        } else {\n          hasNonAnimatedValues = true;\n          nonAnimatedNewValues[key] = value;\n          delete animations[key];\n        }\n      }\n      if (hasAnimations) {\n        var frame = timestamp => {\n          // eslint-disable-next-line @typescript-eslint/no-shadow\n          var animations = state.animations,\n            last = state.last,\n            isAnimationCancelled = state.isAnimationCancelled;\n          if (isAnimationCancelled) {\n            state.isAnimationRunning = false;\n            return;\n          }\n          var updates = {};\n          var allFinished = true;\n          var _loop = function (propName) {\n            var finished = runAnimations(animations[propName], timestamp, propName, updates, animationsActive);\n            if (finished) {\n              /**\n               * If the animated prop is an array, we need to directly set each\n               * property (manually spread it). This prevents issues where the color\n               * prop might be incorrectly linked with its `toValue` and `current`\n               * states, causing abrupt transitions or 'jumps' in animation states.\n               */\n              if (Array.isArray(updates[propName])) {\n                updates[propName].forEach(obj => {\n                  for (var prop in obj) {\n                    last[propName][prop] = obj[prop];\n                  }\n                });\n              } else {\n                last[propName] = updates[propName];\n              }\n              delete animations[propName];\n            } else {\n              allFinished = false;\n            }\n          };\n          for (var propName in animations) {\n            _loop(propName);\n          }\n          if (updates) {\n            (0, _updateProps.updateProps)(viewDescriptors, updates);\n          }\n          if (!allFinished) {\n            requestAnimationFrame(frame);\n          } else {\n            state.isAnimationRunning = false;\n          }\n        };\n        state.animations = animations;\n        if (!state.isAnimationRunning) {\n          state.isAnimationCancelled = false;\n          state.isAnimationRunning = true;\n          frame(frameTimestamp);\n        }\n        if (hasNonAnimatedValues) {\n          (0, _updateProps.updateProps)(viewDescriptors, nonAnimatedNewValues);\n        }\n      } else {\n        state.isAnimationCancelled = true;\n        state.animations = [];\n        if (!(0, _utils.shallowEqual)(oldValues, newValues)) {\n          (0, _updateProps.updateProps)(viewDescriptors, newValues, isAnimatedProps);\n        }\n      }\n      state.last = newValues;\n    };\n    styleUpdater.__closure = {\n      SHOULD_BE_USE_WEB,\n      processBoxShadow: _processBoxShadow.processBoxShadow,\n      isAnimated: _utils.isAnimated,\n      prepareAnimation,\n      runAnimations,\n      updateProps: _updateProps.updateProps,\n      shallowEqual: _utils.shallowEqual\n    };\n    styleUpdater.__workletHash = 6395962061635;\n    styleUpdater.__initData = _worklet_6395962061635_init_data;\n    styleUpdater.__stackDetails = _e;\n    return styleUpdater;\n  }();\n  var _worklet_4651568536366_init_data = {\n    code: \"function jestStyleUpdater_reactNativeReanimated_useAnimatedStyleTs4(viewDescriptors,updater,state,animationsActive,animatedValues,adapters){const{isAnimated,prepareAnimation,runAnimations,updatePropsJestWrapper,shallowEqual}=this.__closure;var _state$animations,_updater;const animations=(_state$animations=state.animations)!==null&&_state$animations!==void 0?_state$animations:{};const newValues=(_updater=updater())!==null&&_updater!==void 0?_updater:{};const oldValues=state.last;let hasAnimations=false;let frameTimestamp;Object.keys(animations).forEach(function(key){const value=newValues[key];if(!isAnimated(value)){delete animations[key];}});Object.keys(newValues).forEach(function(key){const value=newValues[key];if(isAnimated(value)){frameTimestamp=global.__frameTimestamp||global._getAnimationTimestamp();prepareAnimation(frameTimestamp,value,animations[key],oldValues[key]);animations[key]=value;hasAnimations=true;}});function frame(timestamp){const{animations:animations,last:last,isAnimationCancelled:isAnimationCancelled}=state;if(isAnimationCancelled){state.isAnimationRunning=false;return;}const updates={};let allFinished=true;Object.keys(animations).forEach(function(propName){const finished=runAnimations(animations[propName],timestamp,propName,updates,animationsActive);if(finished){last[propName]=updates[propName];delete animations[propName];}else{allFinished=false;}});if(Object.keys(updates).length){updatePropsJestWrapper(viewDescriptors,updates,animatedValues,adapters);}if(!allFinished){requestAnimationFrame(frame);}else{state.isAnimationRunning=false;}}if(hasAnimations){state.animations=animations;if(!state.isAnimationRunning){state.isAnimationCancelled=false;state.isAnimationRunning=true;frame(frameTimestamp);}}else{state.isAnimationCancelled=true;state.animations=[];}state.last=newValues;if(!shallowEqual(oldValues,newValues)){updatePropsJestWrapper(viewDescriptors,newValues,animatedValues,adapters);}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"jestStyleUpdater_reactNativeReanimated_useAnimatedStyleTs4\\\",\\\"viewDescriptors\\\",\\\"updater\\\",\\\"state\\\",\\\"animationsActive\\\",\\\"animatedValues\\\",\\\"adapters\\\",\\\"isAnimated\\\",\\\"prepareAnimation\\\",\\\"runAnimations\\\",\\\"updatePropsJestWrapper\\\",\\\"shallowEqual\\\",\\\"__closure\\\",\\\"_state$animations\\\",\\\"_updater\\\",\\\"animations\\\",\\\"newValues\\\",\\\"oldValues\\\",\\\"last\\\",\\\"hasAnimations\\\",\\\"frameTimestamp\\\",\\\"Object\\\",\\\"keys\\\",\\\"forEach\\\",\\\"key\\\",\\\"value\\\",\\\"global\\\",\\\"__frameTimestamp\\\",\\\"_getAnimationTimestamp\\\",\\\"frame\\\",\\\"timestamp\\\",\\\"isAnimationCancelled\\\",\\\"isAnimationRunning\\\",\\\"updates\\\",\\\"allFinished\\\",\\\"propName\\\",\\\"finished\\\",\\\"length\\\",\\\"requestAnimationFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\\\"],\\\"mappings\\\":\\\"AAiTA,SAAAA,0DAIEA,CAAAC,eACA,CAAAC,OAAoD,CACpDC,KAAA,CAAAC,gBACM,CAAAC,cAAA,CAAAC,QAAA,QAAAC,UAAA,CAAAC,gBAAA,CAAAC,aAAA,CAAAC,sBAAA,CAAAC,YAAA,OAAAC,SAAA,KAAAC,iBAAA,CAAAC,QAAA,CAEN,KAAM,CAAAC,UAA8B,EAAAF,iBAAA,CAAGV,KAAK,CAACY,UAAU,UAAAF,iBAAA,UAAAA,iBAAA,CAAI,CAAC,CAAC,CAC7D,KAAM,CAAAG,SAAS,EAAAF,QAAA,CAAGZ,OAAO,CAAC,CAAC,UAAAY,QAAA,UAAAA,QAAA,CAAI,CAAC,CAAC,CACjC,KAAM,CAAAG,SAAS,CAAGd,KAAK,CAACe,IAAI,CAG5B,GAAI,CAAAC,aAAa,CAAG,KAAK,CACzB,GAAI,CAAAC,cAAkC,CACtCC,MAAM,CAACC,IAAI,CAACP,UAAU,CAAC,CAACQ,OAAO,CAAE,SAAAC,GAAG,CAAK,CACvC,KAAM,CAAAC,KAAK,CAAGT,SAAS,CAACQ,GAAG,CAAC,CAC5B,GAAI,CAACjB,UAAU,CAACkB,KAAK,CAAC,CAAE,CACtB,MAAO,CAAAV,UAAU,CAACS,GAAG,CAAC,CACxB,CACF,CAAC,CAAC,CACFH,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACO,OAAO,CAAE,SAAAC,GAAG,CAAK,CACtC,KAAM,CAAAC,KAAK,CAAGT,SAAS,CAACQ,GAAG,CAAC,CAC5B,GAAIjB,UAAU,CAACkB,KAAK,CAAC,CAAE,CACrBL,cAAc,CACZM,MAAM,CAACC,gBAAgB,EAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC,CAC5DpB,gBAAgB,CAACY,cAAc,CAAEK,KAAK,CAAEV,UAAU,CAACS,GAAG,CAAC,CAAEP,SAAS,CAACO,GAAG,CAAC,CAAC,CACxET,UAAU,CAACS,GAAG,CAAC,CAAGC,KAAK,CACvBN,aAAa,CAAG,IAAI,CACtB,CACF,CAAC,CAAC,CAEF,QAAS,CAAAU,KAAKA,CAACC,SAAoB,CAAE,CAEnC,KAAM,CAAEf,UAAU,CAAVA,UAAU,CAAEG,IAAI,CAAJA,IAAI,CAAEa,oBAAA,CAAAA,oBAAqB,CAAC,CAAG5B,KAAK,CACxD,GAAI4B,oBAAoB,CAAE,CACxB5B,KAAK,CAAC6B,kBAAkB,CAAG,KAAK,CAChC,OACF,CAEA,KAAM,CAAAC,OAA2B,CAAG,CAAC,CAAC,CACtC,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtBb,MAAM,CAACC,IAAI,CAACP,UAAU,CAAC,CAACQ,OAAO,CAAE,SAAAY,QAAQ,CAAK,CAC5C,KAAM,CAAAC,QAAQ,CAAG3B,aAAa,CAC5BM,UAAU,CAACoB,QAAQ,CAAC,CACpBL,SAAS,CACTK,QAAQ,CACRF,OAAO,CACP7B,gBACF,CAAC,CACD,GAAIgC,QAAQ,CAAE,CACZlB,IAAI,CAACiB,QAAQ,CAAC,CAAGF,OAAO,CAACE,QAAQ,CAAC,CAClC,MAAO,CAAApB,UAAU,CAACoB,QAAQ,CAAC,CAC7B,CAAC,IAAM,CACLD,WAAW,CAAG,KAAK,CACrB,CACF,CAAC,CAAC,CAEF,GAAIb,MAAM,CAACC,IAAI,CAACW,OAAO,CAAC,CAACI,MAAM,CAAE,CAC/B3B,sBAAsB,CACpBT,eAAe,CACfgC,OAAO,CACP5B,cAAc,CACdC,QACF,CAAC,CACH,CAEA,GAAI,CAAC4B,WAAW,CAAE,CAChBI,qBAAqB,CAACT,KAAK,CAAC,CAC9B,CAAC,IAAM,CACL1B,KAAK,CAAC6B,kBAAkB,CAAG,KAAK,CAClC,CACF,CAEA,GAAIb,aAAa,CAAE,CACjBhB,KAAK,CAACY,UAAU,CAAGA,UAAU,CAC7B,GAAI,CAACZ,KAAK,CAAC6B,kBAAkB,CAAE,CAC7B7B,KAAK,CAAC4B,oBAAoB,CAAG,KAAK,CAClC5B,KAAK,CAAC6B,kBAAkB,CAAG,IAAI,CAC/BH,KAAK,CAACT,cAAe,CAAC,CACxB,CACF,CAAC,IAAM,CACLjB,KAAK,CAAC4B,oBAAoB,CAAG,IAAI,CACjC5B,KAAK,CAACY,UAAU,CAAG,EAAE,CACvB,CAGAZ,KAAK,CAACe,IAAI,CAAGF,SAAS,CAEtB,GAAI,CAACL,YAAY,CAACM,SAAS,CAAED,SAAS,CAAC,CAAE,CACvCN,sBAAsB,CACpBT,eAAe,CACfe,SAAS,CACTX,cAAc,CACdC,QACF,CAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var jestStyleUpdater = function () {\n    var _e = [new global.Error(), -6, -27];\n    var jestStyleUpdater = function (viewDescriptors, updater, state, animationsActive, animatedValues, adapters) {\n      var animations = state.animations ?? {};\n      var newValues = updater() ?? {};\n      var oldValues = state.last;\n\n      // extract animated props\n      var hasAnimations = false;\n      var frameTimestamp;\n      Object.keys(animations).forEach(key => {\n        var value = newValues[key];\n        if (!(0, _utils.isAnimated)(value)) {\n          delete animations[key];\n        }\n      });\n      Object.keys(newValues).forEach(key => {\n        var value = newValues[key];\n        if ((0, _utils.isAnimated)(value)) {\n          frameTimestamp = global.__frameTimestamp || global._getAnimationTimestamp();\n          prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);\n          animations[key] = value;\n          hasAnimations = true;\n        }\n      });\n      function frame(timestamp) {\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        var animations = state.animations,\n          last = state.last,\n          isAnimationCancelled = state.isAnimationCancelled;\n        if (isAnimationCancelled) {\n          state.isAnimationRunning = false;\n          return;\n        }\n        var updates = {};\n        var allFinished = true;\n        Object.keys(animations).forEach(propName => {\n          var finished = runAnimations(animations[propName], timestamp, propName, updates, animationsActive);\n          if (finished) {\n            last[propName] = updates[propName];\n            delete animations[propName];\n          } else {\n            allFinished = false;\n          }\n        });\n        if (Object.keys(updates).length) {\n          (0, _updateProps.updatePropsJestWrapper)(viewDescriptors, updates, animatedValues, adapters);\n        }\n        if (!allFinished) {\n          requestAnimationFrame(frame);\n        } else {\n          state.isAnimationRunning = false;\n        }\n      }\n      if (hasAnimations) {\n        state.animations = animations;\n        if (!state.isAnimationRunning) {\n          state.isAnimationCancelled = false;\n          state.isAnimationRunning = true;\n          frame(frameTimestamp);\n        }\n      } else {\n        state.isAnimationCancelled = true;\n        state.animations = [];\n      }\n\n      // calculate diff\n      state.last = newValues;\n      if (!(0, _utils.shallowEqual)(oldValues, newValues)) {\n        (0, _updateProps.updatePropsJestWrapper)(viewDescriptors, newValues, animatedValues, adapters);\n      }\n    };\n    jestStyleUpdater.__closure = {\n      isAnimated: _utils.isAnimated,\n      prepareAnimation,\n      runAnimations,\n      updatePropsJestWrapper: _updateProps.updatePropsJestWrapper,\n      shallowEqual: _utils.shallowEqual\n    };\n    jestStyleUpdater.__workletHash = 4651568536366;\n    jestStyleUpdater.__initData = _worklet_4651568536366_init_data;\n    jestStyleUpdater.__stackDetails = _e;\n    return jestStyleUpdater;\n  }(); // check for invalid usage of shared values in returned object\n  function checkSharedValueUsage(prop, currentKey) {\n    if (Array.isArray(prop)) {\n      // if it's an array (i.ex. transform) validate all its elements\n      for (var element of prop) {\n        checkSharedValueUsage(element, currentKey);\n      }\n    } else if (typeof prop === 'object' && prop !== null && prop.value === undefined) {\n      // if it's a nested object, run validation for all its props\n      for (var key of Object.keys(prop)) {\n        checkSharedValueUsage(prop[key], key);\n      }\n    } else if (currentKey !== undefined && typeof prop === 'object' && prop !== null && prop.value !== undefined) {\n      // if shared value is passed instead of its value, throw an error\n      throw new _errors.ReanimatedError(`Invalid value passed to \\`${currentKey}\\`, maybe you forgot to use \\`.value\\`?`);\n    }\n  }\n\n  /**\n   * Lets you create a styles object, similar to StyleSheet styles, which can be\n   * animated using shared values.\n   *\n   * @param updater - A function returning an object with style properties you\n   *   want to animate.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @returns An animated style object which has to be passed to the `style`\n   *   property of an Animated component you want to animate.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedStyle\n   */\n  // You cannot pass Shared Values to `useAnimatedStyle` directly.\n  // @ts-expect-error This overload is required by our API.\n  var _worklet_9173457873940_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedStyleTs5(){const{updater,adaptersArray}=this.__closure;const newValues=updater();adaptersArray.forEach(function(adapter){adapter(newValues);});return newValues;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedStyleTs5\\\",\\\"updater\\\",\\\"adaptersArray\\\",\\\"__closure\\\",\\\"newValues\\\",\\\"forEach\\\",\\\"adapter\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\\\"],\\\"mappings\\\":\\\"AAqhBmB,SAAAA,yCAAMA,CAAA,QAAAC,OAAA,CAAAC,aAAA,OAAAC,SAAA,CAEjB,KAAM,CAAAC,SAAS,CAAGH,OAAO,CAAC,CAAC,CAC3BC,aAAa,CAACG,OAAO,CAAE,SAAAC,OAAO,CAAK,CACjCA,OAAO,CAACF,SAAoC,CAAC,CAC/C,CAAC,CAAC,CACF,MAAO,CAAAA,SAAS,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_5314192339077_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedStyleTs6(){const{jestStyleUpdater,shareableViewDescriptors,updater,remoteState,areAnimationsActive,jestAnimatedValues,adaptersArray}=this.__closure;jestStyleUpdater(shareableViewDescriptors,updater,remoteState,areAnimationsActive,jestAnimatedValues,adaptersArray);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedStyleTs6\\\",\\\"jestStyleUpdater\\\",\\\"shareableViewDescriptors\\\",\\\"updater\\\",\\\"remoteState\\\",\\\"areAnimationsActive\\\",\\\"jestAnimatedValues\\\",\\\"adaptersArray\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\\\"],\\\"mappings\\\":\\\"AAgiBY,SAAAA,yCAAMA,CAAA,QAAAC,gBAAA,CAAAC,wBAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAC,mBAAA,CAAAC,kBAAA,CAAAC,aAAA,OAAAC,SAAA,CAEVP,gBAAgB,CACdC,wBAAwB,CACxBC,OAAO,CACPC,WAAW,CACXC,mBAAmB,CACnBC,kBAAkB,CAClBC,aACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_245709520516_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedStyleTs7(){const{styleUpdater,shareableViewDescriptors,updaterFn,remoteState,areAnimationsActive,isAnimatedProps}=this.__closure;styleUpdater(shareableViewDescriptors,updaterFn,remoteState,areAnimationsActive,isAnimatedProps);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedStyleTs7\\\",\\\"styleUpdater\\\",\\\"shareableViewDescriptors\\\",\\\"updaterFn\\\",\\\"remoteState\\\",\\\"areAnimationsActive\\\",\\\"isAnimatedProps\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedStyle.ts\\\"],\\\"mappings\\\":\\\"AA4iBY,SAAAA,yCAAMA,CAAA,QAAAC,YAAA,CAAAC,wBAAA,CAAAC,SAAA,CAAAC,WAAA,CAAAC,mBAAA,CAAAC,eAAA,OAAAC,SAAA,CAEVN,YAAY,CACVC,wBAAwB,CACxBC,SAAS,CACTC,WAAW,CACXC,mBAAmB,CACnBC,eACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedStyle(updater, dependencies, adapters) {\n    var isAnimatedProps = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n    var animatedUpdaterData = (0, _react.useRef)(null);\n    var inputs = Object.values(updater.__closure ?? {});\n    if (SHOULD_BE_USE_WEB) {\n      if (!inputs.length && dependencies?.length) {\n        // let web work without a Babel plugin\n        inputs = dependencies;\n      }\n      if (__DEV__ && !inputs.length && !dependencies && !(0, _commonTypes.isWorkletFunction)(updater)) {\n        throw new _errors.ReanimatedError(`\\`useAnimatedStyle\\` was used without a dependency array or Babel plugin. Please explicitly pass a dependency array, or enable the Babel plugin.\nFor more, see the docs: \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/web-support#web-without-the-babel-plugin\\`.`);\n      }\n    }\n    var adaptersArray = adapters ? Array.isArray(adapters) ? adapters : [adapters] : [];\n    var adaptersHash = adapters ? (0, _utils.buildWorkletsHash)(adaptersArray) : null;\n    var areAnimationsActive = (0, _useSharedValue.useSharedValue)(true);\n    var jestAnimatedValues = (0, _react.useRef)({});\n\n    // build dependencies\n    if (!dependencies) {\n      dependencies = [...inputs, updater.__workletHash];\n    } else {\n      dependencies.push(updater.__workletHash);\n    }\n    adaptersHash && dependencies.push(adaptersHash);\n    if (!animatedUpdaterData.current) {\n      var initialStyle = (0, _animation.initialUpdaterRun)(updater);\n      if (__DEV__) {\n        (0, _utils.validateAnimatedStyles)(initialStyle);\n      }\n      animatedUpdaterData.current = {\n        initial: {\n          value: initialStyle,\n          updater\n        },\n        remoteState: (0, _core.makeShareable)({\n          last: initialStyle,\n          animations: {},\n          isAnimationCancelled: false,\n          isAnimationRunning: false\n        }),\n        viewDescriptors: (0, _ViewDescriptorsSet.makeViewDescriptorsSet)()\n      };\n    }\n    var _animatedUpdaterData$ = animatedUpdaterData.current,\n      initial = _animatedUpdaterData$.initial,\n      remoteState = _animatedUpdaterData$.remoteState,\n      viewDescriptors = _animatedUpdaterData$.viewDescriptors;\n    var shareableViewDescriptors = viewDescriptors.shareableViewDescriptors;\n    dependencies.push(shareableViewDescriptors);\n    (0, _react.useEffect)(() => {\n      var fun;\n      var updaterFn = updater;\n      if (adapters) {\n        updaterFn = function () {\n          var _e = [new global.Error(), -3, -27];\n          var reactNativeReanimated_useAnimatedStyleTs5 = function () {\n            var newValues = updater();\n            adaptersArray.forEach(adapter => {\n              adapter(newValues);\n            });\n            return newValues;\n          };\n          reactNativeReanimated_useAnimatedStyleTs5.__closure = {\n            updater,\n            adaptersArray\n          };\n          reactNativeReanimated_useAnimatedStyleTs5.__workletHash = 9173457873940;\n          reactNativeReanimated_useAnimatedStyleTs5.__initData = _worklet_9173457873940_init_data;\n          reactNativeReanimated_useAnimatedStyleTs5.__stackDetails = _e;\n          return reactNativeReanimated_useAnimatedStyleTs5;\n        }();\n      }\n      if ((0, _PlatformChecker.isJest)()) {\n        fun = function () {\n          var _e = [new global.Error(), -8, -27];\n          var reactNativeReanimated_useAnimatedStyleTs6 = function () {\n            jestStyleUpdater(shareableViewDescriptors, updater, remoteState, areAnimationsActive, jestAnimatedValues, adaptersArray);\n          };\n          reactNativeReanimated_useAnimatedStyleTs6.__closure = {\n            jestStyleUpdater,\n            shareableViewDescriptors,\n            updater,\n            remoteState,\n            areAnimationsActive,\n            jestAnimatedValues,\n            adaptersArray\n          };\n          reactNativeReanimated_useAnimatedStyleTs6.__workletHash = 5314192339077;\n          reactNativeReanimated_useAnimatedStyleTs6.__initData = _worklet_5314192339077_init_data;\n          reactNativeReanimated_useAnimatedStyleTs6.__stackDetails = _e;\n          return reactNativeReanimated_useAnimatedStyleTs6;\n        }();\n      } else {\n        fun = function () {\n          var _e = [new global.Error(), -7, -27];\n          var reactNativeReanimated_useAnimatedStyleTs7 = function () {\n            styleUpdater(shareableViewDescriptors, updaterFn, remoteState, areAnimationsActive, isAnimatedProps);\n          };\n          reactNativeReanimated_useAnimatedStyleTs7.__closure = {\n            styleUpdater,\n            shareableViewDescriptors,\n            updaterFn,\n            remoteState,\n            areAnimationsActive,\n            isAnimatedProps\n          };\n          reactNativeReanimated_useAnimatedStyleTs7.__workletHash = 245709520516;\n          reactNativeReanimated_useAnimatedStyleTs7.__initData = _worklet_245709520516_init_data;\n          reactNativeReanimated_useAnimatedStyleTs7.__stackDetails = _e;\n          return reactNativeReanimated_useAnimatedStyleTs7;\n        }();\n      }\n      var mapperId = (0, _core.startMapper)(fun, inputs);\n      return () => {\n        (0, _core.stopMapper)(mapperId);\n      };\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, dependencies);\n    (0, _react.useEffect)(() => {\n      areAnimationsActive.value = true;\n      return () => {\n        areAnimationsActive.value = false;\n      };\n    }, [areAnimationsActive]);\n    checkSharedValueUsage(initial.value);\n    var animatedStyleHandle = (0, _react.useRef)(null);\n    if (!animatedStyleHandle.current) {\n      animatedStyleHandle.current = (0, _PlatformChecker.isJest)() ? {\n        viewDescriptors,\n        initial,\n        jestAnimatedValues\n      } : {\n        viewDescriptors,\n        initial\n      };\n    }\n    return animatedStyleHandle.current;\n  }\n});", "lineCount": 533, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedStyle"], [7, 26, 1, 13], [7, 29, 1, 13, "useAnimatedStyle"], [7, 45, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_animation"], [9, 16, 5, 0], [9, 19, 5, 0, "require"], [9, 26, 5, 0], [9, 27, 5, 0, "_dependencyMap"], [9, 41, 5, 0], [10, 2, 17, 0], [10, 6, 17, 0, "_commonTypes"], [10, 18, 17, 0], [10, 21, 17, 0, "require"], [10, 28, 17, 0], [10, 29, 17, 0, "_dependencyMap"], [10, 43, 17, 0], [11, 2, 18, 0], [11, 6, 18, 0, "_core"], [11, 11, 18, 0], [11, 14, 18, 0, "require"], [11, 21, 18, 0], [11, 22, 18, 0, "_dependencyMap"], [11, 36, 18, 0], [12, 2, 20, 0], [12, 6, 20, 0, "_errors"], [12, 13, 20, 0], [12, 16, 20, 0, "require"], [12, 23, 20, 0], [12, 24, 20, 0, "_dependencyMap"], [12, 38, 20, 0], [13, 2, 21, 0], [13, 6, 21, 0, "_PlatformChecker"], [13, 22, 21, 0], [13, 25, 21, 0, "require"], [13, 32, 21, 0], [13, 33, 21, 0, "_dependencyMap"], [13, 47, 21, 0], [14, 2, 22, 0], [14, 6, 22, 0, "_processBoxShadow"], [14, 23, 22, 0], [14, 26, 22, 0, "require"], [14, 33, 22, 0], [14, 34, 22, 0, "_dependencyMap"], [14, 48, 22, 0], [15, 2, 23, 0], [15, 6, 23, 0, "_updateProps"], [15, 18, 23, 0], [15, 21, 23, 0, "require"], [15, 28, 23, 0], [15, 29, 23, 0, "_dependencyMap"], [15, 43, 23, 0], [16, 2, 25, 0], [16, 6, 25, 0, "_ViewDescriptorsSet"], [16, 25, 25, 0], [16, 28, 25, 0, "require"], [16, 35, 25, 0], [16, 36, 25, 0, "_dependencyMap"], [16, 50, 25, 0], [17, 2, 33, 0], [17, 6, 33, 0, "_useSharedValue"], [17, 21, 33, 0], [17, 24, 33, 0, "require"], [17, 31, 33, 0], [17, 32, 33, 0, "_dependencyMap"], [17, 46, 33, 0], [18, 2, 34, 0], [18, 6, 34, 0, "_utils"], [18, 12, 34, 0], [18, 15, 34, 0, "require"], [18, 22, 34, 0], [18, 23, 34, 0, "_dependencyMap"], [18, 37, 34, 0], [19, 2, 41, 0], [19, 6, 41, 6, "SHOULD_BE_USE_WEB"], [19, 23, 41, 23], [19, 26, 41, 26], [19, 30, 41, 26, "shouldBeUseWeb"], [19, 61, 41, 40], [19, 63, 41, 41], [19, 64, 41, 42], [20, 2, 41, 43], [20, 6, 41, 43, "_worklet_6358524786384_init_data"], [20, 38, 41, 43], [21, 4, 41, 43, "code"], [21, 8, 41, 43], [22, 4, 41, 43, "location"], [22, 12, 41, 43], [23, 4, 41, 43, "sourceMap"], [23, 13, 41, 43], [24, 4, 41, 43, "version"], [24, 11, 41, 43], [25, 2, 41, 43], [26, 2, 41, 43], [26, 6, 41, 43, "prepareAnimation"], [26, 22, 41, 43], [26, 25, 59, 0], [27, 4, 59, 0], [27, 8, 59, 0, "_e"], [27, 10, 59, 0], [27, 18, 59, 0, "global"], [27, 24, 59, 0], [27, 25, 59, 0, "Error"], [27, 30, 59, 0], [28, 4, 59, 0], [28, 8, 59, 0, "prepareAnimation"], [28, 24, 59, 0], [28, 36, 59, 0, "prepareAnimation"], [28, 37, 60, 2, "frameTimestamp"], [28, 51, 60, 24], [28, 53, 61, 2, "animatedProp"], [28, 65, 61, 34], [28, 67, 62, 2, "lastAnimation"], [28, 80, 62, 35], [28, 82, 63, 2, "lastValue"], [28, 91, 63, 31], [28, 93, 64, 8], [29, 6, 66, 2], [29, 10, 66, 6, "Array"], [29, 15, 66, 11], [29, 16, 66, 12, "isArray"], [29, 23, 66, 19], [29, 24, 66, 20, "animatedProp"], [29, 36, 66, 32], [29, 37, 66, 33], [29, 39, 66, 35], [30, 8, 67, 4, "animatedProp"], [30, 20, 67, 16], [30, 21, 67, 17, "for<PERSON>ach"], [30, 28, 67, 24], [30, 29, 67, 25], [30, 30, 67, 26, "prop"], [30, 34, 67, 30], [30, 36, 67, 32, "index"], [30, 41, 67, 37], [30, 46, 67, 42], [31, 10, 68, 6, "prepareAnimation"], [31, 26, 68, 22], [31, 27, 69, 8, "frameTimestamp"], [31, 41, 69, 22], [31, 43, 70, 8, "prop"], [31, 47, 70, 12], [31, 49, 71, 8, "lastAnimation"], [31, 62, 71, 21], [31, 66, 71, 25, "lastAnimation"], [31, 79, 71, 38], [31, 80, 71, 39, "index"], [31, 85, 71, 44], [31, 86, 71, 45], [31, 88, 72, 8, "lastValue"], [31, 97, 72, 17], [31, 101, 72, 21, "lastValue"], [31, 110, 72, 30], [31, 111, 72, 31, "index"], [31, 116, 72, 36], [31, 117, 73, 6], [31, 118, 73, 7], [32, 8, 74, 4], [32, 9, 74, 5], [32, 10, 74, 6], [33, 8, 75, 4], [34, 6, 76, 2], [35, 6, 77, 2], [35, 10, 77, 6], [35, 17, 77, 13, "animatedProp"], [35, 29, 77, 25], [35, 34, 77, 30], [35, 42, 77, 38], [35, 46, 77, 42, "animatedProp"], [35, 58, 77, 54], [35, 59, 77, 55, "onFrame"], [35, 66, 77, 62], [35, 68, 77, 64], [36, 8, 78, 4], [36, 12, 78, 10, "animation"], [36, 21, 78, 19], [36, 24, 78, 22, "animatedProp"], [36, 36, 78, 34], [37, 8, 80, 4], [37, 12, 80, 8, "value"], [37, 17, 80, 13], [37, 20, 80, 16, "animation"], [37, 29, 80, 25], [37, 30, 80, 26, "current"], [37, 37, 80, 33], [38, 8, 81, 4], [38, 12, 81, 8, "lastValue"], [38, 21, 81, 17], [38, 26, 81, 22, "undefined"], [38, 35, 81, 31], [38, 39, 81, 35, "lastValue"], [38, 48, 81, 44], [38, 53, 81, 49], [38, 57, 81, 53], [38, 59, 81, 55], [39, 10, 82, 6], [39, 14, 82, 10], [39, 21, 82, 17, "lastValue"], [39, 30, 82, 26], [39, 35, 82, 31], [39, 43, 82, 39], [39, 45, 82, 41], [40, 12, 83, 8], [40, 16, 83, 12, "lastValue"], [40, 25, 83, 21], [40, 26, 83, 22, "value"], [40, 31, 83, 27], [40, 36, 83, 32, "undefined"], [40, 45, 83, 41], [40, 47, 83, 43], [41, 14, 84, 10], [42, 14, 85, 10, "value"], [42, 19, 85, 15], [42, 22, 85, 18, "lastValue"], [42, 31, 85, 27], [42, 32, 85, 28, "value"], [42, 37, 85, 33], [43, 12, 86, 8], [43, 13, 86, 9], [43, 19, 86, 15], [43, 23, 86, 19, "lastValue"], [43, 32, 86, 28], [43, 33, 86, 29, "onFrame"], [43, 40, 86, 36], [43, 45, 86, 41, "undefined"], [43, 54, 86, 50], [43, 56, 86, 52], [44, 14, 87, 10], [44, 18, 87, 14, "lastAnimation"], [44, 31, 87, 27], [44, 33, 87, 29, "current"], [44, 40, 87, 36], [44, 45, 87, 41, "undefined"], [44, 54, 87, 50], [44, 56, 87, 52], [45, 16, 88, 12], [46, 16, 89, 12, "value"], [46, 21, 89, 17], [46, 24, 89, 20, "lastAnimation"], [46, 37, 89, 33], [46, 38, 89, 34, "current"], [46, 45, 89, 41], [47, 14, 90, 10], [47, 15, 90, 11], [47, 21, 90, 17], [47, 25, 90, 21, "lastValue"], [47, 34, 90, 30], [47, 36, 90, 32, "current"], [47, 43, 90, 39], [47, 48, 90, 44, "undefined"], [47, 57, 90, 53], [47, 59, 90, 55], [48, 16, 91, 12], [49, 16, 92, 12, "value"], [49, 21, 92, 17], [49, 24, 92, 20, "lastValue"], [49, 33, 92, 29], [49, 34, 92, 30, "current"], [49, 41, 92, 37], [50, 14, 93, 10], [51, 12, 94, 8], [52, 10, 95, 6], [52, 11, 95, 7], [52, 17, 95, 13], [53, 12, 96, 8], [54, 12, 97, 8, "value"], [54, 17, 97, 13], [54, 20, 97, 16, "lastValue"], [54, 29, 97, 25], [55, 10, 98, 6], [56, 8, 99, 4], [57, 8, 101, 4, "animation"], [57, 17, 101, 13], [57, 18, 101, 14, "callStart"], [57, 27, 101, 23], [57, 30, 101, 27, "timestamp"], [57, 39, 101, 47], [57, 43, 101, 52], [58, 10, 102, 6, "animation"], [58, 19, 102, 15], [58, 20, 102, 16, "onStart"], [58, 27, 102, 23], [58, 28, 102, 24, "animation"], [58, 37, 102, 33], [58, 39, 102, 35, "value"], [58, 44, 102, 40], [58, 46, 102, 42, "timestamp"], [58, 55, 102, 51], [58, 57, 102, 53, "lastAnimation"], [58, 70, 102, 66], [58, 71, 102, 67], [59, 8, 103, 4], [59, 9, 103, 5], [60, 8, 104, 4, "animation"], [60, 17, 104, 13], [60, 18, 104, 14, "callStart"], [60, 27, 104, 23], [60, 28, 104, 24, "frameTimestamp"], [60, 42, 104, 38], [60, 43, 104, 39], [61, 8, 105, 4, "animation"], [61, 17, 105, 13], [61, 18, 105, 14, "callStart"], [61, 27, 105, 23], [61, 30, 105, 26], [61, 34, 105, 30], [62, 6, 106, 2], [62, 7, 106, 3], [62, 13, 106, 9], [62, 17, 106, 13], [62, 24, 106, 20, "animatedProp"], [62, 36, 106, 32], [62, 41, 106, 37], [62, 49, 106, 45], [62, 51, 106, 47], [63, 8, 107, 4], [64, 8, 108, 4, "Object"], [64, 14, 108, 10], [64, 15, 108, 11, "keys"], [64, 19, 108, 15], [64, 20, 108, 16, "animatedProp"], [64, 32, 108, 28], [64, 33, 108, 29], [64, 34, 108, 30, "for<PERSON>ach"], [64, 41, 108, 37], [64, 42, 108, 39, "key"], [64, 45, 108, 42], [64, 49, 109, 6, "prepareAnimation"], [64, 65, 109, 22], [64, 66, 110, 8, "frameTimestamp"], [64, 80, 110, 22], [64, 82, 111, 8, "animatedProp"], [64, 94, 111, 20], [64, 95, 111, 21, "key"], [64, 98, 111, 24], [64, 99, 111, 25], [64, 101, 112, 8, "lastAnimation"], [64, 114, 112, 21], [64, 118, 112, 25, "lastAnimation"], [64, 131, 112, 38], [64, 132, 112, 39, "key"], [64, 135, 112, 42], [64, 136, 112, 43], [64, 138, 113, 8, "lastValue"], [64, 147, 113, 17], [64, 151, 113, 21, "lastValue"], [64, 160, 113, 30], [64, 161, 113, 31, "key"], [64, 164, 113, 34], [64, 165, 114, 6], [64, 166, 115, 4], [64, 167, 115, 5], [65, 6, 116, 2], [66, 4, 117, 0], [66, 5, 117, 1], [67, 4, 117, 1, "prepareAnimation"], [67, 20, 117, 1], [67, 21, 117, 1, "__closure"], [67, 30, 117, 1], [68, 4, 117, 1, "prepareAnimation"], [68, 20, 117, 1], [68, 21, 117, 1, "__workletHash"], [68, 34, 117, 1], [69, 4, 117, 1, "prepareAnimation"], [69, 20, 117, 1], [69, 21, 117, 1, "__initData"], [69, 31, 117, 1], [69, 34, 117, 1, "_worklet_6358524786384_init_data"], [69, 66, 117, 1], [70, 4, 117, 1, "prepareAnimation"], [70, 20, 117, 1], [70, 21, 117, 1, "__stackDetails"], [70, 35, 117, 1], [70, 38, 117, 1, "_e"], [70, 40, 117, 1], [71, 4, 117, 1], [71, 11, 117, 1, "prepareAnimation"], [71, 27, 117, 1], [72, 2, 117, 1], [72, 3, 59, 0], [73, 2, 59, 0], [73, 6, 59, 0, "_worklet_11724700371151_init_data"], [73, 39, 59, 0], [74, 4, 59, 0, "code"], [74, 8, 59, 0], [75, 4, 59, 0, "location"], [75, 12, 59, 0], [76, 4, 59, 0, "sourceMap"], [76, 13, 59, 0], [77, 4, 59, 0, "version"], [77, 11, 59, 0], [78, 2, 59, 0], [79, 2, 59, 0], [79, 6, 59, 0, "runAnimations"], [79, 19, 59, 0], [79, 22, 119, 0], [80, 4, 119, 0], [80, 8, 119, 0, "_e"], [80, 10, 119, 0], [80, 18, 119, 0, "global"], [80, 24, 119, 0], [80, 25, 119, 0, "Error"], [80, 30, 119, 0], [81, 4, 119, 0], [81, 8, 119, 0, "runAnimations"], [81, 21, 119, 0], [81, 33, 119, 0, "runAnimations"], [81, 34, 120, 2, "animation"], [81, 43, 120, 31], [81, 45, 121, 2, "timestamp"], [81, 54, 121, 22], [81, 56, 122, 2, "key"], [81, 59, 122, 22], [81, 61, 123, 2, "result"], [81, 67, 123, 28], [81, 69, 124, 2, "animationsActive"], [81, 85, 124, 40], [81, 87, 125, 2, "forceCopyAnimation"], [81, 105, 125, 30], [81, 107, 126, 11], [82, 6, 128, 2], [82, 10, 128, 6], [82, 11, 128, 7, "animationsActive"], [82, 27, 128, 23], [82, 28, 128, 24, "value"], [82, 33, 128, 29], [82, 35, 128, 31], [83, 8, 129, 4], [83, 15, 129, 11], [83, 19, 129, 15], [84, 6, 130, 2], [85, 6, 131, 2], [85, 10, 131, 6, "Array"], [85, 15, 131, 11], [85, 16, 131, 12, "isArray"], [85, 23, 131, 19], [85, 24, 131, 20, "animation"], [85, 33, 131, 29], [85, 34, 131, 30], [85, 36, 131, 32], [86, 8, 132, 4, "result"], [86, 14, 132, 10], [86, 15, 132, 11, "key"], [86, 18, 132, 14], [86, 19, 132, 15], [86, 22, 132, 18], [86, 24, 132, 20], [87, 8, 133, 4], [87, 12, 133, 8, "allFinished"], [87, 23, 133, 19], [87, 26, 133, 22], [87, 30, 133, 26], [88, 8, 134, 4, "forceCopyAnimation"], [88, 26, 134, 22], [88, 29, 134, 25, "key"], [88, 32, 134, 28], [88, 37, 134, 33], [88, 48, 134, 44], [89, 8, 135, 4, "animation"], [89, 17, 135, 13], [89, 18, 135, 14, "for<PERSON>ach"], [89, 25, 135, 21], [89, 26, 135, 22], [89, 27, 135, 23, "entry"], [89, 32, 135, 28], [89, 34, 135, 30, "index"], [89, 39, 135, 35], [89, 44, 135, 40], [90, 10, 136, 6], [90, 14, 137, 8], [90, 15, 137, 9, "runAnimations"], [90, 28, 137, 22], [90, 29, 138, 10, "entry"], [90, 34, 138, 15], [90, 36, 139, 10, "timestamp"], [90, 45, 139, 19], [90, 47, 140, 10, "index"], [90, 52, 140, 15], [90, 54, 141, 10, "result"], [90, 60, 141, 16], [90, 61, 141, 17, "key"], [90, 64, 141, 20], [90, 65, 141, 21], [90, 67, 142, 10, "animationsActive"], [90, 83, 142, 26], [90, 85, 143, 10, "forceCopyAnimation"], [90, 103, 144, 8], [90, 104, 144, 9], [90, 106, 145, 8], [91, 12, 146, 8, "allFinished"], [91, 23, 146, 19], [91, 26, 146, 22], [91, 31, 146, 27], [92, 10, 147, 6], [93, 8, 148, 4], [93, 9, 148, 5], [93, 10, 148, 6], [94, 8, 149, 4], [94, 15, 149, 11, "allFinished"], [94, 26, 149, 22], [95, 6, 150, 2], [95, 7, 150, 3], [95, 13, 150, 9], [95, 17, 150, 13], [95, 24, 150, 20, "animation"], [95, 33, 150, 29], [95, 38, 150, 34], [95, 46, 150, 42], [95, 50, 150, 46, "animation"], [95, 59, 150, 55], [95, 60, 150, 56, "onFrame"], [95, 67, 150, 63], [95, 69, 150, 65], [96, 8, 151, 4], [96, 12, 151, 8, "finished"], [96, 20, 151, 16], [96, 23, 151, 19], [96, 27, 151, 23], [97, 8, 152, 4], [97, 12, 152, 8], [97, 13, 152, 9, "animation"], [97, 22, 152, 18], [97, 23, 152, 19, "finished"], [97, 31, 152, 27], [97, 33, 152, 29], [98, 10, 153, 6], [98, 14, 153, 10, "animation"], [98, 23, 153, 19], [98, 24, 153, 20, "callStart"], [98, 33, 153, 29], [98, 35, 153, 31], [99, 12, 154, 8, "animation"], [99, 21, 154, 17], [99, 22, 154, 18, "callStart"], [99, 31, 154, 27], [99, 32, 154, 28, "timestamp"], [99, 41, 154, 37], [99, 42, 154, 38], [100, 12, 155, 8, "animation"], [100, 21, 155, 17], [100, 22, 155, 18, "callStart"], [100, 31, 155, 27], [100, 34, 155, 30], [100, 38, 155, 34], [101, 10, 156, 6], [102, 10, 157, 6, "finished"], [102, 18, 157, 14], [102, 21, 157, 17, "animation"], [102, 30, 157, 26], [102, 31, 157, 27, "onFrame"], [102, 38, 157, 34], [102, 39, 157, 35, "animation"], [102, 48, 157, 44], [102, 50, 157, 46, "timestamp"], [102, 59, 157, 55], [102, 60, 157, 56], [103, 10, 158, 6, "animation"], [103, 19, 158, 15], [103, 20, 158, 16, "timestamp"], [103, 29, 158, 25], [103, 32, 158, 28, "timestamp"], [103, 41, 158, 37], [104, 10, 159, 6], [104, 14, 159, 10, "finished"], [104, 22, 159, 18], [104, 24, 159, 20], [105, 12, 160, 8, "animation"], [105, 21, 160, 17], [105, 22, 160, 18, "finished"], [105, 30, 160, 26], [105, 33, 160, 29], [105, 37, 160, 33], [106, 12, 161, 8, "animation"], [106, 21, 161, 17], [106, 22, 161, 18, "callback"], [106, 30, 161, 26], [106, 34, 161, 30, "animation"], [106, 43, 161, 39], [106, 44, 161, 40, "callback"], [106, 52, 161, 48], [106, 53, 161, 49], [106, 57, 161, 53], [106, 58, 161, 54], [106, 72, 161, 68], [106, 73, 161, 69], [107, 10, 162, 6], [108, 8, 163, 4], [109, 8, 164, 4], [110, 0, 165, 0], [111, 0, 166, 0], [112, 0, 167, 0], [113, 0, 168, 0], [114, 8, 169, 4], [114, 12, 169, 8, "forceCopyAnimation"], [114, 30, 169, 26], [114, 32, 169, 28], [115, 10, 170, 6, "result"], [115, 16, 170, 12], [115, 17, 170, 13, "key"], [115, 20, 170, 16], [115, 21, 170, 17], [115, 24, 170, 20], [116, 12, 170, 22], [116, 15, 170, 25, "animation"], [116, 24, 170, 34], [116, 25, 170, 35, "current"], [117, 10, 170, 43], [117, 11, 170, 44], [118, 8, 171, 4], [118, 9, 171, 5], [118, 15, 171, 11], [119, 10, 172, 6, "result"], [119, 16, 172, 12], [119, 17, 172, 13, "key"], [119, 20, 172, 16], [119, 21, 172, 17], [119, 24, 172, 20, "animation"], [119, 33, 172, 29], [119, 34, 172, 30, "current"], [119, 41, 172, 37], [120, 8, 173, 4], [121, 8, 174, 4], [121, 15, 174, 11, "finished"], [121, 23, 174, 19], [122, 6, 175, 2], [122, 7, 175, 3], [122, 13, 175, 9], [122, 17, 175, 13], [122, 24, 175, 20, "animation"], [122, 33, 175, 29], [122, 38, 175, 34], [122, 46, 175, 42], [122, 48, 175, 44], [123, 8, 176, 4, "result"], [123, 14, 176, 10], [123, 15, 176, 11, "key"], [123, 18, 176, 14], [123, 19, 176, 15], [123, 22, 176, 18], [123, 23, 176, 19], [123, 24, 176, 20], [124, 8, 177, 4], [124, 12, 177, 8, "allFinished"], [124, 24, 177, 19], [124, 27, 177, 22], [124, 31, 177, 26], [125, 8, 178, 4, "Object"], [125, 14, 178, 10], [125, 15, 178, 11, "keys"], [125, 19, 178, 15], [125, 20, 178, 16, "animation"], [125, 29, 178, 25], [125, 30, 178, 26], [125, 31, 178, 27, "for<PERSON>ach"], [125, 38, 178, 34], [125, 39, 178, 36, "k"], [125, 40, 178, 37], [125, 44, 178, 42], [126, 10, 179, 6], [126, 14, 180, 8], [126, 15, 180, 9, "runAnimations"], [126, 28, 180, 22], [126, 29, 181, 10, "animation"], [126, 38, 181, 19], [126, 39, 181, 20, "k"], [126, 40, 181, 21], [126, 41, 181, 22], [126, 43, 182, 10, "timestamp"], [126, 52, 182, 19], [126, 54, 183, 10, "k"], [126, 55, 183, 11], [126, 57, 184, 10, "result"], [126, 63, 184, 16], [126, 64, 184, 17, "key"], [126, 67, 184, 20], [126, 68, 184, 21], [126, 70, 185, 10, "animationsActive"], [126, 86, 185, 26], [126, 88, 186, 10, "forceCopyAnimation"], [126, 106, 187, 8], [126, 107, 187, 9], [126, 109, 188, 8], [127, 12, 189, 8, "allFinished"], [127, 24, 189, 19], [127, 27, 189, 22], [127, 32, 189, 27], [128, 10, 190, 6], [129, 8, 191, 4], [129, 9, 191, 5], [129, 10, 191, 6], [130, 8, 192, 4], [130, 15, 192, 11, "allFinished"], [130, 27, 192, 22], [131, 6, 193, 2], [131, 7, 193, 3], [131, 13, 193, 9], [132, 8, 194, 4, "result"], [132, 14, 194, 10], [132, 15, 194, 11, "key"], [132, 18, 194, 14], [132, 19, 194, 15], [132, 22, 194, 18, "animation"], [132, 31, 194, 27], [133, 8, 195, 4], [133, 15, 195, 11], [133, 19, 195, 15], [134, 6, 196, 2], [135, 4, 197, 0], [135, 5, 197, 1], [136, 4, 197, 1, "runAnimations"], [136, 17, 197, 1], [136, 18, 197, 1, "__closure"], [136, 27, 197, 1], [137, 4, 197, 1, "runAnimations"], [137, 17, 197, 1], [137, 18, 197, 1, "__workletHash"], [137, 31, 197, 1], [138, 4, 197, 1, "runAnimations"], [138, 17, 197, 1], [138, 18, 197, 1, "__initData"], [138, 28, 197, 1], [138, 31, 197, 1, "_worklet_11724700371151_init_data"], [138, 64, 197, 1], [139, 4, 197, 1, "runAnimations"], [139, 17, 197, 1], [139, 18, 197, 1, "__stackDetails"], [139, 32, 197, 1], [139, 35, 197, 1, "_e"], [139, 37, 197, 1], [140, 4, 197, 1], [140, 11, 197, 1, "runAnimations"], [140, 24, 197, 1], [141, 2, 197, 1], [141, 3, 119, 0], [142, 2, 119, 0], [142, 6, 119, 0, "_worklet_6395962061635_init_data"], [142, 38, 119, 0], [143, 4, 119, 0, "code"], [143, 8, 119, 0], [144, 4, 119, 0, "location"], [144, 12, 119, 0], [145, 4, 119, 0, "sourceMap"], [145, 13, 119, 0], [146, 4, 119, 0, "version"], [146, 11, 119, 0], [147, 2, 119, 0], [148, 2, 119, 0], [148, 6, 119, 0, "styleUpdater"], [148, 18, 119, 0], [148, 21, 199, 0], [149, 4, 199, 0], [149, 8, 199, 0, "_e"], [149, 10, 199, 0], [149, 18, 199, 0, "global"], [149, 24, 199, 0], [149, 25, 199, 0, "Error"], [149, 30, 199, 0], [150, 4, 199, 0], [150, 8, 199, 0, "styleUpdater"], [150, 20, 199, 0], [150, 32, 199, 0, "styleUpdater"], [150, 33, 200, 2, "viewDescriptors"], [150, 48, 200, 44], [150, 50, 201, 2, "updater"], [150, 57, 201, 79], [150, 59, 202, 2, "state"], [150, 64, 202, 22], [150, 66, 203, 2, "animationsActive"], [150, 82, 203, 40], [150, 84, 205, 8], [151, 6, 205, 8], [151, 10, 204, 2, "isAnimatedProps"], [151, 25, 204, 17], [151, 28, 204, 17, "arguments"], [151, 37, 204, 17], [151, 38, 204, 17, "length"], [151, 44, 204, 17], [151, 52, 204, 17, "arguments"], [151, 61, 204, 17], [151, 69, 204, 17, "undefined"], [151, 78, 204, 17], [151, 81, 204, 17, "arguments"], [151, 90, 204, 17], [151, 96, 204, 20], [151, 101, 204, 25], [152, 6, 207, 2], [152, 10, 207, 8, "animations"], [152, 20, 207, 18], [152, 23, 207, 21, "state"], [152, 28, 207, 26], [152, 29, 207, 27, "animations"], [152, 39, 207, 37], [152, 43, 207, 41], [152, 44, 207, 42], [152, 45, 207, 43], [153, 6, 208, 2], [153, 10, 208, 8, "newValues"], [153, 19, 208, 17], [153, 22, 208, 20, "updater"], [153, 29, 208, 27], [153, 30, 208, 28], [153, 31, 208, 29], [153, 35, 208, 33], [153, 36, 208, 34], [153, 37, 208, 35], [154, 6, 209, 2], [154, 10, 209, 8, "oldValues"], [154, 19, 209, 17], [154, 22, 209, 20, "state"], [154, 27, 209, 25], [154, 28, 209, 26, "last"], [154, 32, 209, 30], [155, 6, 210, 2], [155, 10, 210, 8, "nonAnimatedNewValues"], [155, 30, 210, 40], [155, 33, 210, 43], [155, 34, 210, 44], [155, 35, 210, 45], [156, 6, 212, 2], [156, 10, 212, 6, "hasAnimations"], [156, 23, 212, 19], [156, 26, 212, 22], [156, 31, 212, 27], [157, 6, 213, 2], [157, 10, 213, 6, "frameTimestamp"], [157, 24, 213, 40], [158, 6, 214, 2], [158, 10, 214, 6, "hasNonAnimatedValues"], [158, 30, 214, 26], [158, 33, 214, 29], [158, 38, 214, 34], [159, 6, 215, 2], [159, 10, 215, 6], [159, 11, 215, 7, "SHOULD_BE_USE_WEB"], [159, 28, 215, 24], [159, 32, 215, 28, "newValues"], [159, 41, 215, 37], [159, 42, 215, 38, "boxShadow"], [159, 51, 215, 47], [159, 53, 215, 49], [160, 8, 216, 4], [160, 12, 216, 4, "processBoxShadow"], [160, 46, 216, 20], [160, 48, 216, 21, "newValues"], [160, 57, 216, 30], [160, 58, 216, 31], [161, 6, 217, 2], [162, 6, 218, 2], [162, 11, 218, 7], [162, 15, 218, 13, "key"], [162, 18, 218, 16], [162, 22, 218, 20, "newValues"], [162, 31, 218, 29], [162, 33, 218, 31], [163, 8, 219, 4], [163, 12, 219, 10, "value"], [163, 17, 219, 15], [163, 20, 219, 18, "newValues"], [163, 29, 219, 27], [163, 30, 219, 28, "key"], [163, 33, 219, 31], [163, 34, 219, 32], [164, 8, 220, 4], [164, 12, 220, 8], [164, 16, 220, 8, "isAnimated"], [164, 33, 220, 18], [164, 35, 220, 19, "value"], [164, 40, 220, 24], [164, 41, 220, 25], [164, 43, 220, 27], [165, 10, 221, 6, "frameTimestamp"], [165, 24, 221, 20], [165, 27, 222, 8, "global"], [165, 33, 222, 14], [165, 34, 222, 15, "__frameTimestamp"], [165, 50, 222, 31], [165, 54, 222, 35, "global"], [165, 60, 222, 41], [165, 61, 222, 42, "_getAnimationTimestamp"], [165, 83, 222, 64], [165, 84, 222, 65], [165, 85, 222, 66], [166, 10, 223, 6, "prepareAnimation"], [166, 26, 223, 22], [166, 27, 223, 23, "frameTimestamp"], [166, 41, 223, 37], [166, 43, 223, 39, "value"], [166, 48, 223, 44], [166, 50, 223, 46, "animations"], [166, 60, 223, 56], [166, 61, 223, 57, "key"], [166, 64, 223, 60], [166, 65, 223, 61], [166, 67, 223, 63, "oldValues"], [166, 76, 223, 72], [166, 77, 223, 73, "key"], [166, 80, 223, 76], [166, 81, 223, 77], [166, 82, 223, 78], [167, 10, 224, 6, "animations"], [167, 20, 224, 16], [167, 21, 224, 17, "key"], [167, 24, 224, 20], [167, 25, 224, 21], [167, 28, 224, 24, "value"], [167, 33, 224, 29], [168, 10, 225, 6, "hasAnimations"], [168, 23, 225, 19], [168, 26, 225, 22], [168, 30, 225, 26], [169, 8, 226, 4], [169, 9, 226, 5], [169, 15, 226, 11], [170, 10, 227, 6, "hasNonAnimatedValues"], [170, 30, 227, 26], [170, 33, 227, 29], [170, 37, 227, 33], [171, 10, 228, 6, "nonAnimatedNewValues"], [171, 30, 228, 26], [171, 31, 228, 27, "key"], [171, 34, 228, 30], [171, 35, 228, 31], [171, 38, 228, 34, "value"], [171, 43, 228, 39], [172, 10, 229, 6], [172, 17, 229, 13, "animations"], [172, 27, 229, 23], [172, 28, 229, 24, "key"], [172, 31, 229, 27], [172, 32, 229, 28], [173, 8, 230, 4], [174, 6, 231, 2], [175, 6, 233, 2], [175, 10, 233, 6, "hasAnimations"], [175, 23, 233, 19], [175, 25, 233, 21], [176, 8, 234, 4], [176, 12, 234, 10, "frame"], [176, 17, 234, 15], [176, 20, 234, 19, "timestamp"], [176, 29, 234, 39], [176, 33, 234, 44], [177, 10, 235, 6], [178, 10, 236, 6], [178, 14, 236, 14, "animations"], [178, 24, 236, 24], [178, 27, 236, 57, "state"], [178, 32, 236, 62], [178, 33, 236, 14, "animations"], [178, 43, 236, 24], [179, 12, 236, 26, "last"], [179, 16, 236, 30], [179, 19, 236, 57, "state"], [179, 24, 236, 62], [179, 25, 236, 26, "last"], [179, 29, 236, 30], [180, 12, 236, 32, "isAnimationCancelled"], [180, 32, 236, 52], [180, 35, 236, 57, "state"], [180, 40, 236, 62], [180, 41, 236, 32, "isAnimationCancelled"], [180, 61, 236, 52], [181, 10, 237, 6], [181, 14, 237, 10, "isAnimationCancelled"], [181, 34, 237, 30], [181, 36, 237, 32], [182, 12, 238, 8, "state"], [182, 17, 238, 13], [182, 18, 238, 14, "isAnimationRunning"], [182, 36, 238, 32], [182, 39, 238, 35], [182, 44, 238, 40], [183, 12, 239, 8], [184, 10, 240, 6], [185, 10, 242, 6], [185, 14, 242, 12, "updates"], [185, 21, 242, 39], [185, 24, 242, 42], [185, 25, 242, 43], [185, 26, 242, 44], [186, 10, 243, 6], [186, 14, 243, 10, "allFinished"], [186, 25, 243, 21], [186, 28, 243, 24], [186, 32, 243, 28], [187, 10, 243, 29], [187, 14, 243, 29, "_loop"], [187, 19, 243, 29], [187, 31, 243, 29, "_loop"], [187, 32, 243, 29, "propName"], [187, 40, 243, 29], [187, 42, 244, 41], [188, 12, 245, 8], [188, 16, 245, 14, "finished"], [188, 24, 245, 22], [188, 27, 245, 25, "runAnimations"], [188, 40, 245, 38], [188, 41, 246, 10, "animations"], [188, 51, 246, 20], [188, 52, 246, 21, "propName"], [188, 60, 246, 29], [188, 61, 246, 30], [188, 63, 247, 10, "timestamp"], [188, 72, 247, 19], [188, 74, 248, 10, "propName"], [188, 82, 248, 18], [188, 84, 249, 10, "updates"], [188, 91, 249, 17], [188, 93, 250, 10, "animationsActive"], [188, 109, 251, 8], [188, 110, 251, 9], [189, 12, 252, 8], [189, 16, 252, 12, "finished"], [189, 24, 252, 20], [189, 26, 252, 22], [190, 14, 253, 10], [191, 0, 254, 0], [192, 0, 255, 0], [193, 0, 256, 0], [194, 0, 257, 0], [195, 0, 258, 0], [196, 14, 259, 10], [196, 18, 259, 14, "Array"], [196, 23, 259, 19], [196, 24, 259, 20, "isArray"], [196, 31, 259, 27], [196, 32, 259, 28, "updates"], [196, 39, 259, 35], [196, 40, 259, 36, "propName"], [196, 48, 259, 44], [196, 49, 259, 45], [196, 50, 259, 46], [196, 52, 259, 48], [197, 16, 260, 12, "updates"], [197, 23, 260, 19], [197, 24, 260, 20, "propName"], [197, 32, 260, 28], [197, 33, 260, 29], [197, 34, 260, 30, "for<PERSON>ach"], [197, 41, 260, 37], [197, 42, 260, 39, "obj"], [197, 45, 260, 54], [197, 49, 260, 59], [198, 18, 261, 14], [198, 23, 261, 19], [198, 27, 261, 25, "prop"], [198, 31, 261, 29], [198, 35, 261, 33, "obj"], [198, 38, 261, 36], [198, 40, 261, 38], [199, 20, 262, 16, "last"], [199, 24, 262, 20], [199, 25, 262, 21, "propName"], [199, 33, 262, 29], [199, 34, 262, 30], [199, 35, 262, 31, "prop"], [199, 39, 262, 35], [199, 40, 262, 36], [199, 43, 262, 39, "obj"], [199, 46, 262, 42], [199, 47, 262, 43, "prop"], [199, 51, 262, 47], [199, 52, 262, 48], [200, 18, 263, 14], [201, 16, 264, 12], [201, 17, 264, 13], [201, 18, 264, 14], [202, 14, 265, 10], [202, 15, 265, 11], [202, 21, 265, 17], [203, 16, 266, 12, "last"], [203, 20, 266, 16], [203, 21, 266, 17, "propName"], [203, 29, 266, 25], [203, 30, 266, 26], [203, 33, 266, 29, "updates"], [203, 40, 266, 36], [203, 41, 266, 37, "propName"], [203, 49, 266, 45], [203, 50, 266, 46], [204, 14, 267, 10], [205, 14, 268, 10], [205, 21, 268, 17, "animations"], [205, 31, 268, 27], [205, 32, 268, 28, "propName"], [205, 40, 268, 36], [205, 41, 268, 37], [206, 12, 269, 8], [206, 13, 269, 9], [206, 19, 269, 15], [207, 14, 270, 10, "allFinished"], [207, 25, 270, 21], [207, 28, 270, 24], [207, 33, 270, 29], [208, 12, 271, 8], [209, 10, 272, 6], [209, 11, 272, 7], [210, 10, 244, 6], [210, 15, 244, 11], [210, 19, 244, 17, "propName"], [210, 27, 244, 25], [210, 31, 244, 29, "animations"], [210, 41, 244, 39], [211, 12, 244, 39, "_loop"], [211, 17, 244, 39], [211, 18, 244, 39, "propName"], [211, 26, 244, 39], [212, 10, 244, 39], [213, 10, 274, 6], [213, 14, 274, 10, "updates"], [213, 21, 274, 17], [213, 23, 274, 19], [214, 12, 275, 8], [214, 16, 275, 8, "updateProps"], [214, 40, 275, 19], [214, 42, 275, 20, "viewDescriptors"], [214, 57, 275, 35], [214, 59, 275, 37, "updates"], [214, 66, 275, 44], [214, 67, 275, 45], [215, 10, 276, 6], [216, 10, 278, 6], [216, 14, 278, 10], [216, 15, 278, 11, "allFinished"], [216, 26, 278, 22], [216, 28, 278, 24], [217, 12, 279, 8, "requestAnimationFrame"], [217, 33, 279, 29], [217, 34, 279, 30, "frame"], [217, 39, 279, 35], [217, 40, 279, 36], [218, 10, 280, 6], [218, 11, 280, 7], [218, 17, 280, 13], [219, 12, 281, 8, "state"], [219, 17, 281, 13], [219, 18, 281, 14, "isAnimationRunning"], [219, 36, 281, 32], [219, 39, 281, 35], [219, 44, 281, 40], [220, 10, 282, 6], [221, 8, 283, 4], [221, 9, 283, 5], [222, 8, 285, 4, "state"], [222, 13, 285, 9], [222, 14, 285, 10, "animations"], [222, 24, 285, 20], [222, 27, 285, 23, "animations"], [222, 37, 285, 33], [223, 8, 286, 4], [223, 12, 286, 8], [223, 13, 286, 9, "state"], [223, 18, 286, 14], [223, 19, 286, 15, "isAnimationRunning"], [223, 37, 286, 33], [223, 39, 286, 35], [224, 10, 287, 6, "state"], [224, 15, 287, 11], [224, 16, 287, 12, "isAnimationCancelled"], [224, 36, 287, 32], [224, 39, 287, 35], [224, 44, 287, 40], [225, 10, 288, 6, "state"], [225, 15, 288, 11], [225, 16, 288, 12, "isAnimationRunning"], [225, 34, 288, 30], [225, 37, 288, 33], [225, 41, 288, 37], [226, 10, 289, 6, "frame"], [226, 15, 289, 11], [226, 16, 289, 12, "frameTimestamp"], [226, 30, 289, 27], [226, 31, 289, 28], [227, 8, 290, 4], [228, 8, 292, 4], [228, 12, 292, 8, "hasNonAnimatedValues"], [228, 32, 292, 28], [228, 34, 292, 30], [229, 10, 293, 6], [229, 14, 293, 6, "updateProps"], [229, 38, 293, 17], [229, 40, 293, 18, "viewDescriptors"], [229, 55, 293, 33], [229, 57, 293, 35, "nonAnimatedNewValues"], [229, 77, 293, 55], [229, 78, 293, 56], [230, 8, 294, 4], [231, 6, 295, 2], [231, 7, 295, 3], [231, 13, 295, 9], [232, 8, 296, 4, "state"], [232, 13, 296, 9], [232, 14, 296, 10, "isAnimationCancelled"], [232, 34, 296, 30], [232, 37, 296, 33], [232, 41, 296, 37], [233, 8, 297, 4, "state"], [233, 13, 297, 9], [233, 14, 297, 10, "animations"], [233, 24, 297, 20], [233, 27, 297, 23], [233, 29, 297, 25], [234, 8, 299, 4], [234, 12, 299, 8], [234, 13, 299, 9], [234, 17, 299, 9, "shallowEqual"], [234, 36, 299, 21], [234, 38, 299, 22, "oldValues"], [234, 47, 299, 31], [234, 49, 299, 33, "newValues"], [234, 58, 299, 42], [234, 59, 299, 43], [234, 61, 299, 45], [235, 10, 300, 6], [235, 14, 300, 6, "updateProps"], [235, 38, 300, 17], [235, 40, 300, 18, "viewDescriptors"], [235, 55, 300, 33], [235, 57, 300, 35, "newValues"], [235, 66, 300, 44], [235, 68, 300, 46, "isAnimatedProps"], [235, 83, 300, 61], [235, 84, 300, 62], [236, 8, 301, 4], [237, 6, 302, 2], [238, 6, 303, 2, "state"], [238, 11, 303, 7], [238, 12, 303, 8, "last"], [238, 16, 303, 12], [238, 19, 303, 15, "newValues"], [238, 28, 303, 24], [239, 4, 304, 0], [239, 5, 304, 1], [240, 4, 304, 1, "styleUpdater"], [240, 16, 304, 1], [240, 17, 304, 1, "__closure"], [240, 26, 304, 1], [241, 6, 304, 1, "SHOULD_BE_USE_WEB"], [241, 23, 304, 1], [242, 6, 304, 1, "processBoxShadow"], [242, 22, 304, 1], [242, 24, 216, 4, "processBoxShadow"], [242, 58, 216, 20], [243, 6, 216, 20, "isAnimated"], [243, 16, 216, 20], [243, 18, 220, 8, "isAnimated"], [243, 35, 220, 18], [244, 6, 220, 18, "prepareAnimation"], [244, 22, 220, 18], [245, 6, 220, 18, "runAnimations"], [245, 19, 220, 18], [246, 6, 220, 18, "updateProps"], [246, 17, 220, 18], [246, 19, 275, 8, "updateProps"], [246, 43, 275, 19], [247, 6, 275, 19, "shallowEqual"], [247, 18, 275, 19], [247, 20, 299, 9, "shallowEqual"], [248, 4, 299, 21], [249, 4, 299, 21, "styleUpdater"], [249, 16, 299, 21], [249, 17, 299, 21, "__workletHash"], [249, 30, 299, 21], [250, 4, 299, 21, "styleUpdater"], [250, 16, 299, 21], [250, 17, 299, 21, "__initData"], [250, 27, 299, 21], [250, 30, 299, 21, "_worklet_6395962061635_init_data"], [250, 62, 299, 21], [251, 4, 299, 21, "styleUpdater"], [251, 16, 299, 21], [251, 17, 299, 21, "__stackDetails"], [251, 31, 299, 21], [251, 34, 299, 21, "_e"], [251, 36, 299, 21], [252, 4, 299, 21], [252, 11, 299, 21, "styleUpdater"], [252, 23, 299, 21], [253, 2, 299, 21], [253, 3, 199, 0], [254, 2, 199, 0], [254, 6, 199, 0, "_worklet_4651568536366_init_data"], [254, 38, 199, 0], [255, 4, 199, 0, "code"], [255, 8, 199, 0], [256, 4, 199, 0, "location"], [256, 12, 199, 0], [257, 4, 199, 0, "sourceMap"], [257, 13, 199, 0], [258, 4, 199, 0, "version"], [258, 11, 199, 0], [259, 2, 199, 0], [260, 2, 199, 0], [260, 6, 199, 0, "jestStyleUpdater"], [260, 22, 199, 0], [260, 25, 306, 0], [261, 4, 306, 0], [261, 8, 306, 0, "_e"], [261, 10, 306, 0], [261, 18, 306, 0, "global"], [261, 24, 306, 0], [261, 25, 306, 0, "Error"], [261, 30, 306, 0], [262, 4, 306, 0], [262, 8, 306, 0, "jestStyleUpdater"], [262, 24, 306, 0], [262, 36, 306, 0, "jestStyleUpdater"], [262, 37, 307, 2, "viewDescriptors"], [262, 52, 307, 44], [262, 54, 308, 2, "updater"], [262, 61, 308, 79], [262, 63, 309, 2, "state"], [262, 68, 309, 22], [262, 70, 310, 2, "animationsActive"], [262, 86, 310, 40], [262, 88, 311, 2, "animatedValues"], [262, 102, 311, 54], [262, 104, 312, 2, "adapters"], [262, 112, 312, 42], [262, 114, 313, 8], [263, 6, 315, 2], [263, 10, 315, 8, "animations"], [263, 20, 315, 38], [263, 23, 315, 41, "state"], [263, 28, 315, 46], [263, 29, 315, 47, "animations"], [263, 39, 315, 57], [263, 43, 315, 61], [263, 44, 315, 62], [263, 45, 315, 63], [264, 6, 316, 2], [264, 10, 316, 8, "newValues"], [264, 19, 316, 17], [264, 22, 316, 20, "updater"], [264, 29, 316, 27], [264, 30, 316, 28], [264, 31, 316, 29], [264, 35, 316, 33], [264, 36, 316, 34], [264, 37, 316, 35], [265, 6, 317, 2], [265, 10, 317, 8, "oldValues"], [265, 19, 317, 17], [265, 22, 317, 20, "state"], [265, 27, 317, 25], [265, 28, 317, 26, "last"], [265, 32, 317, 30], [267, 6, 319, 2], [268, 6, 320, 2], [268, 10, 320, 6, "hasAnimations"], [268, 23, 320, 19], [268, 26, 320, 22], [268, 31, 320, 27], [269, 6, 321, 2], [269, 10, 321, 6, "frameTimestamp"], [269, 24, 321, 40], [270, 6, 322, 2, "Object"], [270, 12, 322, 8], [270, 13, 322, 9, "keys"], [270, 17, 322, 13], [270, 18, 322, 14, "animations"], [270, 28, 322, 24], [270, 29, 322, 25], [270, 30, 322, 26, "for<PERSON>ach"], [270, 37, 322, 33], [270, 38, 322, 35, "key"], [270, 41, 322, 38], [270, 45, 322, 43], [271, 8, 323, 4], [271, 12, 323, 10, "value"], [271, 17, 323, 15], [271, 20, 323, 18, "newValues"], [271, 29, 323, 27], [271, 30, 323, 28, "key"], [271, 33, 323, 31], [271, 34, 323, 32], [272, 8, 324, 4], [272, 12, 324, 8], [272, 13, 324, 9], [272, 17, 324, 9, "isAnimated"], [272, 34, 324, 19], [272, 36, 324, 20, "value"], [272, 41, 324, 25], [272, 42, 324, 26], [272, 44, 324, 28], [273, 10, 325, 6], [273, 17, 325, 13, "animations"], [273, 27, 325, 23], [273, 28, 325, 24, "key"], [273, 31, 325, 27], [273, 32, 325, 28], [274, 8, 326, 4], [275, 6, 327, 2], [275, 7, 327, 3], [275, 8, 327, 4], [276, 6, 328, 2, "Object"], [276, 12, 328, 8], [276, 13, 328, 9, "keys"], [276, 17, 328, 13], [276, 18, 328, 14, "newValues"], [276, 27, 328, 23], [276, 28, 328, 24], [276, 29, 328, 25, "for<PERSON>ach"], [276, 36, 328, 32], [276, 37, 328, 34, "key"], [276, 40, 328, 37], [276, 44, 328, 42], [277, 8, 329, 4], [277, 12, 329, 10, "value"], [277, 17, 329, 15], [277, 20, 329, 18, "newValues"], [277, 29, 329, 27], [277, 30, 329, 28, "key"], [277, 33, 329, 31], [277, 34, 329, 32], [278, 8, 330, 4], [278, 12, 330, 8], [278, 16, 330, 8, "isAnimated"], [278, 33, 330, 18], [278, 35, 330, 19, "value"], [278, 40, 330, 24], [278, 41, 330, 25], [278, 43, 330, 27], [279, 10, 331, 6, "frameTimestamp"], [279, 24, 331, 20], [279, 27, 332, 8, "global"], [279, 33, 332, 14], [279, 34, 332, 15, "__frameTimestamp"], [279, 50, 332, 31], [279, 54, 332, 35, "global"], [279, 60, 332, 41], [279, 61, 332, 42, "_getAnimationTimestamp"], [279, 83, 332, 64], [279, 84, 332, 65], [279, 85, 332, 66], [280, 10, 333, 6, "prepareAnimation"], [280, 26, 333, 22], [280, 27, 333, 23, "frameTimestamp"], [280, 41, 333, 37], [280, 43, 333, 39, "value"], [280, 48, 333, 44], [280, 50, 333, 46, "animations"], [280, 60, 333, 56], [280, 61, 333, 57, "key"], [280, 64, 333, 60], [280, 65, 333, 61], [280, 67, 333, 63, "oldValues"], [280, 76, 333, 72], [280, 77, 333, 73, "key"], [280, 80, 333, 76], [280, 81, 333, 77], [280, 82, 333, 78], [281, 10, 334, 6, "animations"], [281, 20, 334, 16], [281, 21, 334, 17, "key"], [281, 24, 334, 20], [281, 25, 334, 21], [281, 28, 334, 24, "value"], [281, 33, 334, 29], [282, 10, 335, 6, "hasAnimations"], [282, 23, 335, 19], [282, 26, 335, 22], [282, 30, 335, 26], [283, 8, 336, 4], [284, 6, 337, 2], [284, 7, 337, 3], [284, 8, 337, 4], [285, 6, 339, 2], [285, 15, 339, 11, "frame"], [285, 20, 339, 16, "frame"], [285, 21, 339, 17, "timestamp"], [285, 30, 339, 37], [285, 32, 339, 39], [286, 8, 340, 4], [287, 8, 341, 4], [287, 12, 341, 12, "animations"], [287, 22, 341, 22], [287, 25, 341, 55, "state"], [287, 30, 341, 60], [287, 31, 341, 12, "animations"], [287, 41, 341, 22], [288, 10, 341, 24, "last"], [288, 14, 341, 28], [288, 17, 341, 55, "state"], [288, 22, 341, 60], [288, 23, 341, 24, "last"], [288, 27, 341, 28], [289, 10, 341, 30, "isAnimationCancelled"], [289, 30, 341, 50], [289, 33, 341, 55, "state"], [289, 38, 341, 60], [289, 39, 341, 30, "isAnimationCancelled"], [289, 59, 341, 50], [290, 8, 342, 4], [290, 12, 342, 8, "isAnimationCancelled"], [290, 32, 342, 28], [290, 34, 342, 30], [291, 10, 343, 6, "state"], [291, 15, 343, 11], [291, 16, 343, 12, "isAnimationRunning"], [291, 34, 343, 30], [291, 37, 343, 33], [291, 42, 343, 38], [292, 10, 344, 6], [293, 8, 345, 4], [294, 8, 347, 4], [294, 12, 347, 10, "updates"], [294, 19, 347, 37], [294, 22, 347, 40], [294, 23, 347, 41], [294, 24, 347, 42], [295, 8, 348, 4], [295, 12, 348, 8, "allFinished"], [295, 23, 348, 19], [295, 26, 348, 22], [295, 30, 348, 26], [296, 8, 349, 4, "Object"], [296, 14, 349, 10], [296, 15, 349, 11, "keys"], [296, 19, 349, 15], [296, 20, 349, 16, "animations"], [296, 30, 349, 26], [296, 31, 349, 27], [296, 32, 349, 28, "for<PERSON>ach"], [296, 39, 349, 35], [296, 40, 349, 37, "propName"], [296, 48, 349, 45], [296, 52, 349, 50], [297, 10, 350, 6], [297, 14, 350, 12, "finished"], [297, 22, 350, 20], [297, 25, 350, 23, "runAnimations"], [297, 38, 350, 36], [297, 39, 351, 8, "animations"], [297, 49, 351, 18], [297, 50, 351, 19, "propName"], [297, 58, 351, 27], [297, 59, 351, 28], [297, 61, 352, 8, "timestamp"], [297, 70, 352, 17], [297, 72, 353, 8, "propName"], [297, 80, 353, 16], [297, 82, 354, 8, "updates"], [297, 89, 354, 15], [297, 91, 355, 8, "animationsActive"], [297, 107, 356, 6], [297, 108, 356, 7], [298, 10, 357, 6], [298, 14, 357, 10, "finished"], [298, 22, 357, 18], [298, 24, 357, 20], [299, 12, 358, 8, "last"], [299, 16, 358, 12], [299, 17, 358, 13, "propName"], [299, 25, 358, 21], [299, 26, 358, 22], [299, 29, 358, 25, "updates"], [299, 36, 358, 32], [299, 37, 358, 33, "propName"], [299, 45, 358, 41], [299, 46, 358, 42], [300, 12, 359, 8], [300, 19, 359, 15, "animations"], [300, 29, 359, 25], [300, 30, 359, 26, "propName"], [300, 38, 359, 34], [300, 39, 359, 35], [301, 10, 360, 6], [301, 11, 360, 7], [301, 17, 360, 13], [302, 12, 361, 8, "allFinished"], [302, 23, 361, 19], [302, 26, 361, 22], [302, 31, 361, 27], [303, 10, 362, 6], [304, 8, 363, 4], [304, 9, 363, 5], [304, 10, 363, 6], [305, 8, 365, 4], [305, 12, 365, 8, "Object"], [305, 18, 365, 14], [305, 19, 365, 15, "keys"], [305, 23, 365, 19], [305, 24, 365, 20, "updates"], [305, 31, 365, 27], [305, 32, 365, 28], [305, 33, 365, 29, "length"], [305, 39, 365, 35], [305, 41, 365, 37], [306, 10, 366, 6], [306, 14, 366, 6, "updatePropsJestWrapper"], [306, 49, 366, 28], [306, 51, 367, 8, "viewDescriptors"], [306, 66, 367, 23], [306, 68, 368, 8, "updates"], [306, 75, 368, 15], [306, 77, 369, 8, "animatedValues"], [306, 91, 369, 22], [306, 93, 370, 8, "adapters"], [306, 101, 371, 6], [306, 102, 371, 7], [307, 8, 372, 4], [308, 8, 374, 4], [308, 12, 374, 8], [308, 13, 374, 9, "allFinished"], [308, 24, 374, 20], [308, 26, 374, 22], [309, 10, 375, 6, "requestAnimationFrame"], [309, 31, 375, 27], [309, 32, 375, 28, "frame"], [309, 37, 375, 33], [309, 38, 375, 34], [310, 8, 376, 4], [310, 9, 376, 5], [310, 15, 376, 11], [311, 10, 377, 6, "state"], [311, 15, 377, 11], [311, 16, 377, 12, "isAnimationRunning"], [311, 34, 377, 30], [311, 37, 377, 33], [311, 42, 377, 38], [312, 8, 378, 4], [313, 6, 379, 2], [314, 6, 381, 2], [314, 10, 381, 6, "hasAnimations"], [314, 23, 381, 19], [314, 25, 381, 21], [315, 8, 382, 4, "state"], [315, 13, 382, 9], [315, 14, 382, 10, "animations"], [315, 24, 382, 20], [315, 27, 382, 23, "animations"], [315, 37, 382, 33], [316, 8, 383, 4], [316, 12, 383, 8], [316, 13, 383, 9, "state"], [316, 18, 383, 14], [316, 19, 383, 15, "isAnimationRunning"], [316, 37, 383, 33], [316, 39, 383, 35], [317, 10, 384, 6, "state"], [317, 15, 384, 11], [317, 16, 384, 12, "isAnimationCancelled"], [317, 36, 384, 32], [317, 39, 384, 35], [317, 44, 384, 40], [318, 10, 385, 6, "state"], [318, 15, 385, 11], [318, 16, 385, 12, "isAnimationRunning"], [318, 34, 385, 30], [318, 37, 385, 33], [318, 41, 385, 37], [319, 10, 386, 6, "frame"], [319, 15, 386, 11], [319, 16, 386, 12, "frameTimestamp"], [319, 30, 386, 27], [319, 31, 386, 28], [320, 8, 387, 4], [321, 6, 388, 2], [321, 7, 388, 3], [321, 13, 388, 9], [322, 8, 389, 4, "state"], [322, 13, 389, 9], [322, 14, 389, 10, "isAnimationCancelled"], [322, 34, 389, 30], [322, 37, 389, 33], [322, 41, 389, 37], [323, 8, 390, 4, "state"], [323, 13, 390, 9], [323, 14, 390, 10, "animations"], [323, 24, 390, 20], [323, 27, 390, 23], [323, 29, 390, 25], [324, 6, 391, 2], [326, 6, 393, 2], [327, 6, 394, 2, "state"], [327, 11, 394, 7], [327, 12, 394, 8, "last"], [327, 16, 394, 12], [327, 19, 394, 15, "newValues"], [327, 28, 394, 24], [328, 6, 396, 2], [328, 10, 396, 6], [328, 11, 396, 7], [328, 15, 396, 7, "shallowEqual"], [328, 34, 396, 19], [328, 36, 396, 20, "oldValues"], [328, 45, 396, 29], [328, 47, 396, 31, "newValues"], [328, 56, 396, 40], [328, 57, 396, 41], [328, 59, 396, 43], [329, 8, 397, 4], [329, 12, 397, 4, "updatePropsJestWrapper"], [329, 47, 397, 26], [329, 49, 398, 6, "viewDescriptors"], [329, 64, 398, 21], [329, 66, 399, 6, "newValues"], [329, 75, 399, 15], [329, 77, 400, 6, "animatedValues"], [329, 91, 400, 20], [329, 93, 401, 6, "adapters"], [329, 101, 402, 4], [329, 102, 402, 5], [330, 6, 403, 2], [331, 4, 404, 0], [331, 5, 404, 1], [332, 4, 404, 1, "jestStyleUpdater"], [332, 20, 404, 1], [332, 21, 404, 1, "__closure"], [332, 30, 404, 1], [333, 6, 404, 1, "isAnimated"], [333, 16, 404, 1], [333, 18, 324, 9, "isAnimated"], [333, 35, 324, 19], [334, 6, 324, 19, "prepareAnimation"], [334, 22, 324, 19], [335, 6, 324, 19, "runAnimations"], [335, 19, 324, 19], [336, 6, 324, 19, "updatePropsJestWrapper"], [336, 28, 324, 19], [336, 30, 366, 6, "updatePropsJestWrapper"], [336, 65, 366, 28], [337, 6, 366, 28, "shallowEqual"], [337, 18, 366, 28], [337, 20, 396, 7, "shallowEqual"], [338, 4, 396, 19], [339, 4, 396, 19, "jestStyleUpdater"], [339, 20, 396, 19], [339, 21, 396, 19, "__workletHash"], [339, 34, 396, 19], [340, 4, 396, 19, "jestStyleUpdater"], [340, 20, 396, 19], [340, 21, 396, 19, "__initData"], [340, 31, 396, 19], [340, 34, 396, 19, "_worklet_4651568536366_init_data"], [340, 66, 396, 19], [341, 4, 396, 19, "jestStyleUpdater"], [341, 20, 396, 19], [341, 21, 396, 19, "__stackDetails"], [341, 35, 396, 19], [341, 38, 396, 19, "_e"], [341, 40, 396, 19], [342, 4, 396, 19], [342, 11, 396, 19, "jestStyleUpdater"], [342, 27, 396, 19], [343, 2, 396, 19], [343, 3, 306, 0], [343, 7, 406, 0], [344, 2, 407, 0], [344, 11, 407, 9, "checkSharedValueUsage"], [344, 32, 407, 30, "checkSharedValueUsage"], [344, 33, 408, 2, "prop"], [344, 37, 408, 43], [344, 39, 409, 2, "current<PERSON><PERSON>"], [344, 49, 409, 21], [344, 51, 410, 8], [345, 4, 411, 2], [345, 8, 411, 6, "Array"], [345, 13, 411, 11], [345, 14, 411, 12, "isArray"], [345, 21, 411, 19], [345, 22, 411, 20, "prop"], [345, 26, 411, 24], [345, 27, 411, 25], [345, 29, 411, 27], [346, 6, 412, 4], [347, 6, 413, 4], [347, 11, 413, 9], [347, 15, 413, 15, "element"], [347, 22, 413, 22], [347, 26, 413, 26, "prop"], [347, 30, 413, 30], [347, 32, 413, 32], [348, 8, 414, 6, "checkSharedValueUsage"], [348, 29, 414, 27], [348, 30, 414, 28, "element"], [348, 37, 414, 35], [348, 39, 414, 37, "current<PERSON><PERSON>"], [348, 49, 414, 47], [348, 50, 414, 48], [349, 6, 415, 4], [350, 4, 416, 2], [350, 5, 416, 3], [350, 11, 416, 9], [350, 15, 417, 4], [350, 22, 417, 11, "prop"], [350, 26, 417, 15], [350, 31, 417, 20], [350, 39, 417, 28], [350, 43, 418, 4, "prop"], [350, 47, 418, 8], [350, 52, 418, 13], [350, 56, 418, 17], [350, 60, 419, 4, "prop"], [350, 64, 419, 8], [350, 65, 419, 9, "value"], [350, 70, 419, 14], [350, 75, 419, 19, "undefined"], [350, 84, 419, 28], [350, 86, 420, 4], [351, 6, 421, 4], [352, 6, 422, 4], [352, 11, 422, 9], [352, 15, 422, 15, "key"], [352, 18, 422, 18], [352, 22, 422, 22, "Object"], [352, 28, 422, 28], [352, 29, 422, 29, "keys"], [352, 33, 422, 33], [352, 34, 422, 34, "prop"], [352, 38, 422, 38], [352, 39, 422, 39], [352, 41, 422, 41], [353, 8, 423, 6, "checkSharedValueUsage"], [353, 29, 423, 27], [353, 30, 423, 28, "prop"], [353, 34, 423, 32], [353, 35, 423, 33, "key"], [353, 38, 423, 36], [353, 39, 423, 37], [353, 41, 423, 39, "key"], [353, 44, 423, 42], [353, 45, 423, 43], [354, 6, 424, 4], [355, 4, 425, 2], [355, 5, 425, 3], [355, 11, 425, 9], [355, 15, 426, 4, "current<PERSON><PERSON>"], [355, 25, 426, 14], [355, 30, 426, 19, "undefined"], [355, 39, 426, 28], [355, 43, 427, 4], [355, 50, 427, 11, "prop"], [355, 54, 427, 15], [355, 59, 427, 20], [355, 67, 427, 28], [355, 71, 428, 4, "prop"], [355, 75, 428, 8], [355, 80, 428, 13], [355, 84, 428, 17], [355, 88, 429, 4, "prop"], [355, 92, 429, 8], [355, 93, 429, 9, "value"], [355, 98, 429, 14], [355, 103, 429, 19, "undefined"], [355, 112, 429, 28], [355, 114, 430, 4], [356, 6, 431, 4], [357, 6, 432, 4], [357, 12, 432, 10], [357, 16, 432, 14, "ReanimatedError"], [357, 39, 432, 29], [357, 40, 433, 6], [357, 69, 433, 35, "current<PERSON><PERSON>"], [357, 79, 433, 45], [357, 120, 434, 4], [357, 121, 434, 5], [358, 4, 435, 2], [359, 2, 436, 0], [361, 2, 438, 0], [362, 0, 439, 0], [363, 0, 440, 0], [364, 0, 441, 0], [365, 0, 442, 0], [366, 0, 443, 0], [367, 0, 444, 0], [368, 0, 445, 0], [369, 0, 446, 0], [370, 0, 447, 0], [371, 0, 448, 0], [372, 0, 449, 0], [373, 2, 450, 0], [374, 2, 451, 0], [375, 2, 451, 0], [375, 6, 451, 0, "_worklet_9173457873940_init_data"], [375, 38, 451, 0], [376, 4, 451, 0, "code"], [376, 8, 451, 0], [377, 4, 451, 0, "location"], [377, 12, 451, 0], [378, 4, 451, 0, "sourceMap"], [378, 13, 451, 0], [379, 4, 451, 0, "version"], [379, 11, 451, 0], [380, 2, 451, 0], [381, 2, 451, 0], [381, 6, 451, 0, "_worklet_5314192339077_init_data"], [381, 38, 451, 0], [382, 4, 451, 0, "code"], [382, 8, 451, 0], [383, 4, 451, 0, "location"], [383, 12, 451, 0], [384, 4, 451, 0, "sourceMap"], [384, 13, 451, 0], [385, 4, 451, 0, "version"], [385, 11, 451, 0], [386, 2, 451, 0], [387, 2, 451, 0], [387, 6, 451, 0, "_worklet_245709520516_init_data"], [387, 37, 451, 0], [388, 4, 451, 0, "code"], [388, 8, 451, 0], [389, 4, 451, 0, "location"], [389, 12, 451, 0], [390, 4, 451, 0, "sourceMap"], [390, 13, 451, 0], [391, 4, 451, 0, "version"], [391, 11, 451, 0], [392, 2, 451, 0], [393, 2, 457, 7], [393, 11, 457, 16, "useAnimatedStyle"], [393, 27, 457, 32, "useAnimatedStyle"], [393, 28, 458, 2, "updater"], [393, 35, 460, 47], [393, 37, 461, 2, "dependencies"], [393, 49, 461, 38], [393, 51, 462, 2, "adapters"], [393, 59, 462, 79], [393, 61, 466, 51], [394, 4, 466, 51], [394, 8, 463, 2, "isAnimatedProps"], [394, 23, 463, 17], [394, 26, 463, 17, "arguments"], [394, 35, 463, 17], [394, 36, 463, 17, "length"], [394, 42, 463, 17], [394, 50, 463, 17, "arguments"], [394, 59, 463, 17], [394, 67, 463, 17, "undefined"], [394, 76, 463, 17], [394, 79, 463, 17, "arguments"], [394, 88, 463, 17], [394, 94, 463, 20], [394, 99, 463, 25], [395, 4, 467, 2], [395, 8, 467, 8, "animatedUpdaterData"], [395, 27, 467, 27], [395, 30, 467, 30], [395, 34, 467, 30, "useRef"], [395, 47, 467, 36], [395, 49, 467, 65], [395, 53, 467, 69], [395, 54, 467, 70], [396, 4, 468, 2], [396, 8, 468, 6, "inputs"], [396, 14, 468, 12], [396, 17, 468, 15, "Object"], [396, 23, 468, 21], [396, 24, 468, 22, "values"], [396, 30, 468, 28], [396, 31, 468, 29, "updater"], [396, 38, 468, 36], [396, 39, 468, 37, "__closure"], [396, 48, 468, 46], [396, 52, 468, 50], [396, 53, 468, 51], [396, 54, 468, 52], [396, 55, 468, 53], [397, 4, 469, 2], [397, 8, 469, 6, "SHOULD_BE_USE_WEB"], [397, 25, 469, 23], [397, 27, 469, 25], [398, 6, 470, 4], [398, 10, 470, 8], [398, 11, 470, 9, "inputs"], [398, 17, 470, 15], [398, 18, 470, 16, "length"], [398, 24, 470, 22], [398, 28, 470, 26, "dependencies"], [398, 40, 470, 38], [398, 42, 470, 40, "length"], [398, 48, 470, 46], [398, 50, 470, 48], [399, 8, 471, 6], [400, 8, 472, 6, "inputs"], [400, 14, 472, 12], [400, 17, 472, 15, "dependencies"], [400, 29, 472, 27], [401, 6, 473, 4], [402, 6, 474, 4], [402, 10, 475, 6, "__DEV__"], [402, 17, 475, 13], [402, 21, 476, 6], [402, 22, 476, 7, "inputs"], [402, 28, 476, 13], [402, 29, 476, 14, "length"], [402, 35, 476, 20], [402, 39, 477, 6], [402, 40, 477, 7, "dependencies"], [402, 52, 477, 19], [402, 56, 478, 6], [402, 57, 478, 7], [402, 61, 478, 7, "isWorkletFunction"], [402, 91, 478, 24], [402, 93, 478, 25, "updater"], [402, 100, 478, 32], [402, 101, 478, 33], [402, 103, 479, 6], [403, 8, 480, 6], [403, 14, 480, 12], [403, 18, 480, 16, "ReanimatedError"], [403, 41, 480, 31], [403, 42, 481, 8], [404, 0, 482, 0], [404, 133, 483, 6], [404, 134, 483, 7], [405, 6, 484, 4], [406, 4, 485, 2], [407, 4, 486, 2], [407, 8, 486, 8, "adaptersArray"], [407, 21, 486, 21], [407, 24, 486, 24, "adapters"], [407, 32, 486, 32], [407, 35, 487, 6, "Array"], [407, 40, 487, 11], [407, 41, 487, 12, "isArray"], [407, 48, 487, 19], [407, 49, 487, 20, "adapters"], [407, 57, 487, 28], [407, 58, 487, 29], [407, 61, 488, 8, "adapters"], [407, 69, 488, 16], [407, 72, 489, 8], [407, 73, 489, 9, "adapters"], [407, 81, 489, 17], [407, 82, 489, 18], [407, 85, 490, 6], [407, 87, 490, 8], [408, 4, 491, 2], [408, 8, 491, 8, "adaptersHash"], [408, 20, 491, 20], [408, 23, 491, 23, "adapters"], [408, 31, 491, 31], [408, 34, 491, 34], [408, 38, 491, 34, "buildWorkletsHash"], [408, 62, 491, 51], [408, 64, 491, 52, "adaptersArray"], [408, 77, 491, 65], [408, 78, 491, 66], [408, 81, 491, 69], [408, 85, 491, 73], [409, 4, 492, 2], [409, 8, 492, 8, "areAnimationsActive"], [409, 27, 492, 27], [409, 30, 492, 30], [409, 34, 492, 30, "useSharedValue"], [409, 64, 492, 44], [409, 66, 492, 54], [409, 70, 492, 58], [409, 71, 492, 59], [410, 4, 493, 2], [410, 8, 493, 8, "jestAnimatedValues"], [410, 26, 493, 26], [410, 29, 493, 29], [410, 33, 493, 29, "useRef"], [410, 46, 493, 35], [410, 48, 494, 4], [410, 49, 494, 5], [410, 50, 495, 2], [410, 51, 495, 3], [412, 4, 497, 2], [413, 4, 498, 2], [413, 8, 498, 6], [413, 9, 498, 7, "dependencies"], [413, 21, 498, 19], [413, 23, 498, 21], [414, 6, 499, 4, "dependencies"], [414, 18, 499, 16], [414, 21, 499, 19], [414, 22, 499, 20], [414, 25, 499, 23, "inputs"], [414, 31, 499, 29], [414, 33, 499, 31, "updater"], [414, 40, 499, 38], [414, 41, 499, 39, "__workletHash"], [414, 54, 499, 52], [414, 55, 499, 53], [415, 4, 500, 2], [415, 5, 500, 3], [415, 11, 500, 9], [416, 6, 501, 4, "dependencies"], [416, 18, 501, 16], [416, 19, 501, 17, "push"], [416, 23, 501, 21], [416, 24, 501, 22, "updater"], [416, 31, 501, 29], [416, 32, 501, 30, "__workletHash"], [416, 45, 501, 43], [416, 46, 501, 44], [417, 4, 502, 2], [418, 4, 503, 2, "adaptersHash"], [418, 16, 503, 14], [418, 20, 503, 18, "dependencies"], [418, 32, 503, 30], [418, 33, 503, 31, "push"], [418, 37, 503, 35], [418, 38, 503, 36, "adaptersHash"], [418, 50, 503, 48], [418, 51, 503, 49], [419, 4, 505, 2], [419, 8, 505, 6], [419, 9, 505, 7, "animatedUpdaterData"], [419, 28, 505, 26], [419, 29, 505, 27, "current"], [419, 36, 505, 34], [419, 38, 505, 36], [420, 6, 506, 4], [420, 10, 506, 10, "initialStyle"], [420, 22, 506, 22], [420, 25, 506, 25], [420, 29, 506, 25, "initialUpdaterRun"], [420, 57, 506, 42], [420, 59, 506, 43, "updater"], [420, 66, 506, 50], [420, 67, 506, 51], [421, 6, 507, 4], [421, 10, 507, 8, "__DEV__"], [421, 17, 507, 15], [421, 19, 507, 17], [422, 8, 508, 6], [422, 12, 508, 6, "validateAnimatedStyles"], [422, 41, 508, 28], [422, 43, 508, 29, "initialStyle"], [422, 55, 508, 41], [422, 56, 508, 42], [423, 6, 509, 4], [424, 6, 510, 4, "animatedUpdaterData"], [424, 25, 510, 23], [424, 26, 510, 24, "current"], [424, 33, 510, 31], [424, 36, 510, 34], [425, 8, 511, 6, "initial"], [425, 15, 511, 13], [425, 17, 511, 15], [426, 10, 512, 8, "value"], [426, 15, 512, 13], [426, 17, 512, 15, "initialStyle"], [426, 29, 512, 27], [427, 10, 513, 8, "updater"], [428, 8, 514, 6], [428, 9, 514, 7], [429, 8, 515, 6, "remoteState"], [429, 19, 515, 17], [429, 21, 515, 19], [429, 25, 515, 19, "makeShareable"], [429, 44, 515, 32], [429, 46, 515, 33], [430, 10, 516, 8, "last"], [430, 14, 516, 12], [430, 16, 516, 14, "initialStyle"], [430, 28, 516, 26], [431, 10, 517, 8, "animations"], [431, 20, 517, 18], [431, 22, 517, 20], [431, 23, 517, 21], [431, 24, 517, 22], [432, 10, 518, 8, "isAnimationCancelled"], [432, 30, 518, 28], [432, 32, 518, 30], [432, 37, 518, 35], [433, 10, 519, 8, "isAnimationRunning"], [433, 28, 519, 26], [433, 30, 519, 28], [434, 8, 520, 6], [434, 9, 520, 7], [434, 10, 520, 8], [435, 8, 521, 6, "viewDescriptors"], [435, 23, 521, 21], [435, 25, 521, 23], [435, 29, 521, 23, "makeViewDescriptorsSet"], [435, 71, 521, 45], [435, 73, 521, 46], [436, 6, 522, 4], [436, 7, 522, 5], [437, 4, 523, 2], [438, 4, 525, 2], [438, 8, 525, 2, "_animatedUpdaterData$"], [438, 29, 525, 2], [438, 32, 525, 52, "animatedUpdaterData"], [438, 51, 525, 71], [438, 52, 525, 72, "current"], [438, 59, 525, 79], [439, 6, 525, 10, "initial"], [439, 13, 525, 17], [439, 16, 525, 17, "_animatedUpdaterData$"], [439, 37, 525, 17], [439, 38, 525, 10, "initial"], [439, 45, 525, 17], [440, 6, 525, 19, "remoteState"], [440, 17, 525, 30], [440, 20, 525, 30, "_animatedUpdaterData$"], [440, 41, 525, 30], [440, 42, 525, 19, "remoteState"], [440, 53, 525, 30], [441, 6, 525, 32, "viewDescriptors"], [441, 21, 525, 47], [441, 24, 525, 47, "_animatedUpdaterData$"], [441, 45, 525, 47], [441, 46, 525, 32, "viewDescriptors"], [441, 61, 525, 47], [442, 4, 526, 2], [442, 8, 526, 8, "shareableViewDescriptors"], [442, 32, 526, 32], [442, 35, 526, 35, "viewDescriptors"], [442, 50, 526, 50], [442, 51, 526, 51, "shareableViewDescriptors"], [442, 75, 526, 75], [443, 4, 528, 2, "dependencies"], [443, 16, 528, 14], [443, 17, 528, 15, "push"], [443, 21, 528, 19], [443, 22, 528, 20, "shareableViewDescriptors"], [443, 46, 528, 44], [443, 47, 528, 45], [444, 4, 530, 2], [444, 8, 530, 2, "useEffect"], [444, 24, 530, 11], [444, 26, 530, 12], [444, 32, 530, 18], [445, 6, 531, 4], [445, 10, 531, 8, "fun"], [445, 13, 531, 11], [446, 6, 532, 4], [446, 10, 532, 8, "updaterFn"], [446, 19, 532, 17], [446, 22, 532, 20, "updater"], [446, 29, 532, 27], [447, 6, 533, 4], [447, 10, 533, 8, "adapters"], [447, 18, 533, 16], [447, 20, 533, 18], [448, 8, 534, 6, "updaterFn"], [448, 17, 534, 15], [448, 20, 534, 19], [449, 10, 534, 19], [449, 14, 534, 19, "_e"], [449, 16, 534, 19], [449, 24, 534, 19, "global"], [449, 30, 534, 19], [449, 31, 534, 19, "Error"], [449, 36, 534, 19], [450, 10, 534, 19], [450, 14, 534, 19, "reactNativeReanimated_useAnimatedStyleTs5"], [450, 55, 534, 19], [450, 67, 534, 19, "reactNativeReanimated_useAnimatedStyleTs5"], [450, 68, 534, 19], [450, 70, 534, 25], [451, 12, 536, 8], [451, 16, 536, 14, "newValues"], [451, 25, 536, 23], [451, 28, 536, 26, "updater"], [451, 35, 536, 33], [451, 36, 536, 34], [451, 37, 536, 35], [452, 12, 537, 8, "adaptersArray"], [452, 25, 537, 21], [452, 26, 537, 22, "for<PERSON>ach"], [452, 33, 537, 29], [452, 34, 537, 31, "adapter"], [452, 41, 537, 38], [452, 45, 537, 43], [453, 14, 538, 10, "adapter"], [453, 21, 538, 17], [453, 22, 538, 18, "newValues"], [453, 31, 538, 54], [453, 32, 538, 55], [454, 12, 539, 8], [454, 13, 539, 9], [454, 14, 539, 10], [455, 12, 540, 8], [455, 19, 540, 15, "newValues"], [455, 28, 540, 24], [456, 10, 541, 6], [456, 11, 541, 7], [457, 10, 541, 7, "reactNativeReanimated_useAnimatedStyleTs5"], [457, 51, 541, 7], [457, 52, 541, 7, "__closure"], [457, 61, 541, 7], [458, 12, 541, 7, "updater"], [458, 19, 541, 7], [459, 12, 541, 7, "adaptersArray"], [460, 10, 541, 7], [461, 10, 541, 7, "reactNativeReanimated_useAnimatedStyleTs5"], [461, 51, 541, 7], [461, 52, 541, 7, "__workletHash"], [461, 65, 541, 7], [462, 10, 541, 7, "reactNativeReanimated_useAnimatedStyleTs5"], [462, 51, 541, 7], [462, 52, 541, 7, "__initData"], [462, 62, 541, 7], [462, 65, 541, 7, "_worklet_9173457873940_init_data"], [462, 97, 541, 7], [463, 10, 541, 7, "reactNativeReanimated_useAnimatedStyleTs5"], [463, 51, 541, 7], [463, 52, 541, 7, "__stackDetails"], [463, 66, 541, 7], [463, 69, 541, 7, "_e"], [463, 71, 541, 7], [464, 10, 541, 7], [464, 17, 541, 7, "reactNativeReanimated_useAnimatedStyleTs5"], [464, 58, 541, 7], [465, 8, 541, 7], [465, 9, 534, 19], [465, 11, 541, 38], [466, 6, 542, 4], [467, 6, 544, 4], [467, 10, 544, 8], [467, 14, 544, 8, "isJest"], [467, 37, 544, 14], [467, 39, 544, 15], [467, 40, 544, 16], [467, 42, 544, 18], [468, 8, 545, 6, "fun"], [468, 11, 545, 9], [468, 14, 545, 12], [469, 10, 545, 12], [469, 14, 545, 12, "_e"], [469, 16, 545, 12], [469, 24, 545, 12, "global"], [469, 30, 545, 12], [469, 31, 545, 12, "Error"], [469, 36, 545, 12], [470, 10, 545, 12], [470, 14, 545, 12, "reactNativeReanimated_useAnimatedStyleTs6"], [470, 55, 545, 12], [470, 67, 545, 12, "reactNativeReanimated_useAnimatedStyleTs6"], [470, 68, 545, 12], [470, 70, 545, 18], [471, 12, 547, 8, "jestStyleUpdater"], [471, 28, 547, 24], [471, 29, 548, 10, "shareableViewDescriptors"], [471, 53, 548, 34], [471, 55, 549, 10, "updater"], [471, 62, 549, 17], [471, 64, 550, 10, "remoteState"], [471, 75, 550, 21], [471, 77, 551, 10, "areAnimationsActive"], [471, 96, 551, 29], [471, 98, 552, 10, "jestAnimatedValues"], [471, 116, 552, 28], [471, 118, 553, 10, "adaptersArray"], [471, 131, 554, 8], [471, 132, 554, 9], [472, 10, 555, 6], [472, 11, 555, 7], [473, 10, 555, 7, "reactNativeReanimated_useAnimatedStyleTs6"], [473, 51, 555, 7], [473, 52, 555, 7, "__closure"], [473, 61, 555, 7], [474, 12, 555, 7, "jestStyleUpdater"], [474, 28, 555, 7], [475, 12, 555, 7, "shareableViewDescriptors"], [475, 36, 555, 7], [476, 12, 555, 7, "updater"], [476, 19, 555, 7], [477, 12, 555, 7, "remoteState"], [477, 23, 555, 7], [478, 12, 555, 7, "areAnimationsActive"], [478, 31, 555, 7], [479, 12, 555, 7, "jestAnimatedValues"], [479, 30, 555, 7], [480, 12, 555, 7, "adaptersArray"], [481, 10, 555, 7], [482, 10, 555, 7, "reactNativeReanimated_useAnimatedStyleTs6"], [482, 51, 555, 7], [482, 52, 555, 7, "__workletHash"], [482, 65, 555, 7], [483, 10, 555, 7, "reactNativeReanimated_useAnimatedStyleTs6"], [483, 51, 555, 7], [483, 52, 555, 7, "__initData"], [483, 62, 555, 7], [483, 65, 555, 7, "_worklet_5314192339077_init_data"], [483, 97, 555, 7], [484, 10, 555, 7, "reactNativeReanimated_useAnimatedStyleTs6"], [484, 51, 555, 7], [484, 52, 555, 7, "__stackDetails"], [484, 66, 555, 7], [484, 69, 555, 7, "_e"], [484, 71, 555, 7], [485, 10, 555, 7], [485, 17, 555, 7, "reactNativeReanimated_useAnimatedStyleTs6"], [485, 58, 555, 7], [486, 8, 555, 7], [486, 9, 545, 12], [486, 11, 555, 7], [487, 6, 556, 4], [487, 7, 556, 5], [487, 13, 556, 11], [488, 8, 557, 6, "fun"], [488, 11, 557, 9], [488, 14, 557, 12], [489, 10, 557, 12], [489, 14, 557, 12, "_e"], [489, 16, 557, 12], [489, 24, 557, 12, "global"], [489, 30, 557, 12], [489, 31, 557, 12, "Error"], [489, 36, 557, 12], [490, 10, 557, 12], [490, 14, 557, 12, "reactNativeReanimated_useAnimatedStyleTs7"], [490, 55, 557, 12], [490, 67, 557, 12, "reactNativeReanimated_useAnimatedStyleTs7"], [490, 68, 557, 12], [490, 70, 557, 18], [491, 12, 559, 8, "styleUpdater"], [491, 24, 559, 20], [491, 25, 560, 10, "shareableViewDescriptors"], [491, 49, 560, 34], [491, 51, 561, 10, "updaterFn"], [491, 60, 561, 19], [491, 62, 562, 10, "remoteState"], [491, 73, 562, 21], [491, 75, 563, 10, "areAnimationsActive"], [491, 94, 563, 29], [491, 96, 564, 10, "isAnimatedProps"], [491, 111, 565, 8], [491, 112, 565, 9], [492, 10, 566, 6], [492, 11, 566, 7], [493, 10, 566, 7, "reactNativeReanimated_useAnimatedStyleTs7"], [493, 51, 566, 7], [493, 52, 566, 7, "__closure"], [493, 61, 566, 7], [494, 12, 566, 7, "styleUpdater"], [494, 24, 566, 7], [495, 12, 566, 7, "shareableViewDescriptors"], [495, 36, 566, 7], [496, 12, 566, 7, "updaterFn"], [496, 21, 566, 7], [497, 12, 566, 7, "remoteState"], [497, 23, 566, 7], [498, 12, 566, 7, "areAnimationsActive"], [498, 31, 566, 7], [499, 12, 566, 7, "isAnimatedProps"], [500, 10, 566, 7], [501, 10, 566, 7, "reactNativeReanimated_useAnimatedStyleTs7"], [501, 51, 566, 7], [501, 52, 566, 7, "__workletHash"], [501, 65, 566, 7], [502, 10, 566, 7, "reactNativeReanimated_useAnimatedStyleTs7"], [502, 51, 566, 7], [502, 52, 566, 7, "__initData"], [502, 62, 566, 7], [502, 65, 566, 7, "_worklet_245709520516_init_data"], [502, 96, 566, 7], [503, 10, 566, 7, "reactNativeReanimated_useAnimatedStyleTs7"], [503, 51, 566, 7], [503, 52, 566, 7, "__stackDetails"], [503, 66, 566, 7], [503, 69, 566, 7, "_e"], [503, 71, 566, 7], [504, 10, 566, 7], [504, 17, 566, 7, "reactNativeReanimated_useAnimatedStyleTs7"], [504, 58, 566, 7], [505, 8, 566, 7], [505, 9, 557, 12], [505, 11, 566, 7], [506, 6, 567, 4], [507, 6, 568, 4], [507, 10, 568, 10, "mapperId"], [507, 18, 568, 18], [507, 21, 568, 21], [507, 25, 568, 21, "startMapper"], [507, 42, 568, 32], [507, 44, 568, 33, "fun"], [507, 47, 568, 36], [507, 49, 568, 38, "inputs"], [507, 55, 568, 44], [507, 56, 568, 45], [508, 6, 569, 4], [508, 13, 569, 11], [508, 19, 569, 17], [509, 8, 570, 6], [509, 12, 570, 6, "stopMapper"], [509, 28, 570, 16], [509, 30, 570, 17, "mapperId"], [509, 38, 570, 25], [509, 39, 570, 26], [510, 6, 571, 4], [510, 7, 571, 5], [511, 6, 572, 4], [512, 4, 573, 2], [512, 5, 573, 3], [512, 7, 573, 5, "dependencies"], [512, 19, 573, 17], [512, 20, 573, 18], [513, 4, 575, 2], [513, 8, 575, 2, "useEffect"], [513, 24, 575, 11], [513, 26, 575, 12], [513, 32, 575, 18], [514, 6, 576, 4, "areAnimationsActive"], [514, 25, 576, 23], [514, 26, 576, 24, "value"], [514, 31, 576, 29], [514, 34, 576, 32], [514, 38, 576, 36], [515, 6, 577, 4], [515, 13, 577, 11], [515, 19, 577, 17], [516, 8, 578, 6, "areAnimationsActive"], [516, 27, 578, 25], [516, 28, 578, 26, "value"], [516, 33, 578, 31], [516, 36, 578, 34], [516, 41, 578, 39], [517, 6, 579, 4], [517, 7, 579, 5], [518, 4, 580, 2], [518, 5, 580, 3], [518, 7, 580, 5], [518, 8, 580, 6, "areAnimationsActive"], [518, 27, 580, 25], [518, 28, 580, 26], [518, 29, 580, 27], [519, 4, 582, 2, "checkSharedValueUsage"], [519, 25, 582, 23], [519, 26, 582, 24, "initial"], [519, 33, 582, 31], [519, 34, 582, 32, "value"], [519, 39, 582, 37], [519, 40, 582, 38], [520, 4, 584, 2], [520, 8, 584, 8, "animatedStyleHandle"], [520, 27, 584, 27], [520, 30, 584, 30], [520, 34, 584, 30, "useRef"], [520, 47, 584, 36], [520, 49, 588, 4], [520, 53, 588, 8], [520, 54, 588, 9], [521, 4, 590, 2], [521, 8, 590, 6], [521, 9, 590, 7, "animatedStyleHandle"], [521, 28, 590, 26], [521, 29, 590, 27, "current"], [521, 36, 590, 34], [521, 38, 590, 36], [522, 6, 591, 4, "animatedStyleHandle"], [522, 25, 591, 23], [522, 26, 591, 24, "current"], [522, 33, 591, 31], [522, 36, 591, 34], [522, 40, 591, 34, "isJest"], [522, 63, 591, 40], [522, 65, 591, 41], [522, 66, 591, 42], [522, 69, 592, 8], [523, 8, 592, 10, "viewDescriptors"], [523, 23, 592, 25], [524, 8, 592, 27, "initial"], [524, 15, 592, 34], [525, 8, 592, 36, "jestAnimatedValues"], [526, 6, 592, 55], [526, 7, 592, 56], [526, 10, 593, 8], [527, 8, 593, 10, "viewDescriptors"], [527, 23, 593, 25], [528, 8, 593, 27, "initial"], [529, 6, 593, 35], [529, 7, 593, 36], [530, 4, 594, 2], [531, 4, 596, 2], [531, 11, 596, 9, "animatedStyleHandle"], [531, 30, 596, 28], [531, 31, 596, 29, "current"], [531, 38, 596, 36], [532, 2, 597, 0], [533, 0, 597, 1], [533, 3]], "functionMap": {"names": ["<global>", "prepareAnimation", "animatedProp.forEach$argument_0", "animation.callStart", "Object.keys.forEach$argument_0", "runAnimations", "animation.forEach$argument_0", "styleUpdater", "frame", "updates.propName.forEach$argument_0", "jestStyleUpdater", "checkSharedValueUsage", "useAnimatedStyle", "useEffect$argument_0", "<anonymous>", "adaptersArray.forEach$argument_0", "fun"], "mappings": "AAA;AC0D;yBCQ;KDO;0BE2B;KFE;sCGK;OHM;CDG;AKE;sBCgB;KDa;mCD8B;KCa;CLM;AOE;kBCmC;sCC0B;aDI;KDmB;CPqB;AUE;kCNgB;GMK;iCNC;GMS;EFE;oCJU;KIc;GEgB;CVyB;AWG;CX6B;OYqB;YCyE;mBCI;8BCG;SDE;ODE;YGI;OHU;YGE;OHS;WCG;KDE;GDE;YCE;WCE;KDE;GDC;CZiB"}}, "type": "js/module"}]}