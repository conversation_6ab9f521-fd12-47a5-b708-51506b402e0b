{"dependencies": [{"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 164, "index": 179}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 180}, "end": {"line": 4, "column": 31, "index": 211}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 212}, "end": {"line": 5, "column": 49, "index": 261}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./extractPathFromURL.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 262}, "end": {"line": 6, "column": 61, "index": 323}}], "key": "KybhRZZjzdT5ZH0MTAmAtk+8vRk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useLinking = useLinking;\n  var _core = require(_dependencyMap[0], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _extractPathFromURL = require(_dependencyMap[3], \"./extractPathFromURL.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var linkingHandlers = [];\n  function useLinking(ref, _ref, onUnhandledLinking) {\n    var _ref$enabled = _ref.enabled,\n      enabled = _ref$enabled === void 0 ? true : _ref$enabled,\n      prefixes = _ref.prefixes,\n      filter = _ref.filter,\n      config = _ref.config,\n      _ref$getInitialURL = _ref.getInitialURL,\n      getInitialURL = _ref$getInitialURL === void 0 ? () => Promise.race([_reactNative.Linking.getInitialURL(), new Promise(resolve => {\n        // Timeout in 150ms if `getInitialState` doesn't resolve\n        // Workaround for https://github.com/facebook/react-native/issues/25675\n        setTimeout(resolve, 150);\n      })]) : _ref$getInitialURL,\n      _ref$subscribe = _ref.subscribe,\n      subscribe = _ref$subscribe === void 0 ? listener => {\n        var callback = _ref2 => {\n          var url = _ref2.url;\n          return listener(url);\n        };\n        var subscription = _reactNative.Linking.addEventListener('url', callback);\n\n        // Storing this in a local variable stops Jest from complaining about import after teardown\n        // @ts-expect-error: removeEventListener is not present in newer RN versions\n        var removeEventListener = _reactNative.Linking.removeEventListener?.bind(_reactNative.Linking);\n        return () => {\n          // https://github.com/facebook/react-native/commit/6d1aca806cee86ad76de771ed3a1cc62982ebcd7\n          if (subscription?.remove) {\n            subscription.remove();\n          } else {\n            removeEventListener?.('url', callback);\n          }\n        };\n      } : _ref$subscribe,\n      _ref$getStateFromPath = _ref.getStateFromPath,\n      getStateFromPath = _ref$getStateFromPath === void 0 ? _core.getStateFromPath : _ref$getStateFromPath,\n      _ref$getActionFromSta = _ref.getActionFromState,\n      getActionFromState = _ref$getActionFromSta === void 0 ? _core.getActionFromState : _ref$getActionFromSta;\n    var independent = (0, _core.useNavigationIndependentTree)();\n    React.useEffect(() => {\n      if (process.env.NODE_ENV === 'production') {\n        return undefined;\n      }\n      if (independent) {\n        return undefined;\n      }\n      if (enabled !== false && linkingHandlers.length) {\n        console.error(['Looks like you have configured linking in multiple places. This is likely an error since deep links should only be handled in one place to avoid conflicts. Make sure that:', \"- You don't have multiple NavigationContainers in the app each with 'linking' enabled\", '- Only a single instance of the root component is rendered', _reactNative.Platform.OS === 'android' ? \"- You have set 'android:launchMode=singleTask' in the '<activity />' section of the 'AndroidManifest.xml' file to avoid launching multiple instances\" : ''].join('\\n').trim());\n      }\n      var handler = Symbol();\n      if (enabled !== false) {\n        linkingHandlers.push(handler);\n      }\n      return () => {\n        var index = linkingHandlers.indexOf(handler);\n        if (index > -1) {\n          linkingHandlers.splice(index, 1);\n        }\n      };\n    }, [enabled, independent]);\n\n    // We store these options in ref to avoid re-creating getInitialState and re-subscribing listeners\n    // This lets user avoid wrapping the items in `React.useCallback` or `React.useMemo`\n    // Not re-creating `getInitialState` is important coz it makes it easier for the user to use in an effect\n    var enabledRef = React.useRef(enabled);\n    var prefixesRef = React.useRef(prefixes);\n    var filterRef = React.useRef(filter);\n    var configRef = React.useRef(config);\n    var getInitialURLRef = React.useRef(getInitialURL);\n    var getStateFromPathRef = React.useRef(getStateFromPath);\n    var getActionFromStateRef = React.useRef(getActionFromState);\n    React.useEffect(() => {\n      enabledRef.current = enabled;\n      prefixesRef.current = prefixes;\n      filterRef.current = filter;\n      configRef.current = config;\n      getInitialURLRef.current = getInitialURL;\n      getStateFromPathRef.current = getStateFromPath;\n      getActionFromStateRef.current = getActionFromState;\n    });\n    var getStateFromURL = React.useCallback(url => {\n      if (!url || filterRef.current && !filterRef.current(url)) {\n        return undefined;\n      }\n      var path = (0, _extractPathFromURL.extractPathFromURL)(prefixesRef.current, url);\n      return path !== undefined ? getStateFromPathRef.current(path, configRef.current) : undefined;\n    }, []);\n    var getInitialState = React.useCallback(() => {\n      var state;\n      if (enabledRef.current) {\n        var url = getInitialURLRef.current();\n        if (url != null) {\n          if (typeof url !== 'string') {\n            return url.then(url => {\n              var state = getStateFromURL(url);\n              if (typeof url === 'string') {\n                // If the link were handled, it gets cleared in NavigationContainer\n                onUnhandledLinking((0, _extractPathFromURL.extractPathFromURL)(prefixes, url));\n              }\n              return state;\n            });\n          } else {\n            onUnhandledLinking((0, _extractPathFromURL.extractPathFromURL)(prefixes, url));\n          }\n        }\n        state = getStateFromURL(url);\n      }\n      var thenable = {\n        then(onfulfilled) {\n          return Promise.resolve(onfulfilled ? onfulfilled(state) : state);\n        },\n        catch() {\n          return thenable;\n        }\n      };\n      return thenable;\n    }, [getStateFromURL, onUnhandledLinking, prefixes]);\n    React.useEffect(() => {\n      var listener = url => {\n        if (!enabled) {\n          return;\n        }\n        var navigation = ref.current;\n        var state = navigation ? getStateFromURL(url) : undefined;\n        if (navigation && state) {\n          // If the link were handled, it gets cleared in NavigationContainer\n          onUnhandledLinking((0, _extractPathFromURL.extractPathFromURL)(prefixes, url));\n          var rootState = navigation.getRootState();\n          if (state.routes.some(r => !rootState?.routeNames.includes(r.name))) {\n            return;\n          }\n          var action = getActionFromStateRef.current(state, configRef.current);\n          if (action !== undefined) {\n            try {\n              navigation.dispatch(action);\n            } catch (e) {\n              // Ignore any errors from deep linking.\n              // This could happen in case of malformed links, navigation object not being initialized etc.\n              console.warn(`An error occurred when trying to handle the link '${url}': ${typeof e === 'object' && e != null && 'message' in e ? e.message : e}`);\n            }\n          } else {\n            navigation.resetRoot(state);\n          }\n        }\n      };\n      return subscribe(listener);\n    }, [enabled, getStateFromURL, onUnhandledLinking, prefixes, ref, subscribe]);\n    return {\n      getInitialState\n    };\n  }\n});", "lineCount": 163, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useLinking"], [7, 20, 1, 13], [7, 23, 1, 13, "useLinking"], [7, 33, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_reactNative"], [10, 18, 5, 0], [10, 21, 5, 0, "require"], [10, 28, 5, 0], [10, 29, 5, 0, "_dependencyMap"], [10, 43, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_extractPathFromURL"], [11, 25, 6, 0], [11, 28, 6, 0, "require"], [11, 35, 6, 0], [11, 36, 6, 0, "_dependencyMap"], [11, 50, 6, 0], [12, 2, 6, 61], [12, 11, 6, 61, "_interopRequireWildcard"], [12, 35, 6, 61, "e"], [12, 36, 6, 61], [12, 38, 6, 61, "t"], [12, 39, 6, 61], [12, 68, 6, 61, "WeakMap"], [12, 75, 6, 61], [12, 81, 6, 61, "r"], [12, 82, 6, 61], [12, 89, 6, 61, "WeakMap"], [12, 96, 6, 61], [12, 100, 6, 61, "n"], [12, 101, 6, 61], [12, 108, 6, 61, "WeakMap"], [12, 115, 6, 61], [12, 127, 6, 61, "_interopRequireWildcard"], [12, 150, 6, 61], [12, 162, 6, 61, "_interopRequireWildcard"], [12, 163, 6, 61, "e"], [12, 164, 6, 61], [12, 166, 6, 61, "t"], [12, 167, 6, 61], [12, 176, 6, 61, "t"], [12, 177, 6, 61], [12, 181, 6, 61, "e"], [12, 182, 6, 61], [12, 186, 6, 61, "e"], [12, 187, 6, 61], [12, 188, 6, 61, "__esModule"], [12, 198, 6, 61], [12, 207, 6, 61, "e"], [12, 208, 6, 61], [12, 214, 6, 61, "o"], [12, 215, 6, 61], [12, 217, 6, 61, "i"], [12, 218, 6, 61], [12, 220, 6, 61, "f"], [12, 221, 6, 61], [12, 226, 6, 61, "__proto__"], [12, 235, 6, 61], [12, 243, 6, 61, "default"], [12, 250, 6, 61], [12, 252, 6, 61, "e"], [12, 253, 6, 61], [12, 270, 6, 61, "e"], [12, 271, 6, 61], [12, 294, 6, 61, "e"], [12, 295, 6, 61], [12, 320, 6, 61, "e"], [12, 321, 6, 61], [12, 330, 6, 61, "f"], [12, 331, 6, 61], [12, 337, 6, 61, "o"], [12, 338, 6, 61], [12, 341, 6, 61, "t"], [12, 342, 6, 61], [12, 345, 6, 61, "n"], [12, 346, 6, 61], [12, 349, 6, 61, "r"], [12, 350, 6, 61], [12, 358, 6, 61, "o"], [12, 359, 6, 61], [12, 360, 6, 61, "has"], [12, 363, 6, 61], [12, 364, 6, 61, "e"], [12, 365, 6, 61], [12, 375, 6, 61, "o"], [12, 376, 6, 61], [12, 377, 6, 61, "get"], [12, 380, 6, 61], [12, 381, 6, 61, "e"], [12, 382, 6, 61], [12, 385, 6, 61, "o"], [12, 386, 6, 61], [12, 387, 6, 61, "set"], [12, 390, 6, 61], [12, 391, 6, 61, "e"], [12, 392, 6, 61], [12, 394, 6, 61, "f"], [12, 395, 6, 61], [12, 409, 6, 61, "_t"], [12, 411, 6, 61], [12, 415, 6, 61, "e"], [12, 416, 6, 61], [12, 432, 6, 61, "_t"], [12, 434, 6, 61], [12, 441, 6, 61, "hasOwnProperty"], [12, 455, 6, 61], [12, 456, 6, 61, "call"], [12, 460, 6, 61], [12, 461, 6, 61, "e"], [12, 462, 6, 61], [12, 464, 6, 61, "_t"], [12, 466, 6, 61], [12, 473, 6, 61, "i"], [12, 474, 6, 61], [12, 478, 6, 61, "o"], [12, 479, 6, 61], [12, 482, 6, 61, "Object"], [12, 488, 6, 61], [12, 489, 6, 61, "defineProperty"], [12, 503, 6, 61], [12, 508, 6, 61, "Object"], [12, 514, 6, 61], [12, 515, 6, 61, "getOwnPropertyDescriptor"], [12, 539, 6, 61], [12, 540, 6, 61, "e"], [12, 541, 6, 61], [12, 543, 6, 61, "_t"], [12, 545, 6, 61], [12, 552, 6, 61, "i"], [12, 553, 6, 61], [12, 554, 6, 61, "get"], [12, 557, 6, 61], [12, 561, 6, 61, "i"], [12, 562, 6, 61], [12, 563, 6, 61, "set"], [12, 566, 6, 61], [12, 570, 6, 61, "o"], [12, 571, 6, 61], [12, 572, 6, 61, "f"], [12, 573, 6, 61], [12, 575, 6, 61, "_t"], [12, 577, 6, 61], [12, 579, 6, 61, "i"], [12, 580, 6, 61], [12, 584, 6, 61, "f"], [12, 585, 6, 61], [12, 586, 6, 61, "_t"], [12, 588, 6, 61], [12, 592, 6, 61, "e"], [12, 593, 6, 61], [12, 594, 6, 61, "_t"], [12, 596, 6, 61], [12, 607, 6, 61, "f"], [12, 608, 6, 61], [12, 613, 6, 61, "e"], [12, 614, 6, 61], [12, 616, 6, 61, "t"], [12, 617, 6, 61], [13, 2, 7, 0], [13, 6, 7, 6, "linkingHandlers"], [13, 21, 7, 21], [13, 24, 7, 24], [13, 26, 7, 26], [14, 2, 8, 7], [14, 11, 8, 16, "useLinking"], [14, 21, 8, 26, "useLinking"], [14, 22, 8, 27, "ref"], [14, 25, 8, 30], [14, 27, 8, 30, "_ref"], [14, 31, 8, 30], [14, 33, 38, 3, "onUnhandledLinking"], [14, 51, 38, 21], [14, 53, 38, 23], [15, 4, 38, 23], [15, 8, 38, 23, "_ref$enabled"], [15, 20, 38, 23], [15, 23, 38, 23, "_ref"], [15, 27, 38, 23], [15, 28, 9, 2, "enabled"], [15, 35, 9, 9], [16, 6, 9, 2, "enabled"], [16, 13, 9, 9], [16, 16, 9, 9, "_ref$enabled"], [16, 28, 9, 9], [16, 42, 9, 12], [16, 46, 9, 16], [16, 49, 9, 16, "_ref$enabled"], [16, 61, 9, 16], [17, 6, 10, 2, "prefixes"], [17, 14, 10, 10], [17, 17, 10, 10, "_ref"], [17, 21, 10, 10], [17, 22, 10, 2, "prefixes"], [17, 30, 10, 10], [18, 6, 11, 2, "filter"], [18, 12, 11, 8], [18, 15, 11, 8, "_ref"], [18, 19, 11, 8], [18, 20, 11, 2, "filter"], [18, 26, 11, 8], [19, 6, 12, 2, "config"], [19, 12, 12, 8], [19, 15, 12, 8, "_ref"], [19, 19, 12, 8], [19, 20, 12, 2, "config"], [19, 26, 12, 8], [20, 6, 12, 8, "_ref$getInitialURL"], [20, 24, 12, 8], [20, 27, 12, 8, "_ref"], [20, 31, 12, 8], [20, 32, 13, 2, "getInitialURL"], [20, 45, 13, 15], [21, 6, 13, 2, "getInitialURL"], [21, 19, 13, 15], [21, 22, 13, 15, "_ref$getInitialURL"], [21, 40, 13, 15], [21, 54, 13, 18], [21, 60, 13, 24, "Promise"], [21, 67, 13, 31], [21, 68, 13, 32, "race"], [21, 72, 13, 36], [21, 73, 13, 37], [21, 74, 13, 38, "Linking"], [21, 94, 13, 45], [21, 95, 13, 46, "getInitialURL"], [21, 108, 13, 59], [21, 109, 13, 60], [21, 110, 13, 61], [21, 112, 13, 63], [21, 116, 13, 67, "Promise"], [21, 123, 13, 74], [21, 124, 13, 75, "resolve"], [21, 131, 13, 82], [21, 135, 13, 86], [22, 8, 14, 4], [23, 8, 15, 4], [24, 8, 16, 4, "setTimeout"], [24, 18, 16, 14], [24, 19, 16, 15, "resolve"], [24, 26, 16, 22], [24, 28, 16, 24], [24, 31, 16, 27], [24, 32, 16, 28], [25, 6, 17, 2], [25, 7, 17, 3], [25, 8, 17, 4], [25, 9, 17, 5], [25, 10, 17, 6], [25, 13, 17, 6, "_ref$getInitialURL"], [25, 31, 17, 6], [26, 6, 17, 6, "_ref$subscribe"], [26, 20, 17, 6], [26, 23, 17, 6, "_ref"], [26, 27, 17, 6], [26, 28, 18, 2, "subscribe"], [26, 37, 18, 11], [27, 6, 18, 2, "subscribe"], [27, 15, 18, 11], [27, 18, 18, 11, "_ref$subscribe"], [27, 32, 18, 11], [27, 46, 18, 14, "listener"], [27, 54, 18, 22], [27, 58, 18, 26], [28, 8, 19, 4], [28, 12, 19, 10, "callback"], [28, 20, 19, 18], [28, 23, 19, 21, "_ref2"], [28, 28, 19, 21], [29, 10, 19, 21], [29, 14, 20, 6, "url"], [29, 17, 20, 9], [29, 20, 20, 9, "_ref2"], [29, 25, 20, 9], [29, 26, 20, 6, "url"], [29, 29, 20, 9], [30, 10, 20, 9], [30, 17, 21, 10, "listener"], [30, 25, 21, 18], [30, 26, 21, 19, "url"], [30, 29, 21, 22], [30, 30, 21, 23], [31, 8, 21, 23], [32, 8, 22, 4], [32, 12, 22, 10, "subscription"], [32, 24, 22, 22], [32, 27, 22, 25, "Linking"], [32, 47, 22, 32], [32, 48, 22, 33, "addEventListener"], [32, 64, 22, 49], [32, 65, 22, 50], [32, 70, 22, 55], [32, 72, 22, 57, "callback"], [32, 80, 22, 65], [32, 81, 22, 66], [34, 8, 24, 4], [35, 8, 25, 4], [36, 8, 26, 4], [36, 12, 26, 10, "removeEventListener"], [36, 31, 26, 29], [36, 34, 26, 32, "Linking"], [36, 54, 26, 39], [36, 55, 26, 40, "removeEventListener"], [36, 74, 26, 59], [36, 76, 26, 61, "bind"], [36, 80, 26, 65], [36, 81, 26, 66, "Linking"], [36, 101, 26, 73], [36, 102, 26, 74], [37, 8, 27, 4], [37, 15, 27, 11], [37, 21, 27, 17], [38, 10, 28, 6], [39, 10, 29, 6], [39, 14, 29, 10, "subscription"], [39, 26, 29, 22], [39, 28, 29, 24, "remove"], [39, 34, 29, 30], [39, 36, 29, 32], [40, 12, 30, 8, "subscription"], [40, 24, 30, 20], [40, 25, 30, 21, "remove"], [40, 31, 30, 27], [40, 32, 30, 28], [40, 33, 30, 29], [41, 10, 31, 6], [41, 11, 31, 7], [41, 17, 31, 13], [42, 12, 32, 8, "removeEventListener"], [42, 31, 32, 27], [42, 34, 32, 30], [42, 39, 32, 35], [42, 41, 32, 37, "callback"], [42, 49, 32, 45], [42, 50, 32, 46], [43, 10, 33, 6], [44, 8, 34, 4], [44, 9, 34, 5], [45, 6, 35, 2], [45, 7, 35, 3], [45, 10, 35, 3, "_ref$subscribe"], [45, 24, 35, 3], [46, 6, 35, 3, "_ref$getStateFromPath"], [46, 27, 35, 3], [46, 30, 35, 3, "_ref"], [46, 34, 35, 3], [46, 35, 36, 2, "getStateFromPath"], [46, 51, 36, 18], [47, 6, 36, 2, "getStateFromPath"], [47, 22, 36, 18], [47, 25, 36, 18, "_ref$getStateFromPath"], [47, 46, 36, 18], [47, 60, 36, 21, "getStateFromPathDefault"], [47, 82, 36, 44], [47, 85, 36, 44, "_ref$getStateFromPath"], [47, 106, 36, 44], [48, 6, 36, 44, "_ref$getActionFromSta"], [48, 27, 36, 44], [48, 30, 36, 44, "_ref"], [48, 34, 36, 44], [48, 35, 37, 2, "getActionFromState"], [48, 53, 37, 20], [49, 6, 37, 2, "getActionFromState"], [49, 24, 37, 20], [49, 27, 37, 20, "_ref$getActionFromSta"], [49, 48, 37, 20], [49, 62, 37, 23, "getActionFromStateDefault"], [49, 86, 37, 48], [49, 89, 37, 48, "_ref$getActionFromSta"], [49, 110, 37, 48], [50, 4, 39, 2], [50, 8, 39, 8, "independent"], [50, 19, 39, 19], [50, 22, 39, 22], [50, 26, 39, 22, "useNavigationIndependentTree"], [50, 60, 39, 50], [50, 62, 39, 51], [50, 63, 39, 52], [51, 4, 40, 2, "React"], [51, 9, 40, 7], [51, 10, 40, 8, "useEffect"], [51, 19, 40, 17], [51, 20, 40, 18], [51, 26, 40, 24], [52, 6, 41, 4], [52, 10, 41, 8, "process"], [52, 17, 41, 15], [52, 18, 41, 16, "env"], [52, 21, 41, 19], [52, 22, 41, 20, "NODE_ENV"], [52, 30, 41, 28], [52, 35, 41, 33], [52, 47, 41, 45], [52, 49, 41, 47], [53, 8, 42, 6], [53, 15, 42, 13, "undefined"], [53, 24, 42, 22], [54, 6, 43, 4], [55, 6, 44, 4], [55, 10, 44, 8, "independent"], [55, 21, 44, 19], [55, 23, 44, 21], [56, 8, 45, 6], [56, 15, 45, 13, "undefined"], [56, 24, 45, 22], [57, 6, 46, 4], [58, 6, 47, 4], [58, 10, 47, 8, "enabled"], [58, 17, 47, 15], [58, 22, 47, 20], [58, 27, 47, 25], [58, 31, 47, 29, "linkingHandlers"], [58, 46, 47, 44], [58, 47, 47, 45, "length"], [58, 53, 47, 51], [58, 55, 47, 53], [59, 8, 48, 6, "console"], [59, 15, 48, 13], [59, 16, 48, 14, "error"], [59, 21, 48, 19], [59, 22, 48, 20], [59, 23, 48, 21], [59, 196, 48, 194], [59, 198, 48, 196], [59, 285, 48, 283], [59, 287, 48, 285], [59, 347, 48, 345], [59, 349, 48, 347, "Platform"], [59, 370, 48, 355], [59, 371, 48, 356, "OS"], [59, 373, 48, 358], [59, 378, 48, 363], [59, 387, 48, 372], [59, 390, 48, 375], [59, 540, 48, 525], [59, 543, 48, 528], [59, 545, 48, 530], [59, 546, 48, 531], [59, 547, 48, 532, "join"], [59, 551, 48, 536], [59, 552, 48, 537], [59, 556, 48, 541], [59, 557, 48, 542], [59, 558, 48, 543, "trim"], [59, 562, 48, 547], [59, 563, 48, 548], [59, 564, 48, 549], [59, 565, 48, 550], [60, 6, 49, 4], [61, 6, 50, 4], [61, 10, 50, 10, "handler"], [61, 17, 50, 17], [61, 20, 50, 20, "Symbol"], [61, 26, 50, 26], [61, 27, 50, 27], [61, 28, 50, 28], [62, 6, 51, 4], [62, 10, 51, 8, "enabled"], [62, 17, 51, 15], [62, 22, 51, 20], [62, 27, 51, 25], [62, 29, 51, 27], [63, 8, 52, 6, "linkingHandlers"], [63, 23, 52, 21], [63, 24, 52, 22, "push"], [63, 28, 52, 26], [63, 29, 52, 27, "handler"], [63, 36, 52, 34], [63, 37, 52, 35], [64, 6, 53, 4], [65, 6, 54, 4], [65, 13, 54, 11], [65, 19, 54, 17], [66, 8, 55, 6], [66, 12, 55, 12, "index"], [66, 17, 55, 17], [66, 20, 55, 20, "linkingHandlers"], [66, 35, 55, 35], [66, 36, 55, 36, "indexOf"], [66, 43, 55, 43], [66, 44, 55, 44, "handler"], [66, 51, 55, 51], [66, 52, 55, 52], [67, 8, 56, 6], [67, 12, 56, 10, "index"], [67, 17, 56, 15], [67, 20, 56, 18], [67, 21, 56, 19], [67, 22, 56, 20], [67, 24, 56, 22], [68, 10, 57, 8, "linkingHandlers"], [68, 25, 57, 23], [68, 26, 57, 24, "splice"], [68, 32, 57, 30], [68, 33, 57, 31, "index"], [68, 38, 57, 36], [68, 40, 57, 38], [68, 41, 57, 39], [68, 42, 57, 40], [69, 8, 58, 6], [70, 6, 59, 4], [70, 7, 59, 5], [71, 4, 60, 2], [71, 5, 60, 3], [71, 7, 60, 5], [71, 8, 60, 6, "enabled"], [71, 15, 60, 13], [71, 17, 60, 15, "independent"], [71, 28, 60, 26], [71, 29, 60, 27], [71, 30, 60, 28], [73, 4, 62, 2], [74, 4, 63, 2], [75, 4, 64, 2], [76, 4, 65, 2], [76, 8, 65, 8, "enabledRef"], [76, 18, 65, 18], [76, 21, 65, 21, "React"], [76, 26, 65, 26], [76, 27, 65, 27, "useRef"], [76, 33, 65, 33], [76, 34, 65, 34, "enabled"], [76, 41, 65, 41], [76, 42, 65, 42], [77, 4, 66, 2], [77, 8, 66, 8, "prefixesRef"], [77, 19, 66, 19], [77, 22, 66, 22, "React"], [77, 27, 66, 27], [77, 28, 66, 28, "useRef"], [77, 34, 66, 34], [77, 35, 66, 35, "prefixes"], [77, 43, 66, 43], [77, 44, 66, 44], [78, 4, 67, 2], [78, 8, 67, 8, "filterRef"], [78, 17, 67, 17], [78, 20, 67, 20, "React"], [78, 25, 67, 25], [78, 26, 67, 26, "useRef"], [78, 32, 67, 32], [78, 33, 67, 33, "filter"], [78, 39, 67, 39], [78, 40, 67, 40], [79, 4, 68, 2], [79, 8, 68, 8, "configRef"], [79, 17, 68, 17], [79, 20, 68, 20, "React"], [79, 25, 68, 25], [79, 26, 68, 26, "useRef"], [79, 32, 68, 32], [79, 33, 68, 33, "config"], [79, 39, 68, 39], [79, 40, 68, 40], [80, 4, 69, 2], [80, 8, 69, 8, "getInitialURLRef"], [80, 24, 69, 24], [80, 27, 69, 27, "React"], [80, 32, 69, 32], [80, 33, 69, 33, "useRef"], [80, 39, 69, 39], [80, 40, 69, 40, "getInitialURL"], [80, 53, 69, 53], [80, 54, 69, 54], [81, 4, 70, 2], [81, 8, 70, 8, "getStateFromPathRef"], [81, 27, 70, 27], [81, 30, 70, 30, "React"], [81, 35, 70, 35], [81, 36, 70, 36, "useRef"], [81, 42, 70, 42], [81, 43, 70, 43, "getStateFromPath"], [81, 59, 70, 59], [81, 60, 70, 60], [82, 4, 71, 2], [82, 8, 71, 8, "getActionFromStateRef"], [82, 29, 71, 29], [82, 32, 71, 32, "React"], [82, 37, 71, 37], [82, 38, 71, 38, "useRef"], [82, 44, 71, 44], [82, 45, 71, 45, "getActionFromState"], [82, 63, 71, 63], [82, 64, 71, 64], [83, 4, 72, 2, "React"], [83, 9, 72, 7], [83, 10, 72, 8, "useEffect"], [83, 19, 72, 17], [83, 20, 72, 18], [83, 26, 72, 24], [84, 6, 73, 4, "enabledRef"], [84, 16, 73, 14], [84, 17, 73, 15, "current"], [84, 24, 73, 22], [84, 27, 73, 25, "enabled"], [84, 34, 73, 32], [85, 6, 74, 4, "prefixesRef"], [85, 17, 74, 15], [85, 18, 74, 16, "current"], [85, 25, 74, 23], [85, 28, 74, 26, "prefixes"], [85, 36, 74, 34], [86, 6, 75, 4, "filterRef"], [86, 15, 75, 13], [86, 16, 75, 14, "current"], [86, 23, 75, 21], [86, 26, 75, 24, "filter"], [86, 32, 75, 30], [87, 6, 76, 4, "configRef"], [87, 15, 76, 13], [87, 16, 76, 14, "current"], [87, 23, 76, 21], [87, 26, 76, 24, "config"], [87, 32, 76, 30], [88, 6, 77, 4, "getInitialURLRef"], [88, 22, 77, 20], [88, 23, 77, 21, "current"], [88, 30, 77, 28], [88, 33, 77, 31, "getInitialURL"], [88, 46, 77, 44], [89, 6, 78, 4, "getStateFromPathRef"], [89, 25, 78, 23], [89, 26, 78, 24, "current"], [89, 33, 78, 31], [89, 36, 78, 34, "getStateFromPath"], [89, 52, 78, 50], [90, 6, 79, 4, "getActionFromStateRef"], [90, 27, 79, 25], [90, 28, 79, 26, "current"], [90, 35, 79, 33], [90, 38, 79, 36, "getActionFromState"], [90, 56, 79, 54], [91, 4, 80, 2], [91, 5, 80, 3], [91, 6, 80, 4], [92, 4, 81, 2], [92, 8, 81, 8, "getStateFromURL"], [92, 23, 81, 23], [92, 26, 81, 26, "React"], [92, 31, 81, 31], [92, 32, 81, 32, "useCallback"], [92, 43, 81, 43], [92, 44, 81, 44, "url"], [92, 47, 81, 47], [92, 51, 81, 51], [93, 6, 82, 4], [93, 10, 82, 8], [93, 11, 82, 9, "url"], [93, 14, 82, 12], [93, 18, 82, 16, "filterRef"], [93, 27, 82, 25], [93, 28, 82, 26, "current"], [93, 35, 82, 33], [93, 39, 82, 37], [93, 40, 82, 38, "filterRef"], [93, 49, 82, 47], [93, 50, 82, 48, "current"], [93, 57, 82, 55], [93, 58, 82, 56, "url"], [93, 61, 82, 59], [93, 62, 82, 60], [93, 64, 82, 62], [94, 8, 83, 6], [94, 15, 83, 13, "undefined"], [94, 24, 83, 22], [95, 6, 84, 4], [96, 6, 85, 4], [96, 10, 85, 10, "path"], [96, 14, 85, 14], [96, 17, 85, 17], [96, 21, 85, 17, "extractPathFromURL"], [96, 59, 85, 35], [96, 61, 85, 36, "prefixesRef"], [96, 72, 85, 47], [96, 73, 85, 48, "current"], [96, 80, 85, 55], [96, 82, 85, 57, "url"], [96, 85, 85, 60], [96, 86, 85, 61], [97, 6, 86, 4], [97, 13, 86, 11, "path"], [97, 17, 86, 15], [97, 22, 86, 20, "undefined"], [97, 31, 86, 29], [97, 34, 86, 32, "getStateFromPathRef"], [97, 53, 86, 51], [97, 54, 86, 52, "current"], [97, 61, 86, 59], [97, 62, 86, 60, "path"], [97, 66, 86, 64], [97, 68, 86, 66, "configRef"], [97, 77, 86, 75], [97, 78, 86, 76, "current"], [97, 85, 86, 83], [97, 86, 86, 84], [97, 89, 86, 87, "undefined"], [97, 98, 86, 96], [98, 4, 87, 2], [98, 5, 87, 3], [98, 7, 87, 5], [98, 9, 87, 7], [98, 10, 87, 8], [99, 4, 88, 2], [99, 8, 88, 8, "getInitialState"], [99, 23, 88, 23], [99, 26, 88, 26, "React"], [99, 31, 88, 31], [99, 32, 88, 32, "useCallback"], [99, 43, 88, 43], [99, 44, 88, 44], [99, 50, 88, 50], [100, 6, 89, 4], [100, 10, 89, 8, "state"], [100, 15, 89, 13], [101, 6, 90, 4], [101, 10, 90, 8, "enabledRef"], [101, 20, 90, 18], [101, 21, 90, 19, "current"], [101, 28, 90, 26], [101, 30, 90, 28], [102, 8, 91, 6], [102, 12, 91, 12, "url"], [102, 15, 91, 15], [102, 18, 91, 18, "getInitialURLRef"], [102, 34, 91, 34], [102, 35, 91, 35, "current"], [102, 42, 91, 42], [102, 43, 91, 43], [102, 44, 91, 44], [103, 8, 92, 6], [103, 12, 92, 10, "url"], [103, 15, 92, 13], [103, 19, 92, 17], [103, 23, 92, 21], [103, 25, 92, 23], [104, 10, 93, 8], [104, 14, 93, 12], [104, 21, 93, 19, "url"], [104, 24, 93, 22], [104, 29, 93, 27], [104, 37, 93, 35], [104, 39, 93, 37], [105, 12, 94, 10], [105, 19, 94, 17, "url"], [105, 22, 94, 20], [105, 23, 94, 21, "then"], [105, 27, 94, 25], [105, 28, 94, 26, "url"], [105, 31, 94, 29], [105, 35, 94, 33], [106, 14, 95, 12], [106, 18, 95, 18, "state"], [106, 23, 95, 23], [106, 26, 95, 26, "getStateFromURL"], [106, 41, 95, 41], [106, 42, 95, 42, "url"], [106, 45, 95, 45], [106, 46, 95, 46], [107, 14, 96, 12], [107, 18, 96, 16], [107, 25, 96, 23, "url"], [107, 28, 96, 26], [107, 33, 96, 31], [107, 41, 96, 39], [107, 43, 96, 41], [108, 16, 97, 14], [109, 16, 98, 14, "onUnhandledLinking"], [109, 34, 98, 32], [109, 35, 98, 33], [109, 39, 98, 33, "extractPathFromURL"], [109, 77, 98, 51], [109, 79, 98, 52, "prefixes"], [109, 87, 98, 60], [109, 89, 98, 62, "url"], [109, 92, 98, 65], [109, 93, 98, 66], [109, 94, 98, 67], [110, 14, 99, 12], [111, 14, 100, 12], [111, 21, 100, 19, "state"], [111, 26, 100, 24], [112, 12, 101, 10], [112, 13, 101, 11], [112, 14, 101, 12], [113, 10, 102, 8], [113, 11, 102, 9], [113, 17, 102, 15], [114, 12, 103, 10, "onUnhandledLinking"], [114, 30, 103, 28], [114, 31, 103, 29], [114, 35, 103, 29, "extractPathFromURL"], [114, 73, 103, 47], [114, 75, 103, 48, "prefixes"], [114, 83, 103, 56], [114, 85, 103, 58, "url"], [114, 88, 103, 61], [114, 89, 103, 62], [114, 90, 103, 63], [115, 10, 104, 8], [116, 8, 105, 6], [117, 8, 106, 6, "state"], [117, 13, 106, 11], [117, 16, 106, 14, "getStateFromURL"], [117, 31, 106, 29], [117, 32, 106, 30, "url"], [117, 35, 106, 33], [117, 36, 106, 34], [118, 6, 107, 4], [119, 6, 108, 4], [119, 10, 108, 10, "thenable"], [119, 18, 108, 18], [119, 21, 108, 21], [120, 8, 109, 6, "then"], [120, 12, 109, 10, "then"], [120, 13, 109, 11, "onfulfilled"], [120, 24, 109, 22], [120, 26, 109, 24], [121, 10, 110, 8], [121, 17, 110, 15, "Promise"], [121, 24, 110, 22], [121, 25, 110, 23, "resolve"], [121, 32, 110, 30], [121, 33, 110, 31, "onfulfilled"], [121, 44, 110, 42], [121, 47, 110, 45, "onfulfilled"], [121, 58, 110, 56], [121, 59, 110, 57, "state"], [121, 64, 110, 62], [121, 65, 110, 63], [121, 68, 110, 66, "state"], [121, 73, 110, 71], [121, 74, 110, 72], [122, 8, 111, 6], [122, 9, 111, 7], [123, 8, 112, 6, "catch"], [123, 13, 112, 11, "catch"], [123, 14, 112, 11], [123, 16, 112, 14], [124, 10, 113, 8], [124, 17, 113, 15, "thenable"], [124, 25, 113, 23], [125, 8, 114, 6], [126, 6, 115, 4], [126, 7, 115, 5], [127, 6, 116, 4], [127, 13, 116, 11, "thenable"], [127, 21, 116, 19], [128, 4, 117, 2], [128, 5, 117, 3], [128, 7, 117, 5], [128, 8, 117, 6, "getStateFromURL"], [128, 23, 117, 21], [128, 25, 117, 23, "onUnhandledLinking"], [128, 43, 117, 41], [128, 45, 117, 43, "prefixes"], [128, 53, 117, 51], [128, 54, 117, 52], [128, 55, 117, 53], [129, 4, 118, 2, "React"], [129, 9, 118, 7], [129, 10, 118, 8, "useEffect"], [129, 19, 118, 17], [129, 20, 118, 18], [129, 26, 118, 24], [130, 6, 119, 4], [130, 10, 119, 10, "listener"], [130, 18, 119, 18], [130, 21, 119, 21, "url"], [130, 24, 119, 24], [130, 28, 119, 28], [131, 8, 120, 6], [131, 12, 120, 10], [131, 13, 120, 11, "enabled"], [131, 20, 120, 18], [131, 22, 120, 20], [132, 10, 121, 8], [133, 8, 122, 6], [134, 8, 123, 6], [134, 12, 123, 12, "navigation"], [134, 22, 123, 22], [134, 25, 123, 25, "ref"], [134, 28, 123, 28], [134, 29, 123, 29, "current"], [134, 36, 123, 36], [135, 8, 124, 6], [135, 12, 124, 12, "state"], [135, 17, 124, 17], [135, 20, 124, 20, "navigation"], [135, 30, 124, 30], [135, 33, 124, 33, "getStateFromURL"], [135, 48, 124, 48], [135, 49, 124, 49, "url"], [135, 52, 124, 52], [135, 53, 124, 53], [135, 56, 124, 56, "undefined"], [135, 65, 124, 65], [136, 8, 125, 6], [136, 12, 125, 10, "navigation"], [136, 22, 125, 20], [136, 26, 125, 24, "state"], [136, 31, 125, 29], [136, 33, 125, 31], [137, 10, 126, 8], [138, 10, 127, 8, "onUnhandledLinking"], [138, 28, 127, 26], [138, 29, 127, 27], [138, 33, 127, 27, "extractPathFromURL"], [138, 71, 127, 45], [138, 73, 127, 46, "prefixes"], [138, 81, 127, 54], [138, 83, 127, 56, "url"], [138, 86, 127, 59], [138, 87, 127, 60], [138, 88, 127, 61], [139, 10, 128, 8], [139, 14, 128, 14, "rootState"], [139, 23, 128, 23], [139, 26, 128, 26, "navigation"], [139, 36, 128, 36], [139, 37, 128, 37, "getRootState"], [139, 49, 128, 49], [139, 50, 128, 50], [139, 51, 128, 51], [140, 10, 129, 8], [140, 14, 129, 12, "state"], [140, 19, 129, 17], [140, 20, 129, 18, "routes"], [140, 26, 129, 24], [140, 27, 129, 25, "some"], [140, 31, 129, 29], [140, 32, 129, 30, "r"], [140, 33, 129, 31], [140, 37, 129, 35], [140, 38, 129, 36, "rootState"], [140, 47, 129, 45], [140, 49, 129, 47, "routeNames"], [140, 59, 129, 57], [140, 60, 129, 58, "includes"], [140, 68, 129, 66], [140, 69, 129, 67, "r"], [140, 70, 129, 68], [140, 71, 129, 69, "name"], [140, 75, 129, 73], [140, 76, 129, 74], [140, 77, 129, 75], [140, 79, 129, 77], [141, 12, 130, 10], [142, 10, 131, 8], [143, 10, 132, 8], [143, 14, 132, 14, "action"], [143, 20, 132, 20], [143, 23, 132, 23, "getActionFromStateRef"], [143, 44, 132, 44], [143, 45, 132, 45, "current"], [143, 52, 132, 52], [143, 53, 132, 53, "state"], [143, 58, 132, 58], [143, 60, 132, 60, "configRef"], [143, 69, 132, 69], [143, 70, 132, 70, "current"], [143, 77, 132, 77], [143, 78, 132, 78], [144, 10, 133, 8], [144, 14, 133, 12, "action"], [144, 20, 133, 18], [144, 25, 133, 23, "undefined"], [144, 34, 133, 32], [144, 36, 133, 34], [145, 12, 134, 10], [145, 16, 134, 14], [146, 14, 135, 12, "navigation"], [146, 24, 135, 22], [146, 25, 135, 23, "dispatch"], [146, 33, 135, 31], [146, 34, 135, 32, "action"], [146, 40, 135, 38], [146, 41, 135, 39], [147, 12, 136, 10], [147, 13, 136, 11], [147, 14, 136, 12], [147, 21, 136, 19, "e"], [147, 22, 136, 20], [147, 24, 136, 22], [148, 14, 137, 12], [149, 14, 138, 12], [150, 14, 139, 12, "console"], [150, 21, 139, 19], [150, 22, 139, 20, "warn"], [150, 26, 139, 24], [150, 27, 139, 25], [150, 80, 139, 78, "url"], [150, 83, 139, 81], [150, 89, 139, 87], [150, 96, 139, 94, "e"], [150, 97, 139, 95], [150, 102, 139, 100], [150, 110, 139, 108], [150, 114, 139, 112, "e"], [150, 115, 139, 113], [150, 119, 139, 117], [150, 123, 139, 121], [150, 127, 139, 125], [150, 136, 139, 134], [150, 140, 139, 138, "e"], [150, 141, 139, 139], [150, 144, 139, 142, "e"], [150, 145, 139, 143], [150, 146, 139, 144, "message"], [150, 153, 139, 151], [150, 156, 139, 154, "e"], [150, 157, 139, 155], [150, 159, 139, 157], [150, 160, 139, 158], [151, 12, 140, 10], [152, 10, 141, 8], [152, 11, 141, 9], [152, 17, 141, 15], [153, 12, 142, 10, "navigation"], [153, 22, 142, 20], [153, 23, 142, 21, "resetRoot"], [153, 32, 142, 30], [153, 33, 142, 31, "state"], [153, 38, 142, 36], [153, 39, 142, 37], [154, 10, 143, 8], [155, 8, 144, 6], [156, 6, 145, 4], [156, 7, 145, 5], [157, 6, 146, 4], [157, 13, 146, 11, "subscribe"], [157, 22, 146, 20], [157, 23, 146, 21, "listener"], [157, 31, 146, 29], [157, 32, 146, 30], [158, 4, 147, 2], [158, 5, 147, 3], [158, 7, 147, 5], [158, 8, 147, 6, "enabled"], [158, 15, 147, 13], [158, 17, 147, 15, "getStateFromURL"], [158, 32, 147, 30], [158, 34, 147, 32, "onUnhandledLinking"], [158, 52, 147, 50], [158, 54, 147, 52, "prefixes"], [158, 62, 147, 60], [158, 64, 147, 62, "ref"], [158, 67, 147, 65], [158, 69, 147, 67, "subscribe"], [158, 78, 147, 76], [158, 79, 147, 77], [158, 80, 147, 78], [159, 4, 148, 2], [159, 11, 148, 9], [160, 6, 149, 4, "getInitialState"], [161, 4, 150, 2], [161, 5, 150, 3], [162, 2, 151, 0], [163, 0, 151, 1], [163, 3]], "functionMap": {"names": ["<global>", "useLinking", "<anonymous>", "Promise$argument_0", "callback", "React.useEffect$argument_0", "getStateFromURL", "getInitialState", "url.then$argument_0", "thenable.then", "thenable._catch", "listener", "state.routes.some$argument_0"], "mappings": "AAA;OCO;kBCK,yDC;GDI,GD;cCC;qBEC;uBFE;GDc;kBIK;WHc;KGK;GJC;kBIY;GJQ;4CKC;GLM;4CMC;0BCM;WDO;MEQ;OFE;MGC;OHE;GNG;kBIC;qBMC;8BCU,4CD;KNgB;GJE;CDI"}}, "type": "js/module"}]}