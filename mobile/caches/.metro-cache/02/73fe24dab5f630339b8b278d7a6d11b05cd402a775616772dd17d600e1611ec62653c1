{"dependencies": [{"name": "expo/dom/global", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 17, "index": 296}, "end": {"line": 7, "column": 43, "index": 322}}], "key": "5bO3ucGHWMBCsJYlm6TwO/aW1HE=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 32, "index": 356}, "end": {"line": 8, "column": 48, "index": 372}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./events", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 17, "index": 392}, "end": {"line": 9, "column": 36, "index": 411}}], "key": "RQEAXnrIpl4a6RHsE1TF/7TG44E=", "exportNames": ["*"]}}, {"name": "../global-state/routing", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 18, "index": 431}, "end": {"line": 10, "column": 52, "index": 465}}], "key": "Cqbl9MOLPE8L7igowwxv8ngZpbk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDomComponentNavigation = useDomComponentNavigation;\n  const global_1 = require(_dependencyMap[0], \"expo/dom/global\");\n  const react_1 = __importDefault(require(_dependencyMap[1], \"react\"));\n  const events_1 = require(_dependencyMap[2], \"./events\");\n  const routing_1 = require(_dependencyMap[3], \"../global-state/routing\");\n  function useDomComponentNavigation() {\n    react_1.default.useEffect(() => {\n      if (true) {\n        return () => {};\n      }\n      return (0, global_1.addGlobalDomEventListener)(({\n        type,\n        data\n      }) => {\n        switch (type) {\n          case events_1.ROUTER_LINK_TYPE:\n            (0, routing_1.linkTo)(data.href, data.options);\n            break;\n          case events_1.ROUTER_DISMISS_ALL_TYPE:\n            (0, routing_1.dismissAll)();\n            break;\n          case events_1.ROUTER_DISMISS_TYPE:\n            (0, routing_1.dismiss)(data.count);\n            break;\n          case events_1.ROUTER_BACK_TYPE:\n            (0, routing_1.goBack)();\n            break;\n          case events_1.ROUTER_SET_PARAMS_TYPE:\n            (0, routing_1.setParams)(data.params);\n            break;\n        }\n      });\n    }, []);\n  }\n});", "lineCount": 46, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__importDefault"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__importDefault"], [4, 52, 2, 51], [4, 56, 2, 56], [4, 66, 2, 66, "mod"], [4, 69, 2, 69], [4, 71, 2, 71], [5, 4, 3, 4], [5, 11, 3, 12, "mod"], [5, 14, 3, 15], [5, 18, 3, 19, "mod"], [5, 21, 3, 22], [5, 22, 3, 23, "__esModule"], [5, 32, 3, 33], [5, 35, 3, 37, "mod"], [5, 38, 3, 40], [5, 41, 3, 43], [6, 6, 3, 45], [6, 15, 3, 54], [6, 17, 3, 56, "mod"], [7, 4, 3, 60], [7, 5, 3, 61], [8, 2, 4, 0], [8, 3, 4, 1], [9, 2, 5, 0, "Object"], [9, 8, 5, 6], [9, 9, 5, 7, "defineProperty"], [9, 23, 5, 21], [9, 24, 5, 22, "exports"], [9, 31, 5, 29], [9, 33, 5, 31], [9, 45, 5, 43], [9, 47, 5, 45], [10, 4, 5, 47, "value"], [10, 9, 5, 52], [10, 11, 5, 54], [11, 2, 5, 59], [11, 3, 5, 60], [11, 4, 5, 61], [12, 2, 6, 0, "exports"], [12, 9, 6, 7], [12, 10, 6, 8, "useDomComponentNavigation"], [12, 35, 6, 33], [12, 38, 6, 36, "useDomComponentNavigation"], [12, 63, 6, 61], [13, 2, 7, 0], [13, 8, 7, 6, "global_1"], [13, 16, 7, 14], [13, 19, 7, 17, "require"], [13, 26, 7, 24], [13, 27, 7, 24, "_dependencyMap"], [13, 41, 7, 24], [13, 63, 7, 42], [13, 64, 7, 43], [14, 2, 8, 0], [14, 8, 8, 6, "react_1"], [14, 15, 8, 13], [14, 18, 8, 16, "__importDefault"], [14, 33, 8, 31], [14, 34, 8, 32, "require"], [14, 41, 8, 39], [14, 42, 8, 39, "_dependencyMap"], [14, 56, 8, 39], [14, 68, 8, 47], [14, 69, 8, 48], [14, 70, 8, 49], [15, 2, 9, 0], [15, 8, 9, 6, "events_1"], [15, 16, 9, 14], [15, 19, 9, 17, "require"], [15, 26, 9, 24], [15, 27, 9, 24, "_dependencyMap"], [15, 41, 9, 24], [15, 56, 9, 35], [15, 57, 9, 36], [16, 2, 10, 0], [16, 8, 10, 6, "routing_1"], [16, 17, 10, 15], [16, 20, 10, 18, "require"], [16, 27, 10, 25], [16, 28, 10, 25, "_dependencyMap"], [16, 42, 10, 25], [16, 72, 10, 51], [16, 73, 10, 52], [17, 2, 11, 0], [17, 11, 11, 9, "useDomComponentNavigation"], [17, 36, 11, 34, "useDomComponentNavigation"], [17, 37, 11, 34], [17, 39, 11, 37], [18, 4, 12, 4, "react_1"], [18, 11, 12, 11], [18, 12, 12, 12, "default"], [18, 19, 12, 19], [18, 20, 12, 20, "useEffect"], [18, 29, 12, 29], [18, 30, 12, 30], [18, 36, 12, 36], [19, 6, 13, 8], [19, 16, 13, 43], [20, 8, 14, 12], [20, 15, 14, 19], [20, 21, 14, 25], [20, 22, 14, 27], [20, 23, 14, 28], [21, 6, 15, 8], [22, 6, 16, 8], [22, 13, 16, 15], [22, 14, 16, 16], [22, 15, 16, 17], [22, 17, 16, 19, "global_1"], [22, 25, 16, 27], [22, 26, 16, 28, "addGlobalDomEventListener"], [22, 51, 16, 53], [22, 53, 16, 55], [22, 54, 16, 56], [23, 8, 16, 58, "type"], [23, 12, 16, 62], [24, 8, 16, 64, "data"], [25, 6, 16, 69], [25, 7, 16, 70], [25, 12, 16, 75], [26, 8, 17, 12], [26, 16, 17, 20, "type"], [26, 20, 17, 24], [27, 10, 18, 16], [27, 15, 18, 21, "events_1"], [27, 23, 18, 29], [27, 24, 18, 30, "ROUTER_LINK_TYPE"], [27, 40, 18, 46], [28, 12, 19, 20], [28, 13, 19, 21], [28, 14, 19, 22], [28, 16, 19, 24, "routing_1"], [28, 25, 19, 33], [28, 26, 19, 34, "linkTo"], [28, 32, 19, 40], [28, 34, 19, 42, "data"], [28, 38, 19, 46], [28, 39, 19, 47, "href"], [28, 43, 19, 51], [28, 45, 19, 53, "data"], [28, 49, 19, 57], [28, 50, 19, 58, "options"], [28, 57, 19, 65], [28, 58, 19, 66], [29, 12, 20, 20], [30, 10, 21, 16], [30, 15, 21, 21, "events_1"], [30, 23, 21, 29], [30, 24, 21, 30, "ROUTER_DISMISS_ALL_TYPE"], [30, 47, 21, 53], [31, 12, 22, 20], [31, 13, 22, 21], [31, 14, 22, 22], [31, 16, 22, 24, "routing_1"], [31, 25, 22, 33], [31, 26, 22, 34, "dismissAll"], [31, 36, 22, 44], [31, 38, 22, 46], [31, 39, 22, 47], [32, 12, 23, 20], [33, 10, 24, 16], [33, 15, 24, 21, "events_1"], [33, 23, 24, 29], [33, 24, 24, 30, "ROUTER_DISMISS_TYPE"], [33, 43, 24, 49], [34, 12, 25, 20], [34, 13, 25, 21], [34, 14, 25, 22], [34, 16, 25, 24, "routing_1"], [34, 25, 25, 33], [34, 26, 25, 34, "dismiss"], [34, 33, 25, 41], [34, 35, 25, 43, "data"], [34, 39, 25, 47], [34, 40, 25, 48, "count"], [34, 45, 25, 53], [34, 46, 25, 54], [35, 12, 26, 20], [36, 10, 27, 16], [36, 15, 27, 21, "events_1"], [36, 23, 27, 29], [36, 24, 27, 30, "ROUTER_BACK_TYPE"], [36, 40, 27, 46], [37, 12, 28, 20], [37, 13, 28, 21], [37, 14, 28, 22], [37, 16, 28, 24, "routing_1"], [37, 25, 28, 33], [37, 26, 28, 34, "goBack"], [37, 32, 28, 40], [37, 34, 28, 42], [37, 35, 28, 43], [38, 12, 29, 20], [39, 10, 30, 16], [39, 15, 30, 21, "events_1"], [39, 23, 30, 29], [39, 24, 30, 30, "ROUTER_SET_PARAMS_TYPE"], [39, 46, 30, 52], [40, 12, 31, 20], [40, 13, 31, 21], [40, 14, 31, 22], [40, 16, 31, 24, "routing_1"], [40, 25, 31, 33], [40, 26, 31, 34, "setParams"], [40, 35, 31, 43], [40, 37, 31, 45, "data"], [40, 41, 31, 49], [40, 42, 31, 50, "params"], [40, 48, 31, 56], [40, 49, 31, 57], [41, 12, 32, 20], [42, 8, 33, 12], [43, 6, 34, 8], [43, 7, 34, 9], [43, 8, 34, 10], [44, 4, 35, 4], [44, 5, 35, 5], [44, 7, 35, 7], [44, 9, 35, 9], [44, 10, 35, 10], [45, 2, 36, 0], [46, 0, 36, 1], [46, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "useDomComponentNavigation", "react_1._default.useEffect$argument_0"], "mappings": "AAA;wDCC;CDE;AEO;8BCC;mBFE,SE;uDFE;SEkB;KDC;CFC"}}, "type": "js/module"}]}