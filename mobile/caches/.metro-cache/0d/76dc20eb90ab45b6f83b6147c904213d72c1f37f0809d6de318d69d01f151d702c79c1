{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useBackButton = useBackButton;\n  function useBackButton(_) {\n    // No-op\n    // BackHandler is not available on web\n  }\n});", "lineCount": 12, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useBackButton"], [7, 23, 1, 13], [7, 26, 1, 13, "useBackButton"], [7, 39, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "useBackButton"], [8, 24, 3, 29, "useBackButton"], [8, 25, 3, 30, "_"], [8, 26, 3, 31], [8, 28, 3, 33], [9, 4, 4, 2], [10, 4, 5, 2], [11, 2, 5, 2], [12, 0, 6, 1], [12, 3]], "functionMap": {"names": ["<global>", "useBackButton"], "mappings": "AAA;OCE;CDG"}}, "type": "js/module"}]}