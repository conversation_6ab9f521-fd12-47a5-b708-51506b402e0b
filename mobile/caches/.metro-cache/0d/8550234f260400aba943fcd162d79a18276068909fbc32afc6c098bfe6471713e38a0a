{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 50, "index": 65}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 66}, "end": {"line": 4, "column": 31, "index": 97}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "./useLinkProps.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 145}, "end": {"line": 6, "column": 49, "index": 194}}], "key": "pbNsupLgLtK+PYpgSgLKE1J9mSA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Link = Link;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-css-interop\"));\n  var _core = require(_dependencyMap[2], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Platform\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/Text\"));\n  var _useLinkProps = require(_dependencyMap[6], \"./useLinkProps.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Component to render link to another screen using a path.\n   * Uses an anchor tag on the web.\n   *\n   * @param props.screen Name of the screen to navigate to (e.g. `'Feeds'`).\n   * @param props.params Params to pass to the screen to navigate to (e.g. `{ sort: 'hot' }`).\n   * @param props.href Optional absolute path to use for the href (e.g. `/feeds/hot`).\n   * @param props.action Optional action to use for in-page navigation. By default, the path is parsed to an action based on linking config.\n   * @param props.children Child elements to render the content.\n   */\n  function Link({\n    screen,\n    params,\n    action,\n    href,\n    style,\n    ...rest\n  }) {\n    const {\n      colors,\n      fonts\n    } = (0, _core.useTheme)();\n    // @ts-expect-error: This is already type-checked by the prop types\n    const props = (0, _useLinkProps.useLinkProps)({\n      screen,\n      params,\n      action,\n      href\n    });\n    const onPress = e => {\n      if ('onPress' in rest) {\n        rest.onPress?.(e);\n      }\n\n      // Let user prevent default behavior\n      if (!e.defaultPrevented) {\n        props.onPress(e);\n      }\n    };\n    return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_Text.default, {\n      ...props,\n      ...rest,\n      ..._Platform.default.select({\n        web: {\n          onClick: onPress\n        },\n        default: {\n          onPress\n        }\n      }),\n      style: [{\n        color: colors.primary\n      }, fonts.regular, style]\n    });\n  }\n});", "lineCount": 71, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Link"], [8, 14, 1, 13], [8, 17, 1, 13, "Link"], [8, 21, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_ReactNativeCSSInterop"], [9, 28, 1, 13], [9, 31, 1, 13, "_interopRequireWildcard"], [9, 54, 1, 13], [9, 55, 1, 13, "require"], [9, 62, 1, 13], [9, 63, 1, 13, "_dependencyMap"], [9, 77, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_core"], [10, 11, 3, 0], [10, 14, 3, 0, "require"], [10, 21, 3, 0], [10, 22, 3, 0, "_dependencyMap"], [10, 36, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "React"], [11, 11, 4, 0], [11, 14, 4, 0, "_interopRequireWildcard"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 4, 31], [12, 6, 4, 31, "_Platform"], [12, 15, 4, 31], [12, 18, 4, 31, "_interopRequireDefault"], [12, 40, 4, 31], [12, 41, 4, 31, "require"], [12, 48, 4, 31], [12, 49, 4, 31, "_dependencyMap"], [12, 63, 4, 31], [13, 2, 4, 31], [13, 6, 4, 31, "_Text"], [13, 11, 4, 31], [13, 14, 4, 31, "_interopRequireDefault"], [13, 36, 4, 31], [13, 37, 4, 31, "require"], [13, 44, 4, 31], [13, 45, 4, 31, "_dependencyMap"], [13, 59, 4, 31], [14, 2, 6, 0], [14, 6, 6, 0, "_useLinkProps"], [14, 19, 6, 0], [14, 22, 6, 0, "require"], [14, 29, 6, 0], [14, 30, 6, 0, "_dependencyMap"], [14, 44, 6, 0], [15, 2, 6, 49], [15, 11, 6, 49, "_interopRequireWildcard"], [15, 35, 6, 49, "e"], [15, 36, 6, 49], [15, 38, 6, 49, "t"], [15, 39, 6, 49], [15, 68, 6, 49, "WeakMap"], [15, 75, 6, 49], [15, 81, 6, 49, "r"], [15, 82, 6, 49], [15, 89, 6, 49, "WeakMap"], [15, 96, 6, 49], [15, 100, 6, 49, "n"], [15, 101, 6, 49], [15, 108, 6, 49, "WeakMap"], [15, 115, 6, 49], [15, 127, 6, 49, "_interopRequireWildcard"], [15, 150, 6, 49], [15, 162, 6, 49, "_interopRequireWildcard"], [15, 163, 6, 49, "e"], [15, 164, 6, 49], [15, 166, 6, 49, "t"], [15, 167, 6, 49], [15, 176, 6, 49, "t"], [15, 177, 6, 49], [15, 181, 6, 49, "e"], [15, 182, 6, 49], [15, 186, 6, 49, "e"], [15, 187, 6, 49], [15, 188, 6, 49, "__esModule"], [15, 198, 6, 49], [15, 207, 6, 49, "e"], [15, 208, 6, 49], [15, 214, 6, 49, "o"], [15, 215, 6, 49], [15, 217, 6, 49, "i"], [15, 218, 6, 49], [15, 220, 6, 49, "f"], [15, 221, 6, 49], [15, 226, 6, 49, "__proto__"], [15, 235, 6, 49], [15, 243, 6, 49, "default"], [15, 250, 6, 49], [15, 252, 6, 49, "e"], [15, 253, 6, 49], [15, 270, 6, 49, "e"], [15, 271, 6, 49], [15, 294, 6, 49, "e"], [15, 295, 6, 49], [15, 320, 6, 49, "e"], [15, 321, 6, 49], [15, 330, 6, 49, "f"], [15, 331, 6, 49], [15, 337, 6, 49, "o"], [15, 338, 6, 49], [15, 341, 6, 49, "t"], [15, 342, 6, 49], [15, 345, 6, 49, "n"], [15, 346, 6, 49], [15, 349, 6, 49, "r"], [15, 350, 6, 49], [15, 358, 6, 49, "o"], [15, 359, 6, 49], [15, 360, 6, 49, "has"], [15, 363, 6, 49], [15, 364, 6, 49, "e"], [15, 365, 6, 49], [15, 375, 6, 49, "o"], [15, 376, 6, 49], [15, 377, 6, 49, "get"], [15, 380, 6, 49], [15, 381, 6, 49, "e"], [15, 382, 6, 49], [15, 385, 6, 49, "o"], [15, 386, 6, 49], [15, 387, 6, 49, "set"], [15, 390, 6, 49], [15, 391, 6, 49, "e"], [15, 392, 6, 49], [15, 394, 6, 49, "f"], [15, 395, 6, 49], [15, 411, 6, 49, "t"], [15, 412, 6, 49], [15, 416, 6, 49, "e"], [15, 417, 6, 49], [15, 433, 6, 49, "t"], [15, 434, 6, 49], [15, 441, 6, 49, "hasOwnProperty"], [15, 455, 6, 49], [15, 456, 6, 49, "call"], [15, 460, 6, 49], [15, 461, 6, 49, "e"], [15, 462, 6, 49], [15, 464, 6, 49, "t"], [15, 465, 6, 49], [15, 472, 6, 49, "i"], [15, 473, 6, 49], [15, 477, 6, 49, "o"], [15, 478, 6, 49], [15, 481, 6, 49, "Object"], [15, 487, 6, 49], [15, 488, 6, 49, "defineProperty"], [15, 502, 6, 49], [15, 507, 6, 49, "Object"], [15, 513, 6, 49], [15, 514, 6, 49, "getOwnPropertyDescriptor"], [15, 538, 6, 49], [15, 539, 6, 49, "e"], [15, 540, 6, 49], [15, 542, 6, 49, "t"], [15, 543, 6, 49], [15, 550, 6, 49, "i"], [15, 551, 6, 49], [15, 552, 6, 49, "get"], [15, 555, 6, 49], [15, 559, 6, 49, "i"], [15, 560, 6, 49], [15, 561, 6, 49, "set"], [15, 564, 6, 49], [15, 568, 6, 49, "o"], [15, 569, 6, 49], [15, 570, 6, 49, "f"], [15, 571, 6, 49], [15, 573, 6, 49, "t"], [15, 574, 6, 49], [15, 576, 6, 49, "i"], [15, 577, 6, 49], [15, 581, 6, 49, "f"], [15, 582, 6, 49], [15, 583, 6, 49, "t"], [15, 584, 6, 49], [15, 588, 6, 49, "e"], [15, 589, 6, 49], [15, 590, 6, 49, "t"], [15, 591, 6, 49], [15, 602, 6, 49, "f"], [15, 603, 6, 49], [15, 608, 6, 49, "e"], [15, 609, 6, 49], [15, 611, 6, 49, "t"], [15, 612, 6, 49], [16, 2, 7, 0], [17, 0, 8, 0], [18, 0, 9, 0], [19, 0, 10, 0], [20, 0, 11, 0], [21, 0, 12, 0], [22, 0, 13, 0], [23, 0, 14, 0], [24, 0, 15, 0], [25, 0, 16, 0], [26, 2, 17, 7], [26, 11, 17, 16, "Link"], [26, 15, 17, 20, "Link"], [26, 16, 17, 21], [27, 4, 18, 2, "screen"], [27, 10, 18, 8], [28, 4, 19, 2, "params"], [28, 10, 19, 8], [29, 4, 20, 2, "action"], [29, 10, 20, 8], [30, 4, 21, 2, "href"], [30, 8, 21, 6], [31, 4, 22, 2, "style"], [31, 9, 22, 7], [32, 4, 23, 2], [32, 7, 23, 5, "rest"], [33, 2, 24, 0], [33, 3, 24, 1], [33, 5, 24, 3], [34, 4, 25, 2], [34, 10, 25, 8], [35, 6, 26, 4, "colors"], [35, 12, 26, 10], [36, 6, 27, 4, "fonts"], [37, 4, 28, 2], [37, 5, 28, 3], [37, 8, 28, 6], [37, 12, 28, 6, "useTheme"], [37, 26, 28, 14], [37, 28, 28, 15], [37, 29, 28, 16], [38, 4, 29, 2], [39, 4, 30, 2], [39, 10, 30, 8, "props"], [39, 15, 30, 13], [39, 18, 30, 16], [39, 22, 30, 16, "useLinkProps"], [39, 48, 30, 28], [39, 50, 30, 29], [40, 6, 31, 4, "screen"], [40, 12, 31, 10], [41, 6, 32, 4, "params"], [41, 12, 32, 10], [42, 6, 33, 4, "action"], [42, 12, 33, 10], [43, 6, 34, 4, "href"], [44, 4, 35, 2], [44, 5, 35, 3], [44, 6, 35, 4], [45, 4, 36, 2], [45, 10, 36, 8, "onPress"], [45, 17, 36, 15], [45, 20, 36, 18, "e"], [45, 21, 36, 19], [45, 25, 36, 23], [46, 6, 37, 4], [46, 10, 37, 8], [46, 19, 37, 17], [46, 23, 37, 21, "rest"], [46, 27, 37, 25], [46, 29, 37, 27], [47, 8, 38, 6, "rest"], [47, 12, 38, 10], [47, 13, 38, 11, "onPress"], [47, 20, 38, 18], [47, 23, 38, 21, "e"], [47, 24, 38, 22], [47, 25, 38, 23], [48, 6, 39, 4], [50, 6, 41, 4], [51, 6, 42, 4], [51, 10, 42, 8], [51, 11, 42, 9, "e"], [51, 12, 42, 10], [51, 13, 42, 11, "defaultPrevented"], [51, 29, 42, 27], [51, 31, 42, 29], [52, 8, 43, 6, "props"], [52, 13, 43, 11], [52, 14, 43, 12, "onPress"], [52, 21, 43, 19], [52, 22, 43, 20, "e"], [52, 23, 43, 21], [52, 24, 43, 22], [53, 6, 44, 4], [54, 4, 45, 2], [54, 5, 45, 3], [55, 4, 46, 2], [55, 11, 46, 9], [55, 24, 46, 22, "_ReactNativeCSSInterop"], [55, 46, 46, 22], [55, 47, 46, 22, "createInteropElement"], [55, 67, 46, 22], [55, 68, 46, 42, "Text"], [55, 81, 46, 46], [55, 83, 46, 48], [56, 6, 47, 4], [56, 9, 47, 7, "props"], [56, 14, 47, 12], [57, 6, 48, 4], [57, 9, 48, 7, "rest"], [57, 13, 48, 11], [58, 6, 49, 4], [58, 9, 49, 7, "Platform"], [58, 26, 49, 15], [58, 27, 49, 16, "select"], [58, 33, 49, 22], [58, 34, 49, 23], [59, 8, 50, 6, "web"], [59, 11, 50, 9], [59, 13, 50, 11], [60, 10, 51, 8, "onClick"], [60, 17, 51, 15], [60, 19, 51, 17, "onPress"], [61, 8, 52, 6], [61, 9, 52, 7], [62, 8, 53, 6, "default"], [62, 15, 53, 13], [62, 17, 53, 15], [63, 10, 54, 8, "onPress"], [64, 8, 55, 6], [65, 6, 56, 4], [65, 7, 56, 5], [65, 8, 56, 6], [66, 6, 57, 4, "style"], [66, 11, 57, 9], [66, 13, 57, 11], [66, 14, 57, 12], [67, 8, 58, 6, "color"], [67, 13, 58, 11], [67, 15, 58, 13, "colors"], [67, 21, 58, 19], [67, 22, 58, 20, "primary"], [68, 6, 59, 4], [68, 7, 59, 5], [68, 9, 59, 7, "fonts"], [68, 14, 59, 12], [68, 15, 59, 13, "regular"], [68, 22, 59, 20], [68, 24, 59, 22, "style"], [68, 29, 59, 27], [69, 4, 60, 2], [69, 5, 60, 3], [69, 6, 60, 4], [70, 2, 61, 0], [71, 0, 61, 1], [71, 3]], "functionMap": {"names": ["<global>", "Link", "onPress"], "mappings": "AAA;OCgB;kBCmB;GDS;CDgB"}}, "type": "js/module"}]}