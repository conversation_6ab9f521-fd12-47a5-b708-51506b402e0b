{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createStringifySafeWithLimits = createStringifySafeWithLimits;\n  exports.default = void 0;\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  /**\n   * Tries to stringify with JSON.stringify and toString, but catches exceptions\n   * (e.g. from circular objects) and always returns a string and never throws.\n   */\n  function createStringifySafeWithLimits(limits) {\n    const {\n      maxDepth = Number.POSITIVE_INFINITY,\n      maxStringLimit = Number.POSITIVE_INFINITY,\n      maxArrayLimit = Number.POSITIVE_INFINITY,\n      maxObjectKeysLimit = Number.POSITIVE_INFINITY\n    } = limits;\n    const stack = [];\n    function replacer(_key, value) {\n      while (stack.length && this !== stack[0]) {\n        stack.shift();\n      }\n      if (typeof value === 'string') {\n        const truncatedString = '...(truncated)...';\n        if (value.length > maxStringLimit + truncatedString.length) {\n          return value.substring(0, maxStringLimit) + truncatedString;\n        }\n        return value;\n      }\n      if (typeof value !== 'object' || value === null) {\n        return value;\n      }\n      let retval = value;\n      if (Array.isArray(value)) {\n        if (stack.length >= maxDepth) {\n          retval = `[ ... array with ${value.length} values ... ]`;\n        } else if (value.length > maxArrayLimit) {\n          retval = value.slice(0, maxArrayLimit).concat([`... extra ${value.length - maxArrayLimit} values truncated ...`]);\n        }\n      } else {\n        // Add refinement after Array.isArray call.\n        if (typeof value !== 'object') {\n          throw new Error('This was already found earlier');\n        }\n        const keys = Object.keys(value);\n        if (stack.length >= maxDepth) {\n          retval = `{ ... object with ${keys.length} keys ... }`;\n        } else if (keys.length > maxObjectKeysLimit) {\n          // Return a sample of the keys.\n          retval = {};\n          for (const k of keys.slice(0, maxObjectKeysLimit)) {\n            retval[k] = value[k];\n          }\n          const truncatedKey = '...(truncated keys)...';\n          retval[truncatedKey] = keys.length - maxObjectKeysLimit;\n        }\n      }\n      stack.unshift(retval);\n      return retval;\n    }\n    return function stringifySafe(arg) {\n      if (arg === undefined) {\n        return 'undefined';\n      } else if (arg === null) {\n        return 'null';\n      } else if (typeof arg === 'function') {\n        try {\n          return arg.toString();\n        } catch {\n          return '[function unknown]';\n        }\n      } else if (arg instanceof Error) {\n        return arg.name + ': ' + arg.message;\n      } else {\n        // Perform a try catch, just in case the object has a circular\n        // reference or stringify throws for some other reason.\n        try {\n          const ret = JSON.stringify(arg, replacer);\n          if (ret === undefined) {\n            return '[\"' + typeof arg + '\" failed to stringify]';\n          }\n          return ret;\n        } catch {\n          if (typeof arg.toString === 'function') {\n            try {\n              // $FlowFixMe[incompatible-use] : toString shouldn't take any arguments in general.\n              return arg.toString();\n            } catch {}\n          }\n        }\n      }\n      return '[\"' + typeof arg + '\" failed to stringify]';\n    };\n  }\n  const stringifySafe = createStringifySafeWithLimits({\n    maxDepth: 10,\n    maxStringLimit: 100,\n    maxArrayLimit: 50,\n    maxObjectKeysLimit: 50\n  });\n  var _default = exports.default = stringifySafe;\n});", "lineCount": 110, "map": [[7, 2, 1, 0], [8, 0, 2, 0], [9, 0, 3, 0], [10, 0, 4, 0], [11, 0, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [15, 2, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 2, 13, 7], [19, 11, 13, 16, "createStringifySafeWithLimits"], [19, 40, 13, 45, "createStringifySafeWithLimits"], [19, 41, 13, 46, "limits"], [19, 47, 18, 1], [19, 49, 18, 25], [20, 4, 19, 2], [20, 10, 19, 8], [21, 6, 20, 4, "max<PERSON><PERSON><PERSON>"], [21, 14, 20, 12], [21, 17, 20, 15, "Number"], [21, 23, 20, 21], [21, 24, 20, 22, "POSITIVE_INFINITY"], [21, 41, 20, 39], [22, 6, 21, 4, "maxStringLimit"], [22, 20, 21, 18], [22, 23, 21, 21, "Number"], [22, 29, 21, 27], [22, 30, 21, 28, "POSITIVE_INFINITY"], [22, 47, 21, 45], [23, 6, 22, 4, "maxArrayLimit"], [23, 19, 22, 17], [23, 22, 22, 20, "Number"], [23, 28, 22, 26], [23, 29, 22, 27, "POSITIVE_INFINITY"], [23, 46, 22, 44], [24, 6, 23, 4, "maxObjectKeysLimit"], [24, 24, 23, 22], [24, 27, 23, 25, "Number"], [24, 33, 23, 31], [24, 34, 23, 32, "POSITIVE_INFINITY"], [25, 4, 24, 2], [25, 5, 24, 3], [25, 8, 24, 6, "limits"], [25, 14, 24, 12], [26, 4, 25, 2], [26, 10, 25, 8, "stack"], [26, 15, 25, 20], [26, 18, 25, 23], [26, 20, 25, 25], [27, 4, 26, 2], [27, 13, 26, 11, "replacer"], [27, 21, 26, 19, "replacer"], [27, 22, 26, 35, "_key"], [27, 26, 26, 47], [27, 28, 26, 49, "value"], [27, 33, 26, 59], [27, 35, 26, 66], [28, 6, 27, 4], [28, 13, 27, 11, "stack"], [28, 18, 27, 16], [28, 19, 27, 17, "length"], [28, 25, 27, 23], [28, 29, 27, 27], [28, 33, 27, 31], [28, 38, 27, 36, "stack"], [28, 43, 27, 41], [28, 44, 27, 42], [28, 45, 27, 43], [28, 46, 27, 44], [28, 48, 27, 46], [29, 8, 28, 6, "stack"], [29, 13, 28, 11], [29, 14, 28, 12, "shift"], [29, 19, 28, 17], [29, 20, 28, 18], [29, 21, 28, 19], [30, 6, 29, 4], [31, 6, 31, 4], [31, 10, 31, 8], [31, 17, 31, 15, "value"], [31, 22, 31, 20], [31, 27, 31, 25], [31, 35, 31, 33], [31, 37, 31, 35], [32, 8, 32, 6], [32, 14, 32, 12, "truncatedString"], [32, 29, 32, 27], [32, 32, 32, 30], [32, 51, 32, 49], [33, 8, 33, 6], [33, 12, 33, 10, "value"], [33, 17, 33, 15], [33, 18, 33, 16, "length"], [33, 24, 33, 22], [33, 27, 33, 25, "maxStringLimit"], [33, 41, 33, 39], [33, 44, 33, 42, "truncatedString"], [33, 59, 33, 57], [33, 60, 33, 58, "length"], [33, 66, 33, 64], [33, 68, 33, 66], [34, 10, 34, 8], [34, 17, 34, 15, "value"], [34, 22, 34, 20], [34, 23, 34, 21, "substring"], [34, 32, 34, 30], [34, 33, 34, 31], [34, 34, 34, 32], [34, 36, 34, 34, "maxStringLimit"], [34, 50, 34, 48], [34, 51, 34, 49], [34, 54, 34, 52, "truncatedString"], [34, 69, 34, 67], [35, 8, 35, 6], [36, 8, 36, 6], [36, 15, 36, 13, "value"], [36, 20, 36, 18], [37, 6, 37, 4], [38, 6, 38, 4], [38, 10, 38, 8], [38, 17, 38, 15, "value"], [38, 22, 38, 20], [38, 27, 38, 25], [38, 35, 38, 33], [38, 39, 38, 37, "value"], [38, 44, 38, 42], [38, 49, 38, 47], [38, 53, 38, 51], [38, 55, 38, 53], [39, 8, 39, 6], [39, 15, 39, 13, "value"], [39, 20, 39, 18], [40, 6, 40, 4], [41, 6, 42, 4], [41, 10, 42, 8, "retval"], [41, 16, 42, 14], [41, 19, 42, 17, "value"], [41, 24, 42, 22], [42, 6, 43, 4], [42, 10, 43, 8, "Array"], [42, 15, 43, 13], [42, 16, 43, 14, "isArray"], [42, 23, 43, 21], [42, 24, 43, 22, "value"], [42, 29, 43, 27], [42, 30, 43, 28], [42, 32, 43, 30], [43, 8, 44, 6], [43, 12, 44, 10, "stack"], [43, 17, 44, 15], [43, 18, 44, 16, "length"], [43, 24, 44, 22], [43, 28, 44, 26, "max<PERSON><PERSON><PERSON>"], [43, 36, 44, 34], [43, 38, 44, 36], [44, 10, 45, 8, "retval"], [44, 16, 45, 14], [44, 19, 45, 17], [44, 39, 45, 37, "value"], [44, 44, 45, 42], [44, 45, 45, 43, "length"], [44, 51, 45, 49], [44, 66, 45, 64], [45, 8, 46, 6], [45, 9, 46, 7], [45, 15, 46, 13], [45, 19, 46, 17, "value"], [45, 24, 46, 22], [45, 25, 46, 23, "length"], [45, 31, 46, 29], [45, 34, 46, 32, "maxArrayLimit"], [45, 47, 46, 45], [45, 49, 46, 47], [46, 10, 47, 8, "retval"], [46, 16, 47, 14], [46, 19, 47, 17, "value"], [46, 24, 47, 22], [46, 25, 48, 11, "slice"], [46, 30, 48, 16], [46, 31, 48, 17], [46, 32, 48, 18], [46, 34, 48, 20, "maxArrayLimit"], [46, 47, 48, 33], [46, 48, 48, 34], [46, 49, 49, 11, "concat"], [46, 55, 49, 17], [46, 56, 49, 18], [46, 57, 49, 19], [46, 70, 49, 32, "value"], [46, 75, 49, 37], [46, 76, 49, 38, "length"], [46, 82, 49, 44], [46, 85, 49, 47, "maxArrayLimit"], [46, 98, 49, 60], [46, 121, 49, 83], [46, 122, 49, 84], [46, 123, 49, 85], [47, 8, 50, 6], [48, 6, 51, 4], [48, 7, 51, 5], [48, 13, 51, 11], [49, 8, 52, 6], [50, 8, 53, 6], [50, 12, 53, 10], [50, 19, 53, 17, "value"], [50, 24, 53, 22], [50, 29, 53, 27], [50, 37, 53, 35], [50, 39, 53, 37], [51, 10, 54, 8], [51, 16, 54, 14], [51, 20, 54, 18, "Error"], [51, 25, 54, 23], [51, 26, 54, 24], [51, 58, 54, 56], [51, 59, 54, 57], [52, 8, 55, 6], [53, 8, 56, 6], [53, 14, 56, 12, "keys"], [53, 18, 56, 16], [53, 21, 56, 19, "Object"], [53, 27, 56, 25], [53, 28, 56, 26, "keys"], [53, 32, 56, 30], [53, 33, 56, 31, "value"], [53, 38, 56, 36], [53, 39, 56, 37], [54, 8, 57, 6], [54, 12, 57, 10, "stack"], [54, 17, 57, 15], [54, 18, 57, 16, "length"], [54, 24, 57, 22], [54, 28, 57, 26, "max<PERSON><PERSON><PERSON>"], [54, 36, 57, 34], [54, 38, 57, 36], [55, 10, 58, 8, "retval"], [55, 16, 58, 14], [55, 19, 58, 17], [55, 40, 58, 38, "keys"], [55, 44, 58, 42], [55, 45, 58, 43, "length"], [55, 51, 58, 49], [55, 64, 58, 62], [56, 8, 59, 6], [56, 9, 59, 7], [56, 15, 59, 13], [56, 19, 59, 17, "keys"], [56, 23, 59, 21], [56, 24, 59, 22, "length"], [56, 30, 59, 28], [56, 33, 59, 31, "maxObjectKeysLimit"], [56, 51, 59, 49], [56, 53, 59, 51], [57, 10, 60, 8], [58, 10, 61, 8, "retval"], [58, 16, 61, 14], [58, 19, 61, 17], [58, 20, 61, 18], [58, 21, 61, 19], [59, 10, 62, 8], [59, 15, 62, 13], [59, 21, 62, 19, "k"], [59, 22, 62, 20], [59, 26, 62, 24, "keys"], [59, 30, 62, 28], [59, 31, 62, 29, "slice"], [59, 36, 62, 34], [59, 37, 62, 35], [59, 38, 62, 36], [59, 40, 62, 38, "maxObjectKeysLimit"], [59, 58, 62, 56], [59, 59, 62, 57], [59, 61, 62, 59], [60, 12, 63, 10, "retval"], [60, 18, 63, 16], [60, 19, 63, 17, "k"], [60, 20, 63, 18], [60, 21, 63, 19], [60, 24, 63, 22, "value"], [60, 29, 63, 27], [60, 30, 63, 28, "k"], [60, 31, 63, 29], [60, 32, 63, 30], [61, 10, 64, 8], [62, 10, 65, 8], [62, 16, 65, 14, "truncatedKey"], [62, 28, 65, 26], [62, 31, 65, 29], [62, 55, 65, 53], [63, 10, 66, 8, "retval"], [63, 16, 66, 14], [63, 17, 66, 15, "truncatedKey"], [63, 29, 66, 27], [63, 30, 66, 28], [63, 33, 66, 31, "keys"], [63, 37, 66, 35], [63, 38, 66, 36, "length"], [63, 44, 66, 42], [63, 47, 66, 45, "maxObjectKeysLimit"], [63, 65, 66, 63], [64, 8, 67, 6], [65, 6, 68, 4], [66, 6, 69, 4, "stack"], [66, 11, 69, 9], [66, 12, 69, 10, "unshift"], [66, 19, 69, 17], [66, 20, 69, 18, "retval"], [66, 26, 69, 24], [66, 27, 69, 25], [67, 6, 70, 4], [67, 13, 70, 11, "retval"], [67, 19, 70, 17], [68, 4, 71, 2], [69, 4, 73, 2], [69, 11, 73, 9], [69, 20, 73, 18, "stringifySafe"], [69, 33, 73, 31, "stringifySafe"], [69, 34, 73, 32, "arg"], [69, 37, 73, 40], [69, 39, 73, 50], [70, 6, 74, 4], [70, 10, 74, 8, "arg"], [70, 13, 74, 11], [70, 18, 74, 16, "undefined"], [70, 27, 74, 25], [70, 29, 74, 27], [71, 8, 75, 6], [71, 15, 75, 13], [71, 26, 75, 24], [72, 6, 76, 4], [72, 7, 76, 5], [72, 13, 76, 11], [72, 17, 76, 15, "arg"], [72, 20, 76, 18], [72, 25, 76, 23], [72, 29, 76, 27], [72, 31, 76, 29], [73, 8, 77, 6], [73, 15, 77, 13], [73, 21, 77, 19], [74, 6, 78, 4], [74, 7, 78, 5], [74, 13, 78, 11], [74, 17, 78, 15], [74, 24, 78, 22, "arg"], [74, 27, 78, 25], [74, 32, 78, 30], [74, 42, 78, 40], [74, 44, 78, 42], [75, 8, 79, 6], [75, 12, 79, 10], [76, 10, 80, 8], [76, 17, 80, 15, "arg"], [76, 20, 80, 18], [76, 21, 80, 19, "toString"], [76, 29, 80, 27], [76, 30, 80, 28], [76, 31, 80, 29], [77, 8, 81, 6], [77, 9, 81, 7], [77, 10, 81, 8], [77, 16, 81, 14], [78, 10, 82, 8], [78, 17, 82, 15], [78, 37, 82, 35], [79, 8, 83, 6], [80, 6, 84, 4], [80, 7, 84, 5], [80, 13, 84, 11], [80, 17, 84, 15, "arg"], [80, 20, 84, 18], [80, 32, 84, 30, "Error"], [80, 37, 84, 35], [80, 39, 84, 37], [81, 8, 85, 6], [81, 15, 85, 13, "arg"], [81, 18, 85, 16], [81, 19, 85, 17, "name"], [81, 23, 85, 21], [81, 26, 85, 24], [81, 30, 85, 28], [81, 33, 85, 31, "arg"], [81, 36, 85, 34], [81, 37, 85, 35, "message"], [81, 44, 85, 42], [82, 6, 86, 4], [82, 7, 86, 5], [82, 13, 86, 11], [83, 8, 87, 6], [84, 8, 88, 6], [85, 8, 89, 6], [85, 12, 89, 10], [86, 10, 90, 8], [86, 16, 90, 14, "ret"], [86, 19, 90, 17], [86, 22, 90, 20, "JSON"], [86, 26, 90, 24], [86, 27, 90, 25, "stringify"], [86, 36, 90, 34], [86, 37, 90, 35, "arg"], [86, 40, 90, 38], [86, 42, 90, 40, "replacer"], [86, 50, 90, 48], [86, 51, 90, 49], [87, 10, 91, 8], [87, 14, 91, 12, "ret"], [87, 17, 91, 15], [87, 22, 91, 20, "undefined"], [87, 31, 91, 29], [87, 33, 91, 31], [88, 12, 92, 10], [88, 19, 92, 17], [88, 23, 92, 21], [88, 26, 92, 24], [88, 33, 92, 31, "arg"], [88, 36, 92, 34], [88, 39, 92, 37], [88, 63, 92, 61], [89, 10, 93, 8], [90, 10, 94, 8], [90, 17, 94, 15, "ret"], [90, 20, 94, 18], [91, 8, 95, 6], [91, 9, 95, 7], [91, 10, 95, 8], [91, 16, 95, 14], [92, 10, 96, 8], [92, 14, 96, 12], [92, 21, 96, 19, "arg"], [92, 24, 96, 22], [92, 25, 96, 23, "toString"], [92, 33, 96, 31], [92, 38, 96, 36], [92, 48, 96, 46], [92, 50, 96, 48], [93, 12, 97, 10], [93, 16, 97, 14], [94, 14, 98, 12], [95, 14, 99, 12], [95, 21, 99, 19, "arg"], [95, 24, 99, 22], [95, 25, 99, 23, "toString"], [95, 33, 99, 31], [95, 34, 99, 32], [95, 35, 99, 33], [96, 12, 100, 10], [96, 13, 100, 11], [96, 14, 100, 12], [96, 20, 100, 18], [96, 21, 100, 19], [97, 10, 101, 8], [98, 8, 102, 6], [99, 6, 103, 4], [100, 6, 104, 4], [100, 13, 104, 11], [100, 17, 104, 15], [100, 20, 104, 18], [100, 27, 104, 25, "arg"], [100, 30, 104, 28], [100, 33, 104, 31], [100, 57, 104, 55], [101, 4, 105, 2], [101, 5, 105, 3], [102, 2, 106, 0], [103, 2, 108, 0], [103, 8, 108, 6, "stringifySafe"], [103, 21, 108, 19], [103, 24, 108, 22, "createStringifySafeWithLimits"], [103, 53, 108, 51], [103, 54, 108, 52], [104, 4, 109, 2, "max<PERSON><PERSON><PERSON>"], [104, 12, 109, 10], [104, 14, 109, 12], [104, 16, 109, 14], [105, 4, 110, 2, "maxStringLimit"], [105, 18, 110, 16], [105, 20, 110, 18], [105, 23, 110, 21], [106, 4, 111, 2, "maxArrayLimit"], [106, 17, 111, 15], [106, 19, 111, 17], [106, 21, 111, 19], [107, 4, 112, 2, "maxObjectKeysLimit"], [107, 22, 112, 20], [107, 24, 112, 22], [108, 2, 113, 0], [108, 3, 113, 1], [108, 4, 113, 2], [109, 2, 113, 3], [109, 6, 113, 3, "_default"], [109, 14, 113, 3], [109, 17, 113, 3, "exports"], [109, 24, 113, 3], [109, 25, 113, 3, "default"], [109, 32, 113, 3], [109, 35, 115, 15, "stringifySafe"], [109, 48, 115, 28], [110, 0, 115, 28], [110, 3]], "functionMap": {"names": ["<global>", "createStringifySafeWithLimits", "replacer", "stringifySafe"], "mappings": "AAA;OCY;ECa;GD6C;SEE;GFgC;CDC"}}, "type": "js/module"}]}