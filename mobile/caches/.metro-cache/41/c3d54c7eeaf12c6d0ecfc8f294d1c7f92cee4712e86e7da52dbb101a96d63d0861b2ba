{"dependencies": [{"name": "./LogBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 71}, "end": {"line": 3, "column": 40, "index": 111}}], "key": "71qs15x6h+023IDOOB2uiatX93c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LogLevel = exports.DEFAULT_LOGGER_CONFIG = void 0;\n  exports.logToLogBoxAndConsole = logToLogBoxAndConsole;\n  exports.updateLoggerConfig = exports.replaceLoggerImplementation = exports.registerLoggerConfig = exports.logger = void 0;\n  var _LogBox = require(_dependencyMap[0], \"./LogBox\");\n  var DOCS_URL = 'https://docs.swmansion.com/react-native-reanimated/docs/debugging/logger-configuration';\n  var DOCS_REFERENCE = `If you don't want to see this message, you can disable the \\`strict\\` mode. Refer to:\\n${DOCS_URL} for more details.`;\n  var LogLevel = exports.LogLevel = /*#__PURE__*/function (LogLevel) {\n    LogLevel[LogLevel[\"warn\"] = 1] = \"warn\";\n    LogLevel[LogLevel[\"error\"] = 2] = \"error\";\n    return LogLevel;\n  }({});\n  var _worklet_8210553843770_init_data = {\n    code: \"function logToConsole_reactNativeReanimated_loggerTs1(data){switch(data.level){case'warn':console.warn(data.message.content);break;case'error':case'fatal':case'syntax':console.error(data.message.content);break;}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"logToConsole_reactNativeReanimated_loggerTs1\\\",\\\"data\\\",\\\"level\\\",\\\"console\\\",\\\"warn\\\",\\\"message\\\",\\\"content\\\",\\\"error\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AAwBA,SAAAA,4CAAqCA,CAAAC,IAAA,EAEnC,OAAQA,IAAI,CAACC,KAAK,EAChB,IAAK,MAAM,CACTC,OAAO,CAACC,IAAI,CAACH,IAAI,CAACI,OAAO,CAACC,OAAO,CAAC,CAClC,MACF,IAAK,OAAO,CACZ,IAAK,OAAO,CACZ,IAAK,QAAQ,CACXH,OAAO,CAACI,KAAK,CAACN,IAAI,CAACI,OAAO,CAACC,OAAO,CAAC,CACnC,MACJ,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var logToConsole = function () {\n    var _e = [new global.Error(), 1, -27];\n    var logToConsole = function (data) {\n      switch (data.level) {\n        case 'warn':\n          console.warn(data.message.content);\n          break;\n        case 'error':\n        case 'fatal':\n        case 'syntax':\n          console.error(data.message.content);\n          break;\n      }\n    };\n    logToConsole.__closure = {};\n    logToConsole.__workletHash = 8210553843770;\n    logToConsole.__initData = _worklet_8210553843770_init_data;\n    logToConsole.__stackDetails = _e;\n    return logToConsole;\n  }();\n  var DEFAULT_LOGGER_CONFIG = exports.DEFAULT_LOGGER_CONFIG = {\n    logFunction: logToConsole,\n    level: LogLevel.warn,\n    strict: true\n  };\n  var _worklet_14730042986167_init_data = {\n    code: \"function formatMessage_reactNativeReanimated_loggerTs2(message){return\\\"[Reanimated] \\\"+message;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"formatMessage_reactNativeReanimated_loggerTs2\\\",\\\"message\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AA4CA,SAAAA,6CAAwCA,CAAAC,OAAA,EAEtC,sBAAuBA,OAAO,CAChC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var formatMessage = function () {\n    var _e = [new global.Error(), 1, -27];\n    var formatMessage = function (message) {\n      return `[Reanimated] ${message}`;\n    };\n    formatMessage.__closure = {};\n    formatMessage.__workletHash = 14730042986167;\n    formatMessage.__initData = _worklet_14730042986167_init_data;\n    formatMessage.__stackDetails = _e;\n    return formatMessage;\n  }();\n  var _worklet_15708974184892_init_data = {\n    code: \"function createLog_reactNativeReanimated_loggerTs3(level,message){const{formatMessage}=this.__closure;const formattedMessage=formatMessage(message);return{level:level,message:{content:formattedMessage,substitutions:[]},category:formattedMessage,componentStack:[],componentStackType:null,stack:new Error().stack};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createLog_reactNativeReanimated_loggerTs3\\\",\\\"level\\\",\\\"message\\\",\\\"formatMessage\\\",\\\"__closure\\\",\\\"formattedMessage\\\",\\\"content\\\",\\\"substitutions\\\",\\\"category\\\",\\\"componentStack\\\",\\\"componentStackType\\\",\\\"stack\\\",\\\"Error\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AAiDA,SAAAA,yCAAoEA,CAAAC,KAAA,CAAAC,OAAA,QAAAC,aAAA,OAAAC,SAAA,CAElE,KAAM,CAAAC,gBAAgB,CAAGF,aAAa,CAACD,OAAO,CAAC,CAE/C,MAAO,CACLD,KAAK,CAALA,KAAK,CACLC,OAAO,CAAE,CACPI,OAAO,CAAED,gBAAgB,CACzBE,aAAa,CAAE,EACjB,CAAC,CACDC,QAAQ,CAAEH,gBAAgB,CAC1BI,cAAc,CAAE,EAAE,CAClBC,kBAAkB,CAAE,IAAI,CAExBC,KAAK,CAAE,GAAI,CAAAC,KAAK,CAAC,CAAC,CAACD,KACrB,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var createLog = function () {\n    var _e = [new global.Error(), -2, -27];\n    var createLog = function (level, message) {\n      var formattedMessage = formatMessage(message);\n      return {\n        level,\n        message: {\n          content: formattedMessage,\n          substitutions: []\n        },\n        category: formattedMessage,\n        componentStack: [],\n        componentStackType: null,\n        // eslint-disable-next-line reanimated/use-reanimated-error\n        stack: new Error().stack\n      };\n    };\n    createLog.__closure = {\n      formatMessage\n    };\n    createLog.__workletHash = 15708974184892;\n    createLog.__initData = _worklet_15708974184892_init_data;\n    createLog.__stackDetails = _e;\n    return createLog;\n  }();\n  /**\n   * Function that logs to LogBox and console. Used to replace the default console\n   * logging with logging to LogBox on the UI thread when runOnJS is available.\n   *\n   * @param data - The details of the log.\n   */\n  function logToLogBoxAndConsole(data) {\n    (0, _LogBox.addLogBoxLog)(data);\n    logToConsole(data);\n  }\n\n  /**\n   * Registers the logger configuration. use it only for Worklet runtimes.\n   *\n   * @param config - The config to register.\n   */\n  var _worklet_14322680997167_init_data = {\n    code: \"function registerLoggerConfig_reactNativeReanimated_loggerTs4(config){global.__reanimatedLoggerConfig=config;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"registerLoggerConfig_reactNativeReanimated_loggerTs4\\\",\\\"config\\\",\\\"global\\\",\\\"__reanimatedLoggerConfig\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AAmFO,SAAAA,oDAA4DA,CAAAC,MAAA,EAEjEC,MAAM,CAACC,wBAAwB,CAAGF,MAAM,CAC1C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var registerLoggerConfig = exports.registerLoggerConfig = function () {\n    var _e = [new global.Error(), 1, -27];\n    var registerLoggerConfig = function (config) {\n      global.__reanimatedLoggerConfig = config;\n    };\n    registerLoggerConfig.__closure = {};\n    registerLoggerConfig.__workletHash = 14322680997167;\n    registerLoggerConfig.__initData = _worklet_14322680997167_init_data;\n    registerLoggerConfig.__stackDetails = _e;\n    return registerLoggerConfig;\n  }();\n  /**\n   * Replaces the default log function with a custom implementation.\n   *\n   * @param logFunction - The custom log function.\n   */\n  var _worklet_15658644657739_init_data = {\n    code: \"function replaceLoggerImplementation_reactNativeReanimated_loggerTs5(logFunction){const{registerLoggerConfig}=this.__closure;registerLoggerConfig({...global.__reanimatedLoggerConfig,logFunction:logFunction});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"replaceLoggerImplementation_reactNativeReanimated_loggerTs5\\\",\\\"logFunction\\\",\\\"registerLoggerConfig\\\",\\\"__closure\\\",\\\"global\\\",\\\"__reanimatedLoggerConfig\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AA6FO,SAAAA,2DAA+DA,CAAAC,WAAA,QAAAC,oBAAA,OAAAC,SAAA,CAEpED,oBAAoB,CAAC,CAAE,GAAGE,MAAM,CAACC,wBAAwB,CAAEJ,WAAA,CAAAA,WAAY,CAAC,CAAC,CAC3E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var replaceLoggerImplementation = exports.replaceLoggerImplementation = function () {\n    var _e = [new global.Error(), -2, -27];\n    var replaceLoggerImplementation = function (logFunction) {\n      registerLoggerConfig({\n        ...global.__reanimatedLoggerConfig,\n        logFunction\n      });\n    };\n    replaceLoggerImplementation.__closure = {\n      registerLoggerConfig\n    };\n    replaceLoggerImplementation.__workletHash = 15658644657739;\n    replaceLoggerImplementation.__initData = _worklet_15658644657739_init_data;\n    replaceLoggerImplementation.__stackDetails = _e;\n    return replaceLoggerImplementation;\n  }();\n  /**\n   * Updates logger configuration.\n   *\n   * @param options - The new logger configuration to apply.\n   *\n   *   - Level: The minimum log level to display.\n   *   - Strict: Whether to log warnings and errors that are not strict. Defaults to\n   *       false.\n   */\n  var _worklet_8817718149339_init_data = {\n    code: \"function updateLoggerConfig_reactNativeReanimated_loggerTs6(options){const{registerLoggerConfig,DEFAULT_LOGGER_CONFIG}=this.__closure;var _options$level,_options$strict;registerLoggerConfig({...global.__reanimatedLoggerConfig,level:(_options$level=options===null||options===void 0?void 0:options.level)!==null&&_options$level!==void 0?_options$level:DEFAULT_LOGGER_CONFIG.level,strict:(_options$strict=options===null||options===void 0?void 0:options.strict)!==null&&_options$strict!==void 0?_options$strict:DEFAULT_LOGGER_CONFIG.strict});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"updateLoggerConfig_reactNativeReanimated_loggerTs6\\\",\\\"options\\\",\\\"registerLoggerConfig\\\",\\\"DEFAULT_LOGGER_CONFIG\\\",\\\"__closure\\\",\\\"_options$level\\\",\\\"_options$strict\\\",\\\"global\\\",\\\"__reanimatedLoggerConfig\\\",\\\"level\\\",\\\"strict\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AA2GO,SAAAA,kDAA6DA,CAAAC,OAAA,QAAAC,oBAAA,CAAAC,qBAAA,OAAAC,SAAA,KAAAC,cAAA,CAAAC,eAAA,CAElEJ,oBAAoB,CAAC,CACnB,GAAGK,MAAM,CAACC,wBAAwB,CAElCC,KAAK,EAAAJ,cAAA,CAAEJ,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEQ,KAAK,UAAAJ,cAAA,UAAAA,cAAA,CAAIF,qBAAqB,CAACM,KAAK,CACpDC,MAAM,EAAAJ,eAAA,CAAEL,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAES,MAAM,UAAAJ,eAAA,UAAAA,eAAA,CAAIH,qBAAqB,CAACO,MACnD,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var updateLoggerConfig = exports.updateLoggerConfig = function () {\n    var _e = [new global.Error(), -3, -27];\n    var updateLoggerConfig = function (options) {\n      registerLoggerConfig({\n        ...global.__reanimatedLoggerConfig,\n        // Don't reuse previous level and strict values from the global config\n        level: options?.level ?? DEFAULT_LOGGER_CONFIG.level,\n        strict: options?.strict ?? DEFAULT_LOGGER_CONFIG.strict\n      });\n    };\n    updateLoggerConfig.__closure = {\n      registerLoggerConfig,\n      DEFAULT_LOGGER_CONFIG\n    };\n    updateLoggerConfig.__workletHash = 8817718149339;\n    updateLoggerConfig.__initData = _worklet_8817718149339_init_data;\n    updateLoggerConfig.__stackDetails = _e;\n    return updateLoggerConfig;\n  }();\n  var _worklet_13545940141647_init_data = {\n    code: \"function handleLog_reactNativeReanimated_loggerTs7(level,message,options){const{LogLevel,DOCS_REFERENCE,createLog}=this.__closure;const config=global.__reanimatedLoggerConfig;if(options.strict&&!config.strict||LogLevel[level]<config.level){return;}if(options.strict){message+=\\\"\\\\n\\\\n\\\"+DOCS_REFERENCE;}config.logFunction(createLog(level,message));}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"handleLog_reactNativeReanimated_loggerTs7\\\",\\\"level\\\",\\\"message\\\",\\\"options\\\",\\\"LogLevel\\\",\\\"DOCS_REFERENCE\\\",\\\"createLog\\\",\\\"__closure\\\",\\\"config\\\",\\\"global\\\",\\\"__reanimatedLoggerConfig\\\",\\\"strict\\\",\\\"logFunction\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AAyHA,SAAAA,yCAIEA,CAAAC,KAAA,CAAAC,OAAA,CAAAC,OAAA,QAAAC,QAAA,CAAAC,cAAA,CAAAC,SAAA,OAAAC,SAAA,CAEA,KAAM,CAAAC,MAAM,CAAGC,MAAM,CAACC,wBAAwB,CAC9C,GAGGP,OAAO,CAACQ,MAAM,EAAI,CAACH,MAAM,CAACG,MAAM,EAEjCP,QAAQ,CAACH,KAAK,CAAC,CAAGO,MAAM,CAACP,KAAK,CAC9B,CACA,OACF,CAEA,GAAIE,OAAO,CAACQ,MAAM,CAAE,CAClBT,OAAO,SAAWG,cAAgB,CACpC,CAEAG,MAAM,CAACI,WAAW,CAACN,SAAS,CAACL,KAAK,CAAEC,OAAO,CAAC,CAAC,CAC/C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var handleLog = function () {\n    var _e = [new global.Error(), -4, -27];\n    var handleLog = function (level, message, options) {\n      var config = global.__reanimatedLoggerConfig;\n      if (\n      // Don't log if the log is marked as strict-only and the config doesn't\n      // enable strict logging\n      options.strict && !config.strict ||\n      // Don't log if the log level is below the minimum configured level\n      LogLevel[level] < config.level) {\n        return;\n      }\n      if (options.strict) {\n        message += `\\n\\n${DOCS_REFERENCE}`;\n      }\n      config.logFunction(createLog(level, message));\n    };\n    handleLog.__closure = {\n      LogLevel,\n      DOCS_REFERENCE,\n      createLog\n    };\n    handleLog.__workletHash = 13545940141647;\n    handleLog.__initData = _worklet_13545940141647_init_data;\n    handleLog.__stackDetails = _e;\n    return handleLog;\n  }();\n  var _worklet_5002261531216_init_data = {\n    code: \"function warn_reactNativeReanimated_loggerTs8(message,options={}){const{handleLog}=this.__closure;handleLog('warn',message,options);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"warn_reactNativeReanimated_loggerTs8\\\",\\\"message\\\",\\\"options\\\",\\\"handleLog\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AAkJE,SAAAA,oCAAgDA,CAAAC,OAAA,CAAAC,OAAA,WAAAC,SAAA,OAAAC,SAAA,CAE9CD,SAAS,CAAC,MAAM,CAAEF,OAAO,CAAEC,OAAO,CAAC,CACrC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_12967497607537_init_data = {\n    code: \"function error_reactNativeReanimated_loggerTs9(message,options={}){const{handleLog}=this.__closure;handleLog('error',message,options);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"error_reactNativeReanimated_loggerTs9\\\",\\\"message\\\",\\\"options\\\",\\\"handleLog\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/logger/logger.ts\\\"],\\\"mappings\\\":\\\"AAsJE,SAAAA,qCAAiDA,CAAAC,OAAA,CAAAC,OAAA,WAAAC,SAAA,OAAAC,SAAA,CAE/CD,SAAS,CAAC,OAAO,CAAEF,OAAO,CAAEC,OAAO,CAAC,CACtC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var logger = exports.logger = {\n    warn: function () {\n      var _e = [new global.Error(), -2, -27];\n      var warn = function (message) {\n        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        handleLog('warn', message, options);\n      };\n      warn.__closure = {\n        handleLog\n      };\n      warn.__workletHash = 5002261531216;\n      warn.__initData = _worklet_5002261531216_init_data;\n      warn.__stackDetails = _e;\n      return warn;\n    }(),\n    error: function () {\n      var _e = [new global.Error(), -2, -27];\n      var error = function (message) {\n        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        handleLog('error', message, options);\n      };\n      error.__closure = {\n        handleLog\n      };\n      error.__workletHash = 12967497607537;\n      error.__initData = _worklet_12967497607537_init_data;\n      error.__stackDetails = _e;\n      return error;\n    }()\n  };\n});", "lineCount": 266, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "LogLevel"], [7, 18, 1, 13], [7, 21, 1, 13, "exports"], [7, 28, 1, 13], [7, 29, 1, 13, "DEFAULT_LOGGER_CONFIG"], [7, 50, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "logToLogBoxAndConsole"], [8, 31, 1, 13], [8, 34, 1, 13, "logToLogBoxAndConsole"], [8, 55, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "updateLoggerConfig"], [9, 28, 1, 13], [9, 31, 1, 13, "exports"], [9, 38, 1, 13], [9, 39, 1, 13, "replaceLoggerImplementation"], [9, 66, 1, 13], [9, 69, 1, 13, "exports"], [9, 76, 1, 13], [9, 77, 1, 13, "registerLoggerConfig"], [9, 97, 1, 13], [9, 100, 1, 13, "exports"], [9, 107, 1, 13], [9, 108, 1, 13, "logger"], [9, 114, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_LogBox"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 5, 0], [11, 6, 5, 6, "DOCS_URL"], [11, 14, 5, 14], [11, 17, 6, 2], [11, 105, 6, 90], [12, 2, 7, 0], [12, 6, 7, 6, "DOCS_REFERENCE"], [12, 20, 7, 20], [12, 23, 7, 23], [12, 113, 7, 113, "DOCS_URL"], [12, 121, 7, 121], [12, 141, 7, 141], [13, 2, 7, 142], [13, 6, 11, 12, "LogLevel"], [13, 14, 11, 20], [13, 17, 11, 20, "exports"], [13, 24, 11, 20], [13, 25, 11, 20, "LogLevel"], [13, 33, 11, 20], [13, 59, 11, 12, "LogLevel"], [13, 67, 11, 20], [14, 4, 11, 12, "LogLevel"], [14, 12, 11, 20], [14, 13, 11, 12, "LogLevel"], [14, 21, 11, 20], [15, 4, 11, 12, "LogLevel"], [15, 12, 11, 20], [15, 13, 11, 12, "LogLevel"], [15, 21, 11, 20], [16, 4, 11, 20], [16, 11, 11, 12, "LogLevel"], [16, 19, 11, 20], [17, 2, 11, 20], [18, 2, 11, 20], [18, 6, 11, 20, "_worklet_8210553843770_init_data"], [18, 38, 11, 20], [19, 4, 11, 20, "code"], [19, 8, 11, 20], [20, 4, 11, 20, "location"], [20, 12, 11, 20], [21, 4, 11, 20, "sourceMap"], [21, 13, 11, 20], [22, 4, 11, 20, "version"], [22, 11, 11, 20], [23, 2, 11, 20], [24, 2, 11, 20], [24, 6, 11, 20, "logToConsole"], [24, 18, 11, 20], [24, 21, 25, 0], [25, 4, 25, 0], [25, 8, 25, 0, "_e"], [25, 10, 25, 0], [25, 18, 25, 0, "global"], [25, 24, 25, 0], [25, 25, 25, 0, "Error"], [25, 30, 25, 0], [26, 4, 25, 0], [26, 8, 25, 0, "logToConsole"], [26, 20, 25, 0], [26, 32, 25, 0, "logToConsole"], [26, 33, 25, 22, "data"], [26, 37, 25, 35], [26, 39, 25, 37], [27, 6, 27, 2], [27, 14, 27, 10, "data"], [27, 18, 27, 14], [27, 19, 27, 15, "level"], [27, 24, 27, 20], [28, 8, 28, 4], [28, 13, 28, 9], [28, 19, 28, 15], [29, 10, 29, 6, "console"], [29, 17, 29, 13], [29, 18, 29, 14, "warn"], [29, 22, 29, 18], [29, 23, 29, 19, "data"], [29, 27, 29, 23], [29, 28, 29, 24, "message"], [29, 35, 29, 31], [29, 36, 29, 32, "content"], [29, 43, 29, 39], [29, 44, 29, 40], [30, 10, 30, 6], [31, 8, 31, 4], [31, 13, 31, 9], [31, 20, 31, 16], [32, 8, 32, 4], [32, 13, 32, 9], [32, 20, 32, 16], [33, 8, 33, 4], [33, 13, 33, 9], [33, 21, 33, 17], [34, 10, 34, 6, "console"], [34, 17, 34, 13], [34, 18, 34, 14, "error"], [34, 23, 34, 19], [34, 24, 34, 20, "data"], [34, 28, 34, 24], [34, 29, 34, 25, "message"], [34, 36, 34, 32], [34, 37, 34, 33, "content"], [34, 44, 34, 40], [34, 45, 34, 41], [35, 10, 35, 6], [36, 6, 36, 2], [37, 4, 37, 0], [37, 5, 37, 1], [38, 4, 37, 1, "logToConsole"], [38, 16, 37, 1], [38, 17, 37, 1, "__closure"], [38, 26, 37, 1], [39, 4, 37, 1, "logToConsole"], [39, 16, 37, 1], [39, 17, 37, 1, "__workletHash"], [39, 30, 37, 1], [40, 4, 37, 1, "logToConsole"], [40, 16, 37, 1], [40, 17, 37, 1, "__initData"], [40, 27, 37, 1], [40, 30, 37, 1, "_worklet_8210553843770_init_data"], [40, 62, 37, 1], [41, 4, 37, 1, "logToConsole"], [41, 16, 37, 1], [41, 17, 37, 1, "__stackDetails"], [41, 31, 37, 1], [41, 34, 37, 1, "_e"], [41, 36, 37, 1], [42, 4, 37, 1], [42, 11, 37, 1, "logToConsole"], [42, 23, 37, 1], [43, 2, 37, 1], [43, 3, 25, 0], [44, 2, 39, 7], [44, 6, 39, 13, "DEFAULT_LOGGER_CONFIG"], [44, 27, 39, 56], [44, 30, 39, 56, "exports"], [44, 37, 39, 56], [44, 38, 39, 56, "DEFAULT_LOGGER_CONFIG"], [44, 59, 39, 56], [44, 62, 39, 59], [45, 4, 40, 2, "logFunction"], [45, 15, 40, 13], [45, 17, 40, 15, "logToConsole"], [45, 29, 40, 27], [46, 4, 41, 2, "level"], [46, 9, 41, 7], [46, 11, 41, 9, "LogLevel"], [46, 19, 41, 17], [46, 20, 41, 18, "warn"], [46, 24, 41, 22], [47, 4, 42, 2, "strict"], [47, 10, 42, 8], [47, 12, 42, 10], [48, 2, 43, 0], [48, 3, 43, 1], [49, 2, 43, 2], [49, 6, 43, 2, "_worklet_14730042986167_init_data"], [49, 39, 43, 2], [50, 4, 43, 2, "code"], [50, 8, 43, 2], [51, 4, 43, 2, "location"], [51, 12, 43, 2], [52, 4, 43, 2, "sourceMap"], [52, 13, 43, 2], [53, 4, 43, 2, "version"], [53, 11, 43, 2], [54, 2, 43, 2], [55, 2, 43, 2], [55, 6, 43, 2, "formatMessage"], [55, 19, 43, 2], [55, 22, 45, 0], [56, 4, 45, 0], [56, 8, 45, 0, "_e"], [56, 10, 45, 0], [56, 18, 45, 0, "global"], [56, 24, 45, 0], [56, 25, 45, 0, "Error"], [56, 30, 45, 0], [57, 4, 45, 0], [57, 8, 45, 0, "formatMessage"], [57, 21, 45, 0], [57, 33, 45, 0, "formatMessage"], [57, 34, 45, 23, "message"], [57, 41, 45, 38], [57, 43, 45, 40], [58, 6, 47, 2], [58, 13, 47, 9], [58, 29, 47, 25, "message"], [58, 36, 47, 32], [58, 38, 47, 34], [59, 4, 48, 0], [59, 5, 48, 1], [60, 4, 48, 1, "formatMessage"], [60, 17, 48, 1], [60, 18, 48, 1, "__closure"], [60, 27, 48, 1], [61, 4, 48, 1, "formatMessage"], [61, 17, 48, 1], [61, 18, 48, 1, "__workletHash"], [61, 31, 48, 1], [62, 4, 48, 1, "formatMessage"], [62, 17, 48, 1], [62, 18, 48, 1, "__initData"], [62, 28, 48, 1], [62, 31, 48, 1, "_worklet_14730042986167_init_data"], [62, 64, 48, 1], [63, 4, 48, 1, "formatMessage"], [63, 17, 48, 1], [63, 18, 48, 1, "__stackDetails"], [63, 32, 48, 1], [63, 35, 48, 1, "_e"], [63, 37, 48, 1], [64, 4, 48, 1], [64, 11, 48, 1, "formatMessage"], [64, 24, 48, 1], [65, 2, 48, 1], [65, 3, 45, 0], [66, 2, 45, 0], [66, 6, 45, 0, "_worklet_15708974184892_init_data"], [66, 39, 45, 0], [67, 4, 45, 0, "code"], [67, 8, 45, 0], [68, 4, 45, 0, "location"], [68, 12, 45, 0], [69, 4, 45, 0, "sourceMap"], [69, 13, 45, 0], [70, 4, 45, 0, "version"], [70, 11, 45, 0], [71, 2, 45, 0], [72, 2, 45, 0], [72, 6, 45, 0, "createLog"], [72, 15, 45, 0], [72, 18, 50, 0], [73, 4, 50, 0], [73, 8, 50, 0, "_e"], [73, 10, 50, 0], [73, 18, 50, 0, "global"], [73, 24, 50, 0], [73, 25, 50, 0, "Error"], [73, 30, 50, 0], [74, 4, 50, 0], [74, 8, 50, 0, "createLog"], [74, 17, 50, 0], [74, 29, 50, 0, "createLog"], [74, 30, 50, 19, "level"], [74, 35, 50, 40], [74, 37, 50, 42, "message"], [74, 44, 50, 57], [74, 46, 50, 68], [75, 6, 52, 2], [75, 10, 52, 8, "formattedMessage"], [75, 26, 52, 24], [75, 29, 52, 27, "formatMessage"], [75, 42, 52, 40], [75, 43, 52, 41, "message"], [75, 50, 52, 48], [75, 51, 52, 49], [76, 6, 54, 2], [76, 13, 54, 9], [77, 8, 55, 4, "level"], [77, 13, 55, 9], [78, 8, 56, 4, "message"], [78, 15, 56, 11], [78, 17, 56, 13], [79, 10, 57, 6, "content"], [79, 17, 57, 13], [79, 19, 57, 15, "formattedMessage"], [79, 35, 57, 31], [80, 10, 58, 6, "substitutions"], [80, 23, 58, 19], [80, 25, 58, 21], [81, 8, 59, 4], [81, 9, 59, 5], [82, 8, 60, 4, "category"], [82, 16, 60, 12], [82, 18, 60, 14, "formattedMessage"], [82, 34, 60, 30], [83, 8, 61, 4, "componentStack"], [83, 22, 61, 18], [83, 24, 61, 20], [83, 26, 61, 22], [84, 8, 62, 4, "componentStackType"], [84, 26, 62, 22], [84, 28, 62, 24], [84, 32, 62, 28], [85, 8, 63, 4], [86, 8, 64, 4, "stack"], [86, 13, 64, 9], [86, 15, 64, 11], [86, 19, 64, 15, "Error"], [86, 24, 64, 20], [86, 25, 64, 21], [86, 26, 64, 22], [86, 27, 64, 23, "stack"], [87, 6, 65, 2], [87, 7, 65, 3], [88, 4, 66, 0], [88, 5, 66, 1], [89, 4, 66, 1, "createLog"], [89, 13, 66, 1], [89, 14, 66, 1, "__closure"], [89, 23, 66, 1], [90, 6, 66, 1, "formatMessage"], [91, 4, 66, 1], [92, 4, 66, 1, "createLog"], [92, 13, 66, 1], [92, 14, 66, 1, "__workletHash"], [92, 27, 66, 1], [93, 4, 66, 1, "createLog"], [93, 13, 66, 1], [93, 14, 66, 1, "__initData"], [93, 24, 66, 1], [93, 27, 66, 1, "_worklet_15708974184892_init_data"], [93, 60, 66, 1], [94, 4, 66, 1, "createLog"], [94, 13, 66, 1], [94, 14, 66, 1, "__stackDetails"], [94, 28, 66, 1], [94, 31, 66, 1, "_e"], [94, 33, 66, 1], [95, 4, 66, 1], [95, 11, 66, 1, "createLog"], [95, 20, 66, 1], [96, 2, 66, 1], [96, 3, 50, 0], [97, 2, 68, 0], [98, 0, 69, 0], [99, 0, 70, 0], [100, 0, 71, 0], [101, 0, 72, 0], [102, 0, 73, 0], [103, 2, 74, 7], [103, 11, 74, 16, "logToLogBoxAndConsole"], [103, 32, 74, 37, "logToLogBoxAndConsole"], [103, 33, 74, 38, "data"], [103, 37, 74, 51], [103, 39, 74, 53], [104, 4, 75, 2], [104, 8, 75, 2, "addLogBoxLog"], [104, 28, 75, 14], [104, 30, 75, 15, "data"], [104, 34, 75, 19], [104, 35, 75, 20], [105, 4, 76, 2, "logToConsole"], [105, 16, 76, 14], [105, 17, 76, 15, "data"], [105, 21, 76, 19], [105, 22, 76, 20], [106, 2, 77, 0], [108, 2, 79, 0], [109, 0, 80, 0], [110, 0, 81, 0], [111, 0, 82, 0], [112, 0, 83, 0], [113, 2, 79, 0], [113, 6, 79, 0, "_worklet_14322680997167_init_data"], [113, 39, 79, 0], [114, 4, 79, 0, "code"], [114, 8, 79, 0], [115, 4, 79, 0, "location"], [115, 12, 79, 0], [116, 4, 79, 0, "sourceMap"], [116, 13, 79, 0], [117, 4, 79, 0, "version"], [117, 11, 79, 0], [118, 2, 79, 0], [119, 2, 79, 0], [119, 6, 79, 0, "registerLoggerConfig"], [119, 26, 79, 0], [119, 29, 79, 0, "exports"], [119, 36, 79, 0], [119, 37, 79, 0, "registerLoggerConfig"], [119, 57, 79, 0], [119, 60, 84, 7], [120, 4, 84, 7], [120, 8, 84, 7, "_e"], [120, 10, 84, 7], [120, 18, 84, 7, "global"], [120, 24, 84, 7], [120, 25, 84, 7, "Error"], [120, 30, 84, 7], [121, 4, 84, 7], [121, 8, 84, 7, "registerLoggerConfig"], [121, 28, 84, 7], [121, 40, 84, 7, "registerLoggerConfig"], [121, 41, 84, 37, "config"], [121, 47, 84, 65], [121, 49, 84, 67], [122, 6, 86, 2, "global"], [122, 12, 86, 8], [122, 13, 86, 9, "__reanimatedLoggerConfig"], [122, 37, 86, 33], [122, 40, 86, 36, "config"], [122, 46, 86, 42], [123, 4, 87, 0], [123, 5, 87, 1], [124, 4, 87, 1, "registerLoggerConfig"], [124, 24, 87, 1], [124, 25, 87, 1, "__closure"], [124, 34, 87, 1], [125, 4, 87, 1, "registerLoggerConfig"], [125, 24, 87, 1], [125, 25, 87, 1, "__workletHash"], [125, 38, 87, 1], [126, 4, 87, 1, "registerLoggerConfig"], [126, 24, 87, 1], [126, 25, 87, 1, "__initData"], [126, 35, 87, 1], [126, 38, 87, 1, "_worklet_14322680997167_init_data"], [126, 71, 87, 1], [127, 4, 87, 1, "registerLoggerConfig"], [127, 24, 87, 1], [127, 25, 87, 1, "__stackDetails"], [127, 39, 87, 1], [127, 42, 87, 1, "_e"], [127, 44, 87, 1], [128, 4, 87, 1], [128, 11, 87, 1, "registerLoggerConfig"], [128, 31, 87, 1], [129, 2, 87, 1], [129, 3, 84, 7], [130, 2, 89, 0], [131, 0, 90, 0], [132, 0, 91, 0], [133, 0, 92, 0], [134, 0, 93, 0], [135, 2, 89, 0], [135, 6, 89, 0, "_worklet_15658644657739_init_data"], [135, 39, 89, 0], [136, 4, 89, 0, "code"], [136, 8, 89, 0], [137, 4, 89, 0, "location"], [137, 12, 89, 0], [138, 4, 89, 0, "sourceMap"], [138, 13, 89, 0], [139, 4, 89, 0, "version"], [139, 11, 89, 0], [140, 2, 89, 0], [141, 2, 89, 0], [141, 6, 89, 0, "replaceLoggerImplementation"], [141, 33, 89, 0], [141, 36, 89, 0, "exports"], [141, 43, 89, 0], [141, 44, 89, 0, "replaceLoggerImplementation"], [141, 71, 89, 0], [141, 74, 94, 7], [142, 4, 94, 7], [142, 8, 94, 7, "_e"], [142, 10, 94, 7], [142, 18, 94, 7, "global"], [142, 24, 94, 7], [142, 25, 94, 7, "Error"], [142, 30, 94, 7], [143, 4, 94, 7], [143, 8, 94, 7, "replaceLoggerImplementation"], [143, 35, 94, 7], [143, 47, 94, 7, "replaceLoggerImplementation"], [143, 48, 94, 44, "logFunction"], [143, 59, 94, 68], [143, 61, 94, 70], [144, 6, 96, 2, "registerLoggerConfig"], [144, 26, 96, 22], [144, 27, 96, 23], [145, 8, 96, 25], [145, 11, 96, 28, "global"], [145, 17, 96, 34], [145, 18, 96, 35, "__reanimatedLoggerConfig"], [145, 42, 96, 59], [146, 8, 96, 61, "logFunction"], [147, 6, 96, 73], [147, 7, 96, 74], [147, 8, 96, 75], [148, 4, 97, 0], [148, 5, 97, 1], [149, 4, 97, 1, "replaceLoggerImplementation"], [149, 31, 97, 1], [149, 32, 97, 1, "__closure"], [149, 41, 97, 1], [150, 6, 97, 1, "registerLoggerConfig"], [151, 4, 97, 1], [152, 4, 97, 1, "replaceLoggerImplementation"], [152, 31, 97, 1], [152, 32, 97, 1, "__workletHash"], [152, 45, 97, 1], [153, 4, 97, 1, "replaceLoggerImplementation"], [153, 31, 97, 1], [153, 32, 97, 1, "__initData"], [153, 42, 97, 1], [153, 45, 97, 1, "_worklet_15658644657739_init_data"], [153, 78, 97, 1], [154, 4, 97, 1, "replaceLoggerImplementation"], [154, 31, 97, 1], [154, 32, 97, 1, "__stackDetails"], [154, 46, 97, 1], [154, 49, 97, 1, "_e"], [154, 51, 97, 1], [155, 4, 97, 1], [155, 11, 97, 1, "replaceLoggerImplementation"], [155, 38, 97, 1], [156, 2, 97, 1], [156, 3, 94, 7], [157, 2, 99, 0], [158, 0, 100, 0], [159, 0, 101, 0], [160, 0, 102, 0], [161, 0, 103, 0], [162, 0, 104, 0], [163, 0, 105, 0], [164, 0, 106, 0], [165, 0, 107, 0], [166, 2, 99, 0], [166, 6, 99, 0, "_worklet_8817718149339_init_data"], [166, 38, 99, 0], [167, 4, 99, 0, "code"], [167, 8, 99, 0], [168, 4, 99, 0, "location"], [168, 12, 99, 0], [169, 4, 99, 0, "sourceMap"], [169, 13, 99, 0], [170, 4, 99, 0, "version"], [170, 11, 99, 0], [171, 2, 99, 0], [172, 2, 99, 0], [172, 6, 99, 0, "updateLoggerConfig"], [172, 24, 99, 0], [172, 27, 99, 0, "exports"], [172, 34, 99, 0], [172, 35, 99, 0, "updateLoggerConfig"], [172, 53, 99, 0], [172, 56, 108, 7], [173, 4, 108, 7], [173, 8, 108, 7, "_e"], [173, 10, 108, 7], [173, 18, 108, 7, "global"], [173, 24, 108, 7], [173, 25, 108, 7, "Error"], [173, 30, 108, 7], [174, 4, 108, 7], [174, 8, 108, 7, "updateLoggerConfig"], [174, 26, 108, 7], [174, 38, 108, 7, "updateLoggerConfig"], [174, 39, 108, 35, "options"], [174, 46, 108, 66], [174, 48, 108, 68], [175, 6, 110, 2, "registerLoggerConfig"], [175, 26, 110, 22], [175, 27, 110, 23], [176, 8, 111, 4], [176, 11, 111, 7, "global"], [176, 17, 111, 13], [176, 18, 111, 14, "__reanimatedLoggerConfig"], [176, 42, 111, 38], [177, 8, 112, 4], [178, 8, 113, 4, "level"], [178, 13, 113, 9], [178, 15, 113, 11, "options"], [178, 22, 113, 18], [178, 24, 113, 20, "level"], [178, 29, 113, 25], [178, 33, 113, 29, "DEFAULT_LOGGER_CONFIG"], [178, 54, 113, 50], [178, 55, 113, 51, "level"], [178, 60, 113, 56], [179, 8, 114, 4, "strict"], [179, 14, 114, 10], [179, 16, 114, 12, "options"], [179, 23, 114, 19], [179, 25, 114, 21, "strict"], [179, 31, 114, 27], [179, 35, 114, 31, "DEFAULT_LOGGER_CONFIG"], [179, 56, 114, 52], [179, 57, 114, 53, "strict"], [180, 6, 115, 2], [180, 7, 115, 3], [180, 8, 115, 4], [181, 4, 116, 0], [181, 5, 116, 1], [182, 4, 116, 1, "updateLoggerConfig"], [182, 22, 116, 1], [182, 23, 116, 1, "__closure"], [182, 32, 116, 1], [183, 6, 116, 1, "registerLoggerConfig"], [183, 26, 116, 1], [184, 6, 116, 1, "DEFAULT_LOGGER_CONFIG"], [185, 4, 116, 1], [186, 4, 116, 1, "updateLoggerConfig"], [186, 22, 116, 1], [186, 23, 116, 1, "__workletHash"], [186, 36, 116, 1], [187, 4, 116, 1, "updateLoggerConfig"], [187, 22, 116, 1], [187, 23, 116, 1, "__initData"], [187, 33, 116, 1], [187, 36, 116, 1, "_worklet_8817718149339_init_data"], [187, 68, 116, 1], [188, 4, 116, 1, "updateLoggerConfig"], [188, 22, 116, 1], [188, 23, 116, 1, "__stackDetails"], [188, 37, 116, 1], [188, 40, 116, 1, "_e"], [188, 42, 116, 1], [189, 4, 116, 1], [189, 11, 116, 1, "updateLoggerConfig"], [189, 29, 116, 1], [190, 2, 116, 1], [190, 3, 108, 7], [191, 2, 108, 7], [191, 6, 108, 7, "_worklet_13545940141647_init_data"], [191, 39, 108, 7], [192, 4, 108, 7, "code"], [192, 8, 108, 7], [193, 4, 108, 7, "location"], [193, 12, 108, 7], [194, 4, 108, 7, "sourceMap"], [194, 13, 108, 7], [195, 4, 108, 7, "version"], [195, 11, 108, 7], [196, 2, 108, 7], [197, 2, 108, 7], [197, 6, 108, 7, "handleLog"], [197, 15, 108, 7], [197, 18, 122, 0], [198, 4, 122, 0], [198, 8, 122, 0, "_e"], [198, 10, 122, 0], [198, 18, 122, 0, "global"], [198, 24, 122, 0], [198, 25, 122, 0, "Error"], [198, 30, 122, 0], [199, 4, 122, 0], [199, 8, 122, 0, "handleLog"], [199, 17, 122, 0], [199, 29, 122, 0, "handleLog"], [199, 30, 123, 2, "level"], [199, 35, 123, 52], [199, 37, 124, 2, "message"], [199, 44, 124, 17], [199, 46, 125, 2, "options"], [199, 53, 125, 21], [199, 55, 126, 2], [200, 6, 128, 2], [200, 10, 128, 8, "config"], [200, 16, 128, 14], [200, 19, 128, 17, "global"], [200, 25, 128, 23], [200, 26, 128, 24, "__reanimatedLoggerConfig"], [200, 50, 128, 48], [201, 6, 129, 2], [202, 6, 130, 4], [203, 6, 131, 4], [204, 6, 132, 5, "options"], [204, 13, 132, 12], [204, 14, 132, 13, "strict"], [204, 20, 132, 19], [204, 24, 132, 23], [204, 25, 132, 24, "config"], [204, 31, 132, 30], [204, 32, 132, 31, "strict"], [204, 38, 132, 37], [205, 6, 133, 4], [206, 6, 134, 4, "LogLevel"], [206, 14, 134, 12], [206, 15, 134, 13, "level"], [206, 20, 134, 18], [206, 21, 134, 19], [206, 24, 134, 22, "config"], [206, 30, 134, 28], [206, 31, 134, 29, "level"], [206, 36, 134, 34], [206, 38, 135, 4], [207, 8, 136, 4], [208, 6, 137, 2], [209, 6, 139, 2], [209, 10, 139, 6, "options"], [209, 17, 139, 13], [209, 18, 139, 14, "strict"], [209, 24, 139, 20], [209, 26, 139, 22], [210, 8, 140, 4, "message"], [210, 15, 140, 11], [210, 19, 140, 15], [210, 26, 140, 22, "DOCS_REFERENCE"], [210, 40, 140, 36], [210, 42, 140, 38], [211, 6, 141, 2], [212, 6, 143, 2, "config"], [212, 12, 143, 8], [212, 13, 143, 9, "logFunction"], [212, 24, 143, 20], [212, 25, 143, 21, "createLog"], [212, 34, 143, 30], [212, 35, 143, 31, "level"], [212, 40, 143, 36], [212, 42, 143, 38, "message"], [212, 49, 143, 45], [212, 50, 143, 46], [212, 51, 143, 47], [213, 4, 144, 0], [213, 5, 144, 1], [214, 4, 144, 1, "handleLog"], [214, 13, 144, 1], [214, 14, 144, 1, "__closure"], [214, 23, 144, 1], [215, 6, 144, 1, "LogLevel"], [215, 14, 144, 1], [216, 6, 144, 1, "DOCS_REFERENCE"], [216, 20, 144, 1], [217, 6, 144, 1, "createLog"], [218, 4, 144, 1], [219, 4, 144, 1, "handleLog"], [219, 13, 144, 1], [219, 14, 144, 1, "__workletHash"], [219, 27, 144, 1], [220, 4, 144, 1, "handleLog"], [220, 13, 144, 1], [220, 14, 144, 1, "__initData"], [220, 24, 144, 1], [220, 27, 144, 1, "_worklet_13545940141647_init_data"], [220, 60, 144, 1], [221, 4, 144, 1, "handleLog"], [221, 13, 144, 1], [221, 14, 144, 1, "__stackDetails"], [221, 28, 144, 1], [221, 31, 144, 1, "_e"], [221, 33, 144, 1], [222, 4, 144, 1], [222, 11, 144, 1, "handleLog"], [222, 20, 144, 1], [223, 2, 144, 1], [223, 3, 122, 0], [224, 2, 122, 0], [224, 6, 122, 0, "_worklet_5002261531216_init_data"], [224, 38, 122, 0], [225, 4, 122, 0, "code"], [225, 8, 122, 0], [226, 4, 122, 0, "location"], [226, 12, 122, 0], [227, 4, 122, 0, "sourceMap"], [227, 13, 122, 0], [228, 4, 122, 0, "version"], [228, 11, 122, 0], [229, 2, 122, 0], [230, 2, 122, 0], [230, 6, 122, 0, "_worklet_12967497607537_init_data"], [230, 39, 122, 0], [231, 4, 122, 0, "code"], [231, 8, 122, 0], [232, 4, 122, 0, "location"], [232, 12, 122, 0], [233, 4, 122, 0, "sourceMap"], [233, 13, 122, 0], [234, 4, 122, 0, "version"], [234, 11, 122, 0], [235, 2, 122, 0], [236, 2, 146, 7], [236, 6, 146, 13, "logger"], [236, 12, 146, 19], [236, 15, 146, 19, "exports"], [236, 22, 146, 19], [236, 23, 146, 19, "logger"], [236, 29, 146, 19], [236, 32, 146, 22], [237, 4, 147, 2, "warn"], [237, 8, 147, 6], [237, 10, 147, 2], [238, 6, 147, 2], [238, 10, 147, 2, "_e"], [238, 12, 147, 2], [238, 20, 147, 2, "global"], [238, 26, 147, 2], [238, 27, 147, 2, "Error"], [238, 32, 147, 2], [239, 6, 147, 2], [239, 10, 147, 2, "warn"], [239, 14, 147, 2], [239, 26, 147, 2, "warn"], [239, 27, 147, 7, "message"], [239, 34, 147, 22], [239, 36, 147, 50], [240, 8, 147, 50], [240, 12, 147, 24, "options"], [240, 19, 147, 43], [240, 22, 147, 43, "arguments"], [240, 31, 147, 43], [240, 32, 147, 43, "length"], [240, 38, 147, 43], [240, 46, 147, 43, "arguments"], [240, 55, 147, 43], [240, 63, 147, 43, "undefined"], [240, 72, 147, 43], [240, 75, 147, 43, "arguments"], [240, 84, 147, 43], [240, 90, 147, 46], [240, 91, 147, 47], [240, 92, 147, 48], [241, 8, 149, 4, "handleLog"], [241, 17, 149, 13], [241, 18, 149, 14], [241, 24, 149, 20], [241, 26, 149, 22, "message"], [241, 33, 149, 29], [241, 35, 149, 31, "options"], [241, 42, 149, 38], [241, 43, 149, 39], [242, 6, 150, 2], [242, 7, 150, 3], [243, 6, 150, 3, "warn"], [243, 10, 150, 3], [243, 11, 150, 3, "__closure"], [243, 20, 150, 3], [244, 8, 150, 3, "handleLog"], [245, 6, 150, 3], [246, 6, 150, 3, "warn"], [246, 10, 150, 3], [246, 11, 150, 3, "__workletHash"], [246, 24, 150, 3], [247, 6, 150, 3, "warn"], [247, 10, 150, 3], [247, 11, 150, 3, "__initData"], [247, 21, 150, 3], [247, 24, 150, 3, "_worklet_5002261531216_init_data"], [247, 56, 150, 3], [248, 6, 150, 3, "warn"], [248, 10, 150, 3], [248, 11, 150, 3, "__stackDetails"], [248, 25, 150, 3], [248, 28, 150, 3, "_e"], [248, 30, 150, 3], [249, 6, 150, 3], [249, 13, 150, 3, "warn"], [249, 17, 150, 3], [250, 4, 150, 3], [250, 5, 147, 2], [251, 4, 151, 2, "error"], [251, 9, 151, 7], [251, 11, 151, 2], [252, 6, 151, 2], [252, 10, 151, 2, "_e"], [252, 12, 151, 2], [252, 20, 151, 2, "global"], [252, 26, 151, 2], [252, 27, 151, 2, "Error"], [252, 32, 151, 2], [253, 6, 151, 2], [253, 10, 151, 2, "error"], [253, 15, 151, 2], [253, 27, 151, 2, "error"], [253, 28, 151, 8, "message"], [253, 35, 151, 23], [253, 37, 151, 51], [254, 8, 151, 51], [254, 12, 151, 25, "options"], [254, 19, 151, 44], [254, 22, 151, 44, "arguments"], [254, 31, 151, 44], [254, 32, 151, 44, "length"], [254, 38, 151, 44], [254, 46, 151, 44, "arguments"], [254, 55, 151, 44], [254, 63, 151, 44, "undefined"], [254, 72, 151, 44], [254, 75, 151, 44, "arguments"], [254, 84, 151, 44], [254, 90, 151, 47], [254, 91, 151, 48], [254, 92, 151, 49], [255, 8, 153, 4, "handleLog"], [255, 17, 153, 13], [255, 18, 153, 14], [255, 25, 153, 21], [255, 27, 153, 23, "message"], [255, 34, 153, 30], [255, 36, 153, 32, "options"], [255, 43, 153, 39], [255, 44, 153, 40], [256, 6, 154, 2], [256, 7, 154, 3], [257, 6, 154, 3, "error"], [257, 11, 154, 3], [257, 12, 154, 3, "__closure"], [257, 21, 154, 3], [258, 8, 154, 3, "handleLog"], [259, 6, 154, 3], [260, 6, 154, 3, "error"], [260, 11, 154, 3], [260, 12, 154, 3, "__workletHash"], [260, 25, 154, 3], [261, 6, 154, 3, "error"], [261, 11, 154, 3], [261, 12, 154, 3, "__initData"], [261, 22, 154, 3], [261, 25, 154, 3, "_worklet_12967497607537_init_data"], [261, 58, 154, 3], [262, 6, 154, 3, "error"], [262, 11, 154, 3], [262, 12, 154, 3, "__stackDetails"], [262, 26, 154, 3], [262, 29, 154, 3, "_e"], [262, 31, 154, 3], [263, 6, 154, 3], [263, 13, 154, 3, "error"], [263, 18, 154, 3], [264, 4, 154, 3], [264, 5, 151, 2], [265, 2, 155, 0], [265, 3, 155, 1], [266, 0, 155, 2], [266, 3]], "functionMap": {"names": ["<global>", "logToConsole", "formatMessage", "createLog", "logToLogBoxAndConsole", "registerLoggerConfig", "replaceLoggerImplementation", "updateLoggerConfig", "handleLog", "warn", "error"], "mappings": "AAA;ACwB;CDY;AEQ;CFG;AGE;CHgB;OIQ;CJG;OKO;CLG;OMO;CNG;OOW;CPQ;AQM;CRsB;ESG;GTG;EUC;GVG"}}, "type": "js/module"}]}