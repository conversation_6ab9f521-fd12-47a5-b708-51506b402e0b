{"dependencies": [{"name": "./ImportMetaRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 9, "index": 73}, "end": {"line": 2, "column": 40, "index": 104}}], "key": "E9M+TcyDJ9PkgtIqN+FAyloPpsM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(globalThis, '__ExpoImportMetaRegistry', {\n    value: require(_dependencyMap[0], \"./ImportMetaRegistry\").ImportMetaRegistry,\n    enumerable: false,\n    writable: true\n  });\n});", "lineCount": 7, "map": [[2, 2, 1, 0, "Object"], [2, 8, 1, 6], [2, 9, 1, 7, "defineProperty"], [2, 23, 1, 21], [2, 24, 1, 22, "globalThis"], [2, 34, 1, 32], [2, 36, 1, 34], [2, 62, 1, 60], [2, 64, 1, 62], [3, 4, 2, 2, "value"], [3, 9, 2, 7], [3, 11, 2, 9, "require"], [3, 18, 2, 16], [3, 19, 2, 16, "_dependencyMap"], [3, 33, 2, 16], [3, 60, 2, 39], [3, 61, 2, 40], [3, 62, 2, 41, "ImportMetaRegistry"], [3, 80, 2, 59], [4, 4, 3, 2, "enumerable"], [4, 14, 3, 12], [4, 16, 3, 14], [4, 21, 3, 19], [5, 4, 4, 2, "writable"], [5, 12, 4, 10], [5, 14, 4, 12], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 0, 5, 3], [7, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}