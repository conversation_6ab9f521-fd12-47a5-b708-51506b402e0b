{"dependencies": [{"name": "react-native-safe-area-context/lib/commonjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 7, "column": 53, "index": 170}}], "key": "rTYCe/OVA9H2u9+IUaGIhNpGQOQ=", "exportNames": ["*"]}}, {"name": "./SafeAreaView.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 280}, "end": {"line": 10, "column": 50, "index": 330}}], "key": "P5/ptiBlbwdnK1mGelHb0sUA/s0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"SafeAreaFrameContext\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.SafeAreaFrameContext;\n    }\n  });\n  Object.defineProperty(exports, \"SafeAreaInsetsContext\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.SafeAreaInsetsContext;\n    }\n  });\n  Object.defineProperty(exports, \"SafeAreaProvider\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.SafeAreaProvider;\n    }\n  });\n  Object.defineProperty(exports, \"SafeAreaView\", {\n    enumerable: true,\n    get: function () {\n      return _SafeAreaView.SafeAreaView;\n    }\n  });\n  Object.defineProperty(exports, \"initialWindowMetrics\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.initialWindowMetrics;\n    }\n  });\n  Object.defineProperty(exports, \"useSafeAreaFrame\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.useSafeAreaFrame;\n    }\n  });\n  exports.useSafeAreaInsets = void 0;\n  var _commonjs = require(_dependencyMap[0], \"react-native-safe-area-context/lib/commonjs\");\n  var _SafeAreaView = require(_dependencyMap[1], \"./SafeAreaView.web\");\n  var _s = $RefreshSig$();\n  const useSafeAreaInsets = () => {\n    _s();\n    const isTabletAndAbove = typeof window !== 'undefined' ? window.self !== window.top : true;\n    const insets = (0, _commonjs.useSafeAreaInsets)();\n    if (isTabletAndAbove) {\n      return {\n        left: 0,\n        right: 0,\n        top: 64,\n        bottom: 34\n      };\n    }\n    return insets;\n  };\n  exports.useSafeAreaInsets = useSafeAreaInsets;\n  _s(useSafeAreaInsets, \"ED3fxwDSQuSkwhym5Hy4GFdn1nc=\", false, function () {\n    return [_commonjs.useSafeAreaInsets];\n  });\n});", "lineCount": 63, "map": [[42, 2, 1, 0], [42, 6, 1, 0, "_commonjs"], [42, 15, 1, 0], [42, 18, 1, 0, "require"], [42, 25, 1, 0], [42, 26, 1, 0, "_dependencyMap"], [42, 40, 1, 0], [43, 2, 10, 0], [43, 6, 10, 0, "_SafeAreaView"], [43, 19, 10, 0], [43, 22, 10, 0, "require"], [43, 29, 10, 0], [43, 30, 10, 0, "_dependencyMap"], [43, 44, 10, 0], [44, 2, 10, 50], [44, 6, 10, 50, "_s"], [44, 8, 10, 50], [44, 11, 10, 50, "$RefreshSig$"], [44, 23, 10, 50], [45, 2, 12, 7], [45, 8, 12, 13, "useSafeAreaInsets"], [45, 25, 12, 30], [45, 28, 12, 33, "useSafeAreaInsets"], [45, 29, 12, 33], [45, 34, 12, 39], [46, 4, 12, 39, "_s"], [46, 6, 12, 39], [47, 4, 13, 1], [47, 10, 13, 7, "isTabletAndAbove"], [47, 26, 13, 23], [47, 29, 14, 2], [47, 36, 14, 9, "window"], [47, 42, 14, 15], [47, 47, 14, 20], [47, 58, 14, 31], [47, 61, 14, 34, "window"], [47, 67, 14, 40], [47, 68, 14, 41, "self"], [47, 72, 14, 45], [47, 77, 14, 50, "window"], [47, 83, 14, 56], [47, 84, 14, 57, "top"], [47, 87, 14, 60], [47, 90, 14, 63], [47, 94, 14, 67], [48, 4, 15, 1], [48, 10, 15, 7, "insets"], [48, 16, 15, 13], [48, 19, 15, 16], [48, 23, 15, 16, "useNativeSafeAreaInsets"], [48, 50, 15, 39], [48, 52, 15, 40], [48, 53, 15, 41], [49, 4, 16, 1], [49, 8, 16, 5, "isTabletAndAbove"], [49, 24, 16, 21], [49, 26, 16, 23], [50, 6, 17, 2], [50, 13, 17, 9], [51, 8, 18, 3, "left"], [51, 12, 18, 7], [51, 14, 18, 9], [51, 15, 18, 10], [52, 8, 19, 3, "right"], [52, 13, 19, 8], [52, 15, 19, 10], [52, 16, 19, 11], [53, 8, 20, 3, "top"], [53, 11, 20, 6], [53, 13, 20, 8], [53, 15, 20, 10], [54, 8, 21, 3, "bottom"], [54, 14, 21, 9], [54, 16, 21, 11], [55, 6, 22, 2], [55, 7, 22, 3], [56, 4, 23, 1], [57, 4, 24, 1], [57, 11, 24, 8, "insets"], [57, 17, 24, 14], [58, 2, 25, 0], [58, 3, 25, 1], [59, 2, 25, 2, "exports"], [59, 9, 25, 2], [59, 10, 25, 2, "useSafeAreaInsets"], [59, 27, 25, 2], [59, 30, 25, 2, "useSafeAreaInsets"], [59, 47, 25, 2], [60, 2, 25, 2, "_s"], [60, 4, 25, 2], [60, 5, 12, 13, "useSafeAreaInsets"], [60, 22, 12, 30], [61, 4, 12, 30], [61, 12, 15, 16, "useNativeSafeAreaInsets"], [61, 39, 15, 39], [62, 2, 15, 39], [63, 0, 15, 39], [63, 3]], "functionMap": {"names": ["<global>", "useSafeAreaInsets"], "mappings": "AAA;iCCW;CDa"}}, "type": "js/module"}]}