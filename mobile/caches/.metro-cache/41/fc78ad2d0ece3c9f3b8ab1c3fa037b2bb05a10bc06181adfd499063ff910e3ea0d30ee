{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FontDisplay = void 0;\n  // @needsAudit\n  /**\n   * Sets the [font-display](https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display)\n   * for a given typeface. The default font value on web is `FontDisplay.AUTO`.\n   * Even though setting the `fontDisplay` does nothing on native platforms, the default behavior\n   * emulates `FontDisplay.SWAP` on flagship devices like iOS, Samsung, Pixel, etc. Default\n   * functionality varies on One Plus devices. In the browser this value is set in the generated\n   * `@font-face` CSS block and not as a style property meaning you cannot dynamically change this\n   * value based on the element it's used in.\n   * @platform web\n   */\n  var FontDisplay;\n  (function (FontDisplay) {\n    /**\n     * __(Default)__ The font display strategy is defined by the user agent or platform.\n     * This generally defaults to the text being invisible until the font is loaded.\n     * Good for buttons or banners that require a specific treatment.\n     */\n    FontDisplay[\"AUTO\"] = \"auto\";\n    /**\n     * Fallback text is rendered immediately with a default font while the desired font is loaded.\n     * This is good for making the content appear to load instantly and is usually preferred.\n     */\n    FontDisplay[\"SWAP\"] = \"swap\";\n    /**\n     * The text will be invisible until the font has loaded. If the font fails to load then nothing\n     * will appear - it's best to turn this off when debugging missing text.\n     */\n    FontDisplay[\"BLOCK\"] = \"block\";\n    /**\n     * Splits the behavior between `SWAP` and `BLOCK`.\n     * There will be a [100ms timeout](https://developers.google.com/web/updates/2016/02/font-display?hl=en)\n     * where the text with a custom font is invisible, after that the text will either swap to the\n     * styled text or it'll show the unstyled text and continue to load the custom font. This is good\n     * for buttons that need a custom font but should also be quickly available to screen-readers.\n     */\n    FontDisplay[\"FALLBACK\"] = \"fallback\";\n    /**\n     * This works almost identically to `FALLBACK`, the only difference is that the browser will\n     * decide to load the font based on slow connection speed or critical resource demand.\n     */\n    FontDisplay[\"OPTIONAL\"] = \"optional\";\n  })(FontDisplay || (exports.FontDisplay = FontDisplay = {}));\n});", "lineCount": 49, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 2, 12, 7], [17, 6, 12, 11, "FontDisplay"], [17, 17, 12, 22], [18, 2, 13, 0], [18, 3, 13, 1], [18, 13, 13, 11, "FontDisplay"], [18, 24, 13, 22], [18, 26, 13, 24], [19, 4, 14, 4], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 4, 19, 4, "FontDisplay"], [24, 15, 19, 15], [24, 16, 19, 16], [24, 22, 19, 22], [24, 23, 19, 23], [24, 26, 19, 26], [24, 32, 19, 32], [25, 4, 20, 4], [26, 0, 21, 0], [27, 0, 22, 0], [28, 0, 23, 0], [29, 4, 24, 4, "FontDisplay"], [29, 15, 24, 15], [29, 16, 24, 16], [29, 22, 24, 22], [29, 23, 24, 23], [29, 26, 24, 26], [29, 32, 24, 32], [30, 4, 25, 4], [31, 0, 26, 0], [32, 0, 27, 0], [33, 0, 28, 0], [34, 4, 29, 4, "FontDisplay"], [34, 15, 29, 15], [34, 16, 29, 16], [34, 23, 29, 23], [34, 24, 29, 24], [34, 27, 29, 27], [34, 34, 29, 34], [35, 4, 30, 4], [36, 0, 31, 0], [37, 0, 32, 0], [38, 0, 33, 0], [39, 0, 34, 0], [40, 0, 35, 0], [41, 0, 36, 0], [42, 4, 37, 4, "FontDisplay"], [42, 15, 37, 15], [42, 16, 37, 16], [42, 26, 37, 26], [42, 27, 37, 27], [42, 30, 37, 30], [42, 40, 37, 40], [43, 4, 38, 4], [44, 0, 39, 0], [45, 0, 40, 0], [46, 0, 41, 0], [47, 4, 42, 4, "FontDisplay"], [47, 15, 42, 15], [47, 16, 42, 16], [47, 26, 42, 26], [47, 27, 42, 27], [47, 30, 42, 30], [47, 40, 42, 40], [48, 2, 43, 0], [48, 3, 43, 1], [48, 5, 43, 3, "FontDisplay"], [48, 16, 43, 14], [48, 21, 43, 14, "exports"], [48, 28, 43, 14], [48, 29, 43, 14, "FontDisplay"], [48, 40, 43, 14], [48, 43, 43, 19, "FontDisplay"], [48, 54, 43, 30], [48, 57, 43, 33], [48, 58, 43, 34], [48, 59, 43, 35], [48, 60, 43, 36], [48, 61, 43, 37], [49, 0, 43, 38], [49, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;CCY;CD8B"}}, "type": "js/module"}]}