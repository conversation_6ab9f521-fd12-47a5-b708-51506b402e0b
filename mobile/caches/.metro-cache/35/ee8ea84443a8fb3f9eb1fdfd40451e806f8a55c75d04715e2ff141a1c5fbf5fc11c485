{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 26, "index": 41}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 104}, "end": {"line": 5, "column": 66, "index": 170}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 171}, "end": {"line": 6, "column": 36, "index": 207}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../fabric/SearchBarNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 230}, "end": {"line": 15, "column": 44, "index": 452}}], "key": "QPGe7hkFRScOQWa16bjrGzHyiBo=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _utils = require(_dependencyMap[2], \"../utils\");\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _SearchBarNativeComponent = _interopRequireWildcard(require(_dependencyMap[4], \"../fabric/SearchBarNativeComponent\"));\n  var _jsxRuntime = require(_dependencyMap[5], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-screens/src/components/SearchBar.tsx\"; // Native components\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NativeSearchBar = _SearchBarNativeComponent.default;\n  var NativeSearchBarCommands = _SearchBarNativeComponent.Commands;\n  function SearchBar(props, forwardedRef) {\n    var searchBarRef = _react.default.useRef(null);\n    _react.default.useImperativeHandle(forwardedRef, () => ({\n      blur: () => {\n        _callMethodWithRef(ref => NativeSearchBarCommands.blur(ref));\n      },\n      focus: () => {\n        _callMethodWithRef(ref => NativeSearchBarCommands.focus(ref));\n      },\n      toggleCancelButton: flag => {\n        _callMethodWithRef(ref => NativeSearchBarCommands.toggleCancelButton(ref, flag));\n      },\n      clearText: () => {\n        _callMethodWithRef(ref => NativeSearchBarCommands.clearText(ref));\n      },\n      setText: text => {\n        _callMethodWithRef(ref => NativeSearchBarCommands.setText(ref, text));\n      },\n      cancelSearch: () => {\n        _callMethodWithRef(ref => NativeSearchBarCommands.cancelSearch(ref));\n      }\n    }));\n    var _callMethodWithRef = _react.default.useCallback(method => {\n      var ref = searchBarRef.current;\n      if (ref) {\n        method(ref);\n      } else {\n        console.warn('Reference to native search bar component has not been updated yet');\n      }\n    }, [searchBarRef]);\n    if (!_utils.isSearchBarAvailableForCurrentPlatform) {\n      console.warn('Importing SearchBar is only valid on iOS and Android devices.');\n      return _reactNative.View;\n    }\n    return (0, _jsxRuntime.jsx)(NativeSearchBar, {\n      ref: searchBarRef,\n      ...props,\n      onSearchFocus: props.onFocus,\n      onSearchBlur: props.onBlur,\n      onSearchButtonPress: props.onSearchButtonPress,\n      onCancelButtonPress: props.onCancelButtonPress,\n      onChangeText: props.onChangeText\n    });\n  }\n  var _default = exports.default = /*#__PURE__*/_react.default.forwardRef(SearchBar);\n});", "lineCount": 63, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_react"], [9, 12, 3, 0], [9, 15, 3, 0, "_interopRequireDefault"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_utils"], [10, 12, 5, 0], [10, 15, 5, 0, "require"], [10, 22, 5, 0], [10, 23, 5, 0, "_dependencyMap"], [10, 37, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_reactNative"], [11, 18, 6, 0], [11, 21, 6, 0, "require"], [11, 28, 6, 0], [11, 29, 6, 0, "_dependencyMap"], [11, 43, 6, 0], [12, 2, 9, 0], [12, 6, 9, 0, "_SearchBarNativeComponent"], [12, 31, 9, 0], [12, 34, 9, 0, "_interopRequireWildcard"], [12, 57, 9, 0], [12, 58, 9, 0, "require"], [12, 65, 9, 0], [12, 66, 9, 0, "_dependencyMap"], [12, 80, 9, 0], [13, 2, 15, 44], [13, 6, 15, 44, "_jsxRuntime"], [13, 17, 15, 44], [13, 20, 15, 44, "require"], [13, 27, 15, 44], [13, 28, 15, 44, "_dependencyMap"], [13, 42, 15, 44], [14, 2, 15, 44], [14, 6, 15, 44, "_jsxFileName"], [14, 18, 15, 44], [14, 110, 8, 0], [15, 2, 8, 0], [15, 11, 8, 0, "_interopRequireWildcard"], [15, 35, 8, 0, "e"], [15, 36, 8, 0], [15, 38, 8, 0, "t"], [15, 39, 8, 0], [15, 68, 8, 0, "WeakMap"], [15, 75, 8, 0], [15, 81, 8, 0, "r"], [15, 82, 8, 0], [15, 89, 8, 0, "WeakMap"], [15, 96, 8, 0], [15, 100, 8, 0, "n"], [15, 101, 8, 0], [15, 108, 8, 0, "WeakMap"], [15, 115, 8, 0], [15, 127, 8, 0, "_interopRequireWildcard"], [15, 150, 8, 0], [15, 162, 8, 0, "_interopRequireWildcard"], [15, 163, 8, 0, "e"], [15, 164, 8, 0], [15, 166, 8, 0, "t"], [15, 167, 8, 0], [15, 176, 8, 0, "t"], [15, 177, 8, 0], [15, 181, 8, 0, "e"], [15, 182, 8, 0], [15, 186, 8, 0, "e"], [15, 187, 8, 0], [15, 188, 8, 0, "__esModule"], [15, 198, 8, 0], [15, 207, 8, 0, "e"], [15, 208, 8, 0], [15, 214, 8, 0, "o"], [15, 215, 8, 0], [15, 217, 8, 0, "i"], [15, 218, 8, 0], [15, 220, 8, 0, "f"], [15, 221, 8, 0], [15, 226, 8, 0, "__proto__"], [15, 235, 8, 0], [15, 243, 8, 0, "default"], [15, 250, 8, 0], [15, 252, 8, 0, "e"], [15, 253, 8, 0], [15, 270, 8, 0, "e"], [15, 271, 8, 0], [15, 294, 8, 0, "e"], [15, 295, 8, 0], [15, 320, 8, 0, "e"], [15, 321, 8, 0], [15, 330, 8, 0, "f"], [15, 331, 8, 0], [15, 337, 8, 0, "o"], [15, 338, 8, 0], [15, 341, 8, 0, "t"], [15, 342, 8, 0], [15, 345, 8, 0, "n"], [15, 346, 8, 0], [15, 349, 8, 0, "r"], [15, 350, 8, 0], [15, 358, 8, 0, "o"], [15, 359, 8, 0], [15, 360, 8, 0, "has"], [15, 363, 8, 0], [15, 364, 8, 0, "e"], [15, 365, 8, 0], [15, 375, 8, 0, "o"], [15, 376, 8, 0], [15, 377, 8, 0, "get"], [15, 380, 8, 0], [15, 381, 8, 0, "e"], [15, 382, 8, 0], [15, 385, 8, 0, "o"], [15, 386, 8, 0], [15, 387, 8, 0, "set"], [15, 390, 8, 0], [15, 391, 8, 0, "e"], [15, 392, 8, 0], [15, 394, 8, 0, "f"], [15, 395, 8, 0], [15, 409, 8, 0, "_t"], [15, 411, 8, 0], [15, 415, 8, 0, "e"], [15, 416, 8, 0], [15, 432, 8, 0, "_t"], [15, 434, 8, 0], [15, 441, 8, 0, "hasOwnProperty"], [15, 455, 8, 0], [15, 456, 8, 0, "call"], [15, 460, 8, 0], [15, 461, 8, 0, "e"], [15, 462, 8, 0], [15, 464, 8, 0, "_t"], [15, 466, 8, 0], [15, 473, 8, 0, "i"], [15, 474, 8, 0], [15, 478, 8, 0, "o"], [15, 479, 8, 0], [15, 482, 8, 0, "Object"], [15, 488, 8, 0], [15, 489, 8, 0, "defineProperty"], [15, 503, 8, 0], [15, 508, 8, 0, "Object"], [15, 514, 8, 0], [15, 515, 8, 0, "getOwnPropertyDescriptor"], [15, 539, 8, 0], [15, 540, 8, 0, "e"], [15, 541, 8, 0], [15, 543, 8, 0, "_t"], [15, 545, 8, 0], [15, 552, 8, 0, "i"], [15, 553, 8, 0], [15, 554, 8, 0, "get"], [15, 557, 8, 0], [15, 561, 8, 0, "i"], [15, 562, 8, 0], [15, 563, 8, 0, "set"], [15, 566, 8, 0], [15, 570, 8, 0, "o"], [15, 571, 8, 0], [15, 572, 8, 0, "f"], [15, 573, 8, 0], [15, 575, 8, 0, "_t"], [15, 577, 8, 0], [15, 579, 8, 0, "i"], [15, 580, 8, 0], [15, 584, 8, 0, "f"], [15, 585, 8, 0], [15, 586, 8, 0, "_t"], [15, 588, 8, 0], [15, 592, 8, 0, "e"], [15, 593, 8, 0], [15, 594, 8, 0, "_t"], [15, 596, 8, 0], [15, 607, 8, 0, "f"], [15, 608, 8, 0], [15, 613, 8, 0, "e"], [15, 614, 8, 0], [15, 616, 8, 0, "t"], [15, 617, 8, 0], [16, 2, 18, 0], [16, 6, 18, 6, "NativeSearchBar"], [16, 21, 21, 32], [16, 24, 22, 2, "SearchBarNativeComponent"], [16, 57, 23, 25], [17, 2, 24, 0], [17, 6, 24, 6, "NativeSearchBarCommands"], [17, 29, 24, 52], [17, 32, 25, 2, "SearchBarNativeCommands"], [17, 66, 25, 50], [18, 2, 38, 0], [18, 11, 38, 9, "SearchBar"], [18, 20, 38, 18, "SearchBar"], [18, 21, 39, 2, "props"], [18, 26, 39, 23], [18, 28, 40, 2, "forwardedRef"], [18, 40, 40, 44], [18, 42, 41, 2], [19, 4, 42, 2], [19, 8, 42, 8, "searchBarRef"], [19, 20, 42, 20], [19, 23, 42, 23, "React"], [19, 37, 42, 28], [19, 38, 42, 29, "useRef"], [19, 44, 42, 35], [19, 45, 42, 62], [19, 49, 42, 66], [19, 50, 42, 67], [20, 4, 44, 2, "React"], [20, 18, 44, 7], [20, 19, 44, 8, "useImperativeHandle"], [20, 38, 44, 27], [20, 39, 44, 28, "forwardedRef"], [20, 51, 44, 40], [20, 53, 44, 42], [20, 60, 44, 49], [21, 6, 45, 4, "blur"], [21, 10, 45, 8], [21, 12, 45, 10, "blur"], [21, 13, 45, 10], [21, 18, 45, 16], [22, 8, 46, 6, "_callMethodWithRef"], [22, 26, 46, 24], [22, 27, 46, 25, "ref"], [22, 30, 46, 28], [22, 34, 46, 32, "NativeSearchBarCommands"], [22, 57, 46, 55], [22, 58, 46, 56, "blur"], [22, 62, 46, 60], [22, 63, 46, 61, "ref"], [22, 66, 46, 64], [22, 67, 46, 65], [22, 68, 46, 66], [23, 6, 47, 4], [23, 7, 47, 5], [24, 6, 48, 4, "focus"], [24, 11, 48, 9], [24, 13, 48, 11, "focus"], [24, 14, 48, 11], [24, 19, 48, 17], [25, 8, 49, 6, "_callMethodWithRef"], [25, 26, 49, 24], [25, 27, 49, 25, "ref"], [25, 30, 49, 28], [25, 34, 49, 32, "NativeSearchBarCommands"], [25, 57, 49, 55], [25, 58, 49, 56, "focus"], [25, 63, 49, 61], [25, 64, 49, 62, "ref"], [25, 67, 49, 65], [25, 68, 49, 66], [25, 69, 49, 67], [26, 6, 50, 4], [26, 7, 50, 5], [27, 6, 51, 4, "toggleCancelButton"], [27, 24, 51, 22], [27, 26, 51, 25, "flag"], [27, 30, 51, 38], [27, 34, 51, 43], [28, 8, 52, 6, "_callMethodWithRef"], [28, 26, 52, 24], [28, 27, 52, 25, "ref"], [28, 30, 52, 28], [28, 34, 53, 8, "NativeSearchBarCommands"], [28, 57, 53, 31], [28, 58, 53, 32, "toggleCancelButton"], [28, 76, 53, 50], [28, 77, 53, 51, "ref"], [28, 80, 53, 54], [28, 82, 53, 56, "flag"], [28, 86, 53, 60], [28, 87, 54, 6], [28, 88, 54, 7], [29, 6, 55, 4], [29, 7, 55, 5], [30, 6, 56, 4, "clearText"], [30, 15, 56, 13], [30, 17, 56, 15, "clearText"], [30, 18, 56, 15], [30, 23, 56, 21], [31, 8, 57, 6, "_callMethodWithRef"], [31, 26, 57, 24], [31, 27, 57, 25, "ref"], [31, 30, 57, 28], [31, 34, 57, 32, "NativeSearchBarCommands"], [31, 57, 57, 55], [31, 58, 57, 56, "clearText"], [31, 67, 57, 65], [31, 68, 57, 66, "ref"], [31, 71, 57, 69], [31, 72, 57, 70], [31, 73, 57, 71], [32, 6, 58, 4], [32, 7, 58, 5], [33, 6, 59, 4, "setText"], [33, 13, 59, 11], [33, 15, 59, 14, "text"], [33, 19, 59, 26], [33, 23, 59, 31], [34, 8, 60, 6, "_callMethodWithRef"], [34, 26, 60, 24], [34, 27, 60, 25, "ref"], [34, 30, 60, 28], [34, 34, 60, 32, "NativeSearchBarCommands"], [34, 57, 60, 55], [34, 58, 60, 56, "setText"], [34, 65, 60, 63], [34, 66, 60, 64, "ref"], [34, 69, 60, 67], [34, 71, 60, 69, "text"], [34, 75, 60, 73], [34, 76, 60, 74], [34, 77, 60, 75], [35, 6, 61, 4], [35, 7, 61, 5], [36, 6, 62, 4, "cancelSearch"], [36, 18, 62, 16], [36, 20, 62, 18, "cancelSearch"], [36, 21, 62, 18], [36, 26, 62, 24], [37, 8, 63, 6, "_callMethodWithRef"], [37, 26, 63, 24], [37, 27, 63, 25, "ref"], [37, 30, 63, 28], [37, 34, 63, 32, "NativeSearchBarCommands"], [37, 57, 63, 55], [37, 58, 63, 56, "cancelSearch"], [37, 70, 63, 68], [37, 71, 63, 69, "ref"], [37, 74, 63, 72], [37, 75, 63, 73], [37, 76, 63, 74], [38, 6, 64, 4], [39, 4, 65, 2], [39, 5, 65, 3], [39, 6, 65, 4], [39, 7, 65, 5], [40, 4, 67, 2], [40, 8, 67, 8, "_callMethodWithRef"], [40, 26, 67, 26], [40, 29, 67, 29, "React"], [40, 43, 67, 34], [40, 44, 67, 35, "useCallback"], [40, 55, 67, 46], [40, 56, 68, 5, "method"], [40, 62, 68, 45], [40, 66, 68, 50], [41, 6, 69, 6], [41, 10, 69, 12, "ref"], [41, 13, 69, 15], [41, 16, 69, 18, "searchBarRef"], [41, 28, 69, 30], [41, 29, 69, 31, "current"], [41, 36, 69, 38], [42, 6, 70, 6], [42, 10, 70, 10, "ref"], [42, 13, 70, 13], [42, 15, 70, 15], [43, 8, 71, 8, "method"], [43, 14, 71, 14], [43, 15, 71, 15, "ref"], [43, 18, 71, 18], [43, 19, 71, 19], [44, 6, 72, 6], [44, 7, 72, 7], [44, 13, 72, 13], [45, 8, 73, 8, "console"], [45, 15, 73, 15], [45, 16, 73, 16, "warn"], [45, 20, 73, 20], [45, 21, 74, 10], [45, 88, 75, 8], [45, 89, 75, 9], [46, 6, 76, 6], [47, 4, 77, 4], [47, 5, 77, 5], [47, 7, 78, 4], [47, 8, 78, 5, "searchBarRef"], [47, 20, 78, 17], [47, 21, 79, 2], [47, 22, 79, 3], [48, 4, 81, 2], [48, 8, 81, 6], [48, 9, 81, 7, "isSearchBarAvailableForCurrentPlatform"], [48, 54, 81, 45], [48, 56, 81, 47], [49, 6, 82, 4, "console"], [49, 13, 82, 11], [49, 14, 82, 12, "warn"], [49, 18, 82, 16], [49, 19, 83, 6], [49, 82, 84, 4], [49, 83, 84, 5], [50, 6, 85, 4], [50, 13, 85, 11, "View"], [50, 30, 85, 15], [51, 4, 86, 2], [52, 4, 88, 2], [52, 11, 89, 4], [52, 15, 89, 4, "_jsxRuntime"], [52, 26, 89, 4], [52, 27, 89, 4, "jsx"], [52, 30, 89, 4], [52, 32, 89, 5, "NativeSearchBar"], [52, 47, 89, 20], [53, 6, 90, 6, "ref"], [53, 9, 90, 9], [53, 11, 90, 11, "searchBarRef"], [53, 23, 90, 24], [54, 6, 90, 24], [54, 9, 91, 10, "props"], [54, 14, 91, 15], [55, 6, 92, 6, "onSearchFocus"], [55, 19, 92, 19], [55, 21, 92, 21, "props"], [55, 26, 92, 26], [55, 27, 92, 27, "onFocus"], [55, 34, 92, 73], [56, 6, 93, 6, "onSearchBlur"], [56, 18, 93, 18], [56, 20, 93, 20, "props"], [56, 25, 93, 25], [56, 26, 93, 26, "onBlur"], [56, 32, 93, 71], [57, 6, 94, 6, "onSearchButtonPress"], [57, 25, 94, 25], [57, 27, 95, 8, "props"], [57, 32, 95, 13], [57, 33, 95, 14, "onSearchButtonPress"], [57, 52, 96, 7], [58, 6, 97, 6, "onCancelButtonPress"], [58, 25, 97, 25], [58, 27, 98, 8, "props"], [58, 32, 98, 13], [58, 33, 98, 14, "onCancelButtonPress"], [58, 52, 99, 7], [59, 6, 100, 6, "onChangeText"], [59, 18, 100, 18], [59, 20, 100, 20, "props"], [59, 25, 100, 25], [59, 26, 100, 26, "onChangeText"], [60, 4, 100, 78], [60, 5, 101, 5], [60, 6, 101, 6], [61, 2, 103, 0], [62, 2, 103, 1], [62, 6, 103, 1, "_default"], [62, 14, 103, 1], [62, 17, 103, 1, "exports"], [62, 24, 103, 1], [62, 25, 103, 1, "default"], [62, 32, 103, 1], [62, 48, 105, 15, "React"], [62, 62, 105, 20], [62, 63, 105, 21, "forwardRef"], [62, 73, 105, 31], [62, 74, 105, 67, "SearchBar"], [62, 83, 105, 76], [62, 84, 105, 77], [63, 0, 105, 77], [63, 3]], "functionMap": {"names": ["<global>", "SearchBar", "React.useImperativeHandle$argument_1", "blur", "_callMethodWithRef$argument_0", "focus", "toggleCancelButton", "clearText", "setText", "cancelSearch", "_callMethodWithRef"], "mappings": "AAA;ACqC;0CCM;UCC;yBCC,wCD;KDC;WGC;yBDC,yCC;KHC;wBIC;yBFC;6DEC;KJE;eKC;yBHC,6CG;KLC;aMC;yBJC,iDI;KNC;kBOC;yBLC,gDK;KPC;IDC;ISG;KTS;CD0B"}}, "type": "js/module"}]}