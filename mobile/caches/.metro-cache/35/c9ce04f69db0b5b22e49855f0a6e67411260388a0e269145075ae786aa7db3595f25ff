{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 30}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var React = require(_dependencyMap[0], \"react\");\n  var TextAncestorContext = React.createContext(false);\n  if (__DEV__) {\n    TextAncestorContext.displayName = 'TextAncestorContext';\n  }\n  var _default = exports.default = TextAncestorContext;\n});", "lineCount": 14, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 13, 0], [8, 6, 13, 6, "React"], [8, 11, 13, 11], [8, 14, 13, 14, "require"], [8, 21, 13, 21], [8, 22, 13, 21, "_dependencyMap"], [8, 36, 13, 21], [8, 48, 13, 29], [8, 49, 13, 30], [9, 2, 18, 0], [9, 6, 18, 6, "TextAncestorContext"], [9, 25, 18, 49], [9, 28, 18, 52, "React"], [9, 33, 18, 57], [9, 34, 18, 58, "createContext"], [9, 47, 18, 71], [9, 48, 18, 72], [9, 53, 18, 77], [9, 54, 18, 78], [10, 2, 19, 0], [10, 6, 19, 4, "__DEV__"], [10, 13, 19, 11], [10, 15, 19, 13], [11, 4, 20, 2, "TextAncestorContext"], [11, 23, 20, 21], [11, 24, 20, 22, "displayName"], [11, 35, 20, 33], [11, 38, 20, 36], [11, 59, 20, 57], [12, 2, 21, 0], [13, 2, 21, 1], [13, 6, 21, 1, "_default"], [13, 14, 21, 1], [13, 17, 21, 1, "exports"], [13, 24, 21, 1], [13, 25, 21, 1, "default"], [13, 32, 21, 1], [13, 35, 22, 15, "TextAncestorContext"], [13, 54, 22, 34], [14, 0, 22, 34], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}