{"dependencies": [{"name": "./toaster.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 39, "index": 54}}], "key": "T2WmRtyvYiai9mUWA+A4OvdsaCE=", "exportNames": ["*"]}}, {"name": "./toast-fns.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 55}, "end": {"line": 4, "column": 39, "index": 94}}], "key": "ujE3OZVMBrdhdzormRIcgm71VM0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"Toaster\", {\n    enumerable: true,\n    get: function () {\n      return _toaster.Toaster;\n    }\n  });\n  Object.defineProperty(exports, \"toast\", {\n    enumerable: true,\n    get: function () {\n      return _toastFns.toast;\n    }\n  });\n  var _toaster = require(_dependencyMap[0], \"./toaster.js\");\n  var _toastFns = require(_dependencyMap[1], \"./toast-fns.js\");\n});", "lineCount": 21, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_toaster"], [10, 21, 1, 13], [10, 22, 1, 13, "Toaster"], [10, 29, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 1, 13, "Object"], [13, 8, 1, 13], [13, 9, 1, 13, "defineProperty"], [13, 23, 1, 13], [13, 24, 1, 13, "exports"], [13, 31, 1, 13], [14, 4, 1, 13, "enumerable"], [14, 14, 1, 13], [15, 4, 1, 13, "get"], [15, 7, 1, 13], [15, 18, 1, 13, "get"], [15, 19, 1, 13], [16, 6, 1, 13], [16, 13, 1, 13, "_toastFns"], [16, 22, 1, 13], [16, 23, 1, 13, "toast"], [16, 28, 1, 13], [17, 4, 1, 13], [18, 2, 1, 13], [19, 2, 3, 0], [19, 6, 3, 0, "_toaster"], [19, 14, 3, 0], [19, 17, 3, 0, "require"], [19, 24, 3, 0], [19, 25, 3, 0, "_dependencyMap"], [19, 39, 3, 0], [20, 2, 4, 0], [20, 6, 4, 0, "_toastFns"], [20, 15, 4, 0], [20, 18, 4, 0, "require"], [20, 25, 4, 0], [20, 26, 4, 0, "_dependencyMap"], [20, 40, 4, 0], [21, 0, 4, 39], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}