{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./hyphenateProperty", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 52, "index": 52}}], "key": "H8O71MoJ0AWjZFTARiieipg/pYQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = resolveArrayValue;\n  var _hyphenateProperty = _interopRequireDefault(require(_dependencyMap[1], \"./hyphenateProperty\"));\n  function resolveArrayValue(property, value) {\n    return value.join(';' + (0, _hyphenateProperty.default)(property) + ':');\n  }\n});", "lineCount": 11, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_hyphenateProperty"], [7, 24, 1, 0], [7, 27, 1, 0, "_interopRequireDefault"], [7, 49, 1, 0], [7, 50, 1, 0, "require"], [7, 57, 1, 0], [7, 58, 1, 0, "_dependencyMap"], [7, 72, 1, 0], [8, 2, 2, 15], [8, 11, 2, 24, "resolveArrayValue"], [8, 28, 2, 41, "resolveArrayValue"], [8, 29, 2, 42, "property"], [8, 37, 2, 50], [8, 39, 2, 52, "value"], [8, 44, 2, 57], [8, 46, 2, 59], [9, 4, 3, 2], [9, 11, 3, 9, "value"], [9, 16, 3, 14], [9, 17, 3, 15, "join"], [9, 21, 3, 19], [9, 22, 3, 20], [9, 25, 3, 23], [9, 28, 3, 26], [9, 32, 3, 26, "hyphenateProperty"], [9, 58, 3, 43], [9, 60, 3, 44, "property"], [9, 68, 3, 52], [9, 69, 3, 53], [9, 72, 3, 56], [9, 75, 3, 59], [9, 76, 3, 60], [10, 2, 4, 0], [11, 0, 4, 1], [11, 3]], "functionMap": {"names": ["<global>", "resolveArrayValue"], "mappings": "AAA;eCC"}}, "type": "js/module"}]}