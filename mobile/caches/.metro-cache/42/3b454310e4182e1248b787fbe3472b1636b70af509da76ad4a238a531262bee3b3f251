{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /* eslint-disable @typescript-eslint/no-explicit-any */\n\n  // TODO: Clean this up since 0.74 is the minimum supported version now.\n  // This is a makeshift solution to handle both 0.73 and 0.74 versions of React Native.\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getViewInfo = void 0;\n  var getViewInfo = element => {\n    if (element._nativeTag !== undefined && element.__nativeTag !== null) {\n      exports.getViewInfo = getViewInfo = getViewInfo73;\n      return getViewInfo73(element);\n    } else if (element.__nativeTag !== undefined && element.__nativeTag !== null) {\n      exports.getViewInfo = getViewInfo = getViewInfoLatest;\n      return getViewInfoLatest(element);\n    }\n    return getViewInfo73(element);\n  };\n  exports.getViewInfo = getViewInfo;\n  function getViewInfo73(element) {\n    return {\n      // we can access view tag in the same way it's accessed here https://github.com/facebook/react/blob/e3f4eb7272d4ca0ee49f27577156b57eeb07cf73/packages/react-native-renderer/src/ReactFabric.js#L146\n      viewName: element?.viewConfig?.uiViewClassName,\n      /**\n       * RN uses viewConfig for components for storing different properties of the\n       * component(example:\n       * https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/Components/ScrollView/ScrollViewNativeComponent.js#L24).\n       * The name we're looking for is in the field named uiViewClassName.\n       */\n      viewTag: element?._nativeTag,\n      viewConfig: element?.viewConfig\n    };\n  }\n  function getViewInfoLatest(element) {\n    return {\n      viewName: element?._viewConfig?.uiViewClassName,\n      viewTag: element?.__nativeTag,\n      viewConfig: element?._viewConfig\n    };\n  }\n});", "lineCount": 44, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [6, 2, 4, 0], [7, 2, 5, 0], [8, 2, 5, 0, "Object"], [8, 8, 5, 0], [8, 9, 5, 0, "defineProperty"], [8, 23, 5, 0], [8, 24, 5, 0, "exports"], [8, 31, 5, 0], [9, 4, 5, 0, "value"], [9, 9, 5, 0], [10, 2, 5, 0], [11, 2, 5, 0, "exports"], [11, 9, 5, 0], [11, 10, 5, 0, "getViewInfo"], [11, 21, 5, 0], [12, 2, 7, 7], [12, 6, 7, 11, "getViewInfo"], [12, 17, 7, 22], [12, 20, 7, 26, "element"], [12, 27, 7, 38], [12, 31, 7, 43], [13, 4, 8, 2], [13, 8, 8, 6, "element"], [13, 15, 8, 13], [13, 16, 8, 14, "_nativeTag"], [13, 26, 8, 24], [13, 31, 8, 29, "undefined"], [13, 40, 8, 38], [13, 44, 8, 42, "element"], [13, 51, 8, 49], [13, 52, 8, 50, "__nativeTag"], [13, 63, 8, 61], [13, 68, 8, 66], [13, 72, 8, 70], [13, 74, 8, 72], [14, 6, 9, 4, "exports"], [14, 13, 9, 4], [14, 14, 9, 4, "getViewInfo"], [14, 25, 9, 4], [14, 28, 9, 4, "getViewInfo"], [14, 39, 9, 15], [14, 42, 9, 18, "getViewInfo73"], [14, 55, 9, 31], [15, 6, 10, 4], [15, 13, 10, 11, "getViewInfo73"], [15, 26, 10, 24], [15, 27, 10, 25, "element"], [15, 34, 10, 32], [15, 35, 10, 33], [16, 4, 11, 2], [16, 5, 11, 3], [16, 11, 11, 9], [16, 15, 12, 4, "element"], [16, 22, 12, 11], [16, 23, 12, 12, "__nativeTag"], [16, 34, 12, 23], [16, 39, 12, 28, "undefined"], [16, 48, 12, 37], [16, 52, 13, 4, "element"], [16, 59, 13, 11], [16, 60, 13, 12, "__nativeTag"], [16, 71, 13, 23], [16, 76, 13, 28], [16, 80, 13, 32], [16, 82, 14, 4], [17, 6, 15, 4, "exports"], [17, 13, 15, 4], [17, 14, 15, 4, "getViewInfo"], [17, 25, 15, 4], [17, 28, 15, 4, "getViewInfo"], [17, 39, 15, 15], [17, 42, 15, 18, "getViewInfoLatest"], [17, 59, 15, 35], [18, 6, 16, 4], [18, 13, 16, 11, "getViewInfoLatest"], [18, 30, 16, 28], [18, 31, 16, 29, "element"], [18, 38, 16, 36], [18, 39, 16, 37], [19, 4, 17, 2], [20, 4, 18, 2], [20, 11, 18, 9, "getViewInfo73"], [20, 24, 18, 22], [20, 25, 18, 23, "element"], [20, 32, 18, 30], [20, 33, 18, 31], [21, 2, 19, 0], [21, 3, 19, 1], [22, 2, 19, 2, "exports"], [22, 9, 19, 2], [22, 10, 19, 2, "getViewInfo"], [22, 21, 19, 2], [22, 24, 19, 2, "getViewInfo"], [22, 35, 19, 2], [23, 2, 21, 0], [23, 11, 21, 9, "getViewInfo73"], [23, 24, 21, 22, "getViewInfo73"], [23, 25, 21, 23, "element"], [23, 32, 21, 35], [23, 34, 21, 37], [24, 4, 22, 2], [24, 11, 22, 9], [25, 6, 23, 4], [26, 6, 24, 4, "viewName"], [26, 14, 24, 12], [26, 16, 24, 14, "element"], [26, 23, 24, 21], [26, 25, 24, 23, "viewConfig"], [26, 35, 24, 33], [26, 37, 24, 35, "uiViewClassName"], [26, 52, 24, 50], [27, 6, 25, 4], [28, 0, 26, 0], [29, 0, 27, 0], [30, 0, 28, 0], [31, 0, 29, 0], [32, 0, 30, 0], [33, 6, 31, 4, "viewTag"], [33, 13, 31, 11], [33, 15, 31, 13, "element"], [33, 22, 31, 20], [33, 24, 31, 22, "_nativeTag"], [33, 34, 31, 32], [34, 6, 32, 4, "viewConfig"], [34, 16, 32, 14], [34, 18, 32, 16, "element"], [34, 25, 32, 23], [34, 27, 32, 25, "viewConfig"], [35, 4, 33, 2], [35, 5, 33, 3], [36, 2, 34, 0], [37, 2, 36, 0], [37, 11, 36, 9, "getViewInfoLatest"], [37, 28, 36, 26, "getViewInfoLatest"], [37, 29, 36, 27, "element"], [37, 36, 36, 39], [37, 38, 36, 41], [38, 4, 37, 2], [38, 11, 37, 9], [39, 6, 38, 4, "viewName"], [39, 14, 38, 12], [39, 16, 38, 14, "element"], [39, 23, 38, 21], [39, 25, 38, 23, "_viewConfig"], [39, 36, 38, 34], [39, 38, 38, 36, "uiViewClassName"], [39, 53, 38, 51], [40, 6, 39, 4, "viewTag"], [40, 13, 39, 11], [40, 15, 39, 13, "element"], [40, 22, 39, 20], [40, 24, 39, 22, "__nativeTag"], [40, 35, 39, 33], [41, 6, 40, 4, "viewConfig"], [41, 16, 40, 14], [41, 18, 40, 16, "element"], [41, 25, 40, 23], [41, 27, 40, 25, "_viewConfig"], [42, 4, 41, 2], [42, 5, 41, 3], [43, 2, 42, 0], [44, 0, 42, 1], [44, 3]], "functionMap": {"names": ["<global>", "getViewInfo", "getViewInfo73", "getViewInfoLatest"], "mappings": "AAA;yBCM;CDY;AEE;CFa;AGE;CHM"}}, "type": "js/module"}]}