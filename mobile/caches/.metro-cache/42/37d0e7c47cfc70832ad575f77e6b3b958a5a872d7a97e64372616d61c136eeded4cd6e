{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "buffer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 41}, "end": {"line": 2, "column": 32, "index": 73}}], "key": "L2R9OUI0/cSYwzijo34ce4VujKY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.fetchText = fetchText;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/Platform\"));\n  var _buffer = require(_dependencyMap[2], \"buffer\");\n  async function fetchText(uri) {\n    if (!uri) {\n      return null;\n    }\n    if (uri.startsWith('data:image/svg+xml;utf8') && _Platform.default.OS === 'android') {\n      return dataUriToXml(uri);\n    } else if (uri.startsWith('data:image/svg+xml;base64')) {\n      return decodeBase64Image(uri);\n    } else {\n      return fetchUriData(uri);\n    }\n  }\n  const decodeBase64Image = uri => {\n    const decoded = decodeURIComponent(uri);\n    const splitContent = decoded.split(';')[1].split(',');\n    const dataType = splitContent[0];\n    const content = splitContent.slice(1).join(',');\n    return _buffer.Buffer.from(content, dataType).toString('utf-8');\n  };\n  function dataUriToXml(uri) {\n    try {\n      // decode and remove data:image/svg+xml;utf8, prefix\n      return decodeURIComponent(uri).split(',').slice(1).join(',');\n    } catch (error) {\n      throw new Error(`Decoding ${uri} failed with error: ${error}`);\n    }\n  }\n  async function fetchUriData(uri) {\n    const response = await fetch(uri);\n    if (response.ok || response.status === 0 && uri.startsWith('file://')) {\n      return await response.text();\n    }\n    throw new Error(`Fetching ${uri} failed with status ${response.status}`);\n  }\n});", "lineCount": 43, "map": [[8, 2, 2, 0], [8, 6, 2, 0, "_buffer"], [8, 13, 2, 0], [8, 16, 2, 0, "require"], [8, 23, 2, 0], [8, 24, 2, 0, "_dependencyMap"], [8, 38, 2, 0], [9, 2, 3, 7], [9, 17, 3, 22, "fetchText"], [9, 26, 3, 31, "fetchText"], [9, 27, 3, 32, "uri"], [9, 30, 3, 35], [9, 32, 3, 37], [10, 4, 4, 2], [10, 8, 4, 6], [10, 9, 4, 7, "uri"], [10, 12, 4, 10], [10, 14, 4, 12], [11, 6, 5, 4], [11, 13, 5, 11], [11, 17, 5, 15], [12, 4, 6, 2], [13, 4, 7, 2], [13, 8, 7, 6, "uri"], [13, 11, 7, 9], [13, 12, 7, 10, "startsWith"], [13, 22, 7, 20], [13, 23, 7, 21], [13, 48, 7, 46], [13, 49, 7, 47], [13, 53, 7, 51, "Platform"], [13, 70, 7, 59], [13, 71, 7, 60, "OS"], [13, 73, 7, 62], [13, 78, 7, 67], [13, 87, 7, 76], [13, 89, 7, 78], [14, 6, 8, 4], [14, 13, 8, 11, "dataUriToXml"], [14, 25, 8, 23], [14, 26, 8, 24, "uri"], [14, 29, 8, 27], [14, 30, 8, 28], [15, 4, 9, 2], [15, 5, 9, 3], [15, 11, 9, 9], [15, 15, 9, 13, "uri"], [15, 18, 9, 16], [15, 19, 9, 17, "startsWith"], [15, 29, 9, 27], [15, 30, 9, 28], [15, 57, 9, 55], [15, 58, 9, 56], [15, 60, 9, 58], [16, 6, 10, 4], [16, 13, 10, 11, "decodeBase64Image"], [16, 30, 10, 28], [16, 31, 10, 29, "uri"], [16, 34, 10, 32], [16, 35, 10, 33], [17, 4, 11, 2], [17, 5, 11, 3], [17, 11, 11, 9], [18, 6, 12, 4], [18, 13, 12, 11, "fetchUriData"], [18, 25, 12, 23], [18, 26, 12, 24, "uri"], [18, 29, 12, 27], [18, 30, 12, 28], [19, 4, 13, 2], [20, 2, 14, 0], [21, 2, 15, 0], [21, 8, 15, 6, "decodeBase64Image"], [21, 25, 15, 23], [21, 28, 15, 26, "uri"], [21, 31, 15, 29], [21, 35, 15, 33], [22, 4, 16, 2], [22, 10, 16, 8, "decoded"], [22, 17, 16, 15], [22, 20, 16, 18, "decodeURIComponent"], [22, 38, 16, 36], [22, 39, 16, 37, "uri"], [22, 42, 16, 40], [22, 43, 16, 41], [23, 4, 17, 2], [23, 10, 17, 8, "splitContent"], [23, 22, 17, 20], [23, 25, 17, 23, "decoded"], [23, 32, 17, 30], [23, 33, 17, 31, "split"], [23, 38, 17, 36], [23, 39, 17, 37], [23, 42, 17, 40], [23, 43, 17, 41], [23, 44, 17, 42], [23, 45, 17, 43], [23, 46, 17, 44], [23, 47, 17, 45, "split"], [23, 52, 17, 50], [23, 53, 17, 51], [23, 56, 17, 54], [23, 57, 17, 55], [24, 4, 18, 2], [24, 10, 18, 8, "dataType"], [24, 18, 18, 16], [24, 21, 18, 19, "splitContent"], [24, 33, 18, 31], [24, 34, 18, 32], [24, 35, 18, 33], [24, 36, 18, 34], [25, 4, 19, 2], [25, 10, 19, 8, "content"], [25, 17, 19, 15], [25, 20, 19, 18, "splitContent"], [25, 32, 19, 30], [25, 33, 19, 31, "slice"], [25, 38, 19, 36], [25, 39, 19, 37], [25, 40, 19, 38], [25, 41, 19, 39], [25, 42, 19, 40, "join"], [25, 46, 19, 44], [25, 47, 19, 45], [25, 50, 19, 48], [25, 51, 19, 49], [26, 4, 20, 2], [26, 11, 20, 9, "<PERSON><PERSON><PERSON>"], [26, 25, 20, 15], [26, 26, 20, 16, "from"], [26, 30, 20, 20], [26, 31, 20, 21, "content"], [26, 38, 20, 28], [26, 40, 20, 30, "dataType"], [26, 48, 20, 38], [26, 49, 20, 39], [26, 50, 20, 40, "toString"], [26, 58, 20, 48], [26, 59, 20, 49], [26, 66, 20, 56], [26, 67, 20, 57], [27, 2, 21, 0], [27, 3, 21, 1], [28, 2, 22, 0], [28, 11, 22, 9, "dataUriToXml"], [28, 23, 22, 21, "dataUriToXml"], [28, 24, 22, 22, "uri"], [28, 27, 22, 25], [28, 29, 22, 27], [29, 4, 23, 2], [29, 8, 23, 6], [30, 6, 24, 4], [31, 6, 25, 4], [31, 13, 25, 11, "decodeURIComponent"], [31, 31, 25, 29], [31, 32, 25, 30, "uri"], [31, 35, 25, 33], [31, 36, 25, 34], [31, 37, 25, 35, "split"], [31, 42, 25, 40], [31, 43, 25, 41], [31, 46, 25, 44], [31, 47, 25, 45], [31, 48, 25, 46, "slice"], [31, 53, 25, 51], [31, 54, 25, 52], [31, 55, 25, 53], [31, 56, 25, 54], [31, 57, 25, 55, "join"], [31, 61, 25, 59], [31, 62, 25, 60], [31, 65, 25, 63], [31, 66, 25, 64], [32, 4, 26, 2], [32, 5, 26, 3], [32, 6, 26, 4], [32, 13, 26, 11, "error"], [32, 18, 26, 16], [32, 20, 26, 18], [33, 6, 27, 4], [33, 12, 27, 10], [33, 16, 27, 14, "Error"], [33, 21, 27, 19], [33, 22, 27, 20], [33, 34, 27, 32, "uri"], [33, 37, 27, 35], [33, 60, 27, 58, "error"], [33, 65, 27, 63], [33, 67, 27, 65], [33, 68, 27, 66], [34, 4, 28, 2], [35, 2, 29, 0], [36, 2, 30, 0], [36, 17, 30, 15, "fetchUriData"], [36, 29, 30, 27, "fetchUriData"], [36, 30, 30, 28, "uri"], [36, 33, 30, 31], [36, 35, 30, 33], [37, 4, 31, 2], [37, 10, 31, 8, "response"], [37, 18, 31, 16], [37, 21, 31, 19], [37, 27, 31, 25, "fetch"], [37, 32, 31, 30], [37, 33, 31, 31, "uri"], [37, 36, 31, 34], [37, 37, 31, 35], [38, 4, 32, 2], [38, 8, 32, 6, "response"], [38, 16, 32, 14], [38, 17, 32, 15, "ok"], [38, 19, 32, 17], [38, 23, 32, 21, "response"], [38, 31, 32, 29], [38, 32, 32, 30, "status"], [38, 38, 32, 36], [38, 43, 32, 41], [38, 44, 32, 42], [38, 48, 32, 46, "uri"], [38, 51, 32, 49], [38, 52, 32, 50, "startsWith"], [38, 62, 32, 60], [38, 63, 32, 61], [38, 72, 32, 70], [38, 73, 32, 71], [38, 75, 32, 73], [39, 6, 33, 4], [39, 13, 33, 11], [39, 19, 33, 17, "response"], [39, 27, 33, 25], [39, 28, 33, 26, "text"], [39, 32, 33, 30], [39, 33, 33, 31], [39, 34, 33, 32], [40, 4, 34, 2], [41, 4, 35, 2], [41, 10, 35, 8], [41, 14, 35, 12, "Error"], [41, 19, 35, 17], [41, 20, 35, 18], [41, 32, 35, 30, "uri"], [41, 35, 35, 33], [41, 58, 35, 56, "response"], [41, 66, 35, 64], [41, 67, 35, 65, "status"], [41, 73, 35, 71], [41, 75, 35, 73], [41, 76, 35, 74], [42, 2, 36, 0], [43, 0, 36, 1], [43, 3]], "functionMap": {"names": ["<global>", "fetchText", "decodeBase64Image", "dataUriToXml", "fetchUriData"], "mappings": "AAA;OCE;CDW;0BEC;CFM;AGC;CHO;AIC;CJM"}}, "type": "js/module"}]}