{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var logListeners;\n  function unstable_setLogListeners(listeners) {\n    logListeners = listeners;\n  }\n  function deepDiffer(one, two) {\n    var maxDepthOrOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : -1;\n    var maybeOptions = arguments.length > 3 ? arguments[3] : undefined;\n    var options = typeof maxDepthOrOptions === 'number' ? maybeOptions : maxDepthOrOptions;\n    var maxDepth = typeof maxDepthOrOptions === 'number' ? maxDepthOrOptions : -1;\n    if (maxDepth === 0) {\n      return true;\n    }\n    if (one === two) {\n      return false;\n    }\n    if (typeof one === 'function' && typeof two === 'function') {\n      var unsafelyIgnoreFunctions = options?.unsafelyIgnoreFunctions;\n      if (unsafelyIgnoreFunctions == null) {\n        if (logListeners && logListeners.onDifferentFunctionsIgnored && (!options || !('unsafelyIgnoreFunctions' in options))) {\n          logListeners.onDifferentFunctionsIgnored(one.name, two.name);\n        }\n        unsafelyIgnoreFunctions = true;\n      }\n      return !unsafelyIgnoreFunctions;\n    }\n    if (typeof one !== 'object' || one === null) {\n      return one !== two;\n    }\n    if (typeof two !== 'object' || two === null) {\n      return true;\n    }\n    if (one.constructor !== two.constructor) {\n      return true;\n    }\n    if (Array.isArray(one)) {\n      var len = one.length;\n      if (two.length !== len) {\n        return true;\n      }\n      for (var ii = 0; ii < len; ii++) {\n        if (deepDiffer(one[ii], two[ii], maxDepth - 1, options)) {\n          return true;\n        }\n      }\n    } else {\n      for (var key in one) {\n        if (deepDiffer(one[key], two[key], maxDepth - 1, options)) {\n          return true;\n        }\n      }\n      for (var twoKey in two) {\n        if (one[twoKey] === undefined && two[twoKey] !== undefined) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  deepDiffer.unstable_setLogListeners = unstable_setLogListeners;\n  var _default = exports.default = deepDiffer;\n});", "lineCount": 68, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 13, 0], [8, 6, 13, 4, "logListeners"], [8, 18, 13, 16], [9, 2, 21, 0], [9, 11, 21, 9, "unstable_setLogListeners"], [9, 35, 21, 33, "unstable_setLogListeners"], [9, 36, 21, 34, "listeners"], [9, 45, 21, 58], [9, 47, 21, 60], [10, 4, 22, 2, "logListeners"], [10, 16, 22, 14], [10, 19, 22, 17, "listeners"], [10, 28, 22, 26], [11, 2, 23, 0], [12, 2, 28, 0], [12, 11, 28, 9, "<PERSON><PERSON><PERSON><PERSON>"], [12, 21, 28, 19, "<PERSON><PERSON><PERSON><PERSON>"], [12, 22, 29, 2, "one"], [12, 25, 29, 10], [12, 27, 30, 2, "two"], [12, 30, 30, 10], [12, 32, 33, 11], [13, 4, 33, 11], [13, 8, 31, 2, "maxDepthOrOptions"], [13, 25, 31, 37], [13, 28, 31, 37, "arguments"], [13, 37, 31, 37], [13, 38, 31, 37, "length"], [13, 44, 31, 37], [13, 52, 31, 37, "arguments"], [13, 61, 31, 37], [13, 69, 31, 37, "undefined"], [13, 78, 31, 37], [13, 81, 31, 37, "arguments"], [13, 90, 31, 37], [13, 96, 31, 40], [13, 97, 31, 41], [13, 98, 31, 42], [14, 4, 31, 42], [14, 8, 32, 2, "maybeOptions"], [14, 20, 32, 24], [14, 23, 32, 24, "arguments"], [14, 32, 32, 24], [14, 33, 32, 24, "length"], [14, 39, 32, 24], [14, 46, 32, 24, "arguments"], [14, 55, 32, 24], [14, 61, 32, 24, "undefined"], [14, 70, 32, 24], [15, 4, 34, 2], [15, 8, 34, 8, "options"], [15, 15, 34, 15], [15, 18, 35, 4], [15, 25, 35, 11, "maxDepthOrOptions"], [15, 42, 35, 28], [15, 47, 35, 33], [15, 55, 35, 41], [15, 58, 35, 44, "maybeOptions"], [15, 70, 35, 56], [15, 73, 35, 59, "maxDepthOrOptions"], [15, 90, 35, 76], [16, 4, 36, 2], [16, 8, 36, 8, "max<PERSON><PERSON><PERSON>"], [16, 16, 36, 16], [16, 19, 37, 4], [16, 26, 37, 11, "maxDepthOrOptions"], [16, 43, 37, 28], [16, 48, 37, 33], [16, 56, 37, 41], [16, 59, 37, 44, "maxDepthOrOptions"], [16, 76, 37, 61], [16, 79, 37, 64], [16, 80, 37, 65], [16, 81, 37, 66], [17, 4, 38, 2], [17, 8, 38, 6, "max<PERSON><PERSON><PERSON>"], [17, 16, 38, 14], [17, 21, 38, 19], [17, 22, 38, 20], [17, 24, 38, 22], [18, 6, 39, 4], [18, 13, 39, 11], [18, 17, 39, 15], [19, 4, 40, 2], [20, 4, 41, 2], [20, 8, 41, 6, "one"], [20, 11, 41, 9], [20, 16, 41, 14, "two"], [20, 19, 41, 17], [20, 21, 41, 19], [21, 6, 43, 4], [21, 13, 43, 11], [21, 18, 43, 16], [22, 4, 44, 2], [23, 4, 45, 2], [23, 8, 45, 6], [23, 15, 45, 13, "one"], [23, 18, 45, 16], [23, 23, 45, 21], [23, 33, 45, 31], [23, 37, 45, 35], [23, 44, 45, 42, "two"], [23, 47, 45, 45], [23, 52, 45, 50], [23, 62, 45, 60], [23, 64, 45, 62], [24, 6, 47, 4], [24, 10, 47, 8, "unsafelyIgnoreFunctions"], [24, 33, 47, 31], [24, 36, 47, 34, "options"], [24, 43, 47, 41], [24, 45, 47, 43, "unsafelyIgnoreFunctions"], [24, 68, 47, 66], [25, 6, 48, 4], [25, 10, 48, 8, "unsafelyIgnoreFunctions"], [25, 33, 48, 31], [25, 37, 48, 35], [25, 41, 48, 39], [25, 43, 48, 41], [26, 8, 49, 6], [26, 12, 50, 8, "logListeners"], [26, 24, 50, 20], [26, 28, 51, 8, "logListeners"], [26, 40, 51, 20], [26, 41, 51, 21, "onDifferentFunctionsIgnored"], [26, 68, 51, 48], [26, 73, 52, 9], [26, 74, 52, 10, "options"], [26, 81, 52, 17], [26, 85, 52, 21], [26, 87, 52, 23], [26, 112, 52, 48], [26, 116, 52, 52, "options"], [26, 123, 52, 59], [26, 124, 52, 60], [26, 125, 52, 61], [26, 127, 53, 8], [27, 10, 54, 8, "logListeners"], [27, 22, 54, 20], [27, 23, 54, 21, "onDifferentFunctionsIgnored"], [27, 50, 54, 48], [27, 51, 54, 49, "one"], [27, 54, 54, 52], [27, 55, 54, 53, "name"], [27, 59, 54, 57], [27, 61, 54, 59, "two"], [27, 64, 54, 62], [27, 65, 54, 63, "name"], [27, 69, 54, 67], [27, 70, 54, 68], [28, 8, 55, 6], [29, 8, 56, 6, "unsafelyIgnoreFunctions"], [29, 31, 56, 29], [29, 34, 56, 32], [29, 38, 56, 36], [30, 6, 57, 4], [31, 6, 58, 4], [31, 13, 58, 11], [31, 14, 58, 12, "unsafelyIgnoreFunctions"], [31, 37, 58, 35], [32, 4, 59, 2], [33, 4, 60, 2], [33, 8, 60, 6], [33, 15, 60, 13, "one"], [33, 18, 60, 16], [33, 23, 60, 21], [33, 31, 60, 29], [33, 35, 60, 33, "one"], [33, 38, 60, 36], [33, 43, 60, 41], [33, 47, 60, 45], [33, 49, 60, 47], [34, 6, 62, 4], [34, 13, 62, 11, "one"], [34, 16, 62, 14], [34, 21, 62, 19, "two"], [34, 24, 62, 22], [35, 4, 63, 2], [36, 4, 64, 2], [36, 8, 64, 6], [36, 15, 64, 13, "two"], [36, 18, 64, 16], [36, 23, 64, 21], [36, 31, 64, 29], [36, 35, 64, 33, "two"], [36, 38, 64, 36], [36, 43, 64, 41], [36, 47, 64, 45], [36, 49, 64, 47], [37, 6, 67, 4], [37, 13, 67, 11], [37, 17, 67, 15], [38, 4, 68, 2], [39, 4, 69, 2], [39, 8, 69, 6, "one"], [39, 11, 69, 9], [39, 12, 69, 10, "constructor"], [39, 23, 69, 21], [39, 28, 69, 26, "two"], [39, 31, 69, 29], [39, 32, 69, 30, "constructor"], [39, 43, 69, 41], [39, 45, 69, 43], [40, 6, 70, 4], [40, 13, 70, 11], [40, 17, 70, 15], [41, 4, 71, 2], [42, 4, 72, 2], [42, 8, 72, 6, "Array"], [42, 13, 72, 11], [42, 14, 72, 12, "isArray"], [42, 21, 72, 19], [42, 22, 72, 20, "one"], [42, 25, 72, 23], [42, 26, 72, 24], [42, 28, 72, 26], [43, 6, 74, 4], [43, 10, 74, 10, "len"], [43, 13, 74, 13], [43, 16, 74, 16, "one"], [43, 19, 74, 19], [43, 20, 74, 20, "length"], [43, 26, 74, 26], [44, 6, 75, 4], [44, 10, 75, 8, "two"], [44, 13, 75, 11], [44, 14, 75, 12, "length"], [44, 20, 75, 18], [44, 25, 75, 23, "len"], [44, 28, 75, 26], [44, 30, 75, 28], [45, 8, 76, 6], [45, 15, 76, 13], [45, 19, 76, 17], [46, 6, 77, 4], [47, 6, 78, 4], [47, 11, 78, 9], [47, 15, 78, 13, "ii"], [47, 17, 78, 15], [47, 20, 78, 18], [47, 21, 78, 19], [47, 23, 78, 21, "ii"], [47, 25, 78, 23], [47, 28, 78, 26, "len"], [47, 31, 78, 29], [47, 33, 78, 31, "ii"], [47, 35, 78, 33], [47, 37, 78, 35], [47, 39, 78, 37], [48, 8, 79, 6], [48, 12, 79, 10, "<PERSON><PERSON><PERSON><PERSON>"], [48, 22, 79, 20], [48, 23, 79, 21, "one"], [48, 26, 79, 24], [48, 27, 79, 25, "ii"], [48, 29, 79, 27], [48, 30, 79, 28], [48, 32, 79, 30, "two"], [48, 35, 79, 33], [48, 36, 79, 34, "ii"], [48, 38, 79, 36], [48, 39, 79, 37], [48, 41, 79, 39, "max<PERSON><PERSON><PERSON>"], [48, 49, 79, 47], [48, 52, 79, 50], [48, 53, 79, 51], [48, 55, 79, 53, "options"], [48, 62, 79, 60], [48, 63, 79, 61], [48, 65, 79, 63], [49, 10, 80, 8], [49, 17, 80, 15], [49, 21, 80, 19], [50, 8, 81, 6], [51, 6, 82, 4], [52, 4, 83, 2], [52, 5, 83, 3], [52, 11, 83, 9], [53, 6, 84, 4], [53, 11, 84, 9], [53, 15, 84, 15, "key"], [53, 18, 84, 18], [53, 22, 84, 22, "one"], [53, 25, 84, 25], [53, 27, 84, 27], [54, 8, 85, 6], [54, 12, 85, 10, "<PERSON><PERSON><PERSON><PERSON>"], [54, 22, 85, 20], [54, 23, 85, 21, "one"], [54, 26, 85, 24], [54, 27, 85, 25, "key"], [54, 30, 85, 28], [54, 31, 85, 29], [54, 33, 85, 31, "two"], [54, 36, 85, 34], [54, 37, 85, 35, "key"], [54, 40, 85, 38], [54, 41, 85, 39], [54, 43, 85, 41, "max<PERSON><PERSON><PERSON>"], [54, 51, 85, 49], [54, 54, 85, 52], [54, 55, 85, 53], [54, 57, 85, 55, "options"], [54, 64, 85, 62], [54, 65, 85, 63], [54, 67, 85, 65], [55, 10, 86, 8], [55, 17, 86, 15], [55, 21, 86, 19], [56, 8, 87, 6], [57, 6, 88, 4], [58, 6, 89, 4], [58, 11, 89, 9], [58, 15, 89, 15, "twoKey"], [58, 21, 89, 21], [58, 25, 89, 25, "two"], [58, 28, 89, 28], [58, 30, 89, 30], [59, 8, 92, 6], [59, 12, 92, 10, "one"], [59, 15, 92, 13], [59, 16, 92, 14, "twoKey"], [59, 22, 92, 20], [59, 23, 92, 21], [59, 28, 92, 26, "undefined"], [59, 37, 92, 35], [59, 41, 92, 39, "two"], [59, 44, 92, 42], [59, 45, 92, 43, "twoKey"], [59, 51, 92, 49], [59, 52, 92, 50], [59, 57, 92, 55, "undefined"], [59, 66, 92, 64], [59, 68, 92, 66], [60, 10, 93, 8], [60, 17, 93, 15], [60, 21, 93, 19], [61, 8, 94, 6], [62, 6, 95, 4], [63, 4, 96, 2], [64, 4, 97, 2], [64, 11, 97, 9], [64, 16, 97, 14], [65, 2, 98, 0], [66, 2, 100, 0, "<PERSON><PERSON><PERSON><PERSON>"], [66, 12, 100, 10], [66, 13, 100, 11, "unstable_setLogListeners"], [66, 37, 100, 35], [66, 40, 100, 38, "unstable_setLogListeners"], [66, 64, 100, 62], [67, 2, 100, 63], [67, 6, 100, 63, "_default"], [67, 14, 100, 63], [67, 17, 100, 63, "exports"], [67, 24, 100, 63], [67, 25, 100, 63, "default"], [67, 32, 100, 63], [67, 35, 101, 15, "<PERSON><PERSON><PERSON><PERSON>"], [67, 45, 101, 25], [68, 0, 101, 25], [68, 3]], "functionMap": {"names": ["<global>", "unstable_setLogListeners", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;ACoB;CDE;AEK;CFsE"}}, "type": "js/module"}]}