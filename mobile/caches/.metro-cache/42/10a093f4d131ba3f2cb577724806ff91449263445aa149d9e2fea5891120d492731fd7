{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeI18nManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 52}}], "key": "GTtUrC2CpOYvjPUf5BXwZN4IHBY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeI18nManager = _interopRequireDefault(require(_dependencyMap[1], \"./NativeI18nManager\"));\n  var i18nConstants = getI18nManagerConstants();\n  function getI18nManagerConstants() {\n    if (_NativeI18nManager.default) {\n      var _NativeI18nManager$ge = _NativeI18nManager.default.getConstants(),\n        isRTL = _NativeI18nManager$ge.isRTL,\n        doLeftAndRightSwapInRTL = _NativeI18nManager$ge.doLeftAndRightSwapInRTL,\n        localeIdentifier = _NativeI18nManager$ge.localeIdentifier;\n      return {\n        isRTL,\n        doLeftAndRightSwapInRTL,\n        localeIdentifier\n      };\n    }\n    return {\n      isRTL: false,\n      doLeftAndRightSwapInRTL: true\n    };\n  }\n  var _default = exports.default = {\n    getConstants: () => {\n      return i18nConstants;\n    },\n    allowRTL: shouldAllow => {\n      if (!_NativeI18nManager.default) {\n        return;\n      }\n      _NativeI18nManager.default.allowRTL(shouldAllow);\n    },\n    forceRTL: shouldForce => {\n      if (!_NativeI18nManager.default) {\n        return;\n      }\n      _NativeI18nManager.default.forceRTL(shouldForce);\n    },\n    swapLeftAndRightInRTL: flipStyles => {\n      if (!_NativeI18nManager.default) {\n        return;\n      }\n      _NativeI18nManager.default.swapLeftAndRightInRTL(flipStyles);\n    },\n    isRTL: i18nConstants.isRTL,\n    doLeftAndRightSwapInRTL: i18nConstants.doLeftAndRightSwapInRTL\n  };\n});", "lineCount": 51, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_NativeI18nManager"], [7, 24, 13, 0], [7, 27, 13, 0, "_interopRequireDefault"], [7, 49, 13, 0], [7, 50, 13, 0, "require"], [7, 57, 13, 0], [7, 58, 13, 0, "_dependencyMap"], [7, 72, 13, 0], [8, 2, 15, 0], [8, 6, 15, 6, "i18nConstants"], [8, 19, 15, 41], [8, 22, 15, 44, "getI18nManagerConstants"], [8, 45, 15, 67], [8, 46, 15, 68], [8, 47, 15, 69], [9, 2, 17, 0], [9, 11, 17, 9, "getI18nManagerConstants"], [9, 34, 17, 32, "getI18nManagerConstants"], [9, 35, 17, 32], [9, 37, 17, 57], [10, 4, 18, 2], [10, 8, 18, 6, "NativeI18nManager"], [10, 34, 18, 23], [10, 36, 18, 25], [11, 6, 19, 4], [11, 10, 19, 4, "_NativeI18nManager$ge"], [11, 31, 19, 4], [11, 34, 20, 6, "NativeI18nManager"], [11, 60, 20, 23], [11, 61, 20, 24, "getConstants"], [11, 73, 20, 36], [11, 74, 20, 37], [11, 75, 20, 38], [12, 8, 19, 11, "isRTL"], [12, 13, 19, 16], [12, 16, 19, 16, "_NativeI18nManager$ge"], [12, 37, 19, 16], [12, 38, 19, 11, "isRTL"], [12, 43, 19, 16], [13, 8, 19, 18, "doLeftAndRightSwapInRTL"], [13, 31, 19, 41], [13, 34, 19, 41, "_NativeI18nManager$ge"], [13, 55, 19, 41], [13, 56, 19, 18, "doLeftAndRightSwapInRTL"], [13, 79, 19, 41], [14, 8, 19, 43, "localeIdentifier"], [14, 24, 19, 59], [14, 27, 19, 59, "_NativeI18nManager$ge"], [14, 48, 19, 59], [14, 49, 19, 43, "localeIdentifier"], [14, 65, 19, 59], [15, 6, 21, 4], [15, 13, 21, 11], [16, 8, 21, 12, "isRTL"], [16, 13, 21, 17], [17, 8, 21, 19, "doLeftAndRightSwapInRTL"], [17, 31, 21, 42], [18, 8, 21, 44, "localeIdentifier"], [19, 6, 21, 60], [19, 7, 21, 61], [20, 4, 22, 2], [21, 4, 24, 2], [21, 11, 24, 9], [22, 6, 25, 4, "isRTL"], [22, 11, 25, 9], [22, 13, 25, 11], [22, 18, 25, 16], [23, 6, 26, 4, "doLeftAndRightSwapInRTL"], [23, 29, 26, 27], [23, 31, 26, 29], [24, 4, 27, 2], [24, 5, 27, 3], [25, 2, 28, 0], [26, 2, 28, 1], [26, 6, 28, 1, "_default"], [26, 14, 28, 1], [26, 17, 28, 1, "exports"], [26, 24, 28, 1], [26, 25, 28, 1, "default"], [26, 32, 28, 1], [26, 35, 30, 15], [27, 4, 31, 2, "getConstants"], [27, 16, 31, 14], [27, 18, 31, 16, "getConstants"], [27, 19, 31, 16], [27, 24, 31, 44], [28, 6, 32, 4], [28, 13, 32, 11, "i18nConstants"], [28, 26, 32, 24], [29, 4, 33, 2], [29, 5, 33, 3], [30, 4, 35, 2, "allowRTL"], [30, 12, 35, 10], [30, 14, 35, 13, "shouldAllow"], [30, 25, 35, 33], [30, 29, 35, 38], [31, 6, 36, 4], [31, 10, 36, 8], [31, 11, 36, 9, "NativeI18nManager"], [31, 37, 36, 26], [31, 39, 36, 28], [32, 8, 37, 6], [33, 6, 38, 4], [34, 6, 40, 4, "NativeI18nManager"], [34, 32, 40, 21], [34, 33, 40, 22, "allowRTL"], [34, 41, 40, 30], [34, 42, 40, 31, "shouldAllow"], [34, 53, 40, 42], [34, 54, 40, 43], [35, 4, 41, 2], [35, 5, 41, 3], [36, 4, 43, 2, "forceRTL"], [36, 12, 43, 10], [36, 14, 43, 13, "<PERSON><PERSON><PERSON><PERSON>"], [36, 25, 43, 33], [36, 29, 43, 38], [37, 6, 44, 4], [37, 10, 44, 8], [37, 11, 44, 9, "NativeI18nManager"], [37, 37, 44, 26], [37, 39, 44, 28], [38, 8, 45, 6], [39, 6, 46, 4], [40, 6, 48, 4, "NativeI18nManager"], [40, 32, 48, 21], [40, 33, 48, 22, "forceRTL"], [40, 41, 48, 30], [40, 42, 48, 31, "<PERSON><PERSON><PERSON><PERSON>"], [40, 53, 48, 42], [40, 54, 48, 43], [41, 4, 49, 2], [41, 5, 49, 3], [42, 4, 51, 2, "swapLeftAndRightInRTL"], [42, 25, 51, 23], [42, 27, 51, 26, "flipStyles"], [42, 37, 51, 45], [42, 41, 51, 50], [43, 6, 52, 4], [43, 10, 52, 8], [43, 11, 52, 9, "NativeI18nManager"], [43, 37, 52, 26], [43, 39, 52, 28], [44, 8, 53, 6], [45, 6, 54, 4], [46, 6, 56, 4, "NativeI18nManager"], [46, 32, 56, 21], [46, 33, 56, 22, "swapLeftAndRightInRTL"], [46, 54, 56, 43], [46, 55, 56, 44, "flipStyles"], [46, 65, 56, 54], [46, 66, 56, 55], [47, 4, 57, 2], [47, 5, 57, 3], [48, 4, 59, 2, "isRTL"], [48, 9, 59, 7], [48, 11, 59, 9, "i18nConstants"], [48, 24, 59, 22], [48, 25, 59, 23, "isRTL"], [48, 30, 59, 61], [49, 4, 60, 2, "doLeftAndRightSwapInRTL"], [49, 27, 60, 25], [49, 29, 61, 4, "i18nConstants"], [49, 42, 61, 17], [49, 43, 61, 18, "doLeftAndRightSwapInRTL"], [50, 2, 62, 0], [50, 3, 62, 1], [51, 0, 62, 1], [51, 3]], "functionMap": {"names": ["<global>", "getI18nManagerConstants", "default.getConstants", "default.allowRTL", "default.forceRTL", "default.swapLeftAndRightInRTL"], "mappings": "AAA;ACgB;CDW;gBEG;GFE;YGE;GHM;YIE;GJM;yBKE;GLM"}}, "type": "js/module"}]}