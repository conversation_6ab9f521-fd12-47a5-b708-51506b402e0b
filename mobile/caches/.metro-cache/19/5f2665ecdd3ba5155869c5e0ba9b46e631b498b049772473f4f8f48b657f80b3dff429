{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 42, "index": 56}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../frameCallback/FrameCallbackRegistryJS", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 58}, "end": {"line": 4, "column": 79, "index": 137}}], "key": "6BDNokJoyne46867QZhBU9NZyJ0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFrameCallback = useFrameCallback;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _FrameCallbackRegistryJS = _interopRequireDefault(require(_dependencyMap[2], \"../frameCallback/FrameCallbackRegistryJS\"));\n  /**\n   * @param setActive - A function that lets you start the frame callback or stop\n   *   it from running.\n   * @param isActive - A boolean indicating whether a callback is running.\n   * @param callbackId - A number indicating a unique identifier of the frame\n   *   callback.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useFrameCallback#returns\n   */\n\n  var frameCallbackRegistry = new _FrameCallbackRegistryJS.default();\n\n  /**\n   * Lets you run a function on every frame update.\n   *\n   * @param callback - A function executed on every frame update.\n   * @param autostart - Whether the callback should start automatically. Defaults\n   *   to `true`.\n   * @returns A frame callback object - {@link FrameCallback}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useFrameCallback\n   */\n  function useFrameCallback(callback) {\n    var autostart = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    var ref = (0, _react.useRef)({\n      setActive: isActive => {\n        frameCallbackRegistry.manageStateFrameCallback(ref.current.callbackId, isActive);\n        ref.current.isActive = isActive;\n      },\n      isActive: autostart,\n      callbackId: -1\n    });\n    (0, _react.useEffect)(() => {\n      ref.current.callbackId = frameCallbackRegistry.registerFrameCallback(callback);\n      var memoizedFrameCallback = ref.current;\n      ref.current.setActive(ref.current.isActive);\n      return () => {\n        frameCallbackRegistry.unregisterFrameCallback(memoizedFrameCallback.callbackId);\n        memoizedFrameCallback.callbackId = -1;\n      };\n    }, [callback, autostart]);\n    return ref.current;\n  }\n});", "lineCount": 52, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useFrameCallback"], [8, 26, 1, 13], [8, 29, 1, 13, "useFrameCallback"], [8, 45, 1, 13], [9, 2, 2, 0], [9, 6, 2, 0, "_react"], [9, 12, 2, 0], [9, 15, 2, 0, "require"], [9, 22, 2, 0], [9, 23, 2, 0, "_dependencyMap"], [9, 37, 2, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_FrameCallbackRegistryJS"], [10, 30, 4, 0], [10, 33, 4, 0, "_interopRequireDefault"], [10, 55, 4, 0], [10, 56, 4, 0, "require"], [10, 63, 4, 0], [10, 64, 4, 0, "_dependencyMap"], [10, 78, 4, 0], [11, 2, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [20, 2, 20, 0], [20, 6, 20, 6, "frameCallbackRegistry"], [20, 27, 20, 27], [20, 30, 20, 30], [20, 34, 20, 34, "FrameCallbackRegistryJS"], [20, 66, 20, 57], [20, 67, 20, 58], [20, 68, 20, 59], [22, 2, 22, 0], [23, 0, 23, 0], [24, 0, 24, 0], [25, 0, 25, 0], [26, 0, 26, 0], [27, 0, 27, 0], [28, 0, 28, 0], [29, 0, 29, 0], [30, 0, 30, 0], [31, 2, 31, 7], [31, 11, 31, 16, "useFrameCallback"], [31, 27, 31, 32, "useFrameCallback"], [31, 28, 32, 2, "callback"], [31, 36, 32, 42], [31, 38, 34, 17], [32, 4, 34, 17], [32, 8, 33, 2, "autostart"], [32, 17, 33, 11], [32, 20, 33, 11, "arguments"], [32, 29, 33, 11], [32, 30, 33, 11, "length"], [32, 36, 33, 11], [32, 44, 33, 11, "arguments"], [32, 53, 33, 11], [32, 61, 33, 11, "undefined"], [32, 70, 33, 11], [32, 73, 33, 11, "arguments"], [32, 82, 33, 11], [32, 88, 33, 14], [32, 92, 33, 18], [33, 4, 35, 2], [33, 8, 35, 8, "ref"], [33, 11, 35, 11], [33, 14, 35, 14], [33, 18, 35, 14, "useRef"], [33, 31, 35, 20], [33, 33, 35, 36], [34, 6, 36, 4, "setActive"], [34, 15, 36, 13], [34, 17, 36, 16, "isActive"], [34, 25, 36, 33], [34, 29, 36, 38], [35, 8, 37, 6, "frameCallbackRegistry"], [35, 29, 37, 27], [35, 30, 37, 28, "manageStateFrameCallback"], [35, 54, 37, 52], [35, 55, 38, 8, "ref"], [35, 58, 38, 11], [35, 59, 38, 12, "current"], [35, 66, 38, 19], [35, 67, 38, 20, "callbackId"], [35, 77, 38, 30], [35, 79, 39, 8, "isActive"], [35, 87, 40, 6], [35, 88, 40, 7], [36, 8, 41, 6, "ref"], [36, 11, 41, 9], [36, 12, 41, 10, "current"], [36, 19, 41, 17], [36, 20, 41, 18, "isActive"], [36, 28, 41, 26], [36, 31, 41, 29, "isActive"], [36, 39, 41, 37], [37, 6, 42, 4], [37, 7, 42, 5], [38, 6, 43, 4, "isActive"], [38, 14, 43, 12], [38, 16, 43, 14, "autostart"], [38, 25, 43, 23], [39, 6, 44, 4, "callbackId"], [39, 16, 44, 14], [39, 18, 44, 16], [39, 19, 44, 17], [40, 4, 45, 2], [40, 5, 45, 3], [40, 6, 45, 4], [41, 4, 47, 2], [41, 8, 47, 2, "useEffect"], [41, 24, 47, 11], [41, 26, 47, 12], [41, 32, 47, 18], [42, 6, 48, 4, "ref"], [42, 9, 48, 7], [42, 10, 48, 8, "current"], [42, 17, 48, 15], [42, 18, 48, 16, "callbackId"], [42, 28, 48, 26], [42, 31, 49, 6, "frameCallbackRegistry"], [42, 52, 49, 27], [42, 53, 49, 28, "registerFrameCallback"], [42, 74, 49, 49], [42, 75, 49, 50, "callback"], [42, 83, 49, 58], [42, 84, 49, 59], [43, 6, 50, 4], [43, 10, 50, 10, "memoizedFrameCallback"], [43, 31, 50, 31], [43, 34, 50, 34, "ref"], [43, 37, 50, 37], [43, 38, 50, 38, "current"], [43, 45, 50, 45], [44, 6, 51, 4, "ref"], [44, 9, 51, 7], [44, 10, 51, 8, "current"], [44, 17, 51, 15], [44, 18, 51, 16, "setActive"], [44, 27, 51, 25], [44, 28, 51, 26, "ref"], [44, 31, 51, 29], [44, 32, 51, 30, "current"], [44, 39, 51, 37], [44, 40, 51, 38, "isActive"], [44, 48, 51, 46], [44, 49, 51, 47], [45, 6, 53, 4], [45, 13, 53, 11], [45, 19, 53, 17], [46, 8, 54, 6, "frameCallbackRegistry"], [46, 29, 54, 27], [46, 30, 54, 28, "unregisterFrameCallback"], [46, 53, 54, 51], [46, 54, 55, 8, "memoizedFrameCallback"], [46, 75, 55, 29], [46, 76, 55, 30, "callbackId"], [46, 86, 56, 6], [46, 87, 56, 7], [47, 8, 57, 6, "memoizedFrameCallback"], [47, 29, 57, 27], [47, 30, 57, 28, "callbackId"], [47, 40, 57, 38], [47, 43, 57, 41], [47, 44, 57, 42], [47, 45, 57, 43], [48, 6, 58, 4], [48, 7, 58, 5], [49, 4, 59, 2], [49, 5, 59, 3], [49, 7, 59, 5], [49, 8, 59, 6, "callback"], [49, 16, 59, 14], [49, 18, 59, 16, "autostart"], [49, 27, 59, 25], [49, 28, 59, 26], [49, 29, 59, 27], [50, 4, 61, 2], [50, 11, 61, 9, "ref"], [50, 14, 61, 12], [50, 15, 61, 13, "current"], [50, 22, 61, 20], [51, 2, 62, 0], [52, 0, 62, 1], [52, 3]], "functionMap": {"names": ["<global>", "useFrameCallback", "useRef$argument_0.setActive", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OC8B;eCK;KDM;YEK;WCM;KDK;GFC;CDG"}}, "type": "js/module"}]}