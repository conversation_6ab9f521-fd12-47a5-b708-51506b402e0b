{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 57}, "end": {"line": 2, "column": 44, "index": 101}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 102}, "end": {"line": 3, "column": 40, "index": 142}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./ExpoLinking", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 143}, "end": {"line": 4, "column": 40, "index": 183}}], "key": "9p576A/DjaRztdSFcD8Nohub+Qg=", "exportNames": ["*"]}}, {"name": "./RNLinking", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 184}, "end": {"line": 5, "column": 36, "index": 220}}], "key": "2y1SxYi56UhLqVRPo+LPBad0ytc=", "exportNames": ["*"]}}, {"name": "./createURL", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 221}, "end": {"line": 6, "column": 36, "index": 257}}], "key": "vA/KQURWJdMSJD6QjTsub0VGQQ0=", "exportNames": ["*"]}}, {"name": "./validateURL", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 258}, "end": {"line": 7, "column": 44, "index": 302}}], "key": "QglbFjhSs1PLl8wx6icnjPl9qpg=", "exportNames": ["*"]}}, {"name": "./Linking.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 142, "column": 0, "index": 5340}, "end": {"line": 142, "column": 32, "index": 5372}}], "key": "UNHzwDbHrazdE82g65F1azNEReY=", "exportNames": ["*"]}}, {"name": "./Schemes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 143, "column": 0, "index": 5373}, "end": {"line": 143, "column": 26, "index": 5399}}], "key": "n/tFEHLQPHdXg5i1nrK9VhhMvhA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    addEventListener: true,\n    parseInitialURLAsync: true,\n    sendIntent: true,\n    openSettings: true,\n    getInitialURL: true,\n    getLinkingURL: true,\n    openURL: true,\n    canOpenURL: true,\n    useURL: true,\n    useLinkingURL: true,\n    parse: true,\n    createURL: true\n  };\n  exports.addEventListener = addEventListener;\n  exports.canOpenURL = canOpenURL;\n  Object.defineProperty(exports, \"createURL\", {\n    enumerable: true,\n    get: function () {\n      return _createURL.createURL;\n    }\n  });\n  exports.getInitialURL = getInitialURL;\n  exports.getLinkingURL = getLinkingURL;\n  exports.openSettings = openSettings;\n  exports.openURL = openURL;\n  Object.defineProperty(exports, \"parse\", {\n    enumerable: true,\n    get: function () {\n      return _createURL.parse;\n    }\n  });\n  exports.parseInitialURLAsync = parseInitialURLAsync;\n  exports.sendIntent = sendIntent;\n  exports.useLinkingURL = useLinkingURL;\n  exports.useURL = useURL;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _expoModulesCore = require(_dependencyMap[3], \"expo-modules-core\");\n  var _react = require(_dependencyMap[4], \"react\");\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _ExpoLinking = _interopRequireDefault(require(_dependencyMap[6], \"./ExpoLinking\"));\n  var _RNLinking = _interopRequireDefault(require(_dependencyMap[7], \"./RNLinking\"));\n  var _createURL = require(_dependencyMap[8], \"./createURL\");\n  var _validateURL = require(_dependencyMap[9], \"./validateURL\");\n  var _Linking = require(_dependencyMap[10], \"./Linking.types\");\n  Object.keys(_Linking).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _Linking[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Linking[key];\n      }\n    });\n  });\n  var _Schemes = require(_dependencyMap[11], \"./Schemes\");\n  Object.keys(_Schemes).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _Schemes[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Schemes[key];\n      }\n    });\n  });\n  // @needsAudit\n  /**\n   * Add a handler to `Linking` changes by listening to the `url` event type and providing the handler.\n   * It is recommended to use the [`useURL()`](#useurl) hook instead.\n   * @param type The only valid type is `'url'`.\n   * @param handler An [`URLListener`](#urllistener) function that takes an `event` object of the type\n   * [`EventType`](#eventtype).\n   * @return An EmitterSubscription that has the remove method from EventSubscription\n   * @see [React Native documentation on Linking](https://reactnative.dev/docs/linking#addeventlistener).\n   */\n  function addEventListener(type, handler) {\n    return _RNLinking.default.addEventListener(type, handler);\n  }\n  // @needsAudit\n  /**\n   * Helper method which wraps React Native's `Linking.getInitialURL()` in `Linking.parse()`.\n   * Parses the deep link information out of the URL used to open the experience initially.\n   * If no link opened the app, all the fields will be `null`.\n   * > On the web it parses the current window URL.\n   * @return A promise that resolves with `ParsedURL` object.\n   */\n  function parseInitialURLAsync() {\n    return _parseInitialURLAsync.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Launch an Android intent with extras.\n   * > Use [`expo-intent-launcher`](./intent-launcher) instead. `sendIntent` is only included in\n   * > `Linking` for API compatibility with React Native's Linking API.\n   * @platform android\n   */\n  function _parseInitialURLAsync() {\n    _parseInitialURLAsync = (0, _asyncToGenerator2.default)(function* () {\n      var initialUrl = yield _RNLinking.default.getInitialURL();\n      if (!initialUrl) {\n        return {\n          scheme: null,\n          hostname: null,\n          path: null,\n          queryParams: null\n        };\n      }\n      return (0, _createURL.parse)(initialUrl);\n    });\n    return _parseInitialURLAsync.apply(this, arguments);\n  }\n  function sendIntent(_x, _x2) {\n    return _sendIntent.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Open the operating system settings app and displays the app’s custom settings, if it has any.\n   */\n  function _sendIntent() {\n    _sendIntent = (0, _asyncToGenerator2.default)(function* (action, extras) {\n      if (_reactNative.Platform.OS === 'android') {\n        return yield _RNLinking.default.sendIntent(action, extras);\n      }\n      throw new _expoModulesCore.UnavailabilityError('Linking', 'sendIntent');\n    });\n    return _sendIntent.apply(this, arguments);\n  }\n  function openSettings() {\n    return _openSettings.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Get the URL that was used to launch the app if it was launched by a link.\n   * @return The URL string that launched your app, or `null`.\n   */\n  function _openSettings() {\n    _openSettings = (0, _asyncToGenerator2.default)(function* () {\n      if (_reactNative.Platform.OS === 'web') {\n        throw new _expoModulesCore.UnavailabilityError('Linking', 'openSettings');\n      }\n      if (_RNLinking.default.openSettings) {\n        return yield _RNLinking.default.openSettings();\n      }\n      yield openURL('app-settings:');\n    });\n    return _openSettings.apply(this, arguments);\n  }\n  function getInitialURL() {\n    return _getInitialURL.apply(this, arguments);\n  }\n  /**\n   * Get the URL that was used to launch the app if it was launched by a link.\n   * @return The URL string that launched your app, or `null`.\n   */\n  function _getInitialURL() {\n    _getInitialURL = (0, _asyncToGenerator2.default)(function* () {\n      return (yield _RNLinking.default.getInitialURL()) ?? null;\n    });\n    return _getInitialURL.apply(this, arguments);\n  }\n  function getLinkingURL() {\n    return _ExpoLinking.default.getLinkingURL();\n  }\n  // @needsAudit\n  /**\n   * Attempt to open the given URL with an installed app. See the [Linking guide](/guides/linking)\n   * for more information.\n   * @param url A URL for the operating system to open. For example: `tel:5555555`, `exp://`.\n   * @return A `Promise` that is fulfilled with `true` if the link is opened operating system\n   * automatically or the user confirms the prompt to open the link. The `Promise` rejects if there\n   * are no applications registered for the URL or the user cancels the dialog.\n   */\n  function openURL(_x3) {\n    return _openURL.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Determine whether or not an installed app can handle a given URL.\n   * On web this always returns `true` because there is no API for detecting what URLs can be opened.\n   * @param url The URL that you want to test can be opened.\n   * @return A `Promise` object that is fulfilled with `true` if the URL can be handled, otherwise it\n   * `false` if not.\n   * The `Promise` will reject on Android if it was impossible to check if the URL can be opened, and\n   * on iOS if you didn't [add the specific scheme in the `LSApplicationQueriesSchemes` key inside **Info.plist**](/guides/linking#linking-from-your-app).\n   */\n  function _openURL() {\n    _openURL = (0, _asyncToGenerator2.default)(function* (url) {\n      (0, _validateURL.validateURL)(url);\n      return yield _RNLinking.default.openURL(url);\n    });\n    return _openURL.apply(this, arguments);\n  }\n  function canOpenURL(_x4) {\n    return _canOpenURL.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Returns the initial URL followed by any subsequent changes to the URL.\n   * @return Returns the initial URL or `null`.\n   */\n  function _canOpenURL() {\n    _canOpenURL = (0, _asyncToGenerator2.default)(function* (url) {\n      (0, _validateURL.validateURL)(url);\n      return yield _RNLinking.default.canOpenURL(url);\n    });\n    return _canOpenURL.apply(this, arguments);\n  }\n  function useURL() {\n    var _useState = (0, _react.useState)(null),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      url = _useState2[0],\n      setLink = _useState2[1];\n    function onChange(event) {\n      setLink(event.url);\n    }\n    (0, _react.useEffect)(() => {\n      getInitialURL().then(url => setLink(url));\n      var subscription = addEventListener('url', onChange);\n      return () => subscription.remove();\n    }, []);\n    return url;\n  }\n  /**\n   * Returns the linking URL followed by any subsequent changes to the URL.\n   * Always returns the initial URL immediately on reload.\n   * @return Returns the initial URL or `null`.\n   */\n  function useLinkingURL() {\n    var _useState3 = (0, _react.useState)(_ExpoLinking.default.getLinkingURL),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      url = _useState4[0],\n      setLink = _useState4[1];\n    function onChange(event) {\n      setLink(event.url);\n    }\n    (0, _react.useEffect)(() => {\n      var subscription = _ExpoLinking.default.addListener('onURLReceived', onChange);\n      return () => subscription.remove();\n    }, []);\n    return url ?? null;\n  }\n});", "lineCount": 246, "map": [[44, 2, 1, 0], [44, 6, 1, 0, "_expoModulesCore"], [44, 22, 1, 0], [44, 25, 1, 0, "require"], [44, 32, 1, 0], [44, 33, 1, 0, "_dependencyMap"], [44, 47, 1, 0], [45, 2, 2, 0], [45, 6, 2, 0, "_react"], [45, 12, 2, 0], [45, 15, 2, 0, "require"], [45, 22, 2, 0], [45, 23, 2, 0, "_dependencyMap"], [45, 37, 2, 0], [46, 2, 3, 0], [46, 6, 3, 0, "_reactNative"], [46, 18, 3, 0], [46, 21, 3, 0, "require"], [46, 28, 3, 0], [46, 29, 3, 0, "_dependencyMap"], [46, 43, 3, 0], [47, 2, 4, 0], [47, 6, 4, 0, "_ExpoLinking"], [47, 18, 4, 0], [47, 21, 4, 0, "_interopRequireDefault"], [47, 43, 4, 0], [47, 44, 4, 0, "require"], [47, 51, 4, 0], [47, 52, 4, 0, "_dependencyMap"], [47, 66, 4, 0], [48, 2, 5, 0], [48, 6, 5, 0, "_RNLinking"], [48, 16, 5, 0], [48, 19, 5, 0, "_interopRequireDefault"], [48, 41, 5, 0], [48, 42, 5, 0, "require"], [48, 49, 5, 0], [48, 50, 5, 0, "_dependencyMap"], [48, 64, 5, 0], [49, 2, 6, 0], [49, 6, 6, 0, "_createURL"], [49, 16, 6, 0], [49, 19, 6, 0, "require"], [49, 26, 6, 0], [49, 27, 6, 0, "_dependencyMap"], [49, 41, 6, 0], [50, 2, 7, 0], [50, 6, 7, 0, "_validateURL"], [50, 18, 7, 0], [50, 21, 7, 0, "require"], [50, 28, 7, 0], [50, 29, 7, 0, "_dependencyMap"], [50, 43, 7, 0], [51, 2, 142, 0], [51, 6, 142, 0, "_Linking"], [51, 14, 142, 0], [51, 17, 142, 0, "require"], [51, 24, 142, 0], [51, 25, 142, 0, "_dependencyMap"], [51, 39, 142, 0], [52, 2, 142, 0, "Object"], [52, 8, 142, 0], [52, 9, 142, 0, "keys"], [52, 13, 142, 0], [52, 14, 142, 0, "_Linking"], [52, 22, 142, 0], [52, 24, 142, 0, "for<PERSON>ach"], [52, 31, 142, 0], [52, 42, 142, 0, "key"], [52, 45, 142, 0], [53, 4, 142, 0], [53, 8, 142, 0, "key"], [53, 11, 142, 0], [53, 29, 142, 0, "key"], [53, 32, 142, 0], [54, 4, 142, 0], [54, 8, 142, 0, "Object"], [54, 14, 142, 0], [54, 15, 142, 0, "prototype"], [54, 24, 142, 0], [54, 25, 142, 0, "hasOwnProperty"], [54, 39, 142, 0], [54, 40, 142, 0, "call"], [54, 44, 142, 0], [54, 45, 142, 0, "_exportNames"], [54, 57, 142, 0], [54, 59, 142, 0, "key"], [54, 62, 142, 0], [55, 4, 142, 0], [55, 8, 142, 0, "key"], [55, 11, 142, 0], [55, 15, 142, 0, "exports"], [55, 22, 142, 0], [55, 26, 142, 0, "exports"], [55, 33, 142, 0], [55, 34, 142, 0, "key"], [55, 37, 142, 0], [55, 43, 142, 0, "_Linking"], [55, 51, 142, 0], [55, 52, 142, 0, "key"], [55, 55, 142, 0], [56, 4, 142, 0, "Object"], [56, 10, 142, 0], [56, 11, 142, 0, "defineProperty"], [56, 25, 142, 0], [56, 26, 142, 0, "exports"], [56, 33, 142, 0], [56, 35, 142, 0, "key"], [56, 38, 142, 0], [57, 6, 142, 0, "enumerable"], [57, 16, 142, 0], [58, 6, 142, 0, "get"], [58, 9, 142, 0], [58, 20, 142, 0, "get"], [58, 21, 142, 0], [59, 8, 142, 0], [59, 15, 142, 0, "_Linking"], [59, 23, 142, 0], [59, 24, 142, 0, "key"], [59, 27, 142, 0], [60, 6, 142, 0], [61, 4, 142, 0], [62, 2, 142, 0], [63, 2, 143, 0], [63, 6, 143, 0, "_Schemes"], [63, 14, 143, 0], [63, 17, 143, 0, "require"], [63, 24, 143, 0], [63, 25, 143, 0, "_dependencyMap"], [63, 39, 143, 0], [64, 2, 143, 0, "Object"], [64, 8, 143, 0], [64, 9, 143, 0, "keys"], [64, 13, 143, 0], [64, 14, 143, 0, "_Schemes"], [64, 22, 143, 0], [64, 24, 143, 0, "for<PERSON>ach"], [64, 31, 143, 0], [64, 42, 143, 0, "key"], [64, 45, 143, 0], [65, 4, 143, 0], [65, 8, 143, 0, "key"], [65, 11, 143, 0], [65, 29, 143, 0, "key"], [65, 32, 143, 0], [66, 4, 143, 0], [66, 8, 143, 0, "Object"], [66, 14, 143, 0], [66, 15, 143, 0, "prototype"], [66, 24, 143, 0], [66, 25, 143, 0, "hasOwnProperty"], [66, 39, 143, 0], [66, 40, 143, 0, "call"], [66, 44, 143, 0], [66, 45, 143, 0, "_exportNames"], [66, 57, 143, 0], [66, 59, 143, 0, "key"], [66, 62, 143, 0], [67, 4, 143, 0], [67, 8, 143, 0, "key"], [67, 11, 143, 0], [67, 15, 143, 0, "exports"], [67, 22, 143, 0], [67, 26, 143, 0, "exports"], [67, 33, 143, 0], [67, 34, 143, 0, "key"], [67, 37, 143, 0], [67, 43, 143, 0, "_Schemes"], [67, 51, 143, 0], [67, 52, 143, 0, "key"], [67, 55, 143, 0], [68, 4, 143, 0, "Object"], [68, 10, 143, 0], [68, 11, 143, 0, "defineProperty"], [68, 25, 143, 0], [68, 26, 143, 0, "exports"], [68, 33, 143, 0], [68, 35, 143, 0, "key"], [68, 38, 143, 0], [69, 6, 143, 0, "enumerable"], [69, 16, 143, 0], [70, 6, 143, 0, "get"], [70, 9, 143, 0], [70, 20, 143, 0, "get"], [70, 21, 143, 0], [71, 8, 143, 0], [71, 15, 143, 0, "_Schemes"], [71, 23, 143, 0], [71, 24, 143, 0, "key"], [71, 27, 143, 0], [72, 6, 143, 0], [73, 4, 143, 0], [74, 2, 143, 0], [75, 2, 8, 0], [76, 2, 9, 0], [77, 0, 10, 0], [78, 0, 11, 0], [79, 0, 12, 0], [80, 0, 13, 0], [81, 0, 14, 0], [82, 0, 15, 0], [83, 0, 16, 0], [84, 0, 17, 0], [85, 2, 18, 7], [85, 11, 18, 16, "addEventListener"], [85, 27, 18, 32, "addEventListener"], [85, 28, 18, 33, "type"], [85, 32, 18, 37], [85, 34, 18, 39, "handler"], [85, 41, 18, 46], [85, 43, 18, 48], [86, 4, 19, 4], [86, 11, 19, 11, "RNLinking"], [86, 29, 19, 20], [86, 30, 19, 21, "addEventListener"], [86, 46, 19, 37], [86, 47, 19, 38, "type"], [86, 51, 19, 42], [86, 53, 19, 44, "handler"], [86, 60, 19, 51], [86, 61, 19, 52], [87, 2, 20, 0], [88, 2, 21, 0], [89, 2, 22, 0], [90, 0, 23, 0], [91, 0, 24, 0], [92, 0, 25, 0], [93, 0, 26, 0], [94, 0, 27, 0], [95, 0, 28, 0], [96, 2, 22, 0], [96, 11, 29, 22, "parseInitialURLAsync"], [96, 31, 29, 42, "parseInitialURLAsync"], [96, 32, 29, 42], [97, 4, 29, 42], [97, 11, 29, 42, "_parseInitialURLAsync"], [97, 32, 29, 42], [97, 33, 29, 42, "apply"], [97, 38, 29, 42], [97, 45, 29, 42, "arguments"], [97, 54, 29, 42], [98, 2, 29, 42], [98, 4, 41, 0], [99, 2, 42, 0], [100, 0, 43, 0], [101, 0, 44, 0], [102, 0, 45, 0], [103, 0, 46, 0], [104, 0, 47, 0], [105, 2, 42, 0], [105, 11, 42, 0, "_parseInitialURLAsync"], [105, 33, 42, 0], [106, 4, 42, 0, "_parseInitialURLAsync"], [106, 25, 42, 0], [106, 32, 42, 0, "_asyncToGenerator2"], [106, 50, 42, 0], [106, 51, 42, 0, "default"], [106, 58, 42, 0], [106, 60, 29, 7], [106, 73, 29, 45], [107, 6, 30, 4], [107, 10, 30, 10, "initialUrl"], [107, 20, 30, 20], [107, 29, 30, 29, "RNLinking"], [107, 47, 30, 38], [107, 48, 30, 39, "getInitialURL"], [107, 61, 30, 52], [107, 62, 30, 53], [107, 63, 30, 54], [108, 6, 31, 4], [108, 10, 31, 8], [108, 11, 31, 9, "initialUrl"], [108, 21, 31, 19], [108, 23, 31, 21], [109, 8, 32, 8], [109, 15, 32, 15], [110, 10, 33, 12, "scheme"], [110, 16, 33, 18], [110, 18, 33, 20], [110, 22, 33, 24], [111, 10, 34, 12, "hostname"], [111, 18, 34, 20], [111, 20, 34, 22], [111, 24, 34, 26], [112, 10, 35, 12, "path"], [112, 14, 35, 16], [112, 16, 35, 18], [112, 20, 35, 22], [113, 10, 36, 12, "queryParams"], [113, 21, 36, 23], [113, 23, 36, 25], [114, 8, 37, 8], [114, 9, 37, 9], [115, 6, 38, 4], [116, 6, 39, 4], [116, 13, 39, 11], [116, 17, 39, 11, "parse"], [116, 33, 39, 16], [116, 35, 39, 17, "initialUrl"], [116, 45, 39, 27], [116, 46, 39, 28], [117, 4, 40, 0], [117, 5, 40, 1], [118, 4, 40, 1], [118, 11, 40, 1, "_parseInitialURLAsync"], [118, 32, 40, 1], [118, 33, 40, 1, "apply"], [118, 38, 40, 1], [118, 45, 40, 1, "arguments"], [118, 54, 40, 1], [119, 2, 40, 1], [120, 2, 40, 1], [120, 11, 48, 22, "sendIntent"], [120, 21, 48, 32, "sendIntent"], [120, 22, 48, 32, "_x"], [120, 24, 48, 32], [120, 26, 48, 32, "_x2"], [120, 29, 48, 32], [121, 4, 48, 32], [121, 11, 48, 32, "_sendIntent"], [121, 22, 48, 32], [121, 23, 48, 32, "apply"], [121, 28, 48, 32], [121, 35, 48, 32, "arguments"], [121, 44, 48, 32], [122, 2, 48, 32], [122, 4, 54, 0], [123, 2, 55, 0], [124, 0, 56, 0], [125, 0, 57, 0], [126, 2, 55, 0], [126, 11, 55, 0, "_sendIntent"], [126, 23, 55, 0], [127, 4, 55, 0, "_sendIntent"], [127, 15, 55, 0], [127, 22, 55, 0, "_asyncToGenerator2"], [127, 40, 55, 0], [127, 41, 55, 0, "default"], [127, 48, 55, 0], [127, 50, 48, 7], [127, 61, 48, 33, "action"], [127, 67, 48, 39], [127, 69, 48, 41, "extras"], [127, 75, 48, 47], [127, 77, 48, 49], [128, 6, 49, 4], [128, 10, 49, 8, "Platform"], [128, 31, 49, 16], [128, 32, 49, 17, "OS"], [128, 34, 49, 19], [128, 39, 49, 24], [128, 48, 49, 33], [128, 50, 49, 35], [129, 8, 50, 8], [129, 21, 50, 21, "RNLinking"], [129, 39, 50, 30], [129, 40, 50, 31, "sendIntent"], [129, 50, 50, 41], [129, 51, 50, 42, "action"], [129, 57, 50, 48], [129, 59, 50, 50, "extras"], [129, 65, 50, 56], [129, 66, 50, 57], [130, 6, 51, 4], [131, 6, 52, 4], [131, 12, 52, 10], [131, 16, 52, 14, "UnavailabilityError"], [131, 52, 52, 33], [131, 53, 52, 34], [131, 62, 52, 43], [131, 64, 52, 45], [131, 76, 52, 57], [131, 77, 52, 58], [132, 4, 53, 0], [132, 5, 53, 1], [133, 4, 53, 1], [133, 11, 53, 1, "_sendIntent"], [133, 22, 53, 1], [133, 23, 53, 1, "apply"], [133, 28, 53, 1], [133, 35, 53, 1, "arguments"], [133, 44, 53, 1], [134, 2, 53, 1], [135, 2, 53, 1], [135, 11, 58, 22, "openSettings"], [135, 23, 58, 34, "openSettings"], [135, 24, 58, 34], [136, 4, 58, 34], [136, 11, 58, 34, "_openSettings"], [136, 24, 58, 34], [136, 25, 58, 34, "apply"], [136, 30, 58, 34], [136, 37, 58, 34, "arguments"], [136, 46, 58, 34], [137, 2, 58, 34], [137, 4, 67, 0], [138, 2, 68, 0], [139, 0, 69, 0], [140, 0, 70, 0], [141, 0, 71, 0], [142, 2, 68, 0], [142, 11, 68, 0, "_openSettings"], [142, 25, 68, 0], [143, 4, 68, 0, "_openSettings"], [143, 17, 68, 0], [143, 24, 68, 0, "_asyncToGenerator2"], [143, 42, 68, 0], [143, 43, 68, 0, "default"], [143, 50, 68, 0], [143, 52, 58, 7], [143, 65, 58, 37], [144, 6, 59, 4], [144, 10, 59, 8, "Platform"], [144, 31, 59, 16], [144, 32, 59, 17, "OS"], [144, 34, 59, 19], [144, 39, 59, 24], [144, 44, 59, 29], [144, 46, 59, 31], [145, 8, 60, 8], [145, 14, 60, 14], [145, 18, 60, 18, "UnavailabilityError"], [145, 54, 60, 37], [145, 55, 60, 38], [145, 64, 60, 47], [145, 66, 60, 49], [145, 80, 60, 63], [145, 81, 60, 64], [146, 6, 61, 4], [147, 6, 62, 4], [147, 10, 62, 8, "RNLinking"], [147, 28, 62, 17], [147, 29, 62, 18, "openSettings"], [147, 41, 62, 30], [147, 43, 62, 32], [148, 8, 63, 8], [148, 21, 63, 21, "RNLinking"], [148, 39, 63, 30], [148, 40, 63, 31, "openSettings"], [148, 52, 63, 43], [148, 53, 63, 44], [148, 54, 63, 45], [149, 6, 64, 4], [150, 6, 65, 4], [150, 12, 65, 10, "openURL"], [150, 19, 65, 17], [150, 20, 65, 18], [150, 35, 65, 33], [150, 36, 65, 34], [151, 4, 66, 0], [151, 5, 66, 1], [152, 4, 66, 1], [152, 11, 66, 1, "_openSettings"], [152, 24, 66, 1], [152, 25, 66, 1, "apply"], [152, 30, 66, 1], [152, 37, 66, 1, "arguments"], [152, 46, 66, 1], [153, 2, 66, 1], [154, 2, 66, 1], [154, 11, 72, 22, "getInitialURL"], [154, 24, 72, 35, "getInitialURL"], [154, 25, 72, 35], [155, 4, 72, 35], [155, 11, 72, 35, "_getInitialURL"], [155, 25, 72, 35], [155, 26, 72, 35, "apply"], [155, 31, 72, 35], [155, 38, 72, 35, "arguments"], [155, 47, 72, 35], [156, 2, 72, 35], [157, 2, 75, 0], [158, 0, 76, 0], [159, 0, 77, 0], [160, 0, 78, 0], [161, 2, 75, 0], [161, 11, 75, 0, "_getInitialURL"], [161, 26, 75, 0], [162, 4, 75, 0, "_getInitialURL"], [162, 18, 75, 0], [162, 25, 75, 0, "_asyncToGenerator2"], [162, 43, 75, 0], [162, 44, 75, 0, "default"], [162, 51, 75, 0], [162, 53, 72, 7], [162, 66, 72, 38], [163, 6, 73, 4], [163, 13, 73, 11], [163, 20, 73, 18, "RNLinking"], [163, 38, 73, 27], [163, 39, 73, 28, "getInitialURL"], [163, 52, 73, 41], [163, 53, 73, 42], [163, 54, 73, 43], [163, 59, 73, 48], [163, 63, 73, 52], [164, 4, 74, 0], [164, 5, 74, 1], [165, 4, 74, 1], [165, 11, 74, 1, "_getInitialURL"], [165, 25, 74, 1], [165, 26, 74, 1, "apply"], [165, 31, 74, 1], [165, 38, 74, 1, "arguments"], [165, 47, 74, 1], [166, 2, 74, 1], [167, 2, 79, 7], [167, 11, 79, 16, "getLinkingURL"], [167, 24, 79, 29, "getLinkingURL"], [167, 25, 79, 29], [167, 27, 79, 32], [168, 4, 80, 4], [168, 11, 80, 11, "ExpoLinking"], [168, 31, 80, 22], [168, 32, 80, 23, "getLinkingURL"], [168, 45, 80, 36], [168, 46, 80, 37], [168, 47, 80, 38], [169, 2, 81, 0], [170, 2, 82, 0], [171, 2, 83, 0], [172, 0, 84, 0], [173, 0, 85, 0], [174, 0, 86, 0], [175, 0, 87, 0], [176, 0, 88, 0], [177, 0, 89, 0], [178, 0, 90, 0], [179, 2, 83, 0], [179, 11, 91, 22, "openURL"], [179, 18, 91, 29, "openURL"], [179, 19, 91, 29, "_x3"], [179, 22, 91, 29], [180, 4, 91, 29], [180, 11, 91, 29, "_openURL"], [180, 19, 91, 29], [180, 20, 91, 29, "apply"], [180, 25, 91, 29], [180, 32, 91, 29, "arguments"], [180, 41, 91, 29], [181, 2, 91, 29], [181, 4, 95, 0], [182, 2, 96, 0], [183, 0, 97, 0], [184, 0, 98, 0], [185, 0, 99, 0], [186, 0, 100, 0], [187, 0, 101, 0], [188, 0, 102, 0], [189, 0, 103, 0], [190, 0, 104, 0], [191, 2, 96, 0], [191, 11, 96, 0, "_openURL"], [191, 20, 96, 0], [192, 4, 96, 0, "_openURL"], [192, 12, 96, 0], [192, 19, 96, 0, "_asyncToGenerator2"], [192, 37, 96, 0], [192, 38, 96, 0, "default"], [192, 45, 96, 0], [192, 47, 91, 7], [192, 58, 91, 30, "url"], [192, 61, 91, 33], [192, 63, 91, 35], [193, 6, 92, 4], [193, 10, 92, 4, "validateURL"], [193, 34, 92, 15], [193, 36, 92, 16, "url"], [193, 39, 92, 19], [193, 40, 92, 20], [194, 6, 93, 4], [194, 19, 93, 17, "RNLinking"], [194, 37, 93, 26], [194, 38, 93, 27, "openURL"], [194, 45, 93, 34], [194, 46, 93, 35, "url"], [194, 49, 93, 38], [194, 50, 93, 39], [195, 4, 94, 0], [195, 5, 94, 1], [196, 4, 94, 1], [196, 11, 94, 1, "_openURL"], [196, 19, 94, 1], [196, 20, 94, 1, "apply"], [196, 25, 94, 1], [196, 32, 94, 1, "arguments"], [196, 41, 94, 1], [197, 2, 94, 1], [198, 2, 94, 1], [198, 11, 105, 22, "canOpenURL"], [198, 21, 105, 32, "canOpenURL"], [198, 22, 105, 32, "_x4"], [198, 25, 105, 32], [199, 4, 105, 32], [199, 11, 105, 32, "_canOpenURL"], [199, 22, 105, 32], [199, 23, 105, 32, "apply"], [199, 28, 105, 32], [199, 35, 105, 32, "arguments"], [199, 44, 105, 32], [200, 2, 105, 32], [200, 4, 109, 0], [201, 2, 110, 0], [202, 0, 111, 0], [203, 0, 112, 0], [204, 0, 113, 0], [205, 2, 110, 0], [205, 11, 110, 0, "_canOpenURL"], [205, 23, 110, 0], [206, 4, 110, 0, "_canOpenURL"], [206, 15, 110, 0], [206, 22, 110, 0, "_asyncToGenerator2"], [206, 40, 110, 0], [206, 41, 110, 0, "default"], [206, 48, 110, 0], [206, 50, 105, 7], [206, 61, 105, 33, "url"], [206, 64, 105, 36], [206, 66, 105, 38], [207, 6, 106, 4], [207, 10, 106, 4, "validateURL"], [207, 34, 106, 15], [207, 36, 106, 16, "url"], [207, 39, 106, 19], [207, 40, 106, 20], [208, 6, 107, 4], [208, 19, 107, 17, "RNLinking"], [208, 37, 107, 26], [208, 38, 107, 27, "canOpenURL"], [208, 48, 107, 37], [208, 49, 107, 38, "url"], [208, 52, 107, 41], [208, 53, 107, 42], [209, 4, 108, 0], [209, 5, 108, 1], [210, 4, 108, 1], [210, 11, 108, 1, "_canOpenURL"], [210, 22, 108, 1], [210, 23, 108, 1, "apply"], [210, 28, 108, 1], [210, 35, 108, 1, "arguments"], [210, 44, 108, 1], [211, 2, 108, 1], [212, 2, 114, 7], [212, 11, 114, 16, "useURL"], [212, 17, 114, 22, "useURL"], [212, 18, 114, 22], [212, 20, 114, 25], [213, 4, 115, 4], [213, 8, 115, 4, "_useState"], [213, 17, 115, 4], [213, 20, 115, 27], [213, 24, 115, 27, "useState"], [213, 39, 115, 35], [213, 41, 115, 36], [213, 45, 115, 40], [213, 46, 115, 41], [214, 6, 115, 41, "_useState2"], [214, 16, 115, 41], [214, 23, 115, 41, "_slicedToArray2"], [214, 38, 115, 41], [214, 39, 115, 41, "default"], [214, 46, 115, 41], [214, 48, 115, 41, "_useState"], [214, 57, 115, 41], [215, 6, 115, 11, "url"], [215, 9, 115, 14], [215, 12, 115, 14, "_useState2"], [215, 22, 115, 14], [216, 6, 115, 16, "setLink"], [216, 13, 115, 23], [216, 16, 115, 23, "_useState2"], [216, 26, 115, 23], [217, 4, 116, 4], [217, 13, 116, 13, "onChange"], [217, 21, 116, 21, "onChange"], [217, 22, 116, 22, "event"], [217, 27, 116, 27], [217, 29, 116, 29], [218, 6, 117, 8, "setLink"], [218, 13, 117, 15], [218, 14, 117, 16, "event"], [218, 19, 117, 21], [218, 20, 117, 22, "url"], [218, 23, 117, 25], [218, 24, 117, 26], [219, 4, 118, 4], [220, 4, 119, 4], [220, 8, 119, 4, "useEffect"], [220, 24, 119, 13], [220, 26, 119, 14], [220, 32, 119, 20], [221, 6, 120, 8, "getInitialURL"], [221, 19, 120, 21], [221, 20, 120, 22], [221, 21, 120, 23], [221, 22, 120, 24, "then"], [221, 26, 120, 28], [221, 27, 120, 30, "url"], [221, 30, 120, 33], [221, 34, 120, 38, "setLink"], [221, 41, 120, 45], [221, 42, 120, 46, "url"], [221, 45, 120, 49], [221, 46, 120, 50], [221, 47, 120, 51], [222, 6, 121, 8], [222, 10, 121, 14, "subscription"], [222, 22, 121, 26], [222, 25, 121, 29, "addEventListener"], [222, 41, 121, 45], [222, 42, 121, 46], [222, 47, 121, 51], [222, 49, 121, 53, "onChange"], [222, 57, 121, 61], [222, 58, 121, 62], [223, 6, 122, 8], [223, 13, 122, 15], [223, 19, 122, 21, "subscription"], [223, 31, 122, 33], [223, 32, 122, 34, "remove"], [223, 38, 122, 40], [223, 39, 122, 41], [223, 40, 122, 42], [224, 4, 123, 4], [224, 5, 123, 5], [224, 7, 123, 7], [224, 9, 123, 9], [224, 10, 123, 10], [225, 4, 124, 4], [225, 11, 124, 11, "url"], [225, 14, 124, 14], [226, 2, 125, 0], [227, 2, 126, 0], [228, 0, 127, 0], [229, 0, 128, 0], [230, 0, 129, 0], [231, 0, 130, 0], [232, 2, 131, 7], [232, 11, 131, 16, "useLinkingURL"], [232, 24, 131, 29, "useLinkingURL"], [232, 25, 131, 29], [232, 27, 131, 32], [233, 4, 132, 4], [233, 8, 132, 4, "_useState3"], [233, 18, 132, 4], [233, 21, 132, 27], [233, 25, 132, 27, "useState"], [233, 40, 132, 35], [233, 42, 132, 36, "ExpoLinking"], [233, 62, 132, 47], [233, 63, 132, 48, "getLinkingURL"], [233, 76, 132, 61], [233, 77, 132, 62], [234, 6, 132, 62, "_useState4"], [234, 16, 132, 62], [234, 23, 132, 62, "_slicedToArray2"], [234, 38, 132, 62], [234, 39, 132, 62, "default"], [234, 46, 132, 62], [234, 48, 132, 62, "_useState3"], [234, 58, 132, 62], [235, 6, 132, 11, "url"], [235, 9, 132, 14], [235, 12, 132, 14, "_useState4"], [235, 22, 132, 14], [236, 6, 132, 16, "setLink"], [236, 13, 132, 23], [236, 16, 132, 23, "_useState4"], [236, 26, 132, 23], [237, 4, 133, 4], [237, 13, 133, 13, "onChange"], [237, 21, 133, 21, "onChange"], [237, 22, 133, 22, "event"], [237, 27, 133, 27], [237, 29, 133, 29], [238, 6, 134, 8, "setLink"], [238, 13, 134, 15], [238, 14, 134, 16, "event"], [238, 19, 134, 21], [238, 20, 134, 22, "url"], [238, 23, 134, 25], [238, 24, 134, 26], [239, 4, 135, 4], [240, 4, 136, 4], [240, 8, 136, 4, "useEffect"], [240, 24, 136, 13], [240, 26, 136, 14], [240, 32, 136, 20], [241, 6, 137, 8], [241, 10, 137, 14, "subscription"], [241, 22, 137, 26], [241, 25, 137, 29, "ExpoLinking"], [241, 45, 137, 40], [241, 46, 137, 41, "addListener"], [241, 57, 137, 52], [241, 58, 137, 53], [241, 73, 137, 68], [241, 75, 137, 70, "onChange"], [241, 83, 137, 78], [241, 84, 137, 79], [242, 6, 138, 8], [242, 13, 138, 15], [242, 19, 138, 21, "subscription"], [242, 31, 138, 33], [242, 32, 138, 34, "remove"], [242, 38, 138, 40], [242, 39, 138, 41], [242, 40, 138, 42], [243, 4, 139, 4], [243, 5, 139, 5], [243, 7, 139, 7], [243, 9, 139, 9], [243, 10, 139, 10], [244, 4, 140, 4], [244, 11, 140, 11, "url"], [244, 14, 140, 14], [244, 18, 140, 18], [244, 22, 140, 22], [245, 2, 141, 0], [246, 0, 141, 1], [246, 3]], "functionMap": {"names": ["<global>", "addEventListener", "parseInitialURLAsync", "sendIntent", "openSettings", "getInitialURL", "getLinkingURL", "openURL", "canOpenURL", "useURL", "onChange", "useEffect$argument_0", "getInitialURL.then$argument_0", "<anonymous>", "useLinkingURL"], "mappings": "AAA;OCiB;CDE;OES;CFW;OGQ;CHK;OIK;CJQ;OKM;CLE;OMK;CNE;OOU;CPG;OQW;CRG;OSM;ICE;KDE;cEC;6BCC,qBD;eEE,2BF;KFC;CTE;OcM;IJE;KIE;cHC;eEE,2BF;KGC;CdE"}}, "type": "js/module"}]}