{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 134}, "end": {"line": 4, "column": 32, "index": 150}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./color-scheme", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 23, "index": 175}, "end": {"line": 5, "column": 48, "index": 200}}], "key": "3cTpokJZH19SAka6qflkHBVT/AA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useColorScheme = useColorScheme;\n  const react_1 = require(_dependencyMap[0], \"react\");\n  const color_scheme_1 = require(_dependencyMap[1], \"./color-scheme\");\n  function useColorScheme() {\n    const [effect, setEffect] = (0, react_1.useState)(() => ({\n      run: () => setEffect(s => ({\n        ...s\n      })),\n      dependencies: new Set()\n    }));\n    return {\n      colorScheme: color_scheme_1.colorScheme.get(effect),\n      setColorScheme: color_scheme_1.colorScheme.set,\n      toggleColorScheme: color_scheme_1.colorScheme.toggle\n    };\n  }\n});", "lineCount": 23, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "useColorScheme"], [7, 24, 3, 22], [7, 27, 3, 25, "useColorScheme"], [7, 41, 3, 39], [8, 2, 4, 0], [8, 8, 4, 6, "react_1"], [8, 15, 4, 13], [8, 18, 4, 16, "require"], [8, 25, 4, 23], [8, 26, 4, 23, "_dependencyMap"], [8, 40, 4, 23], [8, 52, 4, 31], [8, 53, 4, 32], [9, 2, 5, 0], [9, 8, 5, 6, "color_scheme_1"], [9, 22, 5, 20], [9, 25, 5, 23, "require"], [9, 32, 5, 30], [9, 33, 5, 30, "_dependencyMap"], [9, 47, 5, 30], [9, 68, 5, 47], [9, 69, 5, 48], [10, 2, 6, 0], [10, 11, 6, 9, "useColorScheme"], [10, 25, 6, 23, "useColorScheme"], [10, 26, 6, 23], [10, 28, 6, 26], [11, 4, 7, 4], [11, 10, 7, 10], [11, 11, 7, 11, "effect"], [11, 17, 7, 17], [11, 19, 7, 19, "setEffect"], [11, 28, 7, 28], [11, 29, 7, 29], [11, 32, 7, 32], [11, 33, 7, 33], [11, 34, 7, 34], [11, 36, 7, 36, "react_1"], [11, 43, 7, 43], [11, 44, 7, 44, "useState"], [11, 52, 7, 52], [11, 54, 7, 54], [11, 61, 7, 61], [12, 6, 8, 8, "run"], [12, 9, 8, 11], [12, 11, 8, 13, "run"], [12, 12, 8, 13], [12, 17, 8, 19, "setEffect"], [12, 26, 8, 28], [12, 27, 8, 30, "s"], [12, 28, 8, 31], [12, 33, 8, 37], [13, 8, 8, 39], [13, 11, 8, 42, "s"], [14, 6, 8, 44], [14, 7, 8, 45], [14, 8, 8, 46], [14, 9, 8, 47], [15, 6, 9, 8, "dependencies"], [15, 18, 9, 20], [15, 20, 9, 22], [15, 24, 9, 26, "Set"], [15, 27, 9, 29], [15, 28, 9, 30], [16, 4, 10, 4], [16, 5, 10, 5], [16, 6, 10, 6], [16, 7, 10, 7], [17, 4, 11, 4], [17, 11, 11, 11], [18, 6, 12, 8, "colorScheme"], [18, 17, 12, 19], [18, 19, 12, 21, "color_scheme_1"], [18, 33, 12, 35], [18, 34, 12, 36, "colorScheme"], [18, 45, 12, 47], [18, 46, 12, 48, "get"], [18, 49, 12, 51], [18, 50, 12, 52, "effect"], [18, 56, 12, 58], [18, 57, 12, 59], [19, 6, 13, 8, "setColorScheme"], [19, 20, 13, 22], [19, 22, 13, 24, "color_scheme_1"], [19, 36, 13, 38], [19, 37, 13, 39, "colorScheme"], [19, 48, 13, 50], [19, 49, 13, 51, "set"], [19, 52, 13, 54], [20, 6, 14, 8, "toggleColorScheme"], [20, 23, 14, 25], [20, 25, 14, 27, "color_scheme_1"], [20, 39, 14, 41], [20, 40, 14, 42, "colorScheme"], [20, 51, 14, 53], [20, 52, 14, 54, "toggle"], [21, 4, 15, 4], [21, 5, 15, 5], [22, 2, 16, 0], [23, 0, 16, 1], [23, 3]], "functionMap": {"names": ["<global>", "useColorScheme", "<anonymous>", "run", "setEffect$argument_0"], "mappings": "AAA;ACK;sDCC;aCC,gBC,iBD,CD;MDE;CDM"}}, "type": "js/module"}]}