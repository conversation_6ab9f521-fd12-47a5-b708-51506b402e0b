{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2Fnode_modules%2F%40react-navigation%2Felements%2Flib%2Fmodule%2Fassets\",\n    \"width\": 50,\n    \"height\": 85,\n    \"scales\": [1],\n    \"hash\": \"0a328cd9c1afd0afe8e3b1ec5165b1b4\",\n    \"name\": \"back-icon-mask\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"0a328cd9c1afd0afe8e3b1ec5165b1b4\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 124, 1, 144], [5, 4, 1, 145], [5, 11, 1, 152], [5, 13, 1, 153], [5, 15, 1, 155], [6, 4, 1, 156], [6, 12, 1, 164], [6, 14, 1, 165], [6, 16, 1, 167], [7, 4, 1, 168], [7, 12, 1, 176], [7, 14, 1, 177], [7, 15, 1, 178], [7, 16, 1, 179], [7, 17, 1, 180], [8, 4, 1, 181], [8, 10, 1, 187], [8, 12, 1, 188], [8, 46, 1, 222], [9, 4, 1, 223], [9, 10, 1, 229], [9, 12, 1, 230], [9, 28, 1, 246], [10, 4, 1, 247], [10, 10, 1, 253], [10, 12, 1, 254], [10, 17, 1, 259], [11, 4, 1, 260], [11, 16, 1, 272], [11, 18, 1, 273], [11, 19, 1, 274], [11, 53, 1, 308], [12, 2, 1, 309], [12, 3, 1, 310], [13, 0, 1, 310], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}