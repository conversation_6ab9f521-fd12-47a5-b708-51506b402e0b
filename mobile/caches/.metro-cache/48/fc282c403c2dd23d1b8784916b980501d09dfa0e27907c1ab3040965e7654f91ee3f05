{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /**\n   * Copyright (c) <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var emptyFunction = () => {};\n  function StatusBar() {\n    return null;\n  }\n  StatusBar.setBackgroundColor = emptyFunction;\n  StatusBar.setBarStyle = emptyFunction;\n  StatusBar.setHidden = emptyFunction;\n  StatusBar.setNetworkActivityIndicatorVisible = emptyFunction;\n  StatusBar.setTranslucent = emptyFunction;\n  var _default = exports.default = StatusBar;\n});", "lineCount": 25, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [15, 2, 10, 0], [15, 6, 10, 4, "emptyFunction"], [15, 19, 10, 17], [15, 22, 10, 20, "emptyFunction"], [15, 23, 10, 20], [15, 28, 10, 26], [15, 29, 10, 27], [15, 30, 10, 28], [16, 2, 11, 0], [16, 11, 11, 9, "StatusBar"], [16, 20, 11, 18, "StatusBar"], [16, 21, 11, 18], [16, 23, 11, 21], [17, 4, 12, 2], [17, 11, 12, 9], [17, 15, 12, 13], [18, 2, 13, 0], [19, 2, 14, 0, "StatusBar"], [19, 11, 14, 9], [19, 12, 14, 10, "setBackgroundColor"], [19, 30, 14, 28], [19, 33, 14, 31, "emptyFunction"], [19, 46, 14, 44], [20, 2, 15, 0, "StatusBar"], [20, 11, 15, 9], [20, 12, 15, 10, "setBarStyle"], [20, 23, 15, 21], [20, 26, 15, 24, "emptyFunction"], [20, 39, 15, 37], [21, 2, 16, 0, "StatusBar"], [21, 11, 16, 9], [21, 12, 16, 10, "setHidden"], [21, 21, 16, 19], [21, 24, 16, 22, "emptyFunction"], [21, 37, 16, 35], [22, 2, 17, 0, "StatusBar"], [22, 11, 17, 9], [22, 12, 17, 10, "setNetworkActivityIndicatorVisible"], [22, 46, 17, 44], [22, 49, 17, 47, "emptyFunction"], [22, 62, 17, 60], [23, 2, 18, 0, "StatusBar"], [23, 11, 18, 9], [23, 12, 18, 10, "setTranslucent"], [23, 26, 18, 24], [23, 29, 18, 27, "emptyFunction"], [23, 42, 18, 40], [24, 2, 18, 41], [24, 6, 18, 41, "_default"], [24, 14, 18, 41], [24, 17, 18, 41, "exports"], [24, 24, 18, 41], [24, 25, 18, 41, "default"], [24, 32, 18, 41], [24, 35, 19, 15, "StatusBar"], [24, 44, 19, 24], [25, 0, 19, 24], [25, 3]], "functionMap": {"names": ["<global>", "emptyFunction", "StatusBar"], "mappings": "AAA;oBCS,QD;AEC;CFE"}}, "type": "js/module"}]}