{"dependencies": [{"name": "./measure", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 36, "index": 51}}], "key": "B5IHqoArBd4o1xEAyGBYJRJiEtY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getRelativeCoords = void 0;\n  var _measure = require(_dependencyMap[0], \"./measure\");\n  /** An object which contains relative coordinates. */\n  /**\n   * Lets you determines the location on the screen, relative to the given view.\n   *\n   * @param animatedRef - An [animated\n   *   ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef#returns)\n   *   connected to the component you'd want to get the coordinates from.\n   * @param absoluteX - A number which is an absolute x coordinate.\n   * @param absoluteY - A number which is an absolute y coordinate.\n   * @returns An object which contains relative coordinates -\n   *   {@link ComponentCoords}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/getRelativeCoords\n   */\n  const _worklet_5752716742403_init_data = {\n    code: \"function getRelativeCoords_reactNativeReanimated_getRelativeCoordsJs1(animatedRef,absoluteX,absoluteY){const{measure}=this.__closure;const parentCoords=measure(animatedRef);if(parentCoords===null){return null;}return{x:absoluteX-parentCoords.pageX,y:absoluteY-parentCoords.pageY};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/platformFunctions/getRelativeCoords.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getRelativeCoords_reactNativeReanimated_getRelativeCoordsJs1\\\",\\\"animatedRef\\\",\\\"absoluteX\\\",\\\"absoluteY\\\",\\\"measure\\\",\\\"__closure\\\",\\\"parentCoords\\\",\\\"x\\\",\\\"pageX\\\",\\\"y\\\",\\\"pageY\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/platformFunctions/getRelativeCoords.js\\\"],\\\"mappings\\\":\\\"AAkBO,SAAAA,4DAA8DA,CAAAC,WAAA,CAAAC,SAAA,CAAAC,SAAA,QAAAC,OAAA,OAAAC,SAAA,CAGnE,KAAM,CAAAC,YAAY,CAAGF,OAAO,CAACH,WAAW,CAAC,CACzC,GAAIK,YAAY,GAAK,IAAI,CAAE,CACzB,MAAO,KAAI,CACb,CACA,MAAO,CACLC,CAAC,CAAEL,SAAS,CAAGI,YAAY,CAACE,KAAK,CACjCC,CAAC,CAAEN,SAAS,CAAGG,YAAY,CAACI,KAC9B,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const getRelativeCoords = exports.getRelativeCoords = function () {\n    const _e = [new global.Error(), -2, -27];\n    const getRelativeCoords = function (animatedRef, absoluteX, absoluteY) {\n      const parentCoords = (0, _measure.measure)(animatedRef);\n      if (parentCoords === null) {\n        return null;\n      }\n      return {\n        x: absoluteX - parentCoords.pageX,\n        y: absoluteY - parentCoords.pageY\n      };\n    };\n    getRelativeCoords.__closure = {\n      measure: _measure.measure\n    };\n    getRelativeCoords.__workletHash = 5752716742403;\n    getRelativeCoords.__initData = _worklet_5752716742403_init_data;\n    getRelativeCoords.__stackDetails = _e;\n    return getRelativeCoords;\n  }();\n});", "lineCount": 48, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getRelativeCoords"], [7, 27, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_measure"], [8, 14, 3, 0], [8, 17, 3, 0, "require"], [8, 24, 3, 0], [8, 25, 3, 0, "_dependencyMap"], [8, 39, 3, 0], [9, 2, 5, 0], [10, 2, 7, 0], [11, 0, 8, 0], [12, 0, 9, 0], [13, 0, 10, 0], [14, 0, 11, 0], [15, 0, 12, 0], [16, 0, 13, 0], [17, 0, 14, 0], [18, 0, 15, 0], [19, 0, 16, 0], [20, 0, 17, 0], [21, 0, 18, 0], [22, 2, 7, 0], [22, 8, 7, 0, "_worklet_5752716742403_init_data"], [22, 40, 7, 0], [23, 4, 7, 0, "code"], [23, 8, 7, 0], [24, 4, 7, 0, "location"], [24, 12, 7, 0], [25, 4, 7, 0, "sourceMap"], [25, 13, 7, 0], [26, 4, 7, 0, "version"], [26, 11, 7, 0], [27, 2, 7, 0], [28, 2, 7, 0], [28, 8, 7, 0, "getRelativeCoords"], [28, 25, 7, 0], [28, 28, 7, 0, "exports"], [28, 35, 7, 0], [28, 36, 7, 0, "getRelativeCoords"], [28, 53, 7, 0], [28, 56, 19, 7], [29, 4, 19, 7], [29, 10, 19, 7, "_e"], [29, 12, 19, 7], [29, 20, 19, 7, "global"], [29, 26, 19, 7], [29, 27, 19, 7, "Error"], [29, 32, 19, 7], [30, 4, 19, 7], [30, 10, 19, 7, "getRelativeCoords"], [30, 27, 19, 7], [30, 39, 19, 7, "getRelativeCoords"], [30, 40, 19, 34, "animatedRef"], [30, 51, 19, 45], [30, 53, 19, 47, "absoluteX"], [30, 62, 19, 56], [30, 64, 19, 58, "absoluteY"], [30, 73, 19, 67], [30, 75, 19, 69], [31, 6, 22, 2], [31, 12, 22, 8, "parentCoords"], [31, 24, 22, 20], [31, 27, 22, 23], [31, 31, 22, 23, "measure"], [31, 47, 22, 30], [31, 49, 22, 31, "animatedRef"], [31, 60, 22, 42], [31, 61, 22, 43], [32, 6, 23, 2], [32, 10, 23, 6, "parentCoords"], [32, 22, 23, 18], [32, 27, 23, 23], [32, 31, 23, 27], [32, 33, 23, 29], [33, 8, 24, 4], [33, 15, 24, 11], [33, 19, 24, 15], [34, 6, 25, 2], [35, 6, 26, 2], [35, 13, 26, 9], [36, 8, 27, 4, "x"], [36, 9, 27, 5], [36, 11, 27, 7, "absoluteX"], [36, 20, 27, 16], [36, 23, 27, 19, "parentCoords"], [36, 35, 27, 31], [36, 36, 27, 32, "pageX"], [36, 41, 27, 37], [37, 8, 28, 4, "y"], [37, 9, 28, 5], [37, 11, 28, 7, "absoluteY"], [37, 20, 28, 16], [37, 23, 28, 19, "parentCoords"], [37, 35, 28, 31], [37, 36, 28, 32, "pageY"], [38, 6, 29, 2], [38, 7, 29, 3], [39, 4, 30, 0], [39, 5, 30, 1], [40, 4, 30, 1, "getRelativeCoords"], [40, 21, 30, 1], [40, 22, 30, 1, "__closure"], [40, 31, 30, 1], [41, 6, 30, 1, "measure"], [41, 13, 30, 1], [41, 15, 22, 23, "measure"], [42, 4, 22, 30], [43, 4, 22, 30, "getRelativeCoords"], [43, 21, 22, 30], [43, 22, 22, 30, "__workletHash"], [43, 35, 22, 30], [44, 4, 22, 30, "getRelativeCoords"], [44, 21, 22, 30], [44, 22, 22, 30, "__initData"], [44, 32, 22, 30], [44, 35, 22, 30, "_worklet_5752716742403_init_data"], [44, 67, 22, 30], [45, 4, 22, 30, "getRelativeCoords"], [45, 21, 22, 30], [45, 22, 22, 30, "__stackDetails"], [45, 36, 22, 30], [45, 39, 22, 30, "_e"], [45, 41, 22, 30], [46, 4, 22, 30], [46, 11, 22, 30, "getRelativeCoords"], [46, 28, 22, 30], [47, 2, 22, 30], [47, 3, 19, 7], [48, 0, 19, 7], [48, 3]], "functionMap": {"names": ["<global>", "getRelativeCoords"], "mappings": "AAA;OCkB;CDW"}}, "type": "js/module"}]}