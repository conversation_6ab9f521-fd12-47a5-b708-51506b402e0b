{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 122}, "end": {"line": 4, "column": 32, "index": 138}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./imperative-api", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 25, "index": 165}, "end": {"line": 5, "column": 52, "index": 192}}], "key": "2Of+bQUTIvR7p6d/TD+6pd79qeA=", "exportNames": ["*"]}}, {"name": "./link/useLoadedNavigation", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 30, "index": 224}, "end": {"line": 6, "column": 67, "index": 261}}], "key": "F42A3JJbwzNAuJruL45BLASvOHI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Prefetch = Prefetch;\n  var react_1 = require(_dependencyMap[0], \"react\");\n  var imperative_api_1 = require(_dependencyMap[1], \"./imperative-api\");\n  var useLoadedNavigation_1 = require(_dependencyMap[2], \"./link/useLoadedNavigation\");\n  /**\n   * When rendered on a focused screen, this component will preload the specified route.\n   */\n  function Prefetch(props) {\n    var navigation = (0, useLoadedNavigation_1.useOptionalNavigation)();\n    (0, react_1.useLayoutEffect)(() => {\n      if (navigation?.isFocused()) {\n        imperative_api_1.router.prefetch(props.href);\n      }\n    }, [navigation, props.href]);\n    return null;\n  }\n});", "lineCount": 23, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "Prefetch"], [7, 18, 3, 16], [7, 21, 3, 19, "Prefetch"], [7, 29, 3, 27], [8, 2, 4, 0], [8, 6, 4, 6, "react_1"], [8, 13, 4, 13], [8, 16, 4, 16, "require"], [8, 23, 4, 23], [8, 24, 4, 23, "_dependencyMap"], [8, 38, 4, 23], [8, 50, 4, 31], [8, 51, 4, 32], [9, 2, 5, 0], [9, 6, 5, 6, "imperative_api_1"], [9, 22, 5, 22], [9, 25, 5, 25, "require"], [9, 32, 5, 32], [9, 33, 5, 32, "_dependencyMap"], [9, 47, 5, 32], [9, 70, 5, 51], [9, 71, 5, 52], [10, 2, 6, 0], [10, 6, 6, 6, "useLoadedNavigation_1"], [10, 27, 6, 27], [10, 30, 6, 30, "require"], [10, 37, 6, 37], [10, 38, 6, 37, "_dependencyMap"], [10, 52, 6, 37], [10, 85, 6, 66], [10, 86, 6, 67], [11, 2, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 2, 10, 0], [14, 11, 10, 9, "Prefetch"], [14, 19, 10, 17, "Prefetch"], [14, 20, 10, 18, "props"], [14, 25, 10, 23], [14, 27, 10, 25], [15, 4, 11, 4], [15, 8, 11, 10, "navigation"], [15, 18, 11, 20], [15, 21, 11, 23], [15, 22, 11, 24], [15, 23, 11, 25], [15, 25, 11, 27, "useLoadedNavigation_1"], [15, 46, 11, 48], [15, 47, 11, 49, "useOptionalNavigation"], [15, 68, 11, 70], [15, 70, 11, 72], [15, 71, 11, 73], [16, 4, 12, 4], [16, 5, 12, 5], [16, 6, 12, 6], [16, 8, 12, 8, "react_1"], [16, 15, 12, 15], [16, 16, 12, 16, "useLayoutEffect"], [16, 31, 12, 31], [16, 33, 12, 33], [16, 39, 12, 39], [17, 6, 13, 8], [17, 10, 13, 12, "navigation"], [17, 20, 13, 22], [17, 22, 13, 24, "isFocused"], [17, 31, 13, 33], [17, 32, 13, 34], [17, 33, 13, 35], [17, 35, 13, 37], [18, 8, 14, 12, "imperative_api_1"], [18, 24, 14, 28], [18, 25, 14, 29, "router"], [18, 31, 14, 35], [18, 32, 14, 36, "prefetch"], [18, 40, 14, 44], [18, 41, 14, 45, "props"], [18, 46, 14, 50], [18, 47, 14, 51, "href"], [18, 51, 14, 55], [18, 52, 14, 56], [19, 6, 15, 8], [20, 4, 16, 4], [20, 5, 16, 5], [20, 7, 16, 7], [20, 8, 16, 8, "navigation"], [20, 18, 16, 18], [20, 20, 16, 20, "props"], [20, 25, 16, 25], [20, 26, 16, 26, "href"], [20, 30, 16, 30], [20, 31, 16, 31], [20, 32, 16, 32], [21, 4, 17, 4], [21, 11, 17, 11], [21, 15, 17, 15], [22, 2, 18, 0], [23, 0, 18, 1], [23, 3]], "functionMap": {"names": ["<global>", "Prefetch", "<anonymous>"], "mappings": "AAA;ACS;iCCE;KDI;CDE"}}, "type": "js/module"}]}