{"dependencies": [{"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 17, "index": 488}, "end": {"line": 12, "column": 52, "index": 523}}], "key": "uE+cRVNnMKkS9OYKR5fpRqPul5s=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 32, "index": 557}, "end": {"line": 13, "column": 48, "index": 573}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./Route", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 16, "index": 592}, "end": {"line": 14, "column": 34, "index": 610}}], "key": "Uzycn6ZxigdYY0vHqZHurWeuVzU=", "exportNames": ["*"]}}, {"name": "./global-state/storeContext", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 23, "index": 635}, "end": {"line": 15, "column": 61, "index": 673}}], "key": "H9kAQWn5vvh2N/2zS+6/rjBbj/A=", "exportNames": ["*"]}}, {"name": "./import-mode", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 38, "index": 713}, "end": {"line": 16, "column": 62, "index": 737}}], "key": "Zp4co0XTsqwv6MnreOBj3Uv4K5M=", "exportNames": ["*"]}}, {"name": "./primitives", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 21, "index": 761}, "end": {"line": 17, "column": 44, "index": 784}}], "key": "0z9G1cn27A64H/PM6zfVli9J28w=", "exportNames": ["*"]}}, {"name": "./views/EmptyRoute", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 21, "index": 807}, "end": {"line": 18, "column": 50, "index": 836}}], "key": "ScFIxEFlTkvrOxVomKrlP17QHyo=", "exportNames": ["*"]}}, {"name": "./views/SuspenseFallback", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 27, "index": 865}, "end": {"line": 19, "column": 62, "index": 900}}], "key": "FRgCYumK3b69ruh7wzVtET036zU=", "exportNames": ["*"]}}, {"name": "./views/Try", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 20, "column": 14, "index": 916}, "end": {"line": 20, "column": 36, "index": 938}}], "key": "eEtfgnx1sCJM5uSGWZgSz6MRTAI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  var _reactNativeCssInterop = require(_dependencyMap[0], \"react-native-css-interop\");\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[1], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/useScreens.js\";\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useSortedScreens = useSortedScreens;\n  exports.getQualifiedRouteComponent = getQualifiedRouteComponent;\n  exports.screenOptionsFactory = screenOptionsFactory;\n  exports.routeToScreen = routeToScreen;\n  exports.getSingularId = getSingularId;\n  const native_1 = require(_dependencyMap[2], \"@react-navigation/native\");\n  const react_1 = __importDefault(require(_dependencyMap[3], \"react\"));\n  const Route_1 = require(_dependencyMap[4], \"./Route\");\n  const storeContext_1 = require(_dependencyMap[5], \"./global-state/storeContext\");\n  const import_mode_1 = __importDefault(require(_dependencyMap[6], \"./import-mode\"));\n  const primitives_1 = require(_dependencyMap[7], \"./primitives\");\n  const EmptyRoute_1 = require(_dependencyMap[8], \"./views/EmptyRoute\");\n  const SuspenseFallback_1 = require(_dependencyMap[9], \"./views/SuspenseFallback\");\n  const Try_1 = require(_dependencyMap[10], \"./views/Try\");\n  function getSortedChildren(children, order = [], initialRouteName) {\n    if (!order?.length) {\n      return children.sort((0, Route_1.sortRoutesWithInitial)(initialRouteName)).map(route => ({\n        route,\n        props: {}\n      }));\n    }\n    const entries = [...children];\n    const ordered = order.map(({\n      name,\n      redirect,\n      initialParams,\n      listeners,\n      options,\n      getId,\n      dangerouslySingular: singular\n    }) => {\n      if (!entries.length) {\n        console.warn(`[Layout children]: Too many screens defined. Route \"${name}\" is extraneous.`);\n        return null;\n      }\n      const matchIndex = entries.findIndex(child => child.route === name);\n      if (matchIndex === -1) {\n        console.warn(`[Layout children]: No route named \"${name}\" exists in nested children:`, children.map(({\n          route\n        }) => route));\n        return null;\n      } else {\n        // Get match and remove from entries\n        const match = entries[matchIndex];\n        entries.splice(matchIndex, 1);\n        // Ensure to return null after removing from entries.\n        if (redirect) {\n          if (typeof redirect === 'string') {\n            throw new Error(`Redirecting to a specific route is not supported yet.`);\n          }\n          return null;\n        }\n        if (getId) {\n          console.warn(`Deprecated: prop 'getId' on screen ${name} is deprecated. Please rename the prop to 'dangerouslySingular'`);\n          if (singular) {\n            console.warn(`Screen ${name} cannot use both getId and dangerouslySingular together.`);\n          }\n        } else if (singular) {\n          // If singular is set, use it as the getId function.\n          if (typeof singular === 'string') {\n            getId = () => singular;\n          } else if (typeof singular === 'function' && name) {\n            getId = options => singular(name, options.params || {});\n          } else if (singular === true && name) {\n            getId = options => getSingularId(name, options);\n          }\n        }\n        return {\n          route: match,\n          props: {\n            initialParams,\n            listeners,\n            options,\n            getId\n          }\n        };\n      }\n    }).filter(Boolean);\n    // Add any remaining children\n    ordered.push(...entries.sort((0, Route_1.sortRoutesWithInitial)(initialRouteName)).map(route => ({\n      route,\n      props: {}\n    })));\n    return ordered;\n  }\n  /**\n   * @returns React Navigation screens sorted by the `route` property.\n   */\n  function useSortedScreens(order, protectedScreens) {\n    const node = (0, Route_1.useRouteNode)();\n    const sorted = node?.children?.length ? getSortedChildren(node.children, order, node.initialRouteName) : [];\n    return react_1.default.useMemo(() => sorted.filter(item => !protectedScreens.has(item.route.route)).map(value => {\n      return routeToScreen(value.route, value.props);\n    }), [sorted, protectedScreens]);\n  }\n  function fromImport(value, {\n    ErrorBoundary,\n    ...component\n  }) {\n    // If possible, add a more helpful display name for the component stack to improve debugging of React errors such as `Text strings must be rendered within a <Text> component.`.\n    if (component?.default && __DEV__) {\n      component.default.displayName ??= `${component.default.name ?? 'Route'}(${value.contextKey})`;\n    }\n    if (ErrorBoundary) {\n      const Wrapped = react_1.default.forwardRef((props, ref) => {\n        const children = react_1.default.createElement(component.default || EmptyRoute_1.EmptyRoute, {\n          ...props,\n          ref\n        });\n        return _reactNativeCssInteropJsxRuntime.jsx(Try_1.Try, {\n          catch: ErrorBoundary,\n          children: children\n        });\n      });\n      if (__DEV__) {\n        Wrapped.displayName = `ErrorBoundary(${value.contextKey})`;\n      }\n      return {\n        default: Wrapped\n      };\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof component.default === 'object' && component.default && Object.keys(component.default).length === 0) {\n        return {\n          default: EmptyRoute_1.EmptyRoute\n        };\n      }\n    }\n    return {\n      default: component.default\n    };\n  }\n  function fromLoadedRoute(value, res) {\n    if (!(res instanceof Promise)) {\n      return fromImport(value, res);\n    }\n    return res.then(fromImport.bind(null, value));\n  }\n  // TODO: Maybe there's a more React-y way to do this?\n  // Without this store, the process enters a recursive loop.\n  const qualifiedStore = new WeakMap();\n  /** Wrap the component with various enhancements and add access to child routes. */\n  function getQualifiedRouteComponent(value) {\n    if (qualifiedStore.has(value)) {\n      return qualifiedStore.get(value);\n    }\n    let ScreenComponent;\n    // TODO: This ensures sync doesn't use React.lazy, but it's not ideal.\n    if (import_mode_1.default === 'lazy') {\n      ScreenComponent = react_1.default.lazy(async () => {\n        const res = value.loadRoute();\n        return fromLoadedRoute(value, res);\n      });\n      if (__DEV__) {\n        ScreenComponent.displayName = `AsyncRoute(${value.route})`;\n      }\n    } else {\n      const res = value.loadRoute();\n      ScreenComponent = fromImport(value, res).default;\n    }\n    function BaseRoute({\n      // Remove these React Navigation props to\n      // enforce usage of expo-router hooks (where the query params are correct).\n      route,\n      navigation,\n      // Pass all other props to the component\n      ...props\n    }) {\n      const stateForPath = (0, native_1.useStateForPath)();\n      const isFocused = (0, native_1.useIsFocused)();\n      const store = (0, storeContext_1.useExpoRouterStore)();\n      if (isFocused) {\n        const state = navigation.getState();\n        const isLeaf = !('state' in state.routes[state.index]);\n        if (isLeaf && stateForPath) store.setFocusedState(stateForPath);\n      }\n      return _reactNativeCssInteropJsxRuntime.jsx(Route_1.Route, {\n        node: value,\n        route: route,\n        children: _reactNativeCssInteropJsxRuntime.jsx(react_1.default.Suspense, {\n          fallback: _reactNativeCssInteropJsxRuntime.jsx(SuspenseFallback_1.SuspenseFallback, {\n            route: value\n          }),\n          children: _reactNativeCssInteropJsxRuntime.jsx(ScreenComponent, {\n            ...props,\n            // Expose the template segment path, e.g. `(home)`, `[foo]`, `index`\n            // the intention is to make it possible to deduce shared routes.\n            segment: value.route\n          })\n        })\n      });\n    }\n    if (__DEV__) {\n      BaseRoute.displayName = `Route(${value.route})`;\n    }\n    qualifiedStore.set(value, BaseRoute);\n    return BaseRoute;\n  }\n  function screenOptionsFactory(route, options) {\n    return args => {\n      // Only eager load generated components\n      const staticOptions = route.generated ? route.loadRoute()?.getNavOptions : null;\n      const staticResult = typeof staticOptions === 'function' ? staticOptions(args) : staticOptions;\n      const dynamicResult = typeof options === 'function' ? options?.(args) : options;\n      const output = {\n        ...staticResult,\n        ...dynamicResult\n      };\n      // Prevent generated screens from showing up in the tab bar.\n      if (route.generated) {\n        output.tabBarItemStyle = {\n          display: 'none'\n        };\n        output.tabBarButton = () => null;\n        // TODO: React Navigation doesn't provide a way to prevent rendering the drawer item.\n        output.drawerItemStyle = {\n          height: 0,\n          display: 'none'\n        };\n      }\n      return output;\n    };\n  }\n  function routeToScreen(route, {\n    options,\n    getId,\n    ...props\n  } = {}) {\n    return _reactNativeCssInterop.createElement(primitives_1.Screen, {\n      ...props,\n      name: route.route,\n      key: route.route,\n      getId: getId,\n      options: screenOptionsFactory(route, options),\n      getComponent: () => getQualifiedRouteComponent(route),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 13\n      }\n    });\n  }\n  function getSingularId(name, options = {}) {\n    return name.split('/').map(segment => {\n      if (segment.startsWith('[...')) {\n        return options.params?.[segment.slice(4, -1)]?.join('/') || segment;\n      } else if (segment.startsWith('[')) {\n        return options.params?.[segment.slice(1, -1)] || segment;\n      } else {\n        return segment;\n      }\n    }).join('/');\n  }\n});", "lineCount": 270, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_reactNativeCssInterop"], [5, 28, 2, 13], [5, 31, 2, 13, "require"], [5, 38, 2, 13], [5, 39, 2, 13, "_dependencyMap"], [5, 53, 2, 13], [6, 2, 2, 13], [6, 6, 2, 13, "_reactNativeCssInteropJsxRuntime"], [6, 38, 2, 13], [6, 41, 2, 13, "require"], [6, 48, 2, 13], [6, 49, 2, 13, "_dependencyMap"], [6, 63, 2, 13], [7, 2, 2, 13], [7, 6, 2, 13, "_jsxFileName"], [7, 18, 2, 13], [8, 2, 3, 0], [8, 6, 3, 4, "__importDefault"], [8, 21, 3, 19], [8, 24, 3, 23], [8, 28, 3, 27], [8, 32, 3, 31], [8, 36, 3, 35], [8, 37, 3, 36, "__importDefault"], [8, 52, 3, 51], [8, 56, 3, 56], [8, 66, 3, 66, "mod"], [8, 69, 3, 69], [8, 71, 3, 71], [9, 4, 4, 4], [9, 11, 4, 12, "mod"], [9, 14, 4, 15], [9, 18, 4, 19, "mod"], [9, 21, 4, 22], [9, 22, 4, 23, "__esModule"], [9, 32, 4, 33], [9, 35, 4, 37, "mod"], [9, 38, 4, 40], [9, 41, 4, 43], [10, 6, 4, 45], [10, 15, 4, 54], [10, 17, 4, 56, "mod"], [11, 4, 4, 60], [11, 5, 4, 61], [12, 2, 5, 0], [12, 3, 5, 1], [13, 2, 6, 0, "Object"], [13, 8, 6, 6], [13, 9, 6, 7, "defineProperty"], [13, 23, 6, 21], [13, 24, 6, 22, "exports"], [13, 31, 6, 29], [13, 33, 6, 31], [13, 45, 6, 43], [13, 47, 6, 45], [14, 4, 6, 47, "value"], [14, 9, 6, 52], [14, 11, 6, 54], [15, 2, 6, 59], [15, 3, 6, 60], [15, 4, 6, 61], [16, 2, 7, 0, "exports"], [16, 9, 7, 7], [16, 10, 7, 8, "useSortedScreens"], [16, 26, 7, 24], [16, 29, 7, 27, "useSortedScreens"], [16, 45, 7, 43], [17, 2, 8, 0, "exports"], [17, 9, 8, 7], [17, 10, 8, 8, "getQualifiedRouteComponent"], [17, 36, 8, 34], [17, 39, 8, 37, "getQualifiedRouteComponent"], [17, 65, 8, 63], [18, 2, 9, 0, "exports"], [18, 9, 9, 7], [18, 10, 9, 8, "screenOptionsFactory"], [18, 30, 9, 28], [18, 33, 9, 31, "screenOptionsFactory"], [18, 53, 9, 51], [19, 2, 10, 0, "exports"], [19, 9, 10, 7], [19, 10, 10, 8, "routeToScreen"], [19, 23, 10, 21], [19, 26, 10, 24, "routeToScreen"], [19, 39, 10, 37], [20, 2, 11, 0, "exports"], [20, 9, 11, 7], [20, 10, 11, 8, "getSingularId"], [20, 23, 11, 21], [20, 26, 11, 24, "getSingularId"], [20, 39, 11, 37], [21, 2, 12, 0], [21, 8, 12, 6, "native_1"], [21, 16, 12, 14], [21, 19, 12, 17, "require"], [21, 26, 12, 24], [21, 27, 12, 24, "_dependencyMap"], [21, 41, 12, 24], [21, 72, 12, 51], [21, 73, 12, 52], [22, 2, 13, 0], [22, 8, 13, 6, "react_1"], [22, 15, 13, 13], [22, 18, 13, 16, "__importDefault"], [22, 33, 13, 31], [22, 34, 13, 32, "require"], [22, 41, 13, 39], [22, 42, 13, 39, "_dependencyMap"], [22, 56, 13, 39], [22, 68, 13, 47], [22, 69, 13, 48], [22, 70, 13, 49], [23, 2, 14, 0], [23, 8, 14, 6, "Route_1"], [23, 15, 14, 13], [23, 18, 14, 16, "require"], [23, 25, 14, 23], [23, 26, 14, 23, "_dependencyMap"], [23, 40, 14, 23], [23, 54, 14, 33], [23, 55, 14, 34], [24, 2, 15, 0], [24, 8, 15, 6, "storeContext_1"], [24, 22, 15, 20], [24, 25, 15, 23, "require"], [24, 32, 15, 30], [24, 33, 15, 30, "_dependencyMap"], [24, 47, 15, 30], [24, 81, 15, 60], [24, 82, 15, 61], [25, 2, 16, 0], [25, 8, 16, 6, "import_mode_1"], [25, 21, 16, 19], [25, 24, 16, 22, "__importDefault"], [25, 39, 16, 37], [25, 40, 16, 38, "require"], [25, 47, 16, 45], [25, 48, 16, 45, "_dependencyMap"], [25, 62, 16, 45], [25, 82, 16, 61], [25, 83, 16, 62], [25, 84, 16, 63], [26, 2, 17, 0], [26, 8, 17, 6, "primitives_1"], [26, 20, 17, 18], [26, 23, 17, 21, "require"], [26, 30, 17, 28], [26, 31, 17, 28, "_dependencyMap"], [26, 45, 17, 28], [26, 64, 17, 43], [26, 65, 17, 44], [27, 2, 18, 0], [27, 8, 18, 6, "EmptyRoute_1"], [27, 20, 18, 18], [27, 23, 18, 21, "require"], [27, 30, 18, 28], [27, 31, 18, 28, "_dependencyMap"], [27, 45, 18, 28], [27, 70, 18, 49], [27, 71, 18, 50], [28, 2, 19, 0], [28, 8, 19, 6, "SuspenseFallback_1"], [28, 26, 19, 24], [28, 29, 19, 27, "require"], [28, 36, 19, 34], [28, 37, 19, 34, "_dependencyMap"], [28, 51, 19, 34], [28, 82, 19, 61], [28, 83, 19, 62], [29, 2, 20, 0], [29, 8, 20, 6, "Try_1"], [29, 13, 20, 11], [29, 16, 20, 14, "require"], [29, 23, 20, 21], [29, 24, 20, 21, "_dependencyMap"], [29, 38, 20, 21], [29, 57, 20, 35], [29, 58, 20, 36], [30, 2, 21, 0], [30, 11, 21, 9, "getS<PERSON><PERSON><PERSON><PERSON><PERSON>"], [30, 28, 21, 26, "getS<PERSON><PERSON><PERSON><PERSON><PERSON>"], [30, 29, 21, 27, "children"], [30, 37, 21, 35], [30, 39, 21, 37, "order"], [30, 44, 21, 42], [30, 47, 21, 45], [30, 49, 21, 47], [30, 51, 21, 49, "initialRouteName"], [30, 67, 21, 65], [30, 69, 21, 67], [31, 4, 22, 4], [31, 8, 22, 8], [31, 9, 22, 9, "order"], [31, 14, 22, 14], [31, 16, 22, 16, "length"], [31, 22, 22, 22], [31, 24, 22, 24], [32, 6, 23, 8], [32, 13, 23, 15, "children"], [32, 21, 23, 23], [32, 22, 24, 13, "sort"], [32, 26, 24, 17], [32, 27, 24, 18], [32, 28, 24, 19], [32, 29, 24, 20], [32, 31, 24, 22, "Route_1"], [32, 38, 24, 29], [32, 39, 24, 30, "sortRoutesWithInitial"], [32, 60, 24, 51], [32, 62, 24, 53, "initialRouteName"], [32, 78, 24, 69], [32, 79, 24, 70], [32, 80, 24, 71], [32, 81, 25, 13, "map"], [32, 84, 25, 16], [32, 85, 25, 18, "route"], [32, 90, 25, 23], [32, 95, 25, 29], [33, 8, 25, 31, "route"], [33, 13, 25, 36], [34, 8, 25, 38, "props"], [34, 13, 25, 43], [34, 15, 25, 45], [34, 16, 25, 46], [35, 6, 25, 48], [35, 7, 25, 49], [35, 8, 25, 50], [35, 9, 25, 51], [36, 4, 26, 4], [37, 4, 27, 4], [37, 10, 27, 10, "entries"], [37, 17, 27, 17], [37, 20, 27, 20], [37, 21, 27, 21], [37, 24, 27, 24, "children"], [37, 32, 27, 32], [37, 33, 27, 33], [38, 4, 28, 4], [38, 10, 28, 10, "ordered"], [38, 17, 28, 17], [38, 20, 28, 20, "order"], [38, 25, 28, 25], [38, 26, 29, 9, "map"], [38, 29, 29, 12], [38, 30, 29, 13], [38, 31, 29, 14], [39, 6, 29, 16, "name"], [39, 10, 29, 20], [40, 6, 29, 22, "redirect"], [40, 14, 29, 30], [41, 6, 29, 32, "initialParams"], [41, 19, 29, 45], [42, 6, 29, 47, "listeners"], [42, 15, 29, 56], [43, 6, 29, 58, "options"], [43, 13, 29, 65], [44, 6, 29, 67, "getId"], [44, 11, 29, 72], [45, 6, 29, 74, "dangerouslySingular"], [45, 25, 29, 93], [45, 27, 29, 95, "singular"], [46, 4, 29, 105], [46, 5, 29, 106], [46, 10, 29, 111], [47, 6, 30, 8], [47, 10, 30, 12], [47, 11, 30, 13, "entries"], [47, 18, 30, 20], [47, 19, 30, 21, "length"], [47, 25, 30, 27], [47, 27, 30, 29], [48, 8, 31, 12, "console"], [48, 15, 31, 19], [48, 16, 31, 20, "warn"], [48, 20, 31, 24], [48, 21, 31, 25], [48, 76, 31, 80, "name"], [48, 80, 31, 84], [48, 98, 31, 102], [48, 99, 31, 103], [49, 8, 32, 12], [49, 15, 32, 19], [49, 19, 32, 23], [50, 6, 33, 8], [51, 6, 34, 8], [51, 12, 34, 14, "matchIndex"], [51, 22, 34, 24], [51, 25, 34, 27, "entries"], [51, 32, 34, 34], [51, 33, 34, 35, "findIndex"], [51, 42, 34, 44], [51, 43, 34, 46, "child"], [51, 48, 34, 51], [51, 52, 34, 56, "child"], [51, 57, 34, 61], [51, 58, 34, 62, "route"], [51, 63, 34, 67], [51, 68, 34, 72, "name"], [51, 72, 34, 76], [51, 73, 34, 77], [52, 6, 35, 8], [52, 10, 35, 12, "matchIndex"], [52, 20, 35, 22], [52, 25, 35, 27], [52, 26, 35, 28], [52, 27, 35, 29], [52, 29, 35, 31], [53, 8, 36, 12, "console"], [53, 15, 36, 19], [53, 16, 36, 20, "warn"], [53, 20, 36, 24], [53, 21, 36, 25], [53, 59, 36, 63, "name"], [53, 63, 36, 67], [53, 93, 36, 97], [53, 95, 36, 99, "children"], [53, 103, 36, 107], [53, 104, 36, 108, "map"], [53, 107, 36, 111], [53, 108, 36, 112], [53, 109, 36, 113], [54, 10, 36, 115, "route"], [55, 8, 36, 121], [55, 9, 36, 122], [55, 14, 36, 127, "route"], [55, 19, 36, 132], [55, 20, 36, 133], [55, 21, 36, 134], [56, 8, 37, 12], [56, 15, 37, 19], [56, 19, 37, 23], [57, 6, 38, 8], [57, 7, 38, 9], [57, 13, 39, 13], [58, 8, 40, 12], [59, 8, 41, 12], [59, 14, 41, 18, "match"], [59, 19, 41, 23], [59, 22, 41, 26, "entries"], [59, 29, 41, 33], [59, 30, 41, 34, "matchIndex"], [59, 40, 41, 44], [59, 41, 41, 45], [60, 8, 42, 12, "entries"], [60, 15, 42, 19], [60, 16, 42, 20, "splice"], [60, 22, 42, 26], [60, 23, 42, 27, "matchIndex"], [60, 33, 42, 37], [60, 35, 42, 39], [60, 36, 42, 40], [60, 37, 42, 41], [61, 8, 43, 12], [62, 8, 44, 12], [62, 12, 44, 16, "redirect"], [62, 20, 44, 24], [62, 22, 44, 26], [63, 10, 45, 16], [63, 14, 45, 20], [63, 21, 45, 27, "redirect"], [63, 29, 45, 35], [63, 34, 45, 40], [63, 42, 45, 48], [63, 44, 45, 50], [64, 12, 46, 20], [64, 18, 46, 26], [64, 22, 46, 30, "Error"], [64, 27, 46, 35], [64, 28, 46, 36], [64, 83, 46, 91], [64, 84, 46, 92], [65, 10, 47, 16], [66, 10, 48, 16], [66, 17, 48, 23], [66, 21, 48, 27], [67, 8, 49, 12], [68, 8, 50, 12], [68, 12, 50, 16, "getId"], [68, 17, 50, 21], [68, 19, 50, 23], [69, 10, 51, 16, "console"], [69, 17, 51, 23], [69, 18, 51, 24, "warn"], [69, 22, 51, 28], [69, 23, 51, 29], [69, 61, 51, 67, "name"], [69, 65, 51, 71], [69, 130, 51, 136], [69, 131, 51, 137], [70, 10, 52, 16], [70, 14, 52, 20, "singular"], [70, 22, 52, 28], [70, 24, 52, 30], [71, 12, 53, 20, "console"], [71, 19, 53, 27], [71, 20, 53, 28, "warn"], [71, 24, 53, 32], [71, 25, 53, 33], [71, 35, 53, 43, "name"], [71, 39, 53, 47], [71, 97, 53, 105], [71, 98, 53, 106], [72, 10, 54, 16], [73, 8, 55, 12], [73, 9, 55, 13], [73, 15, 56, 17], [73, 19, 56, 21, "singular"], [73, 27, 56, 29], [73, 29, 56, 31], [74, 10, 57, 16], [75, 10, 58, 16], [75, 14, 58, 20], [75, 21, 58, 27, "singular"], [75, 29, 58, 35], [75, 34, 58, 40], [75, 42, 58, 48], [75, 44, 58, 50], [76, 12, 59, 20, "getId"], [76, 17, 59, 25], [76, 20, 59, 28, "getId"], [76, 21, 59, 28], [76, 26, 59, 34, "singular"], [76, 34, 59, 42], [77, 10, 60, 16], [77, 11, 60, 17], [77, 17, 61, 21], [77, 21, 61, 25], [77, 28, 61, 32, "singular"], [77, 36, 61, 40], [77, 41, 61, 45], [77, 51, 61, 55], [77, 55, 61, 59, "name"], [77, 59, 61, 63], [77, 61, 61, 65], [78, 12, 62, 20, "getId"], [78, 17, 62, 25], [78, 20, 62, 29, "options"], [78, 27, 62, 36], [78, 31, 62, 41, "singular"], [78, 39, 62, 49], [78, 40, 62, 50, "name"], [78, 44, 62, 54], [78, 46, 62, 56, "options"], [78, 53, 62, 63], [78, 54, 62, 64, "params"], [78, 60, 62, 70], [78, 64, 62, 74], [78, 65, 62, 75], [78, 66, 62, 76], [78, 67, 62, 77], [79, 10, 63, 16], [79, 11, 63, 17], [79, 17, 64, 21], [79, 21, 64, 25, "singular"], [79, 29, 64, 33], [79, 34, 64, 38], [79, 38, 64, 42], [79, 42, 64, 46, "name"], [79, 46, 64, 50], [79, 48, 64, 52], [80, 12, 65, 20, "getId"], [80, 17, 65, 25], [80, 20, 65, 29, "options"], [80, 27, 65, 36], [80, 31, 65, 41, "getSingularId"], [80, 44, 65, 54], [80, 45, 65, 55, "name"], [80, 49, 65, 59], [80, 51, 65, 61, "options"], [80, 58, 65, 68], [80, 59, 65, 69], [81, 10, 66, 16], [82, 8, 67, 12], [83, 8, 68, 12], [83, 15, 68, 19], [84, 10, 69, 16, "route"], [84, 15, 69, 21], [84, 17, 69, 23, "match"], [84, 22, 69, 28], [85, 10, 70, 16, "props"], [85, 15, 70, 21], [85, 17, 70, 23], [86, 12, 70, 25, "initialParams"], [86, 25, 70, 38], [87, 12, 70, 40, "listeners"], [87, 21, 70, 49], [88, 12, 70, 51, "options"], [88, 19, 70, 58], [89, 12, 70, 60, "getId"], [90, 10, 70, 66], [91, 8, 71, 12], [91, 9, 71, 13], [92, 6, 72, 8], [93, 4, 73, 4], [93, 5, 73, 5], [93, 6, 73, 6], [93, 7, 74, 9, "filter"], [93, 13, 74, 15], [93, 14, 74, 16, "Boolean"], [93, 21, 74, 23], [93, 22, 74, 24], [94, 4, 75, 4], [95, 4, 76, 4, "ordered"], [95, 11, 76, 11], [95, 12, 76, 12, "push"], [95, 16, 76, 16], [95, 17, 76, 17], [95, 20, 76, 20, "entries"], [95, 27, 76, 27], [95, 28, 76, 28, "sort"], [95, 32, 76, 32], [95, 33, 76, 33], [95, 34, 76, 34], [95, 35, 76, 35], [95, 37, 76, 37, "Route_1"], [95, 44, 76, 44], [95, 45, 76, 45, "sortRoutesWithInitial"], [95, 66, 76, 66], [95, 68, 76, 68, "initialRouteName"], [95, 84, 76, 84], [95, 85, 76, 85], [95, 86, 76, 86], [95, 87, 76, 87, "map"], [95, 90, 76, 90], [95, 91, 76, 92, "route"], [95, 96, 76, 97], [95, 101, 76, 103], [96, 6, 76, 105, "route"], [96, 11, 76, 110], [97, 6, 76, 112, "props"], [97, 11, 76, 117], [97, 13, 76, 119], [97, 14, 76, 120], [98, 4, 76, 122], [98, 5, 76, 123], [98, 6, 76, 124], [98, 7, 76, 125], [98, 8, 76, 126], [99, 4, 77, 4], [99, 11, 77, 11, "ordered"], [99, 18, 77, 18], [100, 2, 78, 0], [101, 2, 79, 0], [102, 0, 80, 0], [103, 0, 81, 0], [104, 2, 82, 0], [104, 11, 82, 9, "useSortedScreens"], [104, 27, 82, 25, "useSortedScreens"], [104, 28, 82, 26, "order"], [104, 33, 82, 31], [104, 35, 82, 33, "protectedScreens"], [104, 51, 82, 49], [104, 53, 82, 51], [105, 4, 83, 4], [105, 10, 83, 10, "node"], [105, 14, 83, 14], [105, 17, 83, 17], [105, 18, 83, 18], [105, 19, 83, 19], [105, 21, 83, 21, "Route_1"], [105, 28, 83, 28], [105, 29, 83, 29, "useRouteNode"], [105, 41, 83, 41], [105, 43, 83, 43], [105, 44, 83, 44], [106, 4, 84, 4], [106, 10, 84, 10, "sorted"], [106, 16, 84, 16], [106, 19, 84, 19, "node"], [106, 23, 84, 23], [106, 25, 84, 25, "children"], [106, 33, 84, 33], [106, 35, 84, 35, "length"], [106, 41, 84, 41], [106, 44, 85, 10, "getS<PERSON><PERSON><PERSON><PERSON><PERSON>"], [106, 61, 85, 27], [106, 62, 85, 28, "node"], [106, 66, 85, 32], [106, 67, 85, 33, "children"], [106, 75, 85, 41], [106, 77, 85, 43, "order"], [106, 82, 85, 48], [106, 84, 85, 50, "node"], [106, 88, 85, 54], [106, 89, 85, 55, "initialRouteName"], [106, 105, 85, 71], [106, 106, 85, 72], [106, 109, 86, 10], [106, 111, 86, 12], [107, 4, 87, 4], [107, 11, 87, 11, "react_1"], [107, 18, 87, 18], [107, 19, 87, 19, "default"], [107, 26, 87, 26], [107, 27, 87, 27, "useMemo"], [107, 34, 87, 34], [107, 35, 87, 35], [107, 41, 87, 41, "sorted"], [107, 47, 87, 47], [107, 48, 88, 9, "filter"], [107, 54, 88, 15], [107, 55, 88, 17, "item"], [107, 59, 88, 21], [107, 63, 88, 26], [107, 64, 88, 27, "protectedScreens"], [107, 80, 88, 43], [107, 81, 88, 44, "has"], [107, 84, 88, 47], [107, 85, 88, 48, "item"], [107, 89, 88, 52], [107, 90, 88, 53, "route"], [107, 95, 88, 58], [107, 96, 88, 59, "route"], [107, 101, 88, 64], [107, 102, 88, 65], [107, 103, 88, 66], [107, 104, 89, 9, "map"], [107, 107, 89, 12], [107, 108, 89, 14, "value"], [107, 113, 89, 19], [107, 117, 89, 24], [108, 6, 90, 8], [108, 13, 90, 15, "routeToScreen"], [108, 26, 90, 28], [108, 27, 90, 29, "value"], [108, 32, 90, 34], [108, 33, 90, 35, "route"], [108, 38, 90, 40], [108, 40, 90, 42, "value"], [108, 45, 90, 47], [108, 46, 90, 48, "props"], [108, 51, 90, 53], [108, 52, 90, 54], [109, 4, 91, 4], [109, 5, 91, 5], [109, 6, 91, 6], [109, 8, 91, 8], [109, 9, 91, 9, "sorted"], [109, 15, 91, 15], [109, 17, 91, 17, "protectedScreens"], [109, 33, 91, 33], [109, 34, 91, 34], [109, 35, 91, 35], [110, 2, 92, 0], [111, 2, 93, 0], [111, 11, 93, 9, "fromImport"], [111, 21, 93, 19, "fromImport"], [111, 22, 93, 20, "value"], [111, 27, 93, 25], [111, 29, 93, 27], [112, 4, 93, 29, "Error<PERSON>ou<PERSON><PERSON>"], [112, 17, 93, 42], [113, 4, 93, 44], [113, 7, 93, 47, "component"], [114, 2, 93, 57], [114, 3, 93, 58], [114, 5, 93, 60], [115, 4, 94, 4], [116, 4, 95, 4], [116, 8, 95, 8, "component"], [116, 17, 95, 17], [116, 19, 95, 19, "default"], [116, 26, 95, 26], [116, 30, 95, 30, "__DEV__"], [116, 37, 95, 37], [116, 39, 95, 39], [117, 6, 96, 8, "component"], [117, 15, 96, 17], [117, 16, 96, 18, "default"], [117, 23, 96, 25], [117, 24, 96, 26, "displayName"], [117, 35, 96, 37], [117, 40, 96, 42], [117, 43, 96, 45, "component"], [117, 52, 96, 54], [117, 53, 96, 55, "default"], [117, 60, 96, 62], [117, 61, 96, 63, "name"], [117, 65, 96, 67], [117, 69, 96, 71], [117, 76, 96, 78], [117, 80, 96, 82, "value"], [117, 85, 96, 87], [117, 86, 96, 88, "<PERSON><PERSON>ey"], [117, 96, 96, 98], [117, 99, 96, 101], [118, 4, 97, 4], [119, 4, 98, 4], [119, 8, 98, 8, "Error<PERSON>ou<PERSON><PERSON>"], [119, 21, 98, 21], [119, 23, 98, 23], [120, 6, 99, 8], [120, 12, 99, 14, "Wrapped"], [120, 19, 99, 21], [120, 22, 99, 24, "react_1"], [120, 29, 99, 31], [120, 30, 99, 32, "default"], [120, 37, 99, 39], [120, 38, 99, 40, "forwardRef"], [120, 48, 99, 50], [120, 49, 99, 51], [120, 50, 99, 52, "props"], [120, 55, 99, 57], [120, 57, 99, 59, "ref"], [120, 60, 99, 62], [120, 65, 99, 67], [121, 8, 100, 12], [121, 14, 100, 18, "children"], [121, 22, 100, 26], [121, 25, 100, 29, "react_1"], [121, 32, 100, 36], [121, 33, 100, 37, "default"], [121, 40, 100, 44], [121, 41, 100, 45, "createElement"], [121, 54, 100, 58], [121, 55, 100, 59, "component"], [121, 64, 100, 68], [121, 65, 100, 69, "default"], [121, 72, 100, 76], [121, 76, 100, 80, "EmptyRoute_1"], [121, 88, 100, 92], [121, 89, 100, 93, "EmptyRoute"], [121, 99, 100, 103], [121, 101, 100, 105], [122, 10, 101, 16], [122, 13, 101, 19, "props"], [122, 18, 101, 24], [123, 10, 102, 16, "ref"], [124, 8, 103, 12], [124, 9, 103, 13], [124, 10, 103, 14], [125, 8, 104, 12], [125, 15, 104, 19, "_reactNativeCssInteropJsxRuntime"], [125, 47, 104, 19], [125, 48, 104, 19, "jsx"], [125, 51, 104, 19], [125, 52, 104, 20, "Try_1"], [125, 57, 104, 25], [125, 58, 104, 26, "Try"], [125, 61, 104, 29], [126, 10, 104, 30, "catch"], [126, 15, 104, 35], [126, 17, 104, 37, "Error<PERSON>ou<PERSON><PERSON>"], [126, 30, 104, 51], [127, 10, 104, 51, "children"], [127, 18, 104, 51], [127, 20, 104, 53, "children"], [128, 8, 104, 61], [128, 9, 104, 73], [128, 10, 104, 74], [129, 6, 105, 8], [129, 7, 105, 9], [129, 8, 105, 10], [130, 6, 106, 8], [130, 10, 106, 12, "__DEV__"], [130, 17, 106, 19], [130, 19, 106, 21], [131, 8, 107, 12, "Wrapped"], [131, 15, 107, 19], [131, 16, 107, 20, "displayName"], [131, 27, 107, 31], [131, 30, 107, 34], [131, 47, 107, 51, "value"], [131, 52, 107, 56], [131, 53, 107, 57, "<PERSON><PERSON>ey"], [131, 63, 107, 67], [131, 66, 107, 70], [132, 6, 108, 8], [133, 6, 109, 8], [133, 13, 109, 15], [134, 8, 110, 12, "default"], [134, 15, 110, 19], [134, 17, 110, 21, "Wrapped"], [135, 6, 111, 8], [135, 7, 111, 9], [136, 4, 112, 4], [137, 4, 113, 4], [137, 8, 113, 8, "process"], [137, 15, 113, 15], [137, 16, 113, 16, "env"], [137, 19, 113, 19], [137, 20, 113, 20, "NODE_ENV"], [137, 28, 113, 28], [137, 33, 113, 33], [137, 45, 113, 45], [137, 47, 113, 47], [138, 6, 114, 8], [138, 10, 114, 12], [138, 17, 114, 19, "component"], [138, 26, 114, 28], [138, 27, 114, 29, "default"], [138, 34, 114, 36], [138, 39, 114, 41], [138, 47, 114, 49], [138, 51, 115, 12, "component"], [138, 60, 115, 21], [138, 61, 115, 22, "default"], [138, 68, 115, 29], [138, 72, 116, 12, "Object"], [138, 78, 116, 18], [138, 79, 116, 19, "keys"], [138, 83, 116, 23], [138, 84, 116, 24, "component"], [138, 93, 116, 33], [138, 94, 116, 34, "default"], [138, 101, 116, 41], [138, 102, 116, 42], [138, 103, 116, 43, "length"], [138, 109, 116, 49], [138, 114, 116, 54], [138, 115, 116, 55], [138, 117, 116, 57], [139, 8, 117, 12], [139, 15, 117, 19], [140, 10, 117, 21, "default"], [140, 17, 117, 28], [140, 19, 117, 30, "EmptyRoute_1"], [140, 31, 117, 42], [140, 32, 117, 43, "EmptyRoute"], [141, 8, 117, 54], [141, 9, 117, 55], [142, 6, 118, 8], [143, 4, 119, 4], [144, 4, 120, 4], [144, 11, 120, 11], [145, 6, 120, 13, "default"], [145, 13, 120, 20], [145, 15, 120, 22, "component"], [145, 24, 120, 31], [145, 25, 120, 32, "default"], [146, 4, 120, 40], [146, 5, 120, 41], [147, 2, 121, 0], [148, 2, 122, 0], [148, 11, 122, 9, "fromLoadedRoute"], [148, 26, 122, 24, "fromLoadedRoute"], [148, 27, 122, 25, "value"], [148, 32, 122, 30], [148, 34, 122, 32, "res"], [148, 37, 122, 35], [148, 39, 122, 37], [149, 4, 123, 4], [149, 8, 123, 8], [149, 10, 123, 10, "res"], [149, 13, 123, 13], [149, 25, 123, 25, "Promise"], [149, 32, 123, 32], [149, 33, 123, 33], [149, 35, 123, 35], [150, 6, 124, 8], [150, 13, 124, 15, "fromImport"], [150, 23, 124, 25], [150, 24, 124, 26, "value"], [150, 29, 124, 31], [150, 31, 124, 33, "res"], [150, 34, 124, 36], [150, 35, 124, 37], [151, 4, 125, 4], [152, 4, 126, 4], [152, 11, 126, 11, "res"], [152, 14, 126, 14], [152, 15, 126, 15, "then"], [152, 19, 126, 19], [152, 20, 126, 20, "fromImport"], [152, 30, 126, 30], [152, 31, 126, 31, "bind"], [152, 35, 126, 35], [152, 36, 126, 36], [152, 40, 126, 40], [152, 42, 126, 42, "value"], [152, 47, 126, 47], [152, 48, 126, 48], [152, 49, 126, 49], [153, 2, 127, 0], [154, 2, 128, 0], [155, 2, 129, 0], [156, 2, 130, 0], [156, 8, 130, 6, "qualifiedStore"], [156, 22, 130, 20], [156, 25, 130, 23], [156, 29, 130, 27, "WeakMap"], [156, 36, 130, 34], [156, 37, 130, 35], [156, 38, 130, 36], [157, 2, 131, 0], [158, 2, 132, 0], [158, 11, 132, 9, "getQualifiedRouteComponent"], [158, 37, 132, 35, "getQualifiedRouteComponent"], [158, 38, 132, 36, "value"], [158, 43, 132, 41], [158, 45, 132, 43], [159, 4, 133, 4], [159, 8, 133, 8, "qualifiedStore"], [159, 22, 133, 22], [159, 23, 133, 23, "has"], [159, 26, 133, 26], [159, 27, 133, 27, "value"], [159, 32, 133, 32], [159, 33, 133, 33], [159, 35, 133, 35], [160, 6, 134, 8], [160, 13, 134, 15, "qualifiedStore"], [160, 27, 134, 29], [160, 28, 134, 30, "get"], [160, 31, 134, 33], [160, 32, 134, 34, "value"], [160, 37, 134, 39], [160, 38, 134, 40], [161, 4, 135, 4], [162, 4, 136, 4], [162, 8, 136, 8, "ScreenComponent"], [162, 23, 136, 23], [163, 4, 137, 4], [164, 4, 138, 4], [164, 8, 138, 8, "import_mode_1"], [164, 21, 138, 21], [164, 22, 138, 22, "default"], [164, 29, 138, 29], [164, 34, 138, 34], [164, 40, 138, 40], [164, 42, 138, 42], [165, 6, 139, 8, "ScreenComponent"], [165, 21, 139, 23], [165, 24, 139, 26, "react_1"], [165, 31, 139, 33], [165, 32, 139, 34, "default"], [165, 39, 139, 41], [165, 40, 139, 42, "lazy"], [165, 44, 139, 46], [165, 45, 139, 47], [165, 57, 139, 59], [166, 8, 140, 12], [166, 14, 140, 18, "res"], [166, 17, 140, 21], [166, 20, 140, 24, "value"], [166, 25, 140, 29], [166, 26, 140, 30, "loadRoute"], [166, 35, 140, 39], [166, 36, 140, 40], [166, 37, 140, 41], [167, 8, 141, 12], [167, 15, 141, 19, "fromLoadedRoute"], [167, 30, 141, 34], [167, 31, 141, 35, "value"], [167, 36, 141, 40], [167, 38, 141, 42, "res"], [167, 41, 141, 45], [167, 42, 141, 46], [168, 6, 142, 8], [168, 7, 142, 9], [168, 8, 142, 10], [169, 6, 143, 8], [169, 10, 143, 12, "__DEV__"], [169, 17, 143, 19], [169, 19, 143, 21], [170, 8, 144, 12, "ScreenComponent"], [170, 23, 144, 27], [170, 24, 144, 28, "displayName"], [170, 35, 144, 39], [170, 38, 144, 42], [170, 52, 144, 56, "value"], [170, 57, 144, 61], [170, 58, 144, 62, "route"], [170, 63, 144, 67], [170, 66, 144, 70], [171, 6, 145, 8], [172, 4, 146, 4], [172, 5, 146, 5], [172, 11, 147, 9], [173, 6, 148, 8], [173, 12, 148, 14, "res"], [173, 15, 148, 17], [173, 18, 148, 20, "value"], [173, 23, 148, 25], [173, 24, 148, 26, "loadRoute"], [173, 33, 148, 35], [173, 34, 148, 36], [173, 35, 148, 37], [174, 6, 149, 8, "ScreenComponent"], [174, 21, 149, 23], [174, 24, 149, 26, "fromImport"], [174, 34, 149, 36], [174, 35, 149, 37, "value"], [174, 40, 149, 42], [174, 42, 149, 44, "res"], [174, 45, 149, 47], [174, 46, 149, 48], [174, 47, 149, 49, "default"], [174, 54, 149, 56], [175, 4, 150, 4], [176, 4, 151, 4], [176, 13, 151, 13, "BaseRoute"], [176, 22, 151, 22, "BaseRoute"], [176, 23, 151, 23], [177, 6, 152, 4], [178, 6, 153, 4], [179, 6, 154, 4, "route"], [179, 11, 154, 9], [180, 6, 154, 11, "navigation"], [180, 16, 154, 21], [181, 6, 155, 4], [182, 6, 156, 4], [182, 9, 156, 7, "props"], [183, 4, 156, 13], [183, 5, 156, 14], [183, 7, 156, 16], [184, 6, 157, 8], [184, 12, 157, 14, "stateForPath"], [184, 24, 157, 26], [184, 27, 157, 29], [184, 28, 157, 30], [184, 29, 157, 31], [184, 31, 157, 33, "native_1"], [184, 39, 157, 41], [184, 40, 157, 42, "useStateForPath"], [184, 55, 157, 57], [184, 57, 157, 59], [184, 58, 157, 60], [185, 6, 158, 8], [185, 12, 158, 14, "isFocused"], [185, 21, 158, 23], [185, 24, 158, 26], [185, 25, 158, 27], [185, 26, 158, 28], [185, 28, 158, 30, "native_1"], [185, 36, 158, 38], [185, 37, 158, 39, "useIsFocused"], [185, 49, 158, 51], [185, 51, 158, 53], [185, 52, 158, 54], [186, 6, 159, 8], [186, 12, 159, 14, "store"], [186, 17, 159, 19], [186, 20, 159, 22], [186, 21, 159, 23], [186, 22, 159, 24], [186, 24, 159, 26, "storeContext_1"], [186, 38, 159, 40], [186, 39, 159, 41, "useExpoRouterStore"], [186, 57, 159, 59], [186, 59, 159, 61], [186, 60, 159, 62], [187, 6, 160, 8], [187, 10, 160, 12, "isFocused"], [187, 19, 160, 21], [187, 21, 160, 23], [188, 8, 161, 12], [188, 14, 161, 18, "state"], [188, 19, 161, 23], [188, 22, 161, 26, "navigation"], [188, 32, 161, 36], [188, 33, 161, 37, "getState"], [188, 41, 161, 45], [188, 42, 161, 46], [188, 43, 161, 47], [189, 8, 162, 12], [189, 14, 162, 18, "<PERSON><PERSON><PERSON><PERSON>"], [189, 20, 162, 24], [189, 23, 162, 27], [189, 25, 162, 29], [189, 32, 162, 36], [189, 36, 162, 40, "state"], [189, 41, 162, 45], [189, 42, 162, 46, "routes"], [189, 48, 162, 52], [189, 49, 162, 53, "state"], [189, 54, 162, 58], [189, 55, 162, 59, "index"], [189, 60, 162, 64], [189, 61, 162, 65], [189, 62, 162, 66], [190, 8, 163, 12], [190, 12, 163, 16, "<PERSON><PERSON><PERSON><PERSON>"], [190, 18, 163, 22], [190, 22, 163, 26, "stateForPath"], [190, 34, 163, 38], [190, 36, 164, 16, "store"], [190, 41, 164, 21], [190, 42, 164, 22, "setFocusedState"], [190, 57, 164, 37], [190, 58, 164, 38, "stateForPath"], [190, 70, 164, 50], [190, 71, 164, 51], [191, 6, 165, 8], [192, 6, 166, 8], [192, 13, 166, 16, "_reactNativeCssInteropJsxRuntime"], [192, 45, 166, 16], [192, 46, 166, 16, "jsx"], [192, 49, 166, 16], [192, 50, 166, 17, "Route_1"], [192, 57, 166, 24], [192, 58, 166, 25, "Route"], [192, 63, 166, 30], [193, 8, 166, 31, "node"], [193, 12, 166, 35], [193, 14, 166, 37, "value"], [193, 19, 166, 43], [194, 8, 166, 44, "route"], [194, 13, 166, 49], [194, 15, 166, 51, "route"], [194, 20, 166, 57], [195, 8, 166, 57, "children"], [195, 16, 166, 57], [195, 18, 167, 8, "_reactNativeCssInteropJsxRuntime"], [195, 50, 167, 8], [195, 51, 167, 8, "jsx"], [195, 54, 167, 8], [195, 55, 167, 9, "react_1"], [195, 62, 167, 16], [195, 63, 167, 17, "default"], [195, 70, 167, 24], [195, 71, 167, 25, "Suspense"], [195, 79, 167, 33], [196, 10, 167, 34, "fallback"], [196, 18, 167, 42], [196, 20, 167, 44, "_reactNativeCssInteropJsxRuntime"], [196, 52, 167, 44], [196, 53, 167, 44, "jsx"], [196, 56, 167, 44], [196, 57, 167, 45, "SuspenseFallback_1"], [196, 75, 167, 63], [196, 76, 167, 64, "SuspenseFallback"], [196, 92, 167, 80], [197, 12, 167, 81, "route"], [197, 17, 167, 86], [197, 19, 167, 88, "value"], [198, 10, 167, 94], [198, 11, 167, 95], [198, 12, 167, 97], [199, 10, 167, 97, "children"], [199, 18, 167, 97], [199, 20, 168, 10, "_reactNativeCssInteropJsxRuntime"], [199, 52, 168, 10], [199, 53, 168, 10, "jsx"], [199, 56, 168, 10], [199, 57, 168, 11, "ScreenComponent"], [199, 72, 168, 26], [200, 12, 168, 26], [200, 15, 168, 31, "props"], [200, 20, 168, 36], [201, 12, 169, 8], [202, 12, 170, 8], [203, 12, 171, 8, "segment"], [203, 19, 171, 15], [203, 21, 171, 17, "value"], [203, 26, 171, 22], [203, 27, 171, 23, "route"], [204, 10, 171, 29], [204, 11, 171, 30], [205, 8, 171, 31], [205, 9, 172, 34], [206, 6, 172, 35], [206, 7, 173, 21], [206, 8, 173, 22], [207, 4, 174, 4], [208, 4, 175, 4], [208, 8, 175, 8, "__DEV__"], [208, 15, 175, 15], [208, 17, 175, 17], [209, 6, 176, 8, "BaseRoute"], [209, 15, 176, 17], [209, 16, 176, 18, "displayName"], [209, 27, 176, 29], [209, 30, 176, 32], [209, 39, 176, 41, "value"], [209, 44, 176, 46], [209, 45, 176, 47, "route"], [209, 50, 176, 52], [209, 53, 176, 55], [210, 4, 177, 4], [211, 4, 178, 4, "qualifiedStore"], [211, 18, 178, 18], [211, 19, 178, 19, "set"], [211, 22, 178, 22], [211, 23, 178, 23, "value"], [211, 28, 178, 28], [211, 30, 178, 30, "BaseRoute"], [211, 39, 178, 39], [211, 40, 178, 40], [212, 4, 179, 4], [212, 11, 179, 11, "BaseRoute"], [212, 20, 179, 20], [213, 2, 180, 0], [214, 2, 181, 0], [214, 11, 181, 9, "screenOptionsFactory"], [214, 31, 181, 29, "screenOptionsFactory"], [214, 32, 181, 30, "route"], [214, 37, 181, 35], [214, 39, 181, 37, "options"], [214, 46, 181, 44], [214, 48, 181, 46], [215, 4, 182, 4], [215, 11, 182, 12, "args"], [215, 15, 182, 16], [215, 19, 182, 21], [216, 6, 183, 8], [217, 6, 184, 8], [217, 12, 184, 14, "staticOptions"], [217, 25, 184, 27], [217, 28, 184, 30, "route"], [217, 33, 184, 35], [217, 34, 184, 36, "generated"], [217, 43, 184, 45], [217, 46, 184, 48, "route"], [217, 51, 184, 53], [217, 52, 184, 54, "loadRoute"], [217, 61, 184, 63], [217, 62, 184, 64], [217, 63, 184, 65], [217, 65, 184, 67, "getNavOptions"], [217, 78, 184, 80], [217, 81, 184, 83], [217, 85, 184, 87], [218, 6, 185, 8], [218, 12, 185, 14, "staticResult"], [218, 24, 185, 26], [218, 27, 185, 29], [218, 34, 185, 36, "staticOptions"], [218, 47, 185, 49], [218, 52, 185, 54], [218, 62, 185, 64], [218, 65, 185, 67, "staticOptions"], [218, 78, 185, 80], [218, 79, 185, 81, "args"], [218, 83, 185, 85], [218, 84, 185, 86], [218, 87, 185, 89, "staticOptions"], [218, 100, 185, 102], [219, 6, 186, 8], [219, 12, 186, 14, "dynamicResult"], [219, 25, 186, 27], [219, 28, 186, 30], [219, 35, 186, 37, "options"], [219, 42, 186, 44], [219, 47, 186, 49], [219, 57, 186, 59], [219, 60, 186, 62, "options"], [219, 67, 186, 69], [219, 70, 186, 72, "args"], [219, 74, 186, 76], [219, 75, 186, 77], [219, 78, 186, 80, "options"], [219, 85, 186, 87], [220, 6, 187, 8], [220, 12, 187, 14, "output"], [220, 18, 187, 20], [220, 21, 187, 23], [221, 8, 188, 12], [221, 11, 188, 15, "staticResult"], [221, 23, 188, 27], [222, 8, 189, 12], [222, 11, 189, 15, "dynamicResult"], [223, 6, 190, 8], [223, 7, 190, 9], [224, 6, 191, 8], [225, 6, 192, 8], [225, 10, 192, 12, "route"], [225, 15, 192, 17], [225, 16, 192, 18, "generated"], [225, 25, 192, 27], [225, 27, 192, 29], [226, 8, 193, 12, "output"], [226, 14, 193, 18], [226, 15, 193, 19, "tabBarItemStyle"], [226, 30, 193, 34], [226, 33, 193, 37], [227, 10, 193, 39, "display"], [227, 17, 193, 46], [227, 19, 193, 48], [228, 8, 193, 55], [228, 9, 193, 56], [229, 8, 194, 12, "output"], [229, 14, 194, 18], [229, 15, 194, 19, "tabBarButton"], [229, 27, 194, 31], [229, 30, 194, 34], [229, 36, 194, 40], [229, 40, 194, 44], [230, 8, 195, 12], [231, 8, 196, 12, "output"], [231, 14, 196, 18], [231, 15, 196, 19, "drawerItemStyle"], [231, 30, 196, 34], [231, 33, 196, 37], [232, 10, 196, 39, "height"], [232, 16, 196, 45], [232, 18, 196, 47], [232, 19, 196, 48], [233, 10, 196, 50, "display"], [233, 17, 196, 57], [233, 19, 196, 59], [234, 8, 196, 66], [234, 9, 196, 67], [235, 6, 197, 8], [236, 6, 198, 8], [236, 13, 198, 15, "output"], [236, 19, 198, 21], [237, 4, 199, 4], [237, 5, 199, 5], [238, 2, 200, 0], [239, 2, 201, 0], [239, 11, 201, 9, "routeToScreen"], [239, 24, 201, 22, "routeToScreen"], [239, 25, 201, 23, "route"], [239, 30, 201, 28], [239, 32, 201, 30], [240, 4, 201, 32, "options"], [240, 11, 201, 39], [241, 4, 201, 41, "getId"], [241, 9, 201, 46], [242, 4, 201, 48], [242, 7, 201, 51, "props"], [243, 2, 201, 57], [243, 3, 201, 58], [243, 6, 201, 61], [243, 7, 201, 62], [243, 8, 201, 63], [243, 10, 201, 65], [244, 4, 202, 4], [244, 11, 202, 12, "_reactNativeCssInterop"], [244, 33, 202, 12], [244, 34, 202, 12, "createElement"], [244, 47, 202, 12], [244, 48, 202, 13, "primitives_1"], [244, 60, 202, 25], [244, 61, 202, 26, "Screen"], [244, 67, 202, 32], [245, 6, 202, 32], [245, 9, 202, 37, "props"], [245, 14, 202, 42], [246, 6, 202, 44, "name"], [246, 10, 202, 48], [246, 12, 202, 50, "route"], [246, 17, 202, 55], [246, 18, 202, 56, "route"], [246, 23, 202, 62], [247, 6, 202, 63, "key"], [247, 9, 202, 66], [247, 11, 202, 68, "route"], [247, 16, 202, 73], [247, 17, 202, 74, "route"], [247, 22, 202, 80], [248, 6, 202, 81, "getId"], [248, 11, 202, 86], [248, 13, 202, 88, "getId"], [248, 18, 202, 94], [249, 6, 202, 95, "options"], [249, 13, 202, 102], [249, 15, 202, 104, "screenOptionsFactory"], [249, 35, 202, 124], [249, 36, 202, 125, "route"], [249, 41, 202, 130], [249, 43, 202, 132, "options"], [249, 50, 202, 139], [249, 51, 202, 141], [250, 6, 202, 142, "getComponent"], [250, 18, 202, 154], [250, 20, 202, 156, "getComponent"], [250, 21, 202, 156], [250, 26, 202, 162, "getQualifiedRouteComponent"], [250, 52, 202, 188], [250, 53, 202, 189, "route"], [250, 58, 202, 194], [250, 59, 202, 196], [251, 6, 202, 196, "__self"], [251, 12, 202, 196], [252, 6, 202, 196, "__source"], [252, 14, 202, 196], [253, 8, 202, 196, "fileName"], [253, 16, 202, 196], [253, 18, 202, 196, "_jsxFileName"], [253, 30, 202, 196], [254, 8, 202, 196, "lineNumber"], [254, 18, 202, 196], [255, 8, 202, 196, "columnNumber"], [255, 20, 202, 196], [256, 6, 202, 196], [257, 4, 202, 196], [257, 5, 202, 197], [257, 6, 202, 198], [258, 2, 203, 0], [259, 2, 204, 0], [259, 11, 204, 9, "getSingularId"], [259, 24, 204, 22, "getSingularId"], [259, 25, 204, 23, "name"], [259, 29, 204, 27], [259, 31, 204, 29, "options"], [259, 38, 204, 36], [259, 41, 204, 39], [259, 42, 204, 40], [259, 43, 204, 41], [259, 45, 204, 43], [260, 4, 205, 4], [260, 11, 205, 11, "name"], [260, 15, 205, 15], [260, 16, 206, 9, "split"], [260, 21, 206, 14], [260, 22, 206, 15], [260, 25, 206, 18], [260, 26, 206, 19], [260, 27, 207, 9, "map"], [260, 30, 207, 12], [260, 31, 207, 14, "segment"], [260, 38, 207, 21], [260, 42, 207, 26], [261, 6, 208, 8], [261, 10, 208, 12, "segment"], [261, 17, 208, 19], [261, 18, 208, 20, "startsWith"], [261, 28, 208, 30], [261, 29, 208, 31], [261, 35, 208, 37], [261, 36, 208, 38], [261, 38, 208, 40], [262, 8, 209, 12], [262, 15, 209, 19, "options"], [262, 22, 209, 26], [262, 23, 209, 27, "params"], [262, 29, 209, 33], [262, 32, 209, 36, "segment"], [262, 39, 209, 43], [262, 40, 209, 44, "slice"], [262, 45, 209, 49], [262, 46, 209, 50], [262, 47, 209, 51], [262, 49, 209, 53], [262, 50, 209, 54], [262, 51, 209, 55], [262, 52, 209, 56], [262, 53, 209, 57], [262, 55, 209, 59, "join"], [262, 59, 209, 63], [262, 60, 209, 64], [262, 63, 209, 67], [262, 64, 209, 68], [262, 68, 209, 72, "segment"], [262, 75, 209, 79], [263, 6, 210, 8], [263, 7, 210, 9], [263, 13, 211, 13], [263, 17, 211, 17, "segment"], [263, 24, 211, 24], [263, 25, 211, 25, "startsWith"], [263, 35, 211, 35], [263, 36, 211, 36], [263, 39, 211, 39], [263, 40, 211, 40], [263, 42, 211, 42], [264, 8, 212, 12], [264, 15, 212, 19, "options"], [264, 22, 212, 26], [264, 23, 212, 27, "params"], [264, 29, 212, 33], [264, 32, 212, 36, "segment"], [264, 39, 212, 43], [264, 40, 212, 44, "slice"], [264, 45, 212, 49], [264, 46, 212, 50], [264, 47, 212, 51], [264, 49, 212, 53], [264, 50, 212, 54], [264, 51, 212, 55], [264, 52, 212, 56], [264, 53, 212, 57], [264, 57, 212, 61, "segment"], [264, 64, 212, 68], [265, 6, 213, 8], [265, 7, 213, 9], [265, 13, 214, 13], [266, 8, 215, 12], [266, 15, 215, 19, "segment"], [266, 22, 215, 26], [267, 6, 216, 8], [268, 4, 217, 4], [268, 5, 217, 5], [268, 6, 217, 6], [268, 7, 218, 9, "join"], [268, 11, 218, 13], [268, 12, 218, 14], [268, 15, 218, 17], [268, 16, 218, 18], [269, 2, 219, 0], [270, 0, 219, 1], [270, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "getS<PERSON><PERSON><PERSON><PERSON><PERSON>", "children.sort.map$argument_0", "order.map$argument_0", "entries.findIndex$argument_0", "children.map$argument_0", "getId", "entries.sort.map$argument_0", "useSortedScreens", "react_1._default.useMemo$argument_0", "sorted.filter$argument_0", "sorted.filter.map$argument_0", "fromImport", "react_1._default.forwardRef$argument_0", "fromLoadedRoute", "getQualifiedRouteComponent", "react_1._default.lazy$argument_0", "BaseRoute", "screenOptionsFactory", "output.tabBarButton", "routeToScreen", "primitives_1.Screen.props.getComponent", "getSingularId", "name.split.map$argument_0"], "mappings": "AAA;wDCE;CDE;AEgB;iBCI,iCD;aEI;6CCK,+BD;gHEE,oBF;4BGuB,cH;4BGG,iDH;4BGG,yCH;KFQ;2FMG,iCN;CFE;ASI;mCCK;gBCC,iDD;aEC;KFE,CD;CTC;AaC;mDCM;SDM;CbgB;AeC;CfK;AgBK;+CCO;SDG;IES;KFuB;ChBM;AmBC;WlBC;kCmBY,UnB;KkBK;CnBC;AqBC;4JCC,uCD;CrBC;AuBC;aCG;KDU;CvBE"}}, "type": "js/module"}]}