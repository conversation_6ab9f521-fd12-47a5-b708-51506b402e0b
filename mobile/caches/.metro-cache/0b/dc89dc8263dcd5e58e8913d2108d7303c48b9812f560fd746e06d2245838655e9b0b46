{"dependencies": [{"name": "is-arrayish", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 17, "index": 32}, "end": {"line": 3, "column": 39, "index": 54}}], "key": "zLTJ2woS1Hj+5V7r+13keHemsCY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var isArrayish = require(_dependencyMap[0], \"is-arrayish\");\n  var concat = Array.prototype.concat;\n  var slice = Array.prototype.slice;\n  var swizzle = module.exports = function swizzle(args) {\n    var results = [];\n    for (var i = 0, len = args.length; i < len; i++) {\n      var arg = args[i];\n      if (isArrayish(arg)) {\n        // http://jsperf.com/javascript-array-concat-vs-push/98\n        results = concat.call(results, slice.call(arg));\n      } else {\n        results.push(arg);\n      }\n    }\n    return results;\n  };\n  swizzle.wrap = function (fn) {\n    return function () {\n      return fn(swizzle(arguments));\n    };\n  };\n});", "lineCount": 25, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [4, 6, 3, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [4, 16, 3, 14], [4, 19, 3, 17, "require"], [4, 26, 3, 24], [4, 27, 3, 24, "_dependencyMap"], [4, 41, 3, 24], [4, 59, 3, 38], [4, 60, 3, 39], [5, 2, 5, 0], [5, 6, 5, 4, "concat"], [5, 12, 5, 10], [5, 15, 5, 13, "Array"], [5, 20, 5, 18], [5, 21, 5, 19, "prototype"], [5, 30, 5, 28], [5, 31, 5, 29, "concat"], [5, 37, 5, 35], [6, 2, 6, 0], [6, 6, 6, 4, "slice"], [6, 11, 6, 9], [6, 14, 6, 12, "Array"], [6, 19, 6, 17], [6, 20, 6, 18, "prototype"], [6, 29, 6, 27], [6, 30, 6, 28, "slice"], [6, 35, 6, 33], [7, 2, 8, 0], [7, 6, 8, 4, "swizzle"], [7, 13, 8, 11], [7, 16, 8, 14, "module"], [7, 22, 8, 20], [7, 23, 8, 21, "exports"], [7, 30, 8, 28], [7, 33, 8, 31], [7, 42, 8, 40, "swizzle"], [7, 49, 8, 47, "swizzle"], [7, 50, 8, 48, "args"], [7, 54, 8, 52], [7, 56, 8, 54], [8, 4, 9, 1], [8, 8, 9, 5, "results"], [8, 15, 9, 12], [8, 18, 9, 15], [8, 20, 9, 17], [9, 4, 11, 1], [9, 9, 11, 6], [9, 13, 11, 10, "i"], [9, 14, 11, 11], [9, 17, 11, 14], [9, 18, 11, 15], [9, 20, 11, 17, "len"], [9, 23, 11, 20], [9, 26, 11, 23, "args"], [9, 30, 11, 27], [9, 31, 11, 28, "length"], [9, 37, 11, 34], [9, 39, 11, 36, "i"], [9, 40, 11, 37], [9, 43, 11, 40, "len"], [9, 46, 11, 43], [9, 48, 11, 45, "i"], [9, 49, 11, 46], [9, 51, 11, 48], [9, 53, 11, 50], [10, 6, 12, 2], [10, 10, 12, 6, "arg"], [10, 13, 12, 9], [10, 16, 12, 12, "args"], [10, 20, 12, 16], [10, 21, 12, 17, "i"], [10, 22, 12, 18], [10, 23, 12, 19], [11, 6, 14, 2], [11, 10, 14, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 20, 14, 16], [11, 21, 14, 17, "arg"], [11, 24, 14, 20], [11, 25, 14, 21], [11, 27, 14, 23], [12, 8, 15, 3], [13, 8, 16, 3, "results"], [13, 15, 16, 10], [13, 18, 16, 13, "concat"], [13, 24, 16, 19], [13, 25, 16, 20, "call"], [13, 29, 16, 24], [13, 30, 16, 25, "results"], [13, 37, 16, 32], [13, 39, 16, 34, "slice"], [13, 44, 16, 39], [13, 45, 16, 40, "call"], [13, 49, 16, 44], [13, 50, 16, 45, "arg"], [13, 53, 16, 48], [13, 54, 16, 49], [13, 55, 16, 50], [14, 6, 17, 2], [14, 7, 17, 3], [14, 13, 17, 9], [15, 8, 18, 3, "results"], [15, 15, 18, 10], [15, 16, 18, 11, "push"], [15, 20, 18, 15], [15, 21, 18, 16, "arg"], [15, 24, 18, 19], [15, 25, 18, 20], [16, 6, 19, 2], [17, 4, 20, 1], [18, 4, 22, 1], [18, 11, 22, 8, "results"], [18, 18, 22, 15], [19, 2, 23, 0], [19, 3, 23, 1], [20, 2, 25, 0, "swizzle"], [20, 9, 25, 7], [20, 10, 25, 8, "wrap"], [20, 14, 25, 12], [20, 17, 25, 15], [20, 27, 25, 25, "fn"], [20, 29, 25, 27], [20, 31, 25, 29], [21, 4, 26, 1], [21, 11, 26, 8], [21, 23, 26, 20], [22, 6, 27, 2], [22, 13, 27, 9, "fn"], [22, 15, 27, 11], [22, 16, 27, 12, "swizzle"], [22, 23, 27, 19], [22, 24, 27, 20, "arguments"], [22, 33, 27, 29], [22, 34, 27, 30], [22, 35, 27, 31], [23, 4, 28, 1], [23, 5, 28, 2], [24, 2, 29, 0], [24, 3, 29, 1], [25, 0, 29, 2], [25, 3]], "functionMap": {"names": ["<global>", "swizzle", "swizzle.wrap", "<anonymous>"], "mappings": "AAA;+BCO;CDe;eEE;QCC;EDE;CFC"}}, "type": "js/module"}]}