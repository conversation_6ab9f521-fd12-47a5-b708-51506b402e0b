{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "./ExpoSecureStore", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 48, "index": 48}}], "key": "ZudhOBNf0EF2ra5GBlE3UCprcN0=", "exportNames": ["*"]}}, {"name": "./byte<PERSON>ounter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 49}, "end": {"line": 2, "column": 70, "index": 119}}], "key": "xUg5IjQU0cu+B0aJJMunpd3JZec=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WHEN_UNLOCKED_THIS_DEVICE_ONLY = exports.WHEN_UNLOCKED = exports.WHEN_PASSCODE_SET_THIS_DEVICE_ONLY = exports.ALWAYS_THIS_DEVICE_ONLY = exports.ALWAYS = exports.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY = exports.AFTER_FIRST_UNLOCK = void 0;\n  exports.canUseBiometricAuthentication = canUseBiometricAuthentication;\n  exports.deleteItemAsync = deleteItemAsync;\n  exports.getItem = getItem;\n  exports.getItemAsync = getItemAsync;\n  exports.isAvailableAsync = isAvailableAsync;\n  exports.setItem = setItem;\n  exports.setItemAsync = setItemAsync;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _ExpoSecureStore = _interopRequireDefault(require(_dependencyMap[2], \"./ExpoSecureStore\"));\n  var _byteCounter = require(_dependencyMap[3], \"./byteCounter\");\n  // @needsAudit\n  /**\n   * The data in the keychain item cannot be accessed after a restart until the device has been\n   * unlocked once by the user. This may be useful if you need to access the item when the phone\n   * is locked.\n   */\n  var AFTER_FIRST_UNLOCK = exports.AFTER_FIRST_UNLOCK = _ExpoSecureStore.default.AFTER_FIRST_UNLOCK;\n  // @needsAudit\n  /**\n   * Similar to `AFTER_FIRST_UNLOCK`, except the entry is not migrated to a new device when restoring\n   * from a backup.\n   */\n  var AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY = exports.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY = _ExpoSecureStore.default.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY;\n  // @needsAudit\n  /**\n   * The data in the keychain item can always be accessed regardless of whether the device is locked.\n   * This is the least secure option.\n   *\n   * @deprecated Use an accessibility level that provides some user protection, such as `AFTER_FIRST_UNLOCK`.\n   */\n  var ALWAYS = exports.ALWAYS = _ExpoSecureStore.default.ALWAYS;\n  // @needsAudit\n  /**\n   * Similar to `WHEN_UNLOCKED_THIS_DEVICE_ONLY`, except the user must have set a passcode in order to\n   * store an entry. If the user removes their passcode, the entry will be deleted.\n   */\n  var WHEN_PASSCODE_SET_THIS_DEVICE_ONLY = exports.WHEN_PASSCODE_SET_THIS_DEVICE_ONLY = _ExpoSecureStore.default.WHEN_PASSCODE_SET_THIS_DEVICE_ONLY;\n  // @needsAudit\n  /**\n   * Similar to `ALWAYS`, except the entry is not migrated to a new device when restoring from a backup.\n   *\n   * @deprecated Use an accessibility level that provides some user protection, such as `AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY`.\n   */\n  var ALWAYS_THIS_DEVICE_ONLY = exports.ALWAYS_THIS_DEVICE_ONLY = _ExpoSecureStore.default.ALWAYS_THIS_DEVICE_ONLY;\n  // @needsAudit\n  /**\n   * The data in the keychain item can be accessed only while the device is unlocked by the user.\n   */\n  var WHEN_UNLOCKED = exports.WHEN_UNLOCKED = _ExpoSecureStore.default.WHEN_UNLOCKED;\n  // @needsAudit\n  /**\n   * Similar to `WHEN_UNLOCKED`, except the entry is not migrated to a new device when restoring from\n   * a backup.\n   */\n  var WHEN_UNLOCKED_THIS_DEVICE_ONLY = exports.WHEN_UNLOCKED_THIS_DEVICE_ONLY = _ExpoSecureStore.default.WHEN_UNLOCKED_THIS_DEVICE_ONLY;\n  // @needsAudit\n  /**\n   * Returns whether the SecureStore API is enabled on the current device. This does not check the app\n   * permissions.\n   *\n   * @return Promise which fulfils with a `boolean`, indicating whether the SecureStore API is available\n   * on the current device. Currently, this resolves `true` on Android and iOS only.\n   */\n  function isAvailableAsync() {\n    return _isAvailableAsync.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Delete the value associated with the provided key.\n   *\n   * @param key The key that was used to store the associated value.\n   * @param options An [`SecureStoreOptions`](#securestoreoptions) object.\n   *\n   * @return A promise that rejects if the value can't be deleted.\n   */\n  function _isAvailableAsync() {\n    _isAvailableAsync = (0, _asyncToGenerator2.default)(function* () {\n      return !!_ExpoSecureStore.default.getValueWithKeyAsync;\n    });\n    return _isAvailableAsync.apply(this, arguments);\n  }\n  function deleteItemAsync(_x) {\n    return _deleteItemAsync.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Reads the stored value associated with the provided key.\n   *\n   * @param key The key that was used to store the associated value.\n   * @param options An [`SecureStoreOptions`](#securestoreoptions) object.\n   *\n   * @return A promise that resolves to the previously stored value. It resolves with `null` if there is no entry\n   * for the given key or if the key has been invalidated. It rejects if an error occurs while retrieving the value.\n   *\n   * > Keys are invalidated by the system when biometrics change, such as adding a new fingerprint or changing the face profile used for face recognition.\n   * > After a key has been invalidated, it becomes impossible to read its value.\n   * > This only applies to values stored with `requireAuthentication` set to `true`.\n   */\n  function _deleteItemAsync() {\n    _deleteItemAsync = (0, _asyncToGenerator2.default)(function* (key) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      ensureValidKey(key);\n      yield _ExpoSecureStore.default.deleteValueWithKeyAsync(key, options);\n    });\n    return _deleteItemAsync.apply(this, arguments);\n  }\n  function getItemAsync(_x2) {\n    return _getItemAsync.apply(this, arguments);\n  } // @needsAudit\n  /**\n   * Stores a key–value pair.\n   *\n   * @param key The key to associate with the stored value. Keys may contain alphanumeric characters, `.`, `-`, and `_`.\n   * @param value The value to store. Size limit is 2048 bytes.\n   * @param options An [`SecureStoreOptions`](#securestoreoptions) object.\n   *\n   * @return A promise that rejects if value cannot be stored on the device.\n   */\n  function _getItemAsync() {\n    _getItemAsync = (0, _asyncToGenerator2.default)(function* (key) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      ensureValidKey(key);\n      return yield _ExpoSecureStore.default.getValueWithKeyAsync(key, options);\n    });\n    return _getItemAsync.apply(this, arguments);\n  }\n  function setItemAsync(_x3, _x4) {\n    return _setItemAsync.apply(this, arguments);\n  }\n  /**\n   * Stores a key–value pair synchronously.\n   * > **Note:** This function blocks the JavaScript thread, so the application may not be interactive when the `requireAuthentication` option is set to `true` until the user authenticates.\n   *\n   * @param key The key to associate with the stored value. Keys may contain alphanumeric characters, `.`, `-`, and `_`.\n   * @param value The value to store. Size limit is 2048 bytes.\n   * @param options An [`SecureStoreOptions`](#securestoreoptions) object.\n   *\n   */\n  function _setItemAsync() {\n    _setItemAsync = (0, _asyncToGenerator2.default)(function* (key, value) {\n      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      ensureValidKey(key);\n      if (!isValidValue(value)) {\n        throw new Error(`Invalid value provided to SecureStore. Values must be strings; consider JSON-encoding your values if they are serializable.`);\n      }\n      yield _ExpoSecureStore.default.setValueWithKeyAsync(value, key, options);\n    });\n    return _setItemAsync.apply(this, arguments);\n  }\n  function setItem(key, value) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    ensureValidKey(key);\n    if (!isValidValue(value)) {\n      throw new Error(`Invalid value provided to SecureStore. Values must be strings; consider JSON-encoding your values if they are serializable.`);\n    }\n    return _ExpoSecureStore.default.setValueWithKeySync(value, key, options);\n  }\n  /**\n   * Synchronously reads the stored value associated with the provided key.\n   * > **Note:** This function blocks the JavaScript thread, so the application may not be interactive when reading a value with `requireAuthentication`\n   * > option set to `true` until the user authenticates.\n   * @param key The key that was used to store the associated value.\n   * @param options An [`SecureStoreOptions`](#securestoreoptions) object.\n   *\n   * @return Previously stored value. It resolves with `null` if there is no entry\n   * for the given key or if the key has been invalidated.\n   */\n  function getItem(key) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    ensureValidKey(key);\n    return _ExpoSecureStore.default.getValueWithKeySync(key, options);\n  }\n  /**\n   * Checks if the value can be saved with `requireAuthentication` option enabled.\n   * @return `true` if the device supports biometric authentication and the enrolled method is sufficiently secure. Otherwise, returns `false`. Always returns false on tvOS.\n   * @platform android\n   * @platform ios\n   */\n  function canUseBiometricAuthentication() {\n    return _ExpoSecureStore.default.canUseBiometricAuthentication();\n  }\n  function ensureValidKey(key) {\n    if (!isValidKey(key)) {\n      throw new Error(`Invalid key provided to SecureStore. Keys must not be empty and contain only alphanumeric characters, \".\", \"-\", and \"_\".`);\n    }\n  }\n  function isValidKey(key) {\n    return typeof key === 'string' && /^[\\w.-]+$/.test(key);\n  }\n  function isValidValue(value) {\n    if (typeof value !== 'string') {\n      return false;\n    }\n    if ((0, _byteCounter.byteCountOverLimit)(value, _byteCounter.VALUE_BYTES_LIMIT)) {\n      console.warn(`Value being stored in SecureStore is larger than ${_byteCounter.VALUE_BYTES_LIMIT} bytes and it may not be stored successfully. In a future SDK version, this call may throw an error.`);\n    }\n    return true;\n  }\n});", "lineCount": 203, "map": [[15, 2, 1, 0], [15, 6, 1, 0, "_ExpoSecureStore"], [15, 22, 1, 0], [15, 25, 1, 0, "_interopRequireDefault"], [15, 47, 1, 0], [15, 48, 1, 0, "require"], [15, 55, 1, 0], [15, 56, 1, 0, "_dependencyMap"], [15, 70, 1, 0], [16, 2, 2, 0], [16, 6, 2, 0, "_byteCounter"], [16, 18, 2, 0], [16, 21, 2, 0, "require"], [16, 28, 2, 0], [16, 29, 2, 0, "_dependencyMap"], [16, 43, 2, 0], [17, 2, 3, 0], [18, 2, 4, 0], [19, 0, 5, 0], [20, 0, 6, 0], [21, 0, 7, 0], [22, 0, 8, 0], [23, 2, 9, 7], [23, 6, 9, 13, "AFTER_FIRST_UNLOCK"], [23, 24, 9, 31], [23, 27, 9, 31, "exports"], [23, 34, 9, 31], [23, 35, 9, 31, "AFTER_FIRST_UNLOCK"], [23, 53, 9, 31], [23, 56, 9, 34, "ExpoSecureStore"], [23, 80, 9, 49], [23, 81, 9, 50, "AFTER_FIRST_UNLOCK"], [23, 99, 9, 68], [24, 2, 10, 0], [25, 2, 11, 0], [26, 0, 12, 0], [27, 0, 13, 0], [28, 0, 14, 0], [29, 2, 15, 7], [29, 6, 15, 13, "AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY"], [29, 41, 15, 48], [29, 44, 15, 48, "exports"], [29, 51, 15, 48], [29, 52, 15, 48, "AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY"], [29, 87, 15, 48], [29, 90, 15, 51, "ExpoSecureStore"], [29, 114, 15, 66], [29, 115, 15, 67, "AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY"], [29, 150, 15, 102], [30, 2, 16, 0], [31, 2, 17, 0], [32, 0, 18, 0], [33, 0, 19, 0], [34, 0, 20, 0], [35, 0, 21, 0], [36, 0, 22, 0], [37, 2, 23, 7], [37, 6, 23, 13, "ALWAYS"], [37, 12, 23, 19], [37, 15, 23, 19, "exports"], [37, 22, 23, 19], [37, 23, 23, 19, "ALWAYS"], [37, 29, 23, 19], [37, 32, 23, 22, "ExpoSecureStore"], [37, 56, 23, 37], [37, 57, 23, 38, "ALWAYS"], [37, 63, 23, 44], [38, 2, 24, 0], [39, 2, 25, 0], [40, 0, 26, 0], [41, 0, 27, 0], [42, 0, 28, 0], [43, 2, 29, 7], [43, 6, 29, 13, "WHEN_PASSCODE_SET_THIS_DEVICE_ONLY"], [43, 40, 29, 47], [43, 43, 29, 47, "exports"], [43, 50, 29, 47], [43, 51, 29, 47, "WHEN_PASSCODE_SET_THIS_DEVICE_ONLY"], [43, 85, 29, 47], [43, 88, 29, 50, "ExpoSecureStore"], [43, 112, 29, 65], [43, 113, 29, 66, "WHEN_PASSCODE_SET_THIS_DEVICE_ONLY"], [43, 147, 29, 100], [44, 2, 30, 0], [45, 2, 31, 0], [46, 0, 32, 0], [47, 0, 33, 0], [48, 0, 34, 0], [49, 0, 35, 0], [50, 2, 36, 7], [50, 6, 36, 13, "ALWAYS_THIS_DEVICE_ONLY"], [50, 29, 36, 36], [50, 32, 36, 36, "exports"], [50, 39, 36, 36], [50, 40, 36, 36, "ALWAYS_THIS_DEVICE_ONLY"], [50, 63, 36, 36], [50, 66, 36, 39, "ExpoSecureStore"], [50, 90, 36, 54], [50, 91, 36, 55, "ALWAYS_THIS_DEVICE_ONLY"], [50, 114, 36, 78], [51, 2, 37, 0], [52, 2, 38, 0], [53, 0, 39, 0], [54, 0, 40, 0], [55, 2, 41, 7], [55, 6, 41, 13, "WHEN_UNLOCKED"], [55, 19, 41, 26], [55, 22, 41, 26, "exports"], [55, 29, 41, 26], [55, 30, 41, 26, "WHEN_UNLOCKED"], [55, 43, 41, 26], [55, 46, 41, 29, "ExpoSecureStore"], [55, 70, 41, 44], [55, 71, 41, 45, "WHEN_UNLOCKED"], [55, 84, 41, 58], [56, 2, 42, 0], [57, 2, 43, 0], [58, 0, 44, 0], [59, 0, 45, 0], [60, 0, 46, 0], [61, 2, 47, 7], [61, 6, 47, 13, "WHEN_UNLOCKED_THIS_DEVICE_ONLY"], [61, 36, 47, 43], [61, 39, 47, 43, "exports"], [61, 46, 47, 43], [61, 47, 47, 43, "WHEN_UNLOCKED_THIS_DEVICE_ONLY"], [61, 77, 47, 43], [61, 80, 47, 46, "ExpoSecureStore"], [61, 104, 47, 61], [61, 105, 47, 62, "WHEN_UNLOCKED_THIS_DEVICE_ONLY"], [61, 135, 47, 92], [62, 2, 48, 0], [63, 2, 49, 0], [64, 0, 50, 0], [65, 0, 51, 0], [66, 0, 52, 0], [67, 0, 53, 0], [68, 0, 54, 0], [69, 0, 55, 0], [70, 2, 49, 0], [70, 11, 56, 22, "isAvailableAsync"], [70, 27, 56, 38, "isAvailableAsync"], [70, 28, 56, 38], [71, 4, 56, 38], [71, 11, 56, 38, "_isAvailableAsync"], [71, 28, 56, 38], [71, 29, 56, 38, "apply"], [71, 34, 56, 38], [71, 41, 56, 38, "arguments"], [71, 50, 56, 38], [72, 2, 56, 38], [72, 4, 59, 0], [73, 2, 60, 0], [74, 0, 61, 0], [75, 0, 62, 0], [76, 0, 63, 0], [77, 0, 64, 0], [78, 0, 65, 0], [79, 0, 66, 0], [80, 0, 67, 0], [81, 2, 60, 0], [81, 11, 60, 0, "_isAvailableAsync"], [81, 29, 60, 0], [82, 4, 60, 0, "_isAvailableAsync"], [82, 21, 60, 0], [82, 28, 60, 0, "_asyncToGenerator2"], [82, 46, 60, 0], [82, 47, 60, 0, "default"], [82, 54, 60, 0], [82, 56, 56, 7], [82, 69, 56, 41], [83, 6, 57, 4], [83, 13, 57, 11], [83, 14, 57, 12], [83, 15, 57, 13, "ExpoSecureStore"], [83, 39, 57, 28], [83, 40, 57, 29, "getValueWithKeyAsync"], [83, 60, 57, 49], [84, 4, 58, 0], [84, 5, 58, 1], [85, 4, 58, 1], [85, 11, 58, 1, "_isAvailableAsync"], [85, 28, 58, 1], [85, 29, 58, 1, "apply"], [85, 34, 58, 1], [85, 41, 58, 1, "arguments"], [85, 50, 58, 1], [86, 2, 58, 1], [87, 2, 58, 1], [87, 11, 68, 22, "deleteItemAsync"], [87, 26, 68, 37, "deleteItemAsync"], [87, 27, 68, 37, "_x"], [87, 29, 68, 37], [88, 4, 68, 37], [88, 11, 68, 37, "_deleteItemAsync"], [88, 27, 68, 37], [88, 28, 68, 37, "apply"], [88, 33, 68, 37], [88, 40, 68, 37, "arguments"], [88, 49, 68, 37], [89, 2, 68, 37], [89, 4, 72, 0], [90, 2, 73, 0], [91, 0, 74, 0], [92, 0, 75, 0], [93, 0, 76, 0], [94, 0, 77, 0], [95, 0, 78, 0], [96, 0, 79, 0], [97, 0, 80, 0], [98, 0, 81, 0], [99, 0, 82, 0], [100, 0, 83, 0], [101, 0, 84, 0], [102, 0, 85, 0], [103, 2, 73, 0], [103, 11, 73, 0, "_deleteItemAsync"], [103, 28, 73, 0], [104, 4, 73, 0, "_deleteItemAsync"], [104, 20, 73, 0], [104, 27, 73, 0, "_asyncToGenerator2"], [104, 45, 73, 0], [104, 46, 73, 0, "default"], [104, 53, 73, 0], [104, 55, 68, 7], [104, 66, 68, 38, "key"], [104, 69, 68, 41], [104, 71, 68, 57], [105, 6, 68, 57], [105, 10, 68, 43, "options"], [105, 17, 68, 50], [105, 20, 68, 50, "arguments"], [105, 29, 68, 50], [105, 30, 68, 50, "length"], [105, 36, 68, 50], [105, 44, 68, 50, "arguments"], [105, 53, 68, 50], [105, 61, 68, 50, "undefined"], [105, 70, 68, 50], [105, 73, 68, 50, "arguments"], [105, 82, 68, 50], [105, 88, 68, 53], [105, 89, 68, 54], [105, 90, 68, 55], [106, 6, 69, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [106, 20, 69, 18], [106, 21, 69, 19, "key"], [106, 24, 69, 22], [106, 25, 69, 23], [107, 6, 70, 4], [107, 12, 70, 10, "ExpoSecureStore"], [107, 36, 70, 25], [107, 37, 70, 26, "deleteValueWithKeyAsync"], [107, 60, 70, 49], [107, 61, 70, 50, "key"], [107, 64, 70, 53], [107, 66, 70, 55, "options"], [107, 73, 70, 62], [107, 74, 70, 63], [108, 4, 71, 0], [108, 5, 71, 1], [109, 4, 71, 1], [109, 11, 71, 1, "_deleteItemAsync"], [109, 27, 71, 1], [109, 28, 71, 1, "apply"], [109, 33, 71, 1], [109, 40, 71, 1, "arguments"], [109, 49, 71, 1], [110, 2, 71, 1], [111, 2, 71, 1], [111, 11, 86, 22, "getItemAsync"], [111, 23, 86, 34, "getItemAsync"], [111, 24, 86, 34, "_x2"], [111, 27, 86, 34], [112, 4, 86, 34], [112, 11, 86, 34, "_getItemAsync"], [112, 24, 86, 34], [112, 25, 86, 34, "apply"], [112, 30, 86, 34], [112, 37, 86, 34, "arguments"], [112, 46, 86, 34], [113, 2, 86, 34], [113, 4, 90, 0], [114, 2, 91, 0], [115, 0, 92, 0], [116, 0, 93, 0], [117, 0, 94, 0], [118, 0, 95, 0], [119, 0, 96, 0], [120, 0, 97, 0], [121, 0, 98, 0], [122, 0, 99, 0], [123, 2, 91, 0], [123, 11, 91, 0, "_getItemAsync"], [123, 25, 91, 0], [124, 4, 91, 0, "_getItemAsync"], [124, 17, 91, 0], [124, 24, 91, 0, "_asyncToGenerator2"], [124, 42, 91, 0], [124, 43, 91, 0, "default"], [124, 50, 91, 0], [124, 52, 86, 7], [124, 63, 86, 35, "key"], [124, 66, 86, 38], [124, 68, 86, 54], [125, 6, 86, 54], [125, 10, 86, 40, "options"], [125, 17, 86, 47], [125, 20, 86, 47, "arguments"], [125, 29, 86, 47], [125, 30, 86, 47, "length"], [125, 36, 86, 47], [125, 44, 86, 47, "arguments"], [125, 53, 86, 47], [125, 61, 86, 47, "undefined"], [125, 70, 86, 47], [125, 73, 86, 47, "arguments"], [125, 82, 86, 47], [125, 88, 86, 50], [125, 89, 86, 51], [125, 90, 86, 52], [126, 6, 87, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [126, 20, 87, 18], [126, 21, 87, 19, "key"], [126, 24, 87, 22], [126, 25, 87, 23], [127, 6, 88, 4], [127, 19, 88, 17, "ExpoSecureStore"], [127, 43, 88, 32], [127, 44, 88, 33, "getValueWithKeyAsync"], [127, 64, 88, 53], [127, 65, 88, 54, "key"], [127, 68, 88, 57], [127, 70, 88, 59, "options"], [127, 77, 88, 66], [127, 78, 88, 67], [128, 4, 89, 0], [128, 5, 89, 1], [129, 4, 89, 1], [129, 11, 89, 1, "_getItemAsync"], [129, 24, 89, 1], [129, 25, 89, 1, "apply"], [129, 30, 89, 1], [129, 37, 89, 1, "arguments"], [129, 46, 89, 1], [130, 2, 89, 1], [131, 2, 89, 1], [131, 11, 100, 22, "setItemAsync"], [131, 23, 100, 34, "setItemAsync"], [131, 24, 100, 34, "_x3"], [131, 27, 100, 34], [131, 29, 100, 34, "_x4"], [131, 32, 100, 34], [132, 4, 100, 34], [132, 11, 100, 34, "_setItemAsync"], [132, 24, 100, 34], [132, 25, 100, 34, "apply"], [132, 30, 100, 34], [132, 37, 100, 34, "arguments"], [132, 46, 100, 34], [133, 2, 100, 34], [134, 2, 107, 0], [135, 0, 108, 0], [136, 0, 109, 0], [137, 0, 110, 0], [138, 0, 111, 0], [139, 0, 112, 0], [140, 0, 113, 0], [141, 0, 114, 0], [142, 0, 115, 0], [143, 2, 107, 0], [143, 11, 107, 0, "_setItemAsync"], [143, 25, 107, 0], [144, 4, 107, 0, "_setItemAsync"], [144, 17, 107, 0], [144, 24, 107, 0, "_asyncToGenerator2"], [144, 42, 107, 0], [144, 43, 107, 0, "default"], [144, 50, 107, 0], [144, 52, 100, 7], [144, 63, 100, 35, "key"], [144, 66, 100, 38], [144, 68, 100, 40, "value"], [144, 73, 100, 45], [144, 75, 100, 61], [145, 6, 100, 61], [145, 10, 100, 47, "options"], [145, 17, 100, 54], [145, 20, 100, 54, "arguments"], [145, 29, 100, 54], [145, 30, 100, 54, "length"], [145, 36, 100, 54], [145, 44, 100, 54, "arguments"], [145, 53, 100, 54], [145, 61, 100, 54, "undefined"], [145, 70, 100, 54], [145, 73, 100, 54, "arguments"], [145, 82, 100, 54], [145, 88, 100, 57], [145, 89, 100, 58], [145, 90, 100, 59], [146, 6, 101, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [146, 20, 101, 18], [146, 21, 101, 19, "key"], [146, 24, 101, 22], [146, 25, 101, 23], [147, 6, 102, 4], [147, 10, 102, 8], [147, 11, 102, 9, "isValidValue"], [147, 23, 102, 21], [147, 24, 102, 22, "value"], [147, 29, 102, 27], [147, 30, 102, 28], [147, 32, 102, 30], [148, 8, 103, 8], [148, 14, 103, 14], [148, 18, 103, 18, "Error"], [148, 23, 103, 23], [148, 24, 103, 24], [148, 149, 103, 149], [148, 150, 103, 150], [149, 6, 104, 4], [150, 6, 105, 4], [150, 12, 105, 10, "ExpoSecureStore"], [150, 36, 105, 25], [150, 37, 105, 26, "setValueWithKeyAsync"], [150, 57, 105, 46], [150, 58, 105, 47, "value"], [150, 63, 105, 52], [150, 65, 105, 54, "key"], [150, 68, 105, 57], [150, 70, 105, 59, "options"], [150, 77, 105, 66], [150, 78, 105, 67], [151, 4, 106, 0], [151, 5, 106, 1], [152, 4, 106, 1], [152, 11, 106, 1, "_setItemAsync"], [152, 24, 106, 1], [152, 25, 106, 1, "apply"], [152, 30, 106, 1], [152, 37, 106, 1, "arguments"], [152, 46, 106, 1], [153, 2, 106, 1], [154, 2, 116, 7], [154, 11, 116, 16, "setItem"], [154, 18, 116, 23, "setItem"], [154, 19, 116, 24, "key"], [154, 22, 116, 27], [154, 24, 116, 29, "value"], [154, 29, 116, 34], [154, 31, 116, 50], [155, 4, 116, 50], [155, 8, 116, 36, "options"], [155, 15, 116, 43], [155, 18, 116, 43, "arguments"], [155, 27, 116, 43], [155, 28, 116, 43, "length"], [155, 34, 116, 43], [155, 42, 116, 43, "arguments"], [155, 51, 116, 43], [155, 59, 116, 43, "undefined"], [155, 68, 116, 43], [155, 71, 116, 43, "arguments"], [155, 80, 116, 43], [155, 86, 116, 46], [155, 87, 116, 47], [155, 88, 116, 48], [156, 4, 117, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [156, 18, 117, 18], [156, 19, 117, 19, "key"], [156, 22, 117, 22], [156, 23, 117, 23], [157, 4, 118, 4], [157, 8, 118, 8], [157, 9, 118, 9, "isValidValue"], [157, 21, 118, 21], [157, 22, 118, 22, "value"], [157, 27, 118, 27], [157, 28, 118, 28], [157, 30, 118, 30], [158, 6, 119, 8], [158, 12, 119, 14], [158, 16, 119, 18, "Error"], [158, 21, 119, 23], [158, 22, 119, 24], [158, 147, 119, 149], [158, 148, 119, 150], [159, 4, 120, 4], [160, 4, 121, 4], [160, 11, 121, 11, "ExpoSecureStore"], [160, 35, 121, 26], [160, 36, 121, 27, "setValueWithKeySync"], [160, 55, 121, 46], [160, 56, 121, 47, "value"], [160, 61, 121, 52], [160, 63, 121, 54, "key"], [160, 66, 121, 57], [160, 68, 121, 59, "options"], [160, 75, 121, 66], [160, 76, 121, 67], [161, 2, 122, 0], [162, 2, 123, 0], [163, 0, 124, 0], [164, 0, 125, 0], [165, 0, 126, 0], [166, 0, 127, 0], [167, 0, 128, 0], [168, 0, 129, 0], [169, 0, 130, 0], [170, 0, 131, 0], [171, 0, 132, 0], [172, 2, 133, 7], [172, 11, 133, 16, "getItem"], [172, 18, 133, 23, "getItem"], [172, 19, 133, 24, "key"], [172, 22, 133, 27], [172, 24, 133, 43], [173, 4, 133, 43], [173, 8, 133, 29, "options"], [173, 15, 133, 36], [173, 18, 133, 36, "arguments"], [173, 27, 133, 36], [173, 28, 133, 36, "length"], [173, 34, 133, 36], [173, 42, 133, 36, "arguments"], [173, 51, 133, 36], [173, 59, 133, 36, "undefined"], [173, 68, 133, 36], [173, 71, 133, 36, "arguments"], [173, 80, 133, 36], [173, 86, 133, 39], [173, 87, 133, 40], [173, 88, 133, 41], [174, 4, 134, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [174, 18, 134, 18], [174, 19, 134, 19, "key"], [174, 22, 134, 22], [174, 23, 134, 23], [175, 4, 135, 4], [175, 11, 135, 11, "ExpoSecureStore"], [175, 35, 135, 26], [175, 36, 135, 27, "getValueWithKeySync"], [175, 55, 135, 46], [175, 56, 135, 47, "key"], [175, 59, 135, 50], [175, 61, 135, 52, "options"], [175, 68, 135, 59], [175, 69, 135, 60], [176, 2, 136, 0], [177, 2, 137, 0], [178, 0, 138, 0], [179, 0, 139, 0], [180, 0, 140, 0], [181, 0, 141, 0], [182, 0, 142, 0], [183, 2, 143, 7], [183, 11, 143, 16, "canUseBiometricAuthentication"], [183, 40, 143, 45, "canUseBiometricAuthentication"], [183, 41, 143, 45], [183, 43, 143, 48], [184, 4, 144, 4], [184, 11, 144, 11, "ExpoSecureStore"], [184, 35, 144, 26], [184, 36, 144, 27, "canUseBiometricAuthentication"], [184, 65, 144, 56], [184, 66, 144, 57], [184, 67, 144, 58], [185, 2, 145, 0], [186, 2, 146, 0], [186, 11, 146, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [186, 25, 146, 23, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [186, 26, 146, 24, "key"], [186, 29, 146, 27], [186, 31, 146, 29], [187, 4, 147, 4], [187, 8, 147, 8], [187, 9, 147, 9, "is<PERSON><PERSON><PERSON><PERSON><PERSON>"], [187, 19, 147, 19], [187, 20, 147, 20, "key"], [187, 23, 147, 23], [187, 24, 147, 24], [187, 26, 147, 26], [188, 6, 148, 8], [188, 12, 148, 14], [188, 16, 148, 18, "Error"], [188, 21, 148, 23], [188, 22, 148, 24], [188, 144, 148, 146], [188, 145, 148, 147], [189, 4, 149, 4], [190, 2, 150, 0], [191, 2, 151, 0], [191, 11, 151, 9, "is<PERSON><PERSON><PERSON><PERSON><PERSON>"], [191, 21, 151, 19, "is<PERSON><PERSON><PERSON><PERSON><PERSON>"], [191, 22, 151, 20, "key"], [191, 25, 151, 23], [191, 27, 151, 25], [192, 4, 152, 4], [192, 11, 152, 11], [192, 18, 152, 18, "key"], [192, 21, 152, 21], [192, 26, 152, 26], [192, 34, 152, 34], [192, 38, 152, 38], [192, 49, 152, 49], [192, 50, 152, 50, "test"], [192, 54, 152, 54], [192, 55, 152, 55, "key"], [192, 58, 152, 58], [192, 59, 152, 59], [193, 2, 153, 0], [194, 2, 154, 0], [194, 11, 154, 9, "isValidValue"], [194, 23, 154, 21, "isValidValue"], [194, 24, 154, 22, "value"], [194, 29, 154, 27], [194, 31, 154, 29], [195, 4, 155, 4], [195, 8, 155, 8], [195, 15, 155, 15, "value"], [195, 20, 155, 20], [195, 25, 155, 25], [195, 33, 155, 33], [195, 35, 155, 35], [196, 6, 156, 8], [196, 13, 156, 15], [196, 18, 156, 20], [197, 4, 157, 4], [198, 4, 158, 4], [198, 8, 158, 8], [198, 12, 158, 8, "byteCountOverLimit"], [198, 43, 158, 26], [198, 45, 158, 27, "value"], [198, 50, 158, 32], [198, 52, 158, 34, "VALUE_BYTES_LIMIT"], [198, 82, 158, 51], [198, 83, 158, 52], [198, 85, 158, 54], [199, 6, 159, 8, "console"], [199, 13, 159, 15], [199, 14, 159, 16, "warn"], [199, 18, 159, 20], [199, 19, 159, 21], [199, 71, 159, 73, "VALUE_BYTES_LIMIT"], [199, 101, 159, 90], [199, 203, 159, 192], [199, 204, 159, 193], [200, 4, 160, 4], [201, 4, 161, 4], [201, 11, 161, 11], [201, 15, 161, 15], [202, 2, 162, 0], [203, 0, 162, 1], [203, 3]], "functionMap": {"names": ["<global>", "isAvailableAsync", "deleteItemAsync", "getItemAsync", "setItemAsync", "setItem", "getItem", "canUseBiometricAuthentication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "isValidValue"], "mappings": "AAA;OCuD;CDE;OEU;CFG;OGe;CHG;OIW;CJM;OKU;CLM;OMW;CNG;OOO;CPE;AQC;CRI;ASC;CTE;AUC;CVQ"}}, "type": "js/module"}]}