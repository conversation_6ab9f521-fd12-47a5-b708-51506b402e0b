{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.hasUrlProtocolPrefix = hasUrlProtocolPrefix;\n  exports.isWellKnownUri = isWellKnownUri;\n  exports.shouldLinkExternally = shouldLinkExternally;\n  /**\n   * Does the input string start with a valid URL scheme.\n   * NOTE: Additional strictness added to ensure URLs sent in query parameters for in-app navigation are not matched.\n   */\n  function hasUrlProtocolPrefix(href) {\n    return /^([\\w\\d_+.-]+:)?\\/\\//.test(href);\n  }\n  function isWellKnownUri(href) {\n    // This is a hack and we should change this to work like the web in the future where we have full confidence in the\n    // ability to match URLs and send anything unmatched to the OS. The main difference between this and `hasUrlProtocolPrefix` is\n    // that we don't require `//`, e.g. `mailto:` is valid and common, and `mailto://bacon` is invalid.\n    return /^(https?|mailto|tel|sms|geo|maps|market|itmss?|itms-apps|content|file):/.test(href);\n  }\n  function shouldLinkExternally(href) {\n    // Cheap check first to avoid regex if the href is not a path fragment.\n    return !href.startsWith('.') && (hasUrlProtocolPrefix(href) || isWellKnownUri(href));\n  }\n});", "lineCount": 27, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "hasUrlProtocolPrefix"], [7, 30, 3, 28], [7, 33, 3, 31, "hasUrlProtocolPrefix"], [7, 53, 3, 51], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "isWellKnownUri"], [8, 24, 4, 22], [8, 27, 4, 25, "isWellKnownUri"], [8, 41, 4, 39], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "shouldLinkExternally"], [9, 30, 5, 28], [9, 33, 5, 31, "shouldLinkExternally"], [9, 53, 5, 51], [10, 2, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 2, 10, 0], [14, 11, 10, 9, "hasUrlProtocolPrefix"], [14, 31, 10, 29, "hasUrlProtocolPrefix"], [14, 32, 10, 30, "href"], [14, 36, 10, 34], [14, 38, 10, 36], [15, 4, 11, 4], [15, 11, 11, 11], [15, 33, 11, 33], [15, 34, 11, 34, "test"], [15, 38, 11, 38], [15, 39, 11, 39, "href"], [15, 43, 11, 43], [15, 44, 11, 44], [16, 2, 12, 0], [17, 2, 13, 0], [17, 11, 13, 9, "isWellKnownUri"], [17, 25, 13, 23, "isWellKnownUri"], [17, 26, 13, 24, "href"], [17, 30, 13, 28], [17, 32, 13, 30], [18, 4, 14, 4], [19, 4, 15, 4], [20, 4, 16, 4], [21, 4, 17, 4], [21, 11, 17, 11], [21, 84, 17, 84], [21, 85, 17, 85, "test"], [21, 89, 17, 89], [21, 90, 17, 90, "href"], [21, 94, 17, 94], [21, 95, 17, 95], [22, 2, 18, 0], [23, 2, 19, 0], [23, 11, 19, 9, "shouldLinkExternally"], [23, 31, 19, 29, "shouldLinkExternally"], [23, 32, 19, 30, "href"], [23, 36, 19, 34], [23, 38, 19, 36], [24, 4, 20, 4], [25, 4, 21, 4], [25, 11, 21, 11], [25, 12, 21, 12, "href"], [25, 16, 21, 16], [25, 17, 21, 17, "startsWith"], [25, 27, 21, 27], [25, 28, 21, 28], [25, 31, 21, 31], [25, 32, 21, 32], [25, 37, 21, 37, "hasUrlProtocolPrefix"], [25, 57, 21, 57], [25, 58, 21, 58, "href"], [25, 62, 21, 62], [25, 63, 21, 63], [25, 67, 21, 67, "isWellKnownUri"], [25, 81, 21, 81], [25, 82, 21, 82, "href"], [25, 86, 21, 86], [25, 87, 21, 87], [25, 88, 21, 88], [26, 2, 22, 0], [27, 0, 22, 1], [27, 3]], "functionMap": {"names": ["<global>", "hasUrlProtocolPrefix", "isWellKnownUri", "shouldLinkExternally"], "mappings": "AAA;ACS;CDE;AEC;CFK;AGC;CHG"}}, "type": "js/module"}]}