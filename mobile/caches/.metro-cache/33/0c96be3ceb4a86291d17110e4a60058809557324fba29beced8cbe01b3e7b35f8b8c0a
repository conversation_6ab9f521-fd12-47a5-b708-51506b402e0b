{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2Fnode_modules%2Fexpo-router%2Fassets\",\n    \"width\": 436,\n    \"height\": 266,\n    \"scales\": [1],\n    \"hash\": \"20e71bdf79e3a97bf55fd9e164041578\",\n    \"name\": \"unmatched\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"20e71bdf79e3a97bf55fd9e164041578\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 90, 1, 110], [5, 4, 1, 111], [5, 11, 1, 118], [5, 13, 1, 119], [5, 16, 1, 122], [6, 4, 1, 123], [6, 12, 1, 131], [6, 14, 1, 132], [6, 17, 1, 135], [7, 4, 1, 136], [7, 12, 1, 144], [7, 14, 1, 145], [7, 15, 1, 146], [7, 16, 1, 147], [7, 17, 1, 148], [8, 4, 1, 149], [8, 10, 1, 155], [8, 12, 1, 156], [8, 46, 1, 190], [9, 4, 1, 191], [9, 10, 1, 197], [9, 12, 1, 198], [9, 23, 1, 209], [10, 4, 1, 210], [10, 10, 1, 216], [10, 12, 1, 217], [10, 17, 1, 222], [11, 4, 1, 223], [11, 16, 1, 235], [11, 18, 1, 236], [11, 19, 1, 237], [11, 53, 1, 271], [12, 2, 1, 272], [12, 3, 1, 273], [13, 0, 1, 273], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}