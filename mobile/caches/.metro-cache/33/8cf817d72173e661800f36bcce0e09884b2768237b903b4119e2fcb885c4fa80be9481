{"dependencies": [{"name": "./js-reanimated/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 106}, "end": {"line": 4, "column": 68, "index": 174}}], "key": "8UB+gGkkvGm0KYf1+SQjw0EK/mc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  // this file was created to prevent NativeReanimated from being included in the web bundle\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ReanimatedModule = void 0;\n  var _index = require(_dependencyMap[0], \"./js-reanimated/index.js\");\n  const ReanimatedModule = exports.ReanimatedModule = (0, _index.createJSReanimatedModule)();\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 3, 0, "Object"], [5, 8, 3, 0], [5, 9, 3, 0, "defineProperty"], [5, 23, 3, 0], [5, 24, 3, 0, "exports"], [5, 31, 3, 0], [6, 4, 3, 0, "value"], [6, 9, 3, 0], [7, 2, 3, 0], [8, 2, 3, 0, "exports"], [8, 9, 3, 0], [8, 10, 3, 0, "ReanimatedModule"], [8, 26, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_index"], [9, 12, 4, 0], [9, 15, 4, 0, "require"], [9, 22, 4, 0], [9, 23, 4, 0, "_dependencyMap"], [9, 37, 4, 0], [10, 2, 5, 7], [10, 8, 5, 13, "ReanimatedModule"], [10, 24, 5, 29], [10, 27, 5, 29, "exports"], [10, 34, 5, 29], [10, 35, 5, 29, "ReanimatedModule"], [10, 51, 5, 29], [10, 54, 5, 32], [10, 58, 5, 32, "createJSReanimatedModule"], [10, 89, 5, 56], [10, 91, 5, 57], [10, 92, 5, 58], [11, 0, 5, 59], [11, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}