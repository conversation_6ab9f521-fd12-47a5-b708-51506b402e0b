{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "canvaskit-wasm/bin/full/canvaskit", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 76}, "end": {"line": 3, "column": 62, "index": 138}}], "key": "ix9b8QFceHcicqggWNB7ngpEZ+Q=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LoadSkiaWeb = exports.LoadSkia = void 0;\n  var _canvaskit = _interopRequireDefault(require(_dependencyMap[1], \"canvaskit-wasm/bin/full/canvaskit\"));\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n\n  let ckSharedPromise;\n  const LoadSkiaWeb = async opts => {\n    var _ckSharedPromise;\n    if (global.CanvasKit !== undefined) {\n      return;\n    }\n    ckSharedPromise = (_ckSharedPromise = ckSharedPromise) !== null && _ckSharedPromise !== void 0 ? _ckSharedPromise : (0, _canvaskit.default)(opts);\n    const CanvasKit = await ckSharedPromise;\n    // The CanvasKit API is stored on the global object and used\n    // to create the JsiSKApi in the Skia.web.ts file.\n    global.CanvasKit = CanvasKit;\n  };\n\n  // We keep this function for backward compatibility\n  exports.LoadSkiaWeb = LoadSkiaWeb;\n  const LoadSkia = exports.LoadSkia = LoadSkiaWeb;\n});", "lineCount": 27, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_canvaskit"], [7, 16, 3, 0], [7, 19, 3, 0, "_interopRequireDefault"], [7, 41, 3, 0], [7, 42, 3, 0, "require"], [7, 49, 3, 0], [7, 50, 3, 0, "_dependencyMap"], [7, 64, 3, 0], [8, 2, 1, 0], [9, 2, 2, 0], [11, 2, 4, 0], [11, 6, 4, 4, "ckSharedPromise"], [11, 21, 4, 19], [12, 2, 5, 7], [12, 8, 5, 13, "LoadSkiaWeb"], [12, 19, 5, 24], [12, 22, 5, 27], [12, 28, 5, 33, "opts"], [12, 32, 5, 37], [12, 36, 5, 41], [13, 4, 6, 2], [13, 8, 6, 6, "_ckSharedPromise"], [13, 24, 6, 22], [14, 4, 7, 2], [14, 8, 7, 6, "global"], [14, 14, 7, 12], [14, 15, 7, 13, "CanvasKit"], [14, 24, 7, 22], [14, 29, 7, 27, "undefined"], [14, 38, 7, 36], [14, 40, 7, 38], [15, 6, 8, 4], [16, 4, 9, 2], [17, 4, 10, 2, "ckSharedPromise"], [17, 19, 10, 17], [17, 22, 10, 20], [17, 23, 10, 21, "_ckSharedPromise"], [17, 39, 10, 37], [17, 42, 10, 40, "ckSharedPromise"], [17, 57, 10, 55], [17, 63, 10, 61], [17, 67, 10, 65], [17, 71, 10, 69, "_ckSharedPromise"], [17, 87, 10, 85], [17, 92, 10, 90], [17, 97, 10, 95], [17, 98, 10, 96], [17, 101, 10, 99, "_ckSharedPromise"], [17, 117, 10, 115], [17, 120, 10, 118], [17, 124, 10, 118, "CanvasKitInit"], [17, 142, 10, 131], [17, 144, 10, 132, "opts"], [17, 148, 10, 136], [17, 149, 10, 137], [18, 4, 11, 2], [18, 10, 11, 8, "CanvasKit"], [18, 19, 11, 17], [18, 22, 11, 20], [18, 28, 11, 26, "ckSharedPromise"], [18, 43, 11, 41], [19, 4, 12, 2], [20, 4, 13, 2], [21, 4, 14, 2, "global"], [21, 10, 14, 8], [21, 11, 14, 9, "CanvasKit"], [21, 20, 14, 18], [21, 23, 14, 21, "CanvasKit"], [21, 32, 14, 30], [22, 2, 15, 0], [22, 3, 15, 1], [24, 2, 17, 0], [25, 2, 17, 0, "exports"], [25, 9, 17, 0], [25, 10, 17, 0, "LoadSkiaWeb"], [25, 21, 17, 0], [25, 24, 17, 0, "LoadSkiaWeb"], [25, 35, 17, 0], [26, 2, 18, 7], [26, 8, 18, 13, "LoadSkia"], [26, 16, 18, 21], [26, 19, 18, 21, "exports"], [26, 26, 18, 21], [26, 27, 18, 21, "LoadSkia"], [26, 35, 18, 21], [26, 38, 18, 24, "LoadSkiaWeb"], [26, 49, 18, 35], [27, 0, 18, 36], [27, 3]], "functionMap": {"names": ["<global>", "LoadSkiaWeb"], "mappings": "AAA;2BCI;CDU"}}, "type": "js/module"}]}