{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 39, "index": 39}}], "key": "pPv5KzfRT0rL6NCr7G9k0o4d1W8=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 85, "index": 125}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./AssetUris", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 126}, "end": {"line": 3, "column": 49, "index": 175}}], "key": "s5UQlFlCKXftpyl/zrvZYZ0/A8E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.IS_ENV_WITH_LOCAL_ASSETS = void 0;\n  exports.getLocalAssets = getLocalAssets;\n  exports.getManifest2 = getManifest2;\n  exports.manifestBaseUrl = void 0;\n  var _expoConstants = _interopRequireDefault(require(_dependencyMap[1], \"expo-constants\"));\n  var _expoModulesCore = require(_dependencyMap[2], \"expo-modules-core\");\n  var _AssetUris = require(_dependencyMap[3], \"./AssetUris\");\n  var ExpoUpdates = (0, _expoModulesCore.requireOptionalNativeModule)('ExpoUpdates');\n  var NativeExpoGoModule = (() => {\n    try {\n      return (0, _expoModulesCore.requireNativeModule)('ExpoGo');\n    } catch {\n      return null;\n    }\n  })();\n  function isRunningInExpoGo() {\n    return NativeExpoGoModule != null;\n  }\n  // expo-updates (and Expo Go expo-updates override) manages assets from updates and exposes\n  // the ExpoUpdates.localAssets constant containing information about the assets.\n  var expoUpdatesIsInstalledAndEnabled = !!ExpoUpdates?.isEnabled;\n  var expoUpdatesIsUsingEmbeddedAssets = ExpoUpdates?.isUsingEmbeddedAssets;\n  // if expo-updates is installed but we're running directly from the embedded bundle, we don't want\n  // to override the AssetSourceResolver.\n  var shouldUseUpdatesAssetResolution = expoUpdatesIsInstalledAndEnabled && !expoUpdatesIsUsingEmbeddedAssets;\n  // Expo Go always uses the updates module for asset resolution (local assets) since it\n  // overrides the expo-updates module.\n  var IS_ENV_WITH_LOCAL_ASSETS = exports.IS_ENV_WITH_LOCAL_ASSETS = isRunningInExpoGo() || shouldUseUpdatesAssetResolution;\n  // Get the localAssets property from the ExpoUpdates native module so that we do\n  // not need to include expo-updates as a dependency of expo-asset\n  function getLocalAssets() {\n    return ExpoUpdates?.localAssets ?? {};\n  }\n  function getManifest2() {\n    return _expoConstants.default.__unsafeNoWarnManifest2;\n  }\n  // Compute manifest base URL if available\n  var manifestBaseUrl = exports.manifestBaseUrl = _expoConstants.default.experienceUrl ? (0, _AssetUris.getManifestBaseUrl)(_expoConstants.default.experienceUrl) : null;\n});", "lineCount": 44, "map": [[10, 2, 1, 0], [10, 6, 1, 0, "_expoConstants"], [10, 20, 1, 0], [10, 23, 1, 0, "_interopRequireDefault"], [10, 45, 1, 0], [10, 46, 1, 0, "require"], [10, 53, 1, 0], [10, 54, 1, 0, "_dependencyMap"], [10, 68, 1, 0], [11, 2, 2, 0], [11, 6, 2, 0, "_expoModulesCore"], [11, 22, 2, 0], [11, 25, 2, 0, "require"], [11, 32, 2, 0], [11, 33, 2, 0, "_dependencyMap"], [11, 47, 2, 0], [12, 2, 3, 0], [12, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON>"], [12, 16, 3, 0], [12, 19, 3, 0, "require"], [12, 26, 3, 0], [12, 27, 3, 0, "_dependencyMap"], [12, 41, 3, 0], [13, 2, 4, 0], [13, 6, 4, 6, "ExpoUpdates"], [13, 17, 4, 17], [13, 20, 4, 20], [13, 24, 4, 20, "requireOptionalNativeModule"], [13, 68, 4, 47], [13, 70, 4, 48], [13, 83, 4, 61], [13, 84, 4, 62], [14, 2, 5, 0], [14, 6, 5, 6, "NativeExpoGoModule"], [14, 24, 5, 24], [14, 27, 5, 27], [14, 28, 5, 28], [14, 34, 5, 34], [15, 4, 6, 4], [15, 8, 6, 8], [16, 6, 7, 8], [16, 13, 7, 15], [16, 17, 7, 15, "requireNativeModule"], [16, 53, 7, 34], [16, 55, 7, 35], [16, 63, 7, 43], [16, 64, 7, 44], [17, 4, 8, 4], [17, 5, 8, 5], [17, 6, 9, 4], [17, 12, 9, 10], [18, 6, 10, 8], [18, 13, 10, 15], [18, 17, 10, 19], [19, 4, 11, 4], [20, 2, 12, 0], [20, 3, 12, 1], [20, 5, 12, 3], [20, 6, 12, 4], [21, 2, 13, 0], [21, 11, 13, 9, "isRunningInExpoGo"], [21, 28, 13, 26, "isRunningInExpoGo"], [21, 29, 13, 26], [21, 31, 13, 29], [22, 4, 14, 4], [22, 11, 14, 11, "NativeExpoGoModule"], [22, 29, 14, 29], [22, 33, 14, 33], [22, 37, 14, 37], [23, 2, 15, 0], [24, 2, 16, 0], [25, 2, 17, 0], [26, 2, 18, 0], [26, 6, 18, 6, "expoUpdatesIsInstalledAndEnabled"], [26, 38, 18, 38], [26, 41, 18, 41], [26, 42, 18, 42], [26, 43, 18, 43, "ExpoUpdates"], [26, 54, 18, 54], [26, 56, 18, 56, "isEnabled"], [26, 65, 18, 65], [27, 2, 19, 0], [27, 6, 19, 6, "expoUpdatesIsUsingEmbeddedAssets"], [27, 38, 19, 38], [27, 41, 19, 41, "ExpoUpdates"], [27, 52, 19, 52], [27, 54, 19, 54, "isUsingEmbeddedAssets"], [27, 75, 19, 75], [28, 2, 20, 0], [29, 2, 21, 0], [30, 2, 22, 0], [30, 6, 22, 6, "shouldUseUpdatesAssetResolution"], [30, 37, 22, 37], [30, 40, 22, 40, "expoUpdatesIsInstalledAndEnabled"], [30, 72, 22, 72], [30, 76, 22, 76], [30, 77, 22, 77, "expoUpdatesIsUsingEmbeddedAssets"], [30, 109, 22, 109], [31, 2, 23, 0], [32, 2, 24, 0], [33, 2, 25, 7], [33, 6, 25, 13, "IS_ENV_WITH_LOCAL_ASSETS"], [33, 30, 25, 37], [33, 33, 25, 37, "exports"], [33, 40, 25, 37], [33, 41, 25, 37, "IS_ENV_WITH_LOCAL_ASSETS"], [33, 65, 25, 37], [33, 68, 25, 40, "isRunningInExpoGo"], [33, 85, 25, 57], [33, 86, 25, 58], [33, 87, 25, 59], [33, 91, 25, 63, "shouldUseUpdatesAssetResolution"], [33, 122, 25, 94], [34, 2, 26, 0], [35, 2, 27, 0], [36, 2, 28, 7], [36, 11, 28, 16, "getLocalAssets"], [36, 25, 28, 30, "getLocalAssets"], [36, 26, 28, 30], [36, 28, 28, 33], [37, 4, 29, 4], [37, 11, 29, 11, "ExpoUpdates"], [37, 22, 29, 22], [37, 24, 29, 24, "localAssets"], [37, 35, 29, 35], [37, 39, 29, 39], [37, 40, 29, 40], [37, 41, 29, 41], [38, 2, 30, 0], [39, 2, 31, 7], [39, 11, 31, 16, "getManifest2"], [39, 23, 31, 28, "getManifest2"], [39, 24, 31, 28], [39, 26, 31, 31], [40, 4, 32, 4], [40, 11, 32, 11, "Constants"], [40, 33, 32, 20], [40, 34, 32, 21, "__unsafeNoWarnManifest2"], [40, 57, 32, 44], [41, 2, 33, 0], [42, 2, 34, 0], [43, 2, 35, 7], [43, 6, 35, 13, "manifestBaseUrl"], [43, 21, 35, 28], [43, 24, 35, 28, "exports"], [43, 31, 35, 28], [43, 32, 35, 28, "manifestBaseUrl"], [43, 47, 35, 28], [43, 50, 35, 31, "Constants"], [43, 72, 35, 40], [43, 73, 35, 41, "experienceUrl"], [43, 86, 35, 54], [43, 89, 36, 6], [43, 93, 36, 6, "getManifestBaseUrl"], [43, 122, 36, 24], [43, 124, 36, 25, "Constants"], [43, 146, 36, 34], [43, 147, 36, 35, "experienceUrl"], [43, 160, 36, 48], [43, 161, 36, 49], [43, 164, 37, 6], [43, 168, 37, 10], [44, 0, 37, 11], [44, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "isRunningInExpoGo", "getLocalAssets", "getManifest2"], "mappings": "AAA;4BCI;CDO;AEC;CFE;OGa;CHE;OIC;CJE"}}, "type": "js/module"}]}