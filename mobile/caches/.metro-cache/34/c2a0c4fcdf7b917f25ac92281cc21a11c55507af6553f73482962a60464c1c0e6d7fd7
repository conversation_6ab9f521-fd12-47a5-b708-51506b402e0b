{"dependencies": [{"name": "./SceneStyleInterpolators.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 65, "index": 80}}], "key": "mgKmmXsupybVHyBqIVq6qlFmGcE=", "exportNames": ["*"]}}, {"name": "./TransitionSpecs.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 81}, "end": {"line": 4, "column": 59, "index": 140}}], "key": "AExhOYu4AYmjYeDtMMk9qQWujeM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ShiftTransition = exports.FadeTransition = void 0;\n  var _SceneStyleInterpolators = require(_dependencyMap[0], \"./SceneStyleInterpolators.js\");\n  var _TransitionSpecs = require(_dependencyMap[1], \"./TransitionSpecs.js\");\n  const FadeTransition = exports.FadeTransition = {\n    transitionSpec: _TransitionSpecs.FadeSpec,\n    sceneStyleInterpolator: _SceneStyleInterpolators.forFade\n  };\n  const ShiftTransition = exports.ShiftTransition = {\n    transitionSpec: _TransitionSpecs.ShiftSpec,\n    sceneStyleInterpolator: _SceneStyleInterpolators.forShift\n  };\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ShiftTransition"], [7, 25, 1, 13], [7, 28, 1, 13, "exports"], [7, 35, 1, 13], [7, 36, 1, 13, "FadeTransition"], [7, 50, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_SceneStyleInterpolators"], [8, 30, 3, 0], [8, 33, 3, 0, "require"], [8, 40, 3, 0], [8, 41, 3, 0, "_dependencyMap"], [8, 55, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_TransitionSpecs"], [9, 22, 4, 0], [9, 25, 4, 0, "require"], [9, 32, 4, 0], [9, 33, 4, 0, "_dependencyMap"], [9, 47, 4, 0], [10, 2, 5, 7], [10, 8, 5, 13, "FadeTransition"], [10, 22, 5, 27], [10, 25, 5, 27, "exports"], [10, 32, 5, 27], [10, 33, 5, 27, "FadeTransition"], [10, 47, 5, 27], [10, 50, 5, 30], [11, 4, 6, 2, "transitionSpec"], [11, 18, 6, 16], [11, 20, 6, 18, "FadeSpec"], [11, 45, 6, 26], [12, 4, 7, 2, "sceneStyleInterpolator"], [12, 26, 7, 24], [12, 28, 7, 26, "forFade"], [13, 2, 8, 0], [13, 3, 8, 1], [14, 2, 9, 7], [14, 8, 9, 13, "ShiftTransition"], [14, 23, 9, 28], [14, 26, 9, 28, "exports"], [14, 33, 9, 28], [14, 34, 9, 28, "ShiftTransition"], [14, 49, 9, 28], [14, 52, 9, 31], [15, 4, 10, 2, "transitionSpec"], [15, 18, 10, 16], [15, 20, 10, 18, "ShiftSpec"], [15, 46, 10, 27], [16, 4, 11, 2, "sceneStyleInterpolator"], [16, 26, 11, 24], [16, 28, 11, 26, "forShift"], [17, 2, 12, 0], [17, 3, 12, 1], [18, 0, 12, 2], [18, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}