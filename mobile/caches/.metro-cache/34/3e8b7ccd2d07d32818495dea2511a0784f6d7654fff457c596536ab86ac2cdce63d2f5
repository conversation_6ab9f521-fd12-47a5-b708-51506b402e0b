{"dependencies": [{"name": "./lib/markup", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 14, "index": 155}, "end": {"line": 7, "column": 37, "index": 178}}], "key": "jmWyOIwUhawNxN+alOZIsh3EVEk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n  exports.test = exports.serialize = exports.default = void 0;\n  var _markup = require(_dependencyMap[0], \"./lib/markup\");\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  const ELEMENT_NODE = 1;\n  const TEXT_NODE = 3;\n  const COMMENT_NODE = 8;\n  const FRAGMENT_NODE = 11;\n  const ELEMENT_REGEXP = /^((HTML|SVG)\\w*)?Element$/;\n  const testHasAttribute = val => {\n    try {\n      return typeof val.hasAttribute === 'function' && val.hasAttribute('is');\n    } catch {\n      return false;\n    }\n  };\n  const testNode = val => {\n    const constructorName = val.constructor.name;\n    const {\n      nodeType,\n      tagName\n    } = val;\n    const isCustomElement = typeof tagName === 'string' && tagName.includes('-') || testHasAttribute(val);\n    return nodeType === ELEMENT_NODE && (ELEMENT_REGEXP.test(constructorName) || isCustomElement) || nodeType === TEXT_NODE && constructorName === 'Text' || nodeType === COMMENT_NODE && constructorName === 'Comment' || nodeType === FRAGMENT_NODE && constructorName === 'DocumentFragment';\n  };\n  const test = val => val?.constructor?.name && testNode(val);\n  exports.test = test;\n  function nodeIsText(node) {\n    return node.nodeType === TEXT_NODE;\n  }\n  function nodeIsComment(node) {\n    return node.nodeType === COMMENT_NODE;\n  }\n  function nodeIsFragment(node) {\n    return node.nodeType === FRAGMENT_NODE;\n  }\n  const serialize = (node, config, indentation, depth, refs, printer) => {\n    if (nodeIsText(node)) {\n      return (0, _markup.printText)(node.data, config);\n    }\n    if (nodeIsComment(node)) {\n      return (0, _markup.printComment)(node.data, config);\n    }\n    const type = nodeIsFragment(node) ? 'DocumentFragment' : node.tagName.toLowerCase();\n    if (++depth > config.maxDepth) {\n      return (0, _markup.printElementAsLeaf)(type, config);\n    }\n    return (0, _markup.printElement)(type, (0, _markup.printProps)(nodeIsFragment(node) ? [] : Array.from(node.attributes, attr => attr.name).sort(), nodeIsFragment(node) ? {} : Array.from(node.attributes).reduce((props, attribute) => {\n      props[attribute.name] = attribute.value;\n      return props;\n    }, {}), config, indentation + config.indent, depth, refs, printer), (0, _markup.printChildren)(Array.prototype.slice.call(node.childNodes || node.children), config, indentation + config.indent, depth, refs, printer), config, indentation);\n  };\n  exports.serialize = serialize;\n  const plugin = {\n    serialize,\n    test\n  };\n  var _default = plugin;\n  exports.default = _default;\n});", "lineCount": 71, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "test"], [7, 14, 6, 12], [7, 17, 6, 15, "exports"], [7, 24, 6, 22], [7, 25, 6, 23, "serialize"], [7, 34, 6, 32], [7, 37, 6, 35, "exports"], [7, 44, 6, 42], [7, 45, 6, 43, "default"], [7, 52, 6, 50], [7, 55, 6, 53], [7, 60, 6, 58], [7, 61, 6, 59], [8, 2, 7, 0], [8, 6, 7, 4, "_markup"], [8, 13, 7, 11], [8, 16, 7, 14, "require"], [8, 23, 7, 21], [8, 24, 7, 21, "_dependencyMap"], [8, 38, 7, 21], [8, 57, 7, 36], [8, 58, 7, 37], [9, 2, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [16, 2, 15, 0], [16, 8, 15, 6, "ELEMENT_NODE"], [16, 20, 15, 18], [16, 23, 15, 21], [16, 24, 15, 22], [17, 2, 16, 0], [17, 8, 16, 6, "TEXT_NODE"], [17, 17, 16, 15], [17, 20, 16, 18], [17, 21, 16, 19], [18, 2, 17, 0], [18, 8, 17, 6, "COMMENT_NODE"], [18, 20, 17, 18], [18, 23, 17, 21], [18, 24, 17, 22], [19, 2, 18, 0], [19, 8, 18, 6, "FRAGMENT_NODE"], [19, 21, 18, 19], [19, 24, 18, 22], [19, 26, 18, 24], [20, 2, 19, 0], [20, 8, 19, 6, "ELEMENT_REGEXP"], [20, 22, 19, 20], [20, 25, 19, 23], [20, 52, 19, 50], [21, 2, 20, 0], [21, 8, 20, 6, "testHasAttribute"], [21, 24, 20, 22], [21, 27, 20, 25, "val"], [21, 30, 20, 28], [21, 34, 20, 32], [22, 4, 21, 2], [22, 8, 21, 6], [23, 6, 22, 4], [23, 13, 22, 11], [23, 20, 22, 18, "val"], [23, 23, 22, 21], [23, 24, 22, 22, "hasAttribute"], [23, 36, 22, 34], [23, 41, 22, 39], [23, 51, 22, 49], [23, 55, 22, 53, "val"], [23, 58, 22, 56], [23, 59, 22, 57, "hasAttribute"], [23, 71, 22, 69], [23, 72, 22, 70], [23, 76, 22, 74], [23, 77, 22, 75], [24, 4, 23, 2], [24, 5, 23, 3], [24, 6, 23, 4], [24, 12, 23, 10], [25, 6, 24, 4], [25, 13, 24, 11], [25, 18, 24, 16], [26, 4, 25, 2], [27, 2, 26, 0], [27, 3, 26, 1], [28, 2, 27, 0], [28, 8, 27, 6, "testNode"], [28, 16, 27, 14], [28, 19, 27, 17, "val"], [28, 22, 27, 20], [28, 26, 27, 24], [29, 4, 28, 2], [29, 10, 28, 8, "constructorName"], [29, 25, 28, 23], [29, 28, 28, 26, "val"], [29, 31, 28, 29], [29, 32, 28, 30, "constructor"], [29, 43, 28, 41], [29, 44, 28, 42, "name"], [29, 48, 28, 46], [30, 4, 29, 2], [30, 10, 29, 8], [31, 6, 29, 9, "nodeType"], [31, 14, 29, 17], [32, 6, 29, 19, "tagName"], [33, 4, 29, 26], [33, 5, 29, 27], [33, 8, 29, 30, "val"], [33, 11, 29, 33], [34, 4, 30, 2], [34, 10, 30, 8, "isCustomElement"], [34, 25, 30, 23], [34, 28, 31, 5], [34, 35, 31, 12, "tagName"], [34, 42, 31, 19], [34, 47, 31, 24], [34, 55, 31, 32], [34, 59, 31, 36, "tagName"], [34, 66, 31, 43], [34, 67, 31, 44, "includes"], [34, 75, 31, 52], [34, 76, 31, 53], [34, 79, 31, 56], [34, 80, 31, 57], [34, 84, 32, 4, "testHasAttribute"], [34, 100, 32, 20], [34, 101, 32, 21, "val"], [34, 104, 32, 24], [34, 105, 32, 25], [35, 4, 33, 2], [35, 11, 34, 5, "nodeType"], [35, 19, 34, 13], [35, 24, 34, 18, "ELEMENT_NODE"], [35, 36, 34, 30], [35, 41, 35, 7, "ELEMENT_REGEXP"], [35, 55, 35, 21], [35, 56, 35, 22, "test"], [35, 60, 35, 26], [35, 61, 35, 27, "constructorName"], [35, 76, 35, 42], [35, 77, 35, 43], [35, 81, 35, 47, "isCustomElement"], [35, 96, 35, 62], [35, 97, 35, 63], [35, 101, 36, 5, "nodeType"], [35, 109, 36, 13], [35, 114, 36, 18, "TEXT_NODE"], [35, 123, 36, 27], [35, 127, 36, 31, "constructorName"], [35, 142, 36, 46], [35, 147, 36, 51], [35, 153, 36, 58], [35, 157, 37, 5, "nodeType"], [35, 165, 37, 13], [35, 170, 37, 18, "COMMENT_NODE"], [35, 182, 37, 30], [35, 186, 37, 34, "constructorName"], [35, 201, 37, 49], [35, 206, 37, 54], [35, 215, 37, 64], [35, 219, 38, 5, "nodeType"], [35, 227, 38, 13], [35, 232, 38, 18, "FRAGMENT_NODE"], [35, 245, 38, 31], [35, 249, 38, 35, "constructorName"], [35, 264, 38, 50], [35, 269, 38, 55], [35, 287, 38, 74], [36, 2, 40, 0], [36, 3, 40, 1], [37, 2, 41, 0], [37, 8, 41, 6, "test"], [37, 12, 41, 10], [37, 15, 41, 13, "val"], [37, 18, 41, 16], [37, 22, 41, 20, "val"], [37, 25, 41, 23], [37, 27, 41, 25, "constructor"], [37, 38, 41, 36], [37, 40, 41, 38, "name"], [37, 44, 41, 42], [37, 48, 41, 46, "testNode"], [37, 56, 41, 54], [37, 57, 41, 55, "val"], [37, 60, 41, 58], [37, 61, 41, 59], [38, 2, 42, 0, "exports"], [38, 9, 42, 7], [38, 10, 42, 8, "test"], [38, 14, 42, 12], [38, 17, 42, 15, "test"], [38, 21, 42, 19], [39, 2, 43, 0], [39, 11, 43, 9, "nodeIsText"], [39, 21, 43, 19, "nodeIsText"], [39, 22, 43, 20, "node"], [39, 26, 43, 24], [39, 28, 43, 26], [40, 4, 44, 2], [40, 11, 44, 9, "node"], [40, 15, 44, 13], [40, 16, 44, 14, "nodeType"], [40, 24, 44, 22], [40, 29, 44, 27, "TEXT_NODE"], [40, 38, 44, 36], [41, 2, 45, 0], [42, 2, 46, 0], [42, 11, 46, 9, "nodeIsComment"], [42, 24, 46, 22, "nodeIsComment"], [42, 25, 46, 23, "node"], [42, 29, 46, 27], [42, 31, 46, 29], [43, 4, 47, 2], [43, 11, 47, 9, "node"], [43, 15, 47, 13], [43, 16, 47, 14, "nodeType"], [43, 24, 47, 22], [43, 29, 47, 27, "COMMENT_NODE"], [43, 41, 47, 39], [44, 2, 48, 0], [45, 2, 49, 0], [45, 11, 49, 9, "nodeIsFragment"], [45, 25, 49, 23, "nodeIsFragment"], [45, 26, 49, 24, "node"], [45, 30, 49, 28], [45, 32, 49, 30], [46, 4, 50, 2], [46, 11, 50, 9, "node"], [46, 15, 50, 13], [46, 16, 50, 14, "nodeType"], [46, 24, 50, 22], [46, 29, 50, 27, "FRAGMENT_NODE"], [46, 42, 50, 40], [47, 2, 51, 0], [48, 2, 52, 0], [48, 8, 52, 6, "serialize"], [48, 17, 52, 15], [48, 20, 52, 18, "serialize"], [48, 21, 52, 19, "node"], [48, 25, 52, 23], [48, 27, 52, 25, "config"], [48, 33, 52, 31], [48, 35, 52, 33, "indentation"], [48, 46, 52, 44], [48, 48, 52, 46, "depth"], [48, 53, 52, 51], [48, 55, 52, 53, "refs"], [48, 59, 52, 57], [48, 61, 52, 59, "printer"], [48, 68, 52, 66], [48, 73, 52, 71], [49, 4, 53, 2], [49, 8, 53, 6, "nodeIsText"], [49, 18, 53, 16], [49, 19, 53, 17, "node"], [49, 23, 53, 21], [49, 24, 53, 22], [49, 26, 53, 24], [50, 6, 54, 4], [50, 13, 54, 11], [50, 14, 54, 12], [50, 15, 54, 13], [50, 17, 54, 15, "_markup"], [50, 24, 54, 22], [50, 25, 54, 23, "printText"], [50, 34, 54, 32], [50, 36, 54, 34, "node"], [50, 40, 54, 38], [50, 41, 54, 39, "data"], [50, 45, 54, 43], [50, 47, 54, 45, "config"], [50, 53, 54, 51], [50, 54, 54, 52], [51, 4, 55, 2], [52, 4, 56, 2], [52, 8, 56, 6, "nodeIsComment"], [52, 21, 56, 19], [52, 22, 56, 20, "node"], [52, 26, 56, 24], [52, 27, 56, 25], [52, 29, 56, 27], [53, 6, 57, 4], [53, 13, 57, 11], [53, 14, 57, 12], [53, 15, 57, 13], [53, 17, 57, 15, "_markup"], [53, 24, 57, 22], [53, 25, 57, 23, "printComment"], [53, 37, 57, 35], [53, 39, 57, 37, "node"], [53, 43, 57, 41], [53, 44, 57, 42, "data"], [53, 48, 57, 46], [53, 50, 57, 48, "config"], [53, 56, 57, 54], [53, 57, 57, 55], [54, 4, 58, 2], [55, 4, 59, 2], [55, 10, 59, 8, "type"], [55, 14, 59, 12], [55, 17, 59, 15, "nodeIsFragment"], [55, 31, 59, 29], [55, 32, 59, 30, "node"], [55, 36, 59, 34], [55, 37, 59, 35], [55, 40, 60, 6], [55, 58, 60, 24], [55, 61, 61, 6, "node"], [55, 65, 61, 10], [55, 66, 61, 11, "tagName"], [55, 73, 61, 18], [55, 74, 61, 19, "toLowerCase"], [55, 85, 61, 30], [55, 86, 61, 31], [55, 87, 61, 32], [56, 4, 62, 2], [56, 8, 62, 6], [56, 10, 62, 8, "depth"], [56, 15, 62, 13], [56, 18, 62, 16, "config"], [56, 24, 62, 22], [56, 25, 62, 23, "max<PERSON><PERSON><PERSON>"], [56, 33, 62, 31], [56, 35, 62, 33], [57, 6, 63, 4], [57, 13, 63, 11], [57, 14, 63, 12], [57, 15, 63, 13], [57, 17, 63, 15, "_markup"], [57, 24, 63, 22], [57, 25, 63, 23, "printElementAsLeaf"], [57, 43, 63, 41], [57, 45, 63, 43, "type"], [57, 49, 63, 47], [57, 51, 63, 49, "config"], [57, 57, 63, 55], [57, 58, 63, 56], [58, 4, 64, 2], [59, 4, 65, 2], [59, 11, 65, 9], [59, 12, 65, 10], [59, 13, 65, 11], [59, 15, 65, 13, "_markup"], [59, 22, 65, 20], [59, 23, 65, 21, "printElement"], [59, 35, 65, 33], [59, 37, 66, 4, "type"], [59, 41, 66, 8], [59, 43, 67, 4], [59, 44, 67, 5], [59, 45, 67, 6], [59, 47, 67, 8, "_markup"], [59, 54, 67, 15], [59, 55, 67, 16, "printProps"], [59, 65, 67, 26], [59, 67, 68, 6, "nodeIsFragment"], [59, 81, 68, 20], [59, 82, 68, 21, "node"], [59, 86, 68, 25], [59, 87, 68, 26], [59, 90, 69, 10], [59, 92, 69, 12], [59, 95, 70, 10, "Array"], [59, 100, 70, 15], [59, 101, 70, 16, "from"], [59, 105, 70, 20], [59, 106, 70, 21, "node"], [59, 110, 70, 25], [59, 111, 70, 26, "attributes"], [59, 121, 70, 36], [59, 123, 70, 38, "attr"], [59, 127, 70, 42], [59, 131, 70, 46, "attr"], [59, 135, 70, 50], [59, 136, 70, 51, "name"], [59, 140, 70, 55], [59, 141, 70, 56], [59, 142, 70, 57, "sort"], [59, 146, 70, 61], [59, 147, 70, 62], [59, 148, 70, 63], [59, 150, 71, 6, "nodeIsFragment"], [59, 164, 71, 20], [59, 165, 71, 21, "node"], [59, 169, 71, 25], [59, 170, 71, 26], [59, 173, 72, 10], [59, 174, 72, 11], [59, 175, 72, 12], [59, 178, 73, 10, "Array"], [59, 183, 73, 15], [59, 184, 73, 16, "from"], [59, 188, 73, 20], [59, 189, 73, 21, "node"], [59, 193, 73, 25], [59, 194, 73, 26, "attributes"], [59, 204, 73, 36], [59, 205, 73, 37], [59, 206, 73, 38, "reduce"], [59, 212, 73, 44], [59, 213, 73, 45], [59, 214, 73, 46, "props"], [59, 219, 73, 51], [59, 221, 73, 53, "attribute"], [59, 230, 73, 62], [59, 235, 73, 67], [60, 6, 74, 12, "props"], [60, 11, 74, 17], [60, 12, 74, 18, "attribute"], [60, 21, 74, 27], [60, 22, 74, 28, "name"], [60, 26, 74, 32], [60, 27, 74, 33], [60, 30, 74, 36, "attribute"], [60, 39, 74, 45], [60, 40, 74, 46, "value"], [60, 45, 74, 51], [61, 6, 75, 12], [61, 13, 75, 19, "props"], [61, 18, 75, 24], [62, 4, 76, 10], [62, 5, 76, 11], [62, 7, 76, 13], [62, 8, 76, 14], [62, 9, 76, 15], [62, 10, 76, 16], [62, 12, 77, 6, "config"], [62, 18, 77, 12], [62, 20, 78, 6, "indentation"], [62, 31, 78, 17], [62, 34, 78, 20, "config"], [62, 40, 78, 26], [62, 41, 78, 27, "indent"], [62, 47, 78, 33], [62, 49, 79, 6, "depth"], [62, 54, 79, 11], [62, 56, 80, 6, "refs"], [62, 60, 80, 10], [62, 62, 81, 6, "printer"], [62, 69, 82, 4], [62, 70, 82, 5], [62, 72, 83, 4], [62, 73, 83, 5], [62, 74, 83, 6], [62, 76, 83, 8, "_markup"], [62, 83, 83, 15], [62, 84, 83, 16, "printChildren"], [62, 97, 83, 29], [62, 99, 84, 6, "Array"], [62, 104, 84, 11], [62, 105, 84, 12, "prototype"], [62, 114, 84, 21], [62, 115, 84, 22, "slice"], [62, 120, 84, 27], [62, 121, 84, 28, "call"], [62, 125, 84, 32], [62, 126, 84, 33, "node"], [62, 130, 84, 37], [62, 131, 84, 38, "childNodes"], [62, 141, 84, 48], [62, 145, 84, 52, "node"], [62, 149, 84, 56], [62, 150, 84, 57, "children"], [62, 158, 84, 65], [62, 159, 84, 66], [62, 161, 85, 6, "config"], [62, 167, 85, 12], [62, 169, 86, 6, "indentation"], [62, 180, 86, 17], [62, 183, 86, 20, "config"], [62, 189, 86, 26], [62, 190, 86, 27, "indent"], [62, 196, 86, 33], [62, 198, 87, 6, "depth"], [62, 203, 87, 11], [62, 205, 88, 6, "refs"], [62, 209, 88, 10], [62, 211, 89, 6, "printer"], [62, 218, 90, 4], [62, 219, 90, 5], [62, 221, 91, 4, "config"], [62, 227, 91, 10], [62, 229, 92, 4, "indentation"], [62, 240, 93, 2], [62, 241, 93, 3], [63, 2, 94, 0], [63, 3, 94, 1], [64, 2, 95, 0, "exports"], [64, 9, 95, 7], [64, 10, 95, 8, "serialize"], [64, 19, 95, 17], [64, 22, 95, 20, "serialize"], [64, 31, 95, 29], [65, 2, 96, 0], [65, 8, 96, 6, "plugin"], [65, 14, 96, 12], [65, 17, 96, 15], [66, 4, 97, 2, "serialize"], [66, 13, 97, 11], [67, 4, 98, 2, "test"], [68, 2, 99, 0], [68, 3, 99, 1], [69, 2, 100, 0], [69, 6, 100, 4, "_default"], [69, 14, 100, 12], [69, 17, 100, 15, "plugin"], [69, 23, 100, 21], [70, 2, 101, 0, "exports"], [70, 9, 101, 7], [70, 10, 101, 8, "default"], [70, 17, 101, 15], [70, 20, 101, 18, "_default"], [70, 28, 101, 26], [71, 0, 101, 27], [71, 3]], "functionMap": {"names": ["<global>", "testHasAttribute", "testNode", "test", "nodeIsText", "nodeIsComment", "nodeIsFragment", "serialize", "Array.from$argument_1", "Array.from.reduce$argument_0"], "mappings": "AAA;yBCmB;CDM;iBEC;CFa;aGC,8CH;AIE;CJE;AKC;CLE;AMC;CNE;kBOC;sCCkB,iBD;6CEG;WFG;CPkB"}}, "type": "js/module"}]}