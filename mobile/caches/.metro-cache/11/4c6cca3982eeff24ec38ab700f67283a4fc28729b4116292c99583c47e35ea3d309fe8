{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 224}, "end": {"line": 8, "column": 26, "index": 250}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorSection", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 319}, "end": {"line": 11, "column": 66, "index": 385}}], "key": "psfwCNco8+nKb+3u3V4A+OhHW2E=", "exportNames": ["*"]}}, {"name": "../UI/AnsiHighlight", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 443}, "end": {"line": 13, "column": 43, "index": 486}}], "key": "Z3RNzOVU2rvysIqx3k5+nDf3OuY=", "exportNames": ["*"]}}, {"name": "../UI/LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 487}, "end": {"line": 14, "column": 50, "index": 537}}], "key": "RdVHvqRzw9f347khzra6BLeydT4=", "exportNames": ["*"]}}, {"name": "../UI/LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 538}, "end": {"line": 15, "column": 49, "index": 587}}], "key": "uby2yVzDIT8C23ulqt7pFboB7sg=", "exportNames": ["*"]}}, {"name": "../UI/constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 588}, "end": {"line": 16, "column": 44, "index": 632}}], "key": "7udpK47VazWvYeSWCALyzSM7Lm4=", "exportNames": ["*"]}}, {"name": "../formatProjectFilePath", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 633}, "end": {"line": 17, "column": 65, "index": 698}}], "key": "pNaZpMuUC3vOPvp/6arY6d8e3II=", "exportNames": ["*"]}}, {"name": "../modules/openFileInEditor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 699}, "end": {"line": 18, "column": 59, "index": 758}}], "key": "Az2KxAnhz+/qDZzKal7Ha01jO5c=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LogBoxInspectorCodeFrame = LogBoxInspectorCodeFrame;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/ScrollView\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/View\"));\n  var _LogBoxInspectorSection = require(_dependencyMap[6], \"./LogBoxInspectorSection\");\n  var _AnsiHighlight = require(_dependencyMap[7], \"../UI/AnsiHighlight\");\n  var _LogBoxButton = require(_dependencyMap[8], \"../UI/LogBoxButton\");\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[9], \"../UI/LogBoxStyle\"));\n  var _constants = require(_dependencyMap[10], \"../UI/constants\");\n  var _formatProjectFilePath = require(_dependencyMap[11], \"../formatProjectFilePath\");\n  var _openFileInEditor = _interopRequireDefault(require(_dependencyMap[12], \"../modules/openFileInEditor\"));\n  var _jsxRuntime = require(_dependencyMap[13], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/@expo/metro-runtime/src/error-overlay/overlay/LogBoxInspectorCodeFrame.tsx\";\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function LogBoxInspectorCodeFrame({\n    codeFrame\n  }) {\n    if (codeFrame == null) {\n      return null;\n    }\n    function getFileName() {\n      return (0, _formatProjectFilePath.formatProjectFilePath)(\"/home/<USER>/apps/mobile\", codeFrame?.fileName);\n    }\n    function getLocation() {\n      const location = codeFrame?.location;\n      if (location != null) {\n        return ` (${location.row}:${location.column + 1 /* Code frame columns are zero indexed */})`;\n      }\n      return null;\n    }\n    return (0, _jsxRuntime.jsx)(_LogBoxInspectorSection.LogBoxInspectorSection, {\n      heading: \"Source\",\n      children: (0, _jsxRuntime.jsxs)(_View.default, {\n        style: styles.box,\n        children: [(0, _jsxRuntime.jsx)(_View.default, {\n          style: styles.frame,\n          children: (0, _jsxRuntime.jsx)(_ScrollView.default, {\n            horizontal: true,\n            contentContainerStyle: {\n              flexDirection: 'column'\n            },\n            children: (0, _jsxRuntime.jsx)(_AnsiHighlight.Ansi, {\n              style: styles.content,\n              text: codeFrame.content\n            })\n          })\n        }), (0, _jsxRuntime.jsx)(_LogBoxButton.LogBoxButton, {\n          backgroundColor: {\n            default: 'transparent',\n            pressed: LogBoxStyle.getBackgroundDarkColor(1)\n          },\n          style: styles.button,\n          onPress: () => {\n            (0, _openFileInEditor.default)(codeFrame.fileName, codeFrame.location?.row ?? 0);\n          },\n          children: (0, _jsxRuntime.jsxs)(_Text.default, {\n            style: styles.fileText,\n            children: [getFileName(), getLocation()]\n          })\n        })]\n      })\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    box: {\n      backgroundColor: LogBoxStyle.getBackgroundColor(),\n      borderWidth: 1,\n      borderColor: '#323232',\n      marginLeft: 10,\n      marginRight: 10,\n      marginTop: 5,\n      borderRadius: 3\n    },\n    frame: {\n      padding: 10,\n      borderBottomColor: LogBoxStyle.getTextColor(0.1),\n      borderBottomWidth: 1\n    },\n    button: {\n      paddingTop: 10,\n      paddingBottom: 10\n    },\n    content: {\n      flexDirection: 'column',\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 12,\n      includeFontPadding: false,\n      lineHeight: 20,\n      fontFamily: _constants.CODE_FONT\n    },\n    fileText: {\n      userSelect: 'none',\n      color: LogBoxStyle.getTextColor(0.5),\n      textAlign: 'center',\n      flex: 1,\n      fontSize: 16,\n      includeFontPadding: false,\n      fontFamily: _constants.CODE_FONT\n    }\n  });\n});", "lineCount": 115, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_react"], [7, 12, 8, 0], [7, 15, 8, 0, "_interopRequireDefault"], [7, 37, 8, 0], [7, 38, 8, 0, "require"], [7, 45, 8, 0], [7, 46, 8, 0, "_dependencyMap"], [7, 60, 8, 0], [8, 2, 8, 26], [8, 6, 8, 26, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 17, 8, 26], [8, 20, 8, 26, "_interopRequireDefault"], [8, 42, 8, 26], [8, 43, 8, 26, "require"], [8, 50, 8, 26], [8, 51, 8, 26, "_dependencyMap"], [8, 65, 8, 26], [9, 2, 8, 26], [9, 6, 8, 26, "_StyleSheet"], [9, 17, 8, 26], [9, 20, 8, 26, "_interopRequireDefault"], [9, 42, 8, 26], [9, 43, 8, 26, "require"], [9, 50, 8, 26], [9, 51, 8, 26, "_dependencyMap"], [9, 65, 8, 26], [10, 2, 8, 26], [10, 6, 8, 26, "_Text"], [10, 11, 8, 26], [10, 14, 8, 26, "_interopRequireDefault"], [10, 36, 8, 26], [10, 37, 8, 26, "require"], [10, 44, 8, 26], [10, 45, 8, 26, "_dependencyMap"], [10, 59, 8, 26], [11, 2, 8, 26], [11, 6, 8, 26, "_View"], [11, 11, 8, 26], [11, 14, 8, 26, "_interopRequireDefault"], [11, 36, 8, 26], [11, 37, 8, 26, "require"], [11, 44, 8, 26], [11, 45, 8, 26, "_dependencyMap"], [11, 59, 8, 26], [12, 2, 11, 0], [12, 6, 11, 0, "_LogBoxInspectorSection"], [12, 29, 11, 0], [12, 32, 11, 0, "require"], [12, 39, 11, 0], [12, 40, 11, 0, "_dependencyMap"], [12, 54, 11, 0], [13, 2, 13, 0], [13, 6, 13, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [13, 20, 13, 0], [13, 23, 13, 0, "require"], [13, 30, 13, 0], [13, 31, 13, 0, "_dependencyMap"], [13, 45, 13, 0], [14, 2, 14, 0], [14, 6, 14, 0, "_LogBoxButton"], [14, 19, 14, 0], [14, 22, 14, 0, "require"], [14, 29, 14, 0], [14, 30, 14, 0, "_dependencyMap"], [14, 44, 14, 0], [15, 2, 15, 0], [15, 6, 15, 0, "LogBoxStyle"], [15, 17, 15, 0], [15, 20, 15, 0, "_interopRequireWildcard"], [15, 43, 15, 0], [15, 44, 15, 0, "require"], [15, 51, 15, 0], [15, 52, 15, 0, "_dependencyMap"], [15, 66, 15, 0], [16, 2, 16, 0], [16, 6, 16, 0, "_constants"], [16, 16, 16, 0], [16, 19, 16, 0, "require"], [16, 26, 16, 0], [16, 27, 16, 0, "_dependencyMap"], [16, 41, 16, 0], [17, 2, 17, 0], [17, 6, 17, 0, "_formatProjectFilePath"], [17, 28, 17, 0], [17, 31, 17, 0, "require"], [17, 38, 17, 0], [17, 39, 17, 0, "_dependencyMap"], [17, 53, 17, 0], [18, 2, 18, 0], [18, 6, 18, 0, "_openFileInEditor"], [18, 23, 18, 0], [18, 26, 18, 0, "_interopRequireDefault"], [18, 48, 18, 0], [18, 49, 18, 0, "require"], [18, 56, 18, 0], [18, 57, 18, 0, "_dependencyMap"], [18, 71, 18, 0], [19, 2, 18, 59], [19, 6, 18, 59, "_jsxRuntime"], [19, 17, 18, 59], [19, 20, 18, 59, "require"], [19, 27, 18, 59], [19, 28, 18, 59, "_dependencyMap"], [19, 42, 18, 59], [20, 2, 18, 59], [20, 6, 18, 59, "_jsxFileName"], [20, 18, 18, 59], [21, 2, 1, 0], [22, 0, 2, 0], [23, 0, 3, 0], [24, 0, 4, 0], [25, 0, 5, 0], [26, 0, 6, 0], [27, 0, 7, 0], [28, 2, 1, 0], [28, 11, 1, 0, "_interopRequireWildcard"], [28, 35, 1, 0, "e"], [28, 36, 1, 0], [28, 38, 1, 0, "t"], [28, 39, 1, 0], [28, 68, 1, 0, "WeakMap"], [28, 75, 1, 0], [28, 81, 1, 0, "r"], [28, 82, 1, 0], [28, 89, 1, 0, "WeakMap"], [28, 96, 1, 0], [28, 100, 1, 0, "n"], [28, 101, 1, 0], [28, 108, 1, 0, "WeakMap"], [28, 115, 1, 0], [28, 127, 1, 0, "_interopRequireWildcard"], [28, 150, 1, 0], [28, 162, 1, 0, "_interopRequireWildcard"], [28, 163, 1, 0, "e"], [28, 164, 1, 0], [28, 166, 1, 0, "t"], [28, 167, 1, 0], [28, 176, 1, 0, "t"], [28, 177, 1, 0], [28, 181, 1, 0, "e"], [28, 182, 1, 0], [28, 186, 1, 0, "e"], [28, 187, 1, 0], [28, 188, 1, 0, "__esModule"], [28, 198, 1, 0], [28, 207, 1, 0, "e"], [28, 208, 1, 0], [28, 214, 1, 0, "o"], [28, 215, 1, 0], [28, 217, 1, 0, "i"], [28, 218, 1, 0], [28, 220, 1, 0, "f"], [28, 221, 1, 0], [28, 226, 1, 0, "__proto__"], [28, 235, 1, 0], [28, 243, 1, 0, "default"], [28, 250, 1, 0], [28, 252, 1, 0, "e"], [28, 253, 1, 0], [28, 270, 1, 0, "e"], [28, 271, 1, 0], [28, 294, 1, 0, "e"], [28, 295, 1, 0], [28, 320, 1, 0, "e"], [28, 321, 1, 0], [28, 330, 1, 0, "f"], [28, 331, 1, 0], [28, 337, 1, 0, "o"], [28, 338, 1, 0], [28, 341, 1, 0, "t"], [28, 342, 1, 0], [28, 345, 1, 0, "n"], [28, 346, 1, 0], [28, 349, 1, 0, "r"], [28, 350, 1, 0], [28, 358, 1, 0, "o"], [28, 359, 1, 0], [28, 360, 1, 0, "has"], [28, 363, 1, 0], [28, 364, 1, 0, "e"], [28, 365, 1, 0], [28, 375, 1, 0, "o"], [28, 376, 1, 0], [28, 377, 1, 0, "get"], [28, 380, 1, 0], [28, 381, 1, 0, "e"], [28, 382, 1, 0], [28, 385, 1, 0, "o"], [28, 386, 1, 0], [28, 387, 1, 0, "set"], [28, 390, 1, 0], [28, 391, 1, 0, "e"], [28, 392, 1, 0], [28, 394, 1, 0, "f"], [28, 395, 1, 0], [28, 411, 1, 0, "t"], [28, 412, 1, 0], [28, 416, 1, 0, "e"], [28, 417, 1, 0], [28, 433, 1, 0, "t"], [28, 434, 1, 0], [28, 441, 1, 0, "hasOwnProperty"], [28, 455, 1, 0], [28, 456, 1, 0, "call"], [28, 460, 1, 0], [28, 461, 1, 0, "e"], [28, 462, 1, 0], [28, 464, 1, 0, "t"], [28, 465, 1, 0], [28, 472, 1, 0, "i"], [28, 473, 1, 0], [28, 477, 1, 0, "o"], [28, 478, 1, 0], [28, 481, 1, 0, "Object"], [28, 487, 1, 0], [28, 488, 1, 0, "defineProperty"], [28, 502, 1, 0], [28, 507, 1, 0, "Object"], [28, 513, 1, 0], [28, 514, 1, 0, "getOwnPropertyDescriptor"], [28, 538, 1, 0], [28, 539, 1, 0, "e"], [28, 540, 1, 0], [28, 542, 1, 0, "t"], [28, 543, 1, 0], [28, 550, 1, 0, "i"], [28, 551, 1, 0], [28, 552, 1, 0, "get"], [28, 555, 1, 0], [28, 559, 1, 0, "i"], [28, 560, 1, 0], [28, 561, 1, 0, "set"], [28, 564, 1, 0], [28, 568, 1, 0, "o"], [28, 569, 1, 0], [28, 570, 1, 0, "f"], [28, 571, 1, 0], [28, 573, 1, 0, "t"], [28, 574, 1, 0], [28, 576, 1, 0, "i"], [28, 577, 1, 0], [28, 581, 1, 0, "f"], [28, 582, 1, 0], [28, 583, 1, 0, "t"], [28, 584, 1, 0], [28, 588, 1, 0, "e"], [28, 589, 1, 0], [28, 590, 1, 0, "t"], [28, 591, 1, 0], [28, 602, 1, 0, "f"], [28, 603, 1, 0], [28, 608, 1, 0, "e"], [28, 609, 1, 0], [28, 611, 1, 0, "t"], [28, 612, 1, 0], [29, 2, 22, 7], [29, 11, 22, 16, "LogBoxInspectorCodeFrame"], [29, 35, 22, 40, "LogBoxInspectorCodeFrame"], [29, 36, 22, 41], [30, 4, 22, 43, "codeFrame"], [31, 2, 22, 80], [31, 3, 22, 81], [31, 5, 22, 83], [32, 4, 23, 2], [32, 8, 23, 6, "codeFrame"], [32, 17, 23, 15], [32, 21, 23, 19], [32, 25, 23, 23], [32, 27, 23, 25], [33, 6, 24, 4], [33, 13, 24, 11], [33, 17, 24, 15], [34, 4, 25, 2], [35, 4, 27, 2], [35, 13, 27, 11, "getFileName"], [35, 24, 27, 22, "getFileName"], [35, 25, 27, 22], [35, 27, 27, 25], [36, 6, 28, 4], [36, 13, 28, 11], [36, 17, 28, 11, "formatProjectFilePath"], [36, 61, 28, 32], [36, 89, 28, 64, "codeFrame"], [36, 98, 28, 73], [36, 100, 28, 75, "fileName"], [36, 108, 28, 83], [36, 109, 28, 84], [37, 4, 29, 2], [38, 4, 31, 2], [38, 13, 31, 11, "getLocation"], [38, 24, 31, 22, "getLocation"], [38, 25, 31, 22], [38, 27, 31, 25], [39, 6, 32, 4], [39, 12, 32, 10, "location"], [39, 20, 32, 18], [39, 23, 32, 21, "codeFrame"], [39, 32, 32, 30], [39, 34, 32, 32, "location"], [39, 42, 32, 40], [40, 6, 33, 4], [40, 10, 33, 8, "location"], [40, 18, 33, 16], [40, 22, 33, 20], [40, 26, 33, 24], [40, 28, 33, 26], [41, 8, 34, 6], [41, 15, 34, 13], [41, 20, 34, 18, "location"], [41, 28, 34, 26], [41, 29, 34, 27, "row"], [41, 32, 34, 30], [41, 36, 34, 34, "location"], [41, 44, 34, 42], [41, 45, 34, 43, "column"], [41, 51, 34, 49], [41, 54, 34, 52], [41, 55, 34, 53], [41, 56, 34, 54], [41, 100, 34, 98], [42, 6, 35, 4], [43, 6, 37, 4], [43, 13, 37, 11], [43, 17, 37, 15], [44, 4, 38, 2], [45, 4, 40, 2], [45, 11, 41, 4], [45, 15, 41, 4, "_jsxRuntime"], [45, 26, 41, 4], [45, 27, 41, 4, "jsx"], [45, 30, 41, 4], [45, 32, 41, 5, "_LogBoxInspectorSection"], [45, 55, 41, 5], [45, 56, 41, 5, "LogBoxInspectorSection"], [45, 78, 41, 27], [46, 6, 41, 28, "heading"], [46, 13, 41, 35], [46, 15, 41, 36], [46, 23, 41, 44], [47, 6, 41, 44, "children"], [47, 14, 41, 44], [47, 16, 42, 6], [47, 20, 42, 6, "_jsxRuntime"], [47, 31, 42, 6], [47, 32, 42, 6, "jsxs"], [47, 36, 42, 6], [47, 38, 42, 7, "_View"], [47, 43, 42, 7], [47, 44, 42, 7, "default"], [47, 51, 42, 11], [48, 8, 42, 12, "style"], [48, 13, 42, 17], [48, 15, 42, 19, "styles"], [48, 21, 42, 25], [48, 22, 42, 26, "box"], [48, 25, 42, 30], [49, 8, 42, 30, "children"], [49, 16, 42, 30], [49, 19, 43, 8], [49, 23, 43, 8, "_jsxRuntime"], [49, 34, 43, 8], [49, 35, 43, 8, "jsx"], [49, 38, 43, 8], [49, 40, 43, 9, "_View"], [49, 45, 43, 9], [49, 46, 43, 9, "default"], [49, 53, 43, 13], [50, 10, 43, 14, "style"], [50, 15, 43, 19], [50, 17, 43, 21, "styles"], [50, 23, 43, 27], [50, 24, 43, 28, "frame"], [50, 29, 43, 34], [51, 10, 43, 34, "children"], [51, 18, 43, 34], [51, 20, 44, 10], [51, 24, 44, 10, "_jsxRuntime"], [51, 35, 44, 10], [51, 36, 44, 10, "jsx"], [51, 39, 44, 10], [51, 41, 44, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [51, 52, 44, 11], [51, 53, 44, 11, "default"], [51, 60, 44, 21], [52, 12, 45, 12, "horizontal"], [52, 22, 45, 22], [53, 12, 46, 12, "contentContainerStyle"], [53, 33, 46, 33], [53, 35, 46, 35], [54, 14, 47, 14, "flexDirection"], [54, 27, 47, 27], [54, 29, 47, 29], [55, 12, 48, 12], [55, 13, 48, 14], [56, 12, 48, 14, "children"], [56, 20, 48, 14], [56, 22, 49, 12], [56, 26, 49, 12, "_jsxRuntime"], [56, 37, 49, 12], [56, 38, 49, 12, "jsx"], [56, 41, 49, 12], [56, 43, 49, 13, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [56, 57, 49, 13], [56, 58, 49, 13, "<PERSON><PERSON>"], [56, 62, 49, 17], [57, 14, 49, 18, "style"], [57, 19, 49, 23], [57, 21, 49, 25, "styles"], [57, 27, 49, 31], [57, 28, 49, 32, "content"], [57, 35, 49, 40], [58, 14, 49, 41, "text"], [58, 18, 49, 45], [58, 20, 49, 47, "codeFrame"], [58, 29, 49, 56], [58, 30, 49, 57, "content"], [59, 12, 49, 65], [59, 13, 49, 67], [60, 10, 49, 68], [60, 11, 50, 22], [61, 8, 50, 23], [61, 9, 51, 14], [61, 10, 51, 15], [61, 12, 52, 8], [61, 16, 52, 8, "_jsxRuntime"], [61, 27, 52, 8], [61, 28, 52, 8, "jsx"], [61, 31, 52, 8], [61, 33, 52, 9, "_LogBoxButton"], [61, 46, 52, 9], [61, 47, 52, 9, "LogBoxButton"], [61, 59, 52, 21], [62, 10, 53, 10, "backgroundColor"], [62, 25, 53, 25], [62, 27, 53, 27], [63, 12, 54, 12, "default"], [63, 19, 54, 19], [63, 21, 54, 21], [63, 34, 54, 34], [64, 12, 55, 12, "pressed"], [64, 19, 55, 19], [64, 21, 55, 21, "LogBoxStyle"], [64, 32, 55, 32], [64, 33, 55, 33, "getBackgroundDarkColor"], [64, 55, 55, 55], [64, 56, 55, 56], [64, 57, 55, 57], [65, 10, 56, 10], [65, 11, 56, 12], [66, 10, 57, 10, "style"], [66, 15, 57, 15], [66, 17, 57, 17, "styles"], [66, 23, 57, 23], [66, 24, 57, 24, "button"], [66, 30, 57, 31], [67, 10, 58, 10, "onPress"], [67, 17, 58, 17], [67, 19, 58, 19, "onPress"], [67, 20, 58, 19], [67, 25, 58, 25], [68, 12, 59, 12], [68, 16, 59, 12, "openFileInEditor"], [68, 41, 59, 28], [68, 43, 59, 29, "codeFrame"], [68, 52, 59, 38], [68, 53, 59, 39, "fileName"], [68, 61, 59, 47], [68, 63, 59, 49, "codeFrame"], [68, 72, 59, 58], [68, 73, 59, 59, "location"], [68, 81, 59, 67], [68, 83, 59, 69, "row"], [68, 86, 59, 72], [68, 90, 59, 76], [68, 91, 59, 77], [68, 92, 59, 78], [69, 10, 60, 10], [69, 11, 60, 12], [70, 10, 60, 12, "children"], [70, 18, 60, 12], [70, 20, 61, 10], [70, 24, 61, 10, "_jsxRuntime"], [70, 35, 61, 10], [70, 36, 61, 10, "jsxs"], [70, 40, 61, 10], [70, 42, 61, 11, "_Text"], [70, 47, 61, 11], [70, 48, 61, 11, "default"], [70, 55, 61, 15], [71, 12, 61, 16, "style"], [71, 17, 61, 21], [71, 19, 61, 23, "styles"], [71, 25, 61, 29], [71, 26, 61, 30, "fileText"], [71, 34, 61, 39], [72, 12, 61, 39, "children"], [72, 20, 61, 39], [72, 23, 62, 13, "getFileName"], [72, 34, 62, 24], [72, 35, 62, 25], [72, 36, 62, 26], [72, 38, 63, 13, "getLocation"], [72, 49, 63, 24], [72, 50, 63, 25], [72, 51, 63, 26], [73, 10, 63, 26], [73, 11, 64, 16], [74, 8, 64, 17], [74, 9, 65, 22], [74, 10, 65, 23], [75, 6, 65, 23], [75, 7, 66, 12], [76, 4, 66, 13], [76, 5, 67, 28], [76, 6, 67, 29], [77, 2, 69, 0], [78, 2, 71, 0], [78, 8, 71, 6, "styles"], [78, 14, 71, 12], [78, 17, 71, 15, "StyleSheet"], [78, 36, 71, 25], [78, 37, 71, 26, "create"], [78, 43, 71, 32], [78, 44, 71, 33], [79, 4, 72, 2, "box"], [79, 7, 72, 5], [79, 9, 72, 7], [80, 6, 73, 4, "backgroundColor"], [80, 21, 73, 19], [80, 23, 73, 21, "LogBoxStyle"], [80, 34, 73, 32], [80, 35, 73, 33, "getBackgroundColor"], [80, 53, 73, 51], [80, 54, 73, 52], [80, 55, 73, 53], [81, 6, 74, 4, "borderWidth"], [81, 17, 74, 15], [81, 19, 74, 17], [81, 20, 74, 18], [82, 6, 75, 4, "borderColor"], [82, 17, 75, 15], [82, 19, 75, 17], [82, 28, 75, 26], [83, 6, 76, 4, "marginLeft"], [83, 16, 76, 14], [83, 18, 76, 16], [83, 20, 76, 18], [84, 6, 77, 4, "marginRight"], [84, 17, 77, 15], [84, 19, 77, 17], [84, 21, 77, 19], [85, 6, 78, 4, "marginTop"], [85, 15, 78, 13], [85, 17, 78, 15], [85, 18, 78, 16], [86, 6, 79, 4, "borderRadius"], [86, 18, 79, 16], [86, 20, 79, 18], [87, 4, 80, 2], [87, 5, 80, 3], [88, 4, 81, 2, "frame"], [88, 9, 81, 7], [88, 11, 81, 9], [89, 6, 82, 4, "padding"], [89, 13, 82, 11], [89, 15, 82, 13], [89, 17, 82, 15], [90, 6, 83, 4, "borderBottomColor"], [90, 23, 83, 21], [90, 25, 83, 23, "LogBoxStyle"], [90, 36, 83, 34], [90, 37, 83, 35, "getTextColor"], [90, 49, 83, 47], [90, 50, 83, 48], [90, 53, 83, 51], [90, 54, 83, 52], [91, 6, 84, 4, "borderBottomWidth"], [91, 23, 84, 21], [91, 25, 84, 23], [92, 4, 85, 2], [92, 5, 85, 3], [93, 4, 86, 2, "button"], [93, 10, 86, 8], [93, 12, 86, 10], [94, 6, 87, 4, "paddingTop"], [94, 16, 87, 14], [94, 18, 87, 16], [94, 20, 87, 18], [95, 6, 88, 4, "paddingBottom"], [95, 19, 88, 17], [95, 21, 88, 19], [96, 4, 89, 2], [96, 5, 89, 3], [97, 4, 90, 2, "content"], [97, 11, 90, 9], [97, 13, 90, 11], [98, 6, 91, 4, "flexDirection"], [98, 19, 91, 17], [98, 21, 91, 19], [98, 29, 91, 27], [99, 6, 92, 4, "color"], [99, 11, 92, 9], [99, 13, 92, 11, "LogBoxStyle"], [99, 24, 92, 22], [99, 25, 92, 23, "getTextColor"], [99, 37, 92, 35], [99, 38, 92, 36], [99, 39, 92, 37], [99, 40, 92, 38], [100, 6, 93, 4, "fontSize"], [100, 14, 93, 12], [100, 16, 93, 14], [100, 18, 93, 16], [101, 6, 94, 4, "includeFontPadding"], [101, 24, 94, 22], [101, 26, 94, 24], [101, 31, 94, 29], [102, 6, 95, 4, "lineHeight"], [102, 16, 95, 14], [102, 18, 95, 16], [102, 20, 95, 18], [103, 6, 96, 4, "fontFamily"], [103, 16, 96, 14], [103, 18, 96, 16, "CODE_FONT"], [104, 4, 97, 2], [104, 5, 97, 3], [105, 4, 98, 2, "fileText"], [105, 12, 98, 10], [105, 14, 98, 12], [106, 6, 99, 4, "userSelect"], [106, 16, 99, 14], [106, 18, 99, 16], [106, 24, 99, 22], [107, 6, 100, 4, "color"], [107, 11, 100, 9], [107, 13, 100, 11, "LogBoxStyle"], [107, 24, 100, 22], [107, 25, 100, 23, "getTextColor"], [107, 37, 100, 35], [107, 38, 100, 36], [107, 41, 100, 39], [107, 42, 100, 40], [108, 6, 101, 4, "textAlign"], [108, 15, 101, 13], [108, 17, 101, 15], [108, 25, 101, 23], [109, 6, 102, 4, "flex"], [109, 10, 102, 8], [109, 12, 102, 10], [109, 13, 102, 11], [110, 6, 103, 4, "fontSize"], [110, 14, 103, 12], [110, 16, 103, 14], [110, 18, 103, 16], [111, 6, 104, 4, "includeFontPadding"], [111, 24, 104, 22], [111, 26, 104, 24], [111, 31, 104, 29], [112, 6, 105, 4, "fontFamily"], [112, 16, 105, 14], [112, 18, 105, 16, "CODE_FONT"], [113, 4, 106, 2], [114, 2, 107, 0], [114, 3, 107, 1], [114, 4, 107, 2], [115, 0, 107, 3], [115, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorCodeFrame", "getFileName", "getLocation", "LogBoxButton.props.onPress"], "mappings": "AAA;OCqB;ECK;GDE;EEE;GFO;mBGoB;WHE;CDS"}}, "type": "js/module"}]}