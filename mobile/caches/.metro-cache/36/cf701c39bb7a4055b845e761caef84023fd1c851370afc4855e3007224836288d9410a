{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 38, "column": 29, "index": 1571}, "end": {"line": 38, "column": 45, "index": 1587}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "../Route", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 16, "index": 1606}, "end": {"line": 39, "column": 35, "index": 1625}}], "key": "ic98XhoR1v7tz4h3RiVql/NxHng=", "exportNames": ["*"]}}, {"name": "../useScreens", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 40, "column": 21, "index": 1648}, "end": {"line": 40, "column": 45, "index": 1672}}], "key": "8gimF/GgYNRJ+ojtiVDaShLJVrk=", "exportNames": ["*"]}}, {"name": "../views/Protected", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 41, "column": 20, "index": 1694}, "end": {"line": 41, "column": 49, "index": 1723}}], "key": "k1+uDYZ/MvJqE4WVPvI1cbQswMs=", "exportNames": ["*"]}}, {"name": "../views/Screen", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 42, "column": 17, "index": 1742}, "end": {"line": 42, "column": 43, "index": 1768}}], "key": "CvVDy33sFAANwe0yWc+ZvurKuCc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _objectWithoutProperties = require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\");\n  var _excluded = [\"children\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/layouts/withLayoutContext.js\";\n  var __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = {\n        enumerable: true,\n        get: function () {\n          return m[k];\n        }\n      };\n    }\n    Object.defineProperty(o, k2, desc);\n  } : function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n  });\n  var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n    Object.defineProperty(o, \"default\", {\n      enumerable: true,\n      value: v\n    });\n  } : function (o, v) {\n    o[\"default\"] = v;\n  });\n  var __importStar = this && this.__importStar || function () {\n    var ownKeys = function (o) {\n      ownKeys = Object.getOwnPropertyNames || function (o) {\n        var ar = [];\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n        return ar;\n      };\n      return ownKeys(o);\n    };\n    return function (mod) {\n      if (mod && mod.__esModule) return mod;\n      var result = {};\n      if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n      __setModuleDefault(result, mod);\n      return result;\n    };\n  }();\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFilterScreenChildren = useFilterScreenChildren;\n  exports.withLayoutContext = withLayoutContext;\n  var react_1 = __importStar(require(_dependencyMap[2], \"react\"));\n  var Route_1 = require(_dependencyMap[3], \"../Route\");\n  var useScreens_1 = require(_dependencyMap[4], \"../useScreens\");\n  var Protected_1 = require(_dependencyMap[5], \"../views/Protected\");\n  var Screen_1 = require(_dependencyMap[6], \"../views/Screen\");\n  function useFilterScreenChildren(children) {\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      isCustomNavigator = _ref.isCustomNavigator,\n      contextKey = _ref.contextKey;\n    return (0, react_1.useMemo)(() => {\n      var customChildren = [];\n      var screens = [];\n      var protectedScreens = new Set();\n      function flattenChild(child) {\n        var exclude = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n        if ((0, Screen_1.isScreen)(child, contextKey)) {\n          if (exclude) {\n            protectedScreens.add(child.props.name);\n          } else {\n            screens.push(child.props);\n          }\n          return;\n        }\n        if ((0, Protected_1.isProtectedReactElement)(child)) {\n          if (child.props.guard) {\n            react_1.Children.forEach(child.props.children, protectedChild => flattenChild(protectedChild));\n          } else {\n            react_1.Children.forEach(child.props.children, protectedChild => {\n              flattenChild(protectedChild, true);\n            });\n          }\n          return;\n        }\n        if (isCustomNavigator) {\n          customChildren.push(child);\n          return null;\n        }\n        console.warn(`Layout children must be of type Screen, all other children are ignored. To use custom children, create a custom <Layout />. Update Layout Route at: \"app${contextKey}/_layout\"`);\n        return null;\n      }\n      react_1.Children.forEach(children, child => flattenChild(child));\n      // Add an assertion for development\n      if (process.env.NODE_ENV !== 'production') {\n        // Assert if names are not unique\n        var names = screens?.map(screen => screen && typeof screen === 'object' && 'name' in screen && screen.name);\n        if (names && new Set(names).size !== names.length) {\n          throw new Error('Screen names must be unique: ' + names);\n        }\n      }\n      return {\n        screens,\n        children: customChildren,\n        protectedScreens\n      };\n    }, [children]);\n  }\n  /**\n   * Returns a navigator that automatically injects matched routes and renders nothing when there are no children.\n   * Return type with `children` prop optional.\n   *\n   * Enables use of other built-in React Navigation navigators and other navigators built with the React Navigation custom navigator API.\n   *\n   *  @example\n   * ```tsx app/_layout.tsx\n   * import { ParamListBase, TabNavigationState } from \"@react-navigation/native\";\n   * import {\n   *   createMaterialTopTabNavigator,\n   *   MaterialTopTabNavigationOptions,\n   *   MaterialTopTabNavigationEventMap,\n   * } from \"@react-navigation/material-top-tabs\";\n   * import { withLayoutContext } from \"expo-router\";\n   *\n   * const MaterialTopTabs = createMaterialTopTabNavigator();\n   *\n   * const ExpoRouterMaterialTopTabs = withLayoutContext<\n   *   MaterialTopTabNavigationOptions,\n   *   typeof MaterialTopTabs.Navigator,\n   *   TabNavigationState<ParamListBase>,\n   *   MaterialTopTabNavigationEventMap\n   * >(MaterialTopTabs.Navigator);\n  \n   * export default function TabLayout() {\n   *   return <ExpoRouterMaterialTopTabs />;\n   * }\n   * ```\n   */\n  function withLayoutContext(Nav, processor) {\n    return Object.assign((0, react_1.forwardRef)((_ref2, ref) => {\n      var userDefinedChildren = _ref2.children,\n        props = _objectWithoutProperties(_ref2, _excluded);\n      var contextKey = (0, Route_1.useContextKey)();\n      var _useFilterScreenChild = useFilterScreenChildren(userDefinedChildren, {\n          contextKey\n        }),\n        screens = _useFilterScreenChild.screens,\n        protectedScreens = _useFilterScreenChild.protectedScreens;\n      var processed = processor ? processor(screens ?? []) : screens;\n      var sorted = (0, useScreens_1.useSortedScreens)(processed ?? [], protectedScreens);\n      // Prevent throwing an error when there are no screens.\n      if (!sorted.length) {\n        return null;\n      }\n      return _reactNativeCssInteropJsxRuntime.jsx(Nav, {\n        ...props,\n        id: contextKey,\n        ref: ref,\n        children: sorted\n      });\n    }), {\n      Screen: Screen_1.Screen,\n      Protected: Protected_1.Protected\n    });\n  }\n});", "lineCount": 167, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_reactNativeCssInteropJsxRuntime"], [4, 38, 1, 13], [4, 41, 1, 13, "require"], [4, 48, 1, 13], [4, 49, 1, 13, "_dependencyMap"], [4, 63, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_objectWithoutProperties"], [5, 30, 1, 13], [5, 33, 1, 13, "require"], [5, 40, 1, 13], [5, 41, 1, 13, "_dependencyMap"], [5, 55, 1, 13], [6, 2, 1, 13], [6, 6, 1, 13, "_excluded"], [6, 15, 1, 13], [7, 2, 1, 13], [7, 6, 1, 13, "_jsxFileName"], [7, 18, 1, 13], [8, 2, 2, 0], [8, 6, 2, 4, "__createBinding"], [8, 21, 2, 19], [8, 24, 2, 23], [8, 28, 2, 27], [8, 32, 2, 31], [8, 36, 2, 35], [8, 37, 2, 36, "__createBinding"], [8, 52, 2, 51], [8, 57, 2, 57, "Object"], [8, 63, 2, 63], [8, 64, 2, 64, "create"], [8, 70, 2, 70], [8, 73, 2, 74], [8, 83, 2, 83, "o"], [8, 84, 2, 84], [8, 86, 2, 86, "m"], [8, 87, 2, 87], [8, 89, 2, 89, "k"], [8, 90, 2, 90], [8, 92, 2, 92, "k2"], [8, 94, 2, 94], [8, 96, 2, 96], [9, 4, 3, 4], [9, 8, 3, 8, "k2"], [9, 10, 3, 10], [9, 15, 3, 15, "undefined"], [9, 24, 3, 24], [9, 26, 3, 26, "k2"], [9, 28, 3, 28], [9, 31, 3, 31, "k"], [9, 32, 3, 32], [10, 4, 4, 4], [10, 8, 4, 8, "desc"], [10, 12, 4, 12], [10, 15, 4, 15, "Object"], [10, 21, 4, 21], [10, 22, 4, 22, "getOwnPropertyDescriptor"], [10, 46, 4, 46], [10, 47, 4, 47, "m"], [10, 48, 4, 48], [10, 50, 4, 50, "k"], [10, 51, 4, 51], [10, 52, 4, 52], [11, 4, 5, 4], [11, 8, 5, 8], [11, 9, 5, 9, "desc"], [11, 13, 5, 13], [11, 18, 5, 18], [11, 23, 5, 23], [11, 27, 5, 27, "desc"], [11, 31, 5, 31], [11, 34, 5, 34], [11, 35, 5, 35, "m"], [11, 36, 5, 36], [11, 37, 5, 37, "__esModule"], [11, 47, 5, 47], [11, 50, 5, 50, "desc"], [11, 54, 5, 54], [11, 55, 5, 55, "writable"], [11, 63, 5, 63], [11, 67, 5, 67, "desc"], [11, 71, 5, 71], [11, 72, 5, 72, "configurable"], [11, 84, 5, 84], [11, 85, 5, 85], [11, 87, 5, 87], [12, 6, 6, 6, "desc"], [12, 10, 6, 10], [12, 13, 6, 13], [13, 8, 6, 15, "enumerable"], [13, 18, 6, 25], [13, 20, 6, 27], [13, 24, 6, 31], [14, 8, 6, 33, "get"], [14, 11, 6, 36], [14, 13, 6, 38], [14, 22, 6, 38, "get"], [14, 23, 6, 38], [14, 25, 6, 49], [15, 10, 6, 51], [15, 17, 6, 58, "m"], [15, 18, 6, 59], [15, 19, 6, 60, "k"], [15, 20, 6, 61], [15, 21, 6, 62], [16, 8, 6, 64], [17, 6, 6, 66], [17, 7, 6, 67], [18, 4, 7, 4], [19, 4, 8, 4, "Object"], [19, 10, 8, 10], [19, 11, 8, 11, "defineProperty"], [19, 25, 8, 25], [19, 26, 8, 26, "o"], [19, 27, 8, 27], [19, 29, 8, 29, "k2"], [19, 31, 8, 31], [19, 33, 8, 33, "desc"], [19, 37, 8, 37], [19, 38, 8, 38], [20, 2, 9, 0], [20, 3, 9, 1], [20, 6, 9, 6], [20, 16, 9, 15, "o"], [20, 17, 9, 16], [20, 19, 9, 18, "m"], [20, 20, 9, 19], [20, 22, 9, 21, "k"], [20, 23, 9, 22], [20, 25, 9, 24, "k2"], [20, 27, 9, 26], [20, 29, 9, 28], [21, 4, 10, 4], [21, 8, 10, 8, "k2"], [21, 10, 10, 10], [21, 15, 10, 15, "undefined"], [21, 24, 10, 24], [21, 26, 10, 26, "k2"], [21, 28, 10, 28], [21, 31, 10, 31, "k"], [21, 32, 10, 32], [22, 4, 11, 4, "o"], [22, 5, 11, 5], [22, 6, 11, 6, "k2"], [22, 8, 11, 8], [22, 9, 11, 9], [22, 12, 11, 12, "m"], [22, 13, 11, 13], [22, 14, 11, 14, "k"], [22, 15, 11, 15], [22, 16, 11, 16], [23, 2, 12, 0], [23, 3, 12, 2], [23, 4, 12, 3], [24, 2, 13, 0], [24, 6, 13, 4, "__setModuleDefault"], [24, 24, 13, 22], [24, 27, 13, 26], [24, 31, 13, 30], [24, 35, 13, 34], [24, 39, 13, 38], [24, 40, 13, 39, "__setModuleDefault"], [24, 58, 13, 57], [24, 63, 13, 63, "Object"], [24, 69, 13, 69], [24, 70, 13, 70, "create"], [24, 76, 13, 76], [24, 79, 13, 80], [24, 89, 13, 89, "o"], [24, 90, 13, 90], [24, 92, 13, 92, "v"], [24, 93, 13, 93], [24, 95, 13, 95], [25, 4, 14, 4, "Object"], [25, 10, 14, 10], [25, 11, 14, 11, "defineProperty"], [25, 25, 14, 25], [25, 26, 14, 26, "o"], [25, 27, 14, 27], [25, 29, 14, 29], [25, 38, 14, 38], [25, 40, 14, 40], [26, 6, 14, 42, "enumerable"], [26, 16, 14, 52], [26, 18, 14, 54], [26, 22, 14, 58], [27, 6, 14, 60, "value"], [27, 11, 14, 65], [27, 13, 14, 67, "v"], [28, 4, 14, 69], [28, 5, 14, 70], [28, 6, 14, 71], [29, 2, 15, 0], [29, 3, 15, 1], [29, 6, 15, 5], [29, 16, 15, 14, "o"], [29, 17, 15, 15], [29, 19, 15, 17, "v"], [29, 20, 15, 18], [29, 22, 15, 20], [30, 4, 16, 4, "o"], [30, 5, 16, 5], [30, 6, 16, 6], [30, 15, 16, 15], [30, 16, 16, 16], [30, 19, 16, 19, "v"], [30, 20, 16, 20], [31, 2, 17, 0], [31, 3, 17, 1], [31, 4, 17, 2], [32, 2, 18, 0], [32, 6, 18, 4, "__importStar"], [32, 18, 18, 16], [32, 21, 18, 20], [32, 25, 18, 24], [32, 29, 18, 28], [32, 33, 18, 32], [32, 34, 18, 33, "__importStar"], [32, 46, 18, 45], [32, 50, 18, 51], [32, 62, 18, 63], [33, 4, 19, 4], [33, 8, 19, 8, "ownKeys"], [33, 15, 19, 15], [33, 18, 19, 18], [33, 27, 19, 18, "ownKeys"], [33, 28, 19, 27, "o"], [33, 29, 19, 28], [33, 31, 19, 30], [34, 6, 20, 8, "ownKeys"], [34, 13, 20, 15], [34, 16, 20, 18, "Object"], [34, 22, 20, 24], [34, 23, 20, 25, "getOwnPropertyNames"], [34, 42, 20, 44], [34, 46, 20, 48], [34, 56, 20, 58, "o"], [34, 57, 20, 59], [34, 59, 20, 61], [35, 8, 21, 12], [35, 12, 21, 16, "ar"], [35, 14, 21, 18], [35, 17, 21, 21], [35, 19, 21, 23], [36, 8, 22, 12], [36, 13, 22, 17], [36, 17, 22, 21, "k"], [36, 18, 22, 22], [36, 22, 22, 26, "o"], [36, 23, 22, 27], [36, 25, 22, 29], [36, 29, 22, 33, "Object"], [36, 35, 22, 39], [36, 36, 22, 40, "prototype"], [36, 45, 22, 49], [36, 46, 22, 50, "hasOwnProperty"], [36, 60, 22, 64], [36, 61, 22, 65, "call"], [36, 65, 22, 69], [36, 66, 22, 70, "o"], [36, 67, 22, 71], [36, 69, 22, 73, "k"], [36, 70, 22, 74], [36, 71, 22, 75], [36, 73, 22, 77, "ar"], [36, 75, 22, 79], [36, 76, 22, 80, "ar"], [36, 78, 22, 82], [36, 79, 22, 83, "length"], [36, 85, 22, 89], [36, 86, 22, 90], [36, 89, 22, 93, "k"], [36, 90, 22, 94], [37, 8, 23, 12], [37, 15, 23, 19, "ar"], [37, 17, 23, 21], [38, 6, 24, 8], [38, 7, 24, 9], [39, 6, 25, 8], [39, 13, 25, 15, "ownKeys"], [39, 20, 25, 22], [39, 21, 25, 23, "o"], [39, 22, 25, 24], [39, 23, 25, 25], [40, 4, 26, 4], [40, 5, 26, 5], [41, 4, 27, 4], [41, 11, 27, 11], [41, 21, 27, 21, "mod"], [41, 24, 27, 24], [41, 26, 27, 26], [42, 6, 28, 8], [42, 10, 28, 12, "mod"], [42, 13, 28, 15], [42, 17, 28, 19, "mod"], [42, 20, 28, 22], [42, 21, 28, 23, "__esModule"], [42, 31, 28, 33], [42, 33, 28, 35], [42, 40, 28, 42, "mod"], [42, 43, 28, 45], [43, 6, 29, 8], [43, 10, 29, 12, "result"], [43, 16, 29, 18], [43, 19, 29, 21], [43, 20, 29, 22], [43, 21, 29, 23], [44, 6, 30, 8], [44, 10, 30, 12, "mod"], [44, 13, 30, 15], [44, 17, 30, 19], [44, 21, 30, 23], [44, 23, 30, 25], [44, 28, 30, 30], [44, 32, 30, 34, "k"], [44, 33, 30, 35], [44, 36, 30, 38, "ownKeys"], [44, 43, 30, 45], [44, 44, 30, 46, "mod"], [44, 47, 30, 49], [44, 48, 30, 50], [44, 50, 30, 52, "i"], [44, 51, 30, 53], [44, 54, 30, 56], [44, 55, 30, 57], [44, 57, 30, 59, "i"], [44, 58, 30, 60], [44, 61, 30, 63, "k"], [44, 62, 30, 64], [44, 63, 30, 65, "length"], [44, 69, 30, 71], [44, 71, 30, 73, "i"], [44, 72, 30, 74], [44, 74, 30, 76], [44, 76, 30, 78], [44, 80, 30, 82, "k"], [44, 81, 30, 83], [44, 82, 30, 84, "i"], [44, 83, 30, 85], [44, 84, 30, 86], [44, 89, 30, 91], [44, 98, 30, 100], [44, 100, 30, 102, "__createBinding"], [44, 115, 30, 117], [44, 116, 30, 118, "result"], [44, 122, 30, 124], [44, 124, 30, 126, "mod"], [44, 127, 30, 129], [44, 129, 30, 131, "k"], [44, 130, 30, 132], [44, 131, 30, 133, "i"], [44, 132, 30, 134], [44, 133, 30, 135], [44, 134, 30, 136], [45, 6, 31, 8, "__setModuleDefault"], [45, 24, 31, 26], [45, 25, 31, 27, "result"], [45, 31, 31, 33], [45, 33, 31, 35, "mod"], [45, 36, 31, 38], [45, 37, 31, 39], [46, 6, 32, 8], [46, 13, 32, 15, "result"], [46, 19, 32, 21], [47, 4, 33, 4], [47, 5, 33, 5], [48, 2, 34, 0], [48, 3, 34, 1], [48, 4, 34, 3], [48, 5, 34, 4], [49, 2, 35, 0, "Object"], [49, 8, 35, 6], [49, 9, 35, 7, "defineProperty"], [49, 23, 35, 21], [49, 24, 35, 22, "exports"], [49, 31, 35, 29], [49, 33, 35, 31], [49, 45, 35, 43], [49, 47, 35, 45], [50, 4, 35, 47, "value"], [50, 9, 35, 52], [50, 11, 35, 54], [51, 2, 35, 59], [51, 3, 35, 60], [51, 4, 35, 61], [52, 2, 36, 0, "exports"], [52, 9, 36, 7], [52, 10, 36, 8, "useFilterScreenChildren"], [52, 33, 36, 31], [52, 36, 36, 34, "useFilterScreenChildren"], [52, 59, 36, 57], [53, 2, 37, 0, "exports"], [53, 9, 37, 7], [53, 10, 37, 8, "withLayoutContext"], [53, 27, 37, 25], [53, 30, 37, 28, "withLayoutContext"], [53, 47, 37, 45], [54, 2, 38, 0], [54, 6, 38, 6, "react_1"], [54, 13, 38, 13], [54, 16, 38, 16, "__importStar"], [54, 28, 38, 28], [54, 29, 38, 29, "require"], [54, 36, 38, 36], [54, 37, 38, 36, "_dependencyMap"], [54, 51, 38, 36], [54, 63, 38, 44], [54, 64, 38, 45], [54, 65, 38, 46], [55, 2, 39, 0], [55, 6, 39, 6, "Route_1"], [55, 13, 39, 13], [55, 16, 39, 16, "require"], [55, 23, 39, 23], [55, 24, 39, 23, "_dependencyMap"], [55, 38, 39, 23], [55, 53, 39, 34], [55, 54, 39, 35], [56, 2, 40, 0], [56, 6, 40, 6, "useScreens_1"], [56, 18, 40, 18], [56, 21, 40, 21, "require"], [56, 28, 40, 28], [56, 29, 40, 28, "_dependencyMap"], [56, 43, 40, 28], [56, 63, 40, 44], [56, 64, 40, 45], [57, 2, 41, 0], [57, 6, 41, 6, "Protected_1"], [57, 17, 41, 17], [57, 20, 41, 20, "require"], [57, 27, 41, 27], [57, 28, 41, 27, "_dependencyMap"], [57, 42, 41, 27], [57, 67, 41, 48], [57, 68, 41, 49], [58, 2, 42, 0], [58, 6, 42, 6, "Screen_1"], [58, 14, 42, 14], [58, 17, 42, 17, "require"], [58, 24, 42, 24], [58, 25, 42, 24, "_dependencyMap"], [58, 39, 42, 24], [58, 61, 42, 42], [58, 62, 42, 43], [59, 2, 43, 0], [59, 11, 43, 9, "useFilterScreenChildren"], [59, 34, 43, 32, "useFilterScreenChildren"], [59, 35, 43, 33, "children"], [59, 43, 43, 41], [59, 45, 43, 84], [60, 4, 43, 84], [60, 8, 43, 84, "_ref"], [60, 12, 43, 84], [60, 15, 43, 84, "arguments"], [60, 24, 43, 84], [60, 25, 43, 84, "length"], [60, 31, 43, 84], [60, 39, 43, 84, "arguments"], [60, 48, 43, 84], [60, 56, 43, 84, "undefined"], [60, 65, 43, 84], [60, 68, 43, 84, "arguments"], [60, 77, 43, 84], [60, 83, 43, 80], [60, 84, 43, 81], [60, 85, 43, 82], [61, 6, 43, 45, "isCustomNavigator"], [61, 23, 43, 62], [61, 26, 43, 62, "_ref"], [61, 30, 43, 62], [61, 31, 43, 45, "isCustomNavigator"], [61, 48, 43, 62], [62, 6, 43, 64, "<PERSON><PERSON>ey"], [62, 16, 43, 74], [62, 19, 43, 74, "_ref"], [62, 23, 43, 74], [62, 24, 43, 64, "<PERSON><PERSON>ey"], [62, 34, 43, 74], [63, 4, 44, 4], [63, 11, 44, 11], [63, 12, 44, 12], [63, 13, 44, 13], [63, 15, 44, 15, "react_1"], [63, 22, 44, 22], [63, 23, 44, 23, "useMemo"], [63, 30, 44, 30], [63, 32, 44, 32], [63, 38, 44, 38], [64, 6, 45, 8], [64, 10, 45, 14, "customChildren"], [64, 24, 45, 28], [64, 27, 45, 31], [64, 29, 45, 33], [65, 6, 46, 8], [65, 10, 46, 14, "screens"], [65, 17, 46, 21], [65, 20, 46, 24], [65, 22, 46, 26], [66, 6, 47, 8], [66, 10, 47, 14, "protectedScreens"], [66, 26, 47, 30], [66, 29, 47, 33], [66, 33, 47, 37, "Set"], [66, 36, 47, 40], [66, 37, 47, 41], [66, 38, 47, 42], [67, 6, 48, 8], [67, 15, 48, 17, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [67, 27, 48, 29, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [67, 28, 48, 30, "child"], [67, 33, 48, 35], [67, 35, 48, 54], [68, 8, 48, 54], [68, 12, 48, 37, "exclude"], [68, 19, 48, 44], [68, 22, 48, 44, "arguments"], [68, 31, 48, 44], [68, 32, 48, 44, "length"], [68, 38, 48, 44], [68, 46, 48, 44, "arguments"], [68, 55, 48, 44], [68, 63, 48, 44, "undefined"], [68, 72, 48, 44], [68, 75, 48, 44, "arguments"], [68, 84, 48, 44], [68, 90, 48, 47], [68, 95, 48, 52], [69, 8, 49, 12], [69, 12, 49, 16], [69, 13, 49, 17], [69, 14, 49, 18], [69, 16, 49, 20, "Screen_1"], [69, 24, 49, 28], [69, 25, 49, 29, "isScreen"], [69, 33, 49, 37], [69, 35, 49, 39, "child"], [69, 40, 49, 44], [69, 42, 49, 46, "<PERSON><PERSON>ey"], [69, 52, 49, 56], [69, 53, 49, 57], [69, 55, 49, 59], [70, 10, 50, 16], [70, 14, 50, 20, "exclude"], [70, 21, 50, 27], [70, 23, 50, 29], [71, 12, 51, 20, "protectedScreens"], [71, 28, 51, 36], [71, 29, 51, 37, "add"], [71, 32, 51, 40], [71, 33, 51, 41, "child"], [71, 38, 51, 46], [71, 39, 51, 47, "props"], [71, 44, 51, 52], [71, 45, 51, 53, "name"], [71, 49, 51, 57], [71, 50, 51, 58], [72, 10, 52, 16], [72, 11, 52, 17], [72, 17, 53, 21], [73, 12, 54, 20, "screens"], [73, 19, 54, 27], [73, 20, 54, 28, "push"], [73, 24, 54, 32], [73, 25, 54, 33, "child"], [73, 30, 54, 38], [73, 31, 54, 39, "props"], [73, 36, 54, 44], [73, 37, 54, 45], [74, 10, 55, 16], [75, 10, 56, 16], [76, 8, 57, 12], [77, 8, 58, 12], [77, 12, 58, 16], [77, 13, 58, 17], [77, 14, 58, 18], [77, 16, 58, 20, "Protected_1"], [77, 27, 58, 31], [77, 28, 58, 32, "isProtectedReactElement"], [77, 51, 58, 55], [77, 53, 58, 57, "child"], [77, 58, 58, 62], [77, 59, 58, 63], [77, 61, 58, 65], [78, 10, 59, 16], [78, 14, 59, 20, "child"], [78, 19, 59, 25], [78, 20, 59, 26, "props"], [78, 25, 59, 31], [78, 26, 59, 32, "guard"], [78, 31, 59, 37], [78, 33, 59, 39], [79, 12, 60, 20, "react_1"], [79, 19, 60, 27], [79, 20, 60, 28, "Children"], [79, 28, 60, 36], [79, 29, 60, 37, "for<PERSON>ach"], [79, 36, 60, 44], [79, 37, 60, 45, "child"], [79, 42, 60, 50], [79, 43, 60, 51, "props"], [79, 48, 60, 56], [79, 49, 60, 57, "children"], [79, 57, 60, 65], [79, 59, 60, 68, "<PERSON><PERSON><PERSON><PERSON>"], [79, 73, 60, 82], [79, 77, 60, 87, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [79, 89, 60, 99], [79, 90, 60, 100, "<PERSON><PERSON><PERSON><PERSON>"], [79, 104, 60, 114], [79, 105, 60, 115], [79, 106, 60, 116], [80, 10, 61, 16], [80, 11, 61, 17], [80, 17, 62, 21], [81, 12, 63, 20, "react_1"], [81, 19, 63, 27], [81, 20, 63, 28, "Children"], [81, 28, 63, 36], [81, 29, 63, 37, "for<PERSON>ach"], [81, 36, 63, 44], [81, 37, 63, 45, "child"], [81, 42, 63, 50], [81, 43, 63, 51, "props"], [81, 48, 63, 56], [81, 49, 63, 57, "children"], [81, 57, 63, 65], [81, 59, 63, 68, "<PERSON><PERSON><PERSON><PERSON>"], [81, 73, 63, 82], [81, 77, 63, 87], [82, 14, 64, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [82, 26, 64, 36], [82, 27, 64, 37, "<PERSON><PERSON><PERSON><PERSON>"], [82, 41, 64, 51], [82, 43, 64, 53], [82, 47, 64, 57], [82, 48, 64, 58], [83, 12, 65, 20], [83, 13, 65, 21], [83, 14, 65, 22], [84, 10, 66, 16], [85, 10, 67, 16], [86, 8, 68, 12], [87, 8, 69, 12], [87, 12, 69, 16, "isCustomNavigator"], [87, 29, 69, 33], [87, 31, 69, 35], [88, 10, 70, 16, "customChildren"], [88, 24, 70, 30], [88, 25, 70, 31, "push"], [88, 29, 70, 35], [88, 30, 70, 36, "child"], [88, 35, 70, 41], [88, 36, 70, 42], [89, 10, 71, 16], [89, 17, 71, 23], [89, 21, 71, 27], [90, 8, 72, 12], [91, 8, 73, 12, "console"], [91, 15, 73, 19], [91, 16, 73, 20, "warn"], [91, 20, 73, 24], [91, 21, 73, 25], [91, 176, 73, 180, "<PERSON><PERSON>ey"], [91, 186, 73, 190], [91, 197, 73, 201], [91, 198, 73, 202], [92, 8, 74, 12], [92, 15, 74, 19], [92, 19, 74, 23], [93, 6, 75, 8], [94, 6, 76, 8, "react_1"], [94, 13, 76, 15], [94, 14, 76, 16, "Children"], [94, 22, 76, 24], [94, 23, 76, 25, "for<PERSON>ach"], [94, 30, 76, 32], [94, 31, 76, 33, "children"], [94, 39, 76, 41], [94, 41, 76, 44, "child"], [94, 46, 76, 49], [94, 50, 76, 54, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [94, 62, 76, 66], [94, 63, 76, 67, "child"], [94, 68, 76, 72], [94, 69, 76, 73], [94, 70, 76, 74], [95, 6, 77, 8], [96, 6, 78, 8], [96, 10, 78, 12, "process"], [96, 17, 78, 19], [96, 18, 78, 20, "env"], [96, 21, 78, 23], [96, 22, 78, 24, "NODE_ENV"], [96, 30, 78, 32], [96, 35, 78, 37], [96, 47, 78, 49], [96, 49, 78, 51], [97, 8, 79, 12], [98, 8, 80, 12], [98, 12, 80, 18, "names"], [98, 17, 80, 23], [98, 20, 80, 26, "screens"], [98, 27, 80, 33], [98, 29, 80, 35, "map"], [98, 32, 80, 38], [98, 33, 80, 40, "screen"], [98, 39, 80, 46], [98, 43, 80, 51, "screen"], [98, 49, 80, 57], [98, 53, 80, 61], [98, 60, 80, 68, "screen"], [98, 66, 80, 74], [98, 71, 80, 79], [98, 79, 80, 87], [98, 83, 80, 91], [98, 89, 80, 97], [98, 93, 80, 101, "screen"], [98, 99, 80, 107], [98, 103, 80, 111, "screen"], [98, 109, 80, 117], [98, 110, 80, 118, "name"], [98, 114, 80, 122], [98, 115, 80, 123], [99, 8, 81, 12], [99, 12, 81, 16, "names"], [99, 17, 81, 21], [99, 21, 81, 25], [99, 25, 81, 29, "Set"], [99, 28, 81, 32], [99, 29, 81, 33, "names"], [99, 34, 81, 38], [99, 35, 81, 39], [99, 36, 81, 40, "size"], [99, 40, 81, 44], [99, 45, 81, 49, "names"], [99, 50, 81, 54], [99, 51, 81, 55, "length"], [99, 57, 81, 61], [99, 59, 81, 63], [100, 10, 82, 16], [100, 16, 82, 22], [100, 20, 82, 26, "Error"], [100, 25, 82, 31], [100, 26, 82, 32], [100, 57, 82, 63], [100, 60, 82, 66, "names"], [100, 65, 82, 71], [100, 66, 82, 72], [101, 8, 83, 12], [102, 6, 84, 8], [103, 6, 85, 8], [103, 13, 85, 15], [104, 8, 86, 12, "screens"], [104, 15, 86, 19], [105, 8, 87, 12, "children"], [105, 16, 87, 20], [105, 18, 87, 22, "customChildren"], [105, 32, 87, 36], [106, 8, 88, 12, "protectedScreens"], [107, 6, 89, 8], [107, 7, 89, 9], [108, 4, 90, 4], [108, 5, 90, 5], [108, 7, 90, 7], [108, 8, 90, 8, "children"], [108, 16, 90, 16], [108, 17, 90, 17], [108, 18, 90, 18], [109, 2, 91, 0], [110, 2, 92, 0], [111, 0, 93, 0], [112, 0, 94, 0], [113, 0, 95, 0], [114, 0, 96, 0], [115, 0, 97, 0], [116, 0, 98, 0], [117, 0, 99, 0], [118, 0, 100, 0], [119, 0, 101, 0], [120, 0, 102, 0], [121, 0, 103, 0], [122, 0, 104, 0], [123, 0, 105, 0], [124, 0, 106, 0], [125, 0, 107, 0], [126, 0, 108, 0], [127, 0, 109, 0], [128, 0, 110, 0], [129, 0, 111, 0], [130, 0, 112, 0], [131, 0, 113, 0], [132, 0, 114, 0], [133, 0, 115, 0], [134, 0, 116, 0], [135, 0, 117, 0], [136, 0, 118, 0], [137, 0, 119, 0], [138, 0, 120, 0], [139, 0, 121, 0], [140, 2, 122, 0], [140, 11, 122, 9, "withLayoutContext"], [140, 28, 122, 26, "withLayoutContext"], [140, 29, 122, 27, "Nav"], [140, 32, 122, 30], [140, 34, 122, 32, "processor"], [140, 43, 122, 41], [140, 45, 122, 43], [141, 4, 123, 4], [141, 11, 123, 11, "Object"], [141, 17, 123, 17], [141, 18, 123, 18, "assign"], [141, 24, 123, 24], [141, 25, 123, 25], [141, 26, 123, 26], [141, 27, 123, 27], [141, 29, 123, 29, "react_1"], [141, 36, 123, 36], [141, 37, 123, 37, "forwardRef"], [141, 47, 123, 47], [141, 49, 123, 49], [141, 50, 123, 49, "_ref2"], [141, 55, 123, 49], [141, 57, 123, 95, "ref"], [141, 60, 123, 98], [141, 65, 123, 103], [142, 6, 123, 103], [142, 10, 123, 62, "userDefinedChildren"], [142, 29, 123, 81], [142, 32, 123, 81, "_ref2"], [142, 37, 123, 81], [142, 38, 123, 52, "children"], [142, 46, 123, 60], [143, 8, 123, 86, "props"], [143, 13, 123, 91], [143, 16, 123, 91, "_objectWithoutProperties"], [143, 40, 123, 91], [143, 41, 123, 91, "_ref2"], [143, 46, 123, 91], [143, 48, 123, 91, "_excluded"], [143, 57, 123, 91], [144, 6, 124, 8], [144, 10, 124, 14, "<PERSON><PERSON>ey"], [144, 20, 124, 24], [144, 23, 124, 27], [144, 24, 124, 28], [144, 25, 124, 29], [144, 27, 124, 31, "Route_1"], [144, 34, 124, 38], [144, 35, 124, 39, "useContextKey"], [144, 48, 124, 52], [144, 50, 124, 54], [144, 51, 124, 55], [145, 6, 125, 8], [145, 10, 125, 8, "_useFilterScreenChild"], [145, 31, 125, 8], [145, 34, 125, 46, "useFilterScreenChildren"], [145, 57, 125, 69], [145, 58, 125, 70, "userDefinedChildren"], [145, 77, 125, 89], [145, 79, 125, 91], [146, 10, 126, 12, "<PERSON><PERSON>ey"], [147, 8, 127, 8], [147, 9, 127, 9], [147, 10, 127, 10], [148, 8, 125, 16, "screens"], [148, 15, 125, 23], [148, 18, 125, 23, "_useFilterScreenChild"], [148, 39, 125, 23], [148, 40, 125, 16, "screens"], [148, 47, 125, 23], [149, 8, 125, 25, "protectedScreens"], [149, 24, 125, 41], [149, 27, 125, 41, "_useFilterScreenChild"], [149, 48, 125, 41], [149, 49, 125, 25, "protectedScreens"], [149, 65, 125, 41], [150, 6, 128, 8], [150, 10, 128, 14, "processed"], [150, 19, 128, 23], [150, 22, 128, 26, "processor"], [150, 31, 128, 35], [150, 34, 128, 38, "processor"], [150, 43, 128, 47], [150, 44, 128, 48, "screens"], [150, 51, 128, 55], [150, 55, 128, 59], [150, 57, 128, 61], [150, 58, 128, 62], [150, 61, 128, 65, "screens"], [150, 68, 128, 72], [151, 6, 129, 8], [151, 10, 129, 14, "sorted"], [151, 16, 129, 20], [151, 19, 129, 23], [151, 20, 129, 24], [151, 21, 129, 25], [151, 23, 129, 27, "useScreens_1"], [151, 35, 129, 39], [151, 36, 129, 40, "useSortedScreens"], [151, 52, 129, 56], [151, 54, 129, 58, "processed"], [151, 63, 129, 67], [151, 67, 129, 71], [151, 69, 129, 73], [151, 71, 129, 75, "protectedScreens"], [151, 87, 129, 91], [151, 88, 129, 92], [152, 6, 130, 8], [153, 6, 131, 8], [153, 10, 131, 12], [153, 11, 131, 13, "sorted"], [153, 17, 131, 19], [153, 18, 131, 20, "length"], [153, 24, 131, 26], [153, 26, 131, 28], [154, 8, 132, 12], [154, 15, 132, 19], [154, 19, 132, 23], [155, 6, 133, 8], [156, 6, 134, 8], [156, 13, 134, 15, "_reactNativeCssInteropJsxRuntime"], [156, 45, 134, 15], [156, 46, 134, 15, "jsx"], [156, 49, 134, 15], [156, 50, 134, 16, "Nav"], [156, 53, 134, 19], [157, 8, 134, 19], [157, 11, 134, 24, "props"], [157, 16, 134, 29], [158, 8, 134, 31, "id"], [158, 10, 134, 33], [158, 12, 134, 35, "<PERSON><PERSON>ey"], [158, 22, 134, 46], [159, 8, 134, 47, "ref"], [159, 11, 134, 50], [159, 13, 134, 52, "ref"], [159, 16, 134, 56], [160, 8, 134, 57, "children"], [160, 16, 134, 65], [160, 18, 134, 67, "sorted"], [161, 6, 134, 74], [161, 7, 134, 75], [161, 8, 134, 76], [162, 4, 135, 4], [162, 5, 135, 5], [162, 6, 135, 6], [162, 8, 135, 8], [163, 6, 136, 8, "Screen"], [163, 12, 136, 14], [163, 14, 136, 16, "Screen_1"], [163, 22, 136, 24], [163, 23, 136, 25, "Screen"], [163, 29, 136, 31], [164, 6, 137, 8, "Protected"], [164, 15, 137, 17], [164, 17, 137, 19, "Protected_1"], [164, 28, 137, 30], [164, 29, 137, 31, "Protected"], [165, 4, 138, 4], [165, 5, 138, 5], [165, 6, 138, 6], [166, 2, 139, 0], [167, 0, 139, 1], [167, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "desc.get", "ownKeys", "useFilterScreenChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "react_1.Children.forEach$argument_1", "screens.map$argument_0", "withLayoutContext"], "mappings": "AAA;0ECC;sCCI,2BD;CDG,KC;CDG;gFCC;CDE,IC;CDE;mDCC;kBEC;gDFC;SEI;KFE;CDQ;AIS;gCHC;QII;mECY,gDD;mECG;qBDE;SJU;2CKC,8BL;uCMI,mFN;KGU;CJC;AQ+B;iDPC;KOY;CRI"}}, "type": "js/module"}]}