{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../modules/symbolicateStackTrace", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 296}, "end": {"line": 11, "column": 69, "index": 365}}], "key": "YdkqoDMIPdxOnCsE0E8rzpWj034=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.deleteStack = deleteStack;\n  exports.symbolicate = symbolicate;\n  var _symbolicateStackTrace = _interopRequireDefault(require(_dependencyMap[1], \"../modules/symbolicateStackTrace\"));\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  var cache = new Map();\n\n  /**\n   * Sanitize because sometimes, `symbolicateStackTrace` gives us invalid values.\n   */\n  var sanitize = _ref => {\n    var maybeStack = _ref.stack,\n      codeFrame = _ref.codeFrame;\n    if (!Array.isArray(maybeStack)) {\n      throw new Error('Expected stack to be an array.');\n    }\n    var stack = [];\n    for (var maybeFrame of maybeStack) {\n      var collapse = false;\n      if ('collapse' in maybeFrame) {\n        if (typeof maybeFrame.collapse !== 'boolean') {\n          throw new Error('Expected stack frame `collapse` to be a boolean.');\n        }\n        collapse = maybeFrame.collapse;\n      }\n      stack.push({\n        arguments: [],\n        column: maybeFrame.column,\n        file: maybeFrame.file,\n        lineNumber: maybeFrame.lineNumber,\n        methodName: maybeFrame.methodName,\n        collapse\n      });\n    }\n    return {\n      stack,\n      codeFrame\n    };\n  };\n  function deleteStack(stack) {\n    cache.delete(stack);\n  }\n  function symbolicate(stack) {\n    var promise = cache.get(stack);\n    if (promise == null) {\n      promise = (0, _symbolicateStackTrace.default)(stack).then(sanitize);\n      cache.set(stack, promise);\n    }\n    return promise;\n  }\n});", "lineCount": 62, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_symbolicateStackTrace"], [8, 28, 11, 0], [8, 31, 11, 0, "_interopRequireDefault"], [8, 53, 11, 0], [8, 54, 11, 0, "require"], [8, 61, 11, 0], [8, 62, 11, 0, "_dependencyMap"], [8, 76, 11, 0], [9, 2, 1, 0], [10, 0, 2, 0], [11, 0, 3, 0], [12, 0, 4, 0], [13, 0, 5, 0], [14, 0, 6, 0], [15, 0, 7, 0], [17, 2, 19, 0], [17, 6, 19, 6, "cache"], [17, 11, 19, 56], [17, 14, 19, 59], [17, 18, 19, 63, "Map"], [17, 21, 19, 66], [17, 22, 19, 67], [17, 23, 19, 68], [19, 2, 21, 0], [20, 0, 22, 0], [21, 0, 23, 0], [22, 2, 24, 0], [22, 6, 24, 6, "sanitize"], [22, 14, 24, 14], [22, 17, 24, 17, "_ref"], [22, 21, 24, 17], [22, 25, 27, 54], [23, 4, 27, 54], [23, 8, 25, 9, "maybeStack"], [23, 18, 25, 19], [23, 21, 25, 19, "_ref"], [23, 25, 25, 19], [23, 26, 25, 2, "stack"], [23, 31, 25, 7], [24, 6, 26, 2, "codeFrame"], [24, 15, 26, 11], [24, 18, 26, 11, "_ref"], [24, 22, 26, 11], [24, 23, 26, 2, "codeFrame"], [24, 32, 26, 11], [25, 4, 28, 2], [25, 8, 28, 6], [25, 9, 28, 7, "Array"], [25, 14, 28, 12], [25, 15, 28, 13, "isArray"], [25, 22, 28, 20], [25, 23, 28, 21, "maybeStack"], [25, 33, 28, 31], [25, 34, 28, 32], [25, 36, 28, 34], [26, 6, 29, 4], [26, 12, 29, 10], [26, 16, 29, 14, "Error"], [26, 21, 29, 19], [26, 22, 29, 20], [26, 54, 29, 52], [26, 55, 29, 53], [27, 4, 30, 2], [28, 4, 31, 2], [28, 8, 31, 8, "stack"], [28, 13, 31, 27], [28, 16, 31, 30], [28, 18, 31, 32], [29, 4, 32, 2], [29, 9, 32, 7], [29, 13, 32, 13, "<PERSON><PERSON><PERSON><PERSON>"], [29, 23, 32, 23], [29, 27, 32, 27, "maybeStack"], [29, 37, 32, 37], [29, 39, 32, 39], [30, 6, 33, 4], [30, 10, 33, 8, "collapse"], [30, 18, 33, 16], [30, 21, 33, 19], [30, 26, 33, 24], [31, 6, 34, 4], [31, 10, 34, 8], [31, 20, 34, 18], [31, 24, 34, 22, "<PERSON><PERSON><PERSON><PERSON>"], [31, 34, 34, 32], [31, 36, 34, 34], [32, 8, 35, 6], [32, 12, 35, 10], [32, 19, 35, 17, "<PERSON><PERSON><PERSON><PERSON>"], [32, 29, 35, 27], [32, 30, 35, 28, "collapse"], [32, 38, 35, 36], [32, 43, 35, 41], [32, 52, 35, 50], [32, 54, 35, 52], [33, 10, 36, 8], [33, 16, 36, 14], [33, 20, 36, 18, "Error"], [33, 25, 36, 23], [33, 26, 36, 24], [33, 76, 36, 74], [33, 77, 36, 75], [34, 8, 37, 6], [35, 8, 38, 6, "collapse"], [35, 16, 38, 14], [35, 19, 38, 17, "<PERSON><PERSON><PERSON><PERSON>"], [35, 29, 38, 27], [35, 30, 38, 28, "collapse"], [35, 38, 38, 36], [36, 6, 39, 4], [37, 6, 40, 4, "stack"], [37, 11, 40, 9], [37, 12, 40, 10, "push"], [37, 16, 40, 14], [37, 17, 40, 15], [38, 8, 41, 6, "arguments"], [38, 17, 41, 15], [38, 19, 41, 17], [38, 21, 41, 19], [39, 8, 42, 6, "column"], [39, 14, 42, 12], [39, 16, 42, 14, "<PERSON><PERSON><PERSON><PERSON>"], [39, 26, 42, 24], [39, 27, 42, 25, "column"], [39, 33, 42, 31], [40, 8, 43, 6, "file"], [40, 12, 43, 10], [40, 14, 43, 12, "<PERSON><PERSON><PERSON><PERSON>"], [40, 24, 43, 22], [40, 25, 43, 23, "file"], [40, 29, 43, 27], [41, 8, 44, 6, "lineNumber"], [41, 18, 44, 16], [41, 20, 44, 18, "<PERSON><PERSON><PERSON><PERSON>"], [41, 30, 44, 28], [41, 31, 44, 29, "lineNumber"], [41, 41, 44, 39], [42, 8, 45, 6, "methodName"], [42, 18, 45, 16], [42, 20, 45, 18, "<PERSON><PERSON><PERSON><PERSON>"], [42, 30, 45, 28], [42, 31, 45, 29, "methodName"], [42, 41, 45, 39], [43, 8, 46, 6, "collapse"], [44, 6, 47, 4], [44, 7, 47, 5], [44, 8, 47, 6], [45, 4, 48, 2], [46, 4, 49, 2], [46, 11, 49, 9], [47, 6, 49, 11, "stack"], [47, 11, 49, 16], [48, 6, 49, 18, "codeFrame"], [49, 4, 49, 28], [49, 5, 49, 29], [50, 2, 50, 0], [50, 3, 50, 1], [51, 2, 52, 7], [51, 11, 52, 16, "deleteStack"], [51, 22, 52, 27, "deleteStack"], [51, 23, 52, 28, "stack"], [51, 28, 52, 40], [51, 30, 52, 48], [52, 4, 53, 2, "cache"], [52, 9, 53, 7], [52, 10, 53, 8, "delete"], [52, 16, 53, 14], [52, 17, 53, 15, "stack"], [52, 22, 53, 20], [52, 23, 53, 21], [53, 2, 54, 0], [54, 2, 56, 7], [54, 11, 56, 16, "symbolicate"], [54, 22, 56, 27, "symbolicate"], [54, 23, 56, 28, "stack"], [54, 28, 56, 40], [54, 30, 56, 75], [55, 4, 57, 2], [55, 8, 57, 6, "promise"], [55, 15, 57, 13], [55, 18, 57, 16, "cache"], [55, 23, 57, 21], [55, 24, 57, 22, "get"], [55, 27, 57, 25], [55, 28, 57, 26, "stack"], [55, 33, 57, 31], [55, 34, 57, 32], [56, 4, 58, 2], [56, 8, 58, 6, "promise"], [56, 15, 58, 13], [56, 19, 58, 17], [56, 23, 58, 21], [56, 25, 58, 23], [57, 6, 59, 4, "promise"], [57, 13, 59, 11], [57, 16, 59, 14], [57, 20, 59, 14, "symbolicateStackTrace"], [57, 50, 59, 35], [57, 52, 59, 36, "stack"], [57, 57, 59, 41], [57, 58, 59, 42], [57, 59, 59, 43, "then"], [57, 63, 59, 47], [57, 64, 59, 48, "sanitize"], [57, 72, 59, 56], [57, 73, 59, 57], [58, 6, 60, 4, "cache"], [58, 11, 60, 9], [58, 12, 60, 10, "set"], [58, 15, 60, 13], [58, 16, 60, 14, "stack"], [58, 21, 60, 19], [58, 23, 60, 21, "promise"], [58, 30, 60, 28], [58, 31, 60, 29], [59, 4, 61, 2], [60, 4, 63, 2], [60, 11, 63, 9, "promise"], [60, 18, 63, 16], [61, 2, 64, 0], [62, 0, 64, 1], [62, 3]], "functionMap": {"names": ["<global>", "sanitize", "deleteStack", "symbolicate"], "mappings": "AAA;iBCuB;CD0B;OEE;CFE;OGE;CHQ"}}, "type": "js/module"}]}