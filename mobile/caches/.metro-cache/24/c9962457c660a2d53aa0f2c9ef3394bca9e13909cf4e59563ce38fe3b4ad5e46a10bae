{"dependencies": [{"name": "./matchers", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 19, "index": 281}, "end": {"line": 6, "column": 40, "index": 302}}], "key": "89ylKT57ef0l7ma8+p1HhPaMj94=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.parseRouteSegments = parseRouteSegments;\n  exports.getReactNavigationScreensConfig = getReactNavigationScreensConfig;\n  exports.getReactNavigationConfig = getReactNavigationConfig;\n  var matchers_1 = require(_dependencyMap[0], \"./matchers\");\n  // `[page]` -> `:page`\n  // `page` -> `page`\n  function convertDynamicRouteToReactNavigation(segment) {\n    // NOTE(EvanBacon): To support shared routes we preserve group segments.\n    if (segment === 'index') {\n      return '';\n    }\n    if (segment === '+not-found') {\n      return '*not-found';\n    }\n    var dynamicName = (0, matchers_1.matchDynamicName)(segment);\n    if (dynamicName && !dynamicName.deep) {\n      return `:${dynamicName.name}`;\n    } else if (dynamicName?.deep) {\n      return '*' + dynamicName.name;\n    } else {\n      return segment;\n    }\n  }\n  function parseRouteSegments(segments) {\n    return (\n      // NOTE(EvanBacon): When there are nested routes without layouts\n      // the node.route will be something like `app/home/<USER>\n      // this needs to be split to ensure each segment is parsed correctly.\n      segments.split('/')\n      // Convert each segment to a React Navigation format.\n      .map(convertDynamicRouteToReactNavigation)\n      // Remove any empty paths from groups or index routes.\n      .filter(Boolean)\n      // Join to return as a path.\n      .join('/')\n    );\n  }\n  function convertRouteNodeToScreen(node, metaOnly) {\n    var path = parseRouteSegments(node.route);\n    if (!node.children.length) {\n      if (!metaOnly) {\n        return {\n          path,\n          screens: {},\n          _route: node\n        };\n      }\n      return path;\n    }\n    var screens = getReactNavigationScreensConfig(node.children, metaOnly);\n    var screen = {\n      path,\n      screens\n    };\n    if (node.initialRouteName) {\n      // NOTE(EvanBacon): This is bad because it forces all Layout Routes\n      // to be loaded into memory. We should move towards a system where\n      // the initial route name is either loaded asynchronously in the Layout Route\n      // or defined via a file system convention.\n      screen.initialRouteName = node.initialRouteName;\n    }\n    if (!metaOnly) {\n      screen._route = node;\n    }\n    return screen;\n  }\n  function getReactNavigationScreensConfig(nodes, metaOnly) {\n    return Object.fromEntries(nodes.map(node => [node.route, convertRouteNodeToScreen(node, metaOnly)]));\n  }\n  function getReactNavigationConfig(routes, metaOnly) {\n    var config = {\n      initialRouteName: undefined,\n      screens: getReactNavigationScreensConfig(routes.children, metaOnly)\n    };\n    if (routes.initialRouteName) {\n      // We're using LinkingOptions the generic type is `object` instead of a proper ParamList.\n      // So we need to cast the initialRouteName to `any` to avoid type errors.\n      config.initialRouteName = routes.initialRouteName;\n    }\n    return config;\n  }\n});", "lineCount": 88, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "parseRouteSegments"], [7, 28, 3, 26], [7, 31, 3, 29, "parseRouteSegments"], [7, 49, 3, 47], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "getReactNavigationScreensConfig"], [8, 41, 4, 39], [8, 44, 4, 42, "getReactNavigationScreensConfig"], [8, 75, 4, 73], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "getReactNavigationConfig"], [9, 34, 5, 32], [9, 37, 5, 35, "getReactNavigationConfig"], [9, 61, 5, 59], [10, 2, 6, 0], [10, 6, 6, 6, "matchers_1"], [10, 16, 6, 16], [10, 19, 6, 19, "require"], [10, 26, 6, 26], [10, 27, 6, 26, "_dependencyMap"], [10, 41, 6, 26], [10, 58, 6, 39], [10, 59, 6, 40], [11, 2, 7, 0], [12, 2, 8, 0], [13, 2, 9, 0], [13, 11, 9, 9, "convertDynamicRouteToReactNavigation"], [13, 47, 9, 45, "convertDynamicRouteToReactNavigation"], [13, 48, 9, 46, "segment"], [13, 55, 9, 53], [13, 57, 9, 55], [14, 4, 10, 4], [15, 4, 11, 4], [15, 8, 11, 8, "segment"], [15, 15, 11, 15], [15, 20, 11, 20], [15, 27, 11, 27], [15, 29, 11, 29], [16, 6, 12, 8], [16, 13, 12, 15], [16, 15, 12, 17], [17, 4, 13, 4], [18, 4, 14, 4], [18, 8, 14, 8, "segment"], [18, 15, 14, 15], [18, 20, 14, 20], [18, 32, 14, 32], [18, 34, 14, 34], [19, 6, 15, 8], [19, 13, 15, 15], [19, 25, 15, 27], [20, 4, 16, 4], [21, 4, 17, 4], [21, 8, 17, 10, "dynamicName"], [21, 19, 17, 21], [21, 22, 17, 24], [21, 23, 17, 25], [21, 24, 17, 26], [21, 26, 17, 28, "matchers_1"], [21, 36, 17, 38], [21, 37, 17, 39, "matchDynamicName"], [21, 53, 17, 55], [21, 55, 17, 57, "segment"], [21, 62, 17, 64], [21, 63, 17, 65], [22, 4, 18, 4], [22, 8, 18, 8, "dynamicName"], [22, 19, 18, 19], [22, 23, 18, 23], [22, 24, 18, 24, "dynamicName"], [22, 35, 18, 35], [22, 36, 18, 36, "deep"], [22, 40, 18, 40], [22, 42, 18, 42], [23, 6, 19, 8], [23, 13, 19, 15], [23, 17, 19, 19, "dynamicName"], [23, 28, 19, 30], [23, 29, 19, 31, "name"], [23, 33, 19, 35], [23, 35, 19, 37], [24, 4, 20, 4], [24, 5, 20, 5], [24, 11, 21, 9], [24, 15, 21, 13, "dynamicName"], [24, 26, 21, 24], [24, 28, 21, 26, "deep"], [24, 32, 21, 30], [24, 34, 21, 32], [25, 6, 22, 8], [25, 13, 22, 15], [25, 16, 22, 18], [25, 19, 22, 21, "dynamicName"], [25, 30, 22, 32], [25, 31, 22, 33, "name"], [25, 35, 22, 37], [26, 4, 23, 4], [26, 5, 23, 5], [26, 11, 24, 9], [27, 6, 25, 8], [27, 13, 25, 15, "segment"], [27, 20, 25, 22], [28, 4, 26, 4], [29, 2, 27, 0], [30, 2, 28, 0], [30, 11, 28, 9, "parseRouteSegments"], [30, 29, 28, 27, "parseRouteSegments"], [30, 30, 28, 28, "segments"], [30, 38, 28, 36], [30, 40, 28, 38], [31, 4, 29, 4], [32, 6, 30, 4], [33, 6, 31, 4], [34, 6, 32, 4], [35, 6, 33, 4, "segments"], [35, 14, 33, 12], [35, 15, 34, 9, "split"], [35, 20, 34, 14], [35, 21, 34, 15], [35, 24, 34, 18], [36, 6, 35, 8], [37, 6, 35, 8], [37, 7, 36, 9, "map"], [37, 10, 36, 12], [37, 11, 36, 13, "convertDynamicRouteToReactNavigation"], [37, 47, 36, 49], [38, 6, 37, 8], [39, 6, 37, 8], [39, 7, 38, 9, "filter"], [39, 13, 38, 15], [39, 14, 38, 16, "Boolean"], [39, 21, 38, 23], [40, 6, 39, 8], [41, 6, 39, 8], [41, 7, 40, 9, "join"], [41, 11, 40, 13], [41, 12, 40, 14], [41, 15, 40, 17], [42, 4, 40, 18], [43, 2, 41, 0], [44, 2, 42, 0], [44, 11, 42, 9, "convertRouteNodeToScreen"], [44, 35, 42, 33, "convertRouteNodeToScreen"], [44, 36, 42, 34, "node"], [44, 40, 42, 38], [44, 42, 42, 40, "metaOnly"], [44, 50, 42, 48], [44, 52, 42, 50], [45, 4, 43, 4], [45, 8, 43, 10, "path"], [45, 12, 43, 14], [45, 15, 43, 17, "parseRouteSegments"], [45, 33, 43, 35], [45, 34, 43, 36, "node"], [45, 38, 43, 40], [45, 39, 43, 41, "route"], [45, 44, 43, 46], [45, 45, 43, 47], [46, 4, 44, 4], [46, 8, 44, 8], [46, 9, 44, 9, "node"], [46, 13, 44, 13], [46, 14, 44, 14, "children"], [46, 22, 44, 22], [46, 23, 44, 23, "length"], [46, 29, 44, 29], [46, 31, 44, 31], [47, 6, 45, 8], [47, 10, 45, 12], [47, 11, 45, 13, "metaOnly"], [47, 19, 45, 21], [47, 21, 45, 23], [48, 8, 46, 12], [48, 15, 46, 19], [49, 10, 47, 16, "path"], [49, 14, 47, 20], [50, 10, 48, 16, "screens"], [50, 17, 48, 23], [50, 19, 48, 25], [50, 20, 48, 26], [50, 21, 48, 27], [51, 10, 49, 16, "_route"], [51, 16, 49, 22], [51, 18, 49, 24, "node"], [52, 8, 50, 12], [52, 9, 50, 13], [53, 6, 51, 8], [54, 6, 52, 8], [54, 13, 52, 15, "path"], [54, 17, 52, 19], [55, 4, 53, 4], [56, 4, 54, 4], [56, 8, 54, 10, "screens"], [56, 15, 54, 17], [56, 18, 54, 20, "getReactNavigationScreensConfig"], [56, 49, 54, 51], [56, 50, 54, 52, "node"], [56, 54, 54, 56], [56, 55, 54, 57, "children"], [56, 63, 54, 65], [56, 65, 54, 67, "metaOnly"], [56, 73, 54, 75], [56, 74, 54, 76], [57, 4, 55, 4], [57, 8, 55, 10, "screen"], [57, 14, 55, 16], [57, 17, 55, 19], [58, 6, 56, 8, "path"], [58, 10, 56, 12], [59, 6, 57, 8, "screens"], [60, 4, 58, 4], [60, 5, 58, 5], [61, 4, 59, 4], [61, 8, 59, 8, "node"], [61, 12, 59, 12], [61, 13, 59, 13, "initialRouteName"], [61, 29, 59, 29], [61, 31, 59, 31], [62, 6, 60, 8], [63, 6, 61, 8], [64, 6, 62, 8], [65, 6, 63, 8], [66, 6, 64, 8, "screen"], [66, 12, 64, 14], [66, 13, 64, 15, "initialRouteName"], [66, 29, 64, 31], [66, 32, 64, 34, "node"], [66, 36, 64, 38], [66, 37, 64, 39, "initialRouteName"], [66, 53, 64, 55], [67, 4, 65, 4], [68, 4, 66, 4], [68, 8, 66, 8], [68, 9, 66, 9, "metaOnly"], [68, 17, 66, 17], [68, 19, 66, 19], [69, 6, 67, 8, "screen"], [69, 12, 67, 14], [69, 13, 67, 15, "_route"], [69, 19, 67, 21], [69, 22, 67, 24, "node"], [69, 26, 67, 28], [70, 4, 68, 4], [71, 4, 69, 4], [71, 11, 69, 11, "screen"], [71, 17, 69, 17], [72, 2, 70, 0], [73, 2, 71, 0], [73, 11, 71, 9, "getReactNavigationScreensConfig"], [73, 42, 71, 40, "getReactNavigationScreensConfig"], [73, 43, 71, 41, "nodes"], [73, 48, 71, 46], [73, 50, 71, 48, "metaOnly"], [73, 58, 71, 56], [73, 60, 71, 58], [74, 4, 72, 4], [74, 11, 72, 11, "Object"], [74, 17, 72, 17], [74, 18, 72, 18, "fromEntries"], [74, 29, 72, 29], [74, 30, 72, 30, "nodes"], [74, 35, 72, 35], [74, 36, 72, 36, "map"], [74, 39, 72, 39], [74, 40, 72, 41, "node"], [74, 44, 72, 45], [74, 48, 72, 50], [74, 49, 72, 51, "node"], [74, 53, 72, 55], [74, 54, 72, 56, "route"], [74, 59, 72, 61], [74, 61, 72, 63, "convertRouteNodeToScreen"], [74, 85, 72, 87], [74, 86, 72, 88, "node"], [74, 90, 72, 92], [74, 92, 72, 94, "metaOnly"], [74, 100, 72, 102], [74, 101, 72, 103], [74, 102, 72, 104], [74, 103, 72, 105], [74, 104, 72, 106], [75, 2, 73, 0], [76, 2, 74, 0], [76, 11, 74, 9, "getReactNavigationConfig"], [76, 35, 74, 33, "getReactNavigationConfig"], [76, 36, 74, 34, "routes"], [76, 42, 74, 40], [76, 44, 74, 42, "metaOnly"], [76, 52, 74, 50], [76, 54, 74, 52], [77, 4, 75, 4], [77, 8, 75, 10, "config"], [77, 14, 75, 16], [77, 17, 75, 19], [78, 6, 76, 8, "initialRouteName"], [78, 22, 76, 24], [78, 24, 76, 26, "undefined"], [78, 33, 76, 35], [79, 6, 77, 8, "screens"], [79, 13, 77, 15], [79, 15, 77, 17, "getReactNavigationScreensConfig"], [79, 46, 77, 48], [79, 47, 77, 49, "routes"], [79, 53, 77, 55], [79, 54, 77, 56, "children"], [79, 62, 77, 64], [79, 64, 77, 66, "metaOnly"], [79, 72, 77, 74], [80, 4, 78, 4], [80, 5, 78, 5], [81, 4, 79, 4], [81, 8, 79, 8, "routes"], [81, 14, 79, 14], [81, 15, 79, 15, "initialRouteName"], [81, 31, 79, 31], [81, 33, 79, 33], [82, 6, 80, 8], [83, 6, 81, 8], [84, 6, 82, 8, "config"], [84, 12, 82, 14], [84, 13, 82, 15, "initialRouteName"], [84, 29, 82, 31], [84, 32, 82, 34, "routes"], [84, 38, 82, 40], [84, 39, 82, 41, "initialRouteName"], [84, 55, 82, 57], [85, 4, 83, 4], [86, 4, 84, 4], [86, 11, 84, 11, "config"], [86, 17, 84, 17], [87, 2, 85, 0], [88, 0, 85, 1], [88, 3]], "functionMap": {"names": ["<global>", "convertDynamicRouteToReactNavigation", "parseRouteSegments", "convertRouteNodeToScreen", "getReactNavigationScreensConfig", "nodes.map$argument_0", "getReactNavigationConfig"], "mappings": "AAA;ACQ;CDkB;AEC;CFa;AGC;CH4B;AIC;wCCC,gED;CJC;AMC;CNW"}}, "type": "js/module"}]}