{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./animationParser", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 182}, "end": {"line": 10, "column": 70, "index": 252}}], "key": "veT8C2+eXrYleq+qf11HD5FuNqc=", "exportNames": ["*"]}}, {"name": "./config", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 331}, "end": {"line": 12, "column": 58, "index": 389}}], "key": "apL7GyCxHQJfXSypmIMW0g+q+wo=", "exportNames": ["*"]}}, {"name": "./domUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 390}, "end": {"line": 13, "column": 48, "index": 438}}], "key": "0d4bIOSgNZHGMgw8FnUojmKgfKI=", "exportNames": ["*"]}}, {"name": "./transition/Curved.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 439}, "end": {"line": 14, "column": 59, "index": 498}}], "key": "1KBOwK8Q2OEq8QFDuvJCQw0eB+o=", "exportNames": ["*"]}}, {"name": "./transition/EntryExit.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 499}, "end": {"line": 15, "column": 65, "index": 564}}], "key": "Zqd2/jHiDyu8QgqFlI0df4dLVYc=", "exportNames": ["*"]}}, {"name": "./transition/Fading.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 565}, "end": {"line": 16, "column": 59, "index": 624}}], "key": "EgwY9SdYKUInFsF6St/hG9hWLws=", "exportNames": ["*"]}}, {"name": "./transition/Jumping.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 625}, "end": {"line": 17, "column": 61, "index": 686}}], "key": "OG6qLLnXMIPfvMD/V/gpmK+LDOQ=", "exportNames": ["*"]}}, {"name": "./transition/Linear.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 687}, "end": {"line": 18, "column": 59, "index": 746}}], "key": "XoWBgg1y+eP20Nbh6SDcQAhxVVw=", "exportNames": ["*"]}}, {"name": "./transition/Sequenced.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 747}, "end": {"line": 19, "column": 65, "index": 812}}], "key": "SGAIgwXKpB57X1LdAEgc7SSzCPc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TransitionGenerator = TransitionGenerator;\n  exports.createAnimationWithInitialValues = createAnimationWithInitialValues;\n  exports.createCustomKeyFrameAnimation = createCustomKeyFrameAnimation;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/slicedToArray\"));\n  var _animationParser = require(_dependencyMap[3], \"./animationParser\");\n  var _config = require(_dependencyMap[4], \"./config\");\n  var _domUtils = require(_dependencyMap[5], \"./domUtils\");\n  var _Curved = require(_dependencyMap[6], \"./transition/Curved.web\");\n  var _EntryExit = require(_dependencyMap[7], \"./transition/EntryExit.web\");\n  var _Fading = require(_dependencyMap[8], \"./transition/Fading.web\");\n  var _Jumping = require(_dependencyMap[9], \"./transition/Jumping.web\");\n  var _Linear = require(_dependencyMap[10], \"./transition/Linear.web\");\n  var _Sequenced = require(_dependencyMap[11], \"./transition/Sequenced.web\");\n  var _excluded = [\"transform\"];\n  // Translate values are passed as numbers. However, if `translate` property receives number, it will not automatically\n  // convert it to `px`. Therefore if we want to keep transform we have to add 'px' suffix to each of translate values\n  // that are present inside transform.\n  //\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  function addPxToTransform(transform) {\n    // @ts-ignore `existingTransform` cannot be string because in that case\n    // we throw error in `extractTransformFromStyle`\n    var newTransform = transform.map(transformProp => {\n      var newTransformProp = {};\n      for (var _ref of Object.entries(transformProp)) {\n        var _ref2 = (0, _slicedToArray2.default)(_ref, 2);\n        var key = _ref2[0];\n        var value = _ref2[1];\n        if ((key.includes('translate') || key.includes('perspective')) && typeof value === 'number') {\n          // @ts-ignore After many trials we decided to ignore this error - it says that we cannot use 'key' to index this object.\n          // Sadly it doesn't go away after using cast `key as keyof TransformProperties`.\n          newTransformProp[key] = `${value}px`;\n        } else {\n          // @ts-ignore same as above.\n          newTransformProp[key] = value;\n        }\n      }\n      return newTransformProp;\n    });\n    return newTransform;\n  }\n  function createCustomKeyFrameAnimation(keyframeDefinitions) {\n    for (var value of Object.values(keyframeDefinitions)) {\n      if (value.transform) {\n        value.transform = addPxToTransform(value.transform);\n      }\n    }\n    var animationData = {\n      name: '',\n      style: keyframeDefinitions,\n      duration: -1\n    };\n    animationData.name = generateNextCustomKeyframeName();\n    var parsedKeyframe = (0, _animationParser.convertAnimationObjectToKeyframes)(animationData);\n    (0, _domUtils.insertWebAnimation)(animationData.name, parsedKeyframe);\n    return animationData.name;\n  }\n  function createAnimationWithInitialValues(animationName, initialValues) {\n    var animationStyle = structuredClone(_config.AnimationsData[animationName].style);\n    var firstAnimationStep = animationStyle['0'];\n    var transform = initialValues.transform,\n      rest = (0, _objectWithoutProperties2.default)(initialValues, _excluded);\n    if (transform) {\n      var transformWithPx = addPxToTransform(transform);\n      // If there was no predefined transform, we can simply assign transform from `initialValues`.\n      if (!firstAnimationStep.transform) {\n        firstAnimationStep.transform = transformWithPx;\n      } else {\n        // Othwerwise we have to merge predefined transform with the one provided in `initialValues`.\n        // To do that, we create `Map` that will contain final transform.\n        var transformStyle = new Map();\n\n        // First we assign all of the predefined rules\n        for (var rule of firstAnimationStep.transform) {\n          // In most cases there will be just one iteration\n          for (var _ref3 of Object.entries(rule)) {\n            var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);\n            var property = _ref4[0];\n            var value = _ref4[1];\n            transformStyle.set(property, value);\n          }\n        }\n\n        // Then we either add new rule, or override one that already exists.\n        for (var _rule of transformWithPx) {\n          for (var _ref5 of Object.entries(_rule)) {\n            var _ref6 = (0, _slicedToArray2.default)(_ref5, 2);\n            var _property = _ref6[0];\n            var _value = _ref6[1];\n            transformStyle.set(_property, _value);\n          }\n        }\n\n        // Finally, we convert `Map` with final transform back into array of objects.\n        firstAnimationStep.transform = Array.from(transformStyle, _ref7 => {\n          var _ref8 = (0, _slicedToArray2.default)(_ref7, 2),\n            property = _ref8[0],\n            value = _ref8[1];\n          return {\n            [property]: value\n          };\n        });\n      }\n    }\n    animationStyle['0'] = {\n      ...animationStyle['0'],\n      ...rest\n    };\n\n    // TODO: Maybe we can extract the logic below into separate function\n    var keyframeName = generateNextCustomKeyframeName();\n    var animationObject = {\n      name: keyframeName,\n      style: animationStyle,\n      duration: _config.AnimationsData[animationName].duration\n    };\n    var keyframe = (0, _animationParser.convertAnimationObjectToKeyframes)(animationObject);\n    (0, _domUtils.insertWebAnimation)(keyframeName, keyframe);\n    return keyframeName;\n  }\n  var customKeyframeCounter = 0;\n  function generateNextCustomKeyframeName() {\n    return `REA${customKeyframeCounter++}`;\n  }\n\n  /**\n   * Creates transition of given type, appends it to stylesheet and returns\n   * keyframe name.\n   *\n   * @param transitionType - Type of transition (e.g. LINEAR).\n   * @param transitionData - Object containing data for transforms (translateX,\n   *   scaleX,...).\n   * @returns Keyframe name that represents transition.\n   */\n  function TransitionGenerator(transitionType, transitionData) {\n    var transitionKeyframeName = generateNextCustomKeyframeName();\n    var dummyTransitionKeyframeName;\n    var transitionObject;\n    switch (transitionType) {\n      case _config.TransitionType.LINEAR:\n        transitionObject = (0, _Linear.LinearTransition)(transitionKeyframeName, transitionData);\n        break;\n      case _config.TransitionType.SEQUENCED:\n        transitionObject = (0, _Sequenced.SequencedTransition)(transitionKeyframeName, transitionData);\n        break;\n      case _config.TransitionType.FADING:\n        transitionObject = (0, _Fading.FadingTransition)(transitionKeyframeName, transitionData);\n        break;\n      case _config.TransitionType.JUMPING:\n        transitionObject = (0, _Jumping.JumpingTransition)(transitionKeyframeName, transitionData);\n        break;\n\n      // Here code block with {} is necessary because of eslint\n      case _config.TransitionType.CURVED:\n        {\n          dummyTransitionKeyframeName = generateNextCustomKeyframeName();\n          var _CurvedTransition = (0, _Curved.CurvedTransition)(transitionKeyframeName, dummyTransitionKeyframeName, transitionData),\n            firstKeyframeObj = _CurvedTransition.firstKeyframeObj,\n            secondKeyframeObj = _CurvedTransition.secondKeyframeObj;\n          transitionObject = firstKeyframeObj;\n          var dummyKeyframe = (0, _animationParser.convertAnimationObjectToKeyframes)(secondKeyframeObj);\n          (0, _domUtils.insertWebAnimation)(dummyTransitionKeyframeName, dummyKeyframe);\n          break;\n        }\n      case _config.TransitionType.ENTRY_EXIT:\n        transitionObject = (0, _EntryExit.EntryExitTransition)(transitionKeyframeName, transitionData);\n        break;\n    }\n    var transitionKeyframe = (0, _animationParser.convertAnimationObjectToKeyframes)(transitionObject);\n    (0, _domUtils.insertWebAnimation)(transitionKeyframeName, transitionKeyframe);\n    return {\n      transitionKeyframeName,\n      dummyTransitionKeyframeName\n    };\n  }\n});", "lineCount": 184, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "TransitionGenerator"], [8, 29, 1, 13], [8, 32, 1, 13, "TransitionGenerator"], [8, 51, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "createAnimationWithInitialValues"], [9, 42, 1, 13], [9, 45, 1, 13, "createAnimationWithInitialValues"], [9, 77, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "createCustomKeyFrameAnimation"], [10, 39, 1, 13], [10, 42, 1, 13, "createCustomKeyFrameAnimation"], [10, 71, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_objectWithoutProperties2"], [11, 31, 1, 13], [11, 34, 1, 13, "_interopRequireDefault"], [11, 56, 1, 13], [11, 57, 1, 13, "require"], [11, 64, 1, 13], [11, 65, 1, 13, "_dependencyMap"], [11, 79, 1, 13], [12, 2, 1, 13], [12, 6, 1, 13, "_slicedToArray2"], [12, 21, 1, 13], [12, 24, 1, 13, "_interopRequireDefault"], [12, 46, 1, 13], [12, 47, 1, 13, "require"], [12, 54, 1, 13], [12, 55, 1, 13, "_dependencyMap"], [12, 69, 1, 13], [13, 2, 10, 0], [13, 6, 10, 0, "_animation<PERSON><PERSON>er"], [13, 22, 10, 0], [13, 25, 10, 0, "require"], [13, 32, 10, 0], [13, 33, 10, 0, "_dependencyMap"], [13, 47, 10, 0], [14, 2, 12, 0], [14, 6, 12, 0, "_config"], [14, 13, 12, 0], [14, 16, 12, 0, "require"], [14, 23, 12, 0], [14, 24, 12, 0, "_dependencyMap"], [14, 38, 12, 0], [15, 2, 13, 0], [15, 6, 13, 0, "_domUtils"], [15, 15, 13, 0], [15, 18, 13, 0, "require"], [15, 25, 13, 0], [15, 26, 13, 0, "_dependencyMap"], [15, 40, 13, 0], [16, 2, 14, 0], [16, 6, 14, 0, "_Curved"], [16, 13, 14, 0], [16, 16, 14, 0, "require"], [16, 23, 14, 0], [16, 24, 14, 0, "_dependencyMap"], [16, 38, 14, 0], [17, 2, 15, 0], [17, 6, 15, 0, "_EntryExit"], [17, 16, 15, 0], [17, 19, 15, 0, "require"], [17, 26, 15, 0], [17, 27, 15, 0, "_dependencyMap"], [17, 41, 15, 0], [18, 2, 16, 0], [18, 6, 16, 0, "_Fading"], [18, 13, 16, 0], [18, 16, 16, 0, "require"], [18, 23, 16, 0], [18, 24, 16, 0, "_dependencyMap"], [18, 38, 16, 0], [19, 2, 17, 0], [19, 6, 17, 0, "_Jumping"], [19, 14, 17, 0], [19, 17, 17, 0, "require"], [19, 24, 17, 0], [19, 25, 17, 0, "_dependencyMap"], [19, 39, 17, 0], [20, 2, 18, 0], [20, 6, 18, 0, "_Linear"], [20, 13, 18, 0], [20, 16, 18, 0, "require"], [20, 23, 18, 0], [20, 24, 18, 0, "_dependencyMap"], [20, 38, 18, 0], [21, 2, 19, 0], [21, 6, 19, 0, "_Sequenced"], [21, 16, 19, 0], [21, 19, 19, 0, "require"], [21, 26, 19, 0], [21, 27, 19, 0, "_dependencyMap"], [21, 41, 19, 0], [22, 2, 19, 65], [22, 6, 19, 65, "_excluded"], [22, 15, 19, 65], [23, 2, 23, 0], [24, 2, 24, 0], [25, 2, 25, 0], [26, 2, 26, 0], [27, 2, 27, 0], [28, 2, 28, 0], [28, 11, 28, 9, "addPxToTransform"], [28, 27, 28, 25, "addPxToTransform"], [28, 28, 28, 26, "transform"], [28, 37, 28, 50], [28, 39, 28, 52], [29, 4, 31, 2], [30, 4, 32, 2], [31, 4, 33, 2], [31, 8, 33, 8, "newTransform"], [31, 20, 33, 20], [31, 23, 33, 23, "transform"], [31, 32, 33, 32], [31, 33, 33, 33, "map"], [31, 36, 33, 36], [31, 37, 33, 38, "transformProp"], [31, 50, 33, 68], [31, 54, 33, 73], [32, 6, 34, 4], [32, 10, 34, 10, "newTransformProp"], [32, 26, 34, 60], [32, 29, 34, 63], [32, 30, 34, 64], [32, 31, 34, 65], [33, 6, 35, 4], [33, 15, 35, 4, "_ref"], [33, 19, 35, 4], [33, 23, 35, 31, "Object"], [33, 29, 35, 37], [33, 30, 35, 38, "entries"], [33, 37, 35, 45], [33, 38, 35, 46, "transformProp"], [33, 51, 35, 59], [33, 52, 35, 60], [33, 54, 35, 62], [34, 8, 35, 62], [34, 12, 35, 62, "_ref2"], [34, 17, 35, 62], [34, 24, 35, 62, "_slicedToArray2"], [34, 39, 35, 62], [34, 40, 35, 62, "default"], [34, 47, 35, 62], [34, 49, 35, 62, "_ref"], [34, 53, 35, 62], [35, 8, 35, 62], [35, 12, 35, 16, "key"], [35, 15, 35, 19], [35, 18, 35, 19, "_ref2"], [35, 23, 35, 19], [36, 8, 35, 19], [36, 12, 35, 21, "value"], [36, 17, 35, 26], [36, 20, 35, 26, "_ref2"], [36, 25, 35, 26], [37, 8, 36, 6], [37, 12, 37, 8], [37, 13, 37, 9, "key"], [37, 16, 37, 12], [37, 17, 37, 13, "includes"], [37, 25, 37, 21], [37, 26, 37, 22], [37, 37, 37, 33], [37, 38, 37, 34], [37, 42, 37, 38, "key"], [37, 45, 37, 41], [37, 46, 37, 42, "includes"], [37, 54, 37, 50], [37, 55, 37, 51], [37, 68, 37, 64], [37, 69, 37, 65], [37, 74, 38, 8], [37, 81, 38, 15, "value"], [37, 86, 38, 20], [37, 91, 38, 25], [37, 99, 38, 33], [37, 101, 39, 8], [38, 10, 40, 8], [39, 10, 41, 8], [40, 10, 42, 8, "newTransformProp"], [40, 26, 42, 24], [40, 27, 42, 25, "key"], [40, 30, 42, 28], [40, 31, 42, 29], [40, 34, 42, 32], [40, 37, 42, 35, "value"], [40, 42, 42, 40], [40, 46, 42, 44], [41, 8, 43, 6], [41, 9, 43, 7], [41, 15, 43, 13], [42, 10, 44, 8], [43, 10, 45, 8, "newTransformProp"], [43, 26, 45, 24], [43, 27, 45, 25, "key"], [43, 30, 45, 28], [43, 31, 45, 29], [43, 34, 45, 32, "value"], [43, 39, 45, 37], [44, 8, 46, 6], [45, 6, 47, 4], [46, 6, 48, 4], [46, 13, 48, 11, "newTransformProp"], [46, 29, 48, 27], [47, 4, 49, 2], [47, 5, 49, 3], [47, 6, 49, 4], [48, 4, 51, 2], [48, 11, 51, 9, "newTransform"], [48, 23, 51, 21], [49, 2, 52, 0], [50, 2, 54, 7], [50, 11, 54, 16, "createCustomKeyFrameAnimation"], [50, 40, 54, 45, "createCustomKeyFrameAnimation"], [50, 41, 55, 2, "keyframeDefinitions"], [50, 60, 55, 42], [50, 62, 56, 2], [51, 4, 57, 2], [51, 9, 57, 7], [51, 13, 57, 13, "value"], [51, 18, 57, 18], [51, 22, 57, 22, "Object"], [51, 28, 57, 28], [51, 29, 57, 29, "values"], [51, 35, 57, 35], [51, 36, 57, 36, "keyframeDefinitions"], [51, 55, 57, 55], [51, 56, 57, 56], [51, 58, 57, 58], [52, 6, 58, 4], [52, 10, 58, 8, "value"], [52, 15, 58, 13], [52, 16, 58, 14, "transform"], [52, 25, 58, 23], [52, 27, 58, 25], [53, 8, 59, 6, "value"], [53, 13, 59, 11], [53, 14, 59, 12, "transform"], [53, 23, 59, 21], [53, 26, 59, 24, "addPxToTransform"], [53, 42, 59, 40], [53, 43, 59, 41, "value"], [53, 48, 59, 46], [53, 49, 59, 47, "transform"], [53, 58, 59, 73], [53, 59, 59, 74], [54, 6, 60, 4], [55, 4, 61, 2], [56, 4, 63, 2], [56, 8, 63, 8, "animationData"], [56, 21, 63, 36], [56, 24, 63, 39], [57, 6, 64, 4, "name"], [57, 10, 64, 8], [57, 12, 64, 10], [57, 14, 64, 12], [58, 6, 65, 4, "style"], [58, 11, 65, 9], [58, 13, 65, 11, "keyframeDefinitions"], [58, 32, 65, 30], [59, 6, 66, 4, "duration"], [59, 14, 66, 12], [59, 16, 66, 14], [59, 17, 66, 15], [60, 4, 67, 2], [60, 5, 67, 3], [61, 4, 69, 2, "animationData"], [61, 17, 69, 15], [61, 18, 69, 16, "name"], [61, 22, 69, 20], [61, 25, 69, 23, "generateNextCustomKeyframeName"], [61, 55, 69, 53], [61, 56, 69, 54], [61, 57, 69, 55], [62, 4, 71, 2], [62, 8, 71, 8, "parsedKeyframe"], [62, 22, 71, 22], [62, 25, 71, 25], [62, 29, 71, 25, "convertAnimationObjectToKeyframes"], [62, 79, 71, 58], [62, 81, 71, 59, "animationData"], [62, 94, 71, 72], [62, 95, 71, 73], [63, 4, 73, 2], [63, 8, 73, 2, "insertWebAnimation"], [63, 36, 73, 20], [63, 38, 73, 21, "animationData"], [63, 51, 73, 34], [63, 52, 73, 35, "name"], [63, 56, 73, 39], [63, 58, 73, 41, "parsedKeyframe"], [63, 72, 73, 55], [63, 73, 73, 56], [64, 4, 75, 2], [64, 11, 75, 9, "animationData"], [64, 24, 75, 22], [64, 25, 75, 23, "name"], [64, 29, 75, 27], [65, 2, 76, 0], [66, 2, 78, 7], [66, 11, 78, 16, "createAnimationWithInitialValues"], [66, 43, 78, 48, "createAnimationWithInitialValues"], [66, 44, 79, 2, "animationName"], [66, 57, 79, 23], [66, 59, 80, 2, "initialValues"], [66, 72, 80, 40], [66, 74, 81, 2], [67, 4, 82, 2], [67, 8, 82, 8, "animationStyle"], [67, 22, 82, 22], [67, 25, 82, 25, "structuredClone"], [67, 40, 82, 40], [67, 41, 82, 41, "AnimationsData"], [67, 63, 82, 55], [67, 64, 82, 56, "animationName"], [67, 77, 82, 69], [67, 78, 82, 70], [67, 79, 82, 71, "style"], [67, 84, 82, 76], [67, 85, 82, 77], [68, 4, 83, 2], [68, 8, 83, 8, "firstAnimationStep"], [68, 26, 83, 26], [68, 29, 83, 29, "animationStyle"], [68, 43, 83, 43], [68, 44, 83, 44], [68, 47, 83, 47], [68, 48, 83, 48], [69, 4, 85, 2], [69, 8, 85, 10, "transform"], [69, 17, 85, 19], [69, 20, 85, 33, "initialValues"], [69, 33, 85, 46], [69, 34, 85, 10, "transform"], [69, 43, 85, 19], [70, 6, 85, 24, "rest"], [70, 10, 85, 28], [70, 17, 85, 28, "_objectWithoutProperties2"], [70, 42, 85, 28], [70, 43, 85, 28, "default"], [70, 50, 85, 28], [70, 52, 85, 33, "initialValues"], [70, 65, 85, 46], [70, 67, 85, 46, "_excluded"], [70, 76, 85, 46], [71, 4, 87, 2], [71, 8, 87, 6, "transform"], [71, 17, 87, 15], [71, 19, 87, 17], [72, 6, 88, 4], [72, 10, 88, 10, "transformWithPx"], [72, 25, 88, 25], [72, 28, 88, 28, "addPxToTransform"], [72, 44, 88, 44], [72, 45, 88, 45, "transform"], [72, 54, 88, 71], [72, 55, 88, 72], [73, 6, 89, 4], [74, 6, 90, 4], [74, 10, 90, 8], [74, 11, 90, 9, "firstAnimationStep"], [74, 29, 90, 27], [74, 30, 90, 28, "transform"], [74, 39, 90, 37], [74, 41, 90, 39], [75, 8, 91, 6, "firstAnimationStep"], [75, 26, 91, 24], [75, 27, 91, 25, "transform"], [75, 36, 91, 34], [75, 39, 91, 37, "transformWithPx"], [75, 54, 91, 52], [76, 6, 92, 4], [76, 7, 92, 5], [76, 13, 92, 11], [77, 8, 93, 6], [78, 8, 94, 6], [79, 8, 95, 6], [79, 12, 95, 12, "transformStyle"], [79, 26, 95, 26], [79, 29, 95, 29], [79, 33, 95, 33, "Map"], [79, 36, 95, 36], [79, 37, 95, 50], [79, 38, 95, 51], [81, 8, 97, 6], [82, 8, 98, 6], [82, 13, 98, 11], [82, 17, 98, 17, "rule"], [82, 21, 98, 21], [82, 25, 98, 25, "firstAnimationStep"], [82, 43, 98, 43], [82, 44, 98, 44, "transform"], [82, 53, 98, 53], [82, 55, 98, 55], [83, 10, 99, 8], [84, 10, 100, 8], [84, 19, 100, 8, "_ref3"], [84, 24, 100, 8], [84, 28, 100, 40, "Object"], [84, 34, 100, 46], [84, 35, 100, 47, "entries"], [84, 42, 100, 54], [84, 43, 100, 55, "rule"], [84, 47, 100, 59], [84, 48, 100, 60], [84, 50, 100, 62], [85, 12, 100, 62], [85, 16, 100, 62, "_ref4"], [85, 21, 100, 62], [85, 28, 100, 62, "_slicedToArray2"], [85, 43, 100, 62], [85, 44, 100, 62, "default"], [85, 51, 100, 62], [85, 53, 100, 62, "_ref3"], [85, 58, 100, 62], [86, 12, 100, 62], [86, 16, 100, 20, "property"], [86, 24, 100, 28], [86, 27, 100, 28, "_ref4"], [86, 32, 100, 28], [87, 12, 100, 28], [87, 16, 100, 30, "value"], [87, 21, 100, 35], [87, 24, 100, 35, "_ref4"], [87, 29, 100, 35], [88, 12, 101, 10, "transformStyle"], [88, 26, 101, 24], [88, 27, 101, 25, "set"], [88, 30, 101, 28], [88, 31, 101, 29, "property"], [88, 39, 101, 37], [88, 41, 101, 39, "value"], [88, 46, 101, 44], [88, 47, 101, 45], [89, 10, 102, 8], [90, 8, 103, 6], [92, 8, 105, 6], [93, 8, 106, 6], [93, 13, 106, 11], [93, 17, 106, 17, "rule"], [93, 22, 106, 21], [93, 26, 106, 25, "transformWithPx"], [93, 41, 106, 40], [93, 43, 106, 42], [94, 10, 107, 8], [94, 19, 107, 8, "_ref5"], [94, 24, 107, 8], [94, 28, 107, 40, "Object"], [94, 34, 107, 46], [94, 35, 107, 47, "entries"], [94, 42, 107, 54], [94, 43, 107, 55, "rule"], [94, 48, 107, 59], [94, 49, 107, 60], [94, 51, 107, 62], [95, 12, 107, 62], [95, 16, 107, 62, "_ref6"], [95, 21, 107, 62], [95, 28, 107, 62, "_slicedToArray2"], [95, 43, 107, 62], [95, 44, 107, 62, "default"], [95, 51, 107, 62], [95, 53, 107, 62, "_ref5"], [95, 58, 107, 62], [96, 12, 107, 62], [96, 16, 107, 20, "property"], [96, 25, 107, 28], [96, 28, 107, 28, "_ref6"], [96, 33, 107, 28], [97, 12, 107, 28], [97, 16, 107, 30, "value"], [97, 22, 107, 35], [97, 25, 107, 35, "_ref6"], [97, 30, 107, 35], [98, 12, 108, 10, "transformStyle"], [98, 26, 108, 24], [98, 27, 108, 25, "set"], [98, 30, 108, 28], [98, 31, 108, 29, "property"], [98, 40, 108, 37], [98, 42, 108, 39, "value"], [98, 48, 108, 44], [98, 49, 108, 45], [99, 10, 109, 8], [100, 8, 110, 6], [102, 8, 112, 6], [103, 8, 113, 6, "firstAnimationStep"], [103, 26, 113, 24], [103, 27, 113, 25, "transform"], [103, 36, 113, 34], [103, 39, 113, 37, "Array"], [103, 44, 113, 42], [103, 45, 113, 43, "from"], [103, 49, 113, 47], [103, 50, 114, 8, "transformStyle"], [103, 64, 114, 22], [103, 66, 115, 8, "_ref7"], [103, 71, 115, 8], [104, 10, 115, 8], [104, 14, 115, 8, "_ref8"], [104, 19, 115, 8], [104, 26, 115, 8, "_slicedToArray2"], [104, 41, 115, 8], [104, 42, 115, 8, "default"], [104, 49, 115, 8], [104, 51, 115, 8, "_ref7"], [104, 56, 115, 8], [105, 12, 115, 10, "property"], [105, 20, 115, 18], [105, 23, 115, 18, "_ref8"], [105, 28, 115, 18], [106, 12, 115, 20, "value"], [106, 17, 115, 25], [106, 20, 115, 25, "_ref8"], [106, 25, 115, 25], [107, 10, 115, 25], [107, 17, 115, 32], [108, 12, 116, 10], [108, 13, 116, 11, "property"], [108, 21, 116, 19], [108, 24, 116, 22, "value"], [109, 10, 117, 8], [109, 11, 117, 9], [110, 8, 117, 9], [110, 9, 118, 6], [110, 10, 118, 7], [111, 6, 119, 4], [112, 4, 120, 2], [113, 4, 122, 2, "animationStyle"], [113, 18, 122, 16], [113, 19, 122, 17], [113, 22, 122, 20], [113, 23, 122, 21], [113, 26, 122, 24], [114, 6, 123, 4], [114, 9, 123, 7, "animationStyle"], [114, 23, 123, 21], [114, 24, 123, 22], [114, 27, 123, 25], [114, 28, 123, 26], [115, 6, 124, 4], [115, 9, 124, 7, "rest"], [116, 4, 125, 2], [116, 5, 125, 3], [118, 4, 127, 2], [119, 4, 128, 2], [119, 8, 128, 8, "keyframeName"], [119, 20, 128, 20], [119, 23, 128, 23, "generateNextCustomKeyframeName"], [119, 53, 128, 53], [119, 54, 128, 54], [119, 55, 128, 55], [120, 4, 130, 2], [120, 8, 130, 8, "animationObject"], [120, 23, 130, 38], [120, 26, 130, 41], [121, 6, 131, 4, "name"], [121, 10, 131, 8], [121, 12, 131, 10, "keyframeName"], [121, 24, 131, 22], [122, 6, 132, 4, "style"], [122, 11, 132, 9], [122, 13, 132, 11, "animationStyle"], [122, 27, 132, 25], [123, 6, 133, 4, "duration"], [123, 14, 133, 12], [123, 16, 133, 14, "AnimationsData"], [123, 38, 133, 28], [123, 39, 133, 29, "animationName"], [123, 52, 133, 42], [123, 53, 133, 43], [123, 54, 133, 44, "duration"], [124, 4, 134, 2], [124, 5, 134, 3], [125, 4, 136, 2], [125, 8, 136, 8, "keyframe"], [125, 16, 136, 16], [125, 19, 136, 19], [125, 23, 136, 19, "convertAnimationObjectToKeyframes"], [125, 73, 136, 52], [125, 75, 136, 53, "animationObject"], [125, 90, 136, 68], [125, 91, 136, 69], [126, 4, 138, 2], [126, 8, 138, 2, "insertWebAnimation"], [126, 36, 138, 20], [126, 38, 138, 21, "keyframeName"], [126, 50, 138, 33], [126, 52, 138, 35, "keyframe"], [126, 60, 138, 43], [126, 61, 138, 44], [127, 4, 140, 2], [127, 11, 140, 9, "keyframeName"], [127, 23, 140, 21], [128, 2, 141, 0], [129, 2, 143, 0], [129, 6, 143, 4, "customKeyframeCounter"], [129, 27, 143, 25], [129, 30, 143, 28], [129, 31, 143, 29], [130, 2, 145, 0], [130, 11, 145, 9, "generateNextCustomKeyframeName"], [130, 41, 145, 39, "generateNextCustomKeyframeName"], [130, 42, 145, 39], [130, 44, 145, 42], [131, 4, 146, 2], [131, 11, 146, 9], [131, 17, 146, 15, "customKeyframeCounter"], [131, 38, 146, 36], [131, 40, 146, 38], [131, 42, 146, 40], [132, 2, 147, 0], [134, 2, 149, 0], [135, 0, 150, 0], [136, 0, 151, 0], [137, 0, 152, 0], [138, 0, 153, 0], [139, 0, 154, 0], [140, 0, 155, 0], [141, 0, 156, 0], [142, 0, 157, 0], [143, 2, 158, 7], [143, 11, 158, 16, "TransitionGenerator"], [143, 30, 158, 35, "TransitionGenerator"], [143, 31, 159, 2, "transitionType"], [143, 45, 159, 32], [143, 47, 160, 2, "transitionData"], [143, 61, 160, 32], [143, 63, 161, 2], [144, 4, 162, 2], [144, 8, 162, 8, "transitionKeyframeName"], [144, 30, 162, 30], [144, 33, 162, 33, "generateNextCustomKeyframeName"], [144, 63, 162, 63], [144, 64, 162, 64], [144, 65, 162, 65], [145, 4, 163, 2], [145, 8, 163, 6, "dummyTransitionKeyframeName"], [145, 35, 163, 33], [146, 4, 165, 2], [146, 8, 165, 6, "transitionObject"], [146, 24, 165, 22], [147, 4, 167, 2], [147, 12, 167, 10, "transitionType"], [147, 26, 167, 24], [148, 6, 168, 4], [148, 11, 168, 9, "TransitionType"], [148, 33, 168, 23], [148, 34, 168, 24, "LINEAR"], [148, 40, 168, 30], [149, 8, 169, 6, "transitionObject"], [149, 24, 169, 22], [149, 27, 169, 25], [149, 31, 169, 25, "LinearTransition"], [149, 55, 169, 41], [149, 57, 170, 8, "transitionKeyframeName"], [149, 79, 170, 30], [149, 81, 171, 8, "transitionData"], [149, 95, 172, 6], [149, 96, 172, 7], [150, 8, 173, 6], [151, 6, 174, 4], [151, 11, 174, 9, "TransitionType"], [151, 33, 174, 23], [151, 34, 174, 24, "SEQUENCED"], [151, 43, 174, 33], [152, 8, 175, 6, "transitionObject"], [152, 24, 175, 22], [152, 27, 175, 25], [152, 31, 175, 25, "SequencedTransition"], [152, 61, 175, 44], [152, 63, 176, 8, "transitionKeyframeName"], [152, 85, 176, 30], [152, 87, 177, 8, "transitionData"], [152, 101, 178, 6], [152, 102, 178, 7], [153, 8, 179, 6], [154, 6, 180, 4], [154, 11, 180, 9, "TransitionType"], [154, 33, 180, 23], [154, 34, 180, 24, "FADING"], [154, 40, 180, 30], [155, 8, 181, 6, "transitionObject"], [155, 24, 181, 22], [155, 27, 181, 25], [155, 31, 181, 25, "FadingTransition"], [155, 55, 181, 41], [155, 57, 182, 8, "transitionKeyframeName"], [155, 79, 182, 30], [155, 81, 183, 8, "transitionData"], [155, 95, 184, 6], [155, 96, 184, 7], [156, 8, 185, 6], [157, 6, 186, 4], [157, 11, 186, 9, "TransitionType"], [157, 33, 186, 23], [157, 34, 186, 24, "JUMPING"], [157, 41, 186, 31], [158, 8, 187, 6, "transitionObject"], [158, 24, 187, 22], [158, 27, 187, 25], [158, 31, 187, 25, "JumpingTransition"], [158, 57, 187, 42], [158, 59, 188, 8, "transitionKeyframeName"], [158, 81, 188, 30], [158, 83, 189, 8, "transitionData"], [158, 97, 190, 6], [158, 98, 190, 7], [159, 8, 191, 6], [161, 6, 193, 4], [162, 6, 194, 4], [162, 11, 194, 9, "TransitionType"], [162, 33, 194, 23], [162, 34, 194, 24, "CURVED"], [162, 40, 194, 30], [163, 8, 194, 32], [164, 10, 195, 6, "dummyTransitionKeyframeName"], [164, 37, 195, 33], [164, 40, 195, 36, "generateNextCustomKeyframeName"], [164, 70, 195, 66], [164, 71, 195, 67], [164, 72, 195, 68], [165, 10, 197, 6], [165, 14, 197, 6, "_CurvedTransition"], [165, 31, 197, 6], [165, 34, 197, 54], [165, 38, 197, 54, "CurvedTransition"], [165, 62, 197, 70], [165, 64, 198, 8, "transitionKeyframeName"], [165, 86, 198, 30], [165, 88, 199, 8, "dummyTransitionKeyframeName"], [165, 115, 199, 35], [165, 117, 200, 8, "transitionData"], [165, 131, 201, 6], [165, 132, 201, 7], [166, 12, 197, 14, "firstKeyframeObj"], [166, 28, 197, 30], [166, 31, 197, 30, "_CurvedTransition"], [166, 48, 197, 30], [166, 49, 197, 14, "firstKeyframeObj"], [166, 65, 197, 30], [167, 12, 197, 32, "secondKeyframeObj"], [167, 29, 197, 49], [167, 32, 197, 49, "_CurvedTransition"], [167, 49, 197, 49], [167, 50, 197, 32, "secondKeyframeObj"], [167, 67, 197, 49], [168, 10, 203, 6, "transitionObject"], [168, 26, 203, 22], [168, 29, 203, 25, "firstKeyframeObj"], [168, 45, 203, 41], [169, 10, 205, 6], [169, 14, 205, 12, "dummy<PERSON><PERSON><PERSON>"], [169, 27, 205, 25], [169, 30, 206, 8], [169, 34, 206, 8, "convertAnimationObjectToKeyframes"], [169, 84, 206, 41], [169, 86, 206, 42, "secondKeyframeObj"], [169, 103, 206, 59], [169, 104, 206, 60], [170, 10, 208, 6], [170, 14, 208, 6, "insertWebAnimation"], [170, 42, 208, 24], [170, 44, 208, 25, "dummyTransitionKeyframeName"], [170, 71, 208, 52], [170, 73, 208, 54, "dummy<PERSON><PERSON><PERSON>"], [170, 86, 208, 67], [170, 87, 208, 68], [171, 10, 210, 6], [172, 8, 211, 4], [173, 6, 212, 4], [173, 11, 212, 9, "TransitionType"], [173, 33, 212, 23], [173, 34, 212, 24, "ENTRY_EXIT"], [173, 44, 212, 34], [174, 8, 213, 6, "transitionObject"], [174, 24, 213, 22], [174, 27, 213, 25], [174, 31, 213, 25, "EntryExitTransition"], [174, 61, 213, 44], [174, 63, 214, 8, "transitionKeyframeName"], [174, 85, 214, 30], [174, 87, 215, 8, "transitionData"], [174, 101, 216, 6], [174, 102, 216, 7], [175, 8, 217, 6], [176, 4, 218, 2], [177, 4, 220, 2], [177, 8, 220, 8, "transitionKeyframe"], [177, 26, 220, 26], [177, 29, 221, 4], [177, 33, 221, 4, "convertAnimationObjectToKeyframes"], [177, 83, 221, 37], [177, 85, 221, 38, "transitionObject"], [177, 101, 221, 54], [177, 102, 221, 55], [178, 4, 223, 2], [178, 8, 223, 2, "insertWebAnimation"], [178, 36, 223, 20], [178, 38, 223, 21, "transitionKeyframeName"], [178, 60, 223, 43], [178, 62, 223, 45, "transitionKeyframe"], [178, 80, 223, 63], [178, 81, 223, 64], [179, 4, 225, 2], [179, 11, 225, 9], [180, 6, 225, 11, "transitionKeyframeName"], [180, 28, 225, 33], [181, 6, 225, 35, "dummyTransitionKeyframeName"], [182, 4, 225, 63], [182, 5, 225, 64], [183, 2, 226, 0], [184, 0, 226, 1], [184, 3]], "functionMap": {"names": ["<global>", "addPxToTransform", "transform.map$argument_0", "createCustomKeyFrameAnimation", "createAnimationWithInitialValues", "Array.from$argument_1", "generateNextCustomKeyframeName", "TransitionGenerator"], "mappings": "AAA;AC2B;qCCK;GDgB;CDG;OGE;CHsB;OIE;QCqC;UDE;CJwB;AMI;CNE;OOW;CPoE"}}, "type": "js/module"}]}