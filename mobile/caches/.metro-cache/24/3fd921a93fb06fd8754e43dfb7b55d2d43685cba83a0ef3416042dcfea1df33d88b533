{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CodedError = void 0;\n  /**\n   * A general error class that should be used for all errors in Expo modules.\n   * Guarantees a `code` field that can be used to differentiate between different\n   * types of errors without further subclassing Error.\n   */\n  class CodedError extends Error {\n    constructor(code, message) {\n      super(message);\n      this.code = code;\n    }\n  }\n  exports.CodedError = CodedError;\n});", "lineCount": 18, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 2, 6, 7], [11, 8, 6, 13, "CodedError"], [11, 18, 6, 23], [11, 27, 6, 32, "Error"], [11, 32, 6, 37], [11, 33, 6, 38], [12, 4, 10, 2, "constructor"], [12, 15, 10, 13, "constructor"], [12, 16, 10, 14, "code"], [12, 20, 10, 26], [12, 22, 10, 28, "message"], [12, 29, 10, 43], [12, 31, 10, 45], [13, 6, 11, 4], [13, 11, 11, 9], [13, 12, 11, 10, "message"], [13, 19, 11, 17], [13, 20, 11, 18], [14, 6, 12, 4], [14, 10, 12, 8], [14, 11, 12, 9, "code"], [14, 15, 12, 13], [14, 18, 12, 16, "code"], [14, 22, 12, 20], [15, 4, 13, 2], [16, 2, 14, 0], [17, 2, 14, 1, "exports"], [17, 9, 14, 1], [17, 10, 14, 1, "CodedError"], [17, 20, 14, 1], [17, 23, 14, 1, "CodedError"], [17, 33, 14, 1], [18, 0, 14, 1], [18, 3]], "functionMap": {"names": ["<global>", "CodedError", "constructor"], "mappings": "AAA;OCK;ECI;GDG;CDC"}}, "type": "js/module"}]}