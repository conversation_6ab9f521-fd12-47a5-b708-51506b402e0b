{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createURL = createURL;\n  exports.parse = parse;\n  function createURL(path, {\n    queryParams = {}\n  } = {}) {\n    if (typeof window === 'undefined') return '';\n    const url = new URL(path, window.location.origin);\n    Object.entries(queryParams).forEach(([key, value]) => {\n      if (typeof value === 'string') {\n        url.searchParams.set(key, encodeURIComponent(value));\n      } else if (value != null) {\n        url.searchParams.set(key,\n        // @ts-expect-error: browser supports using array\n        value);\n      }\n    });\n    return url.toString().replace(/\\/$/, '');\n  }\n  function parse(url) {\n    let parsed;\n    try {\n      parsed = new URL(url);\n    } catch {\n      if (typeof window === 'undefined') {\n        return {\n          hostname: null,\n          path: url,\n          queryParams: {},\n          scheme: null\n        };\n      }\n      return {\n        hostname: 'localhost',\n        path: url,\n        queryParams: {},\n        scheme: 'http'\n      };\n    }\n    const queryParams = {};\n    parsed.searchParams.forEach((value, key) => {\n      queryParams[key] = decodeURIComponent(value);\n    });\n    return {\n      hostname: parsed.hostname || null,\n      // TODO: We should probably update native to follow the default URL behavior closer.\n      path: !parsed.hostname && !parsed.pathname ? null : parsed.pathname === '' ? null : parsed.pathname.replace(/^\\//, ''),\n      queryParams,\n      scheme: parsed.protocol.replace(/:$/, '')\n    };\n  }\n});", "lineCount": 55, "map": [[7, 2, 1, 7], [7, 11, 1, 16, "createURL"], [7, 20, 1, 25, "createURL"], [7, 21, 1, 26, "path"], [7, 25, 1, 30], [7, 27, 1, 32], [8, 4, 1, 34, "queryParams"], [8, 15, 1, 45], [8, 18, 1, 48], [8, 19, 1, 49], [9, 2, 1, 51], [9, 3, 1, 52], [9, 6, 1, 55], [9, 7, 1, 56], [9, 8, 1, 57], [9, 10, 1, 59], [10, 4, 2, 4], [10, 8, 2, 8], [10, 15, 2, 15, "window"], [10, 21, 2, 21], [10, 26, 2, 26], [10, 37, 2, 37], [10, 39, 3, 8], [10, 46, 3, 15], [10, 48, 3, 17], [11, 4, 4, 4], [11, 10, 4, 10, "url"], [11, 13, 4, 13], [11, 16, 4, 16], [11, 20, 4, 20, "URL"], [11, 23, 4, 23], [11, 24, 4, 24, "path"], [11, 28, 4, 28], [11, 30, 4, 30, "window"], [11, 36, 4, 36], [11, 37, 4, 37, "location"], [11, 45, 4, 45], [11, 46, 4, 46, "origin"], [11, 52, 4, 52], [11, 53, 4, 53], [12, 4, 5, 4, "Object"], [12, 10, 5, 10], [12, 11, 5, 11, "entries"], [12, 18, 5, 18], [12, 19, 5, 19, "queryParams"], [12, 30, 5, 30], [12, 31, 5, 31], [12, 32, 5, 32, "for<PERSON>ach"], [12, 39, 5, 39], [12, 40, 5, 40], [12, 41, 5, 41], [12, 42, 5, 42, "key"], [12, 45, 5, 45], [12, 47, 5, 47, "value"], [12, 52, 5, 52], [12, 53, 5, 53], [12, 58, 5, 58], [13, 6, 6, 8], [13, 10, 6, 12], [13, 17, 6, 19, "value"], [13, 22, 6, 24], [13, 27, 6, 29], [13, 35, 6, 37], [13, 37, 6, 39], [14, 8, 7, 12, "url"], [14, 11, 7, 15], [14, 12, 7, 16, "searchParams"], [14, 24, 7, 28], [14, 25, 7, 29, "set"], [14, 28, 7, 32], [14, 29, 7, 33, "key"], [14, 32, 7, 36], [14, 34, 7, 38, "encodeURIComponent"], [14, 52, 7, 56], [14, 53, 7, 57, "value"], [14, 58, 7, 62], [14, 59, 7, 63], [14, 60, 7, 64], [15, 6, 8, 8], [15, 7, 8, 9], [15, 13, 9, 13], [15, 17, 9, 17, "value"], [15, 22, 9, 22], [15, 26, 9, 26], [15, 30, 9, 30], [15, 32, 9, 32], [16, 8, 10, 12, "url"], [16, 11, 10, 15], [16, 12, 10, 16, "searchParams"], [16, 24, 10, 28], [16, 25, 10, 29, "set"], [16, 28, 10, 32], [16, 29, 10, 33, "key"], [16, 32, 10, 36], [17, 8, 11, 12], [18, 8, 12, 12, "value"], [18, 13, 12, 17], [18, 14, 12, 18], [19, 6, 13, 8], [20, 4, 14, 4], [20, 5, 14, 5], [20, 6, 14, 6], [21, 4, 15, 4], [21, 11, 15, 11, "url"], [21, 14, 15, 14], [21, 15, 15, 15, "toString"], [21, 23, 15, 23], [21, 24, 15, 24], [21, 25, 15, 25], [21, 26, 15, 26, "replace"], [21, 33, 15, 33], [21, 34, 15, 34], [21, 39, 15, 39], [21, 41, 15, 41], [21, 43, 15, 43], [21, 44, 15, 44], [22, 2, 16, 0], [23, 2, 17, 7], [23, 11, 17, 16, "parse"], [23, 16, 17, 21, "parse"], [23, 17, 17, 22, "url"], [23, 20, 17, 25], [23, 22, 17, 27], [24, 4, 18, 4], [24, 8, 18, 8, "parsed"], [24, 14, 18, 14], [25, 4, 19, 4], [25, 8, 19, 8], [26, 6, 20, 8, "parsed"], [26, 12, 20, 14], [26, 15, 20, 17], [26, 19, 20, 21, "URL"], [26, 22, 20, 24], [26, 23, 20, 25, "url"], [26, 26, 20, 28], [26, 27, 20, 29], [27, 4, 21, 4], [27, 5, 21, 5], [27, 6, 22, 4], [27, 12, 22, 10], [28, 6, 23, 8], [28, 10, 23, 12], [28, 17, 23, 19, "window"], [28, 23, 23, 25], [28, 28, 23, 30], [28, 39, 23, 41], [28, 41, 23, 43], [29, 8, 24, 12], [29, 15, 24, 19], [30, 10, 25, 16, "hostname"], [30, 18, 25, 24], [30, 20, 25, 26], [30, 24, 25, 30], [31, 10, 26, 16, "path"], [31, 14, 26, 20], [31, 16, 26, 22, "url"], [31, 19, 26, 25], [32, 10, 27, 16, "queryParams"], [32, 21, 27, 27], [32, 23, 27, 29], [32, 24, 27, 30], [32, 25, 27, 31], [33, 10, 28, 16, "scheme"], [33, 16, 28, 22], [33, 18, 28, 24], [34, 8, 29, 12], [34, 9, 29, 13], [35, 6, 30, 8], [36, 6, 31, 8], [36, 13, 31, 15], [37, 8, 32, 12, "hostname"], [37, 16, 32, 20], [37, 18, 32, 22], [37, 29, 32, 33], [38, 8, 33, 12, "path"], [38, 12, 33, 16], [38, 14, 33, 18, "url"], [38, 17, 33, 21], [39, 8, 34, 12, "queryParams"], [39, 19, 34, 23], [39, 21, 34, 25], [39, 22, 34, 26], [39, 23, 34, 27], [40, 8, 35, 12, "scheme"], [40, 14, 35, 18], [40, 16, 35, 20], [41, 6, 36, 8], [41, 7, 36, 9], [42, 4, 37, 4], [43, 4, 38, 4], [43, 10, 38, 10, "queryParams"], [43, 21, 38, 21], [43, 24, 38, 24], [43, 25, 38, 25], [43, 26, 38, 26], [44, 4, 39, 4, "parsed"], [44, 10, 39, 10], [44, 11, 39, 11, "searchParams"], [44, 23, 39, 23], [44, 24, 39, 24, "for<PERSON>ach"], [44, 31, 39, 31], [44, 32, 39, 32], [44, 33, 39, 33, "value"], [44, 38, 39, 38], [44, 40, 39, 40, "key"], [44, 43, 39, 43], [44, 48, 39, 48], [45, 6, 40, 8, "queryParams"], [45, 17, 40, 19], [45, 18, 40, 20, "key"], [45, 21, 40, 23], [45, 22, 40, 24], [45, 25, 40, 27, "decodeURIComponent"], [45, 43, 40, 45], [45, 44, 40, 46, "value"], [45, 49, 40, 51], [45, 50, 40, 52], [46, 4, 41, 4], [46, 5, 41, 5], [46, 6, 41, 6], [47, 4, 42, 4], [47, 11, 42, 11], [48, 6, 43, 8, "hostname"], [48, 14, 43, 16], [48, 16, 43, 18, "parsed"], [48, 22, 43, 24], [48, 23, 43, 25, "hostname"], [48, 31, 43, 33], [48, 35, 43, 37], [48, 39, 43, 41], [49, 6, 44, 8], [50, 6, 45, 8, "path"], [50, 10, 45, 12], [50, 12, 45, 14], [50, 13, 45, 15, "parsed"], [50, 19, 45, 21], [50, 20, 45, 22, "hostname"], [50, 28, 45, 30], [50, 32, 45, 34], [50, 33, 45, 35, "parsed"], [50, 39, 45, 41], [50, 40, 45, 42, "pathname"], [50, 48, 45, 50], [50, 51, 46, 14], [50, 55, 46, 18], [50, 58, 47, 14, "parsed"], [50, 64, 47, 20], [50, 65, 47, 21, "pathname"], [50, 73, 47, 29], [50, 78, 47, 34], [50, 80, 47, 36], [50, 83, 48, 18], [50, 87, 48, 22], [50, 90, 49, 18, "parsed"], [50, 96, 49, 24], [50, 97, 49, 25, "pathname"], [50, 105, 49, 33], [50, 106, 49, 34, "replace"], [50, 113, 49, 41], [50, 114, 49, 42], [50, 119, 49, 47], [50, 121, 49, 49], [50, 123, 49, 51], [50, 124, 49, 52], [51, 6, 50, 8, "queryParams"], [51, 17, 50, 19], [52, 6, 51, 8, "scheme"], [52, 12, 51, 14], [52, 14, 51, 16, "parsed"], [52, 20, 51, 22], [52, 21, 51, 23, "protocol"], [52, 29, 51, 31], [52, 30, 51, 32, "replace"], [52, 37, 51, 39], [52, 38, 51, 40], [52, 42, 51, 44], [52, 44, 51, 46], [52, 46, 51, 48], [53, 4, 52, 4], [53, 5, 52, 5], [54, 2, 53, 0], [55, 0, 53, 1], [55, 3]], "functionMap": {"names": ["<global>", "createURL", "Object.entries.forEach$argument_0", "parse", "parsed.searchParams.forEach$argument_0"], "mappings": "AAA,OC;wCCI;KDS;CDE;OGC;gCCsB;KDE;CHY"}}, "type": "js/module"}]}