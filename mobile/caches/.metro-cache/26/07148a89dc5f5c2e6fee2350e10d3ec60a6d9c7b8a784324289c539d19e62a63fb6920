{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.needsToReattach = needsToReattach;\n  // Checks whether the gesture should be reattached to the view, this will happen when:\n  // - The number of gestures in the preparedGesture is different than the number of gestures in the gesture\n  // - The handlerName is different in any of the gestures\n  // - At least one of the gestures changed the thread it runs on\n  function needsToReattach(preparedGesture, newGestures) {\n    if (newGestures.length !== preparedGesture.attachedGestures.length) {\n      return true;\n    }\n    for (let i = 0; i < newGestures.length; i++) {\n      if (newGestures[i].handlerName !== preparedGesture.attachedGestures[i].handlerName || newGestures[i].shouldUseReanimated !== preparedGesture.attachedGestures[i].shouldUseReanimated) {\n        return true;\n      }\n    }\n    return false;\n  }\n});", "lineCount": 21, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 2, 3, 0], [9, 2, 4, 0], [10, 2, 5, 7], [10, 11, 5, 16, "needsToReattach"], [10, 26, 5, 31, "needsToReattach"], [10, 27, 5, 32, "preparedGesture"], [10, 42, 5, 47], [10, 44, 5, 49, "newGestures"], [10, 55, 5, 60], [10, 57, 5, 62], [11, 4, 6, 2], [11, 8, 6, 6, "newGestures"], [11, 19, 6, 17], [11, 20, 6, 18, "length"], [11, 26, 6, 24], [11, 31, 6, 29, "preparedGesture"], [11, 46, 6, 44], [11, 47, 6, 45, "attachedGestures"], [11, 63, 6, 61], [11, 64, 6, 62, "length"], [11, 70, 6, 68], [11, 72, 6, 70], [12, 6, 7, 4], [12, 13, 7, 11], [12, 17, 7, 15], [13, 4, 8, 2], [14, 4, 10, 2], [14, 9, 10, 7], [14, 13, 10, 11, "i"], [14, 14, 10, 12], [14, 17, 10, 15], [14, 18, 10, 16], [14, 20, 10, 18, "i"], [14, 21, 10, 19], [14, 24, 10, 22, "newGestures"], [14, 35, 10, 33], [14, 36, 10, 34, "length"], [14, 42, 10, 40], [14, 44, 10, 42, "i"], [14, 45, 10, 43], [14, 47, 10, 45], [14, 49, 10, 47], [15, 6, 11, 4], [15, 10, 11, 8, "newGestures"], [15, 21, 11, 19], [15, 22, 11, 20, "i"], [15, 23, 11, 21], [15, 24, 11, 22], [15, 25, 11, 23, "handler<PERSON>ame"], [15, 36, 11, 34], [15, 41, 11, 39, "preparedGesture"], [15, 56, 11, 54], [15, 57, 11, 55, "attachedGestures"], [15, 73, 11, 71], [15, 74, 11, 72, "i"], [15, 75, 11, 73], [15, 76, 11, 74], [15, 77, 11, 75, "handler<PERSON>ame"], [15, 88, 11, 86], [15, 92, 11, 90, "newGestures"], [15, 103, 11, 101], [15, 104, 11, 102, "i"], [15, 105, 11, 103], [15, 106, 11, 104], [15, 107, 11, 105, "shouldUseReanimated"], [15, 126, 11, 124], [15, 131, 11, 129, "preparedGesture"], [15, 146, 11, 144], [15, 147, 11, 145, "attachedGestures"], [15, 163, 11, 161], [15, 164, 11, 162, "i"], [15, 165, 11, 163], [15, 166, 11, 164], [15, 167, 11, 165, "shouldUseReanimated"], [15, 186, 11, 184], [15, 188, 11, 186], [16, 8, 12, 6], [16, 15, 12, 13], [16, 19, 12, 17], [17, 6, 13, 4], [18, 4, 14, 2], [19, 4, 16, 2], [19, 11, 16, 9], [19, 16, 16, 14], [20, 2, 17, 0], [21, 0, 17, 1], [21, 3]], "functionMap": {"names": ["<global>", "needsToReattach"], "mappings": "AAA;OCI;CDY"}}, "type": "js/module"}]}