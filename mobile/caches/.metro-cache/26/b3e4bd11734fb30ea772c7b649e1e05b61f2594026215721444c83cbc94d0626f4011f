{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../Core/Devtools/openFileInEditor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 68}}], "key": "3vH1y5vrRpr8TQTBUBKQt4U0X5I=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorSection", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 62}}], "key": "psfwCNco8+nKb+3u3V4A+OhHW2E=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorSourceMapStatus", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 78}}], "key": "8vwWm4m1IX/DlbOBlKQxLJRMibc=", "exportNames": ["*"]}}, {"name": "./LogBoxInspectorStackFrame", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 68}}], "key": "SkZLu5MzYdhQB4fazEvDhLWt4B8=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  exports.getCollapseMessage = getCollapseMessage;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _openFileInEditor = _interopRequireDefault(require(_dependencyMap[3], \"../../Core/Devtools/openFileInEditor\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[5], \"../../Text/Text\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[6], \"./LogBoxButton\"));\n  var _LogBoxInspectorSection = _interopRequireDefault(require(_dependencyMap[7], \"./LogBoxInspectorSection\"));\n  var _LogBoxInspectorSourceMapStatus = _interopRequireDefault(require(_dependencyMap[8], \"./LogBoxInspectorSourceMapStatus\"));\n  var _LogBoxInspectorStackFrame = _interopRequireDefault(require(_dependencyMap[9], \"./LogBoxInspectorStackFrame\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[10], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[11], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[12], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorStackFrames.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function getCollapseMessage(stackFrames, collapsed) {\n    if (stackFrames.length === 0) {\n      return 'No frames to show';\n    }\n    var collapsedCount = stackFrames.reduce((count, _ref) => {\n      var collapse = _ref.collapse;\n      if (collapse === true) {\n        return count + 1;\n      }\n      return count;\n    }, 0);\n    if (collapsedCount === 0) {\n      return 'Showing all frames';\n    }\n    var framePlural = `frame${collapsedCount > 1 ? 's' : ''}`;\n    if (collapsedCount === stackFrames.length) {\n      return collapsed ? `See${collapsedCount > 1 ? ' all ' : ' '}${collapsedCount} collapsed ${framePlural}` : `Collapse${collapsedCount > 1 ? ' all ' : ' '}${collapsedCount} ${framePlural}`;\n    } else {\n      return collapsed ? `See ${collapsedCount} more ${framePlural}` : `Collapse ${collapsedCount} ${framePlural}`;\n    }\n  }\n  function LogBoxInspectorStackFrames(props) {\n    var _React$useState = React.useState(() => {\n        return props.log.getAvailableStack().some(_ref2 => {\n          var collapse = _ref2.collapse;\n          return !collapse;\n        });\n      }),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      collapsed = _React$useState2[0],\n      setCollapsed = _React$useState2[1];\n    function getStackList() {\n      if (collapsed === true) {\n        return props.log.getAvailableStack().filter(_ref3 => {\n          var collapse = _ref3.collapse;\n          return !collapse;\n        });\n      } else {\n        return props.log.getAvailableStack();\n      }\n    }\n    if (props.log.getAvailableStack().length === 0) {\n      return null;\n    }\n    return (0, _jsxRuntime.jsxs)(_LogBoxInspectorSection.default, {\n      heading: \"Call Stack\",\n      action: (0, _jsxRuntime.jsx)(_LogBoxInspectorSourceMapStatus.default, {\n        onPress: props.log.symbolicated.status === 'FAILED' ? props.onRetry : null,\n        status: props.log.symbolicated.status\n      }),\n      children: [props.log.symbolicated.status !== 'COMPLETE' && (0, _jsxRuntime.jsx)(_View.default, {\n        style: stackStyles.hintBox,\n        children: (0, _jsxRuntime.jsx)(_Text.default, {\n          style: stackStyles.hintText,\n          children: \"This call stack is not symbolicated. Some features are unavailable such as viewing the function name or tapping to open files.\"\n        })\n      }), (0, _jsxRuntime.jsx)(StackFrameList, {\n        list: getStackList(),\n        status: props.log.symbolicated.status\n      }), (0, _jsxRuntime.jsx)(StackFrameFooter, {\n        onPress: () => setCollapsed(!collapsed),\n        message: getCollapseMessage(props.log.getAvailableStack(), collapsed)\n      })]\n    });\n  }\n  function StackFrameList(props) {\n    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {\n      children: props.list.map((frame, index) => {\n        var file = frame.file,\n          lineNumber = frame.lineNumber;\n        return (0, _jsxRuntime.jsx)(_LogBoxInspectorStackFrame.default, {\n          frame: frame,\n          onPress: props.status === 'COMPLETE' && file != null && lineNumber != null ? () => (0, _openFileInEditor.default)(file, lineNumber) : null\n        }, index);\n      })\n    });\n  }\n  function StackFrameFooter(props) {\n    return (0, _jsxRuntime.jsx)(_View.default, {\n      style: stackStyles.collapseContainer,\n      children: (0, _jsxRuntime.jsx)(_LogBoxButton.default, {\n        backgroundColor: {\n          default: 'transparent',\n          pressed: LogBoxStyle.getBackgroundColor(1)\n        },\n        onPress: props.onPress,\n        style: stackStyles.collapseButton,\n        children: (0, _jsxRuntime.jsx)(_Text.default, {\n          style: stackStyles.collapse,\n          children: props.message\n        })\n      })\n    });\n  }\n  var stackStyles = _StyleSheet.default.create({\n    section: {\n      marginTop: 15\n    },\n    heading: {\n      alignItems: 'center',\n      flexDirection: 'row',\n      paddingHorizontal: 12,\n      marginBottom: 10\n    },\n    headingText: {\n      color: LogBoxStyle.getTextColor(1),\n      flex: 1,\n      fontSize: 20,\n      fontWeight: '600',\n      includeFontPadding: false,\n      lineHeight: 20\n    },\n    body: {\n      paddingBottom: 10\n    },\n    bodyText: {\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 14,\n      includeFontPadding: false,\n      lineHeight: 18,\n      fontWeight: '500',\n      paddingHorizontal: 27\n    },\n    hintText: {\n      color: LogBoxStyle.getTextColor(0.7),\n      fontSize: 13,\n      includeFontPadding: false,\n      lineHeight: 18,\n      fontWeight: '400',\n      marginHorizontal: 10\n    },\n    hintBox: {\n      backgroundColor: LogBoxStyle.getBackgroundColor(),\n      marginHorizontal: 10,\n      paddingHorizontal: 5,\n      paddingVertical: 10,\n      borderRadius: 5,\n      marginBottom: 5\n    },\n    collapseContainer: {\n      marginLeft: 15,\n      flexDirection: 'row'\n    },\n    collapseButton: {\n      borderRadius: 5\n    },\n    collapse: {\n      color: LogBoxStyle.getTextColor(0.7),\n      fontSize: 12,\n      fontWeight: '300',\n      lineHeight: 20,\n      marginTop: 0,\n      paddingHorizontal: 10,\n      paddingVertical: 5\n    }\n  });\n  var _default = exports.default = LogBoxInspectorStackFrames;\n});", "lineCount": 179, "map": [[9, 2, 15, 0], [9, 6, 15, 0, "_View"], [9, 11, 15, 0], [9, 14, 15, 0, "_interopRequireDefault"], [9, 36, 15, 0], [9, 37, 15, 0, "require"], [9, 44, 15, 0], [9, 45, 15, 0, "_dependencyMap"], [9, 59, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_openFileInEditor"], [10, 23, 16, 0], [10, 26, 16, 0, "_interopRequireDefault"], [10, 48, 16, 0], [10, 49, 16, 0, "require"], [10, 56, 16, 0], [10, 57, 16, 0, "_dependencyMap"], [10, 71, 16, 0], [11, 2, 17, 0], [11, 6, 17, 0, "_StyleSheet"], [11, 17, 17, 0], [11, 20, 17, 0, "_interopRequireDefault"], [11, 42, 17, 0], [11, 43, 17, 0, "require"], [11, 50, 17, 0], [11, 51, 17, 0, "_dependencyMap"], [11, 65, 17, 0], [12, 2, 18, 0], [12, 6, 18, 0, "_Text"], [12, 11, 18, 0], [12, 14, 18, 0, "_interopRequireDefault"], [12, 36, 18, 0], [12, 37, 18, 0, "require"], [12, 44, 18, 0], [12, 45, 18, 0, "_dependencyMap"], [12, 59, 18, 0], [13, 2, 19, 0], [13, 6, 19, 0, "_LogBoxButton"], [13, 19, 19, 0], [13, 22, 19, 0, "_interopRequireDefault"], [13, 44, 19, 0], [13, 45, 19, 0, "require"], [13, 52, 19, 0], [13, 53, 19, 0, "_dependencyMap"], [13, 67, 19, 0], [14, 2, 20, 0], [14, 6, 20, 0, "_LogBoxInspectorSection"], [14, 29, 20, 0], [14, 32, 20, 0, "_interopRequireDefault"], [14, 54, 20, 0], [14, 55, 20, 0, "require"], [14, 62, 20, 0], [14, 63, 20, 0, "_dependencyMap"], [14, 77, 20, 0], [15, 2, 21, 0], [15, 6, 21, 0, "_LogBoxInspectorSourceMapStatus"], [15, 37, 21, 0], [15, 40, 21, 0, "_interopRequireDefault"], [15, 62, 21, 0], [15, 63, 21, 0, "require"], [15, 70, 21, 0], [15, 71, 21, 0, "_dependencyMap"], [15, 85, 21, 0], [16, 2, 22, 0], [16, 6, 22, 0, "_LogBoxInspectorStackFrame"], [16, 32, 22, 0], [16, 35, 22, 0, "_interopRequireDefault"], [16, 57, 22, 0], [16, 58, 22, 0, "require"], [16, 65, 22, 0], [16, 66, 22, 0, "_dependencyMap"], [16, 80, 22, 0], [17, 2, 23, 0], [17, 6, 23, 0, "LogBoxStyle"], [17, 17, 23, 0], [17, 20, 23, 0, "_interopRequireWildcard"], [17, 43, 23, 0], [17, 44, 23, 0, "require"], [17, 51, 23, 0], [17, 52, 23, 0, "_dependencyMap"], [17, 66, 23, 0], [18, 2, 24, 0], [18, 6, 24, 0, "React"], [18, 11, 24, 0], [18, 14, 24, 0, "_interopRequireWildcard"], [18, 37, 24, 0], [18, 38, 24, 0, "require"], [18, 45, 24, 0], [18, 46, 24, 0, "_dependencyMap"], [18, 60, 24, 0], [19, 2, 24, 31], [19, 6, 24, 31, "_jsxRuntime"], [19, 17, 24, 31], [19, 20, 24, 31, "require"], [19, 27, 24, 31], [19, 28, 24, 31, "_dependencyMap"], [19, 42, 24, 31], [20, 2, 24, 31], [20, 6, 24, 31, "_jsxFileName"], [20, 18, 24, 31], [21, 2, 24, 31], [21, 11, 24, 31, "_interopRequireWildcard"], [21, 35, 24, 31, "e"], [21, 36, 24, 31], [21, 38, 24, 31, "t"], [21, 39, 24, 31], [21, 68, 24, 31, "WeakMap"], [21, 75, 24, 31], [21, 81, 24, 31, "r"], [21, 82, 24, 31], [21, 89, 24, 31, "WeakMap"], [21, 96, 24, 31], [21, 100, 24, 31, "n"], [21, 101, 24, 31], [21, 108, 24, 31, "WeakMap"], [21, 115, 24, 31], [21, 127, 24, 31, "_interopRequireWildcard"], [21, 150, 24, 31], [21, 162, 24, 31, "_interopRequireWildcard"], [21, 163, 24, 31, "e"], [21, 164, 24, 31], [21, 166, 24, 31, "t"], [21, 167, 24, 31], [21, 176, 24, 31, "t"], [21, 177, 24, 31], [21, 181, 24, 31, "e"], [21, 182, 24, 31], [21, 186, 24, 31, "e"], [21, 187, 24, 31], [21, 188, 24, 31, "__esModule"], [21, 198, 24, 31], [21, 207, 24, 31, "e"], [21, 208, 24, 31], [21, 214, 24, 31, "o"], [21, 215, 24, 31], [21, 217, 24, 31, "i"], [21, 218, 24, 31], [21, 220, 24, 31, "f"], [21, 221, 24, 31], [21, 226, 24, 31, "__proto__"], [21, 235, 24, 31], [21, 243, 24, 31, "default"], [21, 250, 24, 31], [21, 252, 24, 31, "e"], [21, 253, 24, 31], [21, 270, 24, 31, "e"], [21, 271, 24, 31], [21, 294, 24, 31, "e"], [21, 295, 24, 31], [21, 320, 24, 31, "e"], [21, 321, 24, 31], [21, 330, 24, 31, "f"], [21, 331, 24, 31], [21, 337, 24, 31, "o"], [21, 338, 24, 31], [21, 341, 24, 31, "t"], [21, 342, 24, 31], [21, 345, 24, 31, "n"], [21, 346, 24, 31], [21, 349, 24, 31, "r"], [21, 350, 24, 31], [21, 358, 24, 31, "o"], [21, 359, 24, 31], [21, 360, 24, 31, "has"], [21, 363, 24, 31], [21, 364, 24, 31, "e"], [21, 365, 24, 31], [21, 375, 24, 31, "o"], [21, 376, 24, 31], [21, 377, 24, 31, "get"], [21, 380, 24, 31], [21, 381, 24, 31, "e"], [21, 382, 24, 31], [21, 385, 24, 31, "o"], [21, 386, 24, 31], [21, 387, 24, 31, "set"], [21, 390, 24, 31], [21, 391, 24, 31, "e"], [21, 392, 24, 31], [21, 394, 24, 31, "f"], [21, 395, 24, 31], [21, 409, 24, 31, "_t"], [21, 411, 24, 31], [21, 415, 24, 31, "e"], [21, 416, 24, 31], [21, 432, 24, 31, "_t"], [21, 434, 24, 31], [21, 441, 24, 31, "hasOwnProperty"], [21, 455, 24, 31], [21, 456, 24, 31, "call"], [21, 460, 24, 31], [21, 461, 24, 31, "e"], [21, 462, 24, 31], [21, 464, 24, 31, "_t"], [21, 466, 24, 31], [21, 473, 24, 31, "i"], [21, 474, 24, 31], [21, 478, 24, 31, "o"], [21, 479, 24, 31], [21, 482, 24, 31, "Object"], [21, 488, 24, 31], [21, 489, 24, 31, "defineProperty"], [21, 503, 24, 31], [21, 508, 24, 31, "Object"], [21, 514, 24, 31], [21, 515, 24, 31, "getOwnPropertyDescriptor"], [21, 539, 24, 31], [21, 540, 24, 31, "e"], [21, 541, 24, 31], [21, 543, 24, 31, "_t"], [21, 545, 24, 31], [21, 552, 24, 31, "i"], [21, 553, 24, 31], [21, 554, 24, 31, "get"], [21, 557, 24, 31], [21, 561, 24, 31, "i"], [21, 562, 24, 31], [21, 563, 24, 31, "set"], [21, 566, 24, 31], [21, 570, 24, 31, "o"], [21, 571, 24, 31], [21, 572, 24, 31, "f"], [21, 573, 24, 31], [21, 575, 24, 31, "_t"], [21, 577, 24, 31], [21, 579, 24, 31, "i"], [21, 580, 24, 31], [21, 584, 24, 31, "f"], [21, 585, 24, 31], [21, 586, 24, 31, "_t"], [21, 588, 24, 31], [21, 592, 24, 31, "e"], [21, 593, 24, 31], [21, 594, 24, 31, "_t"], [21, 596, 24, 31], [21, 607, 24, 31, "f"], [21, 608, 24, 31], [21, 613, 24, 31, "e"], [21, 614, 24, 31], [21, 616, 24, 31, "t"], [21, 617, 24, 31], [22, 2, 31, 7], [22, 11, 31, 16, "getCollapseMessage"], [22, 29, 31, 34, "getCollapseMessage"], [22, 30, 32, 2, "stackFrames"], [22, 41, 32, 20], [22, 43, 33, 2, "collapsed"], [22, 52, 33, 20], [22, 54, 34, 10], [23, 4, 35, 2], [23, 8, 35, 6, "stackFrames"], [23, 19, 35, 17], [23, 20, 35, 18, "length"], [23, 26, 35, 24], [23, 31, 35, 29], [23, 32, 35, 30], [23, 34, 35, 32], [24, 6, 36, 4], [24, 13, 36, 11], [24, 32, 36, 30], [25, 4, 37, 2], [26, 4, 39, 2], [26, 8, 39, 8, "collapsedCount"], [26, 22, 39, 22], [26, 25, 39, 25, "stackFrames"], [26, 36, 39, 36], [26, 37, 39, 37, "reduce"], [26, 43, 39, 43], [26, 44, 39, 44], [26, 45, 39, 45, "count"], [26, 50, 39, 50], [26, 52, 39, 50, "_ref"], [26, 56, 39, 50], [26, 61, 39, 67], [27, 6, 39, 67], [27, 10, 39, 53, "collapse"], [27, 18, 39, 61], [27, 21, 39, 61, "_ref"], [27, 25, 39, 61], [27, 26, 39, 53, "collapse"], [27, 34, 39, 61], [28, 6, 40, 4], [28, 10, 40, 8, "collapse"], [28, 18, 40, 16], [28, 23, 40, 21], [28, 27, 40, 25], [28, 29, 40, 27], [29, 8, 41, 6], [29, 15, 41, 13, "count"], [29, 20, 41, 18], [29, 23, 41, 21], [29, 24, 41, 22], [30, 6, 42, 4], [31, 6, 44, 4], [31, 13, 44, 11, "count"], [31, 18, 44, 16], [32, 4, 45, 2], [32, 5, 45, 3], [32, 7, 45, 5], [32, 8, 45, 6], [32, 9, 45, 7], [33, 4, 47, 2], [33, 8, 47, 6, "collapsedCount"], [33, 22, 47, 20], [33, 27, 47, 25], [33, 28, 47, 26], [33, 30, 47, 28], [34, 6, 48, 4], [34, 13, 48, 11], [34, 33, 48, 31], [35, 4, 49, 2], [36, 4, 51, 2], [36, 8, 51, 8, "framePlural"], [36, 19, 51, 19], [36, 22, 51, 22], [36, 30, 51, 30, "collapsedCount"], [36, 44, 51, 44], [36, 47, 51, 47], [36, 48, 51, 48], [36, 51, 51, 51], [36, 54, 51, 54], [36, 57, 51, 57], [36, 59, 51, 59], [36, 61, 51, 61], [37, 4, 52, 2], [37, 8, 52, 6, "collapsedCount"], [37, 22, 52, 20], [37, 27, 52, 25, "stackFrames"], [37, 38, 52, 36], [37, 39, 52, 37, "length"], [37, 45, 52, 43], [37, 47, 52, 45], [38, 6, 53, 4], [38, 13, 53, 11, "collapsed"], [38, 22, 53, 20], [38, 25, 54, 8], [38, 31, 55, 10, "collapsedCount"], [38, 45, 55, 24], [38, 48, 55, 27], [38, 49, 55, 28], [38, 52, 55, 31], [38, 59, 55, 38], [38, 62, 55, 41], [38, 65, 55, 44], [38, 68, 56, 11, "collapsedCount"], [38, 82, 56, 25], [38, 96, 56, 39, "framePlural"], [38, 107, 56, 50], [38, 109, 56, 52], [38, 112, 57, 8], [38, 123, 58, 10, "collapsedCount"], [38, 137, 58, 24], [38, 140, 58, 27], [38, 141, 58, 28], [38, 144, 58, 31], [38, 151, 58, 38], [38, 154, 58, 41], [38, 157, 58, 44], [38, 160, 59, 11, "collapsedCount"], [38, 174, 59, 25], [38, 178, 59, 29, "framePlural"], [38, 189, 59, 40], [38, 191, 59, 42], [39, 4, 60, 2], [39, 5, 60, 3], [39, 11, 60, 9], [40, 6, 61, 4], [40, 13, 61, 11, "collapsed"], [40, 22, 61, 20], [40, 25, 62, 8], [40, 32, 62, 15, "collapsedCount"], [40, 46, 62, 29], [40, 55, 62, 38, "framePlural"], [40, 66, 62, 49], [40, 68, 62, 51], [40, 71, 63, 8], [40, 83, 63, 20, "collapsedCount"], [40, 97, 63, 34], [40, 101, 63, 38, "framePlural"], [40, 112, 63, 49], [40, 114, 63, 51], [41, 4, 64, 2], [42, 2, 65, 0], [43, 2, 67, 0], [43, 11, 67, 9, "LogBoxInspectorStackFrames"], [43, 37, 67, 35, "LogBoxInspectorStackFrames"], [43, 38, 67, 36, "props"], [43, 43, 67, 48], [43, 45, 67, 62], [44, 4, 68, 2], [44, 8, 68, 2, "_React$useState"], [44, 23, 68, 2], [44, 26, 68, 36, "React"], [44, 31, 68, 41], [44, 32, 68, 42, "useState"], [44, 40, 68, 50], [44, 41, 68, 51], [44, 47, 68, 57], [45, 8, 70, 4], [45, 15, 70, 11, "props"], [45, 20, 70, 16], [45, 21, 70, 17, "log"], [45, 24, 70, 20], [45, 25, 70, 21, "getAvailableStack"], [45, 42, 70, 38], [45, 43, 70, 39], [45, 44, 70, 40], [45, 45, 70, 41, "some"], [45, 49, 70, 45], [45, 50, 70, 46, "_ref2"], [45, 55, 70, 46], [46, 10, 70, 46], [46, 14, 70, 48, "collapse"], [46, 22, 70, 56], [46, 25, 70, 56, "_ref2"], [46, 30, 70, 56], [46, 31, 70, 48, "collapse"], [46, 39, 70, 56], [47, 10, 70, 56], [47, 17, 70, 62], [47, 18, 70, 63, "collapse"], [47, 26, 70, 71], [48, 8, 70, 71], [48, 10, 70, 72], [49, 6, 71, 2], [49, 7, 71, 3], [49, 8, 71, 4], [50, 6, 71, 4, "_React$useState2"], [50, 22, 71, 4], [50, 29, 71, 4, "_slicedToArray2"], [50, 44, 71, 4], [50, 45, 71, 4, "default"], [50, 52, 71, 4], [50, 54, 71, 4, "_React$useState"], [50, 69, 71, 4], [51, 6, 68, 9, "collapsed"], [51, 15, 68, 18], [51, 18, 68, 18, "_React$useState2"], [51, 34, 68, 18], [52, 6, 68, 20, "setCollapsed"], [52, 18, 68, 32], [52, 21, 68, 32, "_React$useState2"], [52, 37, 68, 32], [53, 4, 73, 2], [53, 13, 73, 11, "getStackList"], [53, 25, 73, 23, "getStackList"], [53, 26, 73, 23], [53, 28, 73, 26], [54, 6, 74, 4], [54, 10, 74, 8, "collapsed"], [54, 19, 74, 17], [54, 24, 74, 22], [54, 28, 74, 26], [54, 30, 74, 28], [55, 8, 75, 6], [55, 15, 75, 13, "props"], [55, 20, 75, 18], [55, 21, 75, 19, "log"], [55, 24, 75, 22], [55, 25, 75, 23, "getAvailableStack"], [55, 42, 75, 40], [55, 43, 75, 41], [55, 44, 75, 42], [55, 45, 75, 43, "filter"], [55, 51, 75, 49], [55, 52, 75, 50, "_ref3"], [55, 57, 75, 50], [56, 10, 75, 50], [56, 14, 75, 52, "collapse"], [56, 22, 75, 60], [56, 25, 75, 60, "_ref3"], [56, 30, 75, 60], [56, 31, 75, 52, "collapse"], [56, 39, 75, 60], [57, 10, 75, 60], [57, 17, 75, 66], [57, 18, 75, 67, "collapse"], [57, 26, 75, 75], [58, 8, 75, 75], [58, 10, 75, 76], [59, 6, 76, 4], [59, 7, 76, 5], [59, 13, 76, 11], [60, 8, 77, 6], [60, 15, 77, 13, "props"], [60, 20, 77, 18], [60, 21, 77, 19, "log"], [60, 24, 77, 22], [60, 25, 77, 23, "getAvailableStack"], [60, 42, 77, 40], [60, 43, 77, 41], [60, 44, 77, 42], [61, 6, 78, 4], [62, 4, 79, 2], [63, 4, 81, 2], [63, 8, 81, 6, "props"], [63, 13, 81, 11], [63, 14, 81, 12, "log"], [63, 17, 81, 15], [63, 18, 81, 16, "getAvailableStack"], [63, 35, 81, 33], [63, 36, 81, 34], [63, 37, 81, 35], [63, 38, 81, 36, "length"], [63, 44, 81, 42], [63, 49, 81, 47], [63, 50, 81, 48], [63, 52, 81, 50], [64, 6, 82, 4], [64, 13, 82, 11], [64, 17, 82, 15], [65, 4, 83, 2], [66, 4, 85, 2], [66, 11, 86, 4], [66, 15, 86, 4, "_jsxRuntime"], [66, 26, 86, 4], [66, 27, 86, 4, "jsxs"], [66, 31, 86, 4], [66, 33, 86, 5, "_LogBoxInspectorSection"], [66, 56, 86, 5], [66, 57, 86, 5, "default"], [66, 64, 86, 27], [67, 6, 87, 6, "heading"], [67, 13, 87, 13], [67, 15, 87, 14], [67, 27, 87, 26], [68, 6, 88, 6, "action"], [68, 12, 88, 12], [68, 14, 89, 8], [68, 18, 89, 8, "_jsxRuntime"], [68, 29, 89, 8], [68, 30, 89, 8, "jsx"], [68, 33, 89, 8], [68, 35, 89, 9, "_LogBoxInspectorSourceMapStatus"], [68, 66, 89, 9], [68, 67, 89, 9, "default"], [68, 74, 89, 39], [69, 8, 90, 10, "onPress"], [69, 15, 90, 17], [69, 17, 91, 12, "props"], [69, 22, 91, 17], [69, 23, 91, 18, "log"], [69, 26, 91, 21], [69, 27, 91, 22, "symbolicated"], [69, 39, 91, 34], [69, 40, 91, 35, "status"], [69, 46, 91, 41], [69, 51, 91, 46], [69, 59, 91, 54], [69, 62, 91, 57, "props"], [69, 67, 91, 62], [69, 68, 91, 63, "onRetry"], [69, 75, 91, 70], [69, 78, 91, 73], [69, 82, 92, 11], [70, 8, 93, 10, "status"], [70, 14, 93, 16], [70, 16, 93, 18, "props"], [70, 21, 93, 23], [70, 22, 93, 24, "log"], [70, 25, 93, 27], [70, 26, 93, 28, "symbolicated"], [70, 38, 93, 40], [70, 39, 93, 41, "status"], [71, 6, 93, 48], [71, 7, 94, 9], [71, 8, 95, 7], [72, 6, 95, 7, "children"], [72, 14, 95, 7], [72, 17, 96, 7, "props"], [72, 22, 96, 12], [72, 23, 96, 13, "log"], [72, 26, 96, 16], [72, 27, 96, 17, "symbolicated"], [72, 39, 96, 29], [72, 40, 96, 30, "status"], [72, 46, 96, 36], [72, 51, 96, 41], [72, 61, 96, 51], [72, 65, 97, 8], [72, 69, 97, 8, "_jsxRuntime"], [72, 80, 97, 8], [72, 81, 97, 8, "jsx"], [72, 84, 97, 8], [72, 86, 97, 9, "_View"], [72, 91, 97, 9], [72, 92, 97, 9, "default"], [72, 99, 97, 13], [73, 8, 97, 14, "style"], [73, 13, 97, 19], [73, 15, 97, 21, "stackStyles"], [73, 26, 97, 32], [73, 27, 97, 33, "hintBox"], [73, 34, 97, 41], [74, 8, 97, 41, "children"], [74, 16, 97, 41], [74, 18, 98, 10], [74, 22, 98, 10, "_jsxRuntime"], [74, 33, 98, 10], [74, 34, 98, 10, "jsx"], [74, 37, 98, 10], [74, 39, 98, 11, "_Text"], [74, 44, 98, 11], [74, 45, 98, 11, "default"], [74, 52, 98, 15], [75, 10, 98, 16, "style"], [75, 15, 98, 21], [75, 17, 98, 23, "stackStyles"], [75, 28, 98, 34], [75, 29, 98, 35, "hintText"], [75, 37, 98, 44], [76, 10, 98, 44, "children"], [76, 18, 98, 44], [76, 20, 98, 45], [77, 8, 101, 10], [77, 9, 101, 16], [78, 6, 101, 17], [78, 7, 102, 14], [78, 8, 103, 7], [78, 10, 104, 6], [78, 14, 104, 6, "_jsxRuntime"], [78, 25, 104, 6], [78, 26, 104, 6, "jsx"], [78, 29, 104, 6], [78, 31, 104, 7, "StackFrameList"], [78, 45, 104, 21], [79, 8, 105, 8, "list"], [79, 12, 105, 12], [79, 14, 105, 14, "getStackList"], [79, 26, 105, 26], [79, 27, 105, 27], [79, 28, 105, 29], [80, 8, 106, 8, "status"], [80, 14, 106, 14], [80, 16, 106, 16, "props"], [80, 21, 106, 21], [80, 22, 106, 22, "log"], [80, 25, 106, 25], [80, 26, 106, 26, "symbolicated"], [80, 38, 106, 38], [80, 39, 106, 39, "status"], [81, 6, 106, 46], [81, 7, 107, 7], [81, 8, 107, 8], [81, 10, 108, 6], [81, 14, 108, 6, "_jsxRuntime"], [81, 25, 108, 6], [81, 26, 108, 6, "jsx"], [81, 29, 108, 6], [81, 31, 108, 7, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [81, 47, 108, 23], [82, 8, 109, 8, "onPress"], [82, 15, 109, 15], [82, 17, 109, 17, "onPress"], [82, 18, 109, 17], [82, 23, 109, 23, "setCollapsed"], [82, 35, 109, 35], [82, 36, 109, 36], [82, 37, 109, 37, "collapsed"], [82, 46, 109, 46], [82, 47, 109, 48], [83, 8, 110, 8, "message"], [83, 15, 110, 15], [83, 17, 110, 17, "getCollapseMessage"], [83, 35, 110, 35], [83, 36, 110, 36, "props"], [83, 41, 110, 41], [83, 42, 110, 42, "log"], [83, 45, 110, 45], [83, 46, 110, 46, "getAvailableStack"], [83, 63, 110, 63], [83, 64, 110, 64], [83, 65, 110, 65], [83, 67, 110, 67, "collapsed"], [83, 76, 110, 76], [84, 6, 110, 78], [84, 7, 111, 7], [84, 8, 111, 8], [85, 4, 111, 8], [85, 5, 112, 28], [85, 6, 112, 29], [86, 2, 114, 0], [87, 2, 116, 0], [87, 11, 116, 9, "StackFrameList"], [87, 25, 116, 23, "StackFrameList"], [87, 26, 116, 24, "props"], [87, 31, 119, 1], [87, 33, 119, 3], [88, 4, 120, 2], [88, 11, 121, 4], [88, 15, 121, 4, "_jsxRuntime"], [88, 26, 121, 4], [88, 27, 121, 4, "jsx"], [88, 30, 121, 4], [88, 32, 121, 4, "_jsxRuntime"], [88, 43, 121, 4], [88, 44, 121, 4, "Fragment"], [88, 52, 121, 4], [89, 6, 121, 4, "children"], [89, 14, 121, 4], [89, 16, 122, 7, "props"], [89, 21, 122, 12], [89, 22, 122, 13, "list"], [89, 26, 122, 17], [89, 27, 122, 18, "map"], [89, 30, 122, 21], [89, 31, 122, 22], [89, 32, 122, 23, "frame"], [89, 37, 122, 28], [89, 39, 122, 30, "index"], [89, 44, 122, 35], [89, 49, 122, 40], [90, 8, 123, 8], [90, 12, 123, 15, "file"], [90, 16, 123, 19], [90, 19, 123, 35, "frame"], [90, 24, 123, 40], [90, 25, 123, 15, "file"], [90, 29, 123, 19], [91, 10, 123, 21, "lineNumber"], [91, 20, 123, 31], [91, 23, 123, 35, "frame"], [91, 28, 123, 40], [91, 29, 123, 21, "lineNumber"], [91, 39, 123, 31], [92, 8, 124, 8], [92, 15, 125, 10], [92, 19, 125, 10, "_jsxRuntime"], [92, 30, 125, 10], [92, 31, 125, 10, "jsx"], [92, 34, 125, 10], [92, 36, 125, 11, "_LogBoxInspectorStackFrame"], [92, 62, 125, 11], [92, 63, 125, 11, "default"], [92, 70, 125, 36], [93, 10, 127, 12, "frame"], [93, 15, 127, 17], [93, 17, 127, 19, "frame"], [93, 22, 127, 25], [94, 10, 128, 12, "onPress"], [94, 17, 128, 19], [94, 19, 129, 14, "props"], [94, 24, 129, 19], [94, 25, 129, 20, "status"], [94, 31, 129, 26], [94, 36, 129, 31], [94, 46, 129, 41], [94, 50, 129, 45, "file"], [94, 54, 129, 49], [94, 58, 129, 53], [94, 62, 129, 57], [94, 66, 129, 61, "lineNumber"], [94, 76, 129, 71], [94, 80, 129, 75], [94, 84, 129, 79], [94, 87, 130, 18], [94, 93, 130, 24], [94, 97, 130, 24, "openFileInEditor"], [94, 122, 130, 40], [94, 124, 130, 41, "file"], [94, 128, 130, 45], [94, 130, 130, 47, "lineNumber"], [94, 140, 130, 57], [94, 141, 130, 58], [94, 144, 131, 18], [95, 8, 132, 13], [95, 11, 126, 17, "index"], [95, 16, 133, 11], [95, 17, 133, 12], [96, 6, 135, 6], [96, 7, 135, 7], [97, 4, 135, 8], [97, 5, 136, 6], [97, 6, 136, 7], [98, 2, 138, 0], [99, 2, 140, 0], [99, 11, 140, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [99, 27, 140, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [99, 28, 141, 2, "props"], [99, 33, 141, 58], [99, 35, 142, 2], [100, 4, 143, 2], [100, 11, 144, 4], [100, 15, 144, 4, "_jsxRuntime"], [100, 26, 144, 4], [100, 27, 144, 4, "jsx"], [100, 30, 144, 4], [100, 32, 144, 5, "_View"], [100, 37, 144, 5], [100, 38, 144, 5, "default"], [100, 45, 144, 9], [101, 6, 144, 10, "style"], [101, 11, 144, 15], [101, 13, 144, 17, "stackStyles"], [101, 24, 144, 28], [101, 25, 144, 29, "collapseContainer"], [101, 42, 144, 47], [102, 6, 144, 47, "children"], [102, 14, 144, 47], [102, 16, 145, 6], [102, 20, 145, 6, "_jsxRuntime"], [102, 31, 145, 6], [102, 32, 145, 6, "jsx"], [102, 35, 145, 6], [102, 37, 145, 7, "_LogBoxButton"], [102, 50, 145, 7], [102, 51, 145, 7, "default"], [102, 58, 145, 19], [103, 8, 146, 8, "backgroundColor"], [103, 23, 146, 23], [103, 25, 146, 25], [104, 10, 147, 10, "default"], [104, 17, 147, 17], [104, 19, 147, 19], [104, 32, 147, 32], [105, 10, 148, 10, "pressed"], [105, 17, 148, 17], [105, 19, 148, 19, "LogBoxStyle"], [105, 30, 148, 30], [105, 31, 148, 31, "getBackgroundColor"], [105, 49, 148, 49], [105, 50, 148, 50], [105, 51, 148, 51], [106, 8, 149, 8], [106, 9, 149, 10], [107, 8, 150, 8, "onPress"], [107, 15, 150, 15], [107, 17, 150, 17, "props"], [107, 22, 150, 22], [107, 23, 150, 23, "onPress"], [107, 30, 150, 31], [108, 8, 151, 8, "style"], [108, 13, 151, 13], [108, 15, 151, 15, "stackStyles"], [108, 26, 151, 26], [108, 27, 151, 27, "collapseButton"], [108, 41, 151, 42], [109, 8, 151, 42, "children"], [109, 16, 151, 42], [109, 18, 152, 8], [109, 22, 152, 8, "_jsxRuntime"], [109, 33, 152, 8], [109, 34, 152, 8, "jsx"], [109, 37, 152, 8], [109, 39, 152, 9, "_Text"], [109, 44, 152, 9], [109, 45, 152, 9, "default"], [109, 52, 152, 13], [110, 10, 152, 14, "style"], [110, 15, 152, 19], [110, 17, 152, 21, "stackStyles"], [110, 28, 152, 32], [110, 29, 152, 33, "collapse"], [110, 37, 152, 42], [111, 10, 152, 42, "children"], [111, 18, 152, 42], [111, 20, 152, 44, "props"], [111, 25, 152, 49], [111, 26, 152, 50, "message"], [112, 8, 152, 57], [112, 9, 152, 64], [113, 6, 152, 65], [113, 7, 153, 20], [114, 4, 153, 21], [114, 5, 154, 10], [114, 6, 154, 11], [115, 2, 156, 0], [116, 2, 158, 0], [116, 6, 158, 6, "stackStyles"], [116, 17, 158, 17], [116, 20, 158, 20, "StyleSheet"], [116, 39, 158, 30], [116, 40, 158, 31, "create"], [116, 46, 158, 37], [116, 47, 158, 38], [117, 4, 159, 2, "section"], [117, 11, 159, 9], [117, 13, 159, 11], [118, 6, 160, 4, "marginTop"], [118, 15, 160, 13], [118, 17, 160, 15], [119, 4, 161, 2], [119, 5, 161, 3], [120, 4, 162, 2, "heading"], [120, 11, 162, 9], [120, 13, 162, 11], [121, 6, 163, 4, "alignItems"], [121, 16, 163, 14], [121, 18, 163, 16], [121, 26, 163, 24], [122, 6, 164, 4, "flexDirection"], [122, 19, 164, 17], [122, 21, 164, 19], [122, 26, 164, 24], [123, 6, 165, 4, "paddingHorizontal"], [123, 23, 165, 21], [123, 25, 165, 23], [123, 27, 165, 25], [124, 6, 166, 4, "marginBottom"], [124, 18, 166, 16], [124, 20, 166, 18], [125, 4, 167, 2], [125, 5, 167, 3], [126, 4, 168, 2, "headingText"], [126, 15, 168, 13], [126, 17, 168, 15], [127, 6, 169, 4, "color"], [127, 11, 169, 9], [127, 13, 169, 11, "LogBoxStyle"], [127, 24, 169, 22], [127, 25, 169, 23, "getTextColor"], [127, 37, 169, 35], [127, 38, 169, 36], [127, 39, 169, 37], [127, 40, 169, 38], [128, 6, 170, 4, "flex"], [128, 10, 170, 8], [128, 12, 170, 10], [128, 13, 170, 11], [129, 6, 171, 4, "fontSize"], [129, 14, 171, 12], [129, 16, 171, 14], [129, 18, 171, 16], [130, 6, 172, 4, "fontWeight"], [130, 16, 172, 14], [130, 18, 172, 16], [130, 23, 172, 21], [131, 6, 173, 4, "includeFontPadding"], [131, 24, 173, 22], [131, 26, 173, 24], [131, 31, 173, 29], [132, 6, 174, 4, "lineHeight"], [132, 16, 174, 14], [132, 18, 174, 16], [133, 4, 175, 2], [133, 5, 175, 3], [134, 4, 176, 2, "body"], [134, 8, 176, 6], [134, 10, 176, 8], [135, 6, 177, 4, "paddingBottom"], [135, 19, 177, 17], [135, 21, 177, 19], [136, 4, 178, 2], [136, 5, 178, 3], [137, 4, 179, 2, "bodyText"], [137, 12, 179, 10], [137, 14, 179, 12], [138, 6, 180, 4, "color"], [138, 11, 180, 9], [138, 13, 180, 11, "LogBoxStyle"], [138, 24, 180, 22], [138, 25, 180, 23, "getTextColor"], [138, 37, 180, 35], [138, 38, 180, 36], [138, 39, 180, 37], [138, 40, 180, 38], [139, 6, 181, 4, "fontSize"], [139, 14, 181, 12], [139, 16, 181, 14], [139, 18, 181, 16], [140, 6, 182, 4, "includeFontPadding"], [140, 24, 182, 22], [140, 26, 182, 24], [140, 31, 182, 29], [141, 6, 183, 4, "lineHeight"], [141, 16, 183, 14], [141, 18, 183, 16], [141, 20, 183, 18], [142, 6, 184, 4, "fontWeight"], [142, 16, 184, 14], [142, 18, 184, 16], [142, 23, 184, 21], [143, 6, 185, 4, "paddingHorizontal"], [143, 23, 185, 21], [143, 25, 185, 23], [144, 4, 186, 2], [144, 5, 186, 3], [145, 4, 187, 2, "hintText"], [145, 12, 187, 10], [145, 14, 187, 12], [146, 6, 188, 4, "color"], [146, 11, 188, 9], [146, 13, 188, 11, "LogBoxStyle"], [146, 24, 188, 22], [146, 25, 188, 23, "getTextColor"], [146, 37, 188, 35], [146, 38, 188, 36], [146, 41, 188, 39], [146, 42, 188, 40], [147, 6, 189, 4, "fontSize"], [147, 14, 189, 12], [147, 16, 189, 14], [147, 18, 189, 16], [148, 6, 190, 4, "includeFontPadding"], [148, 24, 190, 22], [148, 26, 190, 24], [148, 31, 190, 29], [149, 6, 191, 4, "lineHeight"], [149, 16, 191, 14], [149, 18, 191, 16], [149, 20, 191, 18], [150, 6, 192, 4, "fontWeight"], [150, 16, 192, 14], [150, 18, 192, 16], [150, 23, 192, 21], [151, 6, 193, 4, "marginHorizontal"], [151, 22, 193, 20], [151, 24, 193, 22], [152, 4, 194, 2], [152, 5, 194, 3], [153, 4, 195, 2, "hintBox"], [153, 11, 195, 9], [153, 13, 195, 11], [154, 6, 196, 4, "backgroundColor"], [154, 21, 196, 19], [154, 23, 196, 21, "LogBoxStyle"], [154, 34, 196, 32], [154, 35, 196, 33, "getBackgroundColor"], [154, 53, 196, 51], [154, 54, 196, 52], [154, 55, 196, 53], [155, 6, 197, 4, "marginHorizontal"], [155, 22, 197, 20], [155, 24, 197, 22], [155, 26, 197, 24], [156, 6, 198, 4, "paddingHorizontal"], [156, 23, 198, 21], [156, 25, 198, 23], [156, 26, 198, 24], [157, 6, 199, 4, "paddingVertical"], [157, 21, 199, 19], [157, 23, 199, 21], [157, 25, 199, 23], [158, 6, 200, 4, "borderRadius"], [158, 18, 200, 16], [158, 20, 200, 18], [158, 21, 200, 19], [159, 6, 201, 4, "marginBottom"], [159, 18, 201, 16], [159, 20, 201, 18], [160, 4, 202, 2], [160, 5, 202, 3], [161, 4, 203, 2, "collapseContainer"], [161, 21, 203, 19], [161, 23, 203, 21], [162, 6, 204, 4, "marginLeft"], [162, 16, 204, 14], [162, 18, 204, 16], [162, 20, 204, 18], [163, 6, 205, 4, "flexDirection"], [163, 19, 205, 17], [163, 21, 205, 19], [164, 4, 206, 2], [164, 5, 206, 3], [165, 4, 207, 2, "collapseButton"], [165, 18, 207, 16], [165, 20, 207, 18], [166, 6, 208, 4, "borderRadius"], [166, 18, 208, 16], [166, 20, 208, 18], [167, 4, 209, 2], [167, 5, 209, 3], [168, 4, 210, 2, "collapse"], [168, 12, 210, 10], [168, 14, 210, 12], [169, 6, 211, 4, "color"], [169, 11, 211, 9], [169, 13, 211, 11, "LogBoxStyle"], [169, 24, 211, 22], [169, 25, 211, 23, "getTextColor"], [169, 37, 211, 35], [169, 38, 211, 36], [169, 41, 211, 39], [169, 42, 211, 40], [170, 6, 212, 4, "fontSize"], [170, 14, 212, 12], [170, 16, 212, 14], [170, 18, 212, 16], [171, 6, 213, 4, "fontWeight"], [171, 16, 213, 14], [171, 18, 213, 16], [171, 23, 213, 21], [172, 6, 214, 4, "lineHeight"], [172, 16, 214, 14], [172, 18, 214, 16], [172, 20, 214, 18], [173, 6, 215, 4, "marginTop"], [173, 15, 215, 13], [173, 17, 215, 15], [173, 18, 215, 16], [174, 6, 216, 4, "paddingHorizontal"], [174, 23, 216, 21], [174, 25, 216, 23], [174, 27, 216, 25], [175, 6, 217, 4, "paddingVertical"], [175, 21, 217, 19], [175, 23, 217, 21], [176, 4, 218, 2], [177, 2, 219, 0], [177, 3, 219, 1], [177, 4, 219, 2], [178, 2, 219, 3], [178, 6, 219, 3, "_default"], [178, 14, 219, 3], [178, 17, 219, 3, "exports"], [178, 24, 219, 3], [178, 25, 219, 3, "default"], [178, 32, 219, 3], [178, 35, 221, 15, "LogBoxInspectorStackFrames"], [178, 61, 221, 41], [179, 0, 221, 41], [179, 3]], "functionMap": {"names": ["<global>", "getCollapseMessage", "stackFrames.reduce$argument_0", "LogBoxInspectorStackFrames", "React.useState$argument_0", "props.log.getAvailableStack.some$argument_0", "getStackList", "props.log.getAvailableStack.filter$argument_0", "StackFrameFooter.props.onPress", "StackFrameList", "props.list.map$argument_0", "<anonymous>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OC8B;4CCQ;GDM;CDoB;AGE;mDCC;8CCE,yBD;GDC;EGE;kDCE,yBD;GHI;iBK8B,8BL;CHK;ASE;sBCM;kBCQ,wCD;ODK;CTG;AYE;CZgB"}}, "type": "js/module"}]}