{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../interfaces", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 43, "index": 291}}], "key": "GMKh3a5g5xNaAog15vl07v6pG2U=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 292}, "end": {"line": 5, "column": 46, "index": 338}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _interfaces = require(_dependencyMap[2], \"../interfaces\");\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./GestureHandler\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const DEFAULT_MAX_DURATION_MS = 500;\n  const DEFAULT_MAX_DELAY_MS = 500;\n  const DEFAULT_NUMBER_OF_TAPS = 1;\n  const DEFAULT_MIN_NUMBER_OF_POINTERS = 1;\n  class TapGestureHandler extends _GestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"maxDeltaX\", Number.MIN_SAFE_INTEGER);\n      _defineProperty(this, \"maxDeltaY\", Number.MIN_SAFE_INTEGER);\n      _defineProperty(this, \"maxDistSq\", Number.MIN_SAFE_INTEGER);\n      _defineProperty(this, \"maxDurationMs\", DEFAULT_MAX_DURATION_MS);\n      _defineProperty(this, \"maxDelayMs\", DEFAULT_MAX_DELAY_MS);\n      _defineProperty(this, \"numberOfTaps\", DEFAULT_NUMBER_OF_TAPS);\n      _defineProperty(this, \"minNumberOfPointers\", DEFAULT_MIN_NUMBER_OF_POINTERS);\n      _defineProperty(this, \"currentMaxNumberOfPointers\", 1);\n      _defineProperty(this, \"startX\", 0);\n      _defineProperty(this, \"startY\", 0);\n      _defineProperty(this, \"offsetX\", 0);\n      _defineProperty(this, \"offsetY\", 0);\n      _defineProperty(this, \"lastX\", 0);\n      _defineProperty(this, \"lastY\", 0);\n      _defineProperty(this, \"waitTimeout\", void 0);\n      _defineProperty(this, \"delayTimeout\", void 0);\n      _defineProperty(this, \"tapsSoFar\", 0);\n    }\n    updateGestureConfig({\n      enabled = true,\n      ...props\n    }) {\n      super.updateGestureConfig({\n        enabled: enabled,\n        ...props\n      });\n      if (this.config.numberOfTaps !== undefined) {\n        this.numberOfTaps = this.config.numberOfTaps;\n      }\n      if (this.config.maxDurationMs !== undefined) {\n        this.maxDurationMs = this.config.maxDurationMs;\n      }\n      if (this.config.maxDelayMs !== undefined) {\n        this.maxDelayMs = this.config.maxDelayMs;\n      }\n      if (this.config.maxDeltaX !== undefined) {\n        this.maxDeltaX = this.config.maxDeltaX;\n      }\n      if (this.config.maxDeltaY !== undefined) {\n        this.maxDeltaY = this.config.maxDeltaY;\n      }\n      if (this.config.maxDist !== undefined) {\n        this.maxDistSq = this.config.maxDist * this.config.maxDist;\n      }\n      if (this.config.minPointers !== undefined) {\n        this.minNumberOfPointers = this.config.minPointers;\n      }\n    }\n    resetConfig() {\n      super.resetConfig();\n      this.maxDeltaX = Number.MIN_SAFE_INTEGER;\n      this.maxDeltaY = Number.MIN_SAFE_INTEGER;\n      this.maxDistSq = Number.MIN_SAFE_INTEGER;\n      this.maxDurationMs = DEFAULT_MAX_DURATION_MS;\n      this.maxDelayMs = DEFAULT_MAX_DELAY_MS;\n      this.numberOfTaps = DEFAULT_NUMBER_OF_TAPS;\n      this.minNumberOfPointers = DEFAULT_MIN_NUMBER_OF_POINTERS;\n    }\n    clearTimeouts() {\n      clearTimeout(this.waitTimeout);\n      clearTimeout(this.delayTimeout);\n    }\n    startTap() {\n      this.clearTimeouts();\n      this.waitTimeout = setTimeout(() => this.fail(), this.maxDurationMs);\n    }\n    endTap() {\n      this.clearTimeouts();\n      if (++this.tapsSoFar === this.numberOfTaps && this.currentMaxNumberOfPointers >= this.minNumberOfPointers) {\n        this.activate();\n      } else {\n        this.delayTimeout = setTimeout(() => this.fail(), this.maxDelayMs);\n      }\n    } // Handling Events\n\n    onPointerDown(event) {\n      if (!this.isButtonInConfig(event.button)) {\n        return;\n      }\n      this.tracker.addToTracker(event);\n      super.onPointerDown(event);\n      this.trySettingPosition(event);\n      this.startX = event.x;\n      this.startY = event.y;\n      this.lastX = event.x;\n      this.lastY = event.y;\n      this.updateState(event);\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerAdd(event) {\n      super.onPointerAdd(event);\n      this.tracker.addToTracker(event);\n      this.trySettingPosition(event);\n      this.offsetX += this.lastX - this.startX;\n      this.offsetY += this.lastY - this.startY;\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      this.startX = lastCoords.x;\n      this.startY = lastCoords.y;\n      this.updateState(event);\n    }\n    onPointerUp(event) {\n      super.onPointerUp(event);\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      this.tracker.removeFromTracker(event.pointerId);\n      this.updateState(event);\n    }\n    onPointerRemove(event) {\n      super.onPointerRemove(event);\n      this.tracker.removeFromTracker(event.pointerId);\n      this.offsetX += this.lastX - this.startX;\n      this.offsetY += this.lastY = this.startY;\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      this.startX = this.lastX;\n      this.startY = this.lastY;\n      this.updateState(event);\n    }\n    onPointerMove(event) {\n      this.trySettingPosition(event);\n      this.tracker.track(event);\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      this.updateState(event);\n      super.onPointerMove(event);\n    }\n    onPointerOutOfBounds(event) {\n      this.trySettingPosition(event);\n      this.tracker.track(event);\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      this.updateState(event);\n      super.onPointerOutOfBounds(event);\n    }\n    updateState(event) {\n      if (this.currentMaxNumberOfPointers < this.tracker.trackedPointersCount) {\n        this.currentMaxNumberOfPointers = this.tracker.trackedPointersCount;\n      }\n      if (this.shouldFail()) {\n        this.fail();\n        return;\n      }\n      switch (this.state) {\n        case _State.State.UNDETERMINED:\n          if (event.eventType === _interfaces.EventTypes.DOWN) {\n            this.begin();\n          }\n          this.startTap();\n          break;\n        case _State.State.BEGAN:\n          if (event.eventType === _interfaces.EventTypes.UP) {\n            this.endTap();\n          }\n          if (event.eventType === _interfaces.EventTypes.DOWN) {\n            this.startTap();\n          }\n          break;\n        default:\n          break;\n      }\n    }\n    trySettingPosition(event) {\n      if (this.state !== _State.State.UNDETERMINED) {\n        return;\n      }\n      this.offsetX = 0;\n      this.offsetY = 0;\n      this.startX = event.x;\n      this.startY = event.y;\n    }\n    shouldFail() {\n      const dx = this.lastX - this.startX + this.offsetX;\n      if (this.maxDeltaX !== Number.MIN_SAFE_INTEGER && Math.abs(dx) > this.maxDeltaX) {\n        return true;\n      }\n      const dy = this.lastY - this.startY + this.offsetY;\n      if (this.maxDeltaY !== Number.MIN_SAFE_INTEGER && Math.abs(dy) > this.maxDeltaY) {\n        return true;\n      }\n      const distSq = dy * dy + dx * dx;\n      return this.maxDistSq !== Number.MIN_SAFE_INTEGER && distSq > this.maxDistSq;\n    }\n    activate() {\n      super.activate();\n      this.end();\n    }\n    onCancel() {\n      this.resetProgress();\n      this.clearTimeouts();\n    }\n    resetProgress() {\n      this.clearTimeouts();\n      this.tapsSoFar = 0;\n      this.currentMaxNumberOfPointers = 0;\n    }\n  }\n  exports.default = TapGestureHandler;\n});", "lineCount": 233, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_State"], [7, 12, 3, 0], [7, 15, 3, 0, "require"], [7, 22, 3, 0], [7, 23, 3, 0, "_dependencyMap"], [7, 37, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_interfaces"], [8, 17, 4, 0], [8, 20, 4, 0, "require"], [8, 27, 4, 0], [8, 28, 4, 0, "_dependencyMap"], [8, 42, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_Gesture<PERSON><PERSON>ler"], [9, 21, 5, 0], [9, 24, 5, 0, "_interopRequireDefault"], [9, 46, 5, 0], [9, 47, 5, 0, "require"], [9, 54, 5, 0], [9, 55, 5, 0, "_dependencyMap"], [9, 69, 5, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "obj"], [10, 30, 1, 28], [10, 32, 1, 30, "key"], [10, 35, 1, 33], [10, 37, 1, 35, "value"], [10, 42, 1, 40], [10, 44, 1, 42], [11, 4, 1, 44], [11, 8, 1, 48, "key"], [11, 11, 1, 51], [11, 15, 1, 55, "obj"], [11, 18, 1, 58], [11, 20, 1, 60], [12, 6, 1, 62, "Object"], [12, 12, 1, 68], [12, 13, 1, 69, "defineProperty"], [12, 27, 1, 83], [12, 28, 1, 84, "obj"], [12, 31, 1, 87], [12, 33, 1, 89, "key"], [12, 36, 1, 92], [12, 38, 1, 94], [13, 8, 1, 96, "value"], [13, 13, 1, 101], [13, 15, 1, 103, "value"], [13, 20, 1, 108], [14, 8, 1, 110, "enumerable"], [14, 18, 1, 120], [14, 20, 1, 122], [14, 24, 1, 126], [15, 8, 1, 128, "configurable"], [15, 20, 1, 140], [15, 22, 1, 142], [15, 26, 1, 146], [16, 8, 1, 148, "writable"], [16, 16, 1, 156], [16, 18, 1, 158], [17, 6, 1, 163], [17, 7, 1, 164], [17, 8, 1, 165], [18, 4, 1, 167], [18, 5, 1, 168], [18, 11, 1, 174], [19, 6, 1, 176, "obj"], [19, 9, 1, 179], [19, 10, 1, 180, "key"], [19, 13, 1, 183], [19, 14, 1, 184], [19, 17, 1, 187, "value"], [19, 22, 1, 192], [20, 4, 1, 194], [21, 4, 1, 196], [21, 11, 1, 203, "obj"], [21, 14, 1, 206], [22, 2, 1, 208], [23, 2, 6, 0], [23, 8, 6, 6, "DEFAULT_MAX_DURATION_MS"], [23, 31, 6, 29], [23, 34, 6, 32], [23, 37, 6, 35], [24, 2, 7, 0], [24, 8, 7, 6, "DEFAULT_MAX_DELAY_MS"], [24, 28, 7, 26], [24, 31, 7, 29], [24, 34, 7, 32], [25, 2, 8, 0], [25, 8, 8, 6, "DEFAULT_NUMBER_OF_TAPS"], [25, 30, 8, 28], [25, 33, 8, 31], [25, 34, 8, 32], [26, 2, 9, 0], [26, 8, 9, 6, "DEFAULT_MIN_NUMBER_OF_POINTERS"], [26, 38, 9, 36], [26, 41, 9, 39], [26, 42, 9, 40], [27, 2, 10, 15], [27, 8, 10, 21, "TapGestureHandler"], [27, 25, 10, 38], [27, 34, 10, 47, "Gesture<PERSON>andler"], [27, 57, 10, 61], [27, 58, 10, 62], [28, 4, 11, 2, "constructor"], [28, 15, 11, 13, "constructor"], [28, 16, 11, 14], [28, 19, 11, 17, "args"], [28, 23, 11, 21], [28, 25, 11, 23], [29, 6, 12, 4], [29, 11, 12, 9], [29, 12, 12, 10], [29, 15, 12, 13, "args"], [29, 19, 12, 17], [29, 20, 12, 18], [30, 6, 14, 4, "_defineProperty"], [30, 21, 14, 19], [30, 22, 14, 20], [30, 26, 14, 24], [30, 28, 14, 26], [30, 39, 14, 37], [30, 41, 14, 39, "Number"], [30, 47, 14, 45], [30, 48, 14, 46, "MIN_SAFE_INTEGER"], [30, 64, 14, 62], [30, 65, 14, 63], [31, 6, 16, 4, "_defineProperty"], [31, 21, 16, 19], [31, 22, 16, 20], [31, 26, 16, 24], [31, 28, 16, 26], [31, 39, 16, 37], [31, 41, 16, 39, "Number"], [31, 47, 16, 45], [31, 48, 16, 46, "MIN_SAFE_INTEGER"], [31, 64, 16, 62], [31, 65, 16, 63], [32, 6, 18, 4, "_defineProperty"], [32, 21, 18, 19], [32, 22, 18, 20], [32, 26, 18, 24], [32, 28, 18, 26], [32, 39, 18, 37], [32, 41, 18, 39, "Number"], [32, 47, 18, 45], [32, 48, 18, 46, "MIN_SAFE_INTEGER"], [32, 64, 18, 62], [32, 65, 18, 63], [33, 6, 20, 4, "_defineProperty"], [33, 21, 20, 19], [33, 22, 20, 20], [33, 26, 20, 24], [33, 28, 20, 26], [33, 43, 20, 41], [33, 45, 20, 43, "DEFAULT_MAX_DURATION_MS"], [33, 68, 20, 66], [33, 69, 20, 67], [34, 6, 22, 4, "_defineProperty"], [34, 21, 22, 19], [34, 22, 22, 20], [34, 26, 22, 24], [34, 28, 22, 26], [34, 40, 22, 38], [34, 42, 22, 40, "DEFAULT_MAX_DELAY_MS"], [34, 62, 22, 60], [34, 63, 22, 61], [35, 6, 24, 4, "_defineProperty"], [35, 21, 24, 19], [35, 22, 24, 20], [35, 26, 24, 24], [35, 28, 24, 26], [35, 42, 24, 40], [35, 44, 24, 42, "DEFAULT_NUMBER_OF_TAPS"], [35, 66, 24, 64], [35, 67, 24, 65], [36, 6, 26, 4, "_defineProperty"], [36, 21, 26, 19], [36, 22, 26, 20], [36, 26, 26, 24], [36, 28, 26, 26], [36, 49, 26, 47], [36, 51, 26, 49, "DEFAULT_MIN_NUMBER_OF_POINTERS"], [36, 81, 26, 79], [36, 82, 26, 80], [37, 6, 28, 4, "_defineProperty"], [37, 21, 28, 19], [37, 22, 28, 20], [37, 26, 28, 24], [37, 28, 28, 26], [37, 56, 28, 54], [37, 58, 28, 56], [37, 59, 28, 57], [37, 60, 28, 58], [38, 6, 30, 4, "_defineProperty"], [38, 21, 30, 19], [38, 22, 30, 20], [38, 26, 30, 24], [38, 28, 30, 26], [38, 36, 30, 34], [38, 38, 30, 36], [38, 39, 30, 37], [38, 40, 30, 38], [39, 6, 32, 4, "_defineProperty"], [39, 21, 32, 19], [39, 22, 32, 20], [39, 26, 32, 24], [39, 28, 32, 26], [39, 36, 32, 34], [39, 38, 32, 36], [39, 39, 32, 37], [39, 40, 32, 38], [40, 6, 34, 4, "_defineProperty"], [40, 21, 34, 19], [40, 22, 34, 20], [40, 26, 34, 24], [40, 28, 34, 26], [40, 37, 34, 35], [40, 39, 34, 37], [40, 40, 34, 38], [40, 41, 34, 39], [41, 6, 36, 4, "_defineProperty"], [41, 21, 36, 19], [41, 22, 36, 20], [41, 26, 36, 24], [41, 28, 36, 26], [41, 37, 36, 35], [41, 39, 36, 37], [41, 40, 36, 38], [41, 41, 36, 39], [42, 6, 38, 4, "_defineProperty"], [42, 21, 38, 19], [42, 22, 38, 20], [42, 26, 38, 24], [42, 28, 38, 26], [42, 35, 38, 33], [42, 37, 38, 35], [42, 38, 38, 36], [42, 39, 38, 37], [43, 6, 40, 4, "_defineProperty"], [43, 21, 40, 19], [43, 22, 40, 20], [43, 26, 40, 24], [43, 28, 40, 26], [43, 35, 40, 33], [43, 37, 40, 35], [43, 38, 40, 36], [43, 39, 40, 37], [44, 6, 42, 4, "_defineProperty"], [44, 21, 42, 19], [44, 22, 42, 20], [44, 26, 42, 24], [44, 28, 42, 26], [44, 41, 42, 39], [44, 43, 42, 41], [44, 48, 42, 46], [44, 49, 42, 47], [44, 50, 42, 48], [45, 6, 44, 4, "_defineProperty"], [45, 21, 44, 19], [45, 22, 44, 20], [45, 26, 44, 24], [45, 28, 44, 26], [45, 42, 44, 40], [45, 44, 44, 42], [45, 49, 44, 47], [45, 50, 44, 48], [45, 51, 44, 49], [46, 6, 46, 4, "_defineProperty"], [46, 21, 46, 19], [46, 22, 46, 20], [46, 26, 46, 24], [46, 28, 46, 26], [46, 39, 46, 37], [46, 41, 46, 39], [46, 42, 46, 40], [46, 43, 46, 41], [47, 4, 47, 2], [48, 4, 49, 2, "updateGestureConfig"], [48, 23, 49, 21, "updateGestureConfig"], [48, 24, 49, 22], [49, 6, 50, 4, "enabled"], [49, 13, 50, 11], [49, 16, 50, 14], [49, 20, 50, 18], [50, 6, 51, 4], [50, 9, 51, 7, "props"], [51, 4, 52, 2], [51, 5, 52, 3], [51, 7, 52, 5], [52, 6, 53, 4], [52, 11, 53, 9], [52, 12, 53, 10, "updateGestureConfig"], [52, 31, 53, 29], [52, 32, 53, 30], [53, 8, 54, 6, "enabled"], [53, 15, 54, 13], [53, 17, 54, 15, "enabled"], [53, 24, 54, 22], [54, 8, 55, 6], [54, 11, 55, 9, "props"], [55, 6, 56, 4], [55, 7, 56, 5], [55, 8, 56, 6], [56, 6, 58, 4], [56, 10, 58, 8], [56, 14, 58, 12], [56, 15, 58, 13, "config"], [56, 21, 58, 19], [56, 22, 58, 20, "numberOfTaps"], [56, 34, 58, 32], [56, 39, 58, 37, "undefined"], [56, 48, 58, 46], [56, 50, 58, 48], [57, 8, 59, 6], [57, 12, 59, 10], [57, 13, 59, 11, "numberOfTaps"], [57, 25, 59, 23], [57, 28, 59, 26], [57, 32, 59, 30], [57, 33, 59, 31, "config"], [57, 39, 59, 37], [57, 40, 59, 38, "numberOfTaps"], [57, 52, 59, 50], [58, 6, 60, 4], [59, 6, 62, 4], [59, 10, 62, 8], [59, 14, 62, 12], [59, 15, 62, 13, "config"], [59, 21, 62, 19], [59, 22, 62, 20, "maxDurationMs"], [59, 35, 62, 33], [59, 40, 62, 38, "undefined"], [59, 49, 62, 47], [59, 51, 62, 49], [60, 8, 63, 6], [60, 12, 63, 10], [60, 13, 63, 11, "maxDurationMs"], [60, 26, 63, 24], [60, 29, 63, 27], [60, 33, 63, 31], [60, 34, 63, 32, "config"], [60, 40, 63, 38], [60, 41, 63, 39, "maxDurationMs"], [60, 54, 63, 52], [61, 6, 64, 4], [62, 6, 66, 4], [62, 10, 66, 8], [62, 14, 66, 12], [62, 15, 66, 13, "config"], [62, 21, 66, 19], [62, 22, 66, 20, "max<PERSON>elay<PERSON>"], [62, 32, 66, 30], [62, 37, 66, 35, "undefined"], [62, 46, 66, 44], [62, 48, 66, 46], [63, 8, 67, 6], [63, 12, 67, 10], [63, 13, 67, 11, "max<PERSON>elay<PERSON>"], [63, 23, 67, 21], [63, 26, 67, 24], [63, 30, 67, 28], [63, 31, 67, 29, "config"], [63, 37, 67, 35], [63, 38, 67, 36, "max<PERSON>elay<PERSON>"], [63, 48, 67, 46], [64, 6, 68, 4], [65, 6, 70, 4], [65, 10, 70, 8], [65, 14, 70, 12], [65, 15, 70, 13, "config"], [65, 21, 70, 19], [65, 22, 70, 20, "maxDeltaX"], [65, 31, 70, 29], [65, 36, 70, 34, "undefined"], [65, 45, 70, 43], [65, 47, 70, 45], [66, 8, 71, 6], [66, 12, 71, 10], [66, 13, 71, 11, "maxDeltaX"], [66, 22, 71, 20], [66, 25, 71, 23], [66, 29, 71, 27], [66, 30, 71, 28, "config"], [66, 36, 71, 34], [66, 37, 71, 35, "maxDeltaX"], [66, 46, 71, 44], [67, 6, 72, 4], [68, 6, 74, 4], [68, 10, 74, 8], [68, 14, 74, 12], [68, 15, 74, 13, "config"], [68, 21, 74, 19], [68, 22, 74, 20, "maxDeltaY"], [68, 31, 74, 29], [68, 36, 74, 34, "undefined"], [68, 45, 74, 43], [68, 47, 74, 45], [69, 8, 75, 6], [69, 12, 75, 10], [69, 13, 75, 11, "maxDeltaY"], [69, 22, 75, 20], [69, 25, 75, 23], [69, 29, 75, 27], [69, 30, 75, 28, "config"], [69, 36, 75, 34], [69, 37, 75, 35, "maxDeltaY"], [69, 46, 75, 44], [70, 6, 76, 4], [71, 6, 78, 4], [71, 10, 78, 8], [71, 14, 78, 12], [71, 15, 78, 13, "config"], [71, 21, 78, 19], [71, 22, 78, 20, "maxDist"], [71, 29, 78, 27], [71, 34, 78, 32, "undefined"], [71, 43, 78, 41], [71, 45, 78, 43], [72, 8, 79, 6], [72, 12, 79, 10], [72, 13, 79, 11, "maxDistSq"], [72, 22, 79, 20], [72, 25, 79, 23], [72, 29, 79, 27], [72, 30, 79, 28, "config"], [72, 36, 79, 34], [72, 37, 79, 35, "maxDist"], [72, 44, 79, 42], [72, 47, 79, 45], [72, 51, 79, 49], [72, 52, 79, 50, "config"], [72, 58, 79, 56], [72, 59, 79, 57, "maxDist"], [72, 66, 79, 64], [73, 6, 80, 4], [74, 6, 82, 4], [74, 10, 82, 8], [74, 14, 82, 12], [74, 15, 82, 13, "config"], [74, 21, 82, 19], [74, 22, 82, 20, "minPointers"], [74, 33, 82, 31], [74, 38, 82, 36, "undefined"], [74, 47, 82, 45], [74, 49, 82, 47], [75, 8, 83, 6], [75, 12, 83, 10], [75, 13, 83, 11, "minNumberOfPointers"], [75, 32, 83, 30], [75, 35, 83, 33], [75, 39, 83, 37], [75, 40, 83, 38, "config"], [75, 46, 83, 44], [75, 47, 83, 45, "minPointers"], [75, 58, 83, 56], [76, 6, 84, 4], [77, 4, 85, 2], [78, 4, 87, 2, "resetConfig"], [78, 15, 87, 13, "resetConfig"], [78, 16, 87, 13], [78, 18, 87, 16], [79, 6, 88, 4], [79, 11, 88, 9], [79, 12, 88, 10, "resetConfig"], [79, 23, 88, 21], [79, 24, 88, 22], [79, 25, 88, 23], [80, 6, 89, 4], [80, 10, 89, 8], [80, 11, 89, 9, "maxDeltaX"], [80, 20, 89, 18], [80, 23, 89, 21, "Number"], [80, 29, 89, 27], [80, 30, 89, 28, "MIN_SAFE_INTEGER"], [80, 46, 89, 44], [81, 6, 90, 4], [81, 10, 90, 8], [81, 11, 90, 9, "maxDeltaY"], [81, 20, 90, 18], [81, 23, 90, 21, "Number"], [81, 29, 90, 27], [81, 30, 90, 28, "MIN_SAFE_INTEGER"], [81, 46, 90, 44], [82, 6, 91, 4], [82, 10, 91, 8], [82, 11, 91, 9, "maxDistSq"], [82, 20, 91, 18], [82, 23, 91, 21, "Number"], [82, 29, 91, 27], [82, 30, 91, 28, "MIN_SAFE_INTEGER"], [82, 46, 91, 44], [83, 6, 92, 4], [83, 10, 92, 8], [83, 11, 92, 9, "maxDurationMs"], [83, 24, 92, 22], [83, 27, 92, 25, "DEFAULT_MAX_DURATION_MS"], [83, 50, 92, 48], [84, 6, 93, 4], [84, 10, 93, 8], [84, 11, 93, 9, "max<PERSON>elay<PERSON>"], [84, 21, 93, 19], [84, 24, 93, 22, "DEFAULT_MAX_DELAY_MS"], [84, 44, 93, 42], [85, 6, 94, 4], [85, 10, 94, 8], [85, 11, 94, 9, "numberOfTaps"], [85, 23, 94, 21], [85, 26, 94, 24, "DEFAULT_NUMBER_OF_TAPS"], [85, 48, 94, 46], [86, 6, 95, 4], [86, 10, 95, 8], [86, 11, 95, 9, "minNumberOfPointers"], [86, 30, 95, 28], [86, 33, 95, 31, "DEFAULT_MIN_NUMBER_OF_POINTERS"], [86, 63, 95, 61], [87, 4, 96, 2], [88, 4, 98, 2, "clearTimeouts"], [88, 17, 98, 15, "clearTimeouts"], [88, 18, 98, 15], [88, 20, 98, 18], [89, 6, 99, 4, "clearTimeout"], [89, 18, 99, 16], [89, 19, 99, 17], [89, 23, 99, 21], [89, 24, 99, 22, "waitTimeout"], [89, 35, 99, 33], [89, 36, 99, 34], [90, 6, 100, 4, "clearTimeout"], [90, 18, 100, 16], [90, 19, 100, 17], [90, 23, 100, 21], [90, 24, 100, 22, "delayTimeout"], [90, 36, 100, 34], [90, 37, 100, 35], [91, 4, 101, 2], [92, 4, 103, 2, "startTap"], [92, 12, 103, 10, "startTap"], [92, 13, 103, 10], [92, 15, 103, 13], [93, 6, 104, 4], [93, 10, 104, 8], [93, 11, 104, 9, "clearTimeouts"], [93, 24, 104, 22], [93, 25, 104, 23], [93, 26, 104, 24], [94, 6, 105, 4], [94, 10, 105, 8], [94, 11, 105, 9, "waitTimeout"], [94, 22, 105, 20], [94, 25, 105, 23, "setTimeout"], [94, 35, 105, 33], [94, 36, 105, 34], [94, 42, 105, 40], [94, 46, 105, 44], [94, 47, 105, 45, "fail"], [94, 51, 105, 49], [94, 52, 105, 50], [94, 53, 105, 51], [94, 55, 105, 53], [94, 59, 105, 57], [94, 60, 105, 58, "maxDurationMs"], [94, 73, 105, 71], [94, 74, 105, 72], [95, 4, 106, 2], [96, 4, 108, 2, "endTap"], [96, 10, 108, 8, "endTap"], [96, 11, 108, 8], [96, 13, 108, 11], [97, 6, 109, 4], [97, 10, 109, 8], [97, 11, 109, 9, "clearTimeouts"], [97, 24, 109, 22], [97, 25, 109, 23], [97, 26, 109, 24], [98, 6, 111, 4], [98, 10, 111, 8], [98, 12, 111, 10], [98, 16, 111, 14], [98, 17, 111, 15, "tapsSoFar"], [98, 26, 111, 24], [98, 31, 111, 29], [98, 35, 111, 33], [98, 36, 111, 34, "numberOfTaps"], [98, 48, 111, 46], [98, 52, 111, 50], [98, 56, 111, 54], [98, 57, 111, 55, "currentMaxNumberOfPointers"], [98, 83, 111, 81], [98, 87, 111, 85], [98, 91, 111, 89], [98, 92, 111, 90, "minNumberOfPointers"], [98, 111, 111, 109], [98, 113, 111, 111], [99, 8, 112, 6], [99, 12, 112, 10], [99, 13, 112, 11, "activate"], [99, 21, 112, 19], [99, 22, 112, 20], [99, 23, 112, 21], [100, 6, 113, 4], [100, 7, 113, 5], [100, 13, 113, 11], [101, 8, 114, 6], [101, 12, 114, 10], [101, 13, 114, 11, "delayTimeout"], [101, 25, 114, 23], [101, 28, 114, 26, "setTimeout"], [101, 38, 114, 36], [101, 39, 114, 37], [101, 45, 114, 43], [101, 49, 114, 47], [101, 50, 114, 48, "fail"], [101, 54, 114, 52], [101, 55, 114, 53], [101, 56, 114, 54], [101, 58, 114, 56], [101, 62, 114, 60], [101, 63, 114, 61, "max<PERSON>elay<PERSON>"], [101, 73, 114, 71], [101, 74, 114, 72], [102, 6, 115, 4], [103, 4, 116, 2], [103, 5, 116, 3], [103, 6, 116, 4], [105, 4, 119, 2, "onPointerDown"], [105, 17, 119, 15, "onPointerDown"], [105, 18, 119, 16, "event"], [105, 23, 119, 21], [105, 25, 119, 23], [106, 6, 120, 4], [106, 10, 120, 8], [106, 11, 120, 9], [106, 15, 120, 13], [106, 16, 120, 14, "isButtonInConfig"], [106, 32, 120, 30], [106, 33, 120, 31, "event"], [106, 38, 120, 36], [106, 39, 120, 37, "button"], [106, 45, 120, 43], [106, 46, 120, 44], [106, 48, 120, 46], [107, 8, 121, 6], [108, 6, 122, 4], [109, 6, 124, 4], [109, 10, 124, 8], [109, 11, 124, 9, "tracker"], [109, 18, 124, 16], [109, 19, 124, 17, "addToTracker"], [109, 31, 124, 29], [109, 32, 124, 30, "event"], [109, 37, 124, 35], [109, 38, 124, 36], [110, 6, 125, 4], [110, 11, 125, 9], [110, 12, 125, 10, "onPointerDown"], [110, 25, 125, 23], [110, 26, 125, 24, "event"], [110, 31, 125, 29], [110, 32, 125, 30], [111, 6, 126, 4], [111, 10, 126, 8], [111, 11, 126, 9, "trySettingPosition"], [111, 29, 126, 27], [111, 30, 126, 28, "event"], [111, 35, 126, 33], [111, 36, 126, 34], [112, 6, 127, 4], [112, 10, 127, 8], [112, 11, 127, 9, "startX"], [112, 17, 127, 15], [112, 20, 127, 18, "event"], [112, 25, 127, 23], [112, 26, 127, 24, "x"], [112, 27, 127, 25], [113, 6, 128, 4], [113, 10, 128, 8], [113, 11, 128, 9, "startY"], [113, 17, 128, 15], [113, 20, 128, 18, "event"], [113, 25, 128, 23], [113, 26, 128, 24, "y"], [113, 27, 128, 25], [114, 6, 129, 4], [114, 10, 129, 8], [114, 11, 129, 9, "lastX"], [114, 16, 129, 14], [114, 19, 129, 17, "event"], [114, 24, 129, 22], [114, 25, 129, 23, "x"], [114, 26, 129, 24], [115, 6, 130, 4], [115, 10, 130, 8], [115, 11, 130, 9, "lastY"], [115, 16, 130, 14], [115, 19, 130, 17, "event"], [115, 24, 130, 22], [115, 25, 130, 23, "y"], [115, 26, 130, 24], [116, 6, 131, 4], [116, 10, 131, 8], [116, 11, 131, 9, "updateState"], [116, 22, 131, 20], [116, 23, 131, 21, "event"], [116, 28, 131, 26], [116, 29, 131, 27], [117, 6, 132, 4], [117, 10, 132, 8], [117, 11, 132, 9, "tryToSendTouchEvent"], [117, 30, 132, 28], [117, 31, 132, 29, "event"], [117, 36, 132, 34], [117, 37, 132, 35], [118, 4, 133, 2], [119, 4, 135, 2, "onPointerAdd"], [119, 16, 135, 14, "onPointerAdd"], [119, 17, 135, 15, "event"], [119, 22, 135, 20], [119, 24, 135, 22], [120, 6, 136, 4], [120, 11, 136, 9], [120, 12, 136, 10, "onPointerAdd"], [120, 24, 136, 22], [120, 25, 136, 23, "event"], [120, 30, 136, 28], [120, 31, 136, 29], [121, 6, 137, 4], [121, 10, 137, 8], [121, 11, 137, 9, "tracker"], [121, 18, 137, 16], [121, 19, 137, 17, "addToTracker"], [121, 31, 137, 29], [121, 32, 137, 30, "event"], [121, 37, 137, 35], [121, 38, 137, 36], [122, 6, 138, 4], [122, 10, 138, 8], [122, 11, 138, 9, "trySettingPosition"], [122, 29, 138, 27], [122, 30, 138, 28, "event"], [122, 35, 138, 33], [122, 36, 138, 34], [123, 6, 139, 4], [123, 10, 139, 8], [123, 11, 139, 9, "offsetX"], [123, 18, 139, 16], [123, 22, 139, 20], [123, 26, 139, 24], [123, 27, 139, 25, "lastX"], [123, 32, 139, 30], [123, 35, 139, 33], [123, 39, 139, 37], [123, 40, 139, 38, "startX"], [123, 46, 139, 44], [124, 6, 140, 4], [124, 10, 140, 8], [124, 11, 140, 9, "offsetY"], [124, 18, 140, 16], [124, 22, 140, 20], [124, 26, 140, 24], [124, 27, 140, 25, "lastY"], [124, 32, 140, 30], [124, 35, 140, 33], [124, 39, 140, 37], [124, 40, 140, 38, "startY"], [124, 46, 140, 44], [125, 6, 141, 4], [125, 12, 141, 10, "lastCoords"], [125, 22, 141, 20], [125, 25, 141, 23], [125, 29, 141, 27], [125, 30, 141, 28, "tracker"], [125, 37, 141, 35], [125, 38, 141, 36, "getAbsoluteCoordsAverage"], [125, 62, 141, 60], [125, 63, 141, 61], [125, 64, 141, 62], [126, 6, 142, 4], [126, 10, 142, 8], [126, 11, 142, 9, "lastX"], [126, 16, 142, 14], [126, 19, 142, 17, "lastCoords"], [126, 29, 142, 27], [126, 30, 142, 28, "x"], [126, 31, 142, 29], [127, 6, 143, 4], [127, 10, 143, 8], [127, 11, 143, 9, "lastY"], [127, 16, 143, 14], [127, 19, 143, 17, "lastCoords"], [127, 29, 143, 27], [127, 30, 143, 28, "y"], [127, 31, 143, 29], [128, 6, 144, 4], [128, 10, 144, 8], [128, 11, 144, 9, "startX"], [128, 17, 144, 15], [128, 20, 144, 18, "lastCoords"], [128, 30, 144, 28], [128, 31, 144, 29, "x"], [128, 32, 144, 30], [129, 6, 145, 4], [129, 10, 145, 8], [129, 11, 145, 9, "startY"], [129, 17, 145, 15], [129, 20, 145, 18, "lastCoords"], [129, 30, 145, 28], [129, 31, 145, 29, "y"], [129, 32, 145, 30], [130, 6, 146, 4], [130, 10, 146, 8], [130, 11, 146, 9, "updateState"], [130, 22, 146, 20], [130, 23, 146, 21, "event"], [130, 28, 146, 26], [130, 29, 146, 27], [131, 4, 147, 2], [132, 4, 149, 2, "onPointerUp"], [132, 15, 149, 13, "onPointerUp"], [132, 16, 149, 14, "event"], [132, 21, 149, 19], [132, 23, 149, 21], [133, 6, 150, 4], [133, 11, 150, 9], [133, 12, 150, 10, "onPointerUp"], [133, 23, 150, 21], [133, 24, 150, 22, "event"], [133, 29, 150, 27], [133, 30, 150, 28], [134, 6, 151, 4], [134, 12, 151, 10, "lastCoords"], [134, 22, 151, 20], [134, 25, 151, 23], [134, 29, 151, 27], [134, 30, 151, 28, "tracker"], [134, 37, 151, 35], [134, 38, 151, 36, "getAbsoluteCoordsAverage"], [134, 62, 151, 60], [134, 63, 151, 61], [134, 64, 151, 62], [135, 6, 152, 4], [135, 10, 152, 8], [135, 11, 152, 9, "lastX"], [135, 16, 152, 14], [135, 19, 152, 17, "lastCoords"], [135, 29, 152, 27], [135, 30, 152, 28, "x"], [135, 31, 152, 29], [136, 6, 153, 4], [136, 10, 153, 8], [136, 11, 153, 9, "lastY"], [136, 16, 153, 14], [136, 19, 153, 17, "lastCoords"], [136, 29, 153, 27], [136, 30, 153, 28, "y"], [136, 31, 153, 29], [137, 6, 154, 4], [137, 10, 154, 8], [137, 11, 154, 9, "tracker"], [137, 18, 154, 16], [137, 19, 154, 17, "removeFromTracker"], [137, 36, 154, 34], [137, 37, 154, 35, "event"], [137, 42, 154, 40], [137, 43, 154, 41, "pointerId"], [137, 52, 154, 50], [137, 53, 154, 51], [138, 6, 155, 4], [138, 10, 155, 8], [138, 11, 155, 9, "updateState"], [138, 22, 155, 20], [138, 23, 155, 21, "event"], [138, 28, 155, 26], [138, 29, 155, 27], [139, 4, 156, 2], [140, 4, 158, 2, "onPointerRemove"], [140, 19, 158, 17, "onPointerRemove"], [140, 20, 158, 18, "event"], [140, 25, 158, 23], [140, 27, 158, 25], [141, 6, 159, 4], [141, 11, 159, 9], [141, 12, 159, 10, "onPointerRemove"], [141, 27, 159, 25], [141, 28, 159, 26, "event"], [141, 33, 159, 31], [141, 34, 159, 32], [142, 6, 160, 4], [142, 10, 160, 8], [142, 11, 160, 9, "tracker"], [142, 18, 160, 16], [142, 19, 160, 17, "removeFromTracker"], [142, 36, 160, 34], [142, 37, 160, 35, "event"], [142, 42, 160, 40], [142, 43, 160, 41, "pointerId"], [142, 52, 160, 50], [142, 53, 160, 51], [143, 6, 161, 4], [143, 10, 161, 8], [143, 11, 161, 9, "offsetX"], [143, 18, 161, 16], [143, 22, 161, 20], [143, 26, 161, 24], [143, 27, 161, 25, "lastX"], [143, 32, 161, 30], [143, 35, 161, 33], [143, 39, 161, 37], [143, 40, 161, 38, "startX"], [143, 46, 161, 44], [144, 6, 162, 4], [144, 10, 162, 8], [144, 11, 162, 9, "offsetY"], [144, 18, 162, 16], [144, 22, 162, 20], [144, 26, 162, 24], [144, 27, 162, 25, "lastY"], [144, 32, 162, 30], [144, 35, 162, 33], [144, 39, 162, 37], [144, 40, 162, 38, "startY"], [144, 46, 162, 44], [145, 6, 163, 4], [145, 12, 163, 10, "lastCoords"], [145, 22, 163, 20], [145, 25, 163, 23], [145, 29, 163, 27], [145, 30, 163, 28, "tracker"], [145, 37, 163, 35], [145, 38, 163, 36, "getAbsoluteCoordsAverage"], [145, 62, 163, 60], [145, 63, 163, 61], [145, 64, 163, 62], [146, 6, 164, 4], [146, 10, 164, 8], [146, 11, 164, 9, "lastX"], [146, 16, 164, 14], [146, 19, 164, 17, "lastCoords"], [146, 29, 164, 27], [146, 30, 164, 28, "x"], [146, 31, 164, 29], [147, 6, 165, 4], [147, 10, 165, 8], [147, 11, 165, 9, "lastY"], [147, 16, 165, 14], [147, 19, 165, 17, "lastCoords"], [147, 29, 165, 27], [147, 30, 165, 28, "y"], [147, 31, 165, 29], [148, 6, 166, 4], [148, 10, 166, 8], [148, 11, 166, 9, "startX"], [148, 17, 166, 15], [148, 20, 166, 18], [148, 24, 166, 22], [148, 25, 166, 23, "lastX"], [148, 30, 166, 28], [149, 6, 167, 4], [149, 10, 167, 8], [149, 11, 167, 9, "startY"], [149, 17, 167, 15], [149, 20, 167, 18], [149, 24, 167, 22], [149, 25, 167, 23, "lastY"], [149, 30, 167, 28], [150, 6, 168, 4], [150, 10, 168, 8], [150, 11, 168, 9, "updateState"], [150, 22, 168, 20], [150, 23, 168, 21, "event"], [150, 28, 168, 26], [150, 29, 168, 27], [151, 4, 169, 2], [152, 4, 171, 2, "onPointerMove"], [152, 17, 171, 15, "onPointerMove"], [152, 18, 171, 16, "event"], [152, 23, 171, 21], [152, 25, 171, 23], [153, 6, 172, 4], [153, 10, 172, 8], [153, 11, 172, 9, "trySettingPosition"], [153, 29, 172, 27], [153, 30, 172, 28, "event"], [153, 35, 172, 33], [153, 36, 172, 34], [154, 6, 173, 4], [154, 10, 173, 8], [154, 11, 173, 9, "tracker"], [154, 18, 173, 16], [154, 19, 173, 17, "track"], [154, 24, 173, 22], [154, 25, 173, 23, "event"], [154, 30, 173, 28], [154, 31, 173, 29], [155, 6, 174, 4], [155, 12, 174, 10, "lastCoords"], [155, 22, 174, 20], [155, 25, 174, 23], [155, 29, 174, 27], [155, 30, 174, 28, "tracker"], [155, 37, 174, 35], [155, 38, 174, 36, "getAbsoluteCoordsAverage"], [155, 62, 174, 60], [155, 63, 174, 61], [155, 64, 174, 62], [156, 6, 175, 4], [156, 10, 175, 8], [156, 11, 175, 9, "lastX"], [156, 16, 175, 14], [156, 19, 175, 17, "lastCoords"], [156, 29, 175, 27], [156, 30, 175, 28, "x"], [156, 31, 175, 29], [157, 6, 176, 4], [157, 10, 176, 8], [157, 11, 176, 9, "lastY"], [157, 16, 176, 14], [157, 19, 176, 17, "lastCoords"], [157, 29, 176, 27], [157, 30, 176, 28, "y"], [157, 31, 176, 29], [158, 6, 177, 4], [158, 10, 177, 8], [158, 11, 177, 9, "updateState"], [158, 22, 177, 20], [158, 23, 177, 21, "event"], [158, 28, 177, 26], [158, 29, 177, 27], [159, 6, 178, 4], [159, 11, 178, 9], [159, 12, 178, 10, "onPointerMove"], [159, 25, 178, 23], [159, 26, 178, 24, "event"], [159, 31, 178, 29], [159, 32, 178, 30], [160, 4, 179, 2], [161, 4, 181, 2, "onPointerOutOfBounds"], [161, 24, 181, 22, "onPointerOutOfBounds"], [161, 25, 181, 23, "event"], [161, 30, 181, 28], [161, 32, 181, 30], [162, 6, 182, 4], [162, 10, 182, 8], [162, 11, 182, 9, "trySettingPosition"], [162, 29, 182, 27], [162, 30, 182, 28, "event"], [162, 35, 182, 33], [162, 36, 182, 34], [163, 6, 183, 4], [163, 10, 183, 8], [163, 11, 183, 9, "tracker"], [163, 18, 183, 16], [163, 19, 183, 17, "track"], [163, 24, 183, 22], [163, 25, 183, 23, "event"], [163, 30, 183, 28], [163, 31, 183, 29], [164, 6, 184, 4], [164, 12, 184, 10, "lastCoords"], [164, 22, 184, 20], [164, 25, 184, 23], [164, 29, 184, 27], [164, 30, 184, 28, "tracker"], [164, 37, 184, 35], [164, 38, 184, 36, "getAbsoluteCoordsAverage"], [164, 62, 184, 60], [164, 63, 184, 61], [164, 64, 184, 62], [165, 6, 185, 4], [165, 10, 185, 8], [165, 11, 185, 9, "lastX"], [165, 16, 185, 14], [165, 19, 185, 17, "lastCoords"], [165, 29, 185, 27], [165, 30, 185, 28, "x"], [165, 31, 185, 29], [166, 6, 186, 4], [166, 10, 186, 8], [166, 11, 186, 9, "lastY"], [166, 16, 186, 14], [166, 19, 186, 17, "lastCoords"], [166, 29, 186, 27], [166, 30, 186, 28, "y"], [166, 31, 186, 29], [167, 6, 187, 4], [167, 10, 187, 8], [167, 11, 187, 9, "updateState"], [167, 22, 187, 20], [167, 23, 187, 21, "event"], [167, 28, 187, 26], [167, 29, 187, 27], [168, 6, 188, 4], [168, 11, 188, 9], [168, 12, 188, 10, "onPointerOutOfBounds"], [168, 32, 188, 30], [168, 33, 188, 31, "event"], [168, 38, 188, 36], [168, 39, 188, 37], [169, 4, 189, 2], [170, 4, 191, 2, "updateState"], [170, 15, 191, 13, "updateState"], [170, 16, 191, 14, "event"], [170, 21, 191, 19], [170, 23, 191, 21], [171, 6, 192, 4], [171, 10, 192, 8], [171, 14, 192, 12], [171, 15, 192, 13, "currentMaxNumberOfPointers"], [171, 41, 192, 39], [171, 44, 192, 42], [171, 48, 192, 46], [171, 49, 192, 47, "tracker"], [171, 56, 192, 54], [171, 57, 192, 55, "trackedPointersCount"], [171, 77, 192, 75], [171, 79, 192, 77], [172, 8, 193, 6], [172, 12, 193, 10], [172, 13, 193, 11, "currentMaxNumberOfPointers"], [172, 39, 193, 37], [172, 42, 193, 40], [172, 46, 193, 44], [172, 47, 193, 45, "tracker"], [172, 54, 193, 52], [172, 55, 193, 53, "trackedPointersCount"], [172, 75, 193, 73], [173, 6, 194, 4], [174, 6, 196, 4], [174, 10, 196, 8], [174, 14, 196, 12], [174, 15, 196, 13, "shouldFail"], [174, 25, 196, 23], [174, 26, 196, 24], [174, 27, 196, 25], [174, 29, 196, 27], [175, 8, 197, 6], [175, 12, 197, 10], [175, 13, 197, 11, "fail"], [175, 17, 197, 15], [175, 18, 197, 16], [175, 19, 197, 17], [176, 8, 198, 6], [177, 6, 199, 4], [178, 6, 201, 4], [178, 14, 201, 12], [178, 18, 201, 16], [178, 19, 201, 17, "state"], [178, 24, 201, 22], [179, 8, 202, 6], [179, 13, 202, 11, "State"], [179, 25, 202, 16], [179, 26, 202, 17, "UNDETERMINED"], [179, 38, 202, 29], [180, 10, 203, 8], [180, 14, 203, 12, "event"], [180, 19, 203, 17], [180, 20, 203, 18, "eventType"], [180, 29, 203, 27], [180, 34, 203, 32, "EventTypes"], [180, 56, 203, 42], [180, 57, 203, 43, "DOWN"], [180, 61, 203, 47], [180, 63, 203, 49], [181, 12, 204, 10], [181, 16, 204, 14], [181, 17, 204, 15, "begin"], [181, 22, 204, 20], [181, 23, 204, 21], [181, 24, 204, 22], [182, 10, 205, 8], [183, 10, 207, 8], [183, 14, 207, 12], [183, 15, 207, 13, "startTap"], [183, 23, 207, 21], [183, 24, 207, 22], [183, 25, 207, 23], [184, 10, 208, 8], [185, 8, 210, 6], [185, 13, 210, 11, "State"], [185, 25, 210, 16], [185, 26, 210, 17, "BEGAN"], [185, 31, 210, 22], [186, 10, 211, 8], [186, 14, 211, 12, "event"], [186, 19, 211, 17], [186, 20, 211, 18, "eventType"], [186, 29, 211, 27], [186, 34, 211, 32, "EventTypes"], [186, 56, 211, 42], [186, 57, 211, 43, "UP"], [186, 59, 211, 45], [186, 61, 211, 47], [187, 12, 212, 10], [187, 16, 212, 14], [187, 17, 212, 15, "endTap"], [187, 23, 212, 21], [187, 24, 212, 22], [187, 25, 212, 23], [188, 10, 213, 8], [189, 10, 215, 8], [189, 14, 215, 12, "event"], [189, 19, 215, 17], [189, 20, 215, 18, "eventType"], [189, 29, 215, 27], [189, 34, 215, 32, "EventTypes"], [189, 56, 215, 42], [189, 57, 215, 43, "DOWN"], [189, 61, 215, 47], [189, 63, 215, 49], [190, 12, 216, 10], [190, 16, 216, 14], [190, 17, 216, 15, "startTap"], [190, 25, 216, 23], [190, 26, 216, 24], [190, 27, 216, 25], [191, 10, 217, 8], [192, 10, 219, 8], [193, 8, 221, 6], [194, 10, 222, 8], [195, 6, 223, 4], [196, 4, 224, 2], [197, 4, 226, 2, "trySettingPosition"], [197, 22, 226, 20, "trySettingPosition"], [197, 23, 226, 21, "event"], [197, 28, 226, 26], [197, 30, 226, 28], [198, 6, 227, 4], [198, 10, 227, 8], [198, 14, 227, 12], [198, 15, 227, 13, "state"], [198, 20, 227, 18], [198, 25, 227, 23, "State"], [198, 37, 227, 28], [198, 38, 227, 29, "UNDETERMINED"], [198, 50, 227, 41], [198, 52, 227, 43], [199, 8, 228, 6], [200, 6, 229, 4], [201, 6, 231, 4], [201, 10, 231, 8], [201, 11, 231, 9, "offsetX"], [201, 18, 231, 16], [201, 21, 231, 19], [201, 22, 231, 20], [202, 6, 232, 4], [202, 10, 232, 8], [202, 11, 232, 9, "offsetY"], [202, 18, 232, 16], [202, 21, 232, 19], [202, 22, 232, 20], [203, 6, 233, 4], [203, 10, 233, 8], [203, 11, 233, 9, "startX"], [203, 17, 233, 15], [203, 20, 233, 18, "event"], [203, 25, 233, 23], [203, 26, 233, 24, "x"], [203, 27, 233, 25], [204, 6, 234, 4], [204, 10, 234, 8], [204, 11, 234, 9, "startY"], [204, 17, 234, 15], [204, 20, 234, 18, "event"], [204, 25, 234, 23], [204, 26, 234, 24, "y"], [204, 27, 234, 25], [205, 4, 235, 2], [206, 4, 237, 2, "shouldFail"], [206, 14, 237, 12, "shouldFail"], [206, 15, 237, 12], [206, 17, 237, 15], [207, 6, 238, 4], [207, 12, 238, 10, "dx"], [207, 14, 238, 12], [207, 17, 238, 15], [207, 21, 238, 19], [207, 22, 238, 20, "lastX"], [207, 27, 238, 25], [207, 30, 238, 28], [207, 34, 238, 32], [207, 35, 238, 33, "startX"], [207, 41, 238, 39], [207, 44, 238, 42], [207, 48, 238, 46], [207, 49, 238, 47, "offsetX"], [207, 56, 238, 54], [208, 6, 240, 4], [208, 10, 240, 8], [208, 14, 240, 12], [208, 15, 240, 13, "maxDeltaX"], [208, 24, 240, 22], [208, 29, 240, 27, "Number"], [208, 35, 240, 33], [208, 36, 240, 34, "MIN_SAFE_INTEGER"], [208, 52, 240, 50], [208, 56, 240, 54, "Math"], [208, 60, 240, 58], [208, 61, 240, 59, "abs"], [208, 64, 240, 62], [208, 65, 240, 63, "dx"], [208, 67, 240, 65], [208, 68, 240, 66], [208, 71, 240, 69], [208, 75, 240, 73], [208, 76, 240, 74, "maxDeltaX"], [208, 85, 240, 83], [208, 87, 240, 85], [209, 8, 241, 6], [209, 15, 241, 13], [209, 19, 241, 17], [210, 6, 242, 4], [211, 6, 244, 4], [211, 12, 244, 10, "dy"], [211, 14, 244, 12], [211, 17, 244, 15], [211, 21, 244, 19], [211, 22, 244, 20, "lastY"], [211, 27, 244, 25], [211, 30, 244, 28], [211, 34, 244, 32], [211, 35, 244, 33, "startY"], [211, 41, 244, 39], [211, 44, 244, 42], [211, 48, 244, 46], [211, 49, 244, 47, "offsetY"], [211, 56, 244, 54], [212, 6, 246, 4], [212, 10, 246, 8], [212, 14, 246, 12], [212, 15, 246, 13, "maxDeltaY"], [212, 24, 246, 22], [212, 29, 246, 27, "Number"], [212, 35, 246, 33], [212, 36, 246, 34, "MIN_SAFE_INTEGER"], [212, 52, 246, 50], [212, 56, 246, 54, "Math"], [212, 60, 246, 58], [212, 61, 246, 59, "abs"], [212, 64, 246, 62], [212, 65, 246, 63, "dy"], [212, 67, 246, 65], [212, 68, 246, 66], [212, 71, 246, 69], [212, 75, 246, 73], [212, 76, 246, 74, "maxDeltaY"], [212, 85, 246, 83], [212, 87, 246, 85], [213, 8, 247, 6], [213, 15, 247, 13], [213, 19, 247, 17], [214, 6, 248, 4], [215, 6, 250, 4], [215, 12, 250, 10, "distSq"], [215, 18, 250, 16], [215, 21, 250, 19, "dy"], [215, 23, 250, 21], [215, 26, 250, 24, "dy"], [215, 28, 250, 26], [215, 31, 250, 29, "dx"], [215, 33, 250, 31], [215, 36, 250, 34, "dx"], [215, 38, 250, 36], [216, 6, 251, 4], [216, 13, 251, 11], [216, 17, 251, 15], [216, 18, 251, 16, "maxDistSq"], [216, 27, 251, 25], [216, 32, 251, 30, "Number"], [216, 38, 251, 36], [216, 39, 251, 37, "MIN_SAFE_INTEGER"], [216, 55, 251, 53], [216, 59, 251, 57, "distSq"], [216, 65, 251, 63], [216, 68, 251, 66], [216, 72, 251, 70], [216, 73, 251, 71, "maxDistSq"], [216, 82, 251, 80], [217, 4, 252, 2], [218, 4, 254, 2, "activate"], [218, 12, 254, 10, "activate"], [218, 13, 254, 10], [218, 15, 254, 13], [219, 6, 255, 4], [219, 11, 255, 9], [219, 12, 255, 10, "activate"], [219, 20, 255, 18], [219, 21, 255, 19], [219, 22, 255, 20], [220, 6, 256, 4], [220, 10, 256, 8], [220, 11, 256, 9, "end"], [220, 14, 256, 12], [220, 15, 256, 13], [220, 16, 256, 14], [221, 4, 257, 2], [222, 4, 259, 2, "onCancel"], [222, 12, 259, 10, "onCancel"], [222, 13, 259, 10], [222, 15, 259, 13], [223, 6, 260, 4], [223, 10, 260, 8], [223, 11, 260, 9, "resetProgress"], [223, 24, 260, 22], [223, 25, 260, 23], [223, 26, 260, 24], [224, 6, 261, 4], [224, 10, 261, 8], [224, 11, 261, 9, "clearTimeouts"], [224, 24, 261, 22], [224, 25, 261, 23], [224, 26, 261, 24], [225, 4, 262, 2], [226, 4, 264, 2, "resetProgress"], [226, 17, 264, 15, "resetProgress"], [226, 18, 264, 15], [226, 20, 264, 18], [227, 6, 265, 4], [227, 10, 265, 8], [227, 11, 265, 9, "clearTimeouts"], [227, 24, 265, 22], [227, 25, 265, 23], [227, 26, 265, 24], [228, 6, 266, 4], [228, 10, 266, 8], [228, 11, 266, 9, "tapsSoFar"], [228, 20, 266, 18], [228, 23, 266, 21], [228, 24, 266, 22], [229, 6, 267, 4], [229, 10, 267, 8], [229, 11, 267, 9, "currentMaxNumberOfPointers"], [229, 37, 267, 35], [229, 40, 267, 38], [229, 41, 267, 39], [230, 4, 268, 2], [231, 2, 270, 0], [232, 2, 270, 1, "exports"], [232, 9, 270, 1], [232, 10, 270, 1, "default"], [232, 17, 270, 1], [232, 20, 270, 1, "TapGestureHandler"], [232, 37, 270, 1], [233, 0, 270, 1], [233, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "TapGestureHandler", "constructor", "updateGestureConfig", "resetConfig", "clearTimeouts", "startTap", "setTimeout$argument_0", "endTap", "onPointerDown", "onPointerAdd", "onPointerUp", "onPointerRemove", "onPointerMove", "onPointerOutOfBounds", "updateState", "trySettingPosition", "shouldFail", "activate", "onCancel", "resetProgress"], "mappings": "AAA,iNC;eCS;ECC;GDoC;EEE;GFoC;EGE;GHS;EIE;GJG;EKE;kCCE,iBD;GLC;EOE;qCDM,iBC;GPE;EQG;GRc;ESE;GTY;EUE;GVO;EWE;GXW;EYE;GZQ;EaE;GbQ;EcE;GdiC;EeE;GfS;EgBE;GhBe;EiBE;GjBG;EkBE;GlBG;EmBE;GnBI;CDE"}}, "type": "js/module"}]}