{"dependencies": [{"name": "../interfaces", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 43, "index": 254}}], "key": "GMKh3a5g5xNaAog15vl07v6pG2U=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _interfaces = require(_dependencyMap[0], \"../interfaces\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class RotationGestureDetector {\n    constructor(callbacks) {\n      _defineProperty(this, \"onRotationBegin\", void 0);\n      _defineProperty(this, \"onRotation\", void 0);\n      _defineProperty(this, \"onRotationEnd\", void 0);\n      _defineProperty(this, \"currentTime\", 0);\n      _defineProperty(this, \"previousTime\", 0);\n      _defineProperty(this, \"previousAngle\", 0);\n      _defineProperty(this, \"_rotation\", 0);\n      _defineProperty(this, \"_anchorX\", 0);\n      _defineProperty(this, \"_anchorY\", 0);\n      _defineProperty(this, \"isInProgress\", false);\n      _defineProperty(this, \"keyPointers\", [NaN, NaN]);\n      this.onRotationBegin = callbacks.onRotationBegin;\n      this.onRotation = callbacks.onRotation;\n      this.onRotationEnd = callbacks.onRotationEnd;\n    }\n    updateCurrent(event, tracker) {\n      this.previousTime = this.currentTime;\n      this.currentTime = event.time;\n      const [firstPointerID, secondPointerID] = this.keyPointers;\n      const firstPointerCoords = tracker.getLastAbsoluteCoords(firstPointerID);\n      const secondPointerCoords = tracker.getLastAbsoluteCoords(secondPointerID);\n      const vectorX = secondPointerCoords.x - firstPointerCoords.x;\n      const vectorY = secondPointerCoords.y - firstPointerCoords.y;\n      this._anchorX = (firstPointerCoords.x + secondPointerCoords.x) / 2;\n      this._anchorY = (firstPointerCoords.y + secondPointerCoords.y) / 2; // Angle diff should be positive when rotating in clockwise direction\n\n      const angle = -Math.atan2(vectorY, vectorX);\n      this._rotation = Number.isNaN(this.previousAngle) ? 0 : this.previousAngle - angle;\n      this.previousAngle = angle;\n      if (this.rotation > Math.PI) {\n        this._rotation -= Math.PI;\n      } else if (this.rotation < -Math.PI) {\n        this._rotation += Math.PI;\n      }\n      if (this.rotation > Math.PI / 2) {\n        this._rotation -= Math.PI;\n      } else if (this.rotation < -Math.PI / 2) {\n        this._rotation += Math.PI;\n      }\n    }\n    finish() {\n      if (!this.isInProgress) {\n        return;\n      }\n      this.isInProgress = false;\n      this.keyPointers = [NaN, NaN];\n      this.onRotationEnd(this);\n    }\n    setKeyPointers(tracker) {\n      if (this.keyPointers[0] && this.keyPointers[1]) {\n        return;\n      }\n      const pointerIDs = tracker.trackedPointers.keys();\n      this.keyPointers[0] = pointerIDs.next().value;\n      this.keyPointers[1] = pointerIDs.next().value;\n    }\n    onTouchEvent(event, tracker) {\n      switch (event.eventType) {\n        case _interfaces.EventTypes.DOWN:\n          this.isInProgress = false;\n          break;\n        case _interfaces.EventTypes.ADDITIONAL_POINTER_DOWN:\n          if (this.isInProgress) {\n            break;\n          }\n          this.isInProgress = true;\n          this.previousTime = event.time;\n          this.previousAngle = NaN;\n          this.setKeyPointers(tracker);\n          this.updateCurrent(event, tracker);\n          this.onRotationBegin(this);\n          break;\n        case _interfaces.EventTypes.MOVE:\n          if (!this.isInProgress) {\n            break;\n          }\n          this.updateCurrent(event, tracker);\n          this.onRotation(this);\n          break;\n        case _interfaces.EventTypes.ADDITIONAL_POINTER_UP:\n          if (!this.isInProgress) {\n            break;\n          }\n          if (this.keyPointers.indexOf(event.pointerId) >= 0) {\n            this.finish();\n          }\n          break;\n        case _interfaces.EventTypes.UP:\n          if (this.isInProgress) {\n            this.finish();\n          }\n          break;\n      }\n      return true;\n    }\n    reset() {\n      this.keyPointers = [NaN, NaN];\n      this.isInProgress = false;\n    }\n    get anchorX() {\n      return this._anchorX;\n    }\n    get anchorY() {\n      return this._anchorY;\n    }\n    get rotation() {\n      return this._rotation;\n    }\n    get timeDelta() {\n      return this.currentTime + this.previousTime;\n    }\n  }\n  exports.default = RotationGestureDetector;\n});", "lineCount": 135, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_interfaces"], [6, 17, 3, 0], [6, 20, 3, 0, "require"], [6, 27, 3, 0], [6, 28, 3, 0, "_dependencyMap"], [6, 42, 3, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "obj"], [7, 30, 1, 28], [7, 32, 1, 30, "key"], [7, 35, 1, 33], [7, 37, 1, 35, "value"], [7, 42, 1, 40], [7, 44, 1, 42], [8, 4, 1, 44], [8, 8, 1, 48, "key"], [8, 11, 1, 51], [8, 15, 1, 55, "obj"], [8, 18, 1, 58], [8, 20, 1, 60], [9, 6, 1, 62, "Object"], [9, 12, 1, 68], [9, 13, 1, 69, "defineProperty"], [9, 27, 1, 83], [9, 28, 1, 84, "obj"], [9, 31, 1, 87], [9, 33, 1, 89, "key"], [9, 36, 1, 92], [9, 38, 1, 94], [10, 8, 1, 96, "value"], [10, 13, 1, 101], [10, 15, 1, 103, "value"], [10, 20, 1, 108], [11, 8, 1, 110, "enumerable"], [11, 18, 1, 120], [11, 20, 1, 122], [11, 24, 1, 126], [12, 8, 1, 128, "configurable"], [12, 20, 1, 140], [12, 22, 1, 142], [12, 26, 1, 146], [13, 8, 1, 148, "writable"], [13, 16, 1, 156], [13, 18, 1, 158], [14, 6, 1, 163], [14, 7, 1, 164], [14, 8, 1, 165], [15, 4, 1, 167], [15, 5, 1, 168], [15, 11, 1, 174], [16, 6, 1, 176, "obj"], [16, 9, 1, 179], [16, 10, 1, 180, "key"], [16, 13, 1, 183], [16, 14, 1, 184], [16, 17, 1, 187, "value"], [16, 22, 1, 192], [17, 4, 1, 194], [18, 4, 1, 196], [18, 11, 1, 203, "obj"], [18, 14, 1, 206], [19, 2, 1, 208], [20, 2, 4, 15], [20, 8, 4, 21, "RotationGestureDetector"], [20, 31, 4, 44], [20, 32, 4, 45], [21, 4, 5, 2, "constructor"], [21, 15, 5, 13, "constructor"], [21, 16, 5, 14, "callbacks"], [21, 25, 5, 23], [21, 27, 5, 25], [22, 6, 6, 4, "_defineProperty"], [22, 21, 6, 19], [22, 22, 6, 20], [22, 26, 6, 24], [22, 28, 6, 26], [22, 45, 6, 43], [22, 47, 6, 45], [22, 52, 6, 50], [22, 53, 6, 51], [22, 54, 6, 52], [23, 6, 8, 4, "_defineProperty"], [23, 21, 8, 19], [23, 22, 8, 20], [23, 26, 8, 24], [23, 28, 8, 26], [23, 40, 8, 38], [23, 42, 8, 40], [23, 47, 8, 45], [23, 48, 8, 46], [23, 49, 8, 47], [24, 6, 10, 4, "_defineProperty"], [24, 21, 10, 19], [24, 22, 10, 20], [24, 26, 10, 24], [24, 28, 10, 26], [24, 43, 10, 41], [24, 45, 10, 43], [24, 50, 10, 48], [24, 51, 10, 49], [24, 52, 10, 50], [25, 6, 12, 4, "_defineProperty"], [25, 21, 12, 19], [25, 22, 12, 20], [25, 26, 12, 24], [25, 28, 12, 26], [25, 41, 12, 39], [25, 43, 12, 41], [25, 44, 12, 42], [25, 45, 12, 43], [26, 6, 14, 4, "_defineProperty"], [26, 21, 14, 19], [26, 22, 14, 20], [26, 26, 14, 24], [26, 28, 14, 26], [26, 42, 14, 40], [26, 44, 14, 42], [26, 45, 14, 43], [26, 46, 14, 44], [27, 6, 16, 4, "_defineProperty"], [27, 21, 16, 19], [27, 22, 16, 20], [27, 26, 16, 24], [27, 28, 16, 26], [27, 43, 16, 41], [27, 45, 16, 43], [27, 46, 16, 44], [27, 47, 16, 45], [28, 6, 18, 4, "_defineProperty"], [28, 21, 18, 19], [28, 22, 18, 20], [28, 26, 18, 24], [28, 28, 18, 26], [28, 39, 18, 37], [28, 41, 18, 39], [28, 42, 18, 40], [28, 43, 18, 41], [29, 6, 20, 4, "_defineProperty"], [29, 21, 20, 19], [29, 22, 20, 20], [29, 26, 20, 24], [29, 28, 20, 26], [29, 38, 20, 36], [29, 40, 20, 38], [29, 41, 20, 39], [29, 42, 20, 40], [30, 6, 22, 4, "_defineProperty"], [30, 21, 22, 19], [30, 22, 22, 20], [30, 26, 22, 24], [30, 28, 22, 26], [30, 38, 22, 36], [30, 40, 22, 38], [30, 41, 22, 39], [30, 42, 22, 40], [31, 6, 24, 4, "_defineProperty"], [31, 21, 24, 19], [31, 22, 24, 20], [31, 26, 24, 24], [31, 28, 24, 26], [31, 42, 24, 40], [31, 44, 24, 42], [31, 49, 24, 47], [31, 50, 24, 48], [32, 6, 26, 4, "_defineProperty"], [32, 21, 26, 19], [32, 22, 26, 20], [32, 26, 26, 24], [32, 28, 26, 26], [32, 41, 26, 39], [32, 43, 26, 41], [32, 44, 26, 42, "NaN"], [32, 47, 26, 45], [32, 49, 26, 47, "NaN"], [32, 52, 26, 50], [32, 53, 26, 51], [32, 54, 26, 52], [33, 6, 28, 4], [33, 10, 28, 8], [33, 11, 28, 9, "onRotationBegin"], [33, 26, 28, 24], [33, 29, 28, 27, "callbacks"], [33, 38, 28, 36], [33, 39, 28, 37, "onRotationBegin"], [33, 54, 28, 52], [34, 6, 29, 4], [34, 10, 29, 8], [34, 11, 29, 9, "onRotation"], [34, 21, 29, 19], [34, 24, 29, 22, "callbacks"], [34, 33, 29, 31], [34, 34, 29, 32, "onRotation"], [34, 44, 29, 42], [35, 6, 30, 4], [35, 10, 30, 8], [35, 11, 30, 9, "onRotationEnd"], [35, 24, 30, 22], [35, 27, 30, 25, "callbacks"], [35, 36, 30, 34], [35, 37, 30, 35, "onRotationEnd"], [35, 50, 30, 48], [36, 4, 31, 2], [37, 4, 33, 2, "updateCurrent"], [37, 17, 33, 15, "updateCurrent"], [37, 18, 33, 16, "event"], [37, 23, 33, 21], [37, 25, 33, 23, "tracker"], [37, 32, 33, 30], [37, 34, 33, 32], [38, 6, 34, 4], [38, 10, 34, 8], [38, 11, 34, 9, "previousTime"], [38, 23, 34, 21], [38, 26, 34, 24], [38, 30, 34, 28], [38, 31, 34, 29, "currentTime"], [38, 42, 34, 40], [39, 6, 35, 4], [39, 10, 35, 8], [39, 11, 35, 9, "currentTime"], [39, 22, 35, 20], [39, 25, 35, 23, "event"], [39, 30, 35, 28], [39, 31, 35, 29, "time"], [39, 35, 35, 33], [40, 6, 36, 4], [40, 12, 36, 10], [40, 13, 36, 11, "firstPointerID"], [40, 27, 36, 25], [40, 29, 36, 27, "secondPointerID"], [40, 44, 36, 42], [40, 45, 36, 43], [40, 48, 36, 46], [40, 52, 36, 50], [40, 53, 36, 51, "keyPointers"], [40, 64, 36, 62], [41, 6, 37, 4], [41, 12, 37, 10, "firstPointerCoords"], [41, 30, 37, 28], [41, 33, 37, 31, "tracker"], [41, 40, 37, 38], [41, 41, 37, 39, "getLastAbsoluteCoords"], [41, 62, 37, 60], [41, 63, 37, 61, "firstPointerID"], [41, 77, 37, 75], [41, 78, 37, 76], [42, 6, 38, 4], [42, 12, 38, 10, "secondPointerCoords"], [42, 31, 38, 29], [42, 34, 38, 32, "tracker"], [42, 41, 38, 39], [42, 42, 38, 40, "getLastAbsoluteCoords"], [42, 63, 38, 61], [42, 64, 38, 62, "secondPointerID"], [42, 79, 38, 77], [42, 80, 38, 78], [43, 6, 39, 4], [43, 12, 39, 10, "vectorX"], [43, 19, 39, 17], [43, 22, 39, 20, "secondPointerCoords"], [43, 41, 39, 39], [43, 42, 39, 40, "x"], [43, 43, 39, 41], [43, 46, 39, 44, "firstPointerCoords"], [43, 64, 39, 62], [43, 65, 39, 63, "x"], [43, 66, 39, 64], [44, 6, 40, 4], [44, 12, 40, 10, "vectorY"], [44, 19, 40, 17], [44, 22, 40, 20, "secondPointerCoords"], [44, 41, 40, 39], [44, 42, 40, 40, "y"], [44, 43, 40, 41], [44, 46, 40, 44, "firstPointerCoords"], [44, 64, 40, 62], [44, 65, 40, 63, "y"], [44, 66, 40, 64], [45, 6, 41, 4], [45, 10, 41, 8], [45, 11, 41, 9, "_anchorX"], [45, 19, 41, 17], [45, 22, 41, 20], [45, 23, 41, 21, "firstPointerCoords"], [45, 41, 41, 39], [45, 42, 41, 40, "x"], [45, 43, 41, 41], [45, 46, 41, 44, "secondPointerCoords"], [45, 65, 41, 63], [45, 66, 41, 64, "x"], [45, 67, 41, 65], [45, 71, 41, 69], [45, 72, 41, 70], [46, 6, 42, 4], [46, 10, 42, 8], [46, 11, 42, 9, "_anchorY"], [46, 19, 42, 17], [46, 22, 42, 20], [46, 23, 42, 21, "firstPointerCoords"], [46, 41, 42, 39], [46, 42, 42, 40, "y"], [46, 43, 42, 41], [46, 46, 42, 44, "secondPointerCoords"], [46, 65, 42, 63], [46, 66, 42, 64, "y"], [46, 67, 42, 65], [46, 71, 42, 69], [46, 72, 42, 70], [46, 73, 42, 71], [46, 74, 42, 72], [48, 6, 44, 4], [48, 12, 44, 10, "angle"], [48, 17, 44, 15], [48, 20, 44, 18], [48, 21, 44, 19, "Math"], [48, 25, 44, 23], [48, 26, 44, 24, "atan2"], [48, 31, 44, 29], [48, 32, 44, 30, "vectorY"], [48, 39, 44, 37], [48, 41, 44, 39, "vectorX"], [48, 48, 44, 46], [48, 49, 44, 47], [49, 6, 45, 4], [49, 10, 45, 8], [49, 11, 45, 9, "_rotation"], [49, 20, 45, 18], [49, 23, 45, 21, "Number"], [49, 29, 45, 27], [49, 30, 45, 28, "isNaN"], [49, 35, 45, 33], [49, 36, 45, 34], [49, 40, 45, 38], [49, 41, 45, 39, "previousAngle"], [49, 54, 45, 52], [49, 55, 45, 53], [49, 58, 45, 56], [49, 59, 45, 57], [49, 62, 45, 60], [49, 66, 45, 64], [49, 67, 45, 65, "previousAngle"], [49, 80, 45, 78], [49, 83, 45, 81, "angle"], [49, 88, 45, 86], [50, 6, 46, 4], [50, 10, 46, 8], [50, 11, 46, 9, "previousAngle"], [50, 24, 46, 22], [50, 27, 46, 25, "angle"], [50, 32, 46, 30], [51, 6, 48, 4], [51, 10, 48, 8], [51, 14, 48, 12], [51, 15, 48, 13, "rotation"], [51, 23, 48, 21], [51, 26, 48, 24, "Math"], [51, 30, 48, 28], [51, 31, 48, 29, "PI"], [51, 33, 48, 31], [51, 35, 48, 33], [52, 8, 49, 6], [52, 12, 49, 10], [52, 13, 49, 11, "_rotation"], [52, 22, 49, 20], [52, 26, 49, 24, "Math"], [52, 30, 49, 28], [52, 31, 49, 29, "PI"], [52, 33, 49, 31], [53, 6, 50, 4], [53, 7, 50, 5], [53, 13, 50, 11], [53, 17, 50, 15], [53, 21, 50, 19], [53, 22, 50, 20, "rotation"], [53, 30, 50, 28], [53, 33, 50, 31], [53, 34, 50, 32, "Math"], [53, 38, 50, 36], [53, 39, 50, 37, "PI"], [53, 41, 50, 39], [53, 43, 50, 41], [54, 8, 51, 6], [54, 12, 51, 10], [54, 13, 51, 11, "_rotation"], [54, 22, 51, 20], [54, 26, 51, 24, "Math"], [54, 30, 51, 28], [54, 31, 51, 29, "PI"], [54, 33, 51, 31], [55, 6, 52, 4], [56, 6, 54, 4], [56, 10, 54, 8], [56, 14, 54, 12], [56, 15, 54, 13, "rotation"], [56, 23, 54, 21], [56, 26, 54, 24, "Math"], [56, 30, 54, 28], [56, 31, 54, 29, "PI"], [56, 33, 54, 31], [56, 36, 54, 34], [56, 37, 54, 35], [56, 39, 54, 37], [57, 8, 55, 6], [57, 12, 55, 10], [57, 13, 55, 11, "_rotation"], [57, 22, 55, 20], [57, 26, 55, 24, "Math"], [57, 30, 55, 28], [57, 31, 55, 29, "PI"], [57, 33, 55, 31], [58, 6, 56, 4], [58, 7, 56, 5], [58, 13, 56, 11], [58, 17, 56, 15], [58, 21, 56, 19], [58, 22, 56, 20, "rotation"], [58, 30, 56, 28], [58, 33, 56, 31], [58, 34, 56, 32, "Math"], [58, 38, 56, 36], [58, 39, 56, 37, "PI"], [58, 41, 56, 39], [58, 44, 56, 42], [58, 45, 56, 43], [58, 47, 56, 45], [59, 8, 57, 6], [59, 12, 57, 10], [59, 13, 57, 11, "_rotation"], [59, 22, 57, 20], [59, 26, 57, 24, "Math"], [59, 30, 57, 28], [59, 31, 57, 29, "PI"], [59, 33, 57, 31], [60, 6, 58, 4], [61, 4, 59, 2], [62, 4, 61, 2, "finish"], [62, 10, 61, 8, "finish"], [62, 11, 61, 8], [62, 13, 61, 11], [63, 6, 62, 4], [63, 10, 62, 8], [63, 11, 62, 9], [63, 15, 62, 13], [63, 16, 62, 14, "isInProgress"], [63, 28, 62, 26], [63, 30, 62, 28], [64, 8, 63, 6], [65, 6, 64, 4], [66, 6, 66, 4], [66, 10, 66, 8], [66, 11, 66, 9, "isInProgress"], [66, 23, 66, 21], [66, 26, 66, 24], [66, 31, 66, 29], [67, 6, 67, 4], [67, 10, 67, 8], [67, 11, 67, 9, "keyPointers"], [67, 22, 67, 20], [67, 25, 67, 23], [67, 26, 67, 24, "NaN"], [67, 29, 67, 27], [67, 31, 67, 29, "NaN"], [67, 34, 67, 32], [67, 35, 67, 33], [68, 6, 68, 4], [68, 10, 68, 8], [68, 11, 68, 9, "onRotationEnd"], [68, 24, 68, 22], [68, 25, 68, 23], [68, 29, 68, 27], [68, 30, 68, 28], [69, 4, 69, 2], [70, 4, 71, 2, "set<PERSON>eyPointers"], [70, 18, 71, 16, "set<PERSON>eyPointers"], [70, 19, 71, 17, "tracker"], [70, 26, 71, 24], [70, 28, 71, 26], [71, 6, 72, 4], [71, 10, 72, 8], [71, 14, 72, 12], [71, 15, 72, 13, "keyPointers"], [71, 26, 72, 24], [71, 27, 72, 25], [71, 28, 72, 26], [71, 29, 72, 27], [71, 33, 72, 31], [71, 37, 72, 35], [71, 38, 72, 36, "keyPointers"], [71, 49, 72, 47], [71, 50, 72, 48], [71, 51, 72, 49], [71, 52, 72, 50], [71, 54, 72, 52], [72, 8, 73, 6], [73, 6, 74, 4], [74, 6, 76, 4], [74, 12, 76, 10, "pointerIDs"], [74, 22, 76, 20], [74, 25, 76, 23, "tracker"], [74, 32, 76, 30], [74, 33, 76, 31, "trackedPointers"], [74, 48, 76, 46], [74, 49, 76, 47, "keys"], [74, 53, 76, 51], [74, 54, 76, 52], [74, 55, 76, 53], [75, 6, 77, 4], [75, 10, 77, 8], [75, 11, 77, 9, "keyPointers"], [75, 22, 77, 20], [75, 23, 77, 21], [75, 24, 77, 22], [75, 25, 77, 23], [75, 28, 77, 26, "pointerIDs"], [75, 38, 77, 36], [75, 39, 77, 37, "next"], [75, 43, 77, 41], [75, 44, 77, 42], [75, 45, 77, 43], [75, 46, 77, 44, "value"], [75, 51, 77, 49], [76, 6, 78, 4], [76, 10, 78, 8], [76, 11, 78, 9, "keyPointers"], [76, 22, 78, 20], [76, 23, 78, 21], [76, 24, 78, 22], [76, 25, 78, 23], [76, 28, 78, 26, "pointerIDs"], [76, 38, 78, 36], [76, 39, 78, 37, "next"], [76, 43, 78, 41], [76, 44, 78, 42], [76, 45, 78, 43], [76, 46, 78, 44, "value"], [76, 51, 78, 49], [77, 4, 79, 2], [78, 4, 81, 2, "onTouchEvent"], [78, 16, 81, 14, "onTouchEvent"], [78, 17, 81, 15, "event"], [78, 22, 81, 20], [78, 24, 81, 22, "tracker"], [78, 31, 81, 29], [78, 33, 81, 31], [79, 6, 82, 4], [79, 14, 82, 12, "event"], [79, 19, 82, 17], [79, 20, 82, 18, "eventType"], [79, 29, 82, 27], [80, 8, 83, 6], [80, 13, 83, 11, "EventTypes"], [80, 35, 83, 21], [80, 36, 83, 22, "DOWN"], [80, 40, 83, 26], [81, 10, 84, 8], [81, 14, 84, 12], [81, 15, 84, 13, "isInProgress"], [81, 27, 84, 25], [81, 30, 84, 28], [81, 35, 84, 33], [82, 10, 85, 8], [83, 8, 87, 6], [83, 13, 87, 11, "EventTypes"], [83, 35, 87, 21], [83, 36, 87, 22, "ADDITIONAL_POINTER_DOWN"], [83, 59, 87, 45], [84, 10, 88, 8], [84, 14, 88, 12], [84, 18, 88, 16], [84, 19, 88, 17, "isInProgress"], [84, 31, 88, 29], [84, 33, 88, 31], [85, 12, 89, 10], [86, 10, 90, 8], [87, 10, 92, 8], [87, 14, 92, 12], [87, 15, 92, 13, "isInProgress"], [87, 27, 92, 25], [87, 30, 92, 28], [87, 34, 92, 32], [88, 10, 93, 8], [88, 14, 93, 12], [88, 15, 93, 13, "previousTime"], [88, 27, 93, 25], [88, 30, 93, 28, "event"], [88, 35, 93, 33], [88, 36, 93, 34, "time"], [88, 40, 93, 38], [89, 10, 94, 8], [89, 14, 94, 12], [89, 15, 94, 13, "previousAngle"], [89, 28, 94, 26], [89, 31, 94, 29, "NaN"], [89, 34, 94, 32], [90, 10, 95, 8], [90, 14, 95, 12], [90, 15, 95, 13, "set<PERSON>eyPointers"], [90, 29, 95, 27], [90, 30, 95, 28, "tracker"], [90, 37, 95, 35], [90, 38, 95, 36], [91, 10, 96, 8], [91, 14, 96, 12], [91, 15, 96, 13, "updateCurrent"], [91, 28, 96, 26], [91, 29, 96, 27, "event"], [91, 34, 96, 32], [91, 36, 96, 34, "tracker"], [91, 43, 96, 41], [91, 44, 96, 42], [92, 10, 97, 8], [92, 14, 97, 12], [92, 15, 97, 13, "onRotationBegin"], [92, 30, 97, 28], [92, 31, 97, 29], [92, 35, 97, 33], [92, 36, 97, 34], [93, 10, 98, 8], [94, 8, 100, 6], [94, 13, 100, 11, "EventTypes"], [94, 35, 100, 21], [94, 36, 100, 22, "MOVE"], [94, 40, 100, 26], [95, 10, 101, 8], [95, 14, 101, 12], [95, 15, 101, 13], [95, 19, 101, 17], [95, 20, 101, 18, "isInProgress"], [95, 32, 101, 30], [95, 34, 101, 32], [96, 12, 102, 10], [97, 10, 103, 8], [98, 10, 105, 8], [98, 14, 105, 12], [98, 15, 105, 13, "updateCurrent"], [98, 28, 105, 26], [98, 29, 105, 27, "event"], [98, 34, 105, 32], [98, 36, 105, 34, "tracker"], [98, 43, 105, 41], [98, 44, 105, 42], [99, 10, 106, 8], [99, 14, 106, 12], [99, 15, 106, 13, "onRotation"], [99, 25, 106, 23], [99, 26, 106, 24], [99, 30, 106, 28], [99, 31, 106, 29], [100, 10, 107, 8], [101, 8, 109, 6], [101, 13, 109, 11, "EventTypes"], [101, 35, 109, 21], [101, 36, 109, 22, "ADDITIONAL_POINTER_UP"], [101, 57, 109, 43], [102, 10, 110, 8], [102, 14, 110, 12], [102, 15, 110, 13], [102, 19, 110, 17], [102, 20, 110, 18, "isInProgress"], [102, 32, 110, 30], [102, 34, 110, 32], [103, 12, 111, 10], [104, 10, 112, 8], [105, 10, 114, 8], [105, 14, 114, 12], [105, 18, 114, 16], [105, 19, 114, 17, "keyPointers"], [105, 30, 114, 28], [105, 31, 114, 29, "indexOf"], [105, 38, 114, 36], [105, 39, 114, 37, "event"], [105, 44, 114, 42], [105, 45, 114, 43, "pointerId"], [105, 54, 114, 52], [105, 55, 114, 53], [105, 59, 114, 57], [105, 60, 114, 58], [105, 62, 114, 60], [106, 12, 115, 10], [106, 16, 115, 14], [106, 17, 115, 15, "finish"], [106, 23, 115, 21], [106, 24, 115, 22], [106, 25, 115, 23], [107, 10, 116, 8], [108, 10, 118, 8], [109, 8, 120, 6], [109, 13, 120, 11, "EventTypes"], [109, 35, 120, 21], [109, 36, 120, 22, "UP"], [109, 38, 120, 24], [110, 10, 121, 8], [110, 14, 121, 12], [110, 18, 121, 16], [110, 19, 121, 17, "isInProgress"], [110, 31, 121, 29], [110, 33, 121, 31], [111, 12, 122, 10], [111, 16, 122, 14], [111, 17, 122, 15, "finish"], [111, 23, 122, 21], [111, 24, 122, 22], [111, 25, 122, 23], [112, 10, 123, 8], [113, 10, 125, 8], [114, 6, 126, 4], [115, 6, 128, 4], [115, 13, 128, 11], [115, 17, 128, 15], [116, 4, 129, 2], [117, 4, 131, 2, "reset"], [117, 9, 131, 7, "reset"], [117, 10, 131, 7], [117, 12, 131, 10], [118, 6, 132, 4], [118, 10, 132, 8], [118, 11, 132, 9, "keyPointers"], [118, 22, 132, 20], [118, 25, 132, 23], [118, 26, 132, 24, "NaN"], [118, 29, 132, 27], [118, 31, 132, 29, "NaN"], [118, 34, 132, 32], [118, 35, 132, 33], [119, 6, 133, 4], [119, 10, 133, 8], [119, 11, 133, 9, "isInProgress"], [119, 23, 133, 21], [119, 26, 133, 24], [119, 31, 133, 29], [120, 4, 134, 2], [121, 4, 136, 2], [121, 8, 136, 6, "anchorX"], [121, 15, 136, 13, "anchorX"], [121, 16, 136, 13], [121, 18, 136, 16], [122, 6, 137, 4], [122, 13, 137, 11], [122, 17, 137, 15], [122, 18, 137, 16, "_anchorX"], [122, 26, 137, 24], [123, 4, 138, 2], [124, 4, 140, 2], [124, 8, 140, 6, "anchorY"], [124, 15, 140, 13, "anchorY"], [124, 16, 140, 13], [124, 18, 140, 16], [125, 6, 141, 4], [125, 13, 141, 11], [125, 17, 141, 15], [125, 18, 141, 16, "_anchorY"], [125, 26, 141, 24], [126, 4, 142, 2], [127, 4, 144, 2], [127, 8, 144, 6, "rotation"], [127, 16, 144, 14, "rotation"], [127, 17, 144, 14], [127, 19, 144, 17], [128, 6, 145, 4], [128, 13, 145, 11], [128, 17, 145, 15], [128, 18, 145, 16, "_rotation"], [128, 27, 145, 25], [129, 4, 146, 2], [130, 4, 148, 2], [130, 8, 148, 6, "<PERSON><PERSON><PERSON><PERSON>"], [130, 17, 148, 15, "<PERSON><PERSON><PERSON><PERSON>"], [130, 18, 148, 15], [130, 20, 148, 18], [131, 6, 149, 4], [131, 13, 149, 11], [131, 17, 149, 15], [131, 18, 149, 16, "currentTime"], [131, 29, 149, 27], [131, 32, 149, 30], [131, 36, 149, 34], [131, 37, 149, 35, "previousTime"], [131, 49, 149, 47], [132, 4, 150, 2], [133, 2, 152, 0], [134, 2, 152, 1, "exports"], [134, 9, 152, 1], [134, 10, 152, 1, "default"], [134, 17, 152, 1], [134, 20, 152, 1, "RotationGestureDetector"], [134, 43, 152, 1], [135, 0, 152, 1], [135, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "RotationGestureDetector", "constructor", "updateCurrent", "finish", "set<PERSON>eyPointers", "onTouchEvent", "reset", "get__anchorX", "get__anchorY", "get__rotation", "get__time<PERSON><PERSON><PERSON>"], "mappings": "AAA,iNC;eCG;ECC;GD0B;EEE;GF0B;EGE;GHQ;EIE;GJQ;EKE;GLgD;EME;GNG;EOE;GPE;EQE;GRE;ESE;GTE;EUE;GVE;CDE"}}, "type": "js/module"}]}