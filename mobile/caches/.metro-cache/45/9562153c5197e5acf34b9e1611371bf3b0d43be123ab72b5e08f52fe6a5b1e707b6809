{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 89}}], "key": "TuB5rvhhYFP7S1O2+poQUZyTlqI=", "exportNames": ["*"]}}, {"name": "../../Utilities/codegenNativeCommands", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 74}}], "key": "4G4LeVO/m4MSH9iel0oMRTsRk+g=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 669, "column": 15}, "end": {"line": 669, "column": 55}}, {"start": {"line": 675, "column": 15}, "end": {"line": 675, "column": 55}}, {"start": {"line": 682, "column": 30}, "end": {"line": 682, "column": 70}}, {"start": {"line": 684, "column": 15}, "end": {"line": 684, "column": 55}}, {"start": {"line": 687, "column": 15}, "end": {"line": 687, "column": 55}}, {"start": {"line": 695, "column": 21}, "end": {"line": 695, "column": 61}}, {"start": {"line": 702, "column": 27}, "end": {"line": 702, "column": 67}}, {"start": {"line": 725, "column": 15}, "end": {"line": 725, "column": 55}}, {"start": {"line": 729, "column": 15}, "end": {"line": 729, "column": 55}}, {"start": {"line": 731, "column": 27}, "end": {"line": 731, "column": 67}}, {"start": {"line": 736, "column": 15}, "end": {"line": 736, "column": 55}}, {"start": {"line": 739, "column": 30}, "end": {"line": 739, "column": 70}}], "key": "UxlPATDS52Ssj11/xyoTpBiN584=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = void 0;\n  var NativeComponentRegistry = _interopRequireWildcard(require(_dependencyMap[1], \"../../NativeComponent/NativeComponentRegistry\"));\n  var _codegenNativeCommands = _interopRequireDefault(require(_dependencyMap[2], \"../../Utilities/codegenNativeCommands\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var Commands = exports.Commands = (0, _codegenNativeCommands.default)({\n    supportedCommands: ['focus', 'blur', 'setTextAndSelection']\n  });\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: 'AndroidTextInput',\n    bubblingEventTypes: {\n      topBlur: {\n        phasedRegistrationNames: {\n          bubbled: 'onBlur',\n          captured: 'onBlurCapture'\n        }\n      },\n      topEndEditing: {\n        phasedRegistrationNames: {\n          bubbled: 'onEndEditing',\n          captured: 'onEndEditingCapture'\n        }\n      },\n      topFocus: {\n        phasedRegistrationNames: {\n          bubbled: 'onFocus',\n          captured: 'onFocusCapture'\n        }\n      },\n      topKeyPress: {\n        phasedRegistrationNames: {\n          bubbled: 'onKeyPress',\n          captured: 'onKeyPressCapture'\n        }\n      },\n      topSubmitEditing: {\n        phasedRegistrationNames: {\n          bubbled: 'onSubmitEditing',\n          captured: 'onSubmitEditingCapture'\n        }\n      }\n    },\n    directEventTypes: {\n      topScroll: {\n        registrationName: 'onScroll'\n      }\n    },\n    validAttributes: {\n      maxFontSizeMultiplier: true,\n      adjustsFontSizeToFit: true,\n      minimumFontScale: true,\n      autoFocus: true,\n      placeholder: true,\n      inlineImagePadding: true,\n      contextMenuHidden: true,\n      textShadowColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      maxLength: true,\n      selectTextOnFocus: true,\n      textShadowRadius: true,\n      underlineColorAndroid: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      textDecorationLine: true,\n      submitBehavior: true,\n      textAlignVertical: true,\n      fontStyle: true,\n      textShadowOffset: true,\n      selectionColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      selectionHandleColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      placeholderTextColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      importantForAutofill: true,\n      lineHeight: true,\n      textTransform: true,\n      returnKeyType: true,\n      keyboardType: true,\n      multiline: true,\n      color: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      autoComplete: true,\n      numberOfLines: true,\n      letterSpacing: true,\n      returnKeyLabel: true,\n      fontSize: true,\n      onKeyPress: true,\n      cursorColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      text: true,\n      showSoftInputOnFocus: true,\n      textAlign: true,\n      autoCapitalize: true,\n      autoCorrect: true,\n      caretHidden: true,\n      secureTextEntry: true,\n      textBreakStrategy: true,\n      onScroll: true,\n      onContentSizeChange: true,\n      disableFullscreenUI: true,\n      includeFontPadding: true,\n      fontWeight: true,\n      fontFamily: true,\n      allowFontScaling: true,\n      onSelectionChange: true,\n      mostRecentEventCount: true,\n      inlineImageLeft: true,\n      editable: true,\n      fontVariant: true,\n      borderBottomRightRadius: true,\n      borderBottomColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      borderRadius: true,\n      borderRightColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      borderColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      borderTopRightRadius: true,\n      borderStyle: true,\n      borderBottomLeftRadius: true,\n      borderLeftColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      },\n      borderTopLeftRadius: true,\n      borderTopColor: {\n        process: require(_dependencyMap[3], \"../../StyleSheet/processColor\").default\n      }\n    }\n  };\n  var AndroidTextInputNativeComponent = NativeComponentRegistry.get('AndroidTextInput', () => __INTERNAL_VIEW_CONFIG);\n  var _default = exports.default = AndroidTextInputNativeComponent;\n});", "lineCount": 146, "map": [[7, 2, 29, 0], [7, 6, 29, 0, "NativeComponentRegistry"], [7, 29, 29, 0], [7, 32, 29, 0, "_interopRequireWildcard"], [7, 55, 29, 0], [7, 56, 29, 0, "require"], [7, 63, 29, 0], [7, 64, 29, 0, "_dependencyMap"], [7, 78, 29, 0], [8, 2, 30, 0], [8, 6, 30, 0, "_codegenNativeCommands"], [8, 28, 30, 0], [8, 31, 30, 0, "_interopRequireDefault"], [8, 53, 30, 0], [8, 54, 30, 0, "require"], [8, 61, 30, 0], [8, 62, 30, 0, "_dependencyMap"], [8, 76, 30, 0], [9, 2, 30, 74], [9, 11, 30, 74, "_interopRequireWildcard"], [9, 35, 30, 74, "e"], [9, 36, 30, 74], [9, 38, 30, 74, "t"], [9, 39, 30, 74], [9, 68, 30, 74, "WeakMap"], [9, 75, 30, 74], [9, 81, 30, 74, "r"], [9, 82, 30, 74], [9, 89, 30, 74, "WeakMap"], [9, 96, 30, 74], [9, 100, 30, 74, "n"], [9, 101, 30, 74], [9, 108, 30, 74, "WeakMap"], [9, 115, 30, 74], [9, 127, 30, 74, "_interopRequireWildcard"], [9, 150, 30, 74], [9, 162, 30, 74, "_interopRequireWildcard"], [9, 163, 30, 74, "e"], [9, 164, 30, 74], [9, 166, 30, 74, "t"], [9, 167, 30, 74], [9, 176, 30, 74, "t"], [9, 177, 30, 74], [9, 181, 30, 74, "e"], [9, 182, 30, 74], [9, 186, 30, 74, "e"], [9, 187, 30, 74], [9, 188, 30, 74, "__esModule"], [9, 198, 30, 74], [9, 207, 30, 74, "e"], [9, 208, 30, 74], [9, 214, 30, 74, "o"], [9, 215, 30, 74], [9, 217, 30, 74, "i"], [9, 218, 30, 74], [9, 220, 30, 74, "f"], [9, 221, 30, 74], [9, 226, 30, 74, "__proto__"], [9, 235, 30, 74], [9, 243, 30, 74, "default"], [9, 250, 30, 74], [9, 252, 30, 74, "e"], [9, 253, 30, 74], [9, 270, 30, 74, "e"], [9, 271, 30, 74], [9, 294, 30, 74, "e"], [9, 295, 30, 74], [9, 320, 30, 74, "e"], [9, 321, 30, 74], [9, 330, 30, 74, "f"], [9, 331, 30, 74], [9, 337, 30, 74, "o"], [9, 338, 30, 74], [9, 341, 30, 74, "t"], [9, 342, 30, 74], [9, 345, 30, 74, "n"], [9, 346, 30, 74], [9, 349, 30, 74, "r"], [9, 350, 30, 74], [9, 358, 30, 74, "o"], [9, 359, 30, 74], [9, 360, 30, 74, "has"], [9, 363, 30, 74], [9, 364, 30, 74, "e"], [9, 365, 30, 74], [9, 375, 30, 74, "o"], [9, 376, 30, 74], [9, 377, 30, 74, "get"], [9, 380, 30, 74], [9, 381, 30, 74, "e"], [9, 382, 30, 74], [9, 385, 30, 74, "o"], [9, 386, 30, 74], [9, 387, 30, 74, "set"], [9, 390, 30, 74], [9, 391, 30, 74, "e"], [9, 392, 30, 74], [9, 394, 30, 74, "f"], [9, 395, 30, 74], [9, 409, 30, 74, "_t"], [9, 411, 30, 74], [9, 415, 30, 74, "e"], [9, 416, 30, 74], [9, 432, 30, 74, "_t"], [9, 434, 30, 74], [9, 441, 30, 74, "hasOwnProperty"], [9, 455, 30, 74], [9, 456, 30, 74, "call"], [9, 460, 30, 74], [9, 461, 30, 74, "e"], [9, 462, 30, 74], [9, 464, 30, 74, "_t"], [9, 466, 30, 74], [9, 473, 30, 74, "i"], [9, 474, 30, 74], [9, 478, 30, 74, "o"], [9, 479, 30, 74], [9, 482, 30, 74, "Object"], [9, 488, 30, 74], [9, 489, 30, 74, "defineProperty"], [9, 503, 30, 74], [9, 508, 30, 74, "Object"], [9, 514, 30, 74], [9, 515, 30, 74, "getOwnPropertyDescriptor"], [9, 539, 30, 74], [9, 540, 30, 74, "e"], [9, 541, 30, 74], [9, 543, 30, 74, "_t"], [9, 545, 30, 74], [9, 552, 30, 74, "i"], [9, 553, 30, 74], [9, 554, 30, 74, "get"], [9, 557, 30, 74], [9, 561, 30, 74, "i"], [9, 562, 30, 74], [9, 563, 30, 74, "set"], [9, 566, 30, 74], [9, 570, 30, 74, "o"], [9, 571, 30, 74], [9, 572, 30, 74, "f"], [9, 573, 30, 74], [9, 575, 30, 74, "_t"], [9, 577, 30, 74], [9, 579, 30, 74, "i"], [9, 580, 30, 74], [9, 584, 30, 74, "f"], [9, 585, 30, 74], [9, 586, 30, 74, "_t"], [9, 588, 30, 74], [9, 592, 30, 74, "e"], [9, 593, 30, 74], [9, 594, 30, 74, "_t"], [9, 596, 30, 74], [9, 607, 30, 74, "f"], [9, 608, 30, 74], [9, 613, 30, 74, "e"], [9, 614, 30, 74], [9, 616, 30, 74, "t"], [9, 617, 30, 74], [10, 2, 617, 7], [10, 6, 617, 13, "Commands"], [10, 14, 617, 37], [10, 17, 617, 37, "exports"], [10, 24, 617, 37], [10, 25, 617, 37, "Commands"], [10, 33, 617, 37], [10, 36, 617, 40], [10, 40, 617, 40, "codegenNativeCommands"], [10, 70, 617, 61], [10, 72, 617, 78], [11, 4, 618, 2, "supportedCommands"], [11, 21, 618, 19], [11, 23, 618, 21], [11, 24, 618, 22], [11, 31, 618, 29], [11, 33, 618, 31], [11, 39, 618, 37], [11, 41, 618, 39], [11, 62, 618, 60], [12, 2, 619, 0], [12, 3, 619, 1], [12, 4, 619, 2], [13, 2, 621, 7], [13, 6, 621, 13, "__INTERNAL_VIEW_CONFIG"], [13, 28, 621, 54], [13, 31, 621, 54, "exports"], [13, 38, 621, 54], [13, 39, 621, 54, "__INTERNAL_VIEW_CONFIG"], [13, 61, 621, 54], [13, 64, 621, 57], [14, 4, 622, 2, "uiViewClassName"], [14, 19, 622, 17], [14, 21, 622, 19], [14, 39, 622, 37], [15, 4, 623, 2, "bubblingEventTypes"], [15, 22, 623, 20], [15, 24, 623, 22], [16, 6, 624, 4, "topBlur"], [16, 13, 624, 11], [16, 15, 624, 13], [17, 8, 625, 6, "phasedRegistrationNames"], [17, 31, 625, 29], [17, 33, 625, 31], [18, 10, 626, 8, "bubbled"], [18, 17, 626, 15], [18, 19, 626, 17], [18, 27, 626, 25], [19, 10, 627, 8, "captured"], [19, 18, 627, 16], [19, 20, 627, 18], [20, 8, 628, 6], [21, 6, 629, 4], [21, 7, 629, 5], [22, 6, 630, 4, "topEndEditing"], [22, 19, 630, 17], [22, 21, 630, 19], [23, 8, 631, 6, "phasedRegistrationNames"], [23, 31, 631, 29], [23, 33, 631, 31], [24, 10, 632, 8, "bubbled"], [24, 17, 632, 15], [24, 19, 632, 17], [24, 33, 632, 31], [25, 10, 633, 8, "captured"], [25, 18, 633, 16], [25, 20, 633, 18], [26, 8, 634, 6], [27, 6, 635, 4], [27, 7, 635, 5], [28, 6, 636, 4, "topFocus"], [28, 14, 636, 12], [28, 16, 636, 14], [29, 8, 637, 6, "phasedRegistrationNames"], [29, 31, 637, 29], [29, 33, 637, 31], [30, 10, 638, 8, "bubbled"], [30, 17, 638, 15], [30, 19, 638, 17], [30, 28, 638, 26], [31, 10, 639, 8, "captured"], [31, 18, 639, 16], [31, 20, 639, 18], [32, 8, 640, 6], [33, 6, 641, 4], [33, 7, 641, 5], [34, 6, 642, 4, "topKeyPress"], [34, 17, 642, 15], [34, 19, 642, 17], [35, 8, 643, 6, "phasedRegistrationNames"], [35, 31, 643, 29], [35, 33, 643, 31], [36, 10, 644, 8, "bubbled"], [36, 17, 644, 15], [36, 19, 644, 17], [36, 31, 644, 29], [37, 10, 645, 8, "captured"], [37, 18, 645, 16], [37, 20, 645, 18], [38, 8, 646, 6], [39, 6, 647, 4], [39, 7, 647, 5], [40, 6, 648, 4, "topSubmitEditing"], [40, 22, 648, 20], [40, 24, 648, 22], [41, 8, 649, 6, "phasedRegistrationNames"], [41, 31, 649, 29], [41, 33, 649, 31], [42, 10, 650, 8, "bubbled"], [42, 17, 650, 15], [42, 19, 650, 17], [42, 36, 650, 34], [43, 10, 651, 8, "captured"], [43, 18, 651, 16], [43, 20, 651, 18], [44, 8, 652, 6], [45, 6, 653, 4], [46, 4, 654, 2], [46, 5, 654, 3], [47, 4, 655, 2, "directEventTypes"], [47, 20, 655, 18], [47, 22, 655, 20], [48, 6, 656, 4, "topScroll"], [48, 15, 656, 13], [48, 17, 656, 15], [49, 8, 657, 6, "registrationName"], [49, 24, 657, 22], [49, 26, 657, 24], [50, 6, 658, 4], [51, 4, 659, 2], [51, 5, 659, 3], [52, 4, 660, 2, "validAttributes"], [52, 19, 660, 17], [52, 21, 660, 19], [53, 6, 661, 4, "maxFontSizeMultiplier"], [53, 27, 661, 25], [53, 29, 661, 27], [53, 33, 661, 31], [54, 6, 662, 4, "adjustsFontSizeToFit"], [54, 26, 662, 24], [54, 28, 662, 26], [54, 32, 662, 30], [55, 6, 663, 4, "minimumFontScale"], [55, 22, 663, 20], [55, 24, 663, 22], [55, 28, 663, 26], [56, 6, 664, 4, "autoFocus"], [56, 15, 664, 13], [56, 17, 664, 15], [56, 21, 664, 19], [57, 6, 665, 4, "placeholder"], [57, 17, 665, 15], [57, 19, 665, 17], [57, 23, 665, 21], [58, 6, 666, 4, "inlineImagePadding"], [58, 24, 666, 22], [58, 26, 666, 24], [58, 30, 666, 28], [59, 6, 667, 4, "contextMenuHidden"], [59, 23, 667, 21], [59, 25, 667, 23], [59, 29, 667, 27], [60, 6, 668, 4, "textShadowColor"], [60, 21, 668, 19], [60, 23, 668, 21], [61, 8, 669, 6, "process"], [61, 15, 669, 13], [61, 17, 669, 15, "require"], [61, 24, 669, 22], [61, 25, 669, 22, "_dependencyMap"], [61, 39, 669, 22], [61, 75, 669, 54], [61, 76, 669, 55], [61, 77, 669, 56, "default"], [62, 6, 670, 4], [62, 7, 670, 5], [63, 6, 671, 4, "max<PERSON><PERSON><PERSON>"], [63, 15, 671, 13], [63, 17, 671, 15], [63, 21, 671, 19], [64, 6, 672, 4, "selectTextOnFocus"], [64, 23, 672, 21], [64, 25, 672, 23], [64, 29, 672, 27], [65, 6, 673, 4, "textShadowRadius"], [65, 22, 673, 20], [65, 24, 673, 22], [65, 28, 673, 26], [66, 6, 674, 4, "underlineColorAndroid"], [66, 27, 674, 25], [66, 29, 674, 27], [67, 8, 675, 6, "process"], [67, 15, 675, 13], [67, 17, 675, 15, "require"], [67, 24, 675, 22], [67, 25, 675, 22, "_dependencyMap"], [67, 39, 675, 22], [67, 75, 675, 54], [67, 76, 675, 55], [67, 77, 675, 56, "default"], [68, 6, 676, 4], [68, 7, 676, 5], [69, 6, 677, 4, "textDecorationLine"], [69, 24, 677, 22], [69, 26, 677, 24], [69, 30, 677, 28], [70, 6, 678, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [70, 20, 678, 18], [70, 22, 678, 20], [70, 26, 678, 24], [71, 6, 679, 4, "textAlignVertical"], [71, 23, 679, 21], [71, 25, 679, 23], [71, 29, 679, 27], [72, 6, 680, 4, "fontStyle"], [72, 15, 680, 13], [72, 17, 680, 15], [72, 21, 680, 19], [73, 6, 681, 4, "textShadowOffset"], [73, 22, 681, 20], [73, 24, 681, 22], [73, 28, 681, 26], [74, 6, 682, 4, "selectionColor"], [74, 20, 682, 18], [74, 22, 682, 20], [75, 8, 682, 21, "process"], [75, 15, 682, 28], [75, 17, 682, 30, "require"], [75, 24, 682, 37], [75, 25, 682, 37, "_dependencyMap"], [75, 39, 682, 37], [75, 75, 682, 69], [75, 76, 682, 70], [75, 77, 682, 71, "default"], [76, 6, 682, 78], [76, 7, 682, 79], [77, 6, 683, 4, "selectionHandleColor"], [77, 26, 683, 24], [77, 28, 683, 26], [78, 8, 684, 6, "process"], [78, 15, 684, 13], [78, 17, 684, 15, "require"], [78, 24, 684, 22], [78, 25, 684, 22, "_dependencyMap"], [78, 39, 684, 22], [78, 75, 684, 54], [78, 76, 684, 55], [78, 77, 684, 56, "default"], [79, 6, 685, 4], [79, 7, 685, 5], [80, 6, 686, 4, "placeholderTextColor"], [80, 26, 686, 24], [80, 28, 686, 26], [81, 8, 687, 6, "process"], [81, 15, 687, 13], [81, 17, 687, 15, "require"], [81, 24, 687, 22], [81, 25, 687, 22, "_dependencyMap"], [81, 39, 687, 22], [81, 75, 687, 54], [81, 76, 687, 55], [81, 77, 687, 56, "default"], [82, 6, 688, 4], [82, 7, 688, 5], [83, 6, 689, 4, "importantForAutofill"], [83, 26, 689, 24], [83, 28, 689, 26], [83, 32, 689, 30], [84, 6, 690, 4, "lineHeight"], [84, 16, 690, 14], [84, 18, 690, 16], [84, 22, 690, 20], [85, 6, 691, 4, "textTransform"], [85, 19, 691, 17], [85, 21, 691, 19], [85, 25, 691, 23], [86, 6, 692, 4, "returnKeyType"], [86, 19, 692, 17], [86, 21, 692, 19], [86, 25, 692, 23], [87, 6, 693, 4, "keyboardType"], [87, 18, 693, 16], [87, 20, 693, 18], [87, 24, 693, 22], [88, 6, 694, 4, "multiline"], [88, 15, 694, 13], [88, 17, 694, 15], [88, 21, 694, 19], [89, 6, 695, 4, "color"], [89, 11, 695, 9], [89, 13, 695, 11], [90, 8, 695, 12, "process"], [90, 15, 695, 19], [90, 17, 695, 21, "require"], [90, 24, 695, 28], [90, 25, 695, 28, "_dependencyMap"], [90, 39, 695, 28], [90, 75, 695, 60], [90, 76, 695, 61], [90, 77, 695, 62, "default"], [91, 6, 695, 69], [91, 7, 695, 70], [92, 6, 696, 4, "autoComplete"], [92, 18, 696, 16], [92, 20, 696, 18], [92, 24, 696, 22], [93, 6, 697, 4, "numberOfLines"], [93, 19, 697, 17], [93, 21, 697, 19], [93, 25, 697, 23], [94, 6, 698, 4, "letterSpacing"], [94, 19, 698, 17], [94, 21, 698, 19], [94, 25, 698, 23], [95, 6, 699, 4, "returnKeyLabel"], [95, 20, 699, 18], [95, 22, 699, 20], [95, 26, 699, 24], [96, 6, 700, 4, "fontSize"], [96, 14, 700, 12], [96, 16, 700, 14], [96, 20, 700, 18], [97, 6, 701, 4, "onKeyPress"], [97, 16, 701, 14], [97, 18, 701, 16], [97, 22, 701, 20], [98, 6, 702, 4, "cursorColor"], [98, 17, 702, 15], [98, 19, 702, 17], [99, 8, 702, 18, "process"], [99, 15, 702, 25], [99, 17, 702, 27, "require"], [99, 24, 702, 34], [99, 25, 702, 34, "_dependencyMap"], [99, 39, 702, 34], [99, 75, 702, 66], [99, 76, 702, 67], [99, 77, 702, 68, "default"], [100, 6, 702, 75], [100, 7, 702, 76], [101, 6, 703, 4, "text"], [101, 10, 703, 8], [101, 12, 703, 10], [101, 16, 703, 14], [102, 6, 704, 4, "showSoftInputOnFocus"], [102, 26, 704, 24], [102, 28, 704, 26], [102, 32, 704, 30], [103, 6, 705, 4, "textAlign"], [103, 15, 705, 13], [103, 17, 705, 15], [103, 21, 705, 19], [104, 6, 706, 4, "autoCapitalize"], [104, 20, 706, 18], [104, 22, 706, 20], [104, 26, 706, 24], [105, 6, 707, 4, "autoCorrect"], [105, 17, 707, 15], [105, 19, 707, 17], [105, 23, 707, 21], [106, 6, 708, 4, "caretHidden"], [106, 17, 708, 15], [106, 19, 708, 17], [106, 23, 708, 21], [107, 6, 709, 4, "secureTextEntry"], [107, 21, 709, 19], [107, 23, 709, 21], [107, 27, 709, 25], [108, 6, 710, 4, "textBreakStrategy"], [108, 23, 710, 21], [108, 25, 710, 23], [108, 29, 710, 27], [109, 6, 711, 4, "onScroll"], [109, 14, 711, 12], [109, 16, 711, 14], [109, 20, 711, 18], [110, 6, 712, 4, "onContentSizeChange"], [110, 25, 712, 23], [110, 27, 712, 25], [110, 31, 712, 29], [111, 6, 713, 4, "disableFullscreenUI"], [111, 25, 713, 23], [111, 27, 713, 25], [111, 31, 713, 29], [112, 6, 714, 4, "includeFontPadding"], [112, 24, 714, 22], [112, 26, 714, 24], [112, 30, 714, 28], [113, 6, 715, 4, "fontWeight"], [113, 16, 715, 14], [113, 18, 715, 16], [113, 22, 715, 20], [114, 6, 716, 4, "fontFamily"], [114, 16, 716, 14], [114, 18, 716, 16], [114, 22, 716, 20], [115, 6, 717, 4, "allowFontScaling"], [115, 22, 717, 20], [115, 24, 717, 22], [115, 28, 717, 26], [116, 6, 718, 4, "onSelectionChange"], [116, 23, 718, 21], [116, 25, 718, 23], [116, 29, 718, 27], [117, 6, 719, 4, "mostRecentEventCount"], [117, 26, 719, 24], [117, 28, 719, 26], [117, 32, 719, 30], [118, 6, 720, 4, "inlineImageLeft"], [118, 21, 720, 19], [118, 23, 720, 21], [118, 27, 720, 25], [119, 6, 721, 4, "editable"], [119, 14, 721, 12], [119, 16, 721, 14], [119, 20, 721, 18], [120, 6, 722, 4, "fontVariant"], [120, 17, 722, 15], [120, 19, 722, 17], [120, 23, 722, 21], [121, 6, 723, 4, "borderBottomRightRadius"], [121, 29, 723, 27], [121, 31, 723, 29], [121, 35, 723, 33], [122, 6, 724, 4, "borderBottomColor"], [122, 23, 724, 21], [122, 25, 724, 23], [123, 8, 725, 6, "process"], [123, 15, 725, 13], [123, 17, 725, 15, "require"], [123, 24, 725, 22], [123, 25, 725, 22, "_dependencyMap"], [123, 39, 725, 22], [123, 75, 725, 54], [123, 76, 725, 55], [123, 77, 725, 56, "default"], [124, 6, 726, 4], [124, 7, 726, 5], [125, 6, 727, 4, "borderRadius"], [125, 18, 727, 16], [125, 20, 727, 18], [125, 24, 727, 22], [126, 6, 728, 4, "borderRightColor"], [126, 22, 728, 20], [126, 24, 728, 22], [127, 8, 729, 6, "process"], [127, 15, 729, 13], [127, 17, 729, 15, "require"], [127, 24, 729, 22], [127, 25, 729, 22, "_dependencyMap"], [127, 39, 729, 22], [127, 75, 729, 54], [127, 76, 729, 55], [127, 77, 729, 56, "default"], [128, 6, 730, 4], [128, 7, 730, 5], [129, 6, 731, 4, "borderColor"], [129, 17, 731, 15], [129, 19, 731, 17], [130, 8, 731, 18, "process"], [130, 15, 731, 25], [130, 17, 731, 27, "require"], [130, 24, 731, 34], [130, 25, 731, 34, "_dependencyMap"], [130, 39, 731, 34], [130, 75, 731, 66], [130, 76, 731, 67], [130, 77, 731, 68, "default"], [131, 6, 731, 75], [131, 7, 731, 76], [132, 6, 732, 4, "borderTopRightRadius"], [132, 26, 732, 24], [132, 28, 732, 26], [132, 32, 732, 30], [133, 6, 733, 4, "borderStyle"], [133, 17, 733, 15], [133, 19, 733, 17], [133, 23, 733, 21], [134, 6, 734, 4, "borderBottomLeftRadius"], [134, 28, 734, 26], [134, 30, 734, 28], [134, 34, 734, 32], [135, 6, 735, 4, "borderLeftColor"], [135, 21, 735, 19], [135, 23, 735, 21], [136, 8, 736, 6, "process"], [136, 15, 736, 13], [136, 17, 736, 15, "require"], [136, 24, 736, 22], [136, 25, 736, 22, "_dependencyMap"], [136, 39, 736, 22], [136, 75, 736, 54], [136, 76, 736, 55], [136, 77, 736, 56, "default"], [137, 6, 737, 4], [137, 7, 737, 5], [138, 6, 738, 4, "borderTopLeftRadius"], [138, 25, 738, 23], [138, 27, 738, 25], [138, 31, 738, 29], [139, 6, 739, 4, "borderTopColor"], [139, 20, 739, 18], [139, 22, 739, 20], [140, 8, 739, 21, "process"], [140, 15, 739, 28], [140, 17, 739, 30, "require"], [140, 24, 739, 37], [140, 25, 739, 37, "_dependencyMap"], [140, 39, 739, 37], [140, 75, 739, 69], [140, 76, 739, 70], [140, 77, 739, 71, "default"], [141, 6, 739, 78], [142, 4, 740, 2], [143, 2, 741, 0], [143, 3, 741, 1], [144, 2, 743, 0], [144, 6, 743, 4, "AndroidTextInputNativeComponent"], [144, 37, 743, 35], [144, 40, 743, 38, "NativeComponentRegistry"], [144, 63, 743, 61], [144, 64, 743, 62, "get"], [144, 67, 743, 65], [144, 68, 744, 2], [144, 86, 744, 20], [144, 88, 745, 2], [144, 94, 745, 8, "__INTERNAL_VIEW_CONFIG"], [144, 116, 746, 0], [144, 117, 746, 1], [145, 2, 746, 2], [145, 6, 746, 2, "_default"], [145, 14, 746, 2], [145, 17, 746, 2, "exports"], [145, 24, 746, 2], [145, 25, 746, 2, "default"], [145, 32, 746, 2], [145, 35, 749, 17, "AndroidTextInputNativeComponent"], [145, 66, 749, 48], [146, 0, 749, 48], [146, 3]], "functionMap": {"names": ["<global>", "NativeComponentRegistry.get$argument_1"], "mappings": "AAA;ECwuB,4BD"}}, "type": "js/module"}]}