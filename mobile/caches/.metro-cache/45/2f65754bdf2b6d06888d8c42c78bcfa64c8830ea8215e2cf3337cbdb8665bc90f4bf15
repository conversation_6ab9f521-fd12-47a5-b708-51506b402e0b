{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 40, "index": 55}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.fonts = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var WEB_FONT_STACK = 'system-ui, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"';\n  var fonts = exports.fonts = _reactNative.Platform.select({\n    web: {\n      regular: {\n        fontFamily: WEB_FONT_STACK,\n        fontWeight: '400'\n      },\n      medium: {\n        fontFamily: WEB_FONT_STACK,\n        fontWeight: '500'\n      },\n      bold: {\n        fontFamily: WEB_FONT_STACK,\n        fontWeight: '600'\n      },\n      heavy: {\n        fontFamily: WEB_FONT_STACK,\n        fontWeight: '700'\n      }\n    },\n    ios: {\n      regular: {\n        fontFamily: 'System',\n        fontWeight: '400'\n      },\n      medium: {\n        fontFamily: 'System',\n        fontWeight: '500'\n      },\n      bold: {\n        fontFamily: 'System',\n        fontWeight: '600'\n      },\n      heavy: {\n        fontFamily: 'System',\n        fontWeight: '700'\n      }\n    },\n    default: {\n      regular: {\n        fontFamily: 'sans-serif',\n        fontWeight: 'normal'\n      },\n      medium: {\n        fontFamily: 'sans-serif-medium',\n        fontWeight: 'normal'\n      },\n      bold: {\n        fontFamily: 'sans-serif',\n        fontWeight: '600'\n      },\n      heavy: {\n        fontFamily: 'sans-serif',\n        fontWeight: '700'\n      }\n    }\n  });\n});", "lineCount": 66, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "fonts"], [7, 15, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_reactNative"], [8, 18, 3, 0], [8, 21, 3, 0, "require"], [8, 28, 3, 0], [8, 29, 3, 0, "_dependencyMap"], [8, 43, 3, 0], [9, 2, 4, 0], [9, 6, 4, 6, "WEB_FONT_STACK"], [9, 20, 4, 20], [9, 23, 4, 23], [9, 142, 4, 142], [10, 2, 5, 7], [10, 6, 5, 13, "fonts"], [10, 11, 5, 18], [10, 14, 5, 18, "exports"], [10, 21, 5, 18], [10, 22, 5, 18, "fonts"], [10, 27, 5, 18], [10, 30, 5, 21, "Platform"], [10, 51, 5, 29], [10, 52, 5, 30, "select"], [10, 58, 5, 36], [10, 59, 5, 37], [11, 4, 6, 2, "web"], [11, 7, 6, 5], [11, 9, 6, 7], [12, 6, 7, 4, "regular"], [12, 13, 7, 11], [12, 15, 7, 13], [13, 8, 8, 6, "fontFamily"], [13, 18, 8, 16], [13, 20, 8, 18, "WEB_FONT_STACK"], [13, 34, 8, 32], [14, 8, 9, 6, "fontWeight"], [14, 18, 9, 16], [14, 20, 9, 18], [15, 6, 10, 4], [15, 7, 10, 5], [16, 6, 11, 4, "medium"], [16, 12, 11, 10], [16, 14, 11, 12], [17, 8, 12, 6, "fontFamily"], [17, 18, 12, 16], [17, 20, 12, 18, "WEB_FONT_STACK"], [17, 34, 12, 32], [18, 8, 13, 6, "fontWeight"], [18, 18, 13, 16], [18, 20, 13, 18], [19, 6, 14, 4], [19, 7, 14, 5], [20, 6, 15, 4, "bold"], [20, 10, 15, 8], [20, 12, 15, 10], [21, 8, 16, 6, "fontFamily"], [21, 18, 16, 16], [21, 20, 16, 18, "WEB_FONT_STACK"], [21, 34, 16, 32], [22, 8, 17, 6, "fontWeight"], [22, 18, 17, 16], [22, 20, 17, 18], [23, 6, 18, 4], [23, 7, 18, 5], [24, 6, 19, 4, "heavy"], [24, 11, 19, 9], [24, 13, 19, 11], [25, 8, 20, 6, "fontFamily"], [25, 18, 20, 16], [25, 20, 20, 18, "WEB_FONT_STACK"], [25, 34, 20, 32], [26, 8, 21, 6, "fontWeight"], [26, 18, 21, 16], [26, 20, 21, 18], [27, 6, 22, 4], [28, 4, 23, 2], [28, 5, 23, 3], [29, 4, 24, 2, "ios"], [29, 7, 24, 5], [29, 9, 24, 7], [30, 6, 25, 4, "regular"], [30, 13, 25, 11], [30, 15, 25, 13], [31, 8, 26, 6, "fontFamily"], [31, 18, 26, 16], [31, 20, 26, 18], [31, 28, 26, 26], [32, 8, 27, 6, "fontWeight"], [32, 18, 27, 16], [32, 20, 27, 18], [33, 6, 28, 4], [33, 7, 28, 5], [34, 6, 29, 4, "medium"], [34, 12, 29, 10], [34, 14, 29, 12], [35, 8, 30, 6, "fontFamily"], [35, 18, 30, 16], [35, 20, 30, 18], [35, 28, 30, 26], [36, 8, 31, 6, "fontWeight"], [36, 18, 31, 16], [36, 20, 31, 18], [37, 6, 32, 4], [37, 7, 32, 5], [38, 6, 33, 4, "bold"], [38, 10, 33, 8], [38, 12, 33, 10], [39, 8, 34, 6, "fontFamily"], [39, 18, 34, 16], [39, 20, 34, 18], [39, 28, 34, 26], [40, 8, 35, 6, "fontWeight"], [40, 18, 35, 16], [40, 20, 35, 18], [41, 6, 36, 4], [41, 7, 36, 5], [42, 6, 37, 4, "heavy"], [42, 11, 37, 9], [42, 13, 37, 11], [43, 8, 38, 6, "fontFamily"], [43, 18, 38, 16], [43, 20, 38, 18], [43, 28, 38, 26], [44, 8, 39, 6, "fontWeight"], [44, 18, 39, 16], [44, 20, 39, 18], [45, 6, 40, 4], [46, 4, 41, 2], [46, 5, 41, 3], [47, 4, 42, 2, "default"], [47, 11, 42, 9], [47, 13, 42, 11], [48, 6, 43, 4, "regular"], [48, 13, 43, 11], [48, 15, 43, 13], [49, 8, 44, 6, "fontFamily"], [49, 18, 44, 16], [49, 20, 44, 18], [49, 32, 44, 30], [50, 8, 45, 6, "fontWeight"], [50, 18, 45, 16], [50, 20, 45, 18], [51, 6, 46, 4], [51, 7, 46, 5], [52, 6, 47, 4, "medium"], [52, 12, 47, 10], [52, 14, 47, 12], [53, 8, 48, 6, "fontFamily"], [53, 18, 48, 16], [53, 20, 48, 18], [53, 39, 48, 37], [54, 8, 49, 6, "fontWeight"], [54, 18, 49, 16], [54, 20, 49, 18], [55, 6, 50, 4], [55, 7, 50, 5], [56, 6, 51, 4, "bold"], [56, 10, 51, 8], [56, 12, 51, 10], [57, 8, 52, 6, "fontFamily"], [57, 18, 52, 16], [57, 20, 52, 18], [57, 32, 52, 30], [58, 8, 53, 6, "fontWeight"], [58, 18, 53, 16], [58, 20, 53, 18], [59, 6, 54, 4], [59, 7, 54, 5], [60, 6, 55, 4, "heavy"], [60, 11, 55, 9], [60, 13, 55, 11], [61, 8, 56, 6, "fontFamily"], [61, 18, 56, 16], [61, 20, 56, 18], [61, 32, 56, 30], [62, 8, 57, 6, "fontWeight"], [62, 18, 57, 16], [62, 20, 57, 18], [63, 6, 58, 4], [64, 4, 59, 2], [65, 2, 60, 0], [65, 3, 60, 1], [65, 4, 60, 2], [66, 0, 60, 3], [66, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}