{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Core/Devtools/getDevServer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 77, "index": 77}}], "key": "AJW/Fuh82c1DsneAQ2yLbLao9Yg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _getDevServer = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Core/Devtools/getDevServer\"));\n  var _default = exports.default = _getDevServer.default;\n});", "lineCount": 9, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_getDevServer"], [7, 19, 1, 0], [7, 22, 1, 0, "_interopRequireDefault"], [7, 44, 1, 0], [7, 45, 1, 0, "require"], [7, 52, 1, 0], [7, 53, 1, 0, "_dependencyMap"], [7, 67, 1, 0], [8, 2, 1, 77], [8, 6, 1, 77, "_default"], [8, 14, 1, 77], [8, 17, 1, 77, "exports"], [8, 24, 1, 77], [8, 25, 1, 77, "default"], [8, 32, 1, 77], [8, 35, 3, 15, "getDevServer"], [8, 56, 3, 27], [9, 0, 3, 27], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}