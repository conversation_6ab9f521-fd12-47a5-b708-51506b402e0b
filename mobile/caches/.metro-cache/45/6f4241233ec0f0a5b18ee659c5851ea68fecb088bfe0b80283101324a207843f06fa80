{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectSpread2", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 65, "index": 65}}], "key": "SfRhzMj3Ex6qA89WTFEUm9Lj49A=", "exportNames": ["*"]}}, {"name": "../../../exports/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 276}, "end": {"line": 12, "column": 49, "index": 325}}], "key": "iEIJMkhlCtHWoBgLjJAJYcWbRuk=", "exportNames": ["*"]}}, {"name": "./components/AnimatedFlatList", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 326}, "end": {"line": 13, "column": 53, "index": 379}}], "key": "LlKRDt/bJf/7o8A134HQ7QJte2o=", "exportNames": ["*"]}}, {"name": "./components/AnimatedImage", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 380}, "end": {"line": 14, "column": 47, "index": 427}}], "key": "h5dct1GZXM8T9xShRIuwjYyDr88=", "exportNames": ["*"]}}, {"name": "./components/AnimatedScrollView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 428}, "end": {"line": 15, "column": 57, "index": 485}}], "key": "iVuYvFgBlrn+bHa2LT2JtJ/2fkY=", "exportNames": ["*"]}}, {"name": "./components/AnimatedSectionList", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 486}, "end": {"line": 16, "column": 59, "index": 545}}], "key": "A6I7cjELsr4sAjCm/PTKgG0ZoTQ=", "exportNames": ["*"]}}, {"name": "./components/AnimatedText", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 546}, "end": {"line": 17, "column": 45, "index": 591}}], "key": "FHdj3VXZq6pxXBM4NlEW+ClpVjE=", "exportNames": ["*"]}}, {"name": "./components/AnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 592}, "end": {"line": 18, "column": 45, "index": 637}}], "key": "xWOtbegFxYHmpQSl9bZDf+woVVw=", "exportNames": ["*"]}}, {"name": "./AnimatedMock", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 638}, "end": {"line": 19, "column": 42, "index": 680}}], "key": "ZjSgsgn1QwoNkGUccNerYPclF/Q=", "exportNames": ["*"]}}, {"name": "./AnimatedImplementation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 681}, "end": {"line": 20, "column": 62, "index": 743}}], "key": "MmE1c5G8MIzpHpSfKBLhd7ZPBbI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectSpread2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectSpread2\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"../../../exports/Platform\"));\n  var _AnimatedFlatList = _interopRequireDefault(require(_dependencyMap[3], \"./components/AnimatedFlatList\"));\n  var _AnimatedImage = _interopRequireDefault(require(_dependencyMap[4], \"./components/AnimatedImage\"));\n  var _AnimatedScrollView = _interopRequireDefault(require(_dependencyMap[5], \"./components/AnimatedScrollView\"));\n  var _AnimatedSectionList = _interopRequireDefault(require(_dependencyMap[6], \"./components/AnimatedSectionList\"));\n  var _AnimatedText = _interopRequireDefault(require(_dependencyMap[7], \"./components/AnimatedText\"));\n  var _AnimatedView = _interopRequireDefault(require(_dependencyMap[8], \"./components/AnimatedView\"));\n  var _AnimatedMock = _interopRequireDefault(require(_dependencyMap[9], \"./AnimatedMock\"));\n  var _AnimatedImplementation = _interopRequireDefault(require(_dependencyMap[10], \"./AnimatedImplementation\"));\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  var Animated = _Platform.default.isTesting ? _AnimatedMock.default : _AnimatedImplementation.default;\n  var _default = exports.default = (0, _objectSpread2.default)({\n    FlatList: _AnimatedFlatList.default,\n    Image: _AnimatedImage.default,\n    ScrollView: _AnimatedScrollView.default,\n    SectionList: _AnimatedSectionList.default,\n    Text: _AnimatedText.default,\n    View: _AnimatedView.default\n  }, Animated);\n});", "lineCount": 36, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_objectSpread2"], [7, 20, 1, 0], [7, 23, 1, 0, "_interopRequireDefault"], [7, 45, 1, 0], [7, 46, 1, 0, "require"], [7, 53, 1, 0], [7, 54, 1, 0, "_dependencyMap"], [7, 68, 1, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_Platform"], [8, 15, 12, 0], [8, 18, 12, 0, "_interopRequireDefault"], [8, 40, 12, 0], [8, 41, 12, 0, "require"], [8, 48, 12, 0], [8, 49, 12, 0, "_dependencyMap"], [8, 63, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_AnimatedFlatList"], [9, 23, 13, 0], [9, 26, 13, 0, "_interopRequireDefault"], [9, 48, 13, 0], [9, 49, 13, 0, "require"], [9, 56, 13, 0], [9, 57, 13, 0, "_dependencyMap"], [9, 71, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_AnimatedImage"], [10, 20, 14, 0], [10, 23, 14, 0, "_interopRequireDefault"], [10, 45, 14, 0], [10, 46, 14, 0, "require"], [10, 53, 14, 0], [10, 54, 14, 0, "_dependencyMap"], [10, 68, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_AnimatedScrollView"], [11, 25, 15, 0], [11, 28, 15, 0, "_interopRequireDefault"], [11, 50, 15, 0], [11, 51, 15, 0, "require"], [11, 58, 15, 0], [11, 59, 15, 0, "_dependencyMap"], [11, 73, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "_AnimatedSectionList"], [12, 26, 16, 0], [12, 29, 16, 0, "_interopRequireDefault"], [12, 51, 16, 0], [12, 52, 16, 0, "require"], [12, 59, 16, 0], [12, 60, 16, 0, "_dependencyMap"], [12, 74, 16, 0], [13, 2, 17, 0], [13, 6, 17, 0, "_AnimatedText"], [13, 19, 17, 0], [13, 22, 17, 0, "_interopRequireDefault"], [13, 44, 17, 0], [13, 45, 17, 0, "require"], [13, 52, 17, 0], [13, 53, 17, 0, "_dependencyMap"], [13, 67, 17, 0], [14, 2, 18, 0], [14, 6, 18, 0, "_<PERSON><PERSON><PERSON><PERSON>"], [14, 19, 18, 0], [14, 22, 18, 0, "_interopRequireDefault"], [14, 44, 18, 0], [14, 45, 18, 0, "require"], [14, 52, 18, 0], [14, 53, 18, 0, "_dependencyMap"], [14, 67, 18, 0], [15, 2, 19, 0], [15, 6, 19, 0, "_AnimatedMock"], [15, 19, 19, 0], [15, 22, 19, 0, "_interopRequireDefault"], [15, 44, 19, 0], [15, 45, 19, 0, "require"], [15, 52, 19, 0], [15, 53, 19, 0, "_dependencyMap"], [15, 67, 19, 0], [16, 2, 20, 0], [16, 6, 20, 0, "_AnimatedImplementation"], [16, 29, 20, 0], [16, 32, 20, 0, "_interopRequireDefault"], [16, 54, 20, 0], [16, 55, 20, 0, "require"], [16, 62, 20, 0], [16, 63, 20, 0, "_dependencyMap"], [16, 77, 20, 0], [17, 2, 2, 0], [18, 0, 3, 0], [19, 0, 4, 0], [20, 0, 5, 0], [21, 0, 6, 0], [22, 0, 7, 0], [23, 0, 8, 0], [24, 0, 9, 0], [25, 0, 10, 0], [27, 2, 21, 0], [27, 6, 21, 4, "Animated"], [27, 14, 21, 12], [27, 17, 21, 15, "Platform"], [27, 34, 21, 23], [27, 35, 21, 24, "isTesting"], [27, 44, 21, 33], [27, 47, 21, 36, "AnimatedMock"], [27, 68, 21, 48], [27, 71, 21, 51, "AnimatedImplementation"], [27, 102, 21, 73], [28, 2, 21, 74], [28, 6, 21, 74, "_default"], [28, 14, 21, 74], [28, 17, 21, 74, "exports"], [28, 24, 21, 74], [28, 25, 21, 74, "default"], [28, 32, 21, 74], [28, 35, 22, 15], [28, 39, 22, 15, "_objectSpread"], [28, 61, 22, 28], [28, 63, 22, 29], [29, 4, 23, 2, "FlatList"], [29, 12, 23, 10], [29, 14, 23, 2, "FlatList"], [29, 39, 23, 10], [30, 4, 24, 2, "Image"], [30, 9, 24, 7], [30, 11, 24, 2, "Image"], [30, 33, 24, 7], [31, 4, 25, 2, "ScrollView"], [31, 14, 25, 12], [31, 16, 25, 2, "ScrollView"], [31, 43, 25, 12], [32, 4, 26, 2, "SectionList"], [32, 15, 26, 13], [32, 17, 26, 2, "SectionList"], [32, 45, 26, 13], [33, 4, 27, 2, "Text"], [33, 8, 27, 6], [33, 10, 27, 2, "Text"], [33, 31, 27, 6], [34, 4, 28, 2, "View"], [34, 8, 28, 6], [34, 10, 28, 2, "View"], [35, 2, 29, 0], [35, 3, 29, 1], [35, 5, 29, 3, "Animated"], [35, 13, 29, 11], [35, 14, 29, 12], [36, 0, 29, 12], [36, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}