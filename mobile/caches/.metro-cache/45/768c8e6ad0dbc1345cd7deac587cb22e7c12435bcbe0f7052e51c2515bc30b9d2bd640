{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 63, "index": 78}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 79}, "end": {"line": 4, "column": 31, "index": 110}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 111}, "end": {"line": 5, "column": 75, "index": 186}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../assets/back-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 187}, "end": {"line": 6, "column": 47, "index": 234}}], "key": "HkeccI6hsuVTlj25dEjFpK2xoJM=", "exportNames": ["*"]}}, {"name": "../assets/back-icon-mask.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 235}, "end": {"line": 7, "column": 56, "index": 291}}], "key": "AmHFzlS4CVlJZw6ZbDJ43fmMtBk=", "exportNames": ["*"]}}, {"name": "../MaskedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 292}, "end": {"line": 8, "column": 43, "index": 335}}], "key": "BHKqLDT9VAjLwsU321BC8dwD7Us=", "exportNames": ["*"]}}, {"name": "./HeaderButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 336}, "end": {"line": 9, "column": 49, "index": 385}}], "key": "5Mfp2bWqztZ2HFy80uJBbvbN6HA=", "exportNames": ["*"]}}, {"name": "./HeaderIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 386}, "end": {"line": 10, "column": 58, "index": 444}}], "key": "0JPASIZzwd0DulPaj/kDrorllj8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 445}, "end": {"line": 11, "column": 63, "index": 508}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderBackButton = HeaderBackButton;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _backIcon = _interopRequireDefault(require(_dependencyMap[5], \"../assets/back-icon.png\"));\n  var _backIconMask = _interopRequireDefault(require(_dependencyMap[6], \"../assets/back-icon-mask.png\"));\n  var _MaskedView = require(_dependencyMap[7], \"../MaskedView\");\n  var _HeaderButton = require(_dependencyMap[8], \"./HeaderButton.js\");\n  var _HeaderIcon = require(_dependencyMap[9], \"./HeaderIcon.js\");\n  var _jsxRuntime = require(_dependencyMap[10], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function HeaderBackButton(_ref) {\n    var disabled = _ref.disabled,\n      allowFontScaling = _ref.allowFontScaling,\n      backImage = _ref.backImage,\n      label = _ref.label,\n      labelStyle = _ref.labelStyle,\n      _ref$displayMode = _ref.displayMode,\n      displayMode = _ref$displayMode === void 0 ? _reactNative.Platform.OS === 'ios' ? 'default' : 'minimal' : _ref$displayMode,\n      onLabelLayout = _ref.onLabelLayout,\n      onPress = _ref.onPress,\n      pressColor = _ref.pressColor,\n      pressOpacity = _ref.pressOpacity,\n      screenLayout = _ref.screenLayout,\n      tintColor = _ref.tintColor,\n      titleLayout = _ref.titleLayout,\n      _ref$truncatedLabel = _ref.truncatedLabel,\n      truncatedLabel = _ref$truncatedLabel === void 0 ? 'Back' : _ref$truncatedLabel,\n      _ref$accessibilityLab = _ref.accessibilityLabel,\n      accessibilityLabel = _ref$accessibilityLab === void 0 ? label && label !== 'Back' ? `${label}, back` : 'Go back' : _ref$accessibilityLab,\n      testID = _ref.testID,\n      style = _ref.style,\n      href = _ref.href;\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors,\n      fonts = _useTheme.fonts;\n    var _useLocale = (0, _native.useLocale)(),\n      direction = _useLocale.direction;\n    var _React$useState = React.useState(null),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      labelWidth = _React$useState2[0],\n      setLabelWidth = _React$useState2[1];\n    var _React$useState3 = React.useState(null),\n      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n      truncatedLabelWidth = _React$useState4[0],\n      setTruncatedLabelWidth = _React$useState4[1];\n    var renderBackImage = () => {\n      if (backImage) {\n        return backImage({\n          tintColor: tintColor ?? colors.text\n        });\n      } else {\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderIcon.HeaderIcon, {\n          source: _backIcon.default,\n          tintColor: tintColor,\n          style: [styles.icon, displayMode !== 'minimal' && styles.iconWithLabel]\n        });\n      }\n    };\n    var renderLabel = () => {\n      if (displayMode === 'minimal') {\n        return null;\n      }\n      var availableSpace = titleLayout && screenLayout ? (screenLayout.width - titleLayout.width) / 2 - (ICON_WIDTH + _HeaderIcon.ICON_MARGIN) : null;\n      var potentialLabelText = displayMode === 'default' ? label : truncatedLabel;\n      var finalLabelText = availableSpace && labelWidth && truncatedLabelWidth ? availableSpace > labelWidth ? potentialLabelText : availableSpace > truncatedLabelWidth ? truncatedLabel : null : potentialLabelText;\n      var commonStyle = [fonts.regular, styles.label, labelStyle];\n      var hiddenStyle = [commonStyle, {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        opacity: 0\n      }];\n      var labelElement = /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {\n        style: styles.labelWrapper,\n        children: [label && displayMode === 'default' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.Text, {\n          style: hiddenStyle,\n          numberOfLines: 1,\n          onLayout: e => setLabelWidth(e.nativeEvent.layout.width),\n          children: label\n        }) : null, truncatedLabel ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.Text, {\n          style: hiddenStyle,\n          numberOfLines: 1,\n          onLayout: e => setTruncatedLabelWidth(e.nativeEvent.layout.width),\n          children: truncatedLabel\n        }) : null, finalLabelText ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.Text, {\n          accessible: false,\n          onLayout: onLabelLayout,\n          style: [tintColor ? {\n            color: tintColor\n          } : null, commonStyle],\n          numberOfLines: 1,\n          allowFontScaling: !!allowFontScaling,\n          children: finalLabelText\n        }) : null]\n      });\n      if (backImage || _reactNative.Platform.OS !== 'ios') {\n        // When a custom backimage is specified, we can't mask the label\n        // Otherwise there might be weird effect due to our mask not being the same as the image\n        return labelElement;\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_MaskedView.MaskedView, {\n        maskElement: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {\n          style: [styles.iconMaskContainer,\n          // Extend the mask to the center of the screen so that label isn't clipped during animation\n          screenLayout ? {\n            minWidth: screenLayout.width / 2 - 27\n          } : null],\n          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Image, {\n            source: _backIconMask.default,\n            resizeMode: \"contain\",\n            style: [styles.iconMask, direction === 'rtl' && styles.flip]\n          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n            style: styles.iconMaskFillerRect\n          })]\n        }),\n        children: labelElement\n      });\n    };\n    var handlePress = () => {\n      if (onPress) {\n        requestAnimationFrame(() => onPress());\n      }\n    };\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderButton.HeaderButton, {\n      disabled: disabled,\n      href: href,\n      accessibilityLabel: accessibilityLabel,\n      testID: testID,\n      onPress: handlePress,\n      pressColor: pressColor,\n      pressOpacity: pressOpacity,\n      style: [styles.container, style],\n      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {\n        children: [renderBackImage(), renderLabel()]\n      })\n    });\n  }\n  var ICON_WIDTH = _reactNative.Platform.OS === 'ios' ? 13 : 24;\n  var ICON_MARGIN_END = _reactNative.Platform.OS === 'ios' ? 22 : 3;\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      paddingHorizontal: 0,\n      minWidth: _reactNative.StyleSheet.hairlineWidth,\n      // Avoid collapsing when title is long\n      ..._reactNative.Platform.select({\n        ios: null,\n        default: {\n          marginVertical: 3,\n          marginHorizontal: 11\n        }\n      })\n    },\n    label: {\n      fontSize: 17,\n      // Title and back label are a bit different width due to title being bold\n      // Adjusting the letterSpacing makes them coincide better\n      letterSpacing: 0.35\n    },\n    labelWrapper: {\n      // These styles will make sure that the label doesn't fill the available space\n      // Otherwise it messes with the measurement of the label\n      flexDirection: 'row',\n      alignItems: 'flex-start',\n      marginEnd: _HeaderIcon.ICON_MARGIN\n    },\n    icon: {\n      width: ICON_WIDTH,\n      marginEnd: ICON_MARGIN_END\n    },\n    iconWithLabel: _reactNative.Platform.OS === 'ios' ? {\n      marginEnd: 6\n    } : {},\n    iconMaskContainer: {\n      flex: 1,\n      flexDirection: 'row',\n      justifyContent: 'center'\n    },\n    iconMaskFillerRect: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    iconMask: {\n      height: 21,\n      width: 13,\n      marginStart: -14.5,\n      marginVertical: 12,\n      alignSelf: 'center'\n    },\n    flip: {\n      transform: 'scaleX(-1)'\n    }\n  });\n});", "lineCount": 202, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 26, 1, 13], [8, 29, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 45, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_native"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "React"], [11, 11, 4, 0], [11, 14, 4, 0, "_interopRequireWildcard"], [11, 37, 4, 0], [11, 38, 4, 0, "require"], [11, 45, 4, 0], [11, 46, 4, 0, "_dependencyMap"], [11, 60, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_reactNative"], [12, 18, 5, 0], [12, 21, 5, 0, "require"], [12, 28, 5, 0], [12, 29, 5, 0, "_dependencyMap"], [12, 43, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_backIcon"], [13, 15, 6, 0], [13, 18, 6, 0, "_interopRequireDefault"], [13, 40, 6, 0], [13, 41, 6, 0, "require"], [13, 48, 6, 0], [13, 49, 6, 0, "_dependencyMap"], [13, 63, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_backIconMask"], [14, 19, 7, 0], [14, 22, 7, 0, "_interopRequireDefault"], [14, 44, 7, 0], [14, 45, 7, 0, "require"], [14, 52, 7, 0], [14, 53, 7, 0, "_dependencyMap"], [14, 67, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 17, 8, 0], [15, 20, 8, 0, "require"], [15, 27, 8, 0], [15, 28, 8, 0, "_dependencyMap"], [15, 42, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 19, 9, 0], [16, 22, 9, 0, "require"], [16, 29, 9, 0], [16, 30, 9, 0, "_dependencyMap"], [16, 44, 9, 0], [17, 2, 10, 0], [17, 6, 10, 0, "_HeaderIcon"], [17, 17, 10, 0], [17, 20, 10, 0, "require"], [17, 27, 10, 0], [17, 28, 10, 0, "_dependencyMap"], [17, 42, 10, 0], [18, 2, 11, 0], [18, 6, 11, 0, "_jsxRuntime"], [18, 17, 11, 0], [18, 20, 11, 0, "require"], [18, 27, 11, 0], [18, 28, 11, 0, "_dependencyMap"], [18, 42, 11, 0], [19, 2, 11, 63], [19, 11, 11, 63, "_interopRequireWildcard"], [19, 35, 11, 63, "e"], [19, 36, 11, 63], [19, 38, 11, 63, "t"], [19, 39, 11, 63], [19, 68, 11, 63, "WeakMap"], [19, 75, 11, 63], [19, 81, 11, 63, "r"], [19, 82, 11, 63], [19, 89, 11, 63, "WeakMap"], [19, 96, 11, 63], [19, 100, 11, 63, "n"], [19, 101, 11, 63], [19, 108, 11, 63, "WeakMap"], [19, 115, 11, 63], [19, 127, 11, 63, "_interopRequireWildcard"], [19, 150, 11, 63], [19, 162, 11, 63, "_interopRequireWildcard"], [19, 163, 11, 63, "e"], [19, 164, 11, 63], [19, 166, 11, 63, "t"], [19, 167, 11, 63], [19, 176, 11, 63, "t"], [19, 177, 11, 63], [19, 181, 11, 63, "e"], [19, 182, 11, 63], [19, 186, 11, 63, "e"], [19, 187, 11, 63], [19, 188, 11, 63, "__esModule"], [19, 198, 11, 63], [19, 207, 11, 63, "e"], [19, 208, 11, 63], [19, 214, 11, 63, "o"], [19, 215, 11, 63], [19, 217, 11, 63, "i"], [19, 218, 11, 63], [19, 220, 11, 63, "f"], [19, 221, 11, 63], [19, 226, 11, 63, "__proto__"], [19, 235, 11, 63], [19, 243, 11, 63, "default"], [19, 250, 11, 63], [19, 252, 11, 63, "e"], [19, 253, 11, 63], [19, 270, 11, 63, "e"], [19, 271, 11, 63], [19, 294, 11, 63, "e"], [19, 295, 11, 63], [19, 320, 11, 63, "e"], [19, 321, 11, 63], [19, 330, 11, 63, "f"], [19, 331, 11, 63], [19, 337, 11, 63, "o"], [19, 338, 11, 63], [19, 341, 11, 63, "t"], [19, 342, 11, 63], [19, 345, 11, 63, "n"], [19, 346, 11, 63], [19, 349, 11, 63, "r"], [19, 350, 11, 63], [19, 358, 11, 63, "o"], [19, 359, 11, 63], [19, 360, 11, 63, "has"], [19, 363, 11, 63], [19, 364, 11, 63, "e"], [19, 365, 11, 63], [19, 375, 11, 63, "o"], [19, 376, 11, 63], [19, 377, 11, 63, "get"], [19, 380, 11, 63], [19, 381, 11, 63, "e"], [19, 382, 11, 63], [19, 385, 11, 63, "o"], [19, 386, 11, 63], [19, 387, 11, 63, "set"], [19, 390, 11, 63], [19, 391, 11, 63, "e"], [19, 392, 11, 63], [19, 394, 11, 63, "f"], [19, 395, 11, 63], [19, 409, 11, 63, "_t"], [19, 411, 11, 63], [19, 415, 11, 63, "e"], [19, 416, 11, 63], [19, 432, 11, 63, "_t"], [19, 434, 11, 63], [19, 441, 11, 63, "hasOwnProperty"], [19, 455, 11, 63], [19, 456, 11, 63, "call"], [19, 460, 11, 63], [19, 461, 11, 63, "e"], [19, 462, 11, 63], [19, 464, 11, 63, "_t"], [19, 466, 11, 63], [19, 473, 11, 63, "i"], [19, 474, 11, 63], [19, 478, 11, 63, "o"], [19, 479, 11, 63], [19, 482, 11, 63, "Object"], [19, 488, 11, 63], [19, 489, 11, 63, "defineProperty"], [19, 503, 11, 63], [19, 508, 11, 63, "Object"], [19, 514, 11, 63], [19, 515, 11, 63, "getOwnPropertyDescriptor"], [19, 539, 11, 63], [19, 540, 11, 63, "e"], [19, 541, 11, 63], [19, 543, 11, 63, "_t"], [19, 545, 11, 63], [19, 552, 11, 63, "i"], [19, 553, 11, 63], [19, 554, 11, 63, "get"], [19, 557, 11, 63], [19, 561, 11, 63, "i"], [19, 562, 11, 63], [19, 563, 11, 63, "set"], [19, 566, 11, 63], [19, 570, 11, 63, "o"], [19, 571, 11, 63], [19, 572, 11, 63, "f"], [19, 573, 11, 63], [19, 575, 11, 63, "_t"], [19, 577, 11, 63], [19, 579, 11, 63, "i"], [19, 580, 11, 63], [19, 584, 11, 63, "f"], [19, 585, 11, 63], [19, 586, 11, 63, "_t"], [19, 588, 11, 63], [19, 592, 11, 63, "e"], [19, 593, 11, 63], [19, 594, 11, 63, "_t"], [19, 596, 11, 63], [19, 607, 11, 63, "f"], [19, 608, 11, 63], [19, 613, 11, 63, "e"], [19, 614, 11, 63], [19, 616, 11, 63, "t"], [19, 617, 11, 63], [20, 2, 12, 7], [20, 11, 12, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 27, 12, 32, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 28, 12, 32, "_ref"], [20, 32, 12, 32], [20, 34, 31, 3], [21, 4, 31, 3], [21, 8, 13, 2, "disabled"], [21, 16, 13, 10], [21, 19, 13, 10, "_ref"], [21, 23, 13, 10], [21, 24, 13, 2, "disabled"], [21, 32, 13, 10], [22, 6, 14, 2, "allowFontScaling"], [22, 22, 14, 18], [22, 25, 14, 18, "_ref"], [22, 29, 14, 18], [22, 30, 14, 2, "allowFontScaling"], [22, 46, 14, 18], [23, 6, 15, 2, "backImage"], [23, 15, 15, 11], [23, 18, 15, 11, "_ref"], [23, 22, 15, 11], [23, 23, 15, 2, "backImage"], [23, 32, 15, 11], [24, 6, 16, 2, "label"], [24, 11, 16, 7], [24, 14, 16, 7, "_ref"], [24, 18, 16, 7], [24, 19, 16, 2, "label"], [24, 24, 16, 7], [25, 6, 17, 2, "labelStyle"], [25, 16, 17, 12], [25, 19, 17, 12, "_ref"], [25, 23, 17, 12], [25, 24, 17, 2, "labelStyle"], [25, 34, 17, 12], [26, 6, 17, 12, "_ref$displayMode"], [26, 22, 17, 12], [26, 25, 17, 12, "_ref"], [26, 29, 17, 12], [26, 30, 18, 2, "displayMode"], [26, 41, 18, 13], [27, 6, 18, 2, "displayMode"], [27, 17, 18, 13], [27, 20, 18, 13, "_ref$displayMode"], [27, 36, 18, 13], [27, 50, 18, 16, "Platform"], [27, 71, 18, 24], [27, 72, 18, 25, "OS"], [27, 74, 18, 27], [27, 79, 18, 32], [27, 84, 18, 37], [27, 87, 18, 40], [27, 96, 18, 49], [27, 99, 18, 52], [27, 108, 18, 61], [27, 111, 18, 61, "_ref$displayMode"], [27, 127, 18, 61], [28, 6, 19, 2, "onLabelLayout"], [28, 19, 19, 15], [28, 22, 19, 15, "_ref"], [28, 26, 19, 15], [28, 27, 19, 2, "onLabelLayout"], [28, 40, 19, 15], [29, 6, 20, 2, "onPress"], [29, 13, 20, 9], [29, 16, 20, 9, "_ref"], [29, 20, 20, 9], [29, 21, 20, 2, "onPress"], [29, 28, 20, 9], [30, 6, 21, 2, "pressColor"], [30, 16, 21, 12], [30, 19, 21, 12, "_ref"], [30, 23, 21, 12], [30, 24, 21, 2, "pressColor"], [30, 34, 21, 12], [31, 6, 22, 2, "pressOpacity"], [31, 18, 22, 14], [31, 21, 22, 14, "_ref"], [31, 25, 22, 14], [31, 26, 22, 2, "pressOpacity"], [31, 38, 22, 14], [32, 6, 23, 2, "screenLayout"], [32, 18, 23, 14], [32, 21, 23, 14, "_ref"], [32, 25, 23, 14], [32, 26, 23, 2, "screenLayout"], [32, 38, 23, 14], [33, 6, 24, 2, "tintColor"], [33, 15, 24, 11], [33, 18, 24, 11, "_ref"], [33, 22, 24, 11], [33, 23, 24, 2, "tintColor"], [33, 32, 24, 11], [34, 6, 25, 2, "titleLayout"], [34, 17, 25, 13], [34, 20, 25, 13, "_ref"], [34, 24, 25, 13], [34, 25, 25, 2, "titleLayout"], [34, 36, 25, 13], [35, 6, 25, 13, "_ref$truncatedLabel"], [35, 25, 25, 13], [35, 28, 25, 13, "_ref"], [35, 32, 25, 13], [35, 33, 26, 2, "truncatedLabel"], [35, 47, 26, 16], [36, 6, 26, 2, "truncatedLabel"], [36, 20, 26, 16], [36, 23, 26, 16, "_ref$truncatedLabel"], [36, 42, 26, 16], [36, 56, 26, 19], [36, 62, 26, 25], [36, 65, 26, 25, "_ref$truncatedLabel"], [36, 84, 26, 25], [37, 6, 26, 25, "_ref$accessibilityLab"], [37, 27, 26, 25], [37, 30, 26, 25, "_ref"], [37, 34, 26, 25], [37, 35, 27, 2, "accessibilityLabel"], [37, 53, 27, 20], [38, 6, 27, 2, "accessibilityLabel"], [38, 24, 27, 20], [38, 27, 27, 20, "_ref$accessibilityLab"], [38, 48, 27, 20], [38, 62, 27, 23, "label"], [38, 67, 27, 28], [38, 71, 27, 32, "label"], [38, 76, 27, 37], [38, 81, 27, 42], [38, 87, 27, 48], [38, 90, 27, 51], [38, 93, 27, 54, "label"], [38, 98, 27, 59], [38, 106, 27, 67], [38, 109, 27, 70], [38, 118, 27, 79], [38, 121, 27, 79, "_ref$accessibilityLab"], [38, 142, 27, 79], [39, 6, 28, 2, "testID"], [39, 12, 28, 8], [39, 15, 28, 8, "_ref"], [39, 19, 28, 8], [39, 20, 28, 2, "testID"], [39, 26, 28, 8], [40, 6, 29, 2, "style"], [40, 11, 29, 7], [40, 14, 29, 7, "_ref"], [40, 18, 29, 7], [40, 19, 29, 2, "style"], [40, 24, 29, 7], [41, 6, 30, 2, "href"], [41, 10, 30, 6], [41, 13, 30, 6, "_ref"], [41, 17, 30, 6], [41, 18, 30, 2, "href"], [41, 22, 30, 6], [42, 4, 32, 2], [42, 8, 32, 2, "_useTheme"], [42, 17, 32, 2], [42, 20, 35, 6], [42, 24, 35, 6, "useTheme"], [42, 40, 35, 14], [42, 42, 35, 15], [42, 43, 35, 16], [43, 6, 33, 4, "colors"], [43, 12, 33, 10], [43, 15, 33, 10, "_useTheme"], [43, 24, 33, 10], [43, 25, 33, 4, "colors"], [43, 31, 33, 10], [44, 6, 34, 4, "fonts"], [44, 11, 34, 9], [44, 14, 34, 9, "_useTheme"], [44, 23, 34, 9], [44, 24, 34, 4, "fonts"], [44, 29, 34, 9], [45, 4, 36, 2], [45, 8, 36, 2, "_useLocale"], [45, 18, 36, 2], [45, 21, 38, 6], [45, 25, 38, 6, "useLocale"], [45, 42, 38, 15], [45, 44, 38, 16], [45, 45, 38, 17], [46, 6, 37, 4, "direction"], [46, 15, 37, 13], [46, 18, 37, 13, "_useLocale"], [46, 28, 37, 13], [46, 29, 37, 4, "direction"], [46, 38, 37, 13], [47, 4, 39, 2], [47, 8, 39, 2, "_React$useState"], [47, 23, 39, 2], [47, 26, 39, 38, "React"], [47, 31, 39, 43], [47, 32, 39, 44, "useState"], [47, 40, 39, 52], [47, 41, 39, 53], [47, 45, 39, 57], [47, 46, 39, 58], [48, 6, 39, 58, "_React$useState2"], [48, 22, 39, 58], [48, 29, 39, 58, "_slicedToArray2"], [48, 44, 39, 58], [48, 45, 39, 58, "default"], [48, 52, 39, 58], [48, 54, 39, 58, "_React$useState"], [48, 69, 39, 58], [49, 6, 39, 9, "labelWidth"], [49, 16, 39, 19], [49, 19, 39, 19, "_React$useState2"], [49, 35, 39, 19], [50, 6, 39, 21, "<PERSON><PERSON><PERSON><PERSON>"], [50, 19, 39, 34], [50, 22, 39, 34, "_React$useState2"], [50, 38, 39, 34], [51, 4, 40, 2], [51, 8, 40, 2, "_React$useState3"], [51, 24, 40, 2], [51, 27, 40, 56, "React"], [51, 32, 40, 61], [51, 33, 40, 62, "useState"], [51, 41, 40, 70], [51, 42, 40, 71], [51, 46, 40, 75], [51, 47, 40, 76], [52, 6, 40, 76, "_React$useState4"], [52, 22, 40, 76], [52, 29, 40, 76, "_slicedToArray2"], [52, 44, 40, 76], [52, 45, 40, 76, "default"], [52, 52, 40, 76], [52, 54, 40, 76, "_React$useState3"], [52, 70, 40, 76], [53, 6, 40, 9, "truncated<PERSON><PERSON><PERSON><PERSON>"], [53, 25, 40, 28], [53, 28, 40, 28, "_React$useState4"], [53, 44, 40, 28], [54, 6, 40, 30, "setT<PERSON><PERSON><PERSON><PERSON><PERSON>"], [54, 28, 40, 52], [54, 31, 40, 52, "_React$useState4"], [54, 47, 40, 52], [55, 4, 41, 2], [55, 8, 41, 8, "renderBackImage"], [55, 23, 41, 23], [55, 26, 41, 26, "renderBackImage"], [55, 27, 41, 26], [55, 32, 41, 32], [56, 6, 42, 4], [56, 10, 42, 8, "backImage"], [56, 19, 42, 17], [56, 21, 42, 19], [57, 8, 43, 6], [57, 15, 43, 13, "backImage"], [57, 24, 43, 22], [57, 25, 43, 23], [58, 10, 44, 8, "tintColor"], [58, 19, 44, 17], [58, 21, 44, 19, "tintColor"], [58, 30, 44, 28], [58, 34, 44, 32, "colors"], [58, 40, 44, 38], [58, 41, 44, 39, "text"], [59, 8, 45, 6], [59, 9, 45, 7], [59, 10, 45, 8], [60, 6, 46, 4], [60, 7, 46, 5], [60, 13, 46, 11], [61, 8, 47, 6], [61, 15, 47, 13], [61, 28, 47, 26], [61, 32, 47, 26, "_jsx"], [61, 47, 47, 30], [61, 49, 47, 31, "HeaderIcon"], [61, 71, 47, 41], [61, 73, 47, 43], [62, 10, 48, 8, "source"], [62, 16, 48, 14], [62, 18, 48, 16, "backIcon"], [62, 35, 48, 24], [63, 10, 49, 8, "tintColor"], [63, 19, 49, 17], [63, 21, 49, 19, "tintColor"], [63, 30, 49, 28], [64, 10, 50, 8, "style"], [64, 15, 50, 13], [64, 17, 50, 15], [64, 18, 50, 16, "styles"], [64, 24, 50, 22], [64, 25, 50, 23, "icon"], [64, 29, 50, 27], [64, 31, 50, 29, "displayMode"], [64, 42, 50, 40], [64, 47, 50, 45], [64, 56, 50, 54], [64, 60, 50, 58, "styles"], [64, 66, 50, 64], [64, 67, 50, 65, "iconWithLabel"], [64, 80, 50, 78], [65, 8, 51, 6], [65, 9, 51, 7], [65, 10, 51, 8], [66, 6, 52, 4], [67, 4, 53, 2], [67, 5, 53, 3], [68, 4, 54, 2], [68, 8, 54, 8, "renderLabel"], [68, 19, 54, 19], [68, 22, 54, 22, "renderLabel"], [68, 23, 54, 22], [68, 28, 54, 28], [69, 6, 55, 4], [69, 10, 55, 8, "displayMode"], [69, 21, 55, 19], [69, 26, 55, 24], [69, 35, 55, 33], [69, 37, 55, 35], [70, 8, 56, 6], [70, 15, 56, 13], [70, 19, 56, 17], [71, 6, 57, 4], [72, 6, 58, 4], [72, 10, 58, 10, "availableSpace"], [72, 24, 58, 24], [72, 27, 58, 27, "titleLayout"], [72, 38, 58, 38], [72, 42, 58, 42, "screenLayout"], [72, 54, 58, 54], [72, 57, 58, 57], [72, 58, 58, 58, "screenLayout"], [72, 70, 58, 70], [72, 71, 58, 71, "width"], [72, 76, 58, 76], [72, 79, 58, 79, "titleLayout"], [72, 90, 58, 90], [72, 91, 58, 91, "width"], [72, 96, 58, 96], [72, 100, 58, 100], [72, 101, 58, 101], [72, 105, 58, 105, "ICON_WIDTH"], [72, 115, 58, 115], [72, 118, 58, 118, "ICON_MARGIN"], [72, 141, 58, 129], [72, 142, 58, 130], [72, 145, 58, 133], [72, 149, 58, 137], [73, 6, 59, 4], [73, 10, 59, 10, "potentialLabelText"], [73, 28, 59, 28], [73, 31, 59, 31, "displayMode"], [73, 42, 59, 42], [73, 47, 59, 47], [73, 56, 59, 56], [73, 59, 59, 59, "label"], [73, 64, 59, 64], [73, 67, 59, 67, "truncatedLabel"], [73, 81, 59, 81], [74, 6, 60, 4], [74, 10, 60, 10, "finalLabelText"], [74, 24, 60, 24], [74, 27, 60, 27, "availableSpace"], [74, 41, 60, 41], [74, 45, 60, 45, "labelWidth"], [74, 55, 60, 55], [74, 59, 60, 59, "truncated<PERSON><PERSON><PERSON><PERSON>"], [74, 78, 60, 78], [74, 81, 60, 81, "availableSpace"], [74, 95, 60, 95], [74, 98, 60, 98, "labelWidth"], [74, 108, 60, 108], [74, 111, 60, 111, "potentialLabelText"], [74, 129, 60, 129], [74, 132, 60, 132, "availableSpace"], [74, 146, 60, 146], [74, 149, 60, 149, "truncated<PERSON><PERSON><PERSON><PERSON>"], [74, 168, 60, 168], [74, 171, 60, 171, "truncatedLabel"], [74, 185, 60, 185], [74, 188, 60, 188], [74, 192, 60, 192], [74, 195, 60, 195, "potentialLabelText"], [74, 213, 60, 213], [75, 6, 61, 4], [75, 10, 61, 10, "commonStyle"], [75, 21, 61, 21], [75, 24, 61, 24], [75, 25, 61, 25, "fonts"], [75, 30, 61, 30], [75, 31, 61, 31, "regular"], [75, 38, 61, 38], [75, 40, 61, 40, "styles"], [75, 46, 61, 46], [75, 47, 61, 47, "label"], [75, 52, 61, 52], [75, 54, 61, 54, "labelStyle"], [75, 64, 61, 64], [75, 65, 61, 65], [76, 6, 62, 4], [76, 10, 62, 10, "hiddenStyle"], [76, 21, 62, 21], [76, 24, 62, 24], [76, 25, 62, 25, "commonStyle"], [76, 36, 62, 36], [76, 38, 62, 38], [77, 8, 63, 6, "position"], [77, 16, 63, 14], [77, 18, 63, 16], [77, 28, 63, 26], [78, 8, 64, 6, "top"], [78, 11, 64, 9], [78, 13, 64, 11], [78, 14, 64, 12], [79, 8, 65, 6, "left"], [79, 12, 65, 10], [79, 14, 65, 12], [79, 15, 65, 13], [80, 8, 66, 6, "opacity"], [80, 15, 66, 13], [80, 17, 66, 15], [81, 6, 67, 4], [81, 7, 67, 5], [81, 8, 67, 6], [82, 6, 68, 4], [82, 10, 68, 10, "labelElement"], [82, 22, 68, 22], [82, 25, 68, 25], [82, 38, 68, 38], [82, 42, 68, 38, "_jsxs"], [82, 58, 68, 43], [82, 60, 68, 44, "View"], [82, 77, 68, 48], [82, 79, 68, 50], [83, 8, 69, 6, "style"], [83, 13, 69, 11], [83, 15, 69, 13, "styles"], [83, 21, 69, 19], [83, 22, 69, 20, "labelWrapper"], [83, 34, 69, 32], [84, 8, 70, 6, "children"], [84, 16, 70, 14], [84, 18, 70, 16], [84, 19, 70, 17, "label"], [84, 24, 70, 22], [84, 28, 70, 26, "displayMode"], [84, 39, 70, 37], [84, 44, 70, 42], [84, 53, 70, 51], [84, 56, 70, 54], [84, 69, 70, 67], [84, 73, 70, 67, "_jsx"], [84, 88, 70, 71], [84, 90, 70, 72, "Animated"], [84, 111, 70, 80], [84, 112, 70, 81, "Text"], [84, 116, 70, 85], [84, 118, 70, 87], [85, 10, 71, 8, "style"], [85, 15, 71, 13], [85, 17, 71, 15, "hiddenStyle"], [85, 28, 71, 26], [86, 10, 72, 8, "numberOfLines"], [86, 23, 72, 21], [86, 25, 72, 23], [86, 26, 72, 24], [87, 10, 73, 8, "onLayout"], [87, 18, 73, 16], [87, 20, 73, 18, "e"], [87, 21, 73, 19], [87, 25, 73, 23, "<PERSON><PERSON><PERSON><PERSON>"], [87, 38, 73, 36], [87, 39, 73, 37, "e"], [87, 40, 73, 38], [87, 41, 73, 39, "nativeEvent"], [87, 52, 73, 50], [87, 53, 73, 51, "layout"], [87, 59, 73, 57], [87, 60, 73, 58, "width"], [87, 65, 73, 63], [87, 66, 73, 64], [88, 10, 74, 8, "children"], [88, 18, 74, 16], [88, 20, 74, 18, "label"], [89, 8, 75, 6], [89, 9, 75, 7], [89, 10, 75, 8], [89, 13, 75, 11], [89, 17, 75, 15], [89, 19, 75, 17, "truncatedLabel"], [89, 33, 75, 31], [89, 36, 75, 34], [89, 49, 75, 47], [89, 53, 75, 47, "_jsx"], [89, 68, 75, 51], [89, 70, 75, 52, "Animated"], [89, 91, 75, 60], [89, 92, 75, 61, "Text"], [89, 96, 75, 65], [89, 98, 75, 67], [90, 10, 76, 8, "style"], [90, 15, 76, 13], [90, 17, 76, 15, "hiddenStyle"], [90, 28, 76, 26], [91, 10, 77, 8, "numberOfLines"], [91, 23, 77, 21], [91, 25, 77, 23], [91, 26, 77, 24], [92, 10, 78, 8, "onLayout"], [92, 18, 78, 16], [92, 20, 78, 18, "e"], [92, 21, 78, 19], [92, 25, 78, 23, "setT<PERSON><PERSON><PERSON><PERSON><PERSON>"], [92, 47, 78, 45], [92, 48, 78, 46, "e"], [92, 49, 78, 47], [92, 50, 78, 48, "nativeEvent"], [92, 61, 78, 59], [92, 62, 78, 60, "layout"], [92, 68, 78, 66], [92, 69, 78, 67, "width"], [92, 74, 78, 72], [92, 75, 78, 73], [93, 10, 79, 8, "children"], [93, 18, 79, 16], [93, 20, 79, 18, "truncatedLabel"], [94, 8, 80, 6], [94, 9, 80, 7], [94, 10, 80, 8], [94, 13, 80, 11], [94, 17, 80, 15], [94, 19, 80, 17, "finalLabelText"], [94, 33, 80, 31], [94, 36, 80, 34], [94, 49, 80, 47], [94, 53, 80, 47, "_jsx"], [94, 68, 80, 51], [94, 70, 80, 52, "Animated"], [94, 91, 80, 60], [94, 92, 80, 61, "Text"], [94, 96, 80, 65], [94, 98, 80, 67], [95, 10, 81, 8, "accessible"], [95, 20, 81, 18], [95, 22, 81, 20], [95, 27, 81, 25], [96, 10, 82, 8, "onLayout"], [96, 18, 82, 16], [96, 20, 82, 18, "onLabelLayout"], [96, 33, 82, 31], [97, 10, 83, 8, "style"], [97, 15, 83, 13], [97, 17, 83, 15], [97, 18, 83, 16, "tintColor"], [97, 27, 83, 25], [97, 30, 83, 28], [98, 12, 84, 10, "color"], [98, 17, 84, 15], [98, 19, 84, 17, "tintColor"], [99, 10, 85, 8], [99, 11, 85, 9], [99, 14, 85, 12], [99, 18, 85, 16], [99, 20, 85, 18, "commonStyle"], [99, 31, 85, 29], [99, 32, 85, 30], [100, 10, 86, 8, "numberOfLines"], [100, 23, 86, 21], [100, 25, 86, 23], [100, 26, 86, 24], [101, 10, 87, 8, "allowFontScaling"], [101, 26, 87, 24], [101, 28, 87, 26], [101, 29, 87, 27], [101, 30, 87, 28, "allowFontScaling"], [101, 46, 87, 44], [102, 10, 88, 8, "children"], [102, 18, 88, 16], [102, 20, 88, 18, "finalLabelText"], [103, 8, 89, 6], [103, 9, 89, 7], [103, 10, 89, 8], [103, 13, 89, 11], [103, 17, 89, 15], [104, 6, 90, 4], [104, 7, 90, 5], [104, 8, 90, 6], [105, 6, 91, 4], [105, 10, 91, 8, "backImage"], [105, 19, 91, 17], [105, 23, 91, 21, "Platform"], [105, 44, 91, 29], [105, 45, 91, 30, "OS"], [105, 47, 91, 32], [105, 52, 91, 37], [105, 57, 91, 42], [105, 59, 91, 44], [106, 8, 92, 6], [107, 8, 93, 6], [108, 8, 94, 6], [108, 15, 94, 13, "labelElement"], [108, 27, 94, 25], [109, 6, 95, 4], [110, 6, 96, 4], [110, 13, 96, 11], [110, 26, 96, 24], [110, 30, 96, 24, "_jsx"], [110, 45, 96, 28], [110, 47, 96, 29, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [110, 69, 96, 39], [110, 71, 96, 41], [111, 8, 97, 6, "maskElement"], [111, 19, 97, 17], [111, 21, 97, 19], [111, 34, 97, 32], [111, 38, 97, 32, "_jsxs"], [111, 54, 97, 37], [111, 56, 97, 38, "View"], [111, 73, 97, 42], [111, 75, 97, 44], [112, 10, 98, 8, "style"], [112, 15, 98, 13], [112, 17, 98, 15], [112, 18, 98, 16, "styles"], [112, 24, 98, 22], [112, 25, 98, 23, "iconMaskContainer"], [112, 42, 98, 40], [113, 10, 99, 8], [114, 10, 100, 8, "screenLayout"], [114, 22, 100, 20], [114, 25, 100, 23], [115, 12, 101, 10, "min<PERSON><PERSON><PERSON>"], [115, 20, 101, 18], [115, 22, 101, 20, "screenLayout"], [115, 34, 101, 32], [115, 35, 101, 33, "width"], [115, 40, 101, 38], [115, 43, 101, 41], [115, 44, 101, 42], [115, 47, 101, 45], [116, 10, 102, 8], [116, 11, 102, 9], [116, 14, 102, 12], [116, 18, 102, 16], [116, 19, 102, 17], [117, 10, 103, 8, "children"], [117, 18, 103, 16], [117, 20, 103, 18], [117, 21, 103, 19], [117, 34, 103, 32], [117, 38, 103, 32, "_jsx"], [117, 53, 103, 36], [117, 55, 103, 37, "Image"], [117, 73, 103, 42], [117, 75, 103, 44], [118, 12, 104, 10, "source"], [118, 18, 104, 16], [118, 20, 104, 18, "backIconMask"], [118, 41, 104, 30], [119, 12, 105, 10, "resizeMode"], [119, 22, 105, 20], [119, 24, 105, 22], [119, 33, 105, 31], [120, 12, 106, 10, "style"], [120, 17, 106, 15], [120, 19, 106, 17], [120, 20, 106, 18, "styles"], [120, 26, 106, 24], [120, 27, 106, 25, "iconMask"], [120, 35, 106, 33], [120, 37, 106, 35, "direction"], [120, 46, 106, 44], [120, 51, 106, 49], [120, 56, 106, 54], [120, 60, 106, 58, "styles"], [120, 66, 106, 64], [120, 67, 106, 65, "flip"], [120, 71, 106, 69], [121, 10, 107, 8], [121, 11, 107, 9], [121, 12, 107, 10], [121, 14, 107, 12], [121, 27, 107, 25], [121, 31, 107, 25, "_jsx"], [121, 46, 107, 29], [121, 48, 107, 30, "View"], [121, 65, 107, 34], [121, 67, 107, 36], [122, 12, 108, 10, "style"], [122, 17, 108, 15], [122, 19, 108, 17, "styles"], [122, 25, 108, 23], [122, 26, 108, 24, "iconMaskFillerRect"], [123, 10, 109, 8], [123, 11, 109, 9], [123, 12, 109, 10], [124, 8, 110, 6], [124, 9, 110, 7], [124, 10, 110, 8], [125, 8, 111, 6, "children"], [125, 16, 111, 14], [125, 18, 111, 16, "labelElement"], [126, 6, 112, 4], [126, 7, 112, 5], [126, 8, 112, 6], [127, 4, 113, 2], [127, 5, 113, 3], [128, 4, 114, 2], [128, 8, 114, 8, "handlePress"], [128, 19, 114, 19], [128, 22, 114, 22, "handlePress"], [128, 23, 114, 22], [128, 28, 114, 28], [129, 6, 115, 4], [129, 10, 115, 8, "onPress"], [129, 17, 115, 15], [129, 19, 115, 17], [130, 8, 116, 6, "requestAnimationFrame"], [130, 29, 116, 27], [130, 30, 116, 28], [130, 36, 116, 34, "onPress"], [130, 43, 116, 41], [130, 44, 116, 42], [130, 45, 116, 43], [130, 46, 116, 44], [131, 6, 117, 4], [132, 4, 118, 2], [132, 5, 118, 3], [133, 4, 119, 2], [133, 11, 119, 9], [133, 24, 119, 22], [133, 28, 119, 22, "_jsx"], [133, 43, 119, 26], [133, 45, 119, 27, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [133, 71, 119, 39], [133, 73, 119, 41], [134, 6, 120, 4, "disabled"], [134, 14, 120, 12], [134, 16, 120, 14, "disabled"], [134, 24, 120, 22], [135, 6, 121, 4, "href"], [135, 10, 121, 8], [135, 12, 121, 10, "href"], [135, 16, 121, 14], [136, 6, 122, 4, "accessibilityLabel"], [136, 24, 122, 22], [136, 26, 122, 24, "accessibilityLabel"], [136, 44, 122, 42], [137, 6, 123, 4, "testID"], [137, 12, 123, 10], [137, 14, 123, 12, "testID"], [137, 20, 123, 18], [138, 6, 124, 4, "onPress"], [138, 13, 124, 11], [138, 15, 124, 13, "handlePress"], [138, 26, 124, 24], [139, 6, 125, 4, "pressColor"], [139, 16, 125, 14], [139, 18, 125, 16, "pressColor"], [139, 28, 125, 26], [140, 6, 126, 4, "pressOpacity"], [140, 18, 126, 16], [140, 20, 126, 18, "pressOpacity"], [140, 32, 126, 30], [141, 6, 127, 4, "style"], [141, 11, 127, 9], [141, 13, 127, 11], [141, 14, 127, 12, "styles"], [141, 20, 127, 18], [141, 21, 127, 19, "container"], [141, 30, 127, 28], [141, 32, 127, 30, "style"], [141, 37, 127, 35], [141, 38, 127, 36], [142, 6, 128, 4, "children"], [142, 14, 128, 12], [142, 16, 128, 14], [142, 29, 128, 27], [142, 33, 128, 27, "_jsxs"], [142, 49, 128, 32], [142, 51, 128, 33, "React"], [142, 56, 128, 38], [142, 57, 128, 39, "Fragment"], [142, 65, 128, 47], [142, 67, 128, 49], [143, 8, 129, 6, "children"], [143, 16, 129, 14], [143, 18, 129, 16], [143, 19, 129, 17, "renderBackImage"], [143, 34, 129, 32], [143, 35, 129, 33], [143, 36, 129, 34], [143, 38, 129, 36, "renderLabel"], [143, 49, 129, 47], [143, 50, 129, 48], [143, 51, 129, 49], [144, 6, 130, 4], [144, 7, 130, 5], [145, 4, 131, 2], [145, 5, 131, 3], [145, 6, 131, 4], [146, 2, 132, 0], [147, 2, 133, 0], [147, 6, 133, 6, "ICON_WIDTH"], [147, 16, 133, 16], [147, 19, 133, 19, "Platform"], [147, 40, 133, 27], [147, 41, 133, 28, "OS"], [147, 43, 133, 30], [147, 48, 133, 35], [147, 53, 133, 40], [147, 56, 133, 43], [147, 58, 133, 45], [147, 61, 133, 48], [147, 63, 133, 50], [148, 2, 134, 0], [148, 6, 134, 6, "ICON_MARGIN_END"], [148, 21, 134, 21], [148, 24, 134, 24, "Platform"], [148, 45, 134, 32], [148, 46, 134, 33, "OS"], [148, 48, 134, 35], [148, 53, 134, 40], [148, 58, 134, 45], [148, 61, 134, 48], [148, 63, 134, 50], [148, 66, 134, 53], [148, 67, 134, 54], [149, 2, 135, 0], [149, 6, 135, 6, "styles"], [149, 12, 135, 12], [149, 15, 135, 15, "StyleSheet"], [149, 38, 135, 25], [149, 39, 135, 26, "create"], [149, 45, 135, 32], [149, 46, 135, 33], [150, 4, 136, 2, "container"], [150, 13, 136, 11], [150, 15, 136, 13], [151, 6, 137, 4, "paddingHorizontal"], [151, 23, 137, 21], [151, 25, 137, 23], [151, 26, 137, 24], [152, 6, 138, 4, "min<PERSON><PERSON><PERSON>"], [152, 14, 138, 12], [152, 16, 138, 14, "StyleSheet"], [152, 39, 138, 24], [152, 40, 138, 25, "hairlineWidth"], [152, 53, 138, 38], [153, 6, 139, 4], [154, 6, 140, 4], [154, 9, 140, 7, "Platform"], [154, 30, 140, 15], [154, 31, 140, 16, "select"], [154, 37, 140, 22], [154, 38, 140, 23], [155, 8, 141, 6, "ios"], [155, 11, 141, 9], [155, 13, 141, 11], [155, 17, 141, 15], [156, 8, 142, 6, "default"], [156, 15, 142, 13], [156, 17, 142, 15], [157, 10, 143, 8, "marginVertical"], [157, 24, 143, 22], [157, 26, 143, 24], [157, 27, 143, 25], [158, 10, 144, 8, "marginHorizontal"], [158, 26, 144, 24], [158, 28, 144, 26], [159, 8, 145, 6], [160, 6, 146, 4], [160, 7, 146, 5], [161, 4, 147, 2], [161, 5, 147, 3], [162, 4, 148, 2, "label"], [162, 9, 148, 7], [162, 11, 148, 9], [163, 6, 149, 4, "fontSize"], [163, 14, 149, 12], [163, 16, 149, 14], [163, 18, 149, 16], [164, 6, 150, 4], [165, 6, 151, 4], [166, 6, 152, 4, "letterSpacing"], [166, 19, 152, 17], [166, 21, 152, 19], [167, 4, 153, 2], [167, 5, 153, 3], [168, 4, 154, 2, "labelWrapper"], [168, 16, 154, 14], [168, 18, 154, 16], [169, 6, 155, 4], [170, 6, 156, 4], [171, 6, 157, 4, "flexDirection"], [171, 19, 157, 17], [171, 21, 157, 19], [171, 26, 157, 24], [172, 6, 158, 4, "alignItems"], [172, 16, 158, 14], [172, 18, 158, 16], [172, 30, 158, 28], [173, 6, 159, 4, "marginEnd"], [173, 15, 159, 13], [173, 17, 159, 15, "ICON_MARGIN"], [174, 4, 160, 2], [174, 5, 160, 3], [175, 4, 161, 2, "icon"], [175, 8, 161, 6], [175, 10, 161, 8], [176, 6, 162, 4, "width"], [176, 11, 162, 9], [176, 13, 162, 11, "ICON_WIDTH"], [176, 23, 162, 21], [177, 6, 163, 4, "marginEnd"], [177, 15, 163, 13], [177, 17, 163, 15, "ICON_MARGIN_END"], [178, 4, 164, 2], [178, 5, 164, 3], [179, 4, 165, 2, "iconWithLabel"], [179, 17, 165, 15], [179, 19, 165, 17, "Platform"], [179, 40, 165, 25], [179, 41, 165, 26, "OS"], [179, 43, 165, 28], [179, 48, 165, 33], [179, 53, 165, 38], [179, 56, 165, 41], [180, 6, 166, 4, "marginEnd"], [180, 15, 166, 13], [180, 17, 166, 15], [181, 4, 167, 2], [181, 5, 167, 3], [181, 8, 167, 6], [181, 9, 167, 7], [181, 10, 167, 8], [182, 4, 168, 2, "iconMaskContainer"], [182, 21, 168, 19], [182, 23, 168, 21], [183, 6, 169, 4, "flex"], [183, 10, 169, 8], [183, 12, 169, 10], [183, 13, 169, 11], [184, 6, 170, 4, "flexDirection"], [184, 19, 170, 17], [184, 21, 170, 19], [184, 26, 170, 24], [185, 6, 171, 4, "justifyContent"], [185, 20, 171, 18], [185, 22, 171, 20], [186, 4, 172, 2], [186, 5, 172, 3], [187, 4, 173, 2, "iconMaskFillerRect"], [187, 22, 173, 20], [187, 24, 173, 22], [188, 6, 174, 4, "flex"], [188, 10, 174, 8], [188, 12, 174, 10], [188, 13, 174, 11], [189, 6, 175, 4, "backgroundColor"], [189, 21, 175, 19], [189, 23, 175, 21], [190, 4, 176, 2], [190, 5, 176, 3], [191, 4, 177, 2, "iconMask"], [191, 12, 177, 10], [191, 14, 177, 12], [192, 6, 178, 4, "height"], [192, 12, 178, 10], [192, 14, 178, 12], [192, 16, 178, 14], [193, 6, 179, 4, "width"], [193, 11, 179, 9], [193, 13, 179, 11], [193, 15, 179, 13], [194, 6, 180, 4, "marginStart"], [194, 17, 180, 15], [194, 19, 180, 17], [194, 20, 180, 18], [194, 24, 180, 22], [195, 6, 181, 4, "marginVertical"], [195, 20, 181, 18], [195, 22, 181, 20], [195, 24, 181, 22], [196, 6, 182, 4, "alignSelf"], [196, 15, 182, 13], [196, 17, 182, 15], [197, 4, 183, 2], [197, 5, 183, 3], [198, 4, 184, 2, "flip"], [198, 8, 184, 6], [198, 10, 184, 8], [199, 6, 185, 4, "transform"], [199, 15, 185, 13], [199, 17, 185, 15], [200, 4, 186, 2], [201, 2, 187, 0], [201, 3, 187, 1], [201, 4, 187, 2], [202, 0, 187, 3], [202, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderBackImage", "renderLabel", "_jsx$argument_1.onLayout", "handlePress", "requestAnimationFrame$argument_0"], "mappings": "AAA;OCW;0BC6B;GDY;sBEC;kBCmB,8CD;kBCK,uDD;GFmC;sBIC;4BCE,eD;GJE;CDc"}}, "type": "js/module"}]}