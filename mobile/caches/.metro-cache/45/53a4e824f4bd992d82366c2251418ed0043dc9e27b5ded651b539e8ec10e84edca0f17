{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "./LogBoxMessage", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 44}}], "key": "fJMdC+PfQ6WErkFJyRwI+fU9hlY=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxNotificationMessage;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"../../Text/Text\"));\n  var _LogBoxMessage = _interopRequireDefault(require(_dependencyMap[4], \"./LogBoxMessage\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[5], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[7], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/UI/LogBoxNotificationMessage.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxNotificationMessage(props) {\n    return (0, _jsxRuntime.jsx)(_View.default, {\n      style: styles.container,\n      children: (0, _jsxRuntime.jsx)(_Text.default, {\n        id: \"logbox_notification_message_text\",\n        numberOfLines: 1,\n        style: styles.text,\n        children: props.message && (0, _jsxRuntime.jsx)(_LogBoxMessage.default, {\n          plaintext: true,\n          message: props.message,\n          style: styles.substitutionText\n        })\n      })\n    });\n  }\n  var styles = _StyleSheet.default.create({\n    container: {\n      alignSelf: 'stretch',\n      flexGrow: 1,\n      flexShrink: 1,\n      flexBasis: 'auto',\n      borderLeftColor: LogBoxStyle.getTextColor(0.2),\n      borderLeftWidth: 1,\n      paddingLeft: 8\n    },\n    text: {\n      color: LogBoxStyle.getTextColor(1),\n      flex: 1,\n      fontSize: 14,\n      lineHeight: 22\n    },\n    substitutionText: {\n      color: LogBoxStyle.getTextColor(0.6)\n    }\n  });\n});", "lineCount": 51, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_View"], [7, 11, 13, 0], [7, 14, 13, 0, "_interopRequireDefault"], [7, 36, 13, 0], [7, 37, 13, 0, "require"], [7, 44, 13, 0], [7, 45, 13, 0, "_dependencyMap"], [7, 59, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "_StyleSheet"], [8, 17, 14, 0], [8, 20, 14, 0, "_interopRequireDefault"], [8, 42, 14, 0], [8, 43, 14, 0, "require"], [8, 50, 14, 0], [8, 51, 14, 0, "_dependencyMap"], [8, 65, 14, 0], [9, 2, 15, 0], [9, 6, 15, 0, "_Text"], [9, 11, 15, 0], [9, 14, 15, 0, "_interopRequireDefault"], [9, 36, 15, 0], [9, 37, 15, 0, "require"], [9, 44, 15, 0], [9, 45, 15, 0, "_dependencyMap"], [9, 59, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_LogBoxMessage"], [10, 20, 16, 0], [10, 23, 16, 0, "_interopRequireDefault"], [10, 45, 16, 0], [10, 46, 16, 0, "require"], [10, 53, 16, 0], [10, 54, 16, 0, "_dependencyMap"], [10, 68, 16, 0], [11, 2, 17, 0], [11, 6, 17, 0, "LogBoxStyle"], [11, 17, 17, 0], [11, 20, 17, 0, "_interopRequireWildcard"], [11, 43, 17, 0], [11, 44, 17, 0, "require"], [11, 51, 17, 0], [11, 52, 17, 0, "_dependencyMap"], [11, 66, 17, 0], [12, 2, 18, 0], [12, 6, 18, 0, "React"], [12, 11, 18, 0], [12, 14, 18, 0, "_interopRequireWildcard"], [12, 37, 18, 0], [12, 38, 18, 0, "require"], [12, 45, 18, 0], [12, 46, 18, 0, "_dependencyMap"], [12, 60, 18, 0], [13, 2, 18, 31], [13, 6, 18, 31, "_jsxRuntime"], [13, 17, 18, 31], [13, 20, 18, 31, "require"], [13, 27, 18, 31], [13, 28, 18, 31, "_dependencyMap"], [13, 42, 18, 31], [14, 2, 18, 31], [14, 6, 18, 31, "_jsxFileName"], [14, 18, 18, 31], [15, 2, 18, 31], [15, 11, 18, 31, "_interopRequireWildcard"], [15, 35, 18, 31, "e"], [15, 36, 18, 31], [15, 38, 18, 31, "t"], [15, 39, 18, 31], [15, 68, 18, 31, "WeakMap"], [15, 75, 18, 31], [15, 81, 18, 31, "r"], [15, 82, 18, 31], [15, 89, 18, 31, "WeakMap"], [15, 96, 18, 31], [15, 100, 18, 31, "n"], [15, 101, 18, 31], [15, 108, 18, 31, "WeakMap"], [15, 115, 18, 31], [15, 127, 18, 31, "_interopRequireWildcard"], [15, 150, 18, 31], [15, 162, 18, 31, "_interopRequireWildcard"], [15, 163, 18, 31, "e"], [15, 164, 18, 31], [15, 166, 18, 31, "t"], [15, 167, 18, 31], [15, 176, 18, 31, "t"], [15, 177, 18, 31], [15, 181, 18, 31, "e"], [15, 182, 18, 31], [15, 186, 18, 31, "e"], [15, 187, 18, 31], [15, 188, 18, 31, "__esModule"], [15, 198, 18, 31], [15, 207, 18, 31, "e"], [15, 208, 18, 31], [15, 214, 18, 31, "o"], [15, 215, 18, 31], [15, 217, 18, 31, "i"], [15, 218, 18, 31], [15, 220, 18, 31, "f"], [15, 221, 18, 31], [15, 226, 18, 31, "__proto__"], [15, 235, 18, 31], [15, 243, 18, 31, "default"], [15, 250, 18, 31], [15, 252, 18, 31, "e"], [15, 253, 18, 31], [15, 270, 18, 31, "e"], [15, 271, 18, 31], [15, 294, 18, 31, "e"], [15, 295, 18, 31], [15, 320, 18, 31, "e"], [15, 321, 18, 31], [15, 330, 18, 31, "f"], [15, 331, 18, 31], [15, 337, 18, 31, "o"], [15, 338, 18, 31], [15, 341, 18, 31, "t"], [15, 342, 18, 31], [15, 345, 18, 31, "n"], [15, 346, 18, 31], [15, 349, 18, 31, "r"], [15, 350, 18, 31], [15, 358, 18, 31, "o"], [15, 359, 18, 31], [15, 360, 18, 31, "has"], [15, 363, 18, 31], [15, 364, 18, 31, "e"], [15, 365, 18, 31], [15, 375, 18, 31, "o"], [15, 376, 18, 31], [15, 377, 18, 31, "get"], [15, 380, 18, 31], [15, 381, 18, 31, "e"], [15, 382, 18, 31], [15, 385, 18, 31, "o"], [15, 386, 18, 31], [15, 387, 18, 31, "set"], [15, 390, 18, 31], [15, 391, 18, 31, "e"], [15, 392, 18, 31], [15, 394, 18, 31, "f"], [15, 395, 18, 31], [15, 409, 18, 31, "_t"], [15, 411, 18, 31], [15, 415, 18, 31, "e"], [15, 416, 18, 31], [15, 432, 18, 31, "_t"], [15, 434, 18, 31], [15, 441, 18, 31, "hasOwnProperty"], [15, 455, 18, 31], [15, 456, 18, 31, "call"], [15, 460, 18, 31], [15, 461, 18, 31, "e"], [15, 462, 18, 31], [15, 464, 18, 31, "_t"], [15, 466, 18, 31], [15, 473, 18, 31, "i"], [15, 474, 18, 31], [15, 478, 18, 31, "o"], [15, 479, 18, 31], [15, 482, 18, 31, "Object"], [15, 488, 18, 31], [15, 489, 18, 31, "defineProperty"], [15, 503, 18, 31], [15, 508, 18, 31, "Object"], [15, 514, 18, 31], [15, 515, 18, 31, "getOwnPropertyDescriptor"], [15, 539, 18, 31], [15, 540, 18, 31, "e"], [15, 541, 18, 31], [15, 543, 18, 31, "_t"], [15, 545, 18, 31], [15, 552, 18, 31, "i"], [15, 553, 18, 31], [15, 554, 18, 31, "get"], [15, 557, 18, 31], [15, 561, 18, 31, "i"], [15, 562, 18, 31], [15, 563, 18, 31, "set"], [15, 566, 18, 31], [15, 570, 18, 31, "o"], [15, 571, 18, 31], [15, 572, 18, 31, "f"], [15, 573, 18, 31], [15, 575, 18, 31, "_t"], [15, 577, 18, 31], [15, 579, 18, 31, "i"], [15, 580, 18, 31], [15, 584, 18, 31, "f"], [15, 585, 18, 31], [15, 586, 18, 31, "_t"], [15, 588, 18, 31], [15, 592, 18, 31, "e"], [15, 593, 18, 31], [15, 594, 18, 31, "_t"], [15, 596, 18, 31], [15, 607, 18, 31, "f"], [15, 608, 18, 31], [15, 613, 18, 31, "e"], [15, 614, 18, 31], [15, 616, 18, 31, "t"], [15, 617, 18, 31], [16, 2, 20, 15], [16, 11, 20, 24, "LogBoxNotificationMessage"], [16, 36, 20, 49, "LogBoxNotificationMessage"], [16, 37, 20, 50, "props"], [16, 42, 22, 1], [16, 44, 22, 15], [17, 4, 23, 2], [17, 11, 24, 4], [17, 15, 24, 4, "_jsxRuntime"], [17, 26, 24, 4], [17, 27, 24, 4, "jsx"], [17, 30, 24, 4], [17, 32, 24, 5, "_View"], [17, 37, 24, 5], [17, 38, 24, 5, "default"], [17, 45, 24, 9], [18, 6, 24, 10, "style"], [18, 11, 24, 15], [18, 13, 24, 17, "styles"], [18, 19, 24, 23], [18, 20, 24, 24, "container"], [18, 29, 24, 34], [19, 6, 24, 34, "children"], [19, 14, 24, 34], [19, 16, 25, 6], [19, 20, 25, 6, "_jsxRuntime"], [19, 31, 25, 6], [19, 32, 25, 6, "jsx"], [19, 35, 25, 6], [19, 37, 25, 7, "_Text"], [19, 42, 25, 7], [19, 43, 25, 7, "default"], [19, 50, 25, 11], [20, 8, 26, 8, "id"], [20, 10, 26, 10], [20, 12, 26, 11], [20, 46, 26, 45], [21, 8, 27, 8, "numberOfLines"], [21, 21, 27, 21], [21, 23, 27, 23], [21, 24, 27, 25], [22, 8, 28, 8, "style"], [22, 13, 28, 13], [22, 15, 28, 15, "styles"], [22, 21, 28, 21], [22, 22, 28, 22, "text"], [22, 26, 28, 27], [23, 8, 28, 27, "children"], [23, 16, 28, 27], [23, 18, 29, 9, "props"], [23, 23, 29, 14], [23, 24, 29, 15, "message"], [23, 31, 29, 22], [23, 35, 30, 10], [23, 39, 30, 10, "_jsxRuntime"], [23, 50, 30, 10], [23, 51, 30, 10, "jsx"], [23, 54, 30, 10], [23, 56, 30, 11, "_LogBoxMessage"], [23, 70, 30, 11], [23, 71, 30, 11, "default"], [23, 78, 30, 24], [24, 10, 31, 12, "plaintext"], [24, 19, 31, 21], [25, 10, 32, 12, "message"], [25, 17, 32, 19], [25, 19, 32, 21, "props"], [25, 24, 32, 26], [25, 25, 32, 27, "message"], [25, 32, 32, 35], [26, 10, 33, 12, "style"], [26, 15, 33, 17], [26, 17, 33, 19, "styles"], [26, 23, 33, 25], [26, 24, 33, 26, "substitutionText"], [27, 8, 33, 43], [27, 9, 34, 11], [28, 6, 35, 9], [28, 7, 36, 12], [29, 4, 36, 13], [29, 5, 37, 10], [29, 6, 37, 11], [30, 2, 39, 0], [31, 2, 41, 0], [31, 6, 41, 6, "styles"], [31, 12, 41, 12], [31, 15, 41, 15, "StyleSheet"], [31, 34, 41, 25], [31, 35, 41, 26, "create"], [31, 41, 41, 32], [31, 42, 41, 33], [32, 4, 42, 2, "container"], [32, 13, 42, 11], [32, 15, 42, 13], [33, 6, 43, 4, "alignSelf"], [33, 15, 43, 13], [33, 17, 43, 15], [33, 26, 43, 24], [34, 6, 44, 4, "flexGrow"], [34, 14, 44, 12], [34, 16, 44, 14], [34, 17, 44, 15], [35, 6, 45, 4, "flexShrink"], [35, 16, 45, 14], [35, 18, 45, 16], [35, 19, 45, 17], [36, 6, 46, 4, "flexBasis"], [36, 15, 46, 13], [36, 17, 46, 15], [36, 23, 46, 21], [37, 6, 47, 4, "borderLeftColor"], [37, 21, 47, 19], [37, 23, 47, 21, "LogBoxStyle"], [37, 34, 47, 32], [37, 35, 47, 33, "getTextColor"], [37, 47, 47, 45], [37, 48, 47, 46], [37, 51, 47, 49], [37, 52, 47, 50], [38, 6, 48, 4, "borderLeftWidth"], [38, 21, 48, 19], [38, 23, 48, 21], [38, 24, 48, 22], [39, 6, 49, 4, "paddingLeft"], [39, 17, 49, 15], [39, 19, 49, 17], [40, 4, 50, 2], [40, 5, 50, 3], [41, 4, 51, 2, "text"], [41, 8, 51, 6], [41, 10, 51, 8], [42, 6, 52, 4, "color"], [42, 11, 52, 9], [42, 13, 52, 11, "LogBoxStyle"], [42, 24, 52, 22], [42, 25, 52, 23, "getTextColor"], [42, 37, 52, 35], [42, 38, 52, 36], [42, 39, 52, 37], [42, 40, 52, 38], [43, 6, 53, 4, "flex"], [43, 10, 53, 8], [43, 12, 53, 10], [43, 13, 53, 11], [44, 6, 54, 4, "fontSize"], [44, 14, 54, 12], [44, 16, 54, 14], [44, 18, 54, 16], [45, 6, 55, 4, "lineHeight"], [45, 16, 55, 14], [45, 18, 55, 16], [46, 4, 56, 2], [46, 5, 56, 3], [47, 4, 57, 2, "substitutionText"], [47, 20, 57, 18], [47, 22, 57, 20], [48, 6, 58, 4, "color"], [48, 11, 58, 9], [48, 13, 58, 11, "LogBoxStyle"], [48, 24, 58, 22], [48, 25, 58, 23, "getTextColor"], [48, 37, 58, 35], [48, 38, 58, 36], [48, 41, 58, 39], [49, 4, 59, 2], [50, 2, 60, 0], [50, 3, 60, 1], [50, 4, 60, 2], [51, 0, 60, 3], [51, 3]], "functionMap": {"names": ["<global>", "LogBoxNotificationMessage"], "mappings": "AAA;eCmB;CDmB"}}, "type": "js/module"}]}