{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeDeviceInfo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 50}}], "key": "N5+ee9bimlR6FBjbpiwZkKSmvQk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeDeviceInfo = _interopRequireDefault(require(_dependencyMap[1], \"./NativeDeviceInfo\"));\n  var _default = exports.default = _NativeDeviceInfo.default;\n});", "lineCount": 9, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeDeviceInfo"], [7, 23, 11, 0], [7, 26, 11, 0, "_interopRequireDefault"], [7, 48, 11, 0], [7, 49, 11, 0, "require"], [7, 56, 11, 0], [7, 57, 11, 0, "_dependencyMap"], [7, 71, 11, 0], [8, 2, 11, 50], [8, 6, 11, 50, "_default"], [8, 14, 11, 50], [8, 17, 11, 50, "exports"], [8, 24, 11, 50], [8, 25, 11, 50, "default"], [8, 32, 11, 50], [8, 35, 13, 15, "NativeDeviceInfo"], [8, 60, 13, 31], [9, 0, 13, 31], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}