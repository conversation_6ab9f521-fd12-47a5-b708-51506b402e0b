{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../WorkletEventHandler.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 64, "index": 111}}], "key": "e5fG6a6nTf5/kChbWzO7k8c/97s=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useEvent = useEvent;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _WorkletEventHandler = require(_dependencyMap[1], \"../WorkletEventHandler.js\");\n  /** Worklet to provide as an argument to `useEvent` hook. */\n\n  /**\n   * Lets you run a function whenever a specified native event occurs.\n   *\n   * @param handler - A function that receives an event object with event data -\n   *   {@link EventHandler}.\n   * @param eventNames - An array of event names the `handler` callback will react\n   *   to.\n   * @param rebuild - Whether the event handler should be rebuilt. Defaults to\n   *   `false`.\n   * @returns A function that will be called when the event occurs -\n   *   {@link EventHandlerProcessed}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useEvent\n   */\n  // @ts-expect-error This overload is required by our API.\n  // We don't know which properites of a component that is made into\n  // an AnimatedComponent are event handlers and we don't want to force the user to define it.\n  // Therefore we disguise `useEvent` return type as a simple function and we handle\n  // it being a React Ref in `createAnimatedComponent`.\n\n  function useEvent(handler, eventNames = [], rebuild = false) {\n    const initRef = (0, _react.useRef)(null);\n    if (initRef.current === null) {\n      const workletEventHandler = new _WorkletEventHandler.WorkletEventHandler(handler, eventNames);\n      initRef.current = {\n        workletEventHandler\n      };\n    } else if (rebuild) {\n      const workletEventHandler = initRef.current.workletEventHandler;\n      workletEventHandler.updateEventHandler(handler, eventNames);\n      initRef.current = {\n        workletEventHandler\n      };\n    }\n    return initRef.current;\n  }\n});", "lineCount": 47, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useEvent"], [7, 18, 1, 13], [7, 21, 1, 13, "useEvent"], [7, 29, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_WorkletEventHandler"], [9, 26, 4, 0], [9, 29, 4, 0, "require"], [9, 36, 4, 0], [9, 37, 4, 0, "_dependencyMap"], [9, 51, 4, 0], [10, 2, 6, 0], [12, 2, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 0, 19, 0], [24, 0, 20, 0], [25, 2, 21, 0], [26, 2, 22, 0], [27, 2, 23, 0], [28, 2, 24, 0], [29, 2, 25, 0], [31, 2, 27, 7], [31, 11, 27, 16, "useEvent"], [31, 19, 27, 24, "useEvent"], [31, 20, 27, 25, "handler"], [31, 27, 27, 32], [31, 29, 27, 34, "eventNames"], [31, 39, 27, 44], [31, 42, 27, 47], [31, 44, 27, 49], [31, 46, 27, 51, "rebuild"], [31, 53, 27, 58], [31, 56, 27, 61], [31, 61, 27, 66], [31, 63, 27, 68], [32, 4, 28, 2], [32, 10, 28, 8, "initRef"], [32, 17, 28, 15], [32, 20, 28, 18], [32, 24, 28, 18, "useRef"], [32, 37, 28, 24], [32, 39, 28, 25], [32, 43, 28, 29], [32, 44, 28, 30], [33, 4, 29, 2], [33, 8, 29, 6, "initRef"], [33, 15, 29, 13], [33, 16, 29, 14, "current"], [33, 23, 29, 21], [33, 28, 29, 26], [33, 32, 29, 30], [33, 34, 29, 32], [34, 6, 30, 4], [34, 12, 30, 10, "workletEventHandler"], [34, 31, 30, 29], [34, 34, 30, 32], [34, 38, 30, 36, "WorkletEventHandler"], [34, 78, 30, 55], [34, 79, 30, 56, "handler"], [34, 86, 30, 63], [34, 88, 30, 65, "eventNames"], [34, 98, 30, 75], [34, 99, 30, 76], [35, 6, 31, 4, "initRef"], [35, 13, 31, 11], [35, 14, 31, 12, "current"], [35, 21, 31, 19], [35, 24, 31, 22], [36, 8, 32, 6, "workletEventHandler"], [37, 6, 33, 4], [37, 7, 33, 5], [38, 4, 34, 2], [38, 5, 34, 3], [38, 11, 34, 9], [38, 15, 34, 13, "rebuild"], [38, 22, 34, 20], [38, 24, 34, 22], [39, 6, 35, 4], [39, 12, 35, 10, "workletEventHandler"], [39, 31, 35, 29], [39, 34, 35, 32, "initRef"], [39, 41, 35, 39], [39, 42, 35, 40, "current"], [39, 49, 35, 47], [39, 50, 35, 48, "workletEventHandler"], [39, 69, 35, 67], [40, 6, 36, 4, "workletEventHandler"], [40, 25, 36, 23], [40, 26, 36, 24, "updateEventHandler"], [40, 44, 36, 42], [40, 45, 36, 43, "handler"], [40, 52, 36, 50], [40, 54, 36, 52, "eventNames"], [40, 64, 36, 62], [40, 65, 36, 63], [41, 6, 37, 4, "initRef"], [41, 13, 37, 11], [41, 14, 37, 12, "current"], [41, 21, 37, 19], [41, 24, 37, 22], [42, 8, 38, 6, "workletEventHandler"], [43, 6, 39, 4], [43, 7, 39, 5], [44, 4, 40, 2], [45, 4, 41, 2], [45, 11, 41, 9, "initRef"], [45, 18, 41, 16], [45, 19, 41, 17, "current"], [45, 26, 41, 24], [46, 2, 42, 0], [47, 0, 42, 1], [47, 3]], "functionMap": {"names": ["<global>", "useEvent"], "mappings": "AAA;OC0B;CDe"}}, "type": "js/module"}]}