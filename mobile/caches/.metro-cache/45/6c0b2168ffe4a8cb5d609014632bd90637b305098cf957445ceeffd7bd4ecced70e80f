{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 34, "index": 49}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 50}, "end": {"line": 4, "column": 53, "index": 103}}], "key": "t9lN+rBifYCuaIC+E0heKxRioMA=", "exportNames": ["*"]}}, {"name": "../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 104}, "end": {"line": 5, "column": 55, "index": 159}}], "key": "iJ0YgfbcPgrclB5t1J5j2jedwxA=", "exportNames": ["*"]}}, {"name": "./useSharedValue.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 160}, "end": {"line": 6, "column": 53, "index": 213}}], "key": "DVYfvqRexdAwKhee+lmO6Kkit04=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedReaction = useAnimatedReaction;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _core = require(_dependencyMap[1], \"../core.js\");\n  var _PlatformChecker = require(_dependencyMap[2], \"../PlatformChecker.js\");\n  var _useSharedValue = require(_dependencyMap[3], \"./useSharedValue.js\");\n  /**\n   * Lets you to respond to changes in a [shared\n   * value](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value).\n   * It's especially useful when comparing values previously stored in the shared\n   * value with the current one.\n   *\n   * @param prepare - A function that should return a value to which you'd like to\n   *   react.\n   * @param react - A function that reacts to changes in the value returned by the\n   *   `prepare` function.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useAnimatedReaction\n   */\n  // @ts-expect-error This overload is required by our API.\n  const _worklet_13931034839297_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedReactionJs1(){const{prepare,react,previous}=this.__closure;const input=prepare();react(input,previous.value);previous.value=input;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedReaction.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedReactionJs1\\\",\\\"prepare\\\",\\\"react\\\",\\\"previous\\\",\\\"__closure\\\",\\\"input\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedReaction.js\\\"],\\\"mappings\\\":\\\"AAsCgB,SAAAA,4CAAMA,CAAA,QAAAC,OAAA,CAAAC,KAAA,CAAAC,QAAA,OAAAC,SAAA,CAGhB,KAAM,CAAAC,KAAK,CAAGJ,OAAO,CAAC,CAAC,CACvBC,KAAK,CAACG,KAAK,CAAEF,QAAQ,CAACG,KAAK,CAAC,CAC5BH,QAAQ,CAACG,KAAK,CAAGD,KAAK,CACxB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedReaction(prepare, react, dependencies) {\n    const previous = (0, _useSharedValue.useSharedValue)(null);\n    let inputs = Object.values(prepare.__closure ?? {});\n    if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n      if (!inputs.length && dependencies?.length) {\n        // let web work without a Reanimated Babel plugin\n        inputs = dependencies;\n      }\n    }\n    if (dependencies === undefined) {\n      dependencies = [...Object.values(prepare.__closure ?? {}), ...Object.values(react.__closure ?? {}), prepare.__workletHash, react.__workletHash];\n    } else {\n      dependencies.push(prepare.__workletHash, react.__workletHash);\n    }\n    (0, _react.useEffect)(() => {\n      const fun = function () {\n        const _e = [new global.Error(), -4, -27];\n        const reactNativeReanimated_useAnimatedReactionJs1 = function () {\n          const input = prepare();\n          react(input, previous.value);\n          previous.value = input;\n        };\n        reactNativeReanimated_useAnimatedReactionJs1.__closure = {\n          prepare,\n          react,\n          previous\n        };\n        reactNativeReanimated_useAnimatedReactionJs1.__workletHash = 13931034839297;\n        reactNativeReanimated_useAnimatedReactionJs1.__initData = _worklet_13931034839297_init_data;\n        reactNativeReanimated_useAnimatedReactionJs1.__stackDetails = _e;\n        return reactNativeReanimated_useAnimatedReactionJs1;\n      }();\n      const mapperId = (0, _core.startMapper)(fun, inputs);\n      return () => {\n        (0, _core.stopMapper)(mapperId);\n      };\n    }, dependencies);\n  }\n});", "lineCount": 71, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedReaction"], [7, 29, 1, 13], [7, 32, 1, 13, "useAnimatedReaction"], [7, 51, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_core"], [9, 11, 4, 0], [9, 14, 4, 0, "require"], [9, 21, 4, 0], [9, 22, 4, 0, "_dependencyMap"], [9, 36, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_PlatformChecker"], [10, 22, 5, 0], [10, 25, 5, 0, "require"], [10, 32, 5, 0], [10, 33, 5, 0, "_dependencyMap"], [10, 47, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_useSharedValue"], [11, 21, 6, 0], [11, 24, 6, 0, "require"], [11, 31, 6, 0], [11, 32, 6, 0, "_dependencyMap"], [11, 46, 6, 0], [12, 2, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 0, 19, 0], [24, 0, 20, 0], [25, 0, 21, 0], [26, 2, 22, 0], [27, 2, 22, 0], [27, 8, 22, 0, "_worklet_13931034839297_init_data"], [27, 41, 22, 0], [28, 4, 22, 0, "code"], [28, 8, 22, 0], [29, 4, 22, 0, "location"], [29, 12, 22, 0], [30, 4, 22, 0, "sourceMap"], [30, 13, 22, 0], [31, 4, 22, 0, "version"], [31, 11, 22, 0], [32, 2, 22, 0], [33, 2, 24, 7], [33, 11, 24, 16, "useAnimatedReaction"], [33, 30, 24, 35, "useAnimatedReaction"], [33, 31, 24, 36, "prepare"], [33, 38, 24, 43], [33, 40, 24, 45, "react"], [33, 45, 24, 50], [33, 47, 24, 52, "dependencies"], [33, 59, 24, 64], [33, 61, 24, 66], [34, 4, 25, 2], [34, 10, 25, 8, "previous"], [34, 18, 25, 16], [34, 21, 25, 19], [34, 25, 25, 19, "useSharedValue"], [34, 55, 25, 33], [34, 57, 25, 34], [34, 61, 25, 38], [34, 62, 25, 39], [35, 4, 26, 2], [35, 8, 26, 6, "inputs"], [35, 14, 26, 12], [35, 17, 26, 15, "Object"], [35, 23, 26, 21], [35, 24, 26, 22, "values"], [35, 30, 26, 28], [35, 31, 26, 29, "prepare"], [35, 38, 26, 36], [35, 39, 26, 37, "__closure"], [35, 48, 26, 46], [35, 52, 26, 50], [35, 53, 26, 51], [35, 54, 26, 52], [35, 55, 26, 53], [36, 4, 27, 2], [36, 8, 27, 6], [36, 12, 27, 6, "shouldBeUseWeb"], [36, 43, 27, 20], [36, 45, 27, 21], [36, 46, 27, 22], [36, 48, 27, 24], [37, 6, 28, 4], [37, 10, 28, 8], [37, 11, 28, 9, "inputs"], [37, 17, 28, 15], [37, 18, 28, 16, "length"], [37, 24, 28, 22], [37, 28, 28, 26, "dependencies"], [37, 40, 28, 38], [37, 42, 28, 40, "length"], [37, 48, 28, 46], [37, 50, 28, 48], [38, 8, 29, 6], [39, 8, 30, 6, "inputs"], [39, 14, 30, 12], [39, 17, 30, 15, "dependencies"], [39, 29, 30, 27], [40, 6, 31, 4], [41, 4, 32, 2], [42, 4, 33, 2], [42, 8, 33, 6, "dependencies"], [42, 20, 33, 18], [42, 25, 33, 23, "undefined"], [42, 34, 33, 32], [42, 36, 33, 34], [43, 6, 34, 4, "dependencies"], [43, 18, 34, 16], [43, 21, 34, 19], [43, 22, 34, 20], [43, 25, 34, 23, "Object"], [43, 31, 34, 29], [43, 32, 34, 30, "values"], [43, 38, 34, 36], [43, 39, 34, 37, "prepare"], [43, 46, 34, 44], [43, 47, 34, 45, "__closure"], [43, 56, 34, 54], [43, 60, 34, 58], [43, 61, 34, 59], [43, 62, 34, 60], [43, 63, 34, 61], [43, 65, 34, 63], [43, 68, 34, 66, "Object"], [43, 74, 34, 72], [43, 75, 34, 73, "values"], [43, 81, 34, 79], [43, 82, 34, 80, "react"], [43, 87, 34, 85], [43, 88, 34, 86, "__closure"], [43, 97, 34, 95], [43, 101, 34, 99], [43, 102, 34, 100], [43, 103, 34, 101], [43, 104, 34, 102], [43, 106, 34, 104, "prepare"], [43, 113, 34, 111], [43, 114, 34, 112, "__workletHash"], [43, 127, 34, 125], [43, 129, 34, 127, "react"], [43, 134, 34, 132], [43, 135, 34, 133, "__workletHash"], [43, 148, 34, 146], [43, 149, 34, 147], [44, 4, 35, 2], [44, 5, 35, 3], [44, 11, 35, 9], [45, 6, 36, 4, "dependencies"], [45, 18, 36, 16], [45, 19, 36, 17, "push"], [45, 23, 36, 21], [45, 24, 36, 22, "prepare"], [45, 31, 36, 29], [45, 32, 36, 30, "__workletHash"], [45, 45, 36, 43], [45, 47, 36, 45, "react"], [45, 52, 36, 50], [45, 53, 36, 51, "__workletHash"], [45, 66, 36, 64], [45, 67, 36, 65], [46, 4, 37, 2], [47, 4, 38, 2], [47, 8, 38, 2, "useEffect"], [47, 24, 38, 11], [47, 26, 38, 12], [47, 32, 38, 18], [48, 6, 39, 4], [48, 12, 39, 10, "fun"], [48, 15, 39, 13], [48, 18, 39, 16], [49, 8, 39, 16], [49, 14, 39, 16, "_e"], [49, 16, 39, 16], [49, 24, 39, 16, "global"], [49, 30, 39, 16], [49, 31, 39, 16, "Error"], [49, 36, 39, 16], [50, 8, 39, 16], [50, 14, 39, 16, "reactNativeReanimated_useAnimatedReactionJs1"], [50, 58, 39, 16], [50, 70, 39, 16, "reactNativeReanimated_useAnimatedReactionJs1"], [50, 71, 39, 16], [50, 73, 39, 22], [51, 10, 42, 6], [51, 16, 42, 12, "input"], [51, 21, 42, 17], [51, 24, 42, 20, "prepare"], [51, 31, 42, 27], [51, 32, 42, 28], [51, 33, 42, 29], [52, 10, 43, 6, "react"], [52, 15, 43, 11], [52, 16, 43, 12, "input"], [52, 21, 43, 17], [52, 23, 43, 19, "previous"], [52, 31, 43, 27], [52, 32, 43, 28, "value"], [52, 37, 43, 33], [52, 38, 43, 34], [53, 10, 44, 6, "previous"], [53, 18, 44, 14], [53, 19, 44, 15, "value"], [53, 24, 44, 20], [53, 27, 44, 23, "input"], [53, 32, 44, 28], [54, 8, 45, 4], [54, 9, 45, 5], [55, 8, 45, 5, "reactNativeReanimated_useAnimatedReactionJs1"], [55, 52, 45, 5], [55, 53, 45, 5, "__closure"], [55, 62, 45, 5], [56, 10, 45, 5, "prepare"], [56, 17, 45, 5], [57, 10, 45, 5, "react"], [57, 15, 45, 5], [58, 10, 45, 5, "previous"], [59, 8, 45, 5], [60, 8, 45, 5, "reactNativeReanimated_useAnimatedReactionJs1"], [60, 52, 45, 5], [60, 53, 45, 5, "__workletHash"], [60, 66, 45, 5], [61, 8, 45, 5, "reactNativeReanimated_useAnimatedReactionJs1"], [61, 52, 45, 5], [61, 53, 45, 5, "__initData"], [61, 63, 45, 5], [61, 66, 45, 5, "_worklet_13931034839297_init_data"], [61, 99, 45, 5], [62, 8, 45, 5, "reactNativeReanimated_useAnimatedReactionJs1"], [62, 52, 45, 5], [62, 53, 45, 5, "__stackDetails"], [62, 67, 45, 5], [62, 70, 45, 5, "_e"], [62, 72, 45, 5], [63, 8, 45, 5], [63, 15, 45, 5, "reactNativeReanimated_useAnimatedReactionJs1"], [63, 59, 45, 5], [64, 6, 45, 5], [64, 7, 39, 16], [64, 9, 45, 5], [65, 6, 46, 4], [65, 12, 46, 10, "mapperId"], [65, 20, 46, 18], [65, 23, 46, 21], [65, 27, 46, 21, "startMapper"], [65, 44, 46, 32], [65, 46, 46, 33, "fun"], [65, 49, 46, 36], [65, 51, 46, 38, "inputs"], [65, 57, 46, 44], [65, 58, 46, 45], [66, 6, 47, 4], [66, 13, 47, 11], [66, 19, 47, 17], [67, 8, 48, 6], [67, 12, 48, 6, "stopMapper"], [67, 28, 48, 16], [67, 30, 48, 17, "mapperId"], [67, 38, 48, 25], [67, 39, 48, 26], [68, 6, 49, 4], [68, 7, 49, 5], [69, 4, 50, 2], [69, 5, 50, 3], [69, 7, 50, 5, "dependencies"], [69, 19, 50, 17], [69, 20, 50, 18], [70, 2, 51, 0], [71, 0, 51, 1], [71, 3]], "functionMap": {"names": ["<global>", "useAnimatedReaction", "useEffect$argument_0", "fun", "<anonymous>"], "mappings": "AAA;OCuB;YCc;gBCC;KDM;WEE;KFE;GDC;CDC"}}, "type": "js/module"}]}