{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getInitialSafeArea = getInitialSafeArea;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var DEFAULT_SAFE_AREA = {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0\n  };\n\n  /**\n   * Get the best estimate safe area before native modules have fully loaded.\n   * This is a hack to get the safe area insets without explicitly depending on react-native-safe-area-context.\n   */\n  function getInitialSafeArea() {\n    var RNCSafeAreaContext = _reactNative.TurboModuleRegistry.get('RNCSafeAreaContext');\n\n    // @ts-ignore: we're not using the spec so the return type of getConstants() is {}\n    var initialWindowMetrics = RNCSafeAreaContext?.getConstants()?.initialWindowMetrics;\n    return initialWindowMetrics?.insets ?? DEFAULT_SAFE_AREA;\n  }\n});", "lineCount": 25, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 3, 0], [7, 6, 3, 6, "DEFAULT_SAFE_AREA"], [7, 23, 3, 23], [7, 26, 3, 26], [8, 4, 3, 28, "top"], [8, 7, 3, 31], [8, 9, 3, 33], [8, 10, 3, 34], [9, 4, 3, 36, "bottom"], [9, 10, 3, 42], [9, 12, 3, 44], [9, 13, 3, 45], [10, 4, 3, 47, "left"], [10, 8, 3, 51], [10, 10, 3, 53], [10, 11, 3, 54], [11, 4, 3, 56, "right"], [11, 9, 3, 61], [11, 11, 3, 63], [12, 2, 3, 65], [12, 3, 3, 66], [14, 2, 5, 0], [15, 0, 6, 0], [16, 0, 7, 0], [17, 0, 8, 0], [18, 2, 9, 7], [18, 11, 9, 16, "getInitialSafeArea"], [18, 29, 9, 34, "getInitialSafeArea"], [18, 30, 9, 34], [18, 32, 9, 99], [19, 4, 10, 2], [19, 8, 10, 8, "RNCSafeAreaContext"], [19, 26, 10, 26], [19, 29, 10, 29, "TurboModuleRegistry"], [19, 61, 10, 48], [19, 62, 10, 49, "get"], [19, 65, 10, 52], [19, 66, 10, 53], [19, 86, 10, 73], [19, 87, 10, 74], [21, 4, 12, 2], [22, 4, 13, 2], [22, 8, 13, 8, "initialWindowMetrics"], [22, 28, 13, 28], [22, 31, 13, 31, "RNCSafeAreaContext"], [22, 49, 13, 49], [22, 51, 13, 51, "getConstants"], [22, 63, 13, 63], [22, 64, 13, 64], [22, 65, 13, 65], [22, 67, 13, 67, "initialWindowMetrics"], [22, 87, 13, 87], [23, 4, 15, 2], [23, 11, 15, 9, "initialWindowMetrics"], [23, 31, 15, 29], [23, 33, 15, 31, "insets"], [23, 39, 15, 37], [23, 43, 15, 41, "DEFAULT_SAFE_AREA"], [23, 60, 15, 58], [24, 2, 16, 0], [25, 0, 16, 1], [25, 3]], "functionMap": {"names": ["<global>", "getInitialSafeArea"], "mappings": "AAA;OCQ;CDO"}}, "type": "js/module"}]}