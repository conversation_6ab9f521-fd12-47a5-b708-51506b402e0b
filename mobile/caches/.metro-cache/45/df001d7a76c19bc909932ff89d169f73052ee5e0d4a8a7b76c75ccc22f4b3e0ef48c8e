{"dependencies": [{"name": "./useEvent.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 41, "index": 56}}], "key": "lBxuQoQIRmtUdzkIqbHyc7lI5BM=", "exportNames": ["*"]}}, {"name": "./useHandler.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 57}, "end": {"line": 4, "column": 45, "index": 102}}], "key": "8THmWA5AJY3Y43KoqeF3/ctnJfY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedGestureHandler = useAnimatedGestureHandler;\n  var _useEvent = require(_dependencyMap[0], \"./useEvent.js\");\n  var _useHandler = require(_dependencyMap[1], \"./useHandler.js\");\n  const EVENT_TYPE = {\n    UNDETERMINED: 0,\n    FAILED: 1,\n    BEGAN: 2,\n    CANCELLED: 3,\n    ACTIVE: 4,\n    END: 5\n  };\n\n  // This type comes from React Native Gesture Handler\n  // import type { PanGestureHandlerGestureEvent as DefaultEvent } from 'react-native-gesture-handler';\n\n  /**\n   * @deprecated UseAnimatedGestureHandler is an old API which is no longer\n   *   supported.\n   *\n   *   Please check\n   *   https://docs.swmansion.com/react-native-gesture-handler/docs/guides/upgrading-to-2/\n   *   for information about how to migrate to `react-native-gesture-handler` v2\n   */\n  const _worklet_3268817111188_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedGestureHandlerJs1(e){const{useWeb,EVENT_TYPE,handlers,context}=this.__closure;const event=useWeb?e.nativeEvent:e;if(event.state===EVENT_TYPE.BEGAN&&handlers.onStart){handlers.onStart(event,context);}if(event.state===EVENT_TYPE.ACTIVE&&handlers.onActive){handlers.onActive(event,context);}if(event.oldState===EVENT_TYPE.ACTIVE&&event.state===EVENT_TYPE.END&&handlers.onEnd){handlers.onEnd(event,context);}if(event.oldState===EVENT_TYPE.BEGAN&&event.state===EVENT_TYPE.FAILED&&handlers.onFail){handlers.onFail(event,context);}if(event.oldState===EVENT_TYPE.ACTIVE&&event.state===EVENT_TYPE.CANCELLED&&handlers.onCancel){handlers.onCancel(event,context);}if((event.oldState===EVENT_TYPE.BEGAN||event.oldState===EVENT_TYPE.ACTIVE)&&event.state!==EVENT_TYPE.BEGAN&&event.state!==EVENT_TYPE.ACTIVE&&handlers.onFinish){handlers.onFinish(event,context,event.state===EVENT_TYPE.CANCELLED||event.state===EVENT_TYPE.FAILED);}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedGestureHandler.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedGestureHandlerJs1\\\",\\\"e\\\",\\\"useWeb\\\",\\\"EVENT_TYPE\\\",\\\"handlers\\\",\\\"context\\\",\\\"__closure\\\",\\\"event\\\",\\\"nativeEvent\\\",\\\"state\\\",\\\"BEGAN\\\",\\\"onStart\\\",\\\"ACTIVE\\\",\\\"onActive\\\",\\\"oldState\\\",\\\"END\\\",\\\"onEnd\\\",\\\"FAILED\\\",\\\"onFail\\\",\\\"CANCELLED\\\",\\\"onCancel\\\",\\\"onFinish\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedGestureHandler.js\\\"],\\\"mappings\\\":\\\"AA8BkB,QAAC,CAAAA,kDAAIA,CAAAC,CAAA,QAAAC,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAC,OAAA,OAAAC,SAAA,CAGnB,KAAM,CAAAC,KAAK,CAAGL,MAAM,CAIpBD,CAAC,CAACO,WAAW,CAAGP,CAAC,CACjB,GAAIM,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACO,KAAK,EAAIN,QAAQ,CAACO,OAAO,CAAE,CACxDP,QAAQ,CAACO,OAAO,CAACJ,KAAK,CAAEF,OAAO,CAAC,CAClC,CACA,GAAIE,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACS,MAAM,EAAIR,QAAQ,CAACS,QAAQ,CAAE,CAC1DT,QAAQ,CAACS,QAAQ,CAACN,KAAK,CAAEF,OAAO,CAAC,CACnC,CACA,GAAIE,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACS,MAAM,EAAIL,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACY,GAAG,EAAIX,QAAQ,CAACY,KAAK,CAAE,CAC5FZ,QAAQ,CAACY,KAAK,CAACT,KAAK,CAAEF,OAAO,CAAC,CAChC,CACA,GAAIE,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACO,KAAK,EAAIH,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACc,MAAM,EAAIb,QAAQ,CAACc,MAAM,CAAE,CAC/Fd,QAAQ,CAACc,MAAM,CAACX,KAAK,CAAEF,OAAO,CAAC,CACjC,CACA,GAAIE,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACS,MAAM,EAAIL,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACgB,SAAS,EAAIf,QAAQ,CAACgB,QAAQ,CAAE,CACrGhB,QAAQ,CAACgB,QAAQ,CAACb,KAAK,CAAEF,OAAO,CAAC,CACnC,CACA,GAAI,CAACE,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACO,KAAK,EAAIH,KAAK,CAACO,QAAQ,GAAKX,UAAU,CAACS,MAAM,GAAKL,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACO,KAAK,EAAIH,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACS,MAAM,EAAIR,QAAQ,CAACiB,QAAQ,CAAE,CAC/KjB,QAAQ,CAACiB,QAAQ,CAACd,KAAK,CAAEF,OAAO,CAAEE,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACgB,SAAS,EAAIZ,KAAK,CAACE,KAAK,GAAKN,UAAU,CAACc,MAAM,CAAC,CAC9G,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedGestureHandler(handlers, dependencies) {\n    const {\n      context,\n      doDependenciesDiffer,\n      useWeb\n    } = (0, _useHandler.useHandler)(handlers, dependencies);\n    const handler = function () {\n      const _e = [new global.Error(), -5, -27];\n      const reactNativeReanimated_useAnimatedGestureHandlerJs1 = function (e) {\n        const event = useWeb ?\n        // On Web we get events straight from React Native and they don't have\n        // the `eventName` field there. To simplify the types here we just\n        // cast it as the field was available.\n        e.nativeEvent : e;\n        if (event.state === EVENT_TYPE.BEGAN && handlers.onStart) {\n          handlers.onStart(event, context);\n        }\n        if (event.state === EVENT_TYPE.ACTIVE && handlers.onActive) {\n          handlers.onActive(event, context);\n        }\n        if (event.oldState === EVENT_TYPE.ACTIVE && event.state === EVENT_TYPE.END && handlers.onEnd) {\n          handlers.onEnd(event, context);\n        }\n        if (event.oldState === EVENT_TYPE.BEGAN && event.state === EVENT_TYPE.FAILED && handlers.onFail) {\n          handlers.onFail(event, context);\n        }\n        if (event.oldState === EVENT_TYPE.ACTIVE && event.state === EVENT_TYPE.CANCELLED && handlers.onCancel) {\n          handlers.onCancel(event, context);\n        }\n        if ((event.oldState === EVENT_TYPE.BEGAN || event.oldState === EVENT_TYPE.ACTIVE) && event.state !== EVENT_TYPE.BEGAN && event.state !== EVENT_TYPE.ACTIVE && handlers.onFinish) {\n          handlers.onFinish(event, context, event.state === EVENT_TYPE.CANCELLED || event.state === EVENT_TYPE.FAILED);\n        }\n      };\n      reactNativeReanimated_useAnimatedGestureHandlerJs1.__closure = {\n        useWeb,\n        EVENT_TYPE,\n        handlers,\n        context\n      };\n      reactNativeReanimated_useAnimatedGestureHandlerJs1.__workletHash = 3268817111188;\n      reactNativeReanimated_useAnimatedGestureHandlerJs1.__initData = _worklet_3268817111188_init_data;\n      reactNativeReanimated_useAnimatedGestureHandlerJs1.__stackDetails = _e;\n      return reactNativeReanimated_useAnimatedGestureHandlerJs1;\n    }();\n    if (useWeb) {\n      return handler;\n    }\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return (0, _useEvent.useEvent)(handler, ['onGestureHandlerStateChange', 'onGestureHandlerEvent'], doDependenciesDiffer\n    // This is not correct but we want to make GH think it receives a function.\n    );\n  }\n});", "lineCount": 89, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedGestureHandler"], [7, 35, 1, 13], [7, 38, 1, 13, "useAnimatedGestureHandler"], [7, 63, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_useEvent"], [8, 15, 3, 0], [8, 18, 3, 0, "require"], [8, 25, 3, 0], [8, 26, 3, 0, "_dependencyMap"], [8, 40, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_use<PERSON><PERSON>ler"], [9, 17, 4, 0], [9, 20, 4, 0, "require"], [9, 27, 4, 0], [9, 28, 4, 0, "_dependencyMap"], [9, 42, 4, 0], [10, 2, 5, 0], [10, 8, 5, 6, "EVENT_TYPE"], [10, 18, 5, 16], [10, 21, 5, 19], [11, 4, 6, 2, "UNDETERMINED"], [11, 16, 6, 14], [11, 18, 6, 16], [11, 19, 6, 17], [12, 4, 7, 2, "FAILED"], [12, 10, 7, 8], [12, 12, 7, 10], [12, 13, 7, 11], [13, 4, 8, 2, "BEGAN"], [13, 9, 8, 7], [13, 11, 8, 9], [13, 12, 8, 10], [14, 4, 9, 2, "CANCELLED"], [14, 13, 9, 11], [14, 15, 9, 13], [14, 16, 9, 14], [15, 4, 10, 2, "ACTIVE"], [15, 10, 10, 8], [15, 12, 10, 10], [15, 13, 10, 11], [16, 4, 11, 2, "END"], [16, 7, 11, 5], [16, 9, 11, 7], [17, 2, 12, 0], [17, 3, 12, 1], [19, 2, 14, 0], [20, 2, 15, 0], [22, 2, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 0, 21, 0], [27, 0, 22, 0], [28, 0, 23, 0], [29, 0, 24, 0], [30, 2, 17, 0], [30, 8, 17, 0, "_worklet_3268817111188_init_data"], [30, 40, 17, 0], [31, 4, 17, 0, "code"], [31, 8, 17, 0], [32, 4, 17, 0, "location"], [32, 12, 17, 0], [33, 4, 17, 0, "sourceMap"], [33, 13, 17, 0], [34, 4, 17, 0, "version"], [34, 11, 17, 0], [35, 2, 17, 0], [36, 2, 25, 7], [36, 11, 25, 16, "useAnimatedGestureHandler"], [36, 36, 25, 41, "useAnimatedGestureHandler"], [36, 37, 25, 42, "handlers"], [36, 45, 25, 50], [36, 47, 25, 52, "dependencies"], [36, 59, 25, 64], [36, 61, 25, 66], [37, 4, 26, 2], [37, 10, 26, 8], [38, 6, 27, 4, "context"], [38, 13, 27, 11], [39, 6, 28, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [39, 26, 28, 24], [40, 6, 29, 4, "useWeb"], [41, 4, 30, 2], [41, 5, 30, 3], [41, 8, 30, 6], [41, 12, 30, 6, "useHandler"], [41, 34, 30, 16], [41, 36, 30, 17, "handlers"], [41, 44, 30, 25], [41, 46, 30, 27, "dependencies"], [41, 58, 30, 39], [41, 59, 30, 40], [42, 4, 31, 2], [42, 10, 31, 8, "handler"], [42, 17, 31, 15], [42, 20, 31, 18], [43, 6, 31, 18], [43, 12, 31, 18, "_e"], [43, 14, 31, 18], [43, 22, 31, 18, "global"], [43, 28, 31, 18], [43, 29, 31, 18, "Error"], [43, 34, 31, 18], [44, 6, 31, 18], [44, 12, 31, 18, "reactNativeReanimated_useAnimatedGestureHandlerJs1"], [44, 62, 31, 18], [44, 74, 31, 18, "reactNativeReanimated_useAnimatedGestureHandlerJs1"], [44, 75, 31, 18, "e"], [44, 76, 31, 19], [44, 78, 31, 23], [45, 8, 34, 4], [45, 14, 34, 10, "event"], [45, 19, 34, 15], [45, 22, 34, 18, "useWeb"], [45, 28, 34, 24], [46, 8, 35, 4], [47, 8, 36, 4], [48, 8, 37, 4], [49, 8, 38, 4, "e"], [49, 9, 38, 5], [49, 10, 38, 6, "nativeEvent"], [49, 21, 38, 17], [49, 24, 38, 20, "e"], [49, 25, 38, 21], [50, 8, 39, 4], [50, 12, 39, 8, "event"], [50, 17, 39, 13], [50, 18, 39, 14, "state"], [50, 23, 39, 19], [50, 28, 39, 24, "EVENT_TYPE"], [50, 38, 39, 34], [50, 39, 39, 35, "BEGAN"], [50, 44, 39, 40], [50, 48, 39, 44, "handlers"], [50, 56, 39, 52], [50, 57, 39, 53, "onStart"], [50, 64, 39, 60], [50, 66, 39, 62], [51, 10, 40, 6, "handlers"], [51, 18, 40, 14], [51, 19, 40, 15, "onStart"], [51, 26, 40, 22], [51, 27, 40, 23, "event"], [51, 32, 40, 28], [51, 34, 40, 30, "context"], [51, 41, 40, 37], [51, 42, 40, 38], [52, 8, 41, 4], [53, 8, 42, 4], [53, 12, 42, 8, "event"], [53, 17, 42, 13], [53, 18, 42, 14, "state"], [53, 23, 42, 19], [53, 28, 42, 24, "EVENT_TYPE"], [53, 38, 42, 34], [53, 39, 42, 35, "ACTIVE"], [53, 45, 42, 41], [53, 49, 42, 45, "handlers"], [53, 57, 42, 53], [53, 58, 42, 54, "onActive"], [53, 66, 42, 62], [53, 68, 42, 64], [54, 10, 43, 6, "handlers"], [54, 18, 43, 14], [54, 19, 43, 15, "onActive"], [54, 27, 43, 23], [54, 28, 43, 24, "event"], [54, 33, 43, 29], [54, 35, 43, 31, "context"], [54, 42, 43, 38], [54, 43, 43, 39], [55, 8, 44, 4], [56, 8, 45, 4], [56, 12, 45, 8, "event"], [56, 17, 45, 13], [56, 18, 45, 14, "oldState"], [56, 26, 45, 22], [56, 31, 45, 27, "EVENT_TYPE"], [56, 41, 45, 37], [56, 42, 45, 38, "ACTIVE"], [56, 48, 45, 44], [56, 52, 45, 48, "event"], [56, 57, 45, 53], [56, 58, 45, 54, "state"], [56, 63, 45, 59], [56, 68, 45, 64, "EVENT_TYPE"], [56, 78, 45, 74], [56, 79, 45, 75, "END"], [56, 82, 45, 78], [56, 86, 45, 82, "handlers"], [56, 94, 45, 90], [56, 95, 45, 91, "onEnd"], [56, 100, 45, 96], [56, 102, 45, 98], [57, 10, 46, 6, "handlers"], [57, 18, 46, 14], [57, 19, 46, 15, "onEnd"], [57, 24, 46, 20], [57, 25, 46, 21, "event"], [57, 30, 46, 26], [57, 32, 46, 28, "context"], [57, 39, 46, 35], [57, 40, 46, 36], [58, 8, 47, 4], [59, 8, 48, 4], [59, 12, 48, 8, "event"], [59, 17, 48, 13], [59, 18, 48, 14, "oldState"], [59, 26, 48, 22], [59, 31, 48, 27, "EVENT_TYPE"], [59, 41, 48, 37], [59, 42, 48, 38, "BEGAN"], [59, 47, 48, 43], [59, 51, 48, 47, "event"], [59, 56, 48, 52], [59, 57, 48, 53, "state"], [59, 62, 48, 58], [59, 67, 48, 63, "EVENT_TYPE"], [59, 77, 48, 73], [59, 78, 48, 74, "FAILED"], [59, 84, 48, 80], [59, 88, 48, 84, "handlers"], [59, 96, 48, 92], [59, 97, 48, 93, "onFail"], [59, 103, 48, 99], [59, 105, 48, 101], [60, 10, 49, 6, "handlers"], [60, 18, 49, 14], [60, 19, 49, 15, "onFail"], [60, 25, 49, 21], [60, 26, 49, 22, "event"], [60, 31, 49, 27], [60, 33, 49, 29, "context"], [60, 40, 49, 36], [60, 41, 49, 37], [61, 8, 50, 4], [62, 8, 51, 4], [62, 12, 51, 8, "event"], [62, 17, 51, 13], [62, 18, 51, 14, "oldState"], [62, 26, 51, 22], [62, 31, 51, 27, "EVENT_TYPE"], [62, 41, 51, 37], [62, 42, 51, 38, "ACTIVE"], [62, 48, 51, 44], [62, 52, 51, 48, "event"], [62, 57, 51, 53], [62, 58, 51, 54, "state"], [62, 63, 51, 59], [62, 68, 51, 64, "EVENT_TYPE"], [62, 78, 51, 74], [62, 79, 51, 75, "CANCELLED"], [62, 88, 51, 84], [62, 92, 51, 88, "handlers"], [62, 100, 51, 96], [62, 101, 51, 97, "onCancel"], [62, 109, 51, 105], [62, 111, 51, 107], [63, 10, 52, 6, "handlers"], [63, 18, 52, 14], [63, 19, 52, 15, "onCancel"], [63, 27, 52, 23], [63, 28, 52, 24, "event"], [63, 33, 52, 29], [63, 35, 52, 31, "context"], [63, 42, 52, 38], [63, 43, 52, 39], [64, 8, 53, 4], [65, 8, 54, 4], [65, 12, 54, 8], [65, 13, 54, 9, "event"], [65, 18, 54, 14], [65, 19, 54, 15, "oldState"], [65, 27, 54, 23], [65, 32, 54, 28, "EVENT_TYPE"], [65, 42, 54, 38], [65, 43, 54, 39, "BEGAN"], [65, 48, 54, 44], [65, 52, 54, 48, "event"], [65, 57, 54, 53], [65, 58, 54, 54, "oldState"], [65, 66, 54, 62], [65, 71, 54, 67, "EVENT_TYPE"], [65, 81, 54, 77], [65, 82, 54, 78, "ACTIVE"], [65, 88, 54, 84], [65, 93, 54, 89, "event"], [65, 98, 54, 94], [65, 99, 54, 95, "state"], [65, 104, 54, 100], [65, 109, 54, 105, "EVENT_TYPE"], [65, 119, 54, 115], [65, 120, 54, 116, "BEGAN"], [65, 125, 54, 121], [65, 129, 54, 125, "event"], [65, 134, 54, 130], [65, 135, 54, 131, "state"], [65, 140, 54, 136], [65, 145, 54, 141, "EVENT_TYPE"], [65, 155, 54, 151], [65, 156, 54, 152, "ACTIVE"], [65, 162, 54, 158], [65, 166, 54, 162, "handlers"], [65, 174, 54, 170], [65, 175, 54, 171, "onFinish"], [65, 183, 54, 179], [65, 185, 54, 181], [66, 10, 55, 6, "handlers"], [66, 18, 55, 14], [66, 19, 55, 15, "onFinish"], [66, 27, 55, 23], [66, 28, 55, 24, "event"], [66, 33, 55, 29], [66, 35, 55, 31, "context"], [66, 42, 55, 38], [66, 44, 55, 40, "event"], [66, 49, 55, 45], [66, 50, 55, 46, "state"], [66, 55, 55, 51], [66, 60, 55, 56, "EVENT_TYPE"], [66, 70, 55, 66], [66, 71, 55, 67, "CANCELLED"], [66, 80, 55, 76], [66, 84, 55, 80, "event"], [66, 89, 55, 85], [66, 90, 55, 86, "state"], [66, 95, 55, 91], [66, 100, 55, 96, "EVENT_TYPE"], [66, 110, 55, 106], [66, 111, 55, 107, "FAILED"], [66, 117, 55, 113], [66, 118, 55, 114], [67, 8, 56, 4], [68, 6, 57, 2], [68, 7, 57, 3], [69, 6, 57, 3, "reactNativeReanimated_useAnimatedGestureHandlerJs1"], [69, 56, 57, 3], [69, 57, 57, 3, "__closure"], [69, 66, 57, 3], [70, 8, 57, 3, "useWeb"], [70, 14, 57, 3], [71, 8, 57, 3, "EVENT_TYPE"], [71, 18, 57, 3], [72, 8, 57, 3, "handlers"], [72, 16, 57, 3], [73, 8, 57, 3, "context"], [74, 6, 57, 3], [75, 6, 57, 3, "reactNativeReanimated_useAnimatedGestureHandlerJs1"], [75, 56, 57, 3], [75, 57, 57, 3, "__workletHash"], [75, 70, 57, 3], [76, 6, 57, 3, "reactNativeReanimated_useAnimatedGestureHandlerJs1"], [76, 56, 57, 3], [76, 57, 57, 3, "__initData"], [76, 67, 57, 3], [76, 70, 57, 3, "_worklet_3268817111188_init_data"], [76, 102, 57, 3], [77, 6, 57, 3, "reactNativeReanimated_useAnimatedGestureHandlerJs1"], [77, 56, 57, 3], [77, 57, 57, 3, "__stackDetails"], [77, 71, 57, 3], [77, 74, 57, 3, "_e"], [77, 76, 57, 3], [78, 6, 57, 3], [78, 13, 57, 3, "reactNativeReanimated_useAnimatedGestureHandlerJs1"], [78, 63, 57, 3], [79, 4, 57, 3], [79, 5, 31, 18], [79, 7, 57, 3], [80, 4, 58, 2], [80, 8, 58, 6, "useWeb"], [80, 14, 58, 12], [80, 16, 58, 14], [81, 6, 59, 4], [81, 13, 59, 11, "handler"], [81, 20, 59, 18], [82, 4, 60, 2], [84, 4, 62, 2], [85, 4, 63, 2], [85, 11, 63, 9], [85, 15, 63, 9, "useEvent"], [85, 33, 63, 17], [85, 35, 63, 18, "handler"], [85, 42, 63, 25], [85, 44, 63, 27], [85, 45, 63, 28], [85, 74, 63, 57], [85, 76, 63, 59], [85, 99, 63, 82], [85, 100, 63, 83], [85, 102, 63, 85, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [86, 4, 64, 2], [87, 4, 65, 2], [87, 5, 65, 3], [88, 2, 66, 0], [89, 0, 66, 1], [89, 3]], "functionMap": {"names": ["<global>", "useAnimatedGestureHandler", "handler"], "mappings": "AAA;OCwB;kBCM;GD0B;CDS"}}, "type": "js/module"}]}