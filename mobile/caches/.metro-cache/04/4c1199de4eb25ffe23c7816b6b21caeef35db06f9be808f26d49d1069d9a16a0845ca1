{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 235}, "end": {"line": 11, "column": 31, "index": 266}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-dom", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 267}, "end": {"line": 12, "column": 33, "index": 300}}], "key": "Sde30rf5/ZbiV+hjpiIq3mPfpto=", "exportNames": ["*"]}}, {"name": "../../modules/canUseDom", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 301}, "end": {"line": 13, "column": 48, "index": 349}}], "key": "w0doQ61ImDsi56HxUhg3yNKNXVE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _reactDom = _interopRequireDefault(require(_dependencyMap[2], \"react-dom\"));\n  var _canUseDom = _interopRequireDefault(require(_dependencyMap[3], \"../../modules/canUseDom\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Copyright (c) Nicolas Gallagher.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  function ModalPortal(props) {\n    var children = props.children;\n    var elementRef = React.useRef(null);\n    if (_canUseDom.default && !elementRef.current) {\n      var element = document.createElement('div');\n      if (element && document.body) {\n        document.body.appendChild(element);\n        elementRef.current = element;\n      }\n    }\n    React.useEffect(() => {\n      if (_canUseDom.default) {\n        return () => {\n          if (document.body && elementRef.current) {\n            document.body.removeChild(elementRef.current);\n            elementRef.current = null;\n          }\n        };\n      }\n    }, []);\n    return elementRef.current && _canUseDom.default ? /*#__PURE__*/_reactDom.default.createPortal(children, elementRef.current) : null;\n  }\n  var _default = exports.default = ModalPortal;\n});", "lineCount": 44, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "React"], [7, 11, 11, 0], [7, 14, 11, 0, "_interopRequireWildcard"], [7, 37, 11, 0], [7, 38, 11, 0, "require"], [7, 45, 11, 0], [7, 46, 11, 0, "_dependencyMap"], [7, 60, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_reactDom"], [8, 15, 12, 0], [8, 18, 12, 0, "_interopRequireDefault"], [8, 40, 12, 0], [8, 41, 12, 0, "require"], [8, 48, 12, 0], [8, 49, 12, 0, "_dependencyMap"], [8, 63, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_canUseDom"], [9, 16, 13, 0], [9, 19, 13, 0, "_interopRequireDefault"], [9, 41, 13, 0], [9, 42, 13, 0, "require"], [9, 49, 13, 0], [9, 50, 13, 0, "_dependencyMap"], [9, 64, 13, 0], [10, 2, 13, 48], [10, 11, 13, 48, "_interopRequireWildcard"], [10, 35, 13, 48, "e"], [10, 36, 13, 48], [10, 38, 13, 48, "t"], [10, 39, 13, 48], [10, 68, 13, 48, "WeakMap"], [10, 75, 13, 48], [10, 81, 13, 48, "r"], [10, 82, 13, 48], [10, 89, 13, 48, "WeakMap"], [10, 96, 13, 48], [10, 100, 13, 48, "n"], [10, 101, 13, 48], [10, 108, 13, 48, "WeakMap"], [10, 115, 13, 48], [10, 127, 13, 48, "_interopRequireWildcard"], [10, 150, 13, 48], [10, 162, 13, 48, "_interopRequireWildcard"], [10, 163, 13, 48, "e"], [10, 164, 13, 48], [10, 166, 13, 48, "t"], [10, 167, 13, 48], [10, 176, 13, 48, "t"], [10, 177, 13, 48], [10, 181, 13, 48, "e"], [10, 182, 13, 48], [10, 186, 13, 48, "e"], [10, 187, 13, 48], [10, 188, 13, 48, "__esModule"], [10, 198, 13, 48], [10, 207, 13, 48, "e"], [10, 208, 13, 48], [10, 214, 13, 48, "o"], [10, 215, 13, 48], [10, 217, 13, 48, "i"], [10, 218, 13, 48], [10, 220, 13, 48, "f"], [10, 221, 13, 48], [10, 226, 13, 48, "__proto__"], [10, 235, 13, 48], [10, 243, 13, 48, "default"], [10, 250, 13, 48], [10, 252, 13, 48, "e"], [10, 253, 13, 48], [10, 270, 13, 48, "e"], [10, 271, 13, 48], [10, 294, 13, 48, "e"], [10, 295, 13, 48], [10, 320, 13, 48, "e"], [10, 321, 13, 48], [10, 330, 13, 48, "f"], [10, 331, 13, 48], [10, 337, 13, 48, "o"], [10, 338, 13, 48], [10, 341, 13, 48, "t"], [10, 342, 13, 48], [10, 345, 13, 48, "n"], [10, 346, 13, 48], [10, 349, 13, 48, "r"], [10, 350, 13, 48], [10, 358, 13, 48, "o"], [10, 359, 13, 48], [10, 360, 13, 48, "has"], [10, 363, 13, 48], [10, 364, 13, 48, "e"], [10, 365, 13, 48], [10, 375, 13, 48, "o"], [10, 376, 13, 48], [10, 377, 13, 48, "get"], [10, 380, 13, 48], [10, 381, 13, 48, "e"], [10, 382, 13, 48], [10, 385, 13, 48, "o"], [10, 386, 13, 48], [10, 387, 13, 48, "set"], [10, 390, 13, 48], [10, 391, 13, 48, "e"], [10, 392, 13, 48], [10, 394, 13, 48, "f"], [10, 395, 13, 48], [10, 411, 13, 48, "t"], [10, 412, 13, 48], [10, 416, 13, 48, "e"], [10, 417, 13, 48], [10, 433, 13, 48, "t"], [10, 434, 13, 48], [10, 441, 13, 48, "hasOwnProperty"], [10, 455, 13, 48], [10, 456, 13, 48, "call"], [10, 460, 13, 48], [10, 461, 13, 48, "e"], [10, 462, 13, 48], [10, 464, 13, 48, "t"], [10, 465, 13, 48], [10, 472, 13, 48, "i"], [10, 473, 13, 48], [10, 477, 13, 48, "o"], [10, 478, 13, 48], [10, 481, 13, 48, "Object"], [10, 487, 13, 48], [10, 488, 13, 48, "defineProperty"], [10, 502, 13, 48], [10, 507, 13, 48, "Object"], [10, 513, 13, 48], [10, 514, 13, 48, "getOwnPropertyDescriptor"], [10, 538, 13, 48], [10, 539, 13, 48, "e"], [10, 540, 13, 48], [10, 542, 13, 48, "t"], [10, 543, 13, 48], [10, 550, 13, 48, "i"], [10, 551, 13, 48], [10, 552, 13, 48, "get"], [10, 555, 13, 48], [10, 559, 13, 48, "i"], [10, 560, 13, 48], [10, 561, 13, 48, "set"], [10, 564, 13, 48], [10, 568, 13, 48, "o"], [10, 569, 13, 48], [10, 570, 13, 48, "f"], [10, 571, 13, 48], [10, 573, 13, 48, "t"], [10, 574, 13, 48], [10, 576, 13, 48, "i"], [10, 577, 13, 48], [10, 581, 13, 48, "f"], [10, 582, 13, 48], [10, 583, 13, 48, "t"], [10, 584, 13, 48], [10, 588, 13, 48, "e"], [10, 589, 13, 48], [10, 590, 13, 48, "t"], [10, 591, 13, 48], [10, 602, 13, 48, "f"], [10, 603, 13, 48], [10, 608, 13, 48, "e"], [10, 609, 13, 48], [10, 611, 13, 48, "t"], [10, 612, 13, 48], [11, 2, 1, 0], [12, 0, 2, 0], [13, 0, 3, 0], [14, 0, 4, 0], [15, 0, 5, 0], [16, 0, 6, 0], [17, 0, 7, 0], [18, 0, 8, 0], [19, 0, 9, 0], [21, 2, 14, 0], [21, 11, 14, 9, "ModalPortal"], [21, 22, 14, 20, "ModalPortal"], [21, 23, 14, 21, "props"], [21, 28, 14, 26], [21, 30, 14, 28], [22, 4, 15, 2], [22, 8, 15, 6, "children"], [22, 16, 15, 14], [22, 19, 15, 17, "props"], [22, 24, 15, 22], [22, 25, 15, 23, "children"], [22, 33, 15, 31], [23, 4, 16, 2], [23, 8, 16, 6, "elementRef"], [23, 18, 16, 16], [23, 21, 16, 19, "React"], [23, 26, 16, 24], [23, 27, 16, 25, "useRef"], [23, 33, 16, 31], [23, 34, 16, 32], [23, 38, 16, 36], [23, 39, 16, 37], [24, 4, 17, 2], [24, 8, 17, 6, "canUseDOM"], [24, 26, 17, 15], [24, 30, 17, 19], [24, 31, 17, 20, "elementRef"], [24, 41, 17, 30], [24, 42, 17, 31, "current"], [24, 49, 17, 38], [24, 51, 17, 40], [25, 6, 18, 4], [25, 10, 18, 8, "element"], [25, 17, 18, 15], [25, 20, 18, 18, "document"], [25, 28, 18, 26], [25, 29, 18, 27, "createElement"], [25, 42, 18, 40], [25, 43, 18, 41], [25, 48, 18, 46], [25, 49, 18, 47], [26, 6, 19, 4], [26, 10, 19, 8, "element"], [26, 17, 19, 15], [26, 21, 19, 19, "document"], [26, 29, 19, 27], [26, 30, 19, 28, "body"], [26, 34, 19, 32], [26, 36, 19, 34], [27, 8, 20, 6, "document"], [27, 16, 20, 14], [27, 17, 20, 15, "body"], [27, 21, 20, 19], [27, 22, 20, 20, "append<PERSON><PERSON><PERSON>"], [27, 33, 20, 31], [27, 34, 20, 32, "element"], [27, 41, 20, 39], [27, 42, 20, 40], [28, 8, 21, 6, "elementRef"], [28, 18, 21, 16], [28, 19, 21, 17, "current"], [28, 26, 21, 24], [28, 29, 21, 27, "element"], [28, 36, 21, 34], [29, 6, 22, 4], [30, 4, 23, 2], [31, 4, 24, 2, "React"], [31, 9, 24, 7], [31, 10, 24, 8, "useEffect"], [31, 19, 24, 17], [31, 20, 24, 18], [31, 26, 24, 24], [32, 6, 25, 4], [32, 10, 25, 8, "canUseDOM"], [32, 28, 25, 17], [32, 30, 25, 19], [33, 8, 26, 6], [33, 15, 26, 13], [33, 21, 26, 19], [34, 10, 27, 8], [34, 14, 27, 12, "document"], [34, 22, 27, 20], [34, 23, 27, 21, "body"], [34, 27, 27, 25], [34, 31, 27, 29, "elementRef"], [34, 41, 27, 39], [34, 42, 27, 40, "current"], [34, 49, 27, 47], [34, 51, 27, 49], [35, 12, 28, 10, "document"], [35, 20, 28, 18], [35, 21, 28, 19, "body"], [35, 25, 28, 23], [35, 26, 28, 24, "<PERSON><PERSON><PERSON><PERSON>"], [35, 37, 28, 35], [35, 38, 28, 36, "elementRef"], [35, 48, 28, 46], [35, 49, 28, 47, "current"], [35, 56, 28, 54], [35, 57, 28, 55], [36, 12, 29, 10, "elementRef"], [36, 22, 29, 20], [36, 23, 29, 21, "current"], [36, 30, 29, 28], [36, 33, 29, 31], [36, 37, 29, 35], [37, 10, 30, 8], [38, 8, 31, 6], [38, 9, 31, 7], [39, 6, 32, 4], [40, 4, 33, 2], [40, 5, 33, 3], [40, 7, 33, 5], [40, 9, 33, 7], [40, 10, 33, 8], [41, 4, 34, 2], [41, 11, 34, 9, "elementRef"], [41, 21, 34, 19], [41, 22, 34, 20, "current"], [41, 29, 34, 27], [41, 33, 34, 31, "canUseDOM"], [41, 51, 34, 40], [41, 54, 34, 43], [41, 67, 34, 56, "ReactDOM"], [41, 84, 34, 64], [41, 85, 34, 65, "createPortal"], [41, 97, 34, 77], [41, 98, 34, 78, "children"], [41, 106, 34, 86], [41, 108, 34, 88, "elementRef"], [41, 118, 34, 98], [41, 119, 34, 99, "current"], [41, 126, 34, 106], [41, 127, 34, 107], [41, 130, 34, 110], [41, 134, 34, 114], [42, 2, 35, 0], [43, 2, 35, 1], [43, 6, 35, 1, "_default"], [43, 14, 35, 1], [43, 17, 35, 1, "exports"], [43, 24, 35, 1], [43, 25, 35, 1, "default"], [43, 32, 35, 1], [43, 35, 36, 15, "ModalPortal"], [43, 46, 36, 26], [44, 0, 36, 26], [44, 3]], "functionMap": {"names": ["<global>", "ModalPortal", "React.useEffect$argument_0", "<anonymous>"], "mappings": "AAA;ACa;kBCU;aCE;ODK;GDE;CDE"}}, "type": "js/module"}]}