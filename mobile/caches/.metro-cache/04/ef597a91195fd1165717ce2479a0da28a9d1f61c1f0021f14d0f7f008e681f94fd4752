{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var androidScaleSuffix = {\n    '0.75': 'ldpi',\n    '1': 'mdpi',\n    '1.5': 'hdpi',\n    '2': 'xhdpi',\n    '3': 'xxhdpi',\n    '4': 'xxxhdpi'\n  };\n  var ANDROID_BASE_DENSITY = 160;\n  function getAndroidAssetSuffix(scale) {\n    if (scale.toString() in androidScaleSuffix) {\n      return androidScaleSuffix[scale.toString()];\n    }\n    if (Number.isFinite(scale) && scale > 0) {\n      return Math.round(scale * ANDROID_BASE_DENSITY) + 'dpi';\n    }\n    throw new Error('no such scale ' + scale.toString());\n  }\n  var drawableFileTypes = new Set(['gif', 'jpeg', 'jpg', 'ktx', 'png', 'webp', 'xml']);\n  function getAndroidResourceFolderName(asset, scale) {\n    if (!drawableFileTypes.has(asset.type)) {\n      return 'raw';\n    }\n    var suffix = getAndroidAssetSuffix(scale);\n    if (!suffix) {\n      throw new Error(\"Don't know which android drawable suffix to use for scale: \" + scale + '\\nAsset: ' + JSON.stringify(asset, null, '\\t') + '\\nPossible scales are:' + JSON.stringify(androidScaleSuffix, null, '\\t'));\n    }\n    return 'drawable-' + suffix;\n  }\n  function getAndroidResourceIdentifier(asset) {\n    return (getBasePath(asset) + '/' + asset.name).toLowerCase().replace(/\\//g, '_').replace(/([^a-z0-9_])/g, '').replace(/^(?:assets|assetsunstable_path)_/, '');\n  }\n  function getBasePath(asset) {\n    var basePath = asset.httpServerLocation;\n    return basePath.startsWith('/') ? basePath.slice(1) : basePath;\n  }\n  module.exports = {\n    getAndroidResourceFolderName,\n    getAndroidResourceIdentifier,\n    getBasePath\n  };\n});", "lineCount": 45, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 15, 0], [4, 6, 15, 6, "androidScaleSuffix"], [4, 24, 15, 24], [4, 27, 15, 27], [5, 4, 16, 2], [5, 10, 16, 8], [5, 12, 16, 10], [5, 18, 16, 16], [6, 4, 17, 2], [6, 7, 17, 5], [6, 9, 17, 7], [6, 15, 17, 13], [7, 4, 18, 2], [7, 9, 18, 7], [7, 11, 18, 9], [7, 17, 18, 15], [8, 4, 19, 2], [8, 7, 19, 5], [8, 9, 19, 7], [8, 16, 19, 14], [9, 4, 20, 2], [9, 7, 20, 5], [9, 9, 20, 7], [9, 17, 20, 15], [10, 4, 21, 2], [10, 7, 21, 5], [10, 9, 21, 7], [11, 2, 22, 0], [11, 3, 22, 1], [12, 2, 24, 0], [12, 6, 24, 6, "ANDROID_BASE_DENSITY"], [12, 26, 24, 26], [12, 29, 24, 29], [12, 32, 24, 32], [13, 2, 30, 0], [13, 11, 30, 9, "getAndroidAssetSuffix"], [13, 32, 30, 30, "getAndroidAssetSuffix"], [13, 33, 30, 31, "scale"], [13, 38, 30, 36], [13, 40, 30, 66], [14, 4, 31, 2], [14, 8, 31, 6, "scale"], [14, 13, 31, 11], [14, 14, 31, 12, "toString"], [14, 22, 31, 20], [14, 23, 31, 21], [14, 24, 31, 22], [14, 28, 31, 26, "androidScaleSuffix"], [14, 46, 31, 44], [14, 48, 31, 46], [15, 6, 33, 4], [15, 13, 33, 11, "androidScaleSuffix"], [15, 31, 33, 29], [15, 32, 33, 30, "scale"], [15, 37, 33, 35], [15, 38, 33, 36, "toString"], [15, 46, 33, 44], [15, 47, 33, 45], [15, 48, 33, 46], [15, 49, 33, 47], [16, 4, 34, 2], [17, 4, 37, 2], [17, 8, 37, 6, "Number"], [17, 14, 37, 12], [17, 15, 37, 13, "isFinite"], [17, 23, 37, 21], [17, 24, 37, 22, "scale"], [17, 29, 37, 27], [17, 30, 37, 28], [17, 34, 37, 32, "scale"], [17, 39, 37, 37], [17, 42, 37, 40], [17, 43, 37, 41], [17, 45, 37, 43], [18, 6, 38, 4], [18, 13, 38, 11, "Math"], [18, 17, 38, 15], [18, 18, 38, 16, "round"], [18, 23, 38, 21], [18, 24, 38, 22, "scale"], [18, 29, 38, 27], [18, 32, 38, 30, "ANDROID_BASE_DENSITY"], [18, 52, 38, 50], [18, 53, 38, 51], [18, 56, 38, 54], [18, 61, 38, 59], [19, 4, 39, 2], [20, 4, 40, 2], [20, 10, 40, 8], [20, 14, 40, 12, "Error"], [20, 19, 40, 17], [20, 20, 40, 18], [20, 36, 40, 34], [20, 39, 40, 37, "scale"], [20, 44, 40, 42], [20, 45, 40, 43, "toString"], [20, 53, 40, 51], [20, 54, 40, 52], [20, 55, 40, 53], [20, 56, 40, 54], [21, 2, 41, 0], [22, 2, 44, 0], [22, 6, 44, 6, "drawableFileTypes"], [22, 23, 44, 23], [22, 26, 44, 26], [22, 30, 44, 30, "Set"], [22, 33, 44, 33], [22, 34, 44, 34], [22, 35, 45, 2], [22, 40, 45, 7], [22, 42, 46, 2], [22, 48, 46, 8], [22, 50, 47, 2], [22, 55, 47, 7], [22, 57, 48, 2], [22, 62, 48, 7], [22, 64, 49, 2], [22, 69, 49, 7], [22, 71, 50, 2], [22, 77, 50, 8], [22, 79, 51, 2], [22, 84, 51, 7], [22, 85, 52, 1], [22, 86, 52, 2], [23, 2, 54, 0], [23, 11, 54, 9, "getAndroidResourceFolderName"], [23, 39, 54, 37, "getAndroidResourceFolderName"], [23, 40, 55, 2, "asset"], [23, 45, 55, 7], [23, 47, 56, 2, "scale"], [23, 52, 56, 7], [23, 54, 57, 16], [24, 4, 58, 2], [24, 8, 58, 6], [24, 9, 58, 7, "drawableFileTypes"], [24, 26, 58, 24], [24, 27, 58, 25, "has"], [24, 30, 58, 28], [24, 31, 58, 29, "asset"], [24, 36, 58, 34], [24, 37, 58, 35, "type"], [24, 41, 58, 39], [24, 42, 58, 40], [24, 44, 58, 42], [25, 6, 59, 4], [25, 13, 59, 11], [25, 18, 59, 16], [26, 4, 60, 2], [27, 4, 61, 2], [27, 8, 61, 8, "suffix"], [27, 14, 61, 14], [27, 17, 61, 17, "getAndroidAssetSuffix"], [27, 38, 61, 38], [27, 39, 61, 39, "scale"], [27, 44, 61, 44], [27, 45, 61, 45], [28, 4, 62, 2], [28, 8, 62, 6], [28, 9, 62, 7, "suffix"], [28, 15, 62, 13], [28, 17, 62, 15], [29, 6, 63, 4], [29, 12, 63, 10], [29, 16, 63, 14, "Error"], [29, 21, 63, 19], [29, 22, 64, 6], [29, 83, 64, 67], [29, 86, 65, 8, "scale"], [29, 91, 65, 13], [29, 94, 66, 8], [29, 105, 66, 19], [29, 108, 67, 8, "JSON"], [29, 112, 67, 12], [29, 113, 67, 13, "stringify"], [29, 122, 67, 22], [29, 123, 67, 23, "asset"], [29, 128, 67, 28], [29, 130, 67, 30], [29, 134, 67, 34], [29, 136, 67, 36], [29, 140, 67, 40], [29, 141, 67, 41], [29, 144, 68, 8], [29, 168, 68, 32], [29, 171, 69, 8, "JSON"], [29, 175, 69, 12], [29, 176, 69, 13, "stringify"], [29, 185, 69, 22], [29, 186, 69, 23, "androidScaleSuffix"], [29, 204, 69, 41], [29, 206, 69, 43], [29, 210, 69, 47], [29, 212, 69, 49], [29, 216, 69, 53], [29, 217, 70, 4], [29, 218, 70, 5], [30, 4, 71, 2], [31, 4, 72, 2], [31, 11, 72, 9], [31, 22, 72, 20], [31, 25, 72, 23, "suffix"], [31, 31, 72, 29], [32, 2, 73, 0], [33, 2, 75, 0], [33, 11, 75, 9, "getAndroidResourceIdentifier"], [33, 39, 75, 37, "getAndroidResourceIdentifier"], [33, 40, 76, 2, "asset"], [33, 45, 76, 7], [33, 47, 77, 16], [34, 4, 78, 2], [34, 11, 78, 9], [34, 12, 78, 10, "get<PERSON><PERSON><PERSON><PERSON>"], [34, 23, 78, 21], [34, 24, 78, 22, "asset"], [34, 29, 78, 27], [34, 30, 78, 28], [34, 33, 78, 31], [34, 36, 78, 34], [34, 39, 78, 37, "asset"], [34, 44, 78, 42], [34, 45, 78, 43, "name"], [34, 49, 78, 47], [34, 51, 79, 5, "toLowerCase"], [34, 62, 79, 16], [34, 63, 79, 17], [34, 64, 79, 18], [34, 65, 80, 5, "replace"], [34, 72, 80, 12], [34, 73, 80, 13], [34, 78, 80, 18], [34, 80, 80, 20], [34, 83, 80, 23], [34, 84, 80, 24], [34, 85, 81, 5, "replace"], [34, 92, 81, 12], [34, 93, 81, 13], [34, 108, 81, 28], [34, 110, 81, 30], [34, 112, 81, 32], [34, 113, 81, 33], [34, 114, 82, 5, "replace"], [34, 121, 82, 12], [34, 122, 82, 13], [34, 156, 82, 47], [34, 158, 82, 49], [34, 160, 82, 51], [34, 161, 82, 52], [35, 2, 83, 0], [36, 2, 85, 0], [36, 11, 85, 9, "get<PERSON><PERSON><PERSON><PERSON>"], [36, 22, 85, 20, "get<PERSON><PERSON><PERSON><PERSON>"], [36, 23, 85, 21, "asset"], [36, 28, 85, 26], [36, 30, 85, 63], [37, 4, 86, 2], [37, 8, 86, 8, "basePath"], [37, 16, 86, 16], [37, 19, 86, 19, "asset"], [37, 24, 86, 24], [37, 25, 86, 25, "httpServerLocation"], [37, 43, 86, 43], [38, 4, 87, 2], [38, 11, 87, 9, "basePath"], [38, 19, 87, 17], [38, 20, 87, 18, "startsWith"], [38, 30, 87, 28], [38, 31, 87, 29], [38, 34, 87, 32], [38, 35, 87, 33], [38, 38, 87, 36, "basePath"], [38, 46, 87, 44], [38, 47, 87, 45, "slice"], [38, 52, 87, 50], [38, 53, 87, 51], [38, 54, 87, 52], [38, 55, 87, 53], [38, 58, 87, 56, "basePath"], [38, 66, 87, 64], [39, 2, 88, 0], [40, 2, 90, 0, "module"], [40, 8, 90, 6], [40, 9, 90, 7, "exports"], [40, 16, 90, 14], [40, 19, 90, 17], [41, 4, 91, 2, "getAndroidResourceFolderName"], [41, 32, 91, 30], [42, 4, 92, 2, "getAndroidResourceIdentifier"], [42, 32, 92, 30], [43, 4, 93, 2, "get<PERSON><PERSON><PERSON><PERSON>"], [44, 2, 94, 0], [44, 3, 94, 1], [45, 0, 94, 2], [45, 3]], "functionMap": {"names": ["<global>", "getAndroidAssetSuffix", "getAndroidResourceFolderName", "getAndroidResourceIdentifier", "get<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;AC6B;CDW;AEa;CFmB;AGE;CHQ;AIE;CJG"}}, "type": "js/module"}]}