{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class CircularBuffer {\n    constructor(size) {\n      _defineProperty(this, \"capacity\", void 0);\n      _defineProperty(this, \"buffer\", void 0);\n      _defineProperty(this, \"index\", void 0);\n      _defineProperty(this, \"_size\", void 0);\n      this.capacity = size;\n      this.buffer = new Array(size);\n      this.index = 0;\n      this._size = 0;\n    }\n    push(element) {\n      this.buffer[this.index] = element;\n      this.index = (this.index + 1) % this.capacity;\n      this._size = Math.min(this.size + 1, this.capacity);\n    }\n    get(at) {\n      if (this._size === this.capacity) {\n        let index = (this.index + at) % this.capacity;\n        if (index < 0) {\n          index += this.capacity;\n        }\n        return this.buffer[index];\n      } else {\n        return this.buffer[at];\n      }\n    }\n    clear() {\n      this.buffer = new Array(this.capacity);\n      this.index = 0;\n      this._size = 0;\n    }\n    get size() {\n      return this._size;\n    }\n  }\n  exports.default = CircularBuffer;\n});", "lineCount": 56, "map": [[6, 2, 1, 0], [6, 11, 1, 9, "_defineProperty"], [6, 26, 1, 24, "_defineProperty"], [6, 27, 1, 25, "obj"], [6, 30, 1, 28], [6, 32, 1, 30, "key"], [6, 35, 1, 33], [6, 37, 1, 35, "value"], [6, 42, 1, 40], [6, 44, 1, 42], [7, 4, 1, 44], [7, 8, 1, 48, "key"], [7, 11, 1, 51], [7, 15, 1, 55, "obj"], [7, 18, 1, 58], [7, 20, 1, 60], [8, 6, 1, 62, "Object"], [8, 12, 1, 68], [8, 13, 1, 69, "defineProperty"], [8, 27, 1, 83], [8, 28, 1, 84, "obj"], [8, 31, 1, 87], [8, 33, 1, 89, "key"], [8, 36, 1, 92], [8, 38, 1, 94], [9, 8, 1, 96, "value"], [9, 13, 1, 101], [9, 15, 1, 103, "value"], [9, 20, 1, 108], [10, 8, 1, 110, "enumerable"], [10, 18, 1, 120], [10, 20, 1, 122], [10, 24, 1, 126], [11, 8, 1, 128, "configurable"], [11, 20, 1, 140], [11, 22, 1, 142], [11, 26, 1, 146], [12, 8, 1, 148, "writable"], [12, 16, 1, 156], [12, 18, 1, 158], [13, 6, 1, 163], [13, 7, 1, 164], [13, 8, 1, 165], [14, 4, 1, 167], [14, 5, 1, 168], [14, 11, 1, 174], [15, 6, 1, 176, "obj"], [15, 9, 1, 179], [15, 10, 1, 180, "key"], [15, 13, 1, 183], [15, 14, 1, 184], [15, 17, 1, 187, "value"], [15, 22, 1, 192], [16, 4, 1, 194], [17, 4, 1, 196], [17, 11, 1, 203, "obj"], [17, 14, 1, 206], [18, 2, 1, 208], [19, 2, 3, 15], [19, 8, 3, 21, "Circular<PERSON><PERSON>er"], [19, 22, 3, 35], [19, 23, 3, 36], [20, 4, 4, 2, "constructor"], [20, 15, 4, 13, "constructor"], [20, 16, 4, 14, "size"], [20, 20, 4, 18], [20, 22, 4, 20], [21, 6, 5, 4, "_defineProperty"], [21, 21, 5, 19], [21, 22, 5, 20], [21, 26, 5, 24], [21, 28, 5, 26], [21, 38, 5, 36], [21, 40, 5, 38], [21, 45, 5, 43], [21, 46, 5, 44], [21, 47, 5, 45], [22, 6, 7, 4, "_defineProperty"], [22, 21, 7, 19], [22, 22, 7, 20], [22, 26, 7, 24], [22, 28, 7, 26], [22, 36, 7, 34], [22, 38, 7, 36], [22, 43, 7, 41], [22, 44, 7, 42], [22, 45, 7, 43], [23, 6, 9, 4, "_defineProperty"], [23, 21, 9, 19], [23, 22, 9, 20], [23, 26, 9, 24], [23, 28, 9, 26], [23, 35, 9, 33], [23, 37, 9, 35], [23, 42, 9, 40], [23, 43, 9, 41], [23, 44, 9, 42], [24, 6, 11, 4, "_defineProperty"], [24, 21, 11, 19], [24, 22, 11, 20], [24, 26, 11, 24], [24, 28, 11, 26], [24, 35, 11, 33], [24, 37, 11, 35], [24, 42, 11, 40], [24, 43, 11, 41], [24, 44, 11, 42], [25, 6, 13, 4], [25, 10, 13, 8], [25, 11, 13, 9, "capacity"], [25, 19, 13, 17], [25, 22, 13, 20, "size"], [25, 26, 13, 24], [26, 6, 14, 4], [26, 10, 14, 8], [26, 11, 14, 9, "buffer"], [26, 17, 14, 15], [26, 20, 14, 18], [26, 24, 14, 22, "Array"], [26, 29, 14, 27], [26, 30, 14, 28, "size"], [26, 34, 14, 32], [26, 35, 14, 33], [27, 6, 15, 4], [27, 10, 15, 8], [27, 11, 15, 9, "index"], [27, 16, 15, 14], [27, 19, 15, 17], [27, 20, 15, 18], [28, 6, 16, 4], [28, 10, 16, 8], [28, 11, 16, 9, "_size"], [28, 16, 16, 14], [28, 19, 16, 17], [28, 20, 16, 18], [29, 4, 17, 2], [30, 4, 19, 2, "push"], [30, 8, 19, 6, "push"], [30, 9, 19, 7, "element"], [30, 16, 19, 14], [30, 18, 19, 16], [31, 6, 20, 4], [31, 10, 20, 8], [31, 11, 20, 9, "buffer"], [31, 17, 20, 15], [31, 18, 20, 16], [31, 22, 20, 20], [31, 23, 20, 21, "index"], [31, 28, 20, 26], [31, 29, 20, 27], [31, 32, 20, 30, "element"], [31, 39, 20, 37], [32, 6, 21, 4], [32, 10, 21, 8], [32, 11, 21, 9, "index"], [32, 16, 21, 14], [32, 19, 21, 17], [32, 20, 21, 18], [32, 24, 21, 22], [32, 25, 21, 23, "index"], [32, 30, 21, 28], [32, 33, 21, 31], [32, 34, 21, 32], [32, 38, 21, 36], [32, 42, 21, 40], [32, 43, 21, 41, "capacity"], [32, 51, 21, 49], [33, 6, 22, 4], [33, 10, 22, 8], [33, 11, 22, 9, "_size"], [33, 16, 22, 14], [33, 19, 22, 17, "Math"], [33, 23, 22, 21], [33, 24, 22, 22, "min"], [33, 27, 22, 25], [33, 28, 22, 26], [33, 32, 22, 30], [33, 33, 22, 31, "size"], [33, 37, 22, 35], [33, 40, 22, 38], [33, 41, 22, 39], [33, 43, 22, 41], [33, 47, 22, 45], [33, 48, 22, 46, "capacity"], [33, 56, 22, 54], [33, 57, 22, 55], [34, 4, 23, 2], [35, 4, 25, 2, "get"], [35, 7, 25, 5, "get"], [35, 8, 25, 6, "at"], [35, 10, 25, 8], [35, 12, 25, 10], [36, 6, 26, 4], [36, 10, 26, 8], [36, 14, 26, 12], [36, 15, 26, 13, "_size"], [36, 20, 26, 18], [36, 25, 26, 23], [36, 29, 26, 27], [36, 30, 26, 28, "capacity"], [36, 38, 26, 36], [36, 40, 26, 38], [37, 8, 27, 6], [37, 12, 27, 10, "index"], [37, 17, 27, 15], [37, 20, 27, 18], [37, 21, 27, 19], [37, 25, 27, 23], [37, 26, 27, 24, "index"], [37, 31, 27, 29], [37, 34, 27, 32, "at"], [37, 36, 27, 34], [37, 40, 27, 38], [37, 44, 27, 42], [37, 45, 27, 43, "capacity"], [37, 53, 27, 51], [38, 8, 29, 6], [38, 12, 29, 10, "index"], [38, 17, 29, 15], [38, 20, 29, 18], [38, 21, 29, 19], [38, 23, 29, 21], [39, 10, 30, 8, "index"], [39, 15, 30, 13], [39, 19, 30, 17], [39, 23, 30, 21], [39, 24, 30, 22, "capacity"], [39, 32, 30, 30], [40, 8, 31, 6], [41, 8, 33, 6], [41, 15, 33, 13], [41, 19, 33, 17], [41, 20, 33, 18, "buffer"], [41, 26, 33, 24], [41, 27, 33, 25, "index"], [41, 32, 33, 30], [41, 33, 33, 31], [42, 6, 34, 4], [42, 7, 34, 5], [42, 13, 34, 11], [43, 8, 35, 6], [43, 15, 35, 13], [43, 19, 35, 17], [43, 20, 35, 18, "buffer"], [43, 26, 35, 24], [43, 27, 35, 25, "at"], [43, 29, 35, 27], [43, 30, 35, 28], [44, 6, 36, 4], [45, 4, 37, 2], [46, 4, 39, 2, "clear"], [46, 9, 39, 7, "clear"], [46, 10, 39, 7], [46, 12, 39, 10], [47, 6, 40, 4], [47, 10, 40, 8], [47, 11, 40, 9, "buffer"], [47, 17, 40, 15], [47, 20, 40, 18], [47, 24, 40, 22, "Array"], [47, 29, 40, 27], [47, 30, 40, 28], [47, 34, 40, 32], [47, 35, 40, 33, "capacity"], [47, 43, 40, 41], [47, 44, 40, 42], [48, 6, 41, 4], [48, 10, 41, 8], [48, 11, 41, 9, "index"], [48, 16, 41, 14], [48, 19, 41, 17], [48, 20, 41, 18], [49, 6, 42, 4], [49, 10, 42, 8], [49, 11, 42, 9, "_size"], [49, 16, 42, 14], [49, 19, 42, 17], [49, 20, 42, 18], [50, 4, 43, 2], [51, 4, 45, 2], [51, 8, 45, 6, "size"], [51, 12, 45, 10, "size"], [51, 13, 45, 10], [51, 15, 45, 13], [52, 6, 46, 4], [52, 13, 46, 11], [52, 17, 46, 15], [52, 18, 46, 16, "_size"], [52, 23, 46, 21], [53, 4, 47, 2], [54, 2, 49, 0], [55, 2, 49, 1, "exports"], [55, 9, 49, 1], [55, 10, 49, 1, "default"], [55, 17, 49, 1], [55, 20, 49, 1, "Circular<PERSON><PERSON>er"], [55, 34, 49, 1], [56, 0, 49, 1], [56, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "Circular<PERSON><PERSON>er", "constructor", "push", "get", "clear", "get__size"], "mappings": "AAA,iNC;eCE;ECC;GDa;EEE;GFI;EGE;GHY;EIE;GJI;EKE;GLE;CDE"}}, "type": "js/module"}]}