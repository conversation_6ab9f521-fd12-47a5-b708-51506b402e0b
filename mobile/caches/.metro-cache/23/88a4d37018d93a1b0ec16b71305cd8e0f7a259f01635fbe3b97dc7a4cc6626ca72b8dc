{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = position;\n  function position(property, value) {\n    if (property === 'position' && value === 'sticky') {\n      return ['-webkit-sticky', 'sticky'];\n    }\n  }\n});", "lineCount": 13, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "default"], [7, 17, 6, 15], [7, 20, 6, 18, "position"], [7, 28, 6, 26], [8, 2, 7, 0], [8, 11, 7, 9, "position"], [8, 19, 7, 17, "position"], [8, 20, 7, 18, "property"], [8, 28, 7, 26], [8, 30, 7, 28, "value"], [8, 35, 7, 33], [8, 37, 7, 35], [9, 4, 8, 2], [9, 8, 8, 6, "property"], [9, 16, 8, 14], [9, 21, 8, 19], [9, 31, 8, 29], [9, 35, 8, 33, "value"], [9, 40, 8, 38], [9, 45, 8, 43], [9, 53, 8, 51], [9, 55, 8, 53], [10, 6, 9, 4], [10, 13, 9, 11], [10, 14, 9, 12], [10, 30, 9, 28], [10, 32, 9, 30], [10, 40, 9, 38], [10, 41, 9, 39], [11, 4, 10, 2], [12, 2, 11, 0], [13, 0, 11, 1], [13, 3]], "functionMap": {"names": ["<global>", "position"], "mappings": "AAA;ACM"}}, "type": "js/module"}]}