{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.warnOnce = warnOnce;\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var warnedKeys = {};\n\n  /**\n   * A simple function that prints a warning message once per session.\n   *\n   * @param {string} key - The key used to ensure the message is printed once.\n   *                       This should be unique to the callsite.\n   * @param {string} message - The message to print\n   */\n  function warnOnce(key, message) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (warnedKeys[key]) {\n        return;\n      }\n      console.warn(message);\n      warnedKeys[key] = true;\n    }\n  }\n});", "lineCount": 33, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [15, 2, 10, 0], [15, 6, 10, 4, "<PERSON><PERSON><PERSON><PERSON>"], [15, 16, 10, 14], [15, 19, 10, 17], [15, 20, 10, 18], [15, 21, 10, 19], [17, 2, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 2, 19, 7], [24, 11, 19, 16, "warnOnce"], [24, 19, 19, 24, "warnOnce"], [24, 20, 19, 25, "key"], [24, 23, 19, 28], [24, 25, 19, 30, "message"], [24, 32, 19, 37], [24, 34, 19, 39], [25, 4, 20, 2], [25, 8, 20, 6, "process"], [25, 15, 20, 13], [25, 16, 20, 14, "env"], [25, 19, 20, 17], [25, 20, 20, 18, "NODE_ENV"], [25, 28, 20, 26], [25, 33, 20, 31], [25, 45, 20, 43], [25, 47, 20, 45], [26, 6, 21, 4], [26, 10, 21, 8, "<PERSON><PERSON><PERSON><PERSON>"], [26, 20, 21, 18], [26, 21, 21, 19, "key"], [26, 24, 21, 22], [26, 25, 21, 23], [26, 27, 21, 25], [27, 8, 22, 6], [28, 6, 23, 4], [29, 6, 24, 4, "console"], [29, 13, 24, 11], [29, 14, 24, 12, "warn"], [29, 18, 24, 16], [29, 19, 24, 17, "message"], [29, 26, 24, 24], [29, 27, 24, 25], [30, 6, 25, 4, "<PERSON><PERSON><PERSON><PERSON>"], [30, 16, 25, 14], [30, 17, 25, 15, "key"], [30, 20, 25, 18], [30, 21, 25, 19], [30, 24, 25, 22], [30, 28, 25, 26], [31, 4, 26, 2], [32, 2, 27, 0], [33, 0, 27, 1], [33, 3]], "functionMap": {"names": ["<global>", "warnOnce"], "mappings": "AAA;OCkB"}}, "type": "js/module"}]}