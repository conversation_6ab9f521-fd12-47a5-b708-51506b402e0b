{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getComponentType = getComponentType;\n  var ForwardRefSymbol = Symbol.for(\"react.forward_ref\");\n  function getComponentType(component) {\n    switch (typeof component) {\n      case \"function\":\n      case \"object\":\n        return \"$$typeof\" in component && component.$$typeof === ForwardRefSymbol ? \"forwardRef\" : component.prototype?.isReactComponent ? \"class\" : typeof component;\n      default:\n        return \"unknown\";\n    }\n  }\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "getComponentType"], [7, 26, 3, 24], [7, 29, 3, 27, "getComponentType"], [7, 45, 3, 43], [8, 2, 4, 0], [8, 6, 4, 6, "ForwardRefSymbol"], [8, 22, 4, 22], [8, 25, 4, 25, "Symbol"], [8, 31, 4, 31], [8, 32, 4, 32, "for"], [8, 35, 4, 35], [8, 36, 4, 36], [8, 55, 4, 55], [8, 56, 4, 56], [9, 2, 5, 0], [9, 11, 5, 9, "getComponentType"], [9, 27, 5, 25, "getComponentType"], [9, 28, 5, 26, "component"], [9, 37, 5, 35], [9, 39, 5, 37], [10, 4, 6, 4], [10, 12, 6, 12], [10, 19, 6, 19, "component"], [10, 28, 6, 28], [11, 6, 7, 8], [11, 11, 7, 13], [11, 21, 7, 23], [12, 6, 8, 8], [12, 11, 8, 13], [12, 19, 8, 21], [13, 8, 9, 12], [13, 15, 9, 19], [13, 25, 9, 29], [13, 29, 9, 33, "component"], [13, 38, 9, 42], [13, 42, 9, 46, "component"], [13, 51, 9, 55], [13, 52, 9, 56, "$$typeof"], [13, 60, 9, 64], [13, 65, 9, 69, "ForwardRefSymbol"], [13, 81, 9, 85], [13, 84, 10, 18], [13, 96, 10, 30], [13, 99, 11, 18, "component"], [13, 108, 11, 27], [13, 109, 11, 28, "prototype"], [13, 118, 11, 37], [13, 120, 11, 39, "isReactComponent"], [13, 136, 11, 55], [13, 139, 12, 22], [13, 146, 12, 29], [13, 149, 13, 22], [13, 156, 13, 29, "component"], [13, 165, 13, 38], [14, 6, 14, 8], [15, 8, 15, 12], [15, 15, 15, 19], [15, 24, 15, 28], [16, 4, 16, 4], [17, 2, 17, 0], [18, 0, 17, 1], [18, 3]], "functionMap": {"names": ["<global>", "getComponentType"], "mappings": "AAA;ACI;CDY"}}, "type": "js/module"}]}