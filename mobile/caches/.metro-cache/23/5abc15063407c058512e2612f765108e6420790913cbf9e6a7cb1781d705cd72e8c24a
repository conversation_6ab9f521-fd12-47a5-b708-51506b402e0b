{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  var ExpoLinking = (0, _expoModulesCore.requireNativeModule)('ExpoLinking');\n  var _default = exports.default = ExpoLinking;\n});", "lineCount": 9, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoModulesCore"], [6, 22, 1, 0], [6, 25, 1, 0, "require"], [6, 32, 1, 0], [6, 33, 1, 0, "_dependencyMap"], [6, 47, 1, 0], [7, 2, 2, 0], [7, 6, 2, 6, "ExpoLinking"], [7, 17, 2, 17], [7, 20, 2, 20], [7, 24, 2, 20, "requireNativeModule"], [7, 60, 2, 39], [7, 62, 2, 40], [7, 75, 2, 53], [7, 76, 2, 54], [8, 2, 2, 55], [8, 6, 2, 55, "_default"], [8, 14, 2, 55], [8, 17, 2, 55, "exports"], [8, 24, 2, 55], [8, 25, 2, 55, "default"], [8, 32, 2, 55], [8, 35, 3, 15, "ExpoLinking"], [8, 46, 3, 26], [9, 0, 3, 26], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}