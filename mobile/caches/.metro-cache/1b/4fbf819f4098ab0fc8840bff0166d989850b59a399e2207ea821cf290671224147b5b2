{"dependencies": [{"name": "../getNextHandlerTag", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 57, "index": 268}}], "key": "xPiMMdBr7viMwno5RpgPsKrTYT4=", "exportNames": ["*"]}}, {"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 269}, "end": {"line": 4, "column": 55, "index": 324}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Gesture = exports.ContinousBaseGesture = exports.CALLBACK_TYPE = exports.BaseGesture = void 0;\n  var _getNextHandlerTag = require(_dependencyMap[0], \"../getNextHandlerTag\");\n  var _utils = require(_dependencyMap[1], \"../../utils\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const CALLBACK_TYPE = exports.CALLBACK_TYPE = {\n    UNDEFINED: 0,\n    BEGAN: 1,\n    START: 2,\n    UPDATE: 3,\n    CHANGE: 4,\n    END: 5,\n    FINALIZE: 6,\n    TOUCHES_DOWN: 7,\n    TOUCHES_MOVE: 8,\n    TOUCHES_UP: 9,\n    TOUCHES_CANCELLED: 10\n  }; // Allow using CALLBACK_TYPE as object and type\n  // eslint-disable-next-line @typescript-eslint/no-redeclare\n\n  class Gesture {}\n  exports.Gesture = Gesture;\n  let nextGestureId = 0;\n  class BaseGesture extends Gesture {\n    constructor() {\n      super(); // Used to check whether the gesture config has been updated when wrapping it\n      // with `useMemo`. Since every config will have a unique id, when the dependencies\n      // don't change, the config won't be recreated and the id will stay the same.\n      // If the id is different, it means that the config has changed and the gesture\n      // needs to be updated.\n\n      _defineProperty(this, \"gestureId\", -1);\n      _defineProperty(this, \"handlerTag\", -1);\n      _defineProperty(this, \"handlerName\", '');\n      _defineProperty(this, \"config\", {});\n      _defineProperty(this, \"handlers\", {\n        gestureId: -1,\n        handlerTag: -1,\n        isWorklet: []\n      });\n      this.gestureId = nextGestureId++;\n      this.handlers.gestureId = this.gestureId;\n    }\n    addDependency(key, gesture) {\n      const value = this.config[key];\n      this.config[key] = value ? Array().concat(value, gesture) : [gesture];\n    }\n    /**\n     * Sets a `ref` to the gesture object, allowing for interoperability with the old API.\n     * @param ref\n     */\n\n    withRef(ref) {\n      this.config.ref = ref;\n      return this;\n    } // eslint-disable-next-line @typescript-eslint/ban-types\n\n    isWorklet(callback) {\n      // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n      return callback.__workletHash !== undefined;\n    }\n    /**\n     * Set the callback that is being called when given gesture handler starts receiving touches.\n     * At the moment of this callback the handler is in `BEGAN` state and we don't know yet if it will recognize the gesture at all.\n     * @param callback\n     */\n\n    onBegin(callback) {\n      this.handlers.onBegin = callback;\n      this.handlers.isWorklet[CALLBACK_TYPE.BEGAN] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Set the callback that is being called when the gesture is recognized by the handler and it transitions to the `ACTIVE` state.\n     * @param callback\n     */\n\n    onStart(callback) {\n      this.handlers.onStart = callback;\n      this.handlers.isWorklet[CALLBACK_TYPE.START] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Set the callback that is being called when the gesture that was recognized by the handler finishes and handler reaches `END` state.\n     * It will be called only if the handler was previously in the `ACTIVE` state.\n     * @param callback\n     */\n\n    onEnd(callback) {\n      this.handlers.onEnd = callback; // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n\n      this.handlers.isWorklet[CALLBACK_TYPE.END] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Set the callback that is being called when the handler finalizes handling gesture - the gesture was recognized and has finished or it failed to recognize.\n     * @param callback\n     */\n\n    onFinalize(callback) {\n      this.handlers.onFinalize = callback; // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n\n      this.handlers.isWorklet[CALLBACK_TYPE.FINALIZE] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Set the `onTouchesDown` callback which is called every time a pointer is placed on the screen.\n     * @param callback\n     */\n\n    onTouchesDown(callback) {\n      this.config.needsPointerData = true;\n      this.handlers.onTouchesDown = callback;\n      this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_DOWN] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Set the `onTouchesMove` callback which is called every time a pointer is moved on the screen.\n     * @param callback\n     */\n\n    onTouchesMove(callback) {\n      this.config.needsPointerData = true;\n      this.handlers.onTouchesMove = callback;\n      this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_MOVE] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Set the `onTouchesUp` callback which is called every time a pointer is lifted from the screen.\n     * @param callback\n     */\n\n    onTouchesUp(callback) {\n      this.config.needsPointerData = true;\n      this.handlers.onTouchesUp = callback;\n      this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_UP] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Set the `onTouchesCancelled` callback which is called every time a pointer stops being tracked, for example when the gesture finishes.\n     * @param callback\n     */\n\n    onTouchesCancelled(callback) {\n      this.config.needsPointerData = true;\n      this.handlers.onTouchesCancelled = callback;\n      this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_CANCELLED] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Indicates whether the given handler should be analyzing stream of touch events or not.\n     * @param enabled\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#enabledvalue-boolean\n     */\n\n    enabled(enabled) {\n      this.config.enabled = enabled;\n      return this;\n    }\n    /**\n     * When true the handler will cancel or fail recognition (depending on its current state) whenever the finger leaves the area of the connected view.\n     * @param value\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#shouldcancelwhenoutsidevalue-boolean\n     */\n\n    shouldCancelWhenOutside(value) {\n      this.config.shouldCancelWhenOutside = value;\n      return this;\n    }\n    /**\n     * This parameter enables control over what part of the connected view area can be used to begin recognizing the gesture.\n     * When a negative number is provided the bounds of the view will reduce the area by the given number of points in each of the sides evenly.\n     * @param hitSlop\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#hitslopsettings\n     */\n\n    hitSlop(hitSlop) {\n      this.config.hitSlop = hitSlop;\n      return this;\n    }\n    /**\n     * #### Web only\n     * This parameter allows to specify which `cursor` should be used when gesture activates.\n     * Supports all CSS cursor values (e.g. `\"grab\"`, `\"zoom-in\"`). Default value is set to `\"auto\"`.\n     * @param activeCursor\n     */\n\n    activeCursor(activeCursor) {\n      this.config.activeCursor = activeCursor;\n      return this;\n    }\n    /**\n     * #### Web & Android only\n     * Allows users to choose which mouse button should handler respond to.\n     * Arguments can be combined using `|` operator, e.g. `mouseButton(MouseButton.LEFT | MouseButton.RIGHT)`.\n     * Default value is set to `MouseButton.LEFT`.\n     * @param mouseButton\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#mousebuttonvalue-mousebutton-web--android-only\n     */\n\n    mouseButton(mouseButton) {\n      this.config.mouseButton = mouseButton;\n      return this;\n    }\n    /**\n     * When `react-native-reanimated` is installed, the callbacks passed to the gestures are automatically workletized and run on the UI thread when called.\n     * This option allows for changing this behavior: when `true`, all the callbacks will be run on the JS thread instead of the UI thread, regardless of whether they are worklets or not.\n     * Defaults to `false`.\n     * @param runOnJS\n     */\n\n    runOnJS(runOnJS) {\n      this.config.runOnJS = runOnJS;\n      return this;\n    }\n    /**\n     * Allows gestures across different components to be recognized simultaneously.\n     * @param gestures\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#simultaneouswithexternalgesture\n     */\n\n    simultaneousWithExternalGesture(...gestures) {\n      for (const gesture of gestures) {\n        this.addDependency('simultaneousWith', gesture);\n      }\n      return this;\n    }\n    /**\n     * Allows to delay activation of the handler until all handlers passed as arguments to this method fail (or don't begin at all).\n     * @param gestures\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#requireexternalgesturetofail\n     */\n\n    requireExternalGestureToFail(...gestures) {\n      for (const gesture of gestures) {\n        this.addDependency('requireToFail', gesture);\n      }\n      return this;\n    }\n    /**\n     * Works similarily to `requireExternalGestureToFail` but the direction of the relation is reversed - instead of being one-to-many relation, it's many-to-one.\n     * @param gestures\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#blocksexternalgesture\n     */\n\n    blocksExternalGesture(...gestures) {\n      for (const gesture of gestures) {\n        this.addDependency('blocksHandlers', gesture);\n      }\n      return this;\n    }\n    /**\n     * Sets a `testID` property for gesture object, allowing for querying for it in tests.\n     * @param id\n     */\n\n    withTestId(id) {\n      this.config.testId = id;\n      return this;\n    }\n    /**\n     * #### iOS only\n     * When `true`, the handler will cancel touches for native UI components (`UIButton`, `UISwitch`, etc) it's attached to when it becomes `ACTIVE`.\n     * Default value is `true`.\n     * @param value\n     */\n\n    cancelsTouchesInView(value) {\n      this.config.cancelsTouchesInView = value;\n      return this;\n    }\n    initialize() {\n      this.handlerTag = (0, _getNextHandlerTag.getNextHandlerTag)();\n      this.handlers = {\n        ...this.handlers,\n        handlerTag: this.handlerTag\n      };\n      if (this.config.ref) {\n        this.config.ref.current = this;\n      }\n    }\n    toGestureArray() {\n      return [this];\n    } // eslint-disable-next-line @typescript-eslint/no-empty-function\n\n    prepare() {}\n    get shouldUseReanimated() {\n      // Use Reanimated when runOnJS isn't set explicitly,\n      // all defined callbacks are worklets\n      // and remote debugging is disabled\n      return this.config.runOnJS !== true && !this.handlers.isWorklet.includes(false) && !(0, _utils.isRemoteDebuggingEnabled)();\n    }\n  }\n  exports.BaseGesture = BaseGesture;\n  class ContinousBaseGesture extends BaseGesture {\n    /**\n     * Set the callback that is being called every time the gesture receives an update while it's active.\n     * @param callback\n     */\n    onUpdate(callback) {\n      this.handlers.onUpdate = callback;\n      this.handlers.isWorklet[CALLBACK_TYPE.UPDATE] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * Set the callback that is being called every time the gesture receives an update while it's active.\n     * This callback will receive information about change in value in relation to the last received event.\n     * @param callback\n     */\n\n    onChange(callback) {\n      this.handlers.onChange = callback;\n      this.handlers.isWorklet[CALLBACK_TYPE.CHANGE] = this.isWorklet(callback);\n      return this;\n    }\n    /**\n     * When `true` the handler will not activate by itself even if its activation criteria are met.\n     * Instead you can manipulate its state using state manager.\n     * @param manualActivation\n     */\n\n    manualActivation(manualActivation) {\n      this.config.manualActivation = manualActivation;\n      return this;\n    }\n  }\n  exports.ContinousBaseGesture = ContinousBaseGesture;\n});", "lineCount": 343, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_getNextHandlerTag"], [6, 24, 3, 0], [6, 27, 3, 0, "require"], [6, 34, 3, 0], [6, 35, 3, 0, "_dependencyMap"], [6, 49, 3, 0], [7, 2, 4, 0], [7, 6, 4, 0, "_utils"], [7, 12, 4, 0], [7, 15, 4, 0, "require"], [7, 22, 4, 0], [7, 23, 4, 0, "_dependencyMap"], [7, 37, 4, 0], [8, 2, 1, 0], [8, 11, 1, 9, "_defineProperty"], [8, 26, 1, 24, "_defineProperty"], [8, 27, 1, 25, "obj"], [8, 30, 1, 28], [8, 32, 1, 30, "key"], [8, 35, 1, 33], [8, 37, 1, 35, "value"], [8, 42, 1, 40], [8, 44, 1, 42], [9, 4, 1, 44], [9, 8, 1, 48, "key"], [9, 11, 1, 51], [9, 15, 1, 55, "obj"], [9, 18, 1, 58], [9, 20, 1, 60], [10, 6, 1, 62, "Object"], [10, 12, 1, 68], [10, 13, 1, 69, "defineProperty"], [10, 27, 1, 83], [10, 28, 1, 84, "obj"], [10, 31, 1, 87], [10, 33, 1, 89, "key"], [10, 36, 1, 92], [10, 38, 1, 94], [11, 8, 1, 96, "value"], [11, 13, 1, 101], [11, 15, 1, 103, "value"], [11, 20, 1, 108], [12, 8, 1, 110, "enumerable"], [12, 18, 1, 120], [12, 20, 1, 122], [12, 24, 1, 126], [13, 8, 1, 128, "configurable"], [13, 20, 1, 140], [13, 22, 1, 142], [13, 26, 1, 146], [14, 8, 1, 148, "writable"], [14, 16, 1, 156], [14, 18, 1, 158], [15, 6, 1, 163], [15, 7, 1, 164], [15, 8, 1, 165], [16, 4, 1, 167], [16, 5, 1, 168], [16, 11, 1, 174], [17, 6, 1, 176, "obj"], [17, 9, 1, 179], [17, 10, 1, 180, "key"], [17, 13, 1, 183], [17, 14, 1, 184], [17, 17, 1, 187, "value"], [17, 22, 1, 192], [18, 4, 1, 194], [19, 4, 1, 196], [19, 11, 1, 203, "obj"], [19, 14, 1, 206], [20, 2, 1, 208], [21, 2, 5, 7], [21, 8, 5, 13, "CALLBACK_TYPE"], [21, 21, 5, 26], [21, 24, 5, 26, "exports"], [21, 31, 5, 26], [21, 32, 5, 26, "CALLBACK_TYPE"], [21, 45, 5, 26], [21, 48, 5, 29], [22, 4, 6, 2, "UNDEFINED"], [22, 13, 6, 11], [22, 15, 6, 13], [22, 16, 6, 14], [23, 4, 7, 2, "BEGAN"], [23, 9, 7, 7], [23, 11, 7, 9], [23, 12, 7, 10], [24, 4, 8, 2, "START"], [24, 9, 8, 7], [24, 11, 8, 9], [24, 12, 8, 10], [25, 4, 9, 2, "UPDATE"], [25, 10, 9, 8], [25, 12, 9, 10], [25, 13, 9, 11], [26, 4, 10, 2, "CHANGE"], [26, 10, 10, 8], [26, 12, 10, 10], [26, 13, 10, 11], [27, 4, 11, 2, "END"], [27, 7, 11, 5], [27, 9, 11, 7], [27, 10, 11, 8], [28, 4, 12, 2, "FINALIZE"], [28, 12, 12, 10], [28, 14, 12, 12], [28, 15, 12, 13], [29, 4, 13, 2, "TOUCHES_DOWN"], [29, 16, 13, 14], [29, 18, 13, 16], [29, 19, 13, 17], [30, 4, 14, 2, "TOUCHES_MOVE"], [30, 16, 14, 14], [30, 18, 14, 16], [30, 19, 14, 17], [31, 4, 15, 2, "TOUCHES_UP"], [31, 14, 15, 12], [31, 16, 15, 14], [31, 17, 15, 15], [32, 4, 16, 2, "TOUCHES_CANCELLED"], [32, 21, 16, 19], [32, 23, 16, 21], [33, 2, 17, 0], [33, 3, 17, 1], [33, 4, 17, 2], [33, 5, 17, 3], [34, 2, 18, 0], [36, 2, 20, 7], [36, 8, 20, 13, "Gesture"], [36, 15, 20, 20], [36, 16, 20, 21], [37, 2, 20, 23, "exports"], [37, 9, 20, 23], [37, 10, 20, 23, "Gesture"], [37, 17, 20, 23], [37, 20, 20, 23, "Gesture"], [37, 27, 20, 23], [38, 2, 21, 0], [38, 6, 21, 4, "nextGestureId"], [38, 19, 21, 17], [38, 22, 21, 20], [38, 23, 21, 21], [39, 2, 22, 7], [39, 8, 22, 13, "BaseGesture"], [39, 19, 22, 24], [39, 28, 22, 33, "Gesture"], [39, 35, 22, 40], [39, 36, 22, 41], [40, 4, 23, 2, "constructor"], [40, 15, 23, 13, "constructor"], [40, 16, 23, 13], [40, 18, 23, 16], [41, 6, 24, 4], [41, 11, 24, 9], [41, 12, 24, 10], [41, 13, 24, 11], [41, 14, 24, 12], [41, 15, 24, 13], [42, 6, 25, 4], [43, 6, 26, 4], [44, 6, 27, 4], [45, 6, 28, 4], [47, 6, 30, 4, "_defineProperty"], [47, 21, 30, 19], [47, 22, 30, 20], [47, 26, 30, 24], [47, 28, 30, 26], [47, 39, 30, 37], [47, 41, 30, 39], [47, 42, 30, 40], [47, 43, 30, 41], [47, 44, 30, 42], [48, 6, 32, 4, "_defineProperty"], [48, 21, 32, 19], [48, 22, 32, 20], [48, 26, 32, 24], [48, 28, 32, 26], [48, 40, 32, 38], [48, 42, 32, 40], [48, 43, 32, 41], [48, 44, 32, 42], [48, 45, 32, 43], [49, 6, 34, 4, "_defineProperty"], [49, 21, 34, 19], [49, 22, 34, 20], [49, 26, 34, 24], [49, 28, 34, 26], [49, 41, 34, 39], [49, 43, 34, 41], [49, 45, 34, 43], [49, 46, 34, 44], [50, 6, 36, 4, "_defineProperty"], [50, 21, 36, 19], [50, 22, 36, 20], [50, 26, 36, 24], [50, 28, 36, 26], [50, 36, 36, 34], [50, 38, 36, 36], [50, 39, 36, 37], [50, 40, 36, 38], [50, 41, 36, 39], [51, 6, 38, 4, "_defineProperty"], [51, 21, 38, 19], [51, 22, 38, 20], [51, 26, 38, 24], [51, 28, 38, 26], [51, 38, 38, 36], [51, 40, 38, 38], [52, 8, 39, 6, "gestureId"], [52, 17, 39, 15], [52, 19, 39, 17], [52, 20, 39, 18], [52, 21, 39, 19], [53, 8, 40, 6, "handlerTag"], [53, 18, 40, 16], [53, 20, 40, 18], [53, 21, 40, 19], [53, 22, 40, 20], [54, 8, 41, 6, "isWorklet"], [54, 17, 41, 15], [54, 19, 41, 17], [55, 6, 42, 4], [55, 7, 42, 5], [55, 8, 42, 6], [56, 6, 44, 4], [56, 10, 44, 8], [56, 11, 44, 9, "gestureId"], [56, 20, 44, 18], [56, 23, 44, 21, "nextGestureId"], [56, 36, 44, 34], [56, 38, 44, 36], [57, 6, 45, 4], [57, 10, 45, 8], [57, 11, 45, 9, "handlers"], [57, 19, 45, 17], [57, 20, 45, 18, "gestureId"], [57, 29, 45, 27], [57, 32, 45, 30], [57, 36, 45, 34], [57, 37, 45, 35, "gestureId"], [57, 46, 45, 44], [58, 4, 46, 2], [59, 4, 48, 2, "addDependency"], [59, 17, 48, 15, "addDependency"], [59, 18, 48, 16, "key"], [59, 21, 48, 19], [59, 23, 48, 21, "gesture"], [59, 30, 48, 28], [59, 32, 48, 30], [60, 6, 49, 4], [60, 12, 49, 10, "value"], [60, 17, 49, 15], [60, 20, 49, 18], [60, 24, 49, 22], [60, 25, 49, 23, "config"], [60, 31, 49, 29], [60, 32, 49, 30, "key"], [60, 35, 49, 33], [60, 36, 49, 34], [61, 6, 50, 4], [61, 10, 50, 8], [61, 11, 50, 9, "config"], [61, 17, 50, 15], [61, 18, 50, 16, "key"], [61, 21, 50, 19], [61, 22, 50, 20], [61, 25, 50, 23, "value"], [61, 30, 50, 28], [61, 33, 50, 31, "Array"], [61, 38, 50, 36], [61, 39, 50, 37], [61, 40, 50, 38], [61, 41, 50, 39, "concat"], [61, 47, 50, 45], [61, 48, 50, 46, "value"], [61, 53, 50, 51], [61, 55, 50, 53, "gesture"], [61, 62, 50, 60], [61, 63, 50, 61], [61, 66, 50, 64], [61, 67, 50, 65, "gesture"], [61, 74, 50, 72], [61, 75, 50, 73], [62, 4, 51, 2], [63, 4, 52, 2], [64, 0, 53, 0], [65, 0, 54, 0], [66, 0, 55, 0], [68, 4, 58, 2, "with<PERSON>ef"], [68, 11, 58, 9, "with<PERSON>ef"], [68, 12, 58, 10, "ref"], [68, 15, 58, 13], [68, 17, 58, 15], [69, 6, 59, 4], [69, 10, 59, 8], [69, 11, 59, 9, "config"], [69, 17, 59, 15], [69, 18, 59, 16, "ref"], [69, 21, 59, 19], [69, 24, 59, 22, "ref"], [69, 27, 59, 25], [70, 6, 60, 4], [70, 13, 60, 11], [70, 17, 60, 15], [71, 4, 61, 2], [71, 5, 61, 3], [71, 6, 61, 4], [73, 4, 64, 2, "isWorklet"], [73, 13, 64, 11, "isWorklet"], [73, 14, 64, 12, "callback"], [73, 22, 64, 20], [73, 24, 64, 22], [74, 6, 65, 4], [75, 6, 66, 4], [75, 13, 66, 11, "callback"], [75, 21, 66, 19], [75, 22, 66, 20, "__workletHash"], [75, 35, 66, 33], [75, 40, 66, 38, "undefined"], [75, 49, 66, 47], [76, 4, 67, 2], [77, 4, 68, 2], [78, 0, 69, 0], [79, 0, 70, 0], [80, 0, 71, 0], [81, 0, 72, 0], [83, 4, 75, 2, "onBegin"], [83, 11, 75, 9, "onBegin"], [83, 12, 75, 10, "callback"], [83, 20, 75, 18], [83, 22, 75, 20], [84, 6, 76, 4], [84, 10, 76, 8], [84, 11, 76, 9, "handlers"], [84, 19, 76, 17], [84, 20, 76, 18, "onBegin"], [84, 27, 76, 25], [84, 30, 76, 28, "callback"], [84, 38, 76, 36], [85, 6, 77, 4], [85, 10, 77, 8], [85, 11, 77, 9, "handlers"], [85, 19, 77, 17], [85, 20, 77, 18, "isWorklet"], [85, 29, 77, 27], [85, 30, 77, 28, "CALLBACK_TYPE"], [85, 43, 77, 41], [85, 44, 77, 42, "BEGAN"], [85, 49, 77, 47], [85, 50, 77, 48], [85, 53, 77, 51], [85, 57, 77, 55], [85, 58, 77, 56, "isWorklet"], [85, 67, 77, 65], [85, 68, 77, 66, "callback"], [85, 76, 77, 74], [85, 77, 77, 75], [86, 6, 78, 4], [86, 13, 78, 11], [86, 17, 78, 15], [87, 4, 79, 2], [88, 4, 80, 2], [89, 0, 81, 0], [90, 0, 82, 0], [91, 0, 83, 0], [93, 4, 86, 2, "onStart"], [93, 11, 86, 9, "onStart"], [93, 12, 86, 10, "callback"], [93, 20, 86, 18], [93, 22, 86, 20], [94, 6, 87, 4], [94, 10, 87, 8], [94, 11, 87, 9, "handlers"], [94, 19, 87, 17], [94, 20, 87, 18, "onStart"], [94, 27, 87, 25], [94, 30, 87, 28, "callback"], [94, 38, 87, 36], [95, 6, 88, 4], [95, 10, 88, 8], [95, 11, 88, 9, "handlers"], [95, 19, 88, 17], [95, 20, 88, 18, "isWorklet"], [95, 29, 88, 27], [95, 30, 88, 28, "CALLBACK_TYPE"], [95, 43, 88, 41], [95, 44, 88, 42, "START"], [95, 49, 88, 47], [95, 50, 88, 48], [95, 53, 88, 51], [95, 57, 88, 55], [95, 58, 88, 56, "isWorklet"], [95, 67, 88, 65], [95, 68, 88, 66, "callback"], [95, 76, 88, 74], [95, 77, 88, 75], [96, 6, 89, 4], [96, 13, 89, 11], [96, 17, 89, 15], [97, 4, 90, 2], [98, 4, 91, 2], [99, 0, 92, 0], [100, 0, 93, 0], [101, 0, 94, 0], [102, 0, 95, 0], [104, 4, 98, 2, "onEnd"], [104, 9, 98, 7, "onEnd"], [104, 10, 98, 8, "callback"], [104, 18, 98, 16], [104, 20, 98, 18], [105, 6, 99, 4], [105, 10, 99, 8], [105, 11, 99, 9, "handlers"], [105, 19, 99, 17], [105, 20, 99, 18, "onEnd"], [105, 25, 99, 23], [105, 28, 99, 26, "callback"], [105, 36, 99, 34], [105, 37, 99, 35], [105, 38, 99, 36], [107, 6, 101, 4], [107, 10, 101, 8], [107, 11, 101, 9, "handlers"], [107, 19, 101, 17], [107, 20, 101, 18, "isWorklet"], [107, 29, 101, 27], [107, 30, 101, 28, "CALLBACK_TYPE"], [107, 43, 101, 41], [107, 44, 101, 42, "END"], [107, 47, 101, 45], [107, 48, 101, 46], [107, 51, 101, 49], [107, 55, 101, 53], [107, 56, 101, 54, "isWorklet"], [107, 65, 101, 63], [107, 66, 101, 64, "callback"], [107, 74, 101, 72], [107, 75, 101, 73], [108, 6, 102, 4], [108, 13, 102, 11], [108, 17, 102, 15], [109, 4, 103, 2], [110, 4, 104, 2], [111, 0, 105, 0], [112, 0, 106, 0], [113, 0, 107, 0], [115, 4, 110, 2, "onFinalize"], [115, 14, 110, 12, "onFinalize"], [115, 15, 110, 13, "callback"], [115, 23, 110, 21], [115, 25, 110, 23], [116, 6, 111, 4], [116, 10, 111, 8], [116, 11, 111, 9, "handlers"], [116, 19, 111, 17], [116, 20, 111, 18, "onFinalize"], [116, 30, 111, 28], [116, 33, 111, 31, "callback"], [116, 41, 111, 39], [116, 42, 111, 40], [116, 43, 111, 41], [118, 6, 113, 4], [118, 10, 113, 8], [118, 11, 113, 9, "handlers"], [118, 19, 113, 17], [118, 20, 113, 18, "isWorklet"], [118, 29, 113, 27], [118, 30, 113, 28, "CALLBACK_TYPE"], [118, 43, 113, 41], [118, 44, 113, 42, "FINALIZE"], [118, 52, 113, 50], [118, 53, 113, 51], [118, 56, 113, 54], [118, 60, 113, 58], [118, 61, 113, 59, "isWorklet"], [118, 70, 113, 68], [118, 71, 113, 69, "callback"], [118, 79, 113, 77], [118, 80, 113, 78], [119, 6, 114, 4], [119, 13, 114, 11], [119, 17, 114, 15], [120, 4, 115, 2], [121, 4, 116, 2], [122, 0, 117, 0], [123, 0, 118, 0], [124, 0, 119, 0], [126, 4, 122, 2, "onTouchesDown"], [126, 17, 122, 15, "onTouchesDown"], [126, 18, 122, 16, "callback"], [126, 26, 122, 24], [126, 28, 122, 26], [127, 6, 123, 4], [127, 10, 123, 8], [127, 11, 123, 9, "config"], [127, 17, 123, 15], [127, 18, 123, 16, "needsPointerData"], [127, 34, 123, 32], [127, 37, 123, 35], [127, 41, 123, 39], [128, 6, 124, 4], [128, 10, 124, 8], [128, 11, 124, 9, "handlers"], [128, 19, 124, 17], [128, 20, 124, 18, "onTouchesDown"], [128, 33, 124, 31], [128, 36, 124, 34, "callback"], [128, 44, 124, 42], [129, 6, 125, 4], [129, 10, 125, 8], [129, 11, 125, 9, "handlers"], [129, 19, 125, 17], [129, 20, 125, 18, "isWorklet"], [129, 29, 125, 27], [129, 30, 125, 28, "CALLBACK_TYPE"], [129, 43, 125, 41], [129, 44, 125, 42, "TOUCHES_DOWN"], [129, 56, 125, 54], [129, 57, 125, 55], [129, 60, 125, 58], [129, 64, 125, 62], [129, 65, 125, 63, "isWorklet"], [129, 74, 125, 72], [129, 75, 125, 73, "callback"], [129, 83, 125, 81], [129, 84, 125, 82], [130, 6, 126, 4], [130, 13, 126, 11], [130, 17, 126, 15], [131, 4, 127, 2], [132, 4, 128, 2], [133, 0, 129, 0], [134, 0, 130, 0], [135, 0, 131, 0], [137, 4, 134, 2, "onTouchesMove"], [137, 17, 134, 15, "onTouchesMove"], [137, 18, 134, 16, "callback"], [137, 26, 134, 24], [137, 28, 134, 26], [138, 6, 135, 4], [138, 10, 135, 8], [138, 11, 135, 9, "config"], [138, 17, 135, 15], [138, 18, 135, 16, "needsPointerData"], [138, 34, 135, 32], [138, 37, 135, 35], [138, 41, 135, 39], [139, 6, 136, 4], [139, 10, 136, 8], [139, 11, 136, 9, "handlers"], [139, 19, 136, 17], [139, 20, 136, 18, "onTouchesMove"], [139, 33, 136, 31], [139, 36, 136, 34, "callback"], [139, 44, 136, 42], [140, 6, 137, 4], [140, 10, 137, 8], [140, 11, 137, 9, "handlers"], [140, 19, 137, 17], [140, 20, 137, 18, "isWorklet"], [140, 29, 137, 27], [140, 30, 137, 28, "CALLBACK_TYPE"], [140, 43, 137, 41], [140, 44, 137, 42, "TOUCHES_MOVE"], [140, 56, 137, 54], [140, 57, 137, 55], [140, 60, 137, 58], [140, 64, 137, 62], [140, 65, 137, 63, "isWorklet"], [140, 74, 137, 72], [140, 75, 137, 73, "callback"], [140, 83, 137, 81], [140, 84, 137, 82], [141, 6, 138, 4], [141, 13, 138, 11], [141, 17, 138, 15], [142, 4, 139, 2], [143, 4, 140, 2], [144, 0, 141, 0], [145, 0, 142, 0], [146, 0, 143, 0], [148, 4, 146, 2, "onTouchesUp"], [148, 15, 146, 13, "onTouchesUp"], [148, 16, 146, 14, "callback"], [148, 24, 146, 22], [148, 26, 146, 24], [149, 6, 147, 4], [149, 10, 147, 8], [149, 11, 147, 9, "config"], [149, 17, 147, 15], [149, 18, 147, 16, "needsPointerData"], [149, 34, 147, 32], [149, 37, 147, 35], [149, 41, 147, 39], [150, 6, 148, 4], [150, 10, 148, 8], [150, 11, 148, 9, "handlers"], [150, 19, 148, 17], [150, 20, 148, 18, "onTouchesUp"], [150, 31, 148, 29], [150, 34, 148, 32, "callback"], [150, 42, 148, 40], [151, 6, 149, 4], [151, 10, 149, 8], [151, 11, 149, 9, "handlers"], [151, 19, 149, 17], [151, 20, 149, 18, "isWorklet"], [151, 29, 149, 27], [151, 30, 149, 28, "CALLBACK_TYPE"], [151, 43, 149, 41], [151, 44, 149, 42, "TOUCHES_UP"], [151, 54, 149, 52], [151, 55, 149, 53], [151, 58, 149, 56], [151, 62, 149, 60], [151, 63, 149, 61, "isWorklet"], [151, 72, 149, 70], [151, 73, 149, 71, "callback"], [151, 81, 149, 79], [151, 82, 149, 80], [152, 6, 150, 4], [152, 13, 150, 11], [152, 17, 150, 15], [153, 4, 151, 2], [154, 4, 152, 2], [155, 0, 153, 0], [156, 0, 154, 0], [157, 0, 155, 0], [159, 4, 158, 2, "onTouchesCancelled"], [159, 22, 158, 20, "onTouchesCancelled"], [159, 23, 158, 21, "callback"], [159, 31, 158, 29], [159, 33, 158, 31], [160, 6, 159, 4], [160, 10, 159, 8], [160, 11, 159, 9, "config"], [160, 17, 159, 15], [160, 18, 159, 16, "needsPointerData"], [160, 34, 159, 32], [160, 37, 159, 35], [160, 41, 159, 39], [161, 6, 160, 4], [161, 10, 160, 8], [161, 11, 160, 9, "handlers"], [161, 19, 160, 17], [161, 20, 160, 18, "onTouchesCancelled"], [161, 38, 160, 36], [161, 41, 160, 39, "callback"], [161, 49, 160, 47], [162, 6, 161, 4], [162, 10, 161, 8], [162, 11, 161, 9, "handlers"], [162, 19, 161, 17], [162, 20, 161, 18, "isWorklet"], [162, 29, 161, 27], [162, 30, 161, 28, "CALLBACK_TYPE"], [162, 43, 161, 41], [162, 44, 161, 42, "TOUCHES_CANCELLED"], [162, 61, 161, 59], [162, 62, 161, 60], [162, 65, 161, 63], [162, 69, 161, 67], [162, 70, 161, 68, "isWorklet"], [162, 79, 161, 77], [162, 80, 161, 78, "callback"], [162, 88, 161, 86], [162, 89, 161, 87], [163, 6, 162, 4], [163, 13, 162, 11], [163, 17, 162, 15], [164, 4, 163, 2], [165, 4, 164, 2], [166, 0, 165, 0], [167, 0, 166, 0], [168, 0, 167, 0], [169, 0, 168, 0], [171, 4, 171, 2, "enabled"], [171, 11, 171, 9, "enabled"], [171, 12, 171, 10, "enabled"], [171, 19, 171, 17], [171, 21, 171, 19], [172, 6, 172, 4], [172, 10, 172, 8], [172, 11, 172, 9, "config"], [172, 17, 172, 15], [172, 18, 172, 16, "enabled"], [172, 25, 172, 23], [172, 28, 172, 26, "enabled"], [172, 35, 172, 33], [173, 6, 173, 4], [173, 13, 173, 11], [173, 17, 173, 15], [174, 4, 174, 2], [175, 4, 175, 2], [176, 0, 176, 0], [177, 0, 177, 0], [178, 0, 178, 0], [179, 0, 179, 0], [181, 4, 182, 2, "shouldCancelWhenOutside"], [181, 27, 182, 25, "shouldCancelWhenOutside"], [181, 28, 182, 26, "value"], [181, 33, 182, 31], [181, 35, 182, 33], [182, 6, 183, 4], [182, 10, 183, 8], [182, 11, 183, 9, "config"], [182, 17, 183, 15], [182, 18, 183, 16, "shouldCancelWhenOutside"], [182, 41, 183, 39], [182, 44, 183, 42, "value"], [182, 49, 183, 47], [183, 6, 184, 4], [183, 13, 184, 11], [183, 17, 184, 15], [184, 4, 185, 2], [185, 4, 186, 2], [186, 0, 187, 0], [187, 0, 188, 0], [188, 0, 189, 0], [189, 0, 190, 0], [190, 0, 191, 0], [192, 4, 194, 2, "hitSlop"], [192, 11, 194, 9, "hitSlop"], [192, 12, 194, 10, "hitSlop"], [192, 19, 194, 17], [192, 21, 194, 19], [193, 6, 195, 4], [193, 10, 195, 8], [193, 11, 195, 9, "config"], [193, 17, 195, 15], [193, 18, 195, 16, "hitSlop"], [193, 25, 195, 23], [193, 28, 195, 26, "hitSlop"], [193, 35, 195, 33], [194, 6, 196, 4], [194, 13, 196, 11], [194, 17, 196, 15], [195, 4, 197, 2], [196, 4, 198, 2], [197, 0, 199, 0], [198, 0, 200, 0], [199, 0, 201, 0], [200, 0, 202, 0], [201, 0, 203, 0], [203, 4, 206, 2, "activeCursor"], [203, 16, 206, 14, "activeCursor"], [203, 17, 206, 15, "activeCursor"], [203, 29, 206, 27], [203, 31, 206, 29], [204, 6, 207, 4], [204, 10, 207, 8], [204, 11, 207, 9, "config"], [204, 17, 207, 15], [204, 18, 207, 16, "activeCursor"], [204, 30, 207, 28], [204, 33, 207, 31, "activeCursor"], [204, 45, 207, 43], [205, 6, 208, 4], [205, 13, 208, 11], [205, 17, 208, 15], [206, 4, 209, 2], [207, 4, 210, 2], [208, 0, 211, 0], [209, 0, 212, 0], [210, 0, 213, 0], [211, 0, 214, 0], [212, 0, 215, 0], [213, 0, 216, 0], [214, 0, 217, 0], [216, 4, 220, 2, "mouseButton"], [216, 15, 220, 13, "mouseButton"], [216, 16, 220, 14, "mouseButton"], [216, 27, 220, 25], [216, 29, 220, 27], [217, 6, 221, 4], [217, 10, 221, 8], [217, 11, 221, 9, "config"], [217, 17, 221, 15], [217, 18, 221, 16, "mouseButton"], [217, 29, 221, 27], [217, 32, 221, 30, "mouseButton"], [217, 43, 221, 41], [218, 6, 222, 4], [218, 13, 222, 11], [218, 17, 222, 15], [219, 4, 223, 2], [220, 4, 224, 2], [221, 0, 225, 0], [222, 0, 226, 0], [223, 0, 227, 0], [224, 0, 228, 0], [225, 0, 229, 0], [227, 4, 232, 2, "runOnJS"], [227, 11, 232, 9, "runOnJS"], [227, 12, 232, 10, "runOnJS"], [227, 19, 232, 17], [227, 21, 232, 19], [228, 6, 233, 4], [228, 10, 233, 8], [228, 11, 233, 9, "config"], [228, 17, 233, 15], [228, 18, 233, 16, "runOnJS"], [228, 25, 233, 23], [228, 28, 233, 26, "runOnJS"], [228, 35, 233, 33], [229, 6, 234, 4], [229, 13, 234, 11], [229, 17, 234, 15], [230, 4, 235, 2], [231, 4, 236, 2], [232, 0, 237, 0], [233, 0, 238, 0], [234, 0, 239, 0], [235, 0, 240, 0], [237, 4, 243, 2, "simultaneousWithExternalGesture"], [237, 35, 243, 33, "simultaneousWithExternalGesture"], [237, 36, 243, 34], [237, 39, 243, 37, "gestures"], [237, 47, 243, 45], [237, 49, 243, 47], [238, 6, 244, 4], [238, 11, 244, 9], [238, 17, 244, 15, "gesture"], [238, 24, 244, 22], [238, 28, 244, 26, "gestures"], [238, 36, 244, 34], [238, 38, 244, 36], [239, 8, 245, 6], [239, 12, 245, 10], [239, 13, 245, 11, "addDependency"], [239, 26, 245, 24], [239, 27, 245, 25], [239, 45, 245, 43], [239, 47, 245, 45, "gesture"], [239, 54, 245, 52], [239, 55, 245, 53], [240, 6, 246, 4], [241, 6, 248, 4], [241, 13, 248, 11], [241, 17, 248, 15], [242, 4, 249, 2], [243, 4, 250, 2], [244, 0, 251, 0], [245, 0, 252, 0], [246, 0, 253, 0], [247, 0, 254, 0], [249, 4, 257, 2, "requireExternalGestureToFail"], [249, 32, 257, 30, "requireExternalGestureToFail"], [249, 33, 257, 31], [249, 36, 257, 34, "gestures"], [249, 44, 257, 42], [249, 46, 257, 44], [250, 6, 258, 4], [250, 11, 258, 9], [250, 17, 258, 15, "gesture"], [250, 24, 258, 22], [250, 28, 258, 26, "gestures"], [250, 36, 258, 34], [250, 38, 258, 36], [251, 8, 259, 6], [251, 12, 259, 10], [251, 13, 259, 11, "addDependency"], [251, 26, 259, 24], [251, 27, 259, 25], [251, 42, 259, 40], [251, 44, 259, 42, "gesture"], [251, 51, 259, 49], [251, 52, 259, 50], [252, 6, 260, 4], [253, 6, 262, 4], [253, 13, 262, 11], [253, 17, 262, 15], [254, 4, 263, 2], [255, 4, 264, 2], [256, 0, 265, 0], [257, 0, 266, 0], [258, 0, 267, 0], [259, 0, 268, 0], [261, 4, 271, 2, "blocksExternalGesture"], [261, 25, 271, 23, "blocksExternalGesture"], [261, 26, 271, 24], [261, 29, 271, 27, "gestures"], [261, 37, 271, 35], [261, 39, 271, 37], [262, 6, 272, 4], [262, 11, 272, 9], [262, 17, 272, 15, "gesture"], [262, 24, 272, 22], [262, 28, 272, 26, "gestures"], [262, 36, 272, 34], [262, 38, 272, 36], [263, 8, 273, 6], [263, 12, 273, 10], [263, 13, 273, 11, "addDependency"], [263, 26, 273, 24], [263, 27, 273, 25], [263, 43, 273, 41], [263, 45, 273, 43, "gesture"], [263, 52, 273, 50], [263, 53, 273, 51], [264, 6, 274, 4], [265, 6, 276, 4], [265, 13, 276, 11], [265, 17, 276, 15], [266, 4, 277, 2], [267, 4, 278, 2], [268, 0, 279, 0], [269, 0, 280, 0], [270, 0, 281, 0], [272, 4, 284, 2, "withTestId"], [272, 14, 284, 12, "withTestId"], [272, 15, 284, 13, "id"], [272, 17, 284, 15], [272, 19, 284, 17], [273, 6, 285, 4], [273, 10, 285, 8], [273, 11, 285, 9, "config"], [273, 17, 285, 15], [273, 18, 285, 16, "testId"], [273, 24, 285, 22], [273, 27, 285, 25, "id"], [273, 29, 285, 27], [274, 6, 286, 4], [274, 13, 286, 11], [274, 17, 286, 15], [275, 4, 287, 2], [276, 4, 288, 2], [277, 0, 289, 0], [278, 0, 290, 0], [279, 0, 291, 0], [280, 0, 292, 0], [281, 0, 293, 0], [283, 4, 296, 2, "cancelsTouchesInView"], [283, 24, 296, 22, "cancelsTouchesInView"], [283, 25, 296, 23, "value"], [283, 30, 296, 28], [283, 32, 296, 30], [284, 6, 297, 4], [284, 10, 297, 8], [284, 11, 297, 9, "config"], [284, 17, 297, 15], [284, 18, 297, 16, "cancelsTouchesInView"], [284, 38, 297, 36], [284, 41, 297, 39, "value"], [284, 46, 297, 44], [285, 6, 298, 4], [285, 13, 298, 11], [285, 17, 298, 15], [286, 4, 299, 2], [287, 4, 301, 2, "initialize"], [287, 14, 301, 12, "initialize"], [287, 15, 301, 12], [287, 17, 301, 15], [288, 6, 302, 4], [288, 10, 302, 8], [288, 11, 302, 9, "handlerTag"], [288, 21, 302, 19], [288, 24, 302, 22], [288, 28, 302, 22, "getNextHandlerTag"], [288, 64, 302, 39], [288, 66, 302, 40], [288, 67, 302, 41], [289, 6, 303, 4], [289, 10, 303, 8], [289, 11, 303, 9, "handlers"], [289, 19, 303, 17], [289, 22, 303, 20], [290, 8, 303, 22], [290, 11, 303, 25], [290, 15, 303, 29], [290, 16, 303, 30, "handlers"], [290, 24, 303, 38], [291, 8, 304, 6, "handlerTag"], [291, 18, 304, 16], [291, 20, 304, 18], [291, 24, 304, 22], [291, 25, 304, 23, "handlerTag"], [292, 6, 305, 4], [292, 7, 305, 5], [293, 6, 307, 4], [293, 10, 307, 8], [293, 14, 307, 12], [293, 15, 307, 13, "config"], [293, 21, 307, 19], [293, 22, 307, 20, "ref"], [293, 25, 307, 23], [293, 27, 307, 25], [294, 8, 308, 6], [294, 12, 308, 10], [294, 13, 308, 11, "config"], [294, 19, 308, 17], [294, 20, 308, 18, "ref"], [294, 23, 308, 21], [294, 24, 308, 22, "current"], [294, 31, 308, 29], [294, 34, 308, 32], [294, 38, 308, 36], [295, 6, 309, 4], [296, 4, 310, 2], [297, 4, 312, 2, "toGestureArray"], [297, 18, 312, 16, "toGestureArray"], [297, 19, 312, 16], [297, 21, 312, 19], [298, 6, 313, 4], [298, 13, 313, 11], [298, 14, 313, 12], [298, 18, 313, 16], [298, 19, 313, 17], [299, 4, 314, 2], [299, 5, 314, 3], [299, 6, 314, 4], [301, 4, 317, 2, "prepare"], [301, 11, 317, 9, "prepare"], [301, 12, 317, 9], [301, 14, 317, 12], [301, 15, 317, 13], [302, 4, 319, 2], [302, 8, 319, 6, "shouldUseReanimated"], [302, 27, 319, 25, "shouldUseReanimated"], [302, 28, 319, 25], [302, 30, 319, 28], [303, 6, 320, 4], [304, 6, 321, 4], [305, 6, 322, 4], [306, 6, 323, 4], [306, 13, 323, 11], [306, 17, 323, 15], [306, 18, 323, 16, "config"], [306, 24, 323, 22], [306, 25, 323, 23, "runOnJS"], [306, 32, 323, 30], [306, 37, 323, 35], [306, 41, 323, 39], [306, 45, 323, 43], [306, 46, 323, 44], [306, 50, 323, 48], [306, 51, 323, 49, "handlers"], [306, 59, 323, 57], [306, 60, 323, 58, "isWorklet"], [306, 69, 323, 67], [306, 70, 323, 68, "includes"], [306, 78, 323, 76], [306, 79, 323, 77], [306, 84, 323, 82], [306, 85, 323, 83], [306, 89, 323, 87], [306, 90, 323, 88], [306, 94, 323, 88, "isRemoteDebuggingEnabled"], [306, 125, 323, 112], [306, 127, 323, 113], [306, 128, 323, 114], [307, 4, 324, 2], [308, 2, 326, 0], [309, 2, 326, 1, "exports"], [309, 9, 326, 1], [309, 10, 326, 1, "BaseGesture"], [309, 21, 326, 1], [309, 24, 326, 1, "BaseGesture"], [309, 35, 326, 1], [310, 2, 327, 7], [310, 8, 327, 13, "ContinousBaseGesture"], [310, 28, 327, 33], [310, 37, 327, 42, "BaseGesture"], [310, 48, 327, 53], [310, 49, 327, 54], [311, 4, 328, 2], [312, 0, 329, 0], [313, 0, 330, 0], [314, 0, 331, 0], [315, 4, 332, 2, "onUpdate"], [315, 12, 332, 10, "onUpdate"], [315, 13, 332, 11, "callback"], [315, 21, 332, 19], [315, 23, 332, 21], [316, 6, 333, 4], [316, 10, 333, 8], [316, 11, 333, 9, "handlers"], [316, 19, 333, 17], [316, 20, 333, 18, "onUpdate"], [316, 28, 333, 26], [316, 31, 333, 29, "callback"], [316, 39, 333, 37], [317, 6, 334, 4], [317, 10, 334, 8], [317, 11, 334, 9, "handlers"], [317, 19, 334, 17], [317, 20, 334, 18, "isWorklet"], [317, 29, 334, 27], [317, 30, 334, 28, "CALLBACK_TYPE"], [317, 43, 334, 41], [317, 44, 334, 42, "UPDATE"], [317, 50, 334, 48], [317, 51, 334, 49], [317, 54, 334, 52], [317, 58, 334, 56], [317, 59, 334, 57, "isWorklet"], [317, 68, 334, 66], [317, 69, 334, 67, "callback"], [317, 77, 334, 75], [317, 78, 334, 76], [318, 6, 335, 4], [318, 13, 335, 11], [318, 17, 335, 15], [319, 4, 336, 2], [320, 4, 337, 2], [321, 0, 338, 0], [322, 0, 339, 0], [323, 0, 340, 0], [324, 0, 341, 0], [326, 4, 344, 2, "onChange"], [326, 12, 344, 10, "onChange"], [326, 13, 344, 11, "callback"], [326, 21, 344, 19], [326, 23, 344, 21], [327, 6, 345, 4], [327, 10, 345, 8], [327, 11, 345, 9, "handlers"], [327, 19, 345, 17], [327, 20, 345, 18, "onChange"], [327, 28, 345, 26], [327, 31, 345, 29, "callback"], [327, 39, 345, 37], [328, 6, 346, 4], [328, 10, 346, 8], [328, 11, 346, 9, "handlers"], [328, 19, 346, 17], [328, 20, 346, 18, "isWorklet"], [328, 29, 346, 27], [328, 30, 346, 28, "CALLBACK_TYPE"], [328, 43, 346, 41], [328, 44, 346, 42, "CHANGE"], [328, 50, 346, 48], [328, 51, 346, 49], [328, 54, 346, 52], [328, 58, 346, 56], [328, 59, 346, 57, "isWorklet"], [328, 68, 346, 66], [328, 69, 346, 67, "callback"], [328, 77, 346, 75], [328, 78, 346, 76], [329, 6, 347, 4], [329, 13, 347, 11], [329, 17, 347, 15], [330, 4, 348, 2], [331, 4, 349, 2], [332, 0, 350, 0], [333, 0, 351, 0], [334, 0, 352, 0], [335, 0, 353, 0], [337, 4, 356, 2, "manualActivation"], [337, 20, 356, 18, "manualActivation"], [337, 21, 356, 19, "manualActivation"], [337, 37, 356, 35], [337, 39, 356, 37], [338, 6, 357, 4], [338, 10, 357, 8], [338, 11, 357, 9, "config"], [338, 17, 357, 15], [338, 18, 357, 16, "manualActivation"], [338, 34, 357, 32], [338, 37, 357, 35, "manualActivation"], [338, 53, 357, 51], [339, 6, 358, 4], [339, 13, 358, 11], [339, 17, 358, 15], [340, 4, 359, 2], [341, 2, 361, 0], [342, 2, 361, 1, "exports"], [342, 9, 361, 1], [342, 10, 361, 1, "ContinousBaseGesture"], [342, 30, 361, 1], [342, 33, 361, 1, "ContinousBaseGesture"], [342, 53, 361, 1], [343, 0, 361, 1], [343, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "Gesture", "BaseGesture", "BaseGesture#constructor", "BaseGesture#addDependency", "BaseGesture#withRef", "BaseGesture#isWorklet", "BaseGesture#onBegin", "BaseGesture#onStart", "BaseGesture#onEnd", "BaseGesture#onFinalize", "BaseGesture#onTouchesDown", "BaseGesture#onTouchesMove", "BaseGesture#onTouchesUp", "BaseGesture#onTouchesCancelled", "BaseGesture#enabled", "BaseGesture#shouldCancelWhenOutside", "BaseGesture#hitSlop", "BaseGesture#activeCursor", "BaseGesture#mouseButton", "BaseGesture#runOnJS", "BaseGesture#simultaneousWithExternalGesture", "BaseGesture#requireExternalGestureToFail", "BaseGesture#blocksExternalGesture", "BaseGesture#withTestId", "BaseGesture#cancelsTouchesInView", "BaseGesture#initialize", "BaseGesture#toGestureArray", "BaseGesture#prepare", "BaseGesture#get__shouldUseReanimated", "ContinousBaseGesture", "ContinousBaseGesture#onUpdate", "ContinousBaseGesture#onChange", "ContinousBaseGesture#manualActivation"], "mappings": "AAA,iNC;OCmB,gBD;OEE;ECC;GDuB;EEE;GFG;EGO;GHG;EIG;GJG;EKQ;GLI;EMO;GNI;EOQ;GPK;EQO;GRK;ESO;GTK;EUO;GVK;EWO;GXK;EYO;GZK;EaQ;GbG;EcQ;GdG;EeS;GfG;EgBS;GhBG;EiBW;GjBG;EkBS;GlBG;EmBQ;GnBM;EoBQ;GpBM;EqBQ;GrBM;EsBO;GtBG;EuBS;GvBG;EwBE;GxBS;EyBE;GzBE;E0BG,Y1B;E2BE;G3BK;CFE;O8BC;ECK;GDI;EEQ;GFI;EGQ;GHG;C9BE"}}, "type": "js/module"}]}