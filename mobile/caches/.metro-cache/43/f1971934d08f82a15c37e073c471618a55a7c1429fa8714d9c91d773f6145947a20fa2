{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MetroServerError = void 0;\n  class MetroServerError extends Error {\n    code = 'METRO_SERVER_ERROR';\n    constructor(errorObject, url) {\n      super(errorObject.message);\n      this.url = url;\n      this.name = 'MetroServerError';\n      for (const key in errorObject) {\n        this[key] = errorObject[key];\n      }\n    }\n  }\n  exports.MetroServerError = MetroServerError;\n});", "lineCount": 18, "map": [[6, 2, 1, 7], [6, 8, 1, 13, "MetroServerError"], [6, 24, 1, 29], [6, 33, 1, 38, "Error"], [6, 38, 1, 43], [6, 39, 1, 44], [7, 4, 2, 2, "code"], [7, 8, 2, 6], [7, 11, 2, 9], [7, 31, 2, 29], [8, 4, 4, 2, "constructor"], [8, 15, 4, 13, "constructor"], [8, 16, 5, 4, "errorObject"], [8, 27, 5, 58], [8, 29, 6, 11, "url"], [8, 32, 6, 22], [8, 34, 7, 4], [9, 6, 8, 4], [9, 11, 8, 9], [9, 12, 8, 10, "errorObject"], [9, 23, 8, 21], [9, 24, 8, 22, "message"], [9, 31, 8, 29], [9, 32, 8, 30], [10, 6, 8, 31], [10, 11, 6, 11, "url"], [10, 14, 6, 22], [10, 17, 6, 11, "url"], [10, 20, 6, 22], [11, 6, 9, 4], [11, 10, 9, 8], [11, 11, 9, 9, "name"], [11, 15, 9, 13], [11, 18, 9, 16], [11, 36, 9, 34], [12, 6, 11, 4], [12, 11, 11, 9], [12, 17, 11, 15, "key"], [12, 20, 11, 18], [12, 24, 11, 22, "errorObject"], [12, 35, 11, 33], [12, 37, 11, 35], [13, 8, 12, 7], [13, 12, 12, 11], [13, 13, 12, 20, "key"], [13, 16, 12, 23], [13, 17, 12, 24], [13, 20, 12, 27, "errorObject"], [13, 31, 12, 38], [13, 32, 12, 39, "key"], [13, 35, 12, 42], [13, 36, 12, 43], [14, 6, 13, 4], [15, 4, 14, 2], [16, 2, 15, 0], [17, 2, 15, 1, "exports"], [17, 9, 15, 1], [17, 10, 15, 1, "MetroServerError"], [17, 26, 15, 1], [17, 29, 15, 1, "MetroServerError"], [17, 45, 15, 1], [18, 0, 15, 1], [18, 3]], "functionMap": {"names": ["<global>", "MetroServerError", "MetroServerError#constructor"], "mappings": "AAA,OC;ECG;GDU;CDC"}}, "type": "js/module"}]}