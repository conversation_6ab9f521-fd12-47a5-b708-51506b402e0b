{"dependencies": [{"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 113, "index": 128}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 129}, "end": {"line": 4, "column": 31, "index": 160}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationContainer.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 161}, "end": {"line": 5, "column": 63, "index": 224}}], "key": "LB6hXm0TqYVluaSOGRsC2pfPato=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 225}, "end": {"line": 6, "column": 48, "index": 273}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createStaticNavigation = createStaticNavigation;\n  var _core = require(_dependencyMap[0], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _NavigationContainer = require(_dependencyMap[2], \"./NavigationContainer.js\");\n  var _jsxRuntime = require(_dependencyMap[3], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Create a navigation component from a static navigation config.\n   * The returned component is a wrapper around `NavigationContainer`.\n   *\n   * @param tree Static navigation config.\n   * @returns Navigation component to use in your app.\n   */\n  function createStaticNavigation(tree) {\n    const Component = (0, _core.createComponentForStaticNavigation)(tree, 'RootNavigator');\n    function Navigation({\n      linking,\n      ...rest\n    }, ref) {\n      const linkingConfig = React.useMemo(() => {\n        const screens = (0, _core.createPathConfigForStaticNavigation)(tree, {\n          initialRouteName: linking?.config?.initialRouteName\n        }, linking?.enabled === 'auto');\n        if (!screens) return;\n        return {\n          path: linking?.config?.path,\n          initialRouteName: linking?.config?.initialRouteName,\n          screens\n        };\n      }, [linking?.enabled, linking?.config?.path, linking?.config?.initialRouteName]);\n      const memoizedLinking = React.useMemo(() => {\n        if (!linking) {\n          return undefined;\n        }\n        const enabled = typeof linking.enabled === 'boolean' ? linking.enabled : linkingConfig?.screens != null;\n        return {\n          ...linking,\n          enabled,\n          config: linkingConfig\n        };\n      }, [linking, linkingConfig]);\n      if (linking?.enabled === true && linkingConfig?.screens == null) {\n        throw new Error('Linking is enabled but no linking configuration was found for the screens.\\n\\n' + 'To solve this:\\n' + \"- Specify a 'linking' property for the screens you want to link to.\\n\" + \"- Or set 'linking.enabled' to 'auto' to generate paths automatically.\\n\\n\" + 'See usage guide: https://reactnavigation.org/docs/static-configuration#linking');\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationContainer.NavigationContainer, {\n        ...rest,\n        ref: ref,\n        linking: memoizedLinking,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(Component, {})\n      });\n    }\n    return /*#__PURE__*/React.forwardRef(Navigation);\n  }\n});", "lineCount": 60, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "createStaticNavigation"], [7, 32, 1, 13], [7, 35, 1, 13, "createStaticNavigation"], [7, 57, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationContainer"], [10, 26, 5, 0], [10, 29, 5, 0, "require"], [10, 36, 5, 0], [10, 37, 5, 0, "_dependencyMap"], [10, 51, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_jsxRuntime"], [11, 17, 6, 0], [11, 20, 6, 0, "require"], [11, 27, 6, 0], [11, 28, 6, 0, "_dependencyMap"], [11, 42, 6, 0], [12, 2, 6, 48], [12, 11, 6, 48, "_interopRequireWildcard"], [12, 35, 6, 48, "e"], [12, 36, 6, 48], [12, 38, 6, 48, "t"], [12, 39, 6, 48], [12, 68, 6, 48, "WeakMap"], [12, 75, 6, 48], [12, 81, 6, 48, "r"], [12, 82, 6, 48], [12, 89, 6, 48, "WeakMap"], [12, 96, 6, 48], [12, 100, 6, 48, "n"], [12, 101, 6, 48], [12, 108, 6, 48, "WeakMap"], [12, 115, 6, 48], [12, 127, 6, 48, "_interopRequireWildcard"], [12, 150, 6, 48], [12, 162, 6, 48, "_interopRequireWildcard"], [12, 163, 6, 48, "e"], [12, 164, 6, 48], [12, 166, 6, 48, "t"], [12, 167, 6, 48], [12, 176, 6, 48, "t"], [12, 177, 6, 48], [12, 181, 6, 48, "e"], [12, 182, 6, 48], [12, 186, 6, 48, "e"], [12, 187, 6, 48], [12, 188, 6, 48, "__esModule"], [12, 198, 6, 48], [12, 207, 6, 48, "e"], [12, 208, 6, 48], [12, 214, 6, 48, "o"], [12, 215, 6, 48], [12, 217, 6, 48, "i"], [12, 218, 6, 48], [12, 220, 6, 48, "f"], [12, 221, 6, 48], [12, 226, 6, 48, "__proto__"], [12, 235, 6, 48], [12, 243, 6, 48, "default"], [12, 250, 6, 48], [12, 252, 6, 48, "e"], [12, 253, 6, 48], [12, 270, 6, 48, "e"], [12, 271, 6, 48], [12, 294, 6, 48, "e"], [12, 295, 6, 48], [12, 320, 6, 48, "e"], [12, 321, 6, 48], [12, 330, 6, 48, "f"], [12, 331, 6, 48], [12, 337, 6, 48, "o"], [12, 338, 6, 48], [12, 341, 6, 48, "t"], [12, 342, 6, 48], [12, 345, 6, 48, "n"], [12, 346, 6, 48], [12, 349, 6, 48, "r"], [12, 350, 6, 48], [12, 358, 6, 48, "o"], [12, 359, 6, 48], [12, 360, 6, 48, "has"], [12, 363, 6, 48], [12, 364, 6, 48, "e"], [12, 365, 6, 48], [12, 375, 6, 48, "o"], [12, 376, 6, 48], [12, 377, 6, 48, "get"], [12, 380, 6, 48], [12, 381, 6, 48, "e"], [12, 382, 6, 48], [12, 385, 6, 48, "o"], [12, 386, 6, 48], [12, 387, 6, 48, "set"], [12, 390, 6, 48], [12, 391, 6, 48, "e"], [12, 392, 6, 48], [12, 394, 6, 48, "f"], [12, 395, 6, 48], [12, 411, 6, 48, "t"], [12, 412, 6, 48], [12, 416, 6, 48, "e"], [12, 417, 6, 48], [12, 433, 6, 48, "t"], [12, 434, 6, 48], [12, 441, 6, 48, "hasOwnProperty"], [12, 455, 6, 48], [12, 456, 6, 48, "call"], [12, 460, 6, 48], [12, 461, 6, 48, "e"], [12, 462, 6, 48], [12, 464, 6, 48, "t"], [12, 465, 6, 48], [12, 472, 6, 48, "i"], [12, 473, 6, 48], [12, 477, 6, 48, "o"], [12, 478, 6, 48], [12, 481, 6, 48, "Object"], [12, 487, 6, 48], [12, 488, 6, 48, "defineProperty"], [12, 502, 6, 48], [12, 507, 6, 48, "Object"], [12, 513, 6, 48], [12, 514, 6, 48, "getOwnPropertyDescriptor"], [12, 538, 6, 48], [12, 539, 6, 48, "e"], [12, 540, 6, 48], [12, 542, 6, 48, "t"], [12, 543, 6, 48], [12, 550, 6, 48, "i"], [12, 551, 6, 48], [12, 552, 6, 48, "get"], [12, 555, 6, 48], [12, 559, 6, 48, "i"], [12, 560, 6, 48], [12, 561, 6, 48, "set"], [12, 564, 6, 48], [12, 568, 6, 48, "o"], [12, 569, 6, 48], [12, 570, 6, 48, "f"], [12, 571, 6, 48], [12, 573, 6, 48, "t"], [12, 574, 6, 48], [12, 576, 6, 48, "i"], [12, 577, 6, 48], [12, 581, 6, 48, "f"], [12, 582, 6, 48], [12, 583, 6, 48, "t"], [12, 584, 6, 48], [12, 588, 6, 48, "e"], [12, 589, 6, 48], [12, 590, 6, 48, "t"], [12, 591, 6, 48], [12, 602, 6, 48, "f"], [12, 603, 6, 48], [12, 608, 6, 48, "e"], [12, 609, 6, 48], [12, 611, 6, 48, "t"], [12, 612, 6, 48], [13, 2, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 2, 14, 7], [20, 11, 14, 16, "createStaticNavigation"], [20, 33, 14, 38, "createStaticNavigation"], [20, 34, 14, 39, "tree"], [20, 38, 14, 43], [20, 40, 14, 45], [21, 4, 15, 2], [21, 10, 15, 8, "Component"], [21, 19, 15, 17], [21, 22, 15, 20], [21, 26, 15, 20, "createComponentForStaticNavigation"], [21, 66, 15, 54], [21, 68, 15, 55, "tree"], [21, 72, 15, 59], [21, 74, 15, 61], [21, 89, 15, 76], [21, 90, 15, 77], [22, 4, 16, 2], [22, 13, 16, 11, "Navigation"], [22, 23, 16, 21, "Navigation"], [22, 24, 16, 22], [23, 6, 17, 4, "linking"], [23, 13, 17, 11], [24, 6, 18, 4], [24, 9, 18, 7, "rest"], [25, 4, 19, 2], [25, 5, 19, 3], [25, 7, 19, 5, "ref"], [25, 10, 19, 8], [25, 12, 19, 10], [26, 6, 20, 4], [26, 12, 20, 10, "linkingConfig"], [26, 25, 20, 23], [26, 28, 20, 26, "React"], [26, 33, 20, 31], [26, 34, 20, 32, "useMemo"], [26, 41, 20, 39], [26, 42, 20, 40], [26, 48, 20, 46], [27, 8, 21, 6], [27, 14, 21, 12, "screens"], [27, 21, 21, 19], [27, 24, 21, 22], [27, 28, 21, 22, "createPathConfigForStaticNavigation"], [27, 69, 21, 57], [27, 71, 21, 58, "tree"], [27, 75, 21, 62], [27, 77, 21, 64], [28, 10, 22, 8, "initialRouteName"], [28, 26, 22, 24], [28, 28, 22, 26, "linking"], [28, 35, 22, 33], [28, 37, 22, 35, "config"], [28, 43, 22, 41], [28, 45, 22, 43, "initialRouteName"], [29, 8, 23, 6], [29, 9, 23, 7], [29, 11, 23, 9, "linking"], [29, 18, 23, 16], [29, 20, 23, 18, "enabled"], [29, 27, 23, 25], [29, 32, 23, 30], [29, 38, 23, 36], [29, 39, 23, 37], [30, 8, 24, 6], [30, 12, 24, 10], [30, 13, 24, 11, "screens"], [30, 20, 24, 18], [30, 22, 24, 20], [31, 8, 25, 6], [31, 15, 25, 13], [32, 10, 26, 8, "path"], [32, 14, 26, 12], [32, 16, 26, 14, "linking"], [32, 23, 26, 21], [32, 25, 26, 23, "config"], [32, 31, 26, 29], [32, 33, 26, 31, "path"], [32, 37, 26, 35], [33, 10, 27, 8, "initialRouteName"], [33, 26, 27, 24], [33, 28, 27, 26, "linking"], [33, 35, 27, 33], [33, 37, 27, 35, "config"], [33, 43, 27, 41], [33, 45, 27, 43, "initialRouteName"], [33, 61, 27, 59], [34, 10, 28, 8, "screens"], [35, 8, 29, 6], [35, 9, 29, 7], [36, 6, 30, 4], [36, 7, 30, 5], [36, 9, 30, 7], [36, 10, 30, 8, "linking"], [36, 17, 30, 15], [36, 19, 30, 17, "enabled"], [36, 26, 30, 24], [36, 28, 30, 26, "linking"], [36, 35, 30, 33], [36, 37, 30, 35, "config"], [36, 43, 30, 41], [36, 45, 30, 43, "path"], [36, 49, 30, 47], [36, 51, 30, 49, "linking"], [36, 58, 30, 56], [36, 60, 30, 58, "config"], [36, 66, 30, 64], [36, 68, 30, 66, "initialRouteName"], [36, 84, 30, 82], [36, 85, 30, 83], [36, 86, 30, 84], [37, 6, 31, 4], [37, 12, 31, 10, "memoizedLinking"], [37, 27, 31, 25], [37, 30, 31, 28, "React"], [37, 35, 31, 33], [37, 36, 31, 34, "useMemo"], [37, 43, 31, 41], [37, 44, 31, 42], [37, 50, 31, 48], [38, 8, 32, 6], [38, 12, 32, 10], [38, 13, 32, 11, "linking"], [38, 20, 32, 18], [38, 22, 32, 20], [39, 10, 33, 8], [39, 17, 33, 15, "undefined"], [39, 26, 33, 24], [40, 8, 34, 6], [41, 8, 35, 6], [41, 14, 35, 12, "enabled"], [41, 21, 35, 19], [41, 24, 35, 22], [41, 31, 35, 29, "linking"], [41, 38, 35, 36], [41, 39, 35, 37, "enabled"], [41, 46, 35, 44], [41, 51, 35, 49], [41, 60, 35, 58], [41, 63, 35, 61, "linking"], [41, 70, 35, 68], [41, 71, 35, 69, "enabled"], [41, 78, 35, 76], [41, 81, 35, 79, "linkingConfig"], [41, 94, 35, 92], [41, 96, 35, 94, "screens"], [41, 103, 35, 101], [41, 107, 35, 105], [41, 111, 35, 109], [42, 8, 36, 6], [42, 15, 36, 13], [43, 10, 37, 8], [43, 13, 37, 11, "linking"], [43, 20, 37, 18], [44, 10, 38, 8, "enabled"], [44, 17, 38, 15], [45, 10, 39, 8, "config"], [45, 16, 39, 14], [45, 18, 39, 16, "linkingConfig"], [46, 8, 40, 6], [46, 9, 40, 7], [47, 6, 41, 4], [47, 7, 41, 5], [47, 9, 41, 7], [47, 10, 41, 8, "linking"], [47, 17, 41, 15], [47, 19, 41, 17, "linkingConfig"], [47, 32, 41, 30], [47, 33, 41, 31], [47, 34, 41, 32], [48, 6, 42, 4], [48, 10, 42, 8, "linking"], [48, 17, 42, 15], [48, 19, 42, 17, "enabled"], [48, 26, 42, 24], [48, 31, 42, 29], [48, 35, 42, 33], [48, 39, 42, 37, "linkingConfig"], [48, 52, 42, 50], [48, 54, 42, 52, "screens"], [48, 61, 42, 59], [48, 65, 42, 63], [48, 69, 42, 67], [48, 71, 42, 69], [49, 8, 43, 6], [49, 14, 43, 12], [49, 18, 43, 16, "Error"], [49, 23, 43, 21], [49, 24, 43, 22], [49, 104, 43, 102], [49, 107, 43, 105], [49, 125, 43, 123], [49, 128, 43, 126], [49, 199, 43, 197], [49, 202, 43, 200], [49, 277, 43, 275], [49, 280, 43, 278], [49, 360, 43, 358], [49, 361, 43, 359], [50, 6, 44, 4], [51, 6, 45, 4], [51, 13, 45, 11], [51, 26, 45, 24], [51, 30, 45, 24, "_jsx"], [51, 45, 45, 28], [51, 47, 45, 29, "NavigationContainer"], [51, 87, 45, 48], [51, 89, 45, 50], [52, 8, 46, 6], [52, 11, 46, 9, "rest"], [52, 15, 46, 13], [53, 8, 47, 6, "ref"], [53, 11, 47, 9], [53, 13, 47, 11, "ref"], [53, 16, 47, 14], [54, 8, 48, 6, "linking"], [54, 15, 48, 13], [54, 17, 48, 15, "memoizedLinking"], [54, 32, 48, 30], [55, 8, 49, 6, "children"], [55, 16, 49, 14], [55, 18, 49, 16], [55, 31, 49, 29], [55, 35, 49, 29, "_jsx"], [55, 50, 49, 33], [55, 52, 49, 34, "Component"], [55, 61, 49, 43], [55, 63, 49, 45], [55, 64, 49, 46], [55, 65, 49, 47], [56, 6, 50, 4], [56, 7, 50, 5], [56, 8, 50, 6], [57, 4, 51, 2], [58, 4, 52, 2], [58, 11, 52, 9], [58, 24, 52, 22, "React"], [58, 29, 52, 27], [58, 30, 52, 28, "forwardRef"], [58, 40, 52, 38], [58, 41, 52, 39, "Navigation"], [58, 51, 52, 49], [58, 52, 52, 50], [59, 2, 53, 0], [60, 0, 53, 1], [60, 3]], "functionMap": {"names": ["<global>", "createStaticNavigation", "Navigation", "React.useMemo$argument_0"], "mappings": "AAA;OCa;ECE;wCCI;KDU;0CCC;KDU;GDU;CDE"}}, "type": "js/module"}]}