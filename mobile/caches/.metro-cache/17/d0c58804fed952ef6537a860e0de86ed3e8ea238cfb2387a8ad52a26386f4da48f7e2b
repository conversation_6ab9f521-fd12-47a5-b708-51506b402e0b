{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 66, "index": 81}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 82}, "end": {"line": 4, "column": 26, "index": 108}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 109}, "end": {"line": 5, "column": 31, "index": 140}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "./PlatformPressable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 194}, "end": {"line": 7, "column": 59, "index": 253}}], "key": "7Wm8S4t9JyY/16EtBirZwW7XtgQ=", "exportNames": ["*"]}}, {"name": "./Text.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 254}, "end": {"line": 8, "column": 33, "index": 287}}], "key": "QTnFfg9+sbvsvptKfI6RYkeAj2s=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 288}, "end": {"line": 9, "column": 48, "index": 336}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Button = Button;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[2], \"color\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _PlatformPressable = require(_dependencyMap[6], \"./PlatformPressable.js\");\n  var _Text = require(_dependencyMap[7], \"./Text.js\");\n  var _jsxRuntime = require(_dependencyMap[8], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const BUTTON_RADIUS = 40;\n  function Button(props) {\n    if ('screen' in props || 'action' in props) {\n      // @ts-expect-error: This is already type-checked by the prop types\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(ButtonLink, {\n        ...props\n      });\n    } else {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(ButtonBase, {\n        ...props\n      });\n    }\n  }\n  function ButtonLink({\n    screen,\n    params,\n    action,\n    href,\n    ...rest\n  }) {\n    // @ts-expect-error: This is already type-checked by the prop types\n    const props = (0, _native.useLinkProps)({\n      screen,\n      params,\n      action,\n      href\n    });\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(ButtonBase, {\n      ...rest,\n      ...props\n    });\n  }\n  function ButtonBase({\n    variant = 'tinted',\n    color: customColor,\n    android_ripple,\n    style,\n    children,\n    ...rest\n  }) {\n    const {\n      colors,\n      fonts\n    } = (0, _native.useTheme)();\n    const color = customColor ?? colors.primary;\n    let backgroundColor;\n    let textColor;\n    switch (variant) {\n      case 'plain':\n        backgroundColor = 'transparent';\n        textColor = color;\n        break;\n      case 'tinted':\n        backgroundColor = (0, _color.default)(color).fade(0.85).string();\n        textColor = color;\n        break;\n      case 'filled':\n        backgroundColor = color;\n        textColor = (0, _color.default)(color).isDark() ? 'white' : (0, _color.default)(color).darken(0.71).string();\n        break;\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_PlatformPressable.PlatformPressable, {\n      ...rest,\n      android_ripple: {\n        radius: BUTTON_RADIUS,\n        color: (0, _color.default)(textColor).fade(0.85).string(),\n        ...android_ripple\n      },\n      pressOpacity: _Platform.default.OS === 'ios' ? undefined : 1,\n      hoverEffect: {\n        color: textColor\n      },\n      style: [{\n        backgroundColor\n      }, styles.button, style],\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_Text.Text, {\n        style: [{\n          color: textColor\n        }, fonts.regular, styles.text],\n        children: children\n      })\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    button: {\n      paddingHorizontal: 24,\n      paddingVertical: 10,\n      borderRadius: BUTTON_RADIUS\n    },\n    text: {\n      fontSize: 14,\n      lineHeight: 20,\n      letterSpacing: 0.1,\n      textAlign: 'center'\n    }\n  });\n});", "lineCount": 114, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON>"], [8, 16, 1, 13], [8, 19, 1, 13, "<PERSON><PERSON>"], [8, 25, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_color"], [10, 12, 4, 0], [10, 15, 4, 0, "_interopRequireDefault"], [10, 37, 4, 0], [10, 38, 4, 0, "require"], [10, 45, 4, 0], [10, 46, 4, 0, "_dependencyMap"], [10, 60, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "React"], [11, 11, 5, 0], [11, 14, 5, 0, "_interopRequireWildcard"], [11, 37, 5, 0], [11, 38, 5, 0, "require"], [11, 45, 5, 0], [11, 46, 5, 0, "_dependencyMap"], [11, 60, 5, 0], [12, 2, 5, 31], [12, 6, 5, 31, "_Platform"], [12, 15, 5, 31], [12, 18, 5, 31, "_interopRequireDefault"], [12, 40, 5, 31], [12, 41, 5, 31, "require"], [12, 48, 5, 31], [12, 49, 5, 31, "_dependencyMap"], [12, 63, 5, 31], [13, 2, 5, 31], [13, 6, 5, 31, "_StyleSheet"], [13, 17, 5, 31], [13, 20, 5, 31, "_interopRequireDefault"], [13, 42, 5, 31], [13, 43, 5, 31, "require"], [13, 50, 5, 31], [13, 51, 5, 31, "_dependencyMap"], [13, 65, 5, 31], [14, 2, 7, 0], [14, 6, 7, 0, "_PlatformPressable"], [14, 24, 7, 0], [14, 27, 7, 0, "require"], [14, 34, 7, 0], [14, 35, 7, 0, "_dependencyMap"], [14, 49, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_Text"], [15, 11, 8, 0], [15, 14, 8, 0, "require"], [15, 21, 8, 0], [15, 22, 8, 0, "_dependencyMap"], [15, 36, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_jsxRuntime"], [16, 17, 9, 0], [16, 20, 9, 0, "require"], [16, 27, 9, 0], [16, 28, 9, 0, "_dependencyMap"], [16, 42, 9, 0], [17, 2, 9, 48], [17, 11, 9, 48, "_interopRequireWildcard"], [17, 35, 9, 48, "e"], [17, 36, 9, 48], [17, 38, 9, 48, "t"], [17, 39, 9, 48], [17, 68, 9, 48, "WeakMap"], [17, 75, 9, 48], [17, 81, 9, 48, "r"], [17, 82, 9, 48], [17, 89, 9, 48, "WeakMap"], [17, 96, 9, 48], [17, 100, 9, 48, "n"], [17, 101, 9, 48], [17, 108, 9, 48, "WeakMap"], [17, 115, 9, 48], [17, 127, 9, 48, "_interopRequireWildcard"], [17, 150, 9, 48], [17, 162, 9, 48, "_interopRequireWildcard"], [17, 163, 9, 48, "e"], [17, 164, 9, 48], [17, 166, 9, 48, "t"], [17, 167, 9, 48], [17, 176, 9, 48, "t"], [17, 177, 9, 48], [17, 181, 9, 48, "e"], [17, 182, 9, 48], [17, 186, 9, 48, "e"], [17, 187, 9, 48], [17, 188, 9, 48, "__esModule"], [17, 198, 9, 48], [17, 207, 9, 48, "e"], [17, 208, 9, 48], [17, 214, 9, 48, "o"], [17, 215, 9, 48], [17, 217, 9, 48, "i"], [17, 218, 9, 48], [17, 220, 9, 48, "f"], [17, 221, 9, 48], [17, 226, 9, 48, "__proto__"], [17, 235, 9, 48], [17, 243, 9, 48, "default"], [17, 250, 9, 48], [17, 252, 9, 48, "e"], [17, 253, 9, 48], [17, 270, 9, 48, "e"], [17, 271, 9, 48], [17, 294, 9, 48, "e"], [17, 295, 9, 48], [17, 320, 9, 48, "e"], [17, 321, 9, 48], [17, 330, 9, 48, "f"], [17, 331, 9, 48], [17, 337, 9, 48, "o"], [17, 338, 9, 48], [17, 341, 9, 48, "t"], [17, 342, 9, 48], [17, 345, 9, 48, "n"], [17, 346, 9, 48], [17, 349, 9, 48, "r"], [17, 350, 9, 48], [17, 358, 9, 48, "o"], [17, 359, 9, 48], [17, 360, 9, 48, "has"], [17, 363, 9, 48], [17, 364, 9, 48, "e"], [17, 365, 9, 48], [17, 375, 9, 48, "o"], [17, 376, 9, 48], [17, 377, 9, 48, "get"], [17, 380, 9, 48], [17, 381, 9, 48, "e"], [17, 382, 9, 48], [17, 385, 9, 48, "o"], [17, 386, 9, 48], [17, 387, 9, 48, "set"], [17, 390, 9, 48], [17, 391, 9, 48, "e"], [17, 392, 9, 48], [17, 394, 9, 48, "f"], [17, 395, 9, 48], [17, 411, 9, 48, "t"], [17, 412, 9, 48], [17, 416, 9, 48, "e"], [17, 417, 9, 48], [17, 433, 9, 48, "t"], [17, 434, 9, 48], [17, 441, 9, 48, "hasOwnProperty"], [17, 455, 9, 48], [17, 456, 9, 48, "call"], [17, 460, 9, 48], [17, 461, 9, 48, "e"], [17, 462, 9, 48], [17, 464, 9, 48, "t"], [17, 465, 9, 48], [17, 472, 9, 48, "i"], [17, 473, 9, 48], [17, 477, 9, 48, "o"], [17, 478, 9, 48], [17, 481, 9, 48, "Object"], [17, 487, 9, 48], [17, 488, 9, 48, "defineProperty"], [17, 502, 9, 48], [17, 507, 9, 48, "Object"], [17, 513, 9, 48], [17, 514, 9, 48, "getOwnPropertyDescriptor"], [17, 538, 9, 48], [17, 539, 9, 48, "e"], [17, 540, 9, 48], [17, 542, 9, 48, "t"], [17, 543, 9, 48], [17, 550, 9, 48, "i"], [17, 551, 9, 48], [17, 552, 9, 48, "get"], [17, 555, 9, 48], [17, 559, 9, 48, "i"], [17, 560, 9, 48], [17, 561, 9, 48, "set"], [17, 564, 9, 48], [17, 568, 9, 48, "o"], [17, 569, 9, 48], [17, 570, 9, 48, "f"], [17, 571, 9, 48], [17, 573, 9, 48, "t"], [17, 574, 9, 48], [17, 576, 9, 48, "i"], [17, 577, 9, 48], [17, 581, 9, 48, "f"], [17, 582, 9, 48], [17, 583, 9, 48, "t"], [17, 584, 9, 48], [17, 588, 9, 48, "e"], [17, 589, 9, 48], [17, 590, 9, 48, "t"], [17, 591, 9, 48], [17, 602, 9, 48, "f"], [17, 603, 9, 48], [17, 608, 9, 48, "e"], [17, 609, 9, 48], [17, 611, 9, 48, "t"], [17, 612, 9, 48], [18, 2, 10, 0], [18, 8, 10, 6, "BUTTON_RADIUS"], [18, 21, 10, 19], [18, 24, 10, 22], [18, 26, 10, 24], [19, 2, 11, 7], [19, 11, 11, 16, "<PERSON><PERSON>"], [19, 17, 11, 22, "<PERSON><PERSON>"], [19, 18, 11, 23, "props"], [19, 23, 11, 28], [19, 25, 11, 30], [20, 4, 12, 2], [20, 8, 12, 6], [20, 16, 12, 14], [20, 20, 12, 18, "props"], [20, 25, 12, 23], [20, 29, 12, 27], [20, 37, 12, 35], [20, 41, 12, 39, "props"], [20, 46, 12, 44], [20, 48, 12, 46], [21, 6, 13, 4], [22, 6, 14, 4], [22, 13, 14, 11], [22, 26, 14, 24], [22, 30, 14, 24, "_jsx"], [22, 45, 14, 28], [22, 47, 14, 29, "ButtonLink"], [22, 57, 14, 39], [22, 59, 14, 41], [23, 8, 15, 6], [23, 11, 15, 9, "props"], [24, 6, 16, 4], [24, 7, 16, 5], [24, 8, 16, 6], [25, 4, 17, 2], [25, 5, 17, 3], [25, 11, 17, 9], [26, 6, 18, 4], [26, 13, 18, 11], [26, 26, 18, 24], [26, 30, 18, 24, "_jsx"], [26, 45, 18, 28], [26, 47, 18, 29, "ButtonBase"], [26, 57, 18, 39], [26, 59, 18, 41], [27, 8, 19, 6], [27, 11, 19, 9, "props"], [28, 6, 20, 4], [28, 7, 20, 5], [28, 8, 20, 6], [29, 4, 21, 2], [30, 2, 22, 0], [31, 2, 23, 0], [31, 11, 23, 9, "ButtonLink"], [31, 21, 23, 19, "ButtonLink"], [31, 22, 23, 20], [32, 4, 24, 2, "screen"], [32, 10, 24, 8], [33, 4, 25, 2, "params"], [33, 10, 25, 8], [34, 4, 26, 2, "action"], [34, 10, 26, 8], [35, 4, 27, 2, "href"], [35, 8, 27, 6], [36, 4, 28, 2], [36, 7, 28, 5, "rest"], [37, 2, 29, 0], [37, 3, 29, 1], [37, 5, 29, 3], [38, 4, 30, 2], [39, 4, 31, 2], [39, 10, 31, 8, "props"], [39, 15, 31, 13], [39, 18, 31, 16], [39, 22, 31, 16, "useLinkProps"], [39, 42, 31, 28], [39, 44, 31, 29], [40, 6, 32, 4, "screen"], [40, 12, 32, 10], [41, 6, 33, 4, "params"], [41, 12, 33, 10], [42, 6, 34, 4, "action"], [42, 12, 34, 10], [43, 6, 35, 4, "href"], [44, 4, 36, 2], [44, 5, 36, 3], [44, 6, 36, 4], [45, 4, 37, 2], [45, 11, 37, 9], [45, 24, 37, 22], [45, 28, 37, 22, "_jsx"], [45, 43, 37, 26], [45, 45, 37, 27, "ButtonBase"], [45, 55, 37, 37], [45, 57, 37, 39], [46, 6, 38, 4], [46, 9, 38, 7, "rest"], [46, 13, 38, 11], [47, 6, 39, 4], [47, 9, 39, 7, "props"], [48, 4, 40, 2], [48, 5, 40, 3], [48, 6, 40, 4], [49, 2, 41, 0], [50, 2, 42, 0], [50, 11, 42, 9, "ButtonBase"], [50, 21, 42, 19, "ButtonBase"], [50, 22, 42, 20], [51, 4, 43, 2, "variant"], [51, 11, 43, 9], [51, 14, 43, 12], [51, 22, 43, 20], [52, 4, 44, 2, "color"], [52, 9, 44, 7], [52, 11, 44, 9, "customColor"], [52, 22, 44, 20], [53, 4, 45, 2, "android_ripple"], [53, 18, 45, 16], [54, 4, 46, 2, "style"], [54, 9, 46, 7], [55, 4, 47, 2, "children"], [55, 12, 47, 10], [56, 4, 48, 2], [56, 7, 48, 5, "rest"], [57, 2, 49, 0], [57, 3, 49, 1], [57, 5, 49, 3], [58, 4, 50, 2], [58, 10, 50, 8], [59, 6, 51, 4, "colors"], [59, 12, 51, 10], [60, 6, 52, 4, "fonts"], [61, 4, 53, 2], [61, 5, 53, 3], [61, 8, 53, 6], [61, 12, 53, 6, "useTheme"], [61, 28, 53, 14], [61, 30, 53, 15], [61, 31, 53, 16], [62, 4, 54, 2], [62, 10, 54, 8, "color"], [62, 15, 54, 13], [62, 18, 54, 16, "customColor"], [62, 29, 54, 27], [62, 33, 54, 31, "colors"], [62, 39, 54, 37], [62, 40, 54, 38, "primary"], [62, 47, 54, 45], [63, 4, 55, 2], [63, 8, 55, 6, "backgroundColor"], [63, 23, 55, 21], [64, 4, 56, 2], [64, 8, 56, 6, "textColor"], [64, 17, 56, 15], [65, 4, 57, 2], [65, 12, 57, 10, "variant"], [65, 19, 57, 17], [66, 6, 58, 4], [66, 11, 58, 9], [66, 18, 58, 16], [67, 8, 59, 6, "backgroundColor"], [67, 23, 59, 21], [67, 26, 59, 24], [67, 39, 59, 37], [68, 8, 60, 6, "textColor"], [68, 17, 60, 15], [68, 20, 60, 18, "color"], [68, 25, 60, 23], [69, 8, 61, 6], [70, 6, 62, 4], [70, 11, 62, 9], [70, 19, 62, 17], [71, 8, 63, 6, "backgroundColor"], [71, 23, 63, 21], [71, 26, 63, 24], [71, 30, 63, 24, "Color"], [71, 44, 63, 29], [71, 46, 63, 30, "color"], [71, 51, 63, 35], [71, 52, 63, 36], [71, 53, 63, 37, "fade"], [71, 57, 63, 41], [71, 58, 63, 42], [71, 62, 63, 46], [71, 63, 63, 47], [71, 64, 63, 48, "string"], [71, 70, 63, 54], [71, 71, 63, 55], [71, 72, 63, 56], [72, 8, 64, 6, "textColor"], [72, 17, 64, 15], [72, 20, 64, 18, "color"], [72, 25, 64, 23], [73, 8, 65, 6], [74, 6, 66, 4], [74, 11, 66, 9], [74, 19, 66, 17], [75, 8, 67, 6, "backgroundColor"], [75, 23, 67, 21], [75, 26, 67, 24, "color"], [75, 31, 67, 29], [76, 8, 68, 6, "textColor"], [76, 17, 68, 15], [76, 20, 68, 18], [76, 24, 68, 18, "Color"], [76, 38, 68, 23], [76, 40, 68, 24, "color"], [76, 45, 68, 29], [76, 46, 68, 30], [76, 47, 68, 31, "isDark"], [76, 53, 68, 37], [76, 54, 68, 38], [76, 55, 68, 39], [76, 58, 68, 42], [76, 65, 68, 49], [76, 68, 68, 52], [76, 72, 68, 52, "Color"], [76, 86, 68, 57], [76, 88, 68, 58, "color"], [76, 93, 68, 63], [76, 94, 68, 64], [76, 95, 68, 65, "darken"], [76, 101, 68, 71], [76, 102, 68, 72], [76, 106, 68, 76], [76, 107, 68, 77], [76, 108, 68, 78, "string"], [76, 114, 68, 84], [76, 115, 68, 85], [76, 116, 68, 86], [77, 8, 69, 6], [78, 4, 70, 2], [79, 4, 71, 2], [79, 11, 71, 9], [79, 24, 71, 22], [79, 28, 71, 22, "_jsx"], [79, 43, 71, 26], [79, 45, 71, 27, "PlatformPressable"], [79, 81, 71, 44], [79, 83, 71, 46], [80, 6, 72, 4], [80, 9, 72, 7, "rest"], [80, 13, 72, 11], [81, 6, 73, 4, "android_ripple"], [81, 20, 73, 18], [81, 22, 73, 20], [82, 8, 74, 6, "radius"], [82, 14, 74, 12], [82, 16, 74, 14, "BUTTON_RADIUS"], [82, 29, 74, 27], [83, 8, 75, 6, "color"], [83, 13, 75, 11], [83, 15, 75, 13], [83, 19, 75, 13, "Color"], [83, 33, 75, 18], [83, 35, 75, 19, "textColor"], [83, 44, 75, 28], [83, 45, 75, 29], [83, 46, 75, 30, "fade"], [83, 50, 75, 34], [83, 51, 75, 35], [83, 55, 75, 39], [83, 56, 75, 40], [83, 57, 75, 41, "string"], [83, 63, 75, 47], [83, 64, 75, 48], [83, 65, 75, 49], [84, 8, 76, 6], [84, 11, 76, 9, "android_ripple"], [85, 6, 77, 4], [85, 7, 77, 5], [86, 6, 78, 4, "pressOpacity"], [86, 18, 78, 16], [86, 20, 78, 18, "Platform"], [86, 37, 78, 26], [86, 38, 78, 27, "OS"], [86, 40, 78, 29], [86, 45, 78, 34], [86, 50, 78, 39], [86, 53, 78, 42, "undefined"], [86, 62, 78, 51], [86, 65, 78, 54], [86, 66, 78, 55], [87, 6, 79, 4, "hoverEffect"], [87, 17, 79, 15], [87, 19, 79, 17], [88, 8, 80, 6, "color"], [88, 13, 80, 11], [88, 15, 80, 13, "textColor"], [89, 6, 81, 4], [89, 7, 81, 5], [90, 6, 82, 4, "style"], [90, 11, 82, 9], [90, 13, 82, 11], [90, 14, 82, 12], [91, 8, 83, 6, "backgroundColor"], [92, 6, 84, 4], [92, 7, 84, 5], [92, 9, 84, 7, "styles"], [92, 15, 84, 13], [92, 16, 84, 14, "button"], [92, 22, 84, 20], [92, 24, 84, 22, "style"], [92, 29, 84, 27], [92, 30, 84, 28], [93, 6, 85, 4, "children"], [93, 14, 85, 12], [93, 16, 85, 14], [93, 29, 85, 27], [93, 33, 85, 27, "_jsx"], [93, 48, 85, 31], [93, 50, 85, 32, "Text"], [93, 60, 85, 36], [93, 62, 85, 38], [94, 8, 86, 6, "style"], [94, 13, 86, 11], [94, 15, 86, 13], [94, 16, 86, 14], [95, 10, 87, 8, "color"], [95, 15, 87, 13], [95, 17, 87, 15, "textColor"], [96, 8, 88, 6], [96, 9, 88, 7], [96, 11, 88, 9, "fonts"], [96, 16, 88, 14], [96, 17, 88, 15, "regular"], [96, 24, 88, 22], [96, 26, 88, 24, "styles"], [96, 32, 88, 30], [96, 33, 88, 31, "text"], [96, 37, 88, 35], [96, 38, 88, 36], [97, 8, 89, 6, "children"], [97, 16, 89, 14], [97, 18, 89, 16, "children"], [98, 6, 90, 4], [98, 7, 90, 5], [99, 4, 91, 2], [99, 5, 91, 3], [99, 6, 91, 4], [100, 2, 92, 0], [101, 2, 93, 0], [101, 8, 93, 6, "styles"], [101, 14, 93, 12], [101, 17, 93, 15, "StyleSheet"], [101, 36, 93, 25], [101, 37, 93, 26, "create"], [101, 43, 93, 32], [101, 44, 93, 33], [102, 4, 94, 2, "button"], [102, 10, 94, 8], [102, 12, 94, 10], [103, 6, 95, 4, "paddingHorizontal"], [103, 23, 95, 21], [103, 25, 95, 23], [103, 27, 95, 25], [104, 6, 96, 4, "paddingVertical"], [104, 21, 96, 19], [104, 23, 96, 21], [104, 25, 96, 23], [105, 6, 97, 4, "borderRadius"], [105, 18, 97, 16], [105, 20, 97, 18, "BUTTON_RADIUS"], [106, 4, 98, 2], [106, 5, 98, 3], [107, 4, 99, 2, "text"], [107, 8, 99, 6], [107, 10, 99, 8], [108, 6, 100, 4, "fontSize"], [108, 14, 100, 12], [108, 16, 100, 14], [108, 18, 100, 16], [109, 6, 101, 4, "lineHeight"], [109, 16, 101, 14], [109, 18, 101, 16], [109, 20, 101, 18], [110, 6, 102, 4, "letterSpacing"], [110, 19, 102, 17], [110, 21, 102, 19], [110, 24, 102, 22], [111, 6, 103, 4, "textAlign"], [111, 15, 103, 13], [111, 17, 103, 15], [112, 4, 104, 2], [113, 2, 105, 0], [113, 3, 105, 1], [113, 4, 105, 2], [114, 0, 105, 3], [114, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON>", "ButtonLink", "ButtonBase"], "mappings": "AAA;OCU;CDW;AEC;CFkB;AGC;CHkD"}}, "type": "js/module"}]}