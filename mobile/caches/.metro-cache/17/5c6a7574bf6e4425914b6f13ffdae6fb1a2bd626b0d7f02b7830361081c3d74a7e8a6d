{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationRouteContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 69, "index": 116}}], "key": "AWXnpGNA5UkH1qQUM7hLv2L9KzI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useRoute = useRoute;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationRouteContext = require(_dependencyMap[1], \"./NavigationRouteContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Hook to access the route prop of the parent screen anywhere.\n   *\n   * @returns Route prop of the parent screen.\n   */\n  function useRoute() {\n    const route = React.useContext(_NavigationRouteContext.NavigationRouteContext);\n    if (route === undefined) {\n      throw new Error(\"Couldn't find a route object. Is your component inside a screen in a navigator?\");\n    }\n    return route;\n  }\n});", "lineCount": 23, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useRoute"], [7, 18, 1, 13], [7, 21, 1, 13, "useRoute"], [7, 29, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationRouteContext"], [9, 29, 4, 0], [9, 32, 4, 0, "require"], [9, 39, 4, 0], [9, 40, 4, 0, "_dependencyMap"], [9, 54, 4, 0], [10, 2, 4, 69], [10, 11, 4, 69, "_interopRequireWildcard"], [10, 35, 4, 69, "e"], [10, 36, 4, 69], [10, 38, 4, 69, "t"], [10, 39, 4, 69], [10, 68, 4, 69, "WeakMap"], [10, 75, 4, 69], [10, 81, 4, 69, "r"], [10, 82, 4, 69], [10, 89, 4, 69, "WeakMap"], [10, 96, 4, 69], [10, 100, 4, 69, "n"], [10, 101, 4, 69], [10, 108, 4, 69, "WeakMap"], [10, 115, 4, 69], [10, 127, 4, 69, "_interopRequireWildcard"], [10, 150, 4, 69], [10, 162, 4, 69, "_interopRequireWildcard"], [10, 163, 4, 69, "e"], [10, 164, 4, 69], [10, 166, 4, 69, "t"], [10, 167, 4, 69], [10, 176, 4, 69, "t"], [10, 177, 4, 69], [10, 181, 4, 69, "e"], [10, 182, 4, 69], [10, 186, 4, 69, "e"], [10, 187, 4, 69], [10, 188, 4, 69, "__esModule"], [10, 198, 4, 69], [10, 207, 4, 69, "e"], [10, 208, 4, 69], [10, 214, 4, 69, "o"], [10, 215, 4, 69], [10, 217, 4, 69, "i"], [10, 218, 4, 69], [10, 220, 4, 69, "f"], [10, 221, 4, 69], [10, 226, 4, 69, "__proto__"], [10, 235, 4, 69], [10, 243, 4, 69, "default"], [10, 250, 4, 69], [10, 252, 4, 69, "e"], [10, 253, 4, 69], [10, 270, 4, 69, "e"], [10, 271, 4, 69], [10, 294, 4, 69, "e"], [10, 295, 4, 69], [10, 320, 4, 69, "e"], [10, 321, 4, 69], [10, 330, 4, 69, "f"], [10, 331, 4, 69], [10, 337, 4, 69, "o"], [10, 338, 4, 69], [10, 341, 4, 69, "t"], [10, 342, 4, 69], [10, 345, 4, 69, "n"], [10, 346, 4, 69], [10, 349, 4, 69, "r"], [10, 350, 4, 69], [10, 358, 4, 69, "o"], [10, 359, 4, 69], [10, 360, 4, 69, "has"], [10, 363, 4, 69], [10, 364, 4, 69, "e"], [10, 365, 4, 69], [10, 375, 4, 69, "o"], [10, 376, 4, 69], [10, 377, 4, 69, "get"], [10, 380, 4, 69], [10, 381, 4, 69, "e"], [10, 382, 4, 69], [10, 385, 4, 69, "o"], [10, 386, 4, 69], [10, 387, 4, 69, "set"], [10, 390, 4, 69], [10, 391, 4, 69, "e"], [10, 392, 4, 69], [10, 394, 4, 69, "f"], [10, 395, 4, 69], [10, 411, 4, 69, "t"], [10, 412, 4, 69], [10, 416, 4, 69, "e"], [10, 417, 4, 69], [10, 433, 4, 69, "t"], [10, 434, 4, 69], [10, 441, 4, 69, "hasOwnProperty"], [10, 455, 4, 69], [10, 456, 4, 69, "call"], [10, 460, 4, 69], [10, 461, 4, 69, "e"], [10, 462, 4, 69], [10, 464, 4, 69, "t"], [10, 465, 4, 69], [10, 472, 4, 69, "i"], [10, 473, 4, 69], [10, 477, 4, 69, "o"], [10, 478, 4, 69], [10, 481, 4, 69, "Object"], [10, 487, 4, 69], [10, 488, 4, 69, "defineProperty"], [10, 502, 4, 69], [10, 507, 4, 69, "Object"], [10, 513, 4, 69], [10, 514, 4, 69, "getOwnPropertyDescriptor"], [10, 538, 4, 69], [10, 539, 4, 69, "e"], [10, 540, 4, 69], [10, 542, 4, 69, "t"], [10, 543, 4, 69], [10, 550, 4, 69, "i"], [10, 551, 4, 69], [10, 552, 4, 69, "get"], [10, 555, 4, 69], [10, 559, 4, 69, "i"], [10, 560, 4, 69], [10, 561, 4, 69, "set"], [10, 564, 4, 69], [10, 568, 4, 69, "o"], [10, 569, 4, 69], [10, 570, 4, 69, "f"], [10, 571, 4, 69], [10, 573, 4, 69, "t"], [10, 574, 4, 69], [10, 576, 4, 69, "i"], [10, 577, 4, 69], [10, 581, 4, 69, "f"], [10, 582, 4, 69], [10, 583, 4, 69, "t"], [10, 584, 4, 69], [10, 588, 4, 69, "e"], [10, 589, 4, 69], [10, 590, 4, 69, "t"], [10, 591, 4, 69], [10, 602, 4, 69, "f"], [10, 603, 4, 69], [10, 608, 4, 69, "e"], [10, 609, 4, 69], [10, 611, 4, 69, "t"], [10, 612, 4, 69], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 2, 10, 7], [16, 11, 10, 16, "useRoute"], [16, 19, 10, 24, "useRoute"], [16, 20, 10, 24], [16, 22, 10, 27], [17, 4, 11, 2], [17, 10, 11, 8, "route"], [17, 15, 11, 13], [17, 18, 11, 16, "React"], [17, 23, 11, 21], [17, 24, 11, 22, "useContext"], [17, 34, 11, 32], [17, 35, 11, 33, "NavigationRouteContext"], [17, 81, 11, 55], [17, 82, 11, 56], [18, 4, 12, 2], [18, 8, 12, 6, "route"], [18, 13, 12, 11], [18, 18, 12, 16, "undefined"], [18, 27, 12, 25], [18, 29, 12, 27], [19, 6, 13, 4], [19, 12, 13, 10], [19, 16, 13, 14, "Error"], [19, 21, 13, 19], [19, 22, 13, 20], [19, 103, 13, 101], [19, 104, 13, 102], [20, 4, 14, 2], [21, 4, 15, 2], [21, 11, 15, 9, "route"], [21, 16, 15, 14], [22, 2, 16, 0], [23, 0, 16, 1], [23, 3]], "functionMap": {"names": ["<global>", "useRoute"], "mappings": "AAA;OCS;CDM"}}, "type": "js/module"}]}