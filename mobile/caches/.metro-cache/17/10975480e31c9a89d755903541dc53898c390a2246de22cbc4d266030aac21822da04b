{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  // We default to an empty object shim wherever we don't have an environment-specific implementation\n  /**\n   * @deprecated `NativeModulesProxy` is deprecated and might be removed in the future releases.\n   * Use `requireNativeModule` or `requireOptionalNativeModule` instead.\n   */\n  var _default = exports.default = {};\n});", "lineCount": 12, "map": [[6, 2, 3, 0], [7, 2, 5, 0], [8, 0, 6, 0], [9, 0, 7, 0], [10, 0, 8, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_default"], [11, 14, 5, 0], [11, 17, 5, 0, "exports"], [11, 24, 5, 0], [11, 25, 5, 0, "default"], [11, 32, 5, 0], [11, 35, 9, 15], [11, 36, 9, 16], [11, 37, 9, 17], [12, 0, 9, 17], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}