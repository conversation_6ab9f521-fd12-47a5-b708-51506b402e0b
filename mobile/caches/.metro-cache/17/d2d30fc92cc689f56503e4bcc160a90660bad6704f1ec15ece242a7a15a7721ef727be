{"dependencies": [{"name": "./conversions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 20, "index": 20}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "AUDPhSp3SCRpotk+n4dj5MLm8fk=", "exportNames": ["*"]}}, {"name": "./route", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 14, "index": 60}, "end": {"line": 2, "column": 32, "index": 78}}], "key": "CgLF7khkD2MDURiduSWr++dKYzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var conversions = require(_dependencyMap[0], \"./conversions\");\n  var route = require(_dependencyMap[1], \"./route\");\n  var convert = {};\n  var models = Object.keys(conversions);\n  function wrapRaw(fn) {\n    var wrappedFn = function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var arg0 = args[0];\n      if (arg0 === undefined || arg0 === null) {\n        return arg0;\n      }\n      if (arg0.length > 1) {\n        args = arg0;\n      }\n      return fn(args);\n    };\n\n    // Preserve .conversion property if there is one\n    if ('conversion' in fn) {\n      wrappedFn.conversion = fn.conversion;\n    }\n    return wrappedFn;\n  }\n  function wrapRounded(fn) {\n    var wrappedFn = function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      var arg0 = args[0];\n      if (arg0 === undefined || arg0 === null) {\n        return arg0;\n      }\n      if (arg0.length > 1) {\n        args = arg0;\n      }\n      var result = fn(args);\n\n      // We're assuming the result is an array here.\n      // see notice in conversions.js; don't use box types\n      // in conversion functions.\n      if (typeof result === 'object') {\n        for (var len = result.length, i = 0; i < len; i++) {\n          result[i] = Math.round(result[i]);\n        }\n      }\n      return result;\n    };\n\n    // Preserve .conversion property if there is one\n    if ('conversion' in fn) {\n      wrappedFn.conversion = fn.conversion;\n    }\n    return wrappedFn;\n  }\n  models.forEach(fromModel => {\n    convert[fromModel] = {};\n    Object.defineProperty(convert[fromModel], 'channels', {\n      value: conversions[fromModel].channels\n    });\n    Object.defineProperty(convert[fromModel], 'labels', {\n      value: conversions[fromModel].labels\n    });\n    var routes = route(fromModel);\n    var routeModels = Object.keys(routes);\n    routeModels.forEach(toModel => {\n      var fn = routes[toModel];\n      convert[fromModel][toModel] = wrapRounded(fn);\n      convert[fromModel][toModel].raw = wrapRaw(fn);\n    });\n  });\n  module.exports = convert;\n});", "lineCount": 75, "map": [[2, 2, 1, 0], [2, 6, 1, 6, "conversions"], [2, 17, 1, 17], [2, 20, 1, 20, "require"], [2, 27, 1, 27], [2, 28, 1, 27, "_dependencyMap"], [2, 42, 1, 27], [2, 62, 1, 43], [2, 63, 1, 44], [3, 2, 2, 0], [3, 6, 2, 6, "route"], [3, 11, 2, 11], [3, 14, 2, 14, "require"], [3, 21, 2, 21], [3, 22, 2, 21, "_dependencyMap"], [3, 36, 2, 21], [3, 50, 2, 31], [3, 51, 2, 32], [4, 2, 4, 0], [4, 6, 4, 6, "convert"], [4, 13, 4, 13], [4, 16, 4, 16], [4, 17, 4, 17], [4, 18, 4, 18], [5, 2, 6, 0], [5, 6, 6, 6, "models"], [5, 12, 6, 12], [5, 15, 6, 15, "Object"], [5, 21, 6, 21], [5, 22, 6, 22, "keys"], [5, 26, 6, 26], [5, 27, 6, 27, "conversions"], [5, 38, 6, 38], [5, 39, 6, 39], [6, 2, 8, 0], [6, 11, 8, 9, "wrapRaw"], [6, 18, 8, 16, "wrapRaw"], [6, 19, 8, 17, "fn"], [6, 21, 8, 19], [6, 23, 8, 21], [7, 4, 9, 1], [7, 8, 9, 7, "wrappedFn"], [7, 17, 9, 16], [7, 20, 9, 19], [7, 29, 9, 19, "wrappedFn"], [7, 30, 9, 19], [7, 32, 9, 38], [8, 6, 9, 38], [8, 15, 9, 38, "_len"], [8, 19, 9, 38], [8, 22, 9, 38, "arguments"], [8, 31, 9, 38], [8, 32, 9, 38, "length"], [8, 38, 9, 38], [8, 40, 9, 32, "args"], [8, 44, 9, 36], [8, 51, 9, 36, "Array"], [8, 56, 9, 36], [8, 57, 9, 36, "_len"], [8, 61, 9, 36], [8, 64, 9, 36, "_key"], [8, 68, 9, 36], [8, 74, 9, 36, "_key"], [8, 78, 9, 36], [8, 81, 9, 36, "_len"], [8, 85, 9, 36], [8, 87, 9, 36, "_key"], [8, 91, 9, 36], [9, 8, 9, 32, "args"], [9, 12, 9, 36], [9, 13, 9, 36, "_key"], [9, 17, 9, 36], [9, 21, 9, 36, "arguments"], [9, 30, 9, 36], [9, 31, 9, 36, "_key"], [9, 35, 9, 36], [10, 6, 9, 36], [11, 6, 10, 2], [11, 10, 10, 8, "arg0"], [11, 14, 10, 12], [11, 17, 10, 15, "args"], [11, 21, 10, 19], [11, 22, 10, 20], [11, 23, 10, 21], [11, 24, 10, 22], [12, 6, 11, 2], [12, 10, 11, 6, "arg0"], [12, 14, 11, 10], [12, 19, 11, 15, "undefined"], [12, 28, 11, 24], [12, 32, 11, 28, "arg0"], [12, 36, 11, 32], [12, 41, 11, 37], [12, 45, 11, 41], [12, 47, 11, 43], [13, 8, 12, 3], [13, 15, 12, 10, "arg0"], [13, 19, 12, 14], [14, 6, 13, 2], [15, 6, 15, 2], [15, 10, 15, 6, "arg0"], [15, 14, 15, 10], [15, 15, 15, 11, "length"], [15, 21, 15, 17], [15, 24, 15, 20], [15, 25, 15, 21], [15, 27, 15, 23], [16, 8, 16, 3, "args"], [16, 12, 16, 7], [16, 15, 16, 10, "arg0"], [16, 19, 16, 14], [17, 6, 17, 2], [18, 6, 19, 2], [18, 13, 19, 9, "fn"], [18, 15, 19, 11], [18, 16, 19, 12, "args"], [18, 20, 19, 16], [18, 21, 19, 17], [19, 4, 20, 1], [19, 5, 20, 2], [21, 4, 22, 1], [22, 4, 23, 1], [22, 8, 23, 5], [22, 20, 23, 17], [22, 24, 23, 21, "fn"], [22, 26, 23, 23], [22, 28, 23, 25], [23, 6, 24, 2, "wrappedFn"], [23, 15, 24, 11], [23, 16, 24, 12, "conversion"], [23, 26, 24, 22], [23, 29, 24, 25, "fn"], [23, 31, 24, 27], [23, 32, 24, 28, "conversion"], [23, 42, 24, 38], [24, 4, 25, 1], [25, 4, 27, 1], [25, 11, 27, 8, "wrappedFn"], [25, 20, 27, 17], [26, 2, 28, 0], [27, 2, 30, 0], [27, 11, 30, 9, "wrapRounded"], [27, 22, 30, 20, "wrapRounded"], [27, 23, 30, 21, "fn"], [27, 25, 30, 23], [27, 27, 30, 25], [28, 4, 31, 1], [28, 8, 31, 7, "wrappedFn"], [28, 17, 31, 16], [28, 20, 31, 19], [28, 29, 31, 19, "wrappedFn"], [28, 30, 31, 19], [28, 32, 31, 38], [29, 6, 31, 38], [29, 15, 31, 38, "_len2"], [29, 20, 31, 38], [29, 23, 31, 38, "arguments"], [29, 32, 31, 38], [29, 33, 31, 38, "length"], [29, 39, 31, 38], [29, 41, 31, 32, "args"], [29, 45, 31, 36], [29, 52, 31, 36, "Array"], [29, 57, 31, 36], [29, 58, 31, 36, "_len2"], [29, 63, 31, 36], [29, 66, 31, 36, "_key2"], [29, 71, 31, 36], [29, 77, 31, 36, "_key2"], [29, 82, 31, 36], [29, 85, 31, 36, "_len2"], [29, 90, 31, 36], [29, 92, 31, 36, "_key2"], [29, 97, 31, 36], [30, 8, 31, 32, "args"], [30, 12, 31, 36], [30, 13, 31, 36, "_key2"], [30, 18, 31, 36], [30, 22, 31, 36, "arguments"], [30, 31, 31, 36], [30, 32, 31, 36, "_key2"], [30, 37, 31, 36], [31, 6, 31, 36], [32, 6, 32, 2], [32, 10, 32, 8, "arg0"], [32, 14, 32, 12], [32, 17, 32, 15, "args"], [32, 21, 32, 19], [32, 22, 32, 20], [32, 23, 32, 21], [32, 24, 32, 22], [33, 6, 34, 2], [33, 10, 34, 6, "arg0"], [33, 14, 34, 10], [33, 19, 34, 15, "undefined"], [33, 28, 34, 24], [33, 32, 34, 28, "arg0"], [33, 36, 34, 32], [33, 41, 34, 37], [33, 45, 34, 41], [33, 47, 34, 43], [34, 8, 35, 3], [34, 15, 35, 10, "arg0"], [34, 19, 35, 14], [35, 6, 36, 2], [36, 6, 38, 2], [36, 10, 38, 6, "arg0"], [36, 14, 38, 10], [36, 15, 38, 11, "length"], [36, 21, 38, 17], [36, 24, 38, 20], [36, 25, 38, 21], [36, 27, 38, 23], [37, 8, 39, 3, "args"], [37, 12, 39, 7], [37, 15, 39, 10, "arg0"], [37, 19, 39, 14], [38, 6, 40, 2], [39, 6, 42, 2], [39, 10, 42, 8, "result"], [39, 16, 42, 14], [39, 19, 42, 17, "fn"], [39, 21, 42, 19], [39, 22, 42, 20, "args"], [39, 26, 42, 24], [39, 27, 42, 25], [41, 6, 44, 2], [42, 6, 45, 2], [43, 6, 46, 2], [44, 6, 47, 2], [44, 10, 47, 6], [44, 17, 47, 13, "result"], [44, 23, 47, 19], [44, 28, 47, 24], [44, 36, 47, 32], [44, 38, 47, 34], [45, 8, 48, 3], [45, 13, 48, 8], [45, 17, 48, 12, "len"], [45, 20, 48, 15], [45, 23, 48, 18, "result"], [45, 29, 48, 24], [45, 30, 48, 25, "length"], [45, 36, 48, 31], [45, 38, 48, 33, "i"], [45, 39, 48, 34], [45, 42, 48, 37], [45, 43, 48, 38], [45, 45, 48, 40, "i"], [45, 46, 48, 41], [45, 49, 48, 44, "len"], [45, 52, 48, 47], [45, 54, 48, 49, "i"], [45, 55, 48, 50], [45, 57, 48, 52], [45, 59, 48, 54], [46, 10, 49, 4, "result"], [46, 16, 49, 10], [46, 17, 49, 11, "i"], [46, 18, 49, 12], [46, 19, 49, 13], [46, 22, 49, 16, "Math"], [46, 26, 49, 20], [46, 27, 49, 21, "round"], [46, 32, 49, 26], [46, 33, 49, 27, "result"], [46, 39, 49, 33], [46, 40, 49, 34, "i"], [46, 41, 49, 35], [46, 42, 49, 36], [46, 43, 49, 37], [47, 8, 50, 3], [48, 6, 51, 2], [49, 6, 53, 2], [49, 13, 53, 9, "result"], [49, 19, 53, 15], [50, 4, 54, 1], [50, 5, 54, 2], [52, 4, 56, 1], [53, 4, 57, 1], [53, 8, 57, 5], [53, 20, 57, 17], [53, 24, 57, 21, "fn"], [53, 26, 57, 23], [53, 28, 57, 25], [54, 6, 58, 2, "wrappedFn"], [54, 15, 58, 11], [54, 16, 58, 12, "conversion"], [54, 26, 58, 22], [54, 29, 58, 25, "fn"], [54, 31, 58, 27], [54, 32, 58, 28, "conversion"], [54, 42, 58, 38], [55, 4, 59, 1], [56, 4, 61, 1], [56, 11, 61, 8, "wrappedFn"], [56, 20, 61, 17], [57, 2, 62, 0], [58, 2, 64, 0, "models"], [58, 8, 64, 6], [58, 9, 64, 7, "for<PERSON>ach"], [58, 16, 64, 14], [58, 17, 64, 15, "fromModel"], [58, 26, 64, 24], [58, 30, 64, 28], [59, 4, 65, 1, "convert"], [59, 11, 65, 8], [59, 12, 65, 9, "fromModel"], [59, 21, 65, 18], [59, 22, 65, 19], [59, 25, 65, 22], [59, 26, 65, 23], [59, 27, 65, 24], [60, 4, 67, 1, "Object"], [60, 10, 67, 7], [60, 11, 67, 8, "defineProperty"], [60, 25, 67, 22], [60, 26, 67, 23, "convert"], [60, 33, 67, 30], [60, 34, 67, 31, "fromModel"], [60, 43, 67, 40], [60, 44, 67, 41], [60, 46, 67, 43], [60, 56, 67, 53], [60, 58, 67, 55], [61, 6, 67, 56, "value"], [61, 11, 67, 61], [61, 13, 67, 63, "conversions"], [61, 24, 67, 74], [61, 25, 67, 75, "fromModel"], [61, 34, 67, 84], [61, 35, 67, 85], [61, 36, 67, 86, "channels"], [62, 4, 67, 94], [62, 5, 67, 95], [62, 6, 67, 96], [63, 4, 68, 1, "Object"], [63, 10, 68, 7], [63, 11, 68, 8, "defineProperty"], [63, 25, 68, 22], [63, 26, 68, 23, "convert"], [63, 33, 68, 30], [63, 34, 68, 31, "fromModel"], [63, 43, 68, 40], [63, 44, 68, 41], [63, 46, 68, 43], [63, 54, 68, 51], [63, 56, 68, 53], [64, 6, 68, 54, "value"], [64, 11, 68, 59], [64, 13, 68, 61, "conversions"], [64, 24, 68, 72], [64, 25, 68, 73, "fromModel"], [64, 34, 68, 82], [64, 35, 68, 83], [64, 36, 68, 84, "labels"], [65, 4, 68, 90], [65, 5, 68, 91], [65, 6, 68, 92], [66, 4, 70, 1], [66, 8, 70, 7, "routes"], [66, 14, 70, 13], [66, 17, 70, 16, "route"], [66, 22, 70, 21], [66, 23, 70, 22, "fromModel"], [66, 32, 70, 31], [66, 33, 70, 32], [67, 4, 71, 1], [67, 8, 71, 7, "routeModels"], [67, 19, 71, 18], [67, 22, 71, 21, "Object"], [67, 28, 71, 27], [67, 29, 71, 28, "keys"], [67, 33, 71, 32], [67, 34, 71, 33, "routes"], [67, 40, 71, 39], [67, 41, 71, 40], [68, 4, 73, 1, "routeModels"], [68, 15, 73, 12], [68, 16, 73, 13, "for<PERSON>ach"], [68, 23, 73, 20], [68, 24, 73, 21, "toModel"], [68, 31, 73, 28], [68, 35, 73, 32], [69, 6, 74, 2], [69, 10, 74, 8, "fn"], [69, 12, 74, 10], [69, 15, 74, 13, "routes"], [69, 21, 74, 19], [69, 22, 74, 20, "toModel"], [69, 29, 74, 27], [69, 30, 74, 28], [70, 6, 76, 2, "convert"], [70, 13, 76, 9], [70, 14, 76, 10, "fromModel"], [70, 23, 76, 19], [70, 24, 76, 20], [70, 25, 76, 21, "toModel"], [70, 32, 76, 28], [70, 33, 76, 29], [70, 36, 76, 32, "wrapRounded"], [70, 47, 76, 43], [70, 48, 76, 44, "fn"], [70, 50, 76, 46], [70, 51, 76, 47], [71, 6, 77, 2, "convert"], [71, 13, 77, 9], [71, 14, 77, 10, "fromModel"], [71, 23, 77, 19], [71, 24, 77, 20], [71, 25, 77, 21, "toModel"], [71, 32, 77, 28], [71, 33, 77, 29], [71, 34, 77, 30, "raw"], [71, 37, 77, 33], [71, 40, 77, 36, "wrapRaw"], [71, 47, 77, 43], [71, 48, 77, 44, "fn"], [71, 50, 77, 46], [71, 51, 77, 47], [72, 4, 78, 1], [72, 5, 78, 2], [72, 6, 78, 3], [73, 2, 79, 0], [73, 3, 79, 1], [73, 4, 79, 2], [74, 2, 81, 0, "module"], [74, 8, 81, 6], [74, 9, 81, 7, "exports"], [74, 16, 81, 14], [74, 19, 81, 17, "convert"], [74, 26, 81, 24], [75, 0, 81, 25], [75, 3]], "functionMap": {"names": ["<global>", "wrapRaw", "wrappedFn", "wrapRounded", "models.forEach$argument_0", "routeModels.forEach$argument_0"], "mappings": "AAA;ACO;mBCC;EDW;CDQ;AGE;mBDC;ECuB;CHQ;eIE;qBCS;EDK;CJC"}}, "type": "js/module"}]}