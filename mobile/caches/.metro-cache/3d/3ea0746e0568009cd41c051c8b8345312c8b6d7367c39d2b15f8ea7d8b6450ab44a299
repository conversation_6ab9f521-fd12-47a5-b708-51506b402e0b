{"dependencies": [{"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 16, "index": 160}, "end": {"line": 4, "column": 32, "index": 176}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 23, "index": 201}, "end": {"line": 5, "column": 46, "index": 224}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "../native/styles", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 17, "index": 243}, "end": {"line": 6, "column": 44, "index": 270}}], "key": "YPQs5AoYINXmzwda7M8pZxTYJWI=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 61, "index": 729}, "end": {"line": 19, "column": 102, "index": 770}}], "key": "6pHRDUl9j7DHzZ/OfZoTArvVaDg=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _objectWithoutProperties = require(_dependencyMap[0], \"@babel/runtime/helpers/objectWithoutProperties\");\n  var _excluded = [\"children\"];\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.maybeHijackSafeAreaProvider = maybeHijackSafeAreaProvider;\n  var react_1 = require(_dependencyMap[1], \"react\");\n  var react_native_1 = require(_dependencyMap[2], \"react-native\");\n  var styles_1 = require(_dependencyMap[3], \"../native/styles\");\n  var safeAreaProviderShim;\n  function maybeHijackSafeAreaProvider(type) {\n    var name = type.displayName || type.name;\n    if (react_native_1.Platform.OS !== \"web\" && name === \"SafeAreaProvider\") {\n      safeAreaProviderShim ||= shimFactory(type);\n      type = safeAreaProviderShim;\n    }\n    return type;\n  }\n  function shimFactory(type) {\n    function SafeAreaEnv(_ref) {\n      var children = _ref.children;\n      try {\n        var _require = require(_dependencyMap[4], \"react-native-safe-area-context\"),\n          useSafeAreaInsets = _require.useSafeAreaInsets,\n          SafeAreaProvider = _require.SafeAreaProvider;\n        if (type !== SafeAreaProvider) {\n          return (0, react_1.createElement)(react_1.Fragment, {}, children);\n        }\n        var insets = useSafeAreaInsets();\n        var parentVarContext = (0, react_1.useContext)(styles_1.VariableContext);\n        var parentVars = parentVarContext instanceof Map ? Object.fromEntries(parentVarContext.entries()) : parentVarContext;\n        var value = (0, react_1.useMemo)(() => ({\n          ...parentVars,\n          \"--___css-interop___safe-area-inset-bottom\": insets.bottom,\n          \"--___css-interop___safe-area-inset-left\": insets.left,\n          \"--___css-interop___safe-area-inset-right\": insets.right,\n          \"--___css-interop___safe-area-inset-top\": insets.top\n        }), [parentVarContext, insets]);\n        return (0, react_1.createElement)(styles_1.VariableContext.Provider, {\n          value\n        }, children);\n      } catch {\n        return (0, react_1.createElement)(react_1.Fragment, {}, children);\n      }\n    }\n    return function SafeAreaProviderShim(_ref2) {\n      var children = _ref2.children,\n        props = _objectWithoutProperties(_ref2, _excluded);\n      return (0, react_1.createElement)(type, props, (0, react_1.createElement)(SafeAreaEnv, {}, children));\n    };\n  }\n});", "lineCount": 55, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_objectWithoutProperties"], [4, 30, 1, 13], [4, 33, 1, 13, "require"], [4, 40, 1, 13], [4, 41, 1, 13, "_dependencyMap"], [4, 55, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_excluded"], [5, 15, 1, 13], [6, 2, 2, 0, "Object"], [6, 8, 2, 6], [6, 9, 2, 7, "defineProperty"], [6, 23, 2, 21], [6, 24, 2, 22, "exports"], [6, 31, 2, 29], [6, 33, 2, 31], [6, 45, 2, 43], [6, 47, 2, 45], [7, 4, 2, 47, "value"], [7, 9, 2, 52], [7, 11, 2, 54], [8, 2, 2, 59], [8, 3, 2, 60], [8, 4, 2, 61], [9, 2, 3, 0, "exports"], [9, 9, 3, 7], [9, 10, 3, 8, "maybeHijackSafeAreaProvider"], [9, 37, 3, 35], [9, 40, 3, 38, "maybeHijackSafeAreaProvider"], [9, 67, 3, 65], [10, 2, 4, 0], [10, 6, 4, 6, "react_1"], [10, 13, 4, 13], [10, 16, 4, 16, "require"], [10, 23, 4, 23], [10, 24, 4, 23, "_dependencyMap"], [10, 38, 4, 23], [10, 50, 4, 31], [10, 51, 4, 32], [11, 2, 5, 0], [11, 6, 5, 6, "react_native_1"], [11, 20, 5, 20], [11, 23, 5, 23, "require"], [11, 30, 5, 30], [11, 31, 5, 30, "_dependencyMap"], [11, 45, 5, 30], [11, 64, 5, 45], [11, 65, 5, 46], [12, 2, 6, 0], [12, 6, 6, 6, "styles_1"], [12, 14, 6, 14], [12, 17, 6, 17, "require"], [12, 24, 6, 24], [12, 25, 6, 24, "_dependencyMap"], [12, 39, 6, 24], [12, 62, 6, 43], [12, 63, 6, 44], [13, 2, 7, 0], [13, 6, 7, 4, "safeArea<PERSON>rovider<PERSON><PERSON>"], [13, 26, 7, 24], [14, 2, 8, 0], [14, 11, 8, 9, "maybeHijackSafeAreaProvider"], [14, 38, 8, 36, "maybeHijackSafeAreaProvider"], [14, 39, 8, 37, "type"], [14, 43, 8, 41], [14, 45, 8, 43], [15, 4, 9, 4], [15, 8, 9, 10, "name"], [15, 12, 9, 14], [15, 15, 9, 17, "type"], [15, 19, 9, 21], [15, 20, 9, 22, "displayName"], [15, 31, 9, 33], [15, 35, 9, 37, "type"], [15, 39, 9, 41], [15, 40, 9, 42, "name"], [15, 44, 9, 46], [16, 4, 10, 4], [16, 8, 10, 8, "react_native_1"], [16, 22, 10, 22], [16, 23, 10, 23, "Platform"], [16, 31, 10, 31], [16, 32, 10, 32, "OS"], [16, 34, 10, 34], [16, 39, 10, 39], [16, 44, 10, 44], [16, 48, 10, 48, "name"], [16, 52, 10, 52], [16, 57, 10, 57], [16, 75, 10, 75], [16, 77, 10, 77], [17, 6, 11, 8, "safeArea<PERSON>rovider<PERSON><PERSON>"], [17, 26, 11, 28], [17, 31, 11, 33, "shimFactory"], [17, 42, 11, 44], [17, 43, 11, 45, "type"], [17, 47, 11, 49], [17, 48, 11, 50], [18, 6, 12, 8, "type"], [18, 10, 12, 12], [18, 13, 12, 15, "safeArea<PERSON>rovider<PERSON><PERSON>"], [18, 33, 12, 35], [19, 4, 13, 4], [20, 4, 14, 4], [20, 11, 14, 11, "type"], [20, 15, 14, 15], [21, 2, 15, 0], [22, 2, 16, 0], [22, 11, 16, 9, "shimFactory"], [22, 22, 16, 20, "shimFactory"], [22, 23, 16, 21, "type"], [22, 27, 16, 25], [22, 29, 16, 27], [23, 4, 17, 4], [23, 13, 17, 13, "SafeAreaEnv"], [23, 24, 17, 24, "SafeAreaEnv"], [23, 25, 17, 24, "_ref"], [23, 29, 17, 24], [23, 31, 17, 39], [24, 6, 17, 39], [24, 10, 17, 27, "children"], [24, 18, 17, 35], [24, 21, 17, 35, "_ref"], [24, 25, 17, 35], [24, 26, 17, 27, "children"], [24, 34, 17, 35], [25, 6, 18, 8], [25, 10, 18, 12], [26, 8, 19, 12], [26, 12, 19, 12, "_require"], [26, 20, 19, 12], [26, 23, 19, 61, "require"], [26, 30, 19, 68], [26, 31, 19, 68, "_dependencyMap"], [26, 45, 19, 68], [26, 82, 19, 101], [26, 83, 19, 102], [27, 10, 19, 20, "useSafeAreaInsets"], [27, 27, 19, 37], [27, 30, 19, 37, "_require"], [27, 38, 19, 37], [27, 39, 19, 20, "useSafeAreaInsets"], [27, 56, 19, 37], [28, 10, 19, 39, "SafeAreaProvider"], [28, 26, 19, 55], [28, 29, 19, 55, "_require"], [28, 37, 19, 55], [28, 38, 19, 39, "SafeAreaProvider"], [28, 54, 19, 55], [29, 8, 20, 12], [29, 12, 20, 16, "type"], [29, 16, 20, 20], [29, 21, 20, 25, "SafeAreaProvider"], [29, 37, 20, 41], [29, 39, 20, 43], [30, 10, 21, 16], [30, 17, 21, 23], [30, 18, 21, 24], [30, 19, 21, 25], [30, 21, 21, 27, "react_1"], [30, 28, 21, 34], [30, 29, 21, 35, "createElement"], [30, 42, 21, 48], [30, 44, 21, 50, "react_1"], [30, 51, 21, 57], [30, 52, 21, 58, "Fragment"], [30, 60, 21, 66], [30, 62, 21, 68], [30, 63, 21, 69], [30, 64, 21, 70], [30, 66, 21, 72, "children"], [30, 74, 21, 80], [30, 75, 21, 81], [31, 8, 22, 12], [32, 8, 23, 12], [32, 12, 23, 18, "insets"], [32, 18, 23, 24], [32, 21, 23, 27, "useSafeAreaInsets"], [32, 38, 23, 44], [32, 39, 23, 45], [32, 40, 23, 46], [33, 8, 24, 12], [33, 12, 24, 18, "parentVarContext"], [33, 28, 24, 34], [33, 31, 24, 37], [33, 32, 24, 38], [33, 33, 24, 39], [33, 35, 24, 41, "react_1"], [33, 42, 24, 48], [33, 43, 24, 49, "useContext"], [33, 53, 24, 59], [33, 55, 24, 61, "styles_1"], [33, 63, 24, 69], [33, 64, 24, 70, "VariableContext"], [33, 79, 24, 85], [33, 80, 24, 86], [34, 8, 25, 12], [34, 12, 25, 18, "parentVars"], [34, 22, 25, 28], [34, 25, 25, 31, "parentVarContext"], [34, 41, 25, 47], [34, 53, 25, 59, "Map"], [34, 56, 25, 62], [34, 59, 26, 18, "Object"], [34, 65, 26, 24], [34, 66, 26, 25, "fromEntries"], [34, 77, 26, 36], [34, 78, 26, 37, "parentVarContext"], [34, 94, 26, 53], [34, 95, 26, 54, "entries"], [34, 102, 26, 61], [34, 103, 26, 62], [34, 104, 26, 63], [34, 105, 26, 64], [34, 108, 27, 18, "parentVarContext"], [34, 124, 27, 34], [35, 8, 28, 12], [35, 12, 28, 18, "value"], [35, 17, 28, 23], [35, 20, 28, 26], [35, 21, 28, 27], [35, 22, 28, 28], [35, 24, 28, 30, "react_1"], [35, 31, 28, 37], [35, 32, 28, 38, "useMemo"], [35, 39, 28, 45], [35, 41, 28, 47], [35, 48, 28, 54], [36, 10, 29, 16], [36, 13, 29, 19, "parentVars"], [36, 23, 29, 29], [37, 10, 30, 16], [37, 53, 30, 59], [37, 55, 30, 61, "insets"], [37, 61, 30, 67], [37, 62, 30, 68, "bottom"], [37, 68, 30, 74], [38, 10, 31, 16], [38, 51, 31, 57], [38, 53, 31, 59, "insets"], [38, 59, 31, 65], [38, 60, 31, 66, "left"], [38, 64, 31, 70], [39, 10, 32, 16], [39, 52, 32, 58], [39, 54, 32, 60, "insets"], [39, 60, 32, 66], [39, 61, 32, 67, "right"], [39, 66, 32, 72], [40, 10, 33, 16], [40, 50, 33, 56], [40, 52, 33, 58, "insets"], [40, 58, 33, 64], [40, 59, 33, 65, "top"], [41, 8, 34, 12], [41, 9, 34, 13], [41, 10, 34, 14], [41, 12, 34, 16], [41, 13, 34, 17, "parentVarContext"], [41, 29, 34, 33], [41, 31, 34, 35, "insets"], [41, 37, 34, 41], [41, 38, 34, 42], [41, 39, 34, 43], [42, 8, 35, 12], [42, 15, 35, 19], [42, 16, 35, 20], [42, 17, 35, 21], [42, 19, 35, 23, "react_1"], [42, 26, 35, 30], [42, 27, 35, 31, "createElement"], [42, 40, 35, 44], [42, 42, 35, 46, "styles_1"], [42, 50, 35, 54], [42, 51, 35, 55, "VariableContext"], [42, 66, 35, 70], [42, 67, 35, 71, "Provider"], [42, 75, 35, 79], [42, 77, 35, 81], [43, 10, 35, 83, "value"], [44, 8, 35, 89], [44, 9, 35, 90], [44, 11, 35, 92, "children"], [44, 19, 35, 100], [44, 20, 35, 101], [45, 6, 36, 8], [45, 7, 36, 9], [45, 8, 37, 8], [45, 14, 37, 14], [46, 8, 38, 12], [46, 15, 38, 19], [46, 16, 38, 20], [46, 17, 38, 21], [46, 19, 38, 23, "react_1"], [46, 26, 38, 30], [46, 27, 38, 31, "createElement"], [46, 40, 38, 44], [46, 42, 38, 46, "react_1"], [46, 49, 38, 53], [46, 50, 38, 54, "Fragment"], [46, 58, 38, 62], [46, 60, 38, 64], [46, 61, 38, 65], [46, 62, 38, 66], [46, 64, 38, 68, "children"], [46, 72, 38, 76], [46, 73, 38, 77], [47, 6, 39, 8], [48, 4, 40, 4], [49, 4, 41, 4], [49, 11, 41, 11], [49, 20, 41, 20, "SafeAreaProvider<PERSON>him"], [49, 40, 41, 40, "SafeAreaProvider<PERSON>him"], [49, 41, 41, 40, "_ref2"], [49, 46, 41, 40], [49, 48, 41, 65], [50, 6, 41, 65], [50, 10, 41, 43, "children"], [50, 18, 41, 51], [50, 21, 41, 51, "_ref2"], [50, 26, 41, 51], [50, 27, 41, 43, "children"], [50, 35, 41, 51], [51, 8, 41, 56, "props"], [51, 13, 41, 61], [51, 16, 41, 61, "_objectWithoutProperties"], [51, 40, 41, 61], [51, 41, 41, 61, "_ref2"], [51, 46, 41, 61], [51, 48, 41, 61, "_excluded"], [51, 57, 41, 61], [52, 6, 42, 8], [52, 13, 42, 15], [52, 14, 42, 16], [52, 15, 42, 17], [52, 17, 42, 19, "react_1"], [52, 24, 42, 26], [52, 25, 42, 27, "createElement"], [52, 38, 42, 40], [52, 40, 42, 42, "type"], [52, 44, 42, 46], [52, 46, 42, 48, "props"], [52, 51, 42, 53], [52, 53, 42, 55], [52, 54, 42, 56], [52, 55, 42, 57], [52, 57, 42, 59, "react_1"], [52, 64, 42, 66], [52, 65, 42, 67, "createElement"], [52, 78, 42, 80], [52, 80, 42, 82, "SafeAreaEnv"], [52, 91, 42, 93], [52, 93, 42, 95], [52, 94, 42, 96], [52, 95, 42, 97], [52, 97, 42, 99, "children"], [52, 105, 42, 107], [52, 106, 42, 108], [52, 107, 42, 109], [53, 4, 43, 4], [53, 5, 43, 5], [54, 2, 44, 0], [55, 0, 44, 1], [55, 3]], "functionMap": {"names": ["<global>", "maybeHijackSafeAreaProvider", "shimFactory", "SafeAreaEnv", "<anonymous>", "SafeAreaProvider<PERSON>him"], "mappings": "AAA;ACO;CDO;AEC;ICC;+CCW;cDM;KDM;WGC;KHE;CFC"}}, "type": "js/module"}]}