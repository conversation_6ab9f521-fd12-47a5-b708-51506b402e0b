{"dependencies": [{"name": "react-native-url-polyfill/auto", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "FjDCbK7LcT49IdUkzoo8Fm4C4ig=", "exportNames": ["*"]}}, {"name": "./src/__create/polyfills", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 41}, "end": {"line": 2, "column": 34, "index": 75}}], "key": "Yc7nm55l9s3if1fDfadIOT7q6/s=", "exportNames": ["*"]}}, {"name": "@expo/metro-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 118}, "end": {"line": 4, "column": 29, "index": 147}}], "key": "IJjWK/LtC41RhjPhesvGeXROaKo=", "exportNames": ["*"]}}, {"name": "./global.css", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 148}, "end": {"line": 5, "column": 22, "index": 170}}], "key": "hZQCJzeYeEigMt/Uj47kMFmD41E=", "exportNames": ["*"]}}, {"name": "expo-router/build/qualified-entry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 171}, "end": {"line": 6, "column": 56, "index": 227}}], "key": "Qyb7Tupy95xd9lTJGU8vqaaMFGk=", "exportNames": ["*"]}}, {"name": "expo-router/build/renderRootComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 228}, "end": {"line": 7, "column": 76, "index": 304}}], "key": "Hei9Ii1pD7Y7Fj76NYfgJDq1dCQ=", "exportNames": ["*"]}}, {"name": "buffer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 16, "index": 92}, "end": {"line": 3, "column": 33, "index": 109}}], "key": "kYC7RadcB7k9ZEYd5zOmBbv1F14=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  require(_dependencyMap[0], \"react-native-url-polyfill/auto\");\n  require(_dependencyMap[1], \"./src/__create/polyfills\");\n  require(_dependencyMap[2], \"@expo/metro-runtime\");\n  require(_dependencyMap[3], \"./global.css\");\n  var _qualifiedEntry = require(_dependencyMap[4], \"expo-router/build/qualified-entry\");\n  var _renderRootComponent = require(_dependencyMap[5], \"expo-router/build/renderRootComponent\");\n  global.Buffer = require(_dependencyMap[6], \"buffer\").Buffer;\n  (0, _renderRootComponent.renderRootComponent)(_qualifiedEntry.App);\n});", "lineCount": 10, "map": [[2, 2, 1, 0, "require"], [2, 9, 1, 0], [2, 10, 1, 0, "_dependencyMap"], [2, 24, 1, 0], [3, 2, 2, 0, "require"], [3, 9, 2, 0], [3, 10, 2, 0, "_dependencyMap"], [3, 24, 2, 0], [4, 2, 4, 0, "require"], [4, 9, 4, 0], [4, 10, 4, 0, "_dependencyMap"], [4, 24, 4, 0], [5, 2, 5, 0, "require"], [5, 9, 5, 0], [5, 10, 5, 0, "_dependencyMap"], [5, 24, 5, 0], [6, 2, 6, 0], [6, 6, 6, 0, "_qualifiedEntry"], [6, 21, 6, 0], [6, 24, 6, 0, "require"], [6, 31, 6, 0], [6, 32, 6, 0, "_dependencyMap"], [6, 46, 6, 0], [7, 2, 7, 0], [7, 6, 7, 0, "_renderRootComponent"], [7, 26, 7, 0], [7, 29, 7, 0, "require"], [7, 36, 7, 0], [7, 37, 7, 0, "_dependencyMap"], [7, 51, 7, 0], [8, 2, 3, 0, "global"], [8, 8, 3, 6], [8, 9, 3, 7, "<PERSON><PERSON><PERSON>"], [8, 15, 3, 13], [8, 18, 3, 16, "require"], [8, 25, 3, 23], [8, 26, 3, 23, "_dependencyMap"], [8, 40, 3, 23], [8, 53, 3, 32], [8, 54, 3, 33], [8, 55, 3, 34, "<PERSON><PERSON><PERSON>"], [8, 61, 3, 40], [9, 2, 9, 0], [9, 6, 9, 0, "renderRootComponent"], [9, 46, 9, 19], [9, 48, 9, 20, "App"], [9, 67, 9, 23], [9, 68, 9, 24], [10, 0, 9, 25], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}