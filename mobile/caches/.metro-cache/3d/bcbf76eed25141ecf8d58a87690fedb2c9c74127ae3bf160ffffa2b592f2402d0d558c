{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.valueSetter = void 0;\n  const _worklet_8092936786998_init_data = {\n    code: \"function valueSetter_reactNativeReanimated_valueSetterJs1(mutable,value,forceUpdate=false){const previousAnimation=mutable._animation;if(previousAnimation){previousAnimation.cancelled=true;mutable._animation=null;}if(typeof value==='function'||value!==null&&typeof value==='object'&&value.onFrame!==undefined){const animation=typeof value==='function'?value():value;if(mutable._value===animation.current&&!animation.isHigherOrder&&!forceUpdate){animation.callback&&animation.callback(true);return;}const initializeAnimation=function(timestamp){animation.onStart(animation,mutable.value,timestamp,previousAnimation);};const currentTimestamp=global.__frameTimestamp||global._getAnimationTimestamp();initializeAnimation(currentTimestamp);const step=function(newTimestamp){const timestamp=newTimestamp<(animation.timestamp||0)?animation.timestamp:newTimestamp;if(animation.cancelled){animation.callback&&animation.callback(false);return;}const finished=animation.onFrame(animation,timestamp);animation.finished=true;animation.timestamp=timestamp;mutable._value=animation.current;if(finished){animation.callback&&animation.callback(true);}else{requestAnimationFrame(step);}};mutable._animation=animation;step(currentTimestamp);}else{if(mutable._value===value&&!forceUpdate){return;}mutable._value=value;}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/valueSetter.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"valueSetter_reactNativeReanimated_valueSetterJs1\\\",\\\"mutable\\\",\\\"value\\\",\\\"forceUpdate\\\",\\\"previousAnimation\\\",\\\"_animation\\\",\\\"cancelled\\\",\\\"onFrame\\\",\\\"undefined\\\",\\\"animation\\\",\\\"_value\\\",\\\"current\\\",\\\"isHigherOrder\\\",\\\"callback\\\",\\\"initializeAnimation\\\",\\\"timestamp\\\",\\\"onStart\\\",\\\"currentTimestamp\\\",\\\"global\\\",\\\"__frameTimestamp\\\",\\\"_getAnimationTimestamp\\\",\\\"step\\\",\\\"newTimestamp\\\",\\\"finished\\\",\\\"requestAnimationFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/valueSetter.js\\\"],\\\"mappings\\\":\\\"AAEO,SAAAA,gDAAwDA,CAAAC,OAAE,CAAAC,KAAA,CAAAC,WAAA,QAG/D,KAAM,CAAAC,iBAAiB,CAAGH,OAAO,CAACI,UAAU,CAC5C,GAAID,iBAAiB,CAAE,CACrBA,iBAAiB,CAACE,SAAS,CAAG,IAAI,CAClCL,OAAO,CAACI,UAAU,CAAG,IAAI,CAC3B,CACA,GAAI,MAAO,CAAAH,KAAK,GAAK,UAAU,EAAIA,KAAK,GAAK,IAAI,EAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAE9EA,KAAK,CAACK,OAAO,GAAKC,SAAS,CAAE,CAC3B,KAAM,CAAAC,SAAS,CAAG,MAAO,CAAAP,KAAK,GAAK,UAAU,CAE7CA,KAAK,CAAC,CAAC,CAEPA,KAAK,CAKL,GAAID,OAAO,CAACS,MAAM,GAAKD,SAAS,CAACE,OAAO,EAAI,CAACF,SAAS,CAACG,aAAa,EAAI,CAACT,WAAW,CAAE,CACpFM,SAAS,CAACI,QAAQ,EAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAI,CAAC,CAC9C,OACF,CAEA,KAAM,CAAAC,mBAAmB,CAAG,QAAAA,CAAAC,SAAS,CAAI,CACvCN,SAAS,CAACO,OAAO,CAACP,SAAS,CAAER,OAAO,CAACC,KAAK,CAAEa,SAAS,CAAEX,iBAAiB,CAAC,CAC3E,CAAC,CACD,KAAM,CAAAa,gBAAgB,CAAGC,MAAM,CAACC,gBAAgB,EAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC,CACnFN,mBAAmB,CAACG,gBAAgB,CAAC,CACrC,KAAM,CAAAI,IAAI,CAAG,QAAAA,CAAAC,YAAY,CAAI,CAK3B,KAAM,CAAAP,SAAS,CAAGO,YAAY,EAAIb,SAAS,CAACM,SAAS,EAAI,CAAC,CAAC,CAAGN,SAAS,CAACM,SAAS,CAAGO,YAAY,CAChG,GAAIb,SAAS,CAACH,SAAS,CAAE,CACvBG,SAAS,CAACI,QAAQ,EAAIJ,SAAS,CAACI,QAAQ,CAAC,KAAoB,CAAC,CAC9D,OACF,CACA,KAAM,CAAAU,QAAQ,CAAGd,SAAS,CAACF,OAAO,CAACE,SAAS,CAAEM,SAAS,CAAC,CACxDN,SAAS,CAACc,QAAQ,CAAG,IAAI,CACzBd,SAAS,CAACM,SAAS,CAAGA,SAAS,CAI/Bd,OAAO,CAACS,MAAM,CAAGD,SAAS,CAACE,OAAO,CAClC,GAAIY,QAAQ,CAAE,CACZd,SAAS,CAACI,QAAQ,EAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAmB,CAAC,CAC/D,CAAC,IAAM,CACLW,qBAAqB,CAACH,IAAI,CAAC,CAC7B,CACF,CAAC,CACDpB,OAAO,CAACI,UAAU,CAAGI,SAAS,CAC9BY,IAAI,CAACJ,gBAAgB,CAAC,CACxB,CAAC,IAAM,CAGL,GAAIhB,OAAO,CAACS,MAAM,GAAKR,KAAK,EAAI,CAACC,WAAW,CAAE,CAC5C,OACF,CACAF,OAAO,CAACS,MAAM,CAAGR,KAAK,CACxB,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const valueSetter = exports.valueSetter = function () {\n    const _e = [new global.Error(), 1, -27];\n    const valueSetter = function (mutable, value, forceUpdate = false) {\n      const previousAnimation = mutable._animation;\n      if (previousAnimation) {\n        previousAnimation.cancelled = true;\n        mutable._animation = null;\n      }\n      if (typeof value === 'function' || value !== null && typeof value === 'object' &&\n      // TODO TYPESCRIPT fix this after fixing AnimationObject type\n      value.onFrame !== undefined) {\n        const animation = typeof value === 'function' ?\n        // TODO TYPESCRIPT fix this after fixing AnimationObject type\n        value() :\n        // TODO TYPESCRIPT fix this after fixing AnimationObject type\n        value;\n        // prevent setting again to the same value\n        // and triggering the mappers that treat this value as an input\n        // this happens when the animation's target value(stored in animation.current until animation.onStart is called) is set to the same value as a current one(this._value)\n        // built in animations that are not higher order(withTiming, withSpring) hold target value in .current\n        if (mutable._value === animation.current && !animation.isHigherOrder && !forceUpdate) {\n          animation.callback && animation.callback(true);\n          return;\n        }\n        // animated set\n        const initializeAnimation = timestamp => {\n          animation.onStart(animation, mutable.value, timestamp, previousAnimation);\n        };\n        const currentTimestamp = global.__frameTimestamp || global._getAnimationTimestamp();\n        initializeAnimation(currentTimestamp);\n        const step = newTimestamp => {\n          // Function `requestAnimationFrame` adds callback to an array, all the callbacks are flushed with function `__flushAnimationFrame`\n          // Usually we flush them inside function `nativeRequestAnimationFrame` and then the given timestamp is the timestamp of end of the current frame.\n          // However function `__flushAnimationFrame` may also be called inside `registerEventHandler` - then we get actual timestamp which is earlier than the end of the frame.\n\n          const timestamp = newTimestamp < (animation.timestamp || 0) ? animation.timestamp : newTimestamp;\n          if (animation.cancelled) {\n            animation.callback && animation.callback(false /* finished */);\n            return;\n          }\n          const finished = animation.onFrame(animation, timestamp);\n          animation.finished = true;\n          animation.timestamp = timestamp;\n          // TODO TYPESCRIPT\n          // For now I'll assume that `animation.current` is always defined\n          // but actually need to dive into animations to understand it\n          mutable._value = animation.current;\n          if (finished) {\n            animation.callback && animation.callback(true /* finished */);\n          } else {\n            requestAnimationFrame(step);\n          }\n        };\n        mutable._animation = animation;\n        step(currentTimestamp);\n      } else {\n        // prevent setting again to the same value\n        // and triggering the mappers that treat this value as an input\n        if (mutable._value === value && !forceUpdate) {\n          return;\n        }\n        mutable._value = value;\n      }\n    };\n    valueSetter.__closure = {};\n    valueSetter.__workletHash = 8092936786998;\n    valueSetter.__initData = _worklet_8092936786998_init_data;\n    valueSetter.__stackDetails = _e;\n    return valueSetter;\n  }();\n});", "lineCount": 84, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "valueSetter"], [7, 21, 1, 13], [8, 2, 1, 13], [8, 8, 1, 13, "_worklet_8092936786998_init_data"], [8, 40, 1, 13], [9, 4, 1, 13, "code"], [9, 8, 1, 13], [10, 4, 1, 13, "location"], [10, 12, 1, 13], [11, 4, 1, 13, "sourceMap"], [11, 13, 1, 13], [12, 4, 1, 13, "version"], [12, 11, 1, 13], [13, 2, 1, 13], [14, 2, 1, 13], [14, 8, 1, 13, "valueSetter"], [14, 19, 1, 13], [14, 22, 1, 13, "exports"], [14, 29, 1, 13], [14, 30, 1, 13, "valueSetter"], [14, 41, 1, 13], [14, 44, 3, 7], [15, 4, 3, 7], [15, 10, 3, 7, "_e"], [15, 12, 3, 7], [15, 20, 3, 7, "global"], [15, 26, 3, 7], [15, 27, 3, 7, "Error"], [15, 32, 3, 7], [16, 4, 3, 7], [16, 10, 3, 7, "valueSetter"], [16, 21, 3, 7], [16, 33, 3, 7, "valueSetter"], [16, 34, 3, 28, "mutable"], [16, 41, 3, 35], [16, 43, 3, 37, "value"], [16, 48, 3, 42], [16, 50, 3, 44, "forceUpdate"], [16, 61, 3, 55], [16, 64, 3, 58], [16, 69, 3, 63], [16, 71, 3, 65], [17, 6, 6, 2], [17, 12, 6, 8, "previousAnimation"], [17, 29, 6, 25], [17, 32, 6, 28, "mutable"], [17, 39, 6, 35], [17, 40, 6, 36, "_animation"], [17, 50, 6, 46], [18, 6, 7, 2], [18, 10, 7, 6, "previousAnimation"], [18, 27, 7, 23], [18, 29, 7, 25], [19, 8, 8, 4, "previousAnimation"], [19, 25, 8, 21], [19, 26, 8, 22, "cancelled"], [19, 35, 8, 31], [19, 38, 8, 34], [19, 42, 8, 38], [20, 8, 9, 4, "mutable"], [20, 15, 9, 11], [20, 16, 9, 12, "_animation"], [20, 26, 9, 22], [20, 29, 9, 25], [20, 33, 9, 29], [21, 6, 10, 2], [22, 6, 11, 2], [22, 10, 11, 6], [22, 17, 11, 13, "value"], [22, 22, 11, 18], [22, 27, 11, 23], [22, 37, 11, 33], [22, 41, 11, 37, "value"], [22, 46, 11, 42], [22, 51, 11, 47], [22, 55, 11, 51], [22, 59, 11, 55], [22, 66, 11, 62, "value"], [22, 71, 11, 67], [22, 76, 11, 72], [22, 84, 11, 80], [23, 6, 12, 2], [24, 6, 13, 2, "value"], [24, 11, 13, 7], [24, 12, 13, 8, "onFrame"], [24, 19, 13, 15], [24, 24, 13, 20, "undefined"], [24, 33, 13, 29], [24, 35, 13, 31], [25, 8, 14, 4], [25, 14, 14, 10, "animation"], [25, 23, 14, 19], [25, 26, 14, 22], [25, 33, 14, 29, "value"], [25, 38, 14, 34], [25, 43, 14, 39], [25, 53, 14, 49], [26, 8, 15, 4], [27, 8, 16, 4, "value"], [27, 13, 16, 9], [27, 14, 16, 10], [27, 15, 16, 11], [28, 8, 17, 4], [29, 8, 18, 4, "value"], [29, 13, 18, 9], [30, 8, 19, 4], [31, 8, 20, 4], [32, 8, 21, 4], [33, 8, 22, 4], [34, 8, 23, 4], [34, 12, 23, 8, "mutable"], [34, 19, 23, 15], [34, 20, 23, 16, "_value"], [34, 26, 23, 22], [34, 31, 23, 27, "animation"], [34, 40, 23, 36], [34, 41, 23, 37, "current"], [34, 48, 23, 44], [34, 52, 23, 48], [34, 53, 23, 49, "animation"], [34, 62, 23, 58], [34, 63, 23, 59, "isHigherOrder"], [34, 76, 23, 72], [34, 80, 23, 76], [34, 81, 23, 77, "forceUpdate"], [34, 92, 23, 88], [34, 94, 23, 90], [35, 10, 24, 6, "animation"], [35, 19, 24, 15], [35, 20, 24, 16, "callback"], [35, 28, 24, 24], [35, 32, 24, 28, "animation"], [35, 41, 24, 37], [35, 42, 24, 38, "callback"], [35, 50, 24, 46], [35, 51, 24, 47], [35, 55, 24, 51], [35, 56, 24, 52], [36, 10, 25, 6], [37, 8, 26, 4], [38, 8, 27, 4], [39, 8, 28, 4], [39, 14, 28, 10, "initializeAnimation"], [39, 33, 28, 29], [39, 36, 28, 32, "timestamp"], [39, 45, 28, 41], [39, 49, 28, 45], [40, 10, 29, 6, "animation"], [40, 19, 29, 15], [40, 20, 29, 16, "onStart"], [40, 27, 29, 23], [40, 28, 29, 24, "animation"], [40, 37, 29, 33], [40, 39, 29, 35, "mutable"], [40, 46, 29, 42], [40, 47, 29, 43, "value"], [40, 52, 29, 48], [40, 54, 29, 50, "timestamp"], [40, 63, 29, 59], [40, 65, 29, 61, "previousAnimation"], [40, 82, 29, 78], [40, 83, 29, 79], [41, 8, 30, 4], [41, 9, 30, 5], [42, 8, 31, 4], [42, 14, 31, 10, "currentTimestamp"], [42, 30, 31, 26], [42, 33, 31, 29, "global"], [42, 39, 31, 35], [42, 40, 31, 36, "__frameTimestamp"], [42, 56, 31, 52], [42, 60, 31, 56, "global"], [42, 66, 31, 62], [42, 67, 31, 63, "_getAnimationTimestamp"], [42, 89, 31, 85], [42, 90, 31, 86], [42, 91, 31, 87], [43, 8, 32, 4, "initializeAnimation"], [43, 27, 32, 23], [43, 28, 32, 24, "currentTimestamp"], [43, 44, 32, 40], [43, 45, 32, 41], [44, 8, 33, 4], [44, 14, 33, 10, "step"], [44, 18, 33, 14], [44, 21, 33, 17, "newTimestamp"], [44, 33, 33, 29], [44, 37, 33, 33], [45, 10, 34, 6], [46, 10, 35, 6], [47, 10, 36, 6], [49, 10, 38, 6], [49, 16, 38, 12, "timestamp"], [49, 25, 38, 21], [49, 28, 38, 24, "newTimestamp"], [49, 40, 38, 36], [49, 44, 38, 40, "animation"], [49, 53, 38, 49], [49, 54, 38, 50, "timestamp"], [49, 63, 38, 59], [49, 67, 38, 63], [49, 68, 38, 64], [49, 69, 38, 65], [49, 72, 38, 68, "animation"], [49, 81, 38, 77], [49, 82, 38, 78, "timestamp"], [49, 91, 38, 87], [49, 94, 38, 90, "newTimestamp"], [49, 106, 38, 102], [50, 10, 39, 6], [50, 14, 39, 10, "animation"], [50, 23, 39, 19], [50, 24, 39, 20, "cancelled"], [50, 33, 39, 29], [50, 35, 39, 31], [51, 12, 40, 8, "animation"], [51, 21, 40, 17], [51, 22, 40, 18, "callback"], [51, 30, 40, 26], [51, 34, 40, 30, "animation"], [51, 43, 40, 39], [51, 44, 40, 40, "callback"], [51, 52, 40, 48], [51, 53, 40, 49], [51, 58, 40, 54], [51, 59, 40, 55], [51, 73, 40, 69], [51, 74, 40, 70], [52, 12, 41, 8], [53, 10, 42, 6], [54, 10, 43, 6], [54, 16, 43, 12, "finished"], [54, 24, 43, 20], [54, 27, 43, 23, "animation"], [54, 36, 43, 32], [54, 37, 43, 33, "onFrame"], [54, 44, 43, 40], [54, 45, 43, 41, "animation"], [54, 54, 43, 50], [54, 56, 43, 52, "timestamp"], [54, 65, 43, 61], [54, 66, 43, 62], [55, 10, 44, 6, "animation"], [55, 19, 44, 15], [55, 20, 44, 16, "finished"], [55, 28, 44, 24], [55, 31, 44, 27], [55, 35, 44, 31], [56, 10, 45, 6, "animation"], [56, 19, 45, 15], [56, 20, 45, 16, "timestamp"], [56, 29, 45, 25], [56, 32, 45, 28, "timestamp"], [56, 41, 45, 37], [57, 10, 46, 6], [58, 10, 47, 6], [59, 10, 48, 6], [60, 10, 49, 6, "mutable"], [60, 17, 49, 13], [60, 18, 49, 14, "_value"], [60, 24, 49, 20], [60, 27, 49, 23, "animation"], [60, 36, 49, 32], [60, 37, 49, 33, "current"], [60, 44, 49, 40], [61, 10, 50, 6], [61, 14, 50, 10, "finished"], [61, 22, 50, 18], [61, 24, 50, 20], [62, 12, 51, 8, "animation"], [62, 21, 51, 17], [62, 22, 51, 18, "callback"], [62, 30, 51, 26], [62, 34, 51, 30, "animation"], [62, 43, 51, 39], [62, 44, 51, 40, "callback"], [62, 52, 51, 48], [62, 53, 51, 49], [62, 57, 51, 53], [62, 58, 51, 54], [62, 72, 51, 68], [62, 73, 51, 69], [63, 10, 52, 6], [63, 11, 52, 7], [63, 17, 52, 13], [64, 12, 53, 8, "requestAnimationFrame"], [64, 33, 53, 29], [64, 34, 53, 30, "step"], [64, 38, 53, 34], [64, 39, 53, 35], [65, 10, 54, 6], [66, 8, 55, 4], [66, 9, 55, 5], [67, 8, 56, 4, "mutable"], [67, 15, 56, 11], [67, 16, 56, 12, "_animation"], [67, 26, 56, 22], [67, 29, 56, 25, "animation"], [67, 38, 56, 34], [68, 8, 57, 4, "step"], [68, 12, 57, 8], [68, 13, 57, 9, "currentTimestamp"], [68, 29, 57, 25], [68, 30, 57, 26], [69, 6, 58, 2], [69, 7, 58, 3], [69, 13, 58, 9], [70, 8, 59, 4], [71, 8, 60, 4], [72, 8, 61, 4], [72, 12, 61, 8, "mutable"], [72, 19, 61, 15], [72, 20, 61, 16, "_value"], [72, 26, 61, 22], [72, 31, 61, 27, "value"], [72, 36, 61, 32], [72, 40, 61, 36], [72, 41, 61, 37, "forceUpdate"], [72, 52, 61, 48], [72, 54, 61, 50], [73, 10, 62, 6], [74, 8, 63, 4], [75, 8, 64, 4, "mutable"], [75, 15, 64, 11], [75, 16, 64, 12, "_value"], [75, 22, 64, 18], [75, 25, 64, 21, "value"], [75, 30, 64, 26], [76, 6, 65, 2], [77, 4, 66, 0], [77, 5, 66, 1], [78, 4, 66, 1, "valueSetter"], [78, 15, 66, 1], [78, 16, 66, 1, "__closure"], [78, 25, 66, 1], [79, 4, 66, 1, "valueSetter"], [79, 15, 66, 1], [79, 16, 66, 1, "__workletHash"], [79, 29, 66, 1], [80, 4, 66, 1, "valueSetter"], [80, 15, 66, 1], [80, 16, 66, 1, "__initData"], [80, 26, 66, 1], [80, 29, 66, 1, "_worklet_8092936786998_init_data"], [80, 61, 66, 1], [81, 4, 66, 1, "valueSetter"], [81, 15, 66, 1], [81, 16, 66, 1, "__stackDetails"], [81, 30, 66, 1], [81, 33, 66, 1, "_e"], [81, 35, 66, 1], [82, 4, 66, 1], [82, 11, 66, 1, "valueSetter"], [82, 22, 66, 1], [83, 2, 66, 1], [83, 3, 3, 7], [84, 0, 3, 7], [84, 3]], "functionMap": {"names": ["<global>", "valueSetter", "initializeAnimation", "step"], "mappings": "AAA;OCE;gCCyB;KDE;iBEG;KFsB;CDW"}}, "type": "js/module"}]}