{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../Components/Touchable/TouchableWithoutFeedback", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 91}}], "key": "Lz+ZckcQtsvvpJWm+sNzQkI16Rs=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _TouchableWithoutFeedback = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/Touchable/TouchableWithoutFeedback\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../../StyleSheet/StyleSheet\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[5], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[7], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/UI/LogBoxButton.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxButton(props) {\n    var _React$useState = React.useState(false),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n      pressed = _React$useState2[0],\n      setPressed = _React$useState2[1];\n    var backgroundColor = props.backgroundColor;\n    if (!backgroundColor) {\n      backgroundColor = {\n        default: LogBoxStyle.getBackgroundColor(0.95),\n        pressed: LogBoxStyle.getBackgroundColor(0.6)\n      };\n    }\n    var content = (0, _jsxRuntime.jsx)(_View.default, {\n      id: props.id,\n      style: _StyleSheet.default.compose({\n        backgroundColor: pressed ? backgroundColor.pressed : backgroundColor.default\n      }, props.style),\n      children: props.children\n    });\n    return props.onPress == null ? content : (0, _jsxRuntime.jsx)(_TouchableWithoutFeedback.default, {\n      hitSlop: props.hitSlop,\n      onPress: props.onPress,\n      onPressIn: () => setPressed(true),\n      onPressOut: () => setPressed(false),\n      children: content\n    });\n  }\n  var _default = exports.default = LogBoxButton;\n});", "lineCount": 44, "map": [[8, 2, 15, 0], [8, 6, 15, 0, "_TouchableWithoutFeedback"], [8, 31, 15, 0], [8, 34, 15, 0, "_interopRequireDefault"], [8, 56, 15, 0], [8, 57, 15, 0, "require"], [8, 64, 15, 0], [8, 65, 15, 0, "_dependencyMap"], [8, 79, 15, 0], [9, 2, 16, 0], [9, 6, 16, 0, "_View"], [9, 11, 16, 0], [9, 14, 16, 0, "_interopRequireDefault"], [9, 36, 16, 0], [9, 37, 16, 0, "require"], [9, 44, 16, 0], [9, 45, 16, 0, "_dependencyMap"], [9, 59, 16, 0], [10, 2, 17, 0], [10, 6, 17, 0, "_StyleSheet"], [10, 17, 17, 0], [10, 20, 17, 0, "_interopRequireDefault"], [10, 42, 17, 0], [10, 43, 17, 0, "require"], [10, 50, 17, 0], [10, 51, 17, 0, "_dependencyMap"], [10, 65, 17, 0], [11, 2, 18, 0], [11, 6, 18, 0, "LogBoxStyle"], [11, 17, 18, 0], [11, 20, 18, 0, "_interopRequireWildcard"], [11, 43, 18, 0], [11, 44, 18, 0, "require"], [11, 51, 18, 0], [11, 52, 18, 0, "_dependencyMap"], [11, 66, 18, 0], [12, 2, 19, 0], [12, 6, 19, 0, "React"], [12, 11, 19, 0], [12, 14, 19, 0, "_interopRequireWildcard"], [12, 37, 19, 0], [12, 38, 19, 0, "require"], [12, 45, 19, 0], [12, 46, 19, 0, "_dependencyMap"], [12, 60, 19, 0], [13, 2, 19, 31], [13, 6, 19, 31, "_jsxRuntime"], [13, 17, 19, 31], [13, 20, 19, 31, "require"], [13, 27, 19, 31], [13, 28, 19, 31, "_dependencyMap"], [13, 42, 19, 31], [14, 2, 19, 31], [14, 6, 19, 31, "_jsxFileName"], [14, 18, 19, 31], [15, 2, 19, 31], [15, 11, 19, 31, "_interopRequireWildcard"], [15, 35, 19, 31, "e"], [15, 36, 19, 31], [15, 38, 19, 31, "t"], [15, 39, 19, 31], [15, 68, 19, 31, "WeakMap"], [15, 75, 19, 31], [15, 81, 19, 31, "r"], [15, 82, 19, 31], [15, 89, 19, 31, "WeakMap"], [15, 96, 19, 31], [15, 100, 19, 31, "n"], [15, 101, 19, 31], [15, 108, 19, 31, "WeakMap"], [15, 115, 19, 31], [15, 127, 19, 31, "_interopRequireWildcard"], [15, 150, 19, 31], [15, 162, 19, 31, "_interopRequireWildcard"], [15, 163, 19, 31, "e"], [15, 164, 19, 31], [15, 166, 19, 31, "t"], [15, 167, 19, 31], [15, 176, 19, 31, "t"], [15, 177, 19, 31], [15, 181, 19, 31, "e"], [15, 182, 19, 31], [15, 186, 19, 31, "e"], [15, 187, 19, 31], [15, 188, 19, 31, "__esModule"], [15, 198, 19, 31], [15, 207, 19, 31, "e"], [15, 208, 19, 31], [15, 214, 19, 31, "o"], [15, 215, 19, 31], [15, 217, 19, 31, "i"], [15, 218, 19, 31], [15, 220, 19, 31, "f"], [15, 221, 19, 31], [15, 226, 19, 31, "__proto__"], [15, 235, 19, 31], [15, 243, 19, 31, "default"], [15, 250, 19, 31], [15, 252, 19, 31, "e"], [15, 253, 19, 31], [15, 270, 19, 31, "e"], [15, 271, 19, 31], [15, 294, 19, 31, "e"], [15, 295, 19, 31], [15, 320, 19, 31, "e"], [15, 321, 19, 31], [15, 330, 19, 31, "f"], [15, 331, 19, 31], [15, 337, 19, 31, "o"], [15, 338, 19, 31], [15, 341, 19, 31, "t"], [15, 342, 19, 31], [15, 345, 19, 31, "n"], [15, 346, 19, 31], [15, 349, 19, 31, "r"], [15, 350, 19, 31], [15, 358, 19, 31, "o"], [15, 359, 19, 31], [15, 360, 19, 31, "has"], [15, 363, 19, 31], [15, 364, 19, 31, "e"], [15, 365, 19, 31], [15, 375, 19, 31, "o"], [15, 376, 19, 31], [15, 377, 19, 31, "get"], [15, 380, 19, 31], [15, 381, 19, 31, "e"], [15, 382, 19, 31], [15, 385, 19, 31, "o"], [15, 386, 19, 31], [15, 387, 19, 31, "set"], [15, 390, 19, 31], [15, 391, 19, 31, "e"], [15, 392, 19, 31], [15, 394, 19, 31, "f"], [15, 395, 19, 31], [15, 409, 19, 31, "_t"], [15, 411, 19, 31], [15, 415, 19, 31, "e"], [15, 416, 19, 31], [15, 432, 19, 31, "_t"], [15, 434, 19, 31], [15, 441, 19, 31, "hasOwnProperty"], [15, 455, 19, 31], [15, 456, 19, 31, "call"], [15, 460, 19, 31], [15, 461, 19, 31, "e"], [15, 462, 19, 31], [15, 464, 19, 31, "_t"], [15, 466, 19, 31], [15, 473, 19, 31, "i"], [15, 474, 19, 31], [15, 478, 19, 31, "o"], [15, 479, 19, 31], [15, 482, 19, 31, "Object"], [15, 488, 19, 31], [15, 489, 19, 31, "defineProperty"], [15, 503, 19, 31], [15, 508, 19, 31, "Object"], [15, 514, 19, 31], [15, 515, 19, 31, "getOwnPropertyDescriptor"], [15, 539, 19, 31], [15, 540, 19, 31, "e"], [15, 541, 19, 31], [15, 543, 19, 31, "_t"], [15, 545, 19, 31], [15, 552, 19, 31, "i"], [15, 553, 19, 31], [15, 554, 19, 31, "get"], [15, 557, 19, 31], [15, 561, 19, 31, "i"], [15, 562, 19, 31], [15, 563, 19, 31, "set"], [15, 566, 19, 31], [15, 570, 19, 31, "o"], [15, 571, 19, 31], [15, 572, 19, 31, "f"], [15, 573, 19, 31], [15, 575, 19, 31, "_t"], [15, 577, 19, 31], [15, 579, 19, 31, "i"], [15, 580, 19, 31], [15, 584, 19, 31, "f"], [15, 585, 19, 31], [15, 586, 19, 31, "_t"], [15, 588, 19, 31], [15, 592, 19, 31, "e"], [15, 593, 19, 31], [15, 594, 19, 31, "_t"], [15, 596, 19, 31], [15, 607, 19, 31, "f"], [15, 608, 19, 31], [15, 613, 19, 31, "e"], [15, 614, 19, 31], [15, 616, 19, 31, "t"], [15, 617, 19, 31], [16, 2, 33, 0], [16, 11, 33, 9, "LogBoxButton"], [16, 23, 33, 21, "LogBoxButton"], [16, 24, 33, 22, "props"], [16, 29, 33, 34], [16, 31, 33, 48], [17, 4, 34, 2], [17, 8, 34, 2, "_React$useState"], [17, 23, 34, 2], [17, 26, 34, 32, "React"], [17, 31, 34, 37], [17, 32, 34, 38, "useState"], [17, 40, 34, 46], [17, 41, 34, 47], [17, 46, 34, 52], [17, 47, 34, 53], [18, 6, 34, 53, "_React$useState2"], [18, 22, 34, 53], [18, 29, 34, 53, "_slicedToArray2"], [18, 44, 34, 53], [18, 45, 34, 53, "default"], [18, 52, 34, 53], [18, 54, 34, 53, "_React$useState"], [18, 69, 34, 53], [19, 6, 34, 9, "pressed"], [19, 13, 34, 16], [19, 16, 34, 16, "_React$useState2"], [19, 32, 34, 16], [20, 6, 34, 18, "setPressed"], [20, 16, 34, 28], [20, 19, 34, 28, "_React$useState2"], [20, 35, 34, 28], [21, 4, 36, 2], [21, 8, 36, 6, "backgroundColor"], [21, 23, 36, 21], [21, 26, 36, 24, "props"], [21, 31, 36, 29], [21, 32, 36, 30, "backgroundColor"], [21, 47, 36, 45], [22, 4, 37, 2], [22, 8, 37, 6], [22, 9, 37, 7, "backgroundColor"], [22, 24, 37, 22], [22, 26, 37, 24], [23, 6, 38, 4, "backgroundColor"], [23, 21, 38, 19], [23, 24, 38, 22], [24, 8, 39, 6, "default"], [24, 15, 39, 13], [24, 17, 39, 15, "LogBoxStyle"], [24, 28, 39, 26], [24, 29, 39, 27, "getBackgroundColor"], [24, 47, 39, 45], [24, 48, 39, 46], [24, 52, 39, 50], [24, 53, 39, 51], [25, 8, 40, 6, "pressed"], [25, 15, 40, 13], [25, 17, 40, 15, "LogBoxStyle"], [25, 28, 40, 26], [25, 29, 40, 27, "getBackgroundColor"], [25, 47, 40, 45], [25, 48, 40, 46], [25, 51, 40, 49], [26, 6, 41, 4], [26, 7, 41, 5], [27, 4, 42, 2], [28, 4, 44, 2], [28, 8, 44, 8, "content"], [28, 15, 44, 15], [28, 18, 45, 4], [28, 22, 45, 4, "_jsxRuntime"], [28, 33, 45, 4], [28, 34, 45, 4, "jsx"], [28, 37, 45, 4], [28, 39, 45, 5, "_View"], [28, 44, 45, 5], [28, 45, 45, 5, "default"], [28, 52, 45, 9], [29, 6, 46, 6, "id"], [29, 8, 46, 8], [29, 10, 46, 10, "props"], [29, 15, 46, 15], [29, 16, 46, 16, "id"], [29, 18, 46, 19], [30, 6, 47, 6, "style"], [30, 11, 47, 11], [30, 13, 47, 13, "StyleSheet"], [30, 32, 47, 23], [30, 33, 47, 24, "compose"], [30, 40, 47, 31], [30, 41, 48, 8], [31, 8, 49, 10, "backgroundColor"], [31, 23, 49, 25], [31, 25, 49, 27, "pressed"], [31, 32, 49, 34], [31, 35, 50, 14, "backgroundColor"], [31, 50, 50, 29], [31, 51, 50, 30, "pressed"], [31, 58, 50, 37], [31, 61, 51, 14, "backgroundColor"], [31, 76, 51, 29], [31, 77, 51, 30, "default"], [32, 6, 52, 8], [32, 7, 52, 9], [32, 9, 53, 8, "props"], [32, 14, 53, 13], [32, 15, 53, 14, "style"], [32, 20, 54, 6], [32, 21, 54, 8], [33, 6, 54, 8, "children"], [33, 14, 54, 8], [33, 16, 55, 7, "props"], [33, 21, 55, 12], [33, 22, 55, 13, "children"], [34, 4, 55, 21], [34, 5, 56, 10], [34, 6, 57, 3], [35, 4, 59, 2], [35, 11, 59, 9, "props"], [35, 16, 59, 14], [35, 17, 59, 15, "onPress"], [35, 24, 59, 22], [35, 28, 59, 26], [35, 32, 59, 30], [35, 35, 60, 4, "content"], [35, 42, 60, 11], [35, 45, 62, 4], [35, 49, 62, 4, "_jsxRuntime"], [35, 60, 62, 4], [35, 61, 62, 4, "jsx"], [35, 64, 62, 4], [35, 66, 62, 5, "_TouchableWithoutFeedback"], [35, 91, 62, 5], [35, 92, 62, 5, "default"], [35, 99, 62, 29], [36, 6, 63, 6, "hitSlop"], [36, 13, 63, 13], [36, 15, 63, 15, "props"], [36, 20, 63, 20], [36, 21, 63, 21, "hitSlop"], [36, 28, 63, 29], [37, 6, 64, 6, "onPress"], [37, 13, 64, 13], [37, 15, 64, 15, "props"], [37, 20, 64, 20], [37, 21, 64, 21, "onPress"], [37, 28, 64, 29], [38, 6, 65, 6, "onPressIn"], [38, 15, 65, 15], [38, 17, 65, 17, "onPressIn"], [38, 18, 65, 17], [38, 23, 65, 23, "setPressed"], [38, 33, 65, 33], [38, 34, 65, 34], [38, 38, 65, 38], [38, 39, 65, 40], [39, 6, 66, 6, "onPressOut"], [39, 16, 66, 16], [39, 18, 66, 18, "onPressOut"], [39, 19, 66, 18], [39, 24, 66, 24, "setPressed"], [39, 34, 66, 34], [39, 35, 66, 35], [39, 40, 66, 40], [39, 41, 66, 42], [40, 6, 66, 42, "children"], [40, 14, 66, 42], [40, 16, 67, 7, "content"], [41, 4, 67, 14], [41, 5, 68, 30], [41, 6, 69, 3], [42, 2, 70, 0], [43, 2, 70, 1], [43, 6, 70, 1, "_default"], [43, 14, 70, 1], [43, 17, 70, 1, "exports"], [43, 24, 70, 1], [43, 25, 70, 1, "default"], [43, 32, 70, 1], [43, 35, 72, 15, "LogBoxButton"], [43, 47, 72, 27], [44, 0, 72, 27], [44, 3]], "functionMap": {"names": ["<global>", "LogBoxButton", "TouchableWithoutFeedback.props.onPressIn", "TouchableWithoutFeedback.props.onPressOut"], "mappings": "AAA;ACgC;iBCgC,sBD;kBEC,uBF;CDI"}}, "type": "js/module"}]}