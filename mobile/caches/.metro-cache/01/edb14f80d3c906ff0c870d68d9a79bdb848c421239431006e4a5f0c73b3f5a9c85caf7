{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 48}, "end": {"line": 2, "column": 87, "index": 135}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "../../../findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 177}, "end": {"line": 4, "column": 53, "index": 230}}], "key": "k+xfarWxri7fB3IShKFMK0oi5UQ=", "exportNames": ["*"]}}, {"name": "../../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 231}, "end": {"line": 5, "column": 43, "index": 274}}], "key": "4wo4OYT4MSo2InL8kiWmZxvepwE=", "exportNames": ["*"]}}, {"name": "../../../GestureHandlerRootViewContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 275}, "end": {"line": 6, "column": 83, "index": 358}}], "key": "v6b9cfauRqYeWu9wWOEUTyMIHSA=", "exportNames": ["*"]}}, {"name": "./useAnimatedGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 359}, "end": {"line": 7, "column": 58, "index": 417}}], "key": "2qsvw/0Wn5ZQ0k+d9VbJV8PW2us=", "exportNames": ["*"]}}, {"name": "./attachHandlers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 418}, "end": {"line": 8, "column": 50, "index": 468}}], "key": "3mjR74KCCo5t43evU8Hvoyi9yu0=", "exportNames": ["*"]}}, {"name": "./needsToReattach", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 469}, "end": {"line": 9, "column": 52, "index": 521}}], "key": "AnC4N1Crd90FP+3Mxk358neOkRo=", "exportNames": ["*"]}}, {"name": "./dropHandlers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 522}, "end": {"line": 10, "column": 46, "index": 568}}], "key": "3pg09hFbTrtcJ+KzQ97dAmmPlSE=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 569}, "end": {"line": 11, "column": 46, "index": 615}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}, {"name": "./Wrap", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 616}, "end": {"line": 12, "column": 44, "index": 660}}], "key": "3O9fTt6BDgaEKEy9t1chSR0HFNQ=", "exportNames": ["*"]}}, {"name": "./useDetectorUpdater", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 661}, "end": {"line": 13, "column": 58, "index": 719}}], "key": "707zRwYZ3uzpHSm+Rwc2R1MvfJw=", "exportNames": ["*"]}}, {"name": "./useViewRefHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 720}, "end": {"line": 14, "column": 56, "index": 776}}], "key": "tm4O9dzaDCUn7KS1TB05c8nzNaA=", "exportNames": ["*"]}}, {"name": "./useMountReactions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 777}, "end": {"line": 15, "column": 56, "index": 833}}], "key": "PrdD1p6YXRuOP5neQrVF/37n7P4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.GestureDetector = void 0;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-css-interop\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Platform\"));\n  var _findNodeHandle = _interopRequireDefault(require(_dependencyMap[4], \"../../../findNodeHandle\"));\n  var _utils = require(_dependencyMap[5], \"../../../utils\");\n  var _GestureHandlerRootViewContext = _interopRequireDefault(require(_dependencyMap[6], \"../../../GestureHandlerRootViewContext\"));\n  var _useAnimatedGesture = require(_dependencyMap[7], \"./useAnimatedGesture\");\n  var _attachHandlers = require(_dependencyMap[8], \"./attachHandlers\");\n  var _needsToReattach = require(_dependencyMap[9], \"./needsToReattach\");\n  var _dropHandlers = require(_dependencyMap[10], \"./dropHandlers\");\n  var _utils2 = require(_dependencyMap[11], \"./utils\");\n  var _Wrap = require(_dependencyMap[12], \"./Wrap\");\n  var _useDetectorUpdater = require(_dependencyMap[13], \"./useDetectorUpdater\");\n  var _useViewRefHandler = require(_dependencyMap[14], \"./useViewRefHandler\");\n  var _useMountReactions = require(_dependencyMap[15], \"./useMountReactions\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /* eslint-disable react/no-unused-prop-types */\n\n  function propagateDetectorConfig(props, gesture) {\n    const keysToPropagate = ['userSelect', 'enableContextMenu', 'touchAction'];\n    for (const key of keysToPropagate) {\n      const value = props[key];\n      if (value === undefined) {\n        continue;\n      }\n      for (const g of gesture.toGestureArray()) {\n        const config = g.config;\n        config[key] = value;\n      }\n    }\n  }\n\n  /**\n   * `GestureDetector` is responsible for creating and updating native gesture handlers based on the config of provided gesture.\n   *\n   * ### Props\n   * - `gesture`\n   * - `userSelect` (**Web only**)\n   * - `enableContextMenu` (**Web only**)\n   * - `touchAction` (**Web only**)\n   *\n   * ### Remarks\n   * - Gesture Detector will use first native view in its subtree to recognize gestures, however if this view is used only to group its children it may get automatically collapsed.\n   * - Using the same instance of a gesture across multiple Gesture Detectors is not possible.\n   *\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/gesture-detector\n   */\n  const GestureDetector = props => {\n    const rootViewContext = (0, _react.useContext)(_GestureHandlerRootViewContext.default);\n    if (__DEV__ && !rootViewContext && !(0, _utils.isTestEnv)() && _Platform.default.OS !== 'web') {\n      throw new Error('GestureDetector must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.');\n    } // Gesture config should be wrapped with useMemo to prevent unnecessary re-renders\n\n    const gestureConfig = props.gesture;\n    propagateDetectorConfig(props, gestureConfig);\n    const gesturesToAttach = (0, _react.useMemo)(() => gestureConfig.toGestureArray(), [gestureConfig]);\n    const shouldUseReanimated = gesturesToAttach.some(g => g.shouldUseReanimated);\n    const webEventHandlersRef = (0, _utils2.useWebEventHandlers)(); // Store state in ref to prevent unnecessary renders\n\n    const state = (0, _react.useRef)({\n      firstRender: true,\n      viewRef: null,\n      previousViewTag: -1,\n      forceRebuildReanimatedEvent: false\n    }).current;\n    const preparedGesture = _react.default.useRef({\n      attachedGestures: [],\n      animatedEventHandler: null,\n      animatedHandlers: null,\n      shouldUseReanimated: shouldUseReanimated,\n      isMounted: false\n    }).current;\n    const updateAttachedGestures = (0, _useDetectorUpdater.useDetectorUpdater)(state, preparedGesture, gesturesToAttach, gestureConfig, webEventHandlersRef);\n    const refHandler = (0, _useViewRefHandler.useViewRefHandler)(state, updateAttachedGestures); // Reanimated event should be rebuilt only when gestures are reattached, otherwise\n    // config update will be enough as all necessary items are stored in shared values anyway\n\n    const needsToRebuildReanimatedEvent = state.firstRender || state.forceRebuildReanimatedEvent || (0, _needsToReattach.needsToReattach)(preparedGesture, gesturesToAttach);\n    state.forceRebuildReanimatedEvent = false;\n    (0, _useAnimatedGesture.useAnimatedGesture)(preparedGesture, needsToRebuildReanimatedEvent);\n    (0, _react.useLayoutEffect)(() => {\n      const viewTag = (0, _findNodeHandle.default)(state.viewRef);\n      preparedGesture.isMounted = true;\n      (0, _attachHandlers.attachHandlers)({\n        preparedGesture,\n        gestureConfig,\n        gesturesToAttach,\n        webEventHandlersRef,\n        viewTag\n      });\n      return () => {\n        preparedGesture.isMounted = false;\n        (0, _dropHandlers.dropHandlers)(preparedGesture);\n      };\n    }, []);\n    (0, _react.useEffect)(() => {\n      if (state.firstRender) {\n        state.firstRender = false;\n      } else {\n        updateAttachedGestures();\n      }\n    }, [props]);\n    (0, _useMountReactions.useMountReactions)(updateAttachedGestures, preparedGesture);\n    if (shouldUseReanimated) {\n      return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_Wrap.AnimatedWrap, {\n        ref: refHandler,\n        onGestureHandlerEvent: preparedGesture.animatedEventHandler\n      }, props.children);\n    } else {\n      return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_Wrap.Wrap, {\n        ref: refHandler\n      }, props.children);\n    }\n  };\n  exports.GestureDetector = GestureDetector;\n});", "lineCount": 121, "map": [[8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "_interopRequireWildcard"], [8, 38, 2, 0], [8, 39, 2, 0, "require"], [8, 46, 2, 0], [8, 47, 2, 0, "_dependencyMap"], [8, 61, 2, 0], [9, 2, 2, 87], [9, 6, 2, 87, "_Platform"], [9, 15, 2, 87], [9, 18, 2, 87, "_interopRequireDefault"], [9, 40, 2, 87], [9, 41, 2, 87, "require"], [9, 48, 2, 87], [9, 49, 2, 87, "_dependencyMap"], [9, 63, 2, 87], [10, 2, 4, 0], [10, 6, 4, 0, "_findNodeHandle"], [10, 21, 4, 0], [10, 24, 4, 0, "_interopRequireDefault"], [10, 46, 4, 0], [10, 47, 4, 0, "require"], [10, 54, 4, 0], [10, 55, 4, 0, "_dependencyMap"], [10, 69, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_utils"], [11, 12, 5, 0], [11, 15, 5, 0, "require"], [11, 22, 5, 0], [11, 23, 5, 0, "_dependencyMap"], [11, 37, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_GestureHandlerRootViewContext"], [12, 36, 6, 0], [12, 39, 6, 0, "_interopRequireDefault"], [12, 61, 6, 0], [12, 62, 6, 0, "require"], [12, 69, 6, 0], [12, 70, 6, 0, "_dependencyMap"], [12, 84, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_useAnimatedGesture"], [13, 25, 7, 0], [13, 28, 7, 0, "require"], [13, 35, 7, 0], [13, 36, 7, 0, "_dependencyMap"], [13, 50, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_attachHandlers"], [14, 21, 8, 0], [14, 24, 8, 0, "require"], [14, 31, 8, 0], [14, 32, 8, 0, "_dependencyMap"], [14, 46, 8, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_needsToReattach"], [15, 22, 9, 0], [15, 25, 9, 0, "require"], [15, 32, 9, 0], [15, 33, 9, 0, "_dependencyMap"], [15, 47, 9, 0], [16, 2, 10, 0], [16, 6, 10, 0, "_dropHandlers"], [16, 19, 10, 0], [16, 22, 10, 0, "require"], [16, 29, 10, 0], [16, 30, 10, 0, "_dependencyMap"], [16, 44, 10, 0], [17, 2, 11, 0], [17, 6, 11, 0, "_utils2"], [17, 13, 11, 0], [17, 16, 11, 0, "require"], [17, 23, 11, 0], [17, 24, 11, 0, "_dependencyMap"], [17, 38, 11, 0], [18, 2, 12, 0], [18, 6, 12, 0, "_Wrap"], [18, 11, 12, 0], [18, 14, 12, 0, "require"], [18, 21, 12, 0], [18, 22, 12, 0, "_dependencyMap"], [18, 36, 12, 0], [19, 2, 13, 0], [19, 6, 13, 0, "_useDetectorUpdater"], [19, 25, 13, 0], [19, 28, 13, 0, "require"], [19, 35, 13, 0], [19, 36, 13, 0, "_dependencyMap"], [19, 50, 13, 0], [20, 2, 14, 0], [20, 6, 14, 0, "_useViewRefHandler"], [20, 24, 14, 0], [20, 27, 14, 0, "require"], [20, 34, 14, 0], [20, 35, 14, 0, "_dependencyMap"], [20, 49, 14, 0], [21, 2, 15, 0], [21, 6, 15, 0, "_useMountReactions"], [21, 24, 15, 0], [21, 27, 15, 0, "require"], [21, 34, 15, 0], [21, 35, 15, 0, "_dependencyMap"], [21, 49, 15, 0], [22, 2, 15, 56], [22, 11, 15, 56, "_interopRequireWildcard"], [22, 35, 15, 56, "e"], [22, 36, 15, 56], [22, 38, 15, 56, "t"], [22, 39, 15, 56], [22, 68, 15, 56, "WeakMap"], [22, 75, 15, 56], [22, 81, 15, 56, "r"], [22, 82, 15, 56], [22, 89, 15, 56, "WeakMap"], [22, 96, 15, 56], [22, 100, 15, 56, "n"], [22, 101, 15, 56], [22, 108, 15, 56, "WeakMap"], [22, 115, 15, 56], [22, 127, 15, 56, "_interopRequireWildcard"], [22, 150, 15, 56], [22, 162, 15, 56, "_interopRequireWildcard"], [22, 163, 15, 56, "e"], [22, 164, 15, 56], [22, 166, 15, 56, "t"], [22, 167, 15, 56], [22, 176, 15, 56, "t"], [22, 177, 15, 56], [22, 181, 15, 56, "e"], [22, 182, 15, 56], [22, 186, 15, 56, "e"], [22, 187, 15, 56], [22, 188, 15, 56, "__esModule"], [22, 198, 15, 56], [22, 207, 15, 56, "e"], [22, 208, 15, 56], [22, 214, 15, 56, "o"], [22, 215, 15, 56], [22, 217, 15, 56, "i"], [22, 218, 15, 56], [22, 220, 15, 56, "f"], [22, 221, 15, 56], [22, 226, 15, 56, "__proto__"], [22, 235, 15, 56], [22, 243, 15, 56, "default"], [22, 250, 15, 56], [22, 252, 15, 56, "e"], [22, 253, 15, 56], [22, 270, 15, 56, "e"], [22, 271, 15, 56], [22, 294, 15, 56, "e"], [22, 295, 15, 56], [22, 320, 15, 56, "e"], [22, 321, 15, 56], [22, 330, 15, 56, "f"], [22, 331, 15, 56], [22, 337, 15, 56, "o"], [22, 338, 15, 56], [22, 341, 15, 56, "t"], [22, 342, 15, 56], [22, 345, 15, 56, "n"], [22, 346, 15, 56], [22, 349, 15, 56, "r"], [22, 350, 15, 56], [22, 358, 15, 56, "o"], [22, 359, 15, 56], [22, 360, 15, 56, "has"], [22, 363, 15, 56], [22, 364, 15, 56, "e"], [22, 365, 15, 56], [22, 375, 15, 56, "o"], [22, 376, 15, 56], [22, 377, 15, 56, "get"], [22, 380, 15, 56], [22, 381, 15, 56, "e"], [22, 382, 15, 56], [22, 385, 15, 56, "o"], [22, 386, 15, 56], [22, 387, 15, 56, "set"], [22, 390, 15, 56], [22, 391, 15, 56, "e"], [22, 392, 15, 56], [22, 394, 15, 56, "f"], [22, 395, 15, 56], [22, 411, 15, 56, "t"], [22, 412, 15, 56], [22, 416, 15, 56, "e"], [22, 417, 15, 56], [22, 433, 15, 56, "t"], [22, 434, 15, 56], [22, 441, 15, 56, "hasOwnProperty"], [22, 455, 15, 56], [22, 456, 15, 56, "call"], [22, 460, 15, 56], [22, 461, 15, 56, "e"], [22, 462, 15, 56], [22, 464, 15, 56, "t"], [22, 465, 15, 56], [22, 472, 15, 56, "i"], [22, 473, 15, 56], [22, 477, 15, 56, "o"], [22, 478, 15, 56], [22, 481, 15, 56, "Object"], [22, 487, 15, 56], [22, 488, 15, 56, "defineProperty"], [22, 502, 15, 56], [22, 507, 15, 56, "Object"], [22, 513, 15, 56], [22, 514, 15, 56, "getOwnPropertyDescriptor"], [22, 538, 15, 56], [22, 539, 15, 56, "e"], [22, 540, 15, 56], [22, 542, 15, 56, "t"], [22, 543, 15, 56], [22, 550, 15, 56, "i"], [22, 551, 15, 56], [22, 552, 15, 56, "get"], [22, 555, 15, 56], [22, 559, 15, 56, "i"], [22, 560, 15, 56], [22, 561, 15, 56, "set"], [22, 564, 15, 56], [22, 568, 15, 56, "o"], [22, 569, 15, 56], [22, 570, 15, 56, "f"], [22, 571, 15, 56], [22, 573, 15, 56, "t"], [22, 574, 15, 56], [22, 576, 15, 56, "i"], [22, 577, 15, 56], [22, 581, 15, 56, "f"], [22, 582, 15, 56], [22, 583, 15, 56, "t"], [22, 584, 15, 56], [22, 588, 15, 56, "e"], [22, 589, 15, 56], [22, 590, 15, 56, "t"], [22, 591, 15, 56], [22, 602, 15, 56, "f"], [22, 603, 15, 56], [22, 608, 15, 56, "e"], [22, 609, 15, 56], [22, 611, 15, 56, "t"], [22, 612, 15, 56], [23, 2, 1, 0], [25, 2, 17, 0], [25, 11, 17, 9, "propagateDetectorConfig"], [25, 34, 17, 32, "propagateDetectorConfig"], [25, 35, 17, 33, "props"], [25, 40, 17, 38], [25, 42, 17, 40, "gesture"], [25, 49, 17, 47], [25, 51, 17, 49], [26, 4, 18, 2], [26, 10, 18, 8, "keysToPropagate"], [26, 25, 18, 23], [26, 28, 18, 26], [26, 29, 18, 27], [26, 41, 18, 39], [26, 43, 18, 41], [26, 62, 18, 60], [26, 64, 18, 62], [26, 77, 18, 75], [26, 78, 18, 76], [27, 4, 20, 2], [27, 9, 20, 7], [27, 15, 20, 13, "key"], [27, 18, 20, 16], [27, 22, 20, 20, "keysToPropagate"], [27, 37, 20, 35], [27, 39, 20, 37], [28, 6, 21, 4], [28, 12, 21, 10, "value"], [28, 17, 21, 15], [28, 20, 21, 18, "props"], [28, 25, 21, 23], [28, 26, 21, 24, "key"], [28, 29, 21, 27], [28, 30, 21, 28], [29, 6, 23, 4], [29, 10, 23, 8, "value"], [29, 15, 23, 13], [29, 20, 23, 18, "undefined"], [29, 29, 23, 27], [29, 31, 23, 29], [30, 8, 24, 6], [31, 6, 25, 4], [32, 6, 27, 4], [32, 11, 27, 9], [32, 17, 27, 15, "g"], [32, 18, 27, 16], [32, 22, 27, 20, "gesture"], [32, 29, 27, 27], [32, 30, 27, 28, "toGestureArray"], [32, 44, 27, 42], [32, 45, 27, 43], [32, 46, 27, 44], [32, 48, 27, 46], [33, 8, 28, 6], [33, 14, 28, 12, "config"], [33, 20, 28, 18], [33, 23, 28, 21, "g"], [33, 24, 28, 22], [33, 25, 28, 23, "config"], [33, 31, 28, 29], [34, 8, 29, 6, "config"], [34, 14, 29, 12], [34, 15, 29, 13, "key"], [34, 18, 29, 16], [34, 19, 29, 17], [34, 22, 29, 20, "value"], [34, 27, 29, 25], [35, 6, 30, 4], [36, 4, 31, 2], [37, 2, 32, 0], [39, 2, 34, 0], [40, 0, 35, 0], [41, 0, 36, 0], [42, 0, 37, 0], [43, 0, 38, 0], [44, 0, 39, 0], [45, 0, 40, 0], [46, 0, 41, 0], [47, 0, 42, 0], [48, 0, 43, 0], [49, 0, 44, 0], [50, 0, 45, 0], [51, 0, 46, 0], [52, 0, 47, 0], [53, 0, 48, 0], [54, 2, 49, 7], [54, 8, 49, 13, "GestureDetector"], [54, 23, 49, 28], [54, 26, 49, 31, "props"], [54, 31, 49, 36], [54, 35, 49, 40], [55, 4, 50, 2], [55, 10, 50, 8, "rootViewContext"], [55, 25, 50, 23], [55, 28, 50, 26], [55, 32, 50, 26, "useContext"], [55, 49, 50, 36], [55, 51, 50, 37, "GestureHandlerRootViewContext"], [55, 89, 50, 66], [55, 90, 50, 67], [56, 4, 52, 2], [56, 8, 52, 6, "__DEV__"], [56, 15, 52, 13], [56, 19, 52, 17], [56, 20, 52, 18, "rootViewContext"], [56, 35, 52, 33], [56, 39, 52, 37], [56, 40, 52, 38], [56, 44, 52, 38, "isTestEnv"], [56, 60, 52, 47], [56, 62, 52, 48], [56, 63, 52, 49], [56, 67, 52, 53, "Platform"], [56, 84, 52, 61], [56, 85, 52, 62, "OS"], [56, 87, 52, 64], [56, 92, 52, 69], [56, 97, 52, 74], [56, 99, 52, 76], [57, 6, 53, 4], [57, 12, 53, 10], [57, 16, 53, 14, "Error"], [57, 21, 53, 19], [57, 22, 53, 20], [57, 238, 53, 236], [57, 239, 53, 237], [58, 4, 54, 2], [58, 5, 54, 3], [58, 6, 54, 4], [60, 4, 57, 2], [60, 10, 57, 8, "gestureConfig"], [60, 23, 57, 21], [60, 26, 57, 24, "props"], [60, 31, 57, 29], [60, 32, 57, 30, "gesture"], [60, 39, 57, 37], [61, 4, 58, 2, "propagateDetectorConfig"], [61, 27, 58, 25], [61, 28, 58, 26, "props"], [61, 33, 58, 31], [61, 35, 58, 33, "gestureConfig"], [61, 48, 58, 46], [61, 49, 58, 47], [62, 4, 59, 2], [62, 10, 59, 8, "gestures<PERSON>oAtta<PERSON>"], [62, 26, 59, 24], [62, 29, 59, 27], [62, 33, 59, 27, "useMemo"], [62, 47, 59, 34], [62, 49, 59, 35], [62, 55, 59, 41, "gestureConfig"], [62, 68, 59, 54], [62, 69, 59, 55, "toGestureArray"], [62, 83, 59, 69], [62, 84, 59, 70], [62, 85, 59, 71], [62, 87, 59, 73], [62, 88, 59, 74, "gestureConfig"], [62, 101, 59, 87], [62, 102, 59, 88], [62, 103, 59, 89], [63, 4, 60, 2], [63, 10, 60, 8, "shouldUseReanimated"], [63, 29, 60, 27], [63, 32, 60, 30, "gestures<PERSON>oAtta<PERSON>"], [63, 48, 60, 46], [63, 49, 60, 47, "some"], [63, 53, 60, 51], [63, 54, 60, 52, "g"], [63, 55, 60, 53], [63, 59, 60, 57, "g"], [63, 60, 60, 58], [63, 61, 60, 59, "shouldUseReanimated"], [63, 80, 60, 78], [63, 81, 60, 79], [64, 4, 61, 2], [64, 10, 61, 8, "webEventHandlersRef"], [64, 29, 61, 27], [64, 32, 61, 30], [64, 36, 61, 30, "useWebEventHandlers"], [64, 63, 61, 49], [64, 65, 61, 50], [64, 66, 61, 51], [64, 67, 61, 52], [64, 68, 61, 53], [66, 4, 63, 2], [66, 10, 63, 8, "state"], [66, 15, 63, 13], [66, 18, 63, 16], [66, 22, 63, 16, "useRef"], [66, 35, 63, 22], [66, 37, 63, 23], [67, 6, 64, 4, "firstRender"], [67, 17, 64, 15], [67, 19, 64, 17], [67, 23, 64, 21], [68, 6, 65, 4, "viewRef"], [68, 13, 65, 11], [68, 15, 65, 13], [68, 19, 65, 17], [69, 6, 66, 4, "previousViewTag"], [69, 21, 66, 19], [69, 23, 66, 21], [69, 24, 66, 22], [69, 25, 66, 23], [70, 6, 67, 4, "forceRebuildReanimatedEvent"], [70, 33, 67, 31], [70, 35, 67, 33], [71, 4, 68, 2], [71, 5, 68, 3], [71, 6, 68, 4], [71, 7, 68, 5, "current"], [71, 14, 68, 12], [72, 4, 69, 2], [72, 10, 69, 8, "preparedGesture"], [72, 25, 69, 23], [72, 28, 69, 26, "React"], [72, 42, 69, 31], [72, 43, 69, 32, "useRef"], [72, 49, 69, 38], [72, 50, 69, 39], [73, 6, 70, 4, "attachedGestures"], [73, 22, 70, 20], [73, 24, 70, 22], [73, 26, 70, 24], [74, 6, 71, 4, "animatedEventHandler"], [74, 26, 71, 24], [74, 28, 71, 26], [74, 32, 71, 30], [75, 6, 72, 4, "animatedHandlers"], [75, 22, 72, 20], [75, 24, 72, 22], [75, 28, 72, 26], [76, 6, 73, 4, "shouldUseReanimated"], [76, 25, 73, 23], [76, 27, 73, 25, "shouldUseReanimated"], [76, 46, 73, 44], [77, 6, 74, 4, "isMounted"], [77, 15, 74, 13], [77, 17, 74, 15], [78, 4, 75, 2], [78, 5, 75, 3], [78, 6, 75, 4], [78, 7, 75, 5, "current"], [78, 14, 75, 12], [79, 4, 76, 2], [79, 10, 76, 8, "updateAttachedGestures"], [79, 32, 76, 30], [79, 35, 76, 33], [79, 39, 76, 33, "useDetectorUpdater"], [79, 77, 76, 51], [79, 79, 76, 52, "state"], [79, 84, 76, 57], [79, 86, 76, 59, "preparedGesture"], [79, 101, 76, 74], [79, 103, 76, 76, "gestures<PERSON>oAtta<PERSON>"], [79, 119, 76, 92], [79, 121, 76, 94, "gestureConfig"], [79, 134, 76, 107], [79, 136, 76, 109, "webEventHandlersRef"], [79, 155, 76, 128], [79, 156, 76, 129], [80, 4, 77, 2], [80, 10, 77, 8, "ref<PERSON><PERSON><PERSON>"], [80, 20, 77, 18], [80, 23, 77, 21], [80, 27, 77, 21, "useViewRefHandler"], [80, 63, 77, 38], [80, 65, 77, 39, "state"], [80, 70, 77, 44], [80, 72, 77, 46, "updateAttachedGestures"], [80, 94, 77, 68], [80, 95, 77, 69], [80, 96, 77, 70], [80, 97, 77, 71], [81, 4, 78, 2], [83, 4, 80, 2], [83, 10, 80, 8, "needsToRebuildReanimatedEvent"], [83, 39, 80, 37], [83, 42, 80, 40, "state"], [83, 47, 80, 45], [83, 48, 80, 46, "firstRender"], [83, 59, 80, 57], [83, 63, 80, 61, "state"], [83, 68, 80, 66], [83, 69, 80, 67, "forceRebuildReanimatedEvent"], [83, 96, 80, 94], [83, 100, 80, 98], [83, 104, 80, 98, "needsToReattach"], [83, 136, 80, 113], [83, 138, 80, 114, "preparedGesture"], [83, 153, 80, 129], [83, 155, 80, 131, "gestures<PERSON>oAtta<PERSON>"], [83, 171, 80, 147], [83, 172, 80, 148], [84, 4, 81, 2, "state"], [84, 9, 81, 7], [84, 10, 81, 8, "forceRebuildReanimatedEvent"], [84, 37, 81, 35], [84, 40, 81, 38], [84, 45, 81, 43], [85, 4, 82, 2], [85, 8, 82, 2, "useAnimatedGesture"], [85, 46, 82, 20], [85, 48, 82, 21, "preparedGesture"], [85, 63, 82, 36], [85, 65, 82, 38, "needsToRebuildReanimatedEvent"], [85, 94, 82, 67], [85, 95, 82, 68], [86, 4, 83, 2], [86, 8, 83, 2, "useLayoutEffect"], [86, 30, 83, 17], [86, 32, 83, 18], [86, 38, 83, 24], [87, 6, 84, 4], [87, 12, 84, 10, "viewTag"], [87, 19, 84, 17], [87, 22, 84, 20], [87, 26, 84, 20, "findNodeHandle"], [87, 49, 84, 34], [87, 51, 84, 35, "state"], [87, 56, 84, 40], [87, 57, 84, 41, "viewRef"], [87, 64, 84, 48], [87, 65, 84, 49], [88, 6, 85, 4, "preparedGesture"], [88, 21, 85, 19], [88, 22, 85, 20, "isMounted"], [88, 31, 85, 29], [88, 34, 85, 32], [88, 38, 85, 36], [89, 6, 86, 4], [89, 10, 86, 4, "attachHandlers"], [89, 40, 86, 18], [89, 42, 86, 19], [90, 8, 87, 6, "preparedGesture"], [90, 23, 87, 21], [91, 8, 88, 6, "gestureConfig"], [91, 21, 88, 19], [92, 8, 89, 6, "gestures<PERSON>oAtta<PERSON>"], [92, 24, 89, 22], [93, 8, 90, 6, "webEventHandlersRef"], [93, 27, 90, 25], [94, 8, 91, 6, "viewTag"], [95, 6, 92, 4], [95, 7, 92, 5], [95, 8, 92, 6], [96, 6, 93, 4], [96, 13, 93, 11], [96, 19, 93, 17], [97, 8, 94, 6, "preparedGesture"], [97, 23, 94, 21], [97, 24, 94, 22, "isMounted"], [97, 33, 94, 31], [97, 36, 94, 34], [97, 41, 94, 39], [98, 8, 95, 6], [98, 12, 95, 6, "dropHandlers"], [98, 38, 95, 18], [98, 40, 95, 19, "preparedGesture"], [98, 55, 95, 34], [98, 56, 95, 35], [99, 6, 96, 4], [99, 7, 96, 5], [100, 4, 97, 2], [100, 5, 97, 3], [100, 7, 97, 5], [100, 9, 97, 7], [100, 10, 97, 8], [101, 4, 98, 2], [101, 8, 98, 2, "useEffect"], [101, 24, 98, 11], [101, 26, 98, 12], [101, 32, 98, 18], [102, 6, 99, 4], [102, 10, 99, 8, "state"], [102, 15, 99, 13], [102, 16, 99, 14, "firstRender"], [102, 27, 99, 25], [102, 29, 99, 27], [103, 8, 100, 6, "state"], [103, 13, 100, 11], [103, 14, 100, 12, "firstRender"], [103, 25, 100, 23], [103, 28, 100, 26], [103, 33, 100, 31], [104, 6, 101, 4], [104, 7, 101, 5], [104, 13, 101, 11], [105, 8, 102, 6, "updateAttachedGestures"], [105, 30, 102, 28], [105, 31, 102, 29], [105, 32, 102, 30], [106, 6, 103, 4], [107, 4, 104, 2], [107, 5, 104, 3], [107, 7, 104, 5], [107, 8, 104, 6, "props"], [107, 13, 104, 11], [107, 14, 104, 12], [107, 15, 104, 13], [108, 4, 105, 2], [108, 8, 105, 2, "useMountReactions"], [108, 44, 105, 19], [108, 46, 105, 20, "updateAttachedGestures"], [108, 68, 105, 42], [108, 70, 105, 44, "preparedGesture"], [108, 85, 105, 59], [108, 86, 105, 60], [109, 4, 107, 2], [109, 8, 107, 6, "shouldUseReanimated"], [109, 27, 107, 25], [109, 29, 107, 27], [110, 6, 108, 4], [110, 13, 108, 11], [110, 26, 108, 24, "_ReactNativeCSSInterop"], [110, 48, 108, 24], [110, 49, 108, 24, "createInteropElement"], [110, 69, 108, 24], [110, 70, 108, 44, "AnimatedWrap"], [110, 88, 108, 56], [110, 90, 108, 58], [111, 8, 109, 6, "ref"], [111, 11, 109, 9], [111, 13, 109, 11, "ref<PERSON><PERSON><PERSON>"], [111, 23, 109, 21], [112, 8, 110, 6, "onGestureHandlerEvent"], [112, 29, 110, 27], [112, 31, 110, 29, "preparedGesture"], [112, 46, 110, 44], [112, 47, 110, 45, "animatedEventHandler"], [113, 6, 111, 4], [113, 7, 111, 5], [113, 9, 111, 7, "props"], [113, 14, 111, 12], [113, 15, 111, 13, "children"], [113, 23, 111, 21], [113, 24, 111, 22], [114, 4, 112, 2], [114, 5, 112, 3], [114, 11, 112, 9], [115, 6, 113, 4], [115, 13, 113, 11], [115, 26, 113, 24, "_ReactNativeCSSInterop"], [115, 48, 113, 24], [115, 49, 113, 24, "createInteropElement"], [115, 69, 113, 24], [115, 70, 113, 44, "Wrap"], [115, 80, 113, 48], [115, 82, 113, 50], [116, 8, 114, 6, "ref"], [116, 11, 114, 9], [116, 13, 114, 11, "ref<PERSON><PERSON><PERSON>"], [117, 6, 115, 4], [117, 7, 115, 5], [117, 9, 115, 7, "props"], [117, 14, 115, 12], [117, 15, 115, 13, "children"], [117, 23, 115, 21], [117, 24, 115, 22], [118, 4, 116, 2], [119, 2, 117, 0], [119, 3, 117, 1], [120, 2, 117, 2, "exports"], [120, 9, 117, 2], [120, 10, 117, 2, "GestureDetector"], [120, 25, 117, 2], [120, 28, 117, 2, "GestureDetector"], [120, 43, 117, 2], [121, 0, 117, 2], [121, 3]], "functionMap": {"names": ["<global>", "propagateDetectorConfig", "GestureDetector", "useMemo$argument_0", "gesturesToAttach.some$argument_0", "useLayoutEffect$argument_0", "<anonymous>", "useEffect$argument_0"], "mappings": "AAA;ACgB;CDe;+BEiB;mCCU,oCD;oDEC,0BF;kBGuB;WCU;KDG;GHC;YKC;GLM;CFa"}}, "type": "js/module"}]}