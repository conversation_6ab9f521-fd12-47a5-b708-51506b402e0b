{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  module.exports = str => encodeURIComponent(str).replace(/[!'()*]/g, x => `%${x.charCodeAt(0).toString(16).toUpperCase()}`);\n});", "lineCount": 5, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "module"], [4, 8, 2, 6], [4, 9, 2, 7, "exports"], [4, 16, 2, 14], [4, 19, 2, 17, "str"], [4, 22, 2, 20], [4, 26, 2, 24, "encodeURIComponent"], [4, 44, 2, 42], [4, 45, 2, 43, "str"], [4, 48, 2, 46], [4, 49, 2, 47], [4, 50, 2, 48, "replace"], [4, 57, 2, 55], [4, 58, 2, 56], [4, 68, 2, 66], [4, 70, 2, 68, "x"], [4, 71, 2, 69], [4, 75, 2, 73], [4, 79, 2, 77, "x"], [4, 80, 2, 78], [4, 81, 2, 79, "charCodeAt"], [4, 91, 2, 89], [4, 92, 2, 90], [4, 93, 2, 91], [4, 94, 2, 92], [4, 95, 2, 93, "toString"], [4, 103, 2, 101], [4, 104, 2, 102], [4, 106, 2, 104], [4, 107, 2, 105], [4, 108, 2, 106, "toUpperCase"], [4, 119, 2, 117], [4, 120, 2, 118], [4, 121, 2, 119], [4, 123, 2, 121], [4, 124, 2, 122], [5, 0, 2, 123], [5, 3]], "functionMap": {"names": ["<global>", "module.exports", "encodeURIComponent.replace$argument_1"], "mappings": "AAA;iBCC,mDC,qDD,CD"}}, "type": "js/module"}]}