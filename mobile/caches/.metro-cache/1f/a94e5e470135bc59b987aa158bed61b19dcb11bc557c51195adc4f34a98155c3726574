{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 0, "index": 443}, "end": {"line": 17, "column": 33, "index": 571}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RNCSafeAreaView';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNCSafeAreaView\",\n    validAttributes: {\n      mode: true,\n      edges: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 18, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 15, 0], [8, 6, 15, 0, "NativeComponentRegistry"], [8, 29, 17, 33], [8, 32, 15, 0, "require"], [8, 39, 17, 33], [8, 40, 17, 33, "_dependencyMap"], [8, 54, 17, 33], [8, 123, 17, 32], [8, 124, 17, 33], [9, 2, 15, 0], [9, 6, 15, 0, "nativeComponentName"], [9, 25, 17, 33], [9, 28, 15, 0], [9, 45, 17, 33], [10, 2, 15, 0], [10, 6, 15, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 17, 33], [10, 31, 17, 33, "exports"], [10, 38, 17, 33], [10, 39, 17, 33, "__INTERNAL_VIEW_CONFIG"], [10, 61, 17, 33], [10, 64, 15, 0], [11, 4, 15, 0, "uiViewClassName"], [11, 19, 17, 33], [11, 21, 15, 0], [11, 38, 17, 33], [12, 4, 15, 0, "validAttributes"], [12, 19, 17, 33], [12, 21, 15, 0], [13, 6, 15, 0, "mode"], [13, 10, 17, 33], [13, 12, 15, 0], [13, 16, 17, 33], [14, 6, 15, 0, "edges"], [14, 11, 17, 33], [14, 13, 15, 0], [15, 4, 17, 32], [16, 2, 17, 32], [16, 3, 17, 33], [17, 2, 17, 33], [17, 6, 17, 33, "_default"], [17, 14, 17, 33], [17, 17, 17, 33, "exports"], [17, 24, 17, 33], [17, 25, 17, 33, "default"], [17, 32, 17, 33], [17, 35, 15, 0, "NativeComponentRegistry"], [17, 58, 17, 33], [17, 59, 15, 0, "get"], [17, 62, 17, 33], [17, 63, 15, 0, "nativeComponentName"], [17, 82, 17, 33], [17, 84, 15, 0], [17, 90, 15, 0, "__INTERNAL_VIEW_CONFIG"], [17, 112, 17, 32], [17, 113, 17, 33], [18, 0, 17, 33], [18, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}