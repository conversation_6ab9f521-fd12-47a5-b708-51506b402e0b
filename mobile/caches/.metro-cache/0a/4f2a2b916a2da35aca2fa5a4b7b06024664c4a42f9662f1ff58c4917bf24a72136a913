{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/extends", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "yLIpKqfSeOZo7yhmpj6jeRbKj/A=", "exportNames": ["*"]}}, {"name": "./AppContainer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 290}, "end": {"line": 12, "column": 42, "index": 332}}], "key": "K8Zpy/fyhPoc3DCEhjexwVpeeaw=", "exportNames": ["*"]}}, {"name": "fbjs/lib/invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 333}, "end": {"line": 13, "column": 43, "index": 376}}], "key": "bGUa+dDG2WEhPiIlobT3urS95UE=", "exportNames": ["*"]}}, {"name": "../render", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 377}, "end": {"line": 14, "column": 44, "index": 421}}], "key": "gEsJzwuhgQpR/lhjh20t7zfe8Ho=", "exportNames": ["*"]}}, {"name": "../StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 422}, "end": {"line": 15, "column": 39, "index": 461}}], "key": "Pz10tXyA/z/1zTYUTTxDDbnOtjE=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 462}, "end": {"line": 16, "column": 26, "index": 488}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = renderApplication;\n  exports.getApplication = getApplication;\n  var _extends2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/extends\"));\n  var _AppContainer = _interopRequireDefault(require(_dependencyMap[2], \"./AppContainer\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[3], \"fbjs/lib/invariant\"));\n  var _render = _interopRequireWildcard(require(_dependencyMap[4], \"../render\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"../StyleSheet\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[6], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Copyright (c) Nicolas Gallagher.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  function renderApplication(RootComponent, WrapperComponent, callback, options) {\n    var shouldHydrate = options.hydrate,\n      initialProps = options.initialProps,\n      rootTag = options.rootTag;\n    var renderFn = shouldHydrate ? _render.hydrate : _render.default;\n    (0, _invariant.default)(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);\n    return renderFn(/*#__PURE__*/_react.default.createElement(_AppContainer.default, {\n      WrapperComponent: WrapperComponent,\n      ref: callback,\n      rootTag: rootTag\n    }, /*#__PURE__*/_react.default.createElement(RootComponent, initialProps)), rootTag);\n  }\n  function getApplication(RootComponent, initialProps, WrapperComponent) {\n    var element = /*#__PURE__*/_react.default.createElement(_AppContainer.default, {\n      WrapperComponent: WrapperComponent,\n      rootTag: {}\n    }, /*#__PURE__*/_react.default.createElement(RootComponent, initialProps));\n    // Don't escape CSS text\n    var getStyleElement = props => {\n      var sheet = _StyleSheet.default.getSheet();\n      return /*#__PURE__*/_react.default.createElement(\"style\", (0, _extends2.default)({}, props, {\n        dangerouslySetInnerHTML: {\n          __html: sheet.textContent\n        },\n        id: sheet.id\n      }));\n    };\n    return {\n      element,\n      getStyleElement\n    };\n  }\n});", "lineCount": 57, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_extends2"], [8, 15, 1, 0], [8, 18, 1, 0, "_interopRequireDefault"], [8, 40, 1, 0], [8, 41, 1, 0, "require"], [8, 48, 1, 0], [8, 49, 1, 0, "_dependencyMap"], [8, 63, 1, 0], [9, 2, 12, 0], [9, 6, 12, 0, "_AppContainer"], [9, 19, 12, 0], [9, 22, 12, 0, "_interopRequireDefault"], [9, 44, 12, 0], [9, 45, 12, 0, "require"], [9, 52, 12, 0], [9, 53, 12, 0, "_dependencyMap"], [9, 67, 12, 0], [10, 2, 13, 0], [10, 6, 13, 0, "_invariant"], [10, 16, 13, 0], [10, 19, 13, 0, "_interopRequireDefault"], [10, 41, 13, 0], [10, 42, 13, 0, "require"], [10, 49, 13, 0], [10, 50, 13, 0, "_dependencyMap"], [10, 64, 13, 0], [11, 2, 14, 0], [11, 6, 14, 0, "_render"], [11, 13, 14, 0], [11, 16, 14, 0, "_interopRequireWildcard"], [11, 39, 14, 0], [11, 40, 14, 0, "require"], [11, 47, 14, 0], [11, 48, 14, 0, "_dependencyMap"], [11, 62, 14, 0], [12, 2, 15, 0], [12, 6, 15, 0, "_StyleSheet"], [12, 17, 15, 0], [12, 20, 15, 0, "_interopRequireDefault"], [12, 42, 15, 0], [12, 43, 15, 0, "require"], [12, 50, 15, 0], [12, 51, 15, 0, "_dependencyMap"], [12, 65, 15, 0], [13, 2, 16, 0], [13, 6, 16, 0, "_react"], [13, 12, 16, 0], [13, 15, 16, 0, "_interopRequireDefault"], [13, 37, 16, 0], [13, 38, 16, 0, "require"], [13, 45, 16, 0], [13, 46, 16, 0, "_dependencyMap"], [13, 60, 16, 0], [14, 2, 16, 26], [14, 11, 16, 26, "_interopRequireWildcard"], [14, 35, 16, 26, "e"], [14, 36, 16, 26], [14, 38, 16, 26, "t"], [14, 39, 16, 26], [14, 68, 16, 26, "WeakMap"], [14, 75, 16, 26], [14, 81, 16, 26, "r"], [14, 82, 16, 26], [14, 89, 16, 26, "WeakMap"], [14, 96, 16, 26], [14, 100, 16, 26, "n"], [14, 101, 16, 26], [14, 108, 16, 26, "WeakMap"], [14, 115, 16, 26], [14, 127, 16, 26, "_interopRequireWildcard"], [14, 150, 16, 26], [14, 162, 16, 26, "_interopRequireWildcard"], [14, 163, 16, 26, "e"], [14, 164, 16, 26], [14, 166, 16, 26, "t"], [14, 167, 16, 26], [14, 176, 16, 26, "t"], [14, 177, 16, 26], [14, 181, 16, 26, "e"], [14, 182, 16, 26], [14, 186, 16, 26, "e"], [14, 187, 16, 26], [14, 188, 16, 26, "__esModule"], [14, 198, 16, 26], [14, 207, 16, 26, "e"], [14, 208, 16, 26], [14, 214, 16, 26, "o"], [14, 215, 16, 26], [14, 217, 16, 26, "i"], [14, 218, 16, 26], [14, 220, 16, 26, "f"], [14, 221, 16, 26], [14, 226, 16, 26, "__proto__"], [14, 235, 16, 26], [14, 243, 16, 26, "default"], [14, 250, 16, 26], [14, 252, 16, 26, "e"], [14, 253, 16, 26], [14, 270, 16, 26, "e"], [14, 271, 16, 26], [14, 294, 16, 26, "e"], [14, 295, 16, 26], [14, 320, 16, 26, "e"], [14, 321, 16, 26], [14, 330, 16, 26, "f"], [14, 331, 16, 26], [14, 337, 16, 26, "o"], [14, 338, 16, 26], [14, 341, 16, 26, "t"], [14, 342, 16, 26], [14, 345, 16, 26, "n"], [14, 346, 16, 26], [14, 349, 16, 26, "r"], [14, 350, 16, 26], [14, 358, 16, 26, "o"], [14, 359, 16, 26], [14, 360, 16, 26, "has"], [14, 363, 16, 26], [14, 364, 16, 26, "e"], [14, 365, 16, 26], [14, 375, 16, 26, "o"], [14, 376, 16, 26], [14, 377, 16, 26, "get"], [14, 380, 16, 26], [14, 381, 16, 26, "e"], [14, 382, 16, 26], [14, 385, 16, 26, "o"], [14, 386, 16, 26], [14, 387, 16, 26, "set"], [14, 390, 16, 26], [14, 391, 16, 26, "e"], [14, 392, 16, 26], [14, 394, 16, 26, "f"], [14, 395, 16, 26], [14, 411, 16, 26, "t"], [14, 412, 16, 26], [14, 416, 16, 26, "e"], [14, 417, 16, 26], [14, 433, 16, 26, "t"], [14, 434, 16, 26], [14, 441, 16, 26, "hasOwnProperty"], [14, 455, 16, 26], [14, 456, 16, 26, "call"], [14, 460, 16, 26], [14, 461, 16, 26, "e"], [14, 462, 16, 26], [14, 464, 16, 26, "t"], [14, 465, 16, 26], [14, 472, 16, 26, "i"], [14, 473, 16, 26], [14, 477, 16, 26, "o"], [14, 478, 16, 26], [14, 481, 16, 26, "Object"], [14, 487, 16, 26], [14, 488, 16, 26, "defineProperty"], [14, 502, 16, 26], [14, 507, 16, 26, "Object"], [14, 513, 16, 26], [14, 514, 16, 26, "getOwnPropertyDescriptor"], [14, 538, 16, 26], [14, 539, 16, 26, "e"], [14, 540, 16, 26], [14, 542, 16, 26, "t"], [14, 543, 16, 26], [14, 550, 16, 26, "i"], [14, 551, 16, 26], [14, 552, 16, 26, "get"], [14, 555, 16, 26], [14, 559, 16, 26, "i"], [14, 560, 16, 26], [14, 561, 16, 26, "set"], [14, 564, 16, 26], [14, 568, 16, 26, "o"], [14, 569, 16, 26], [14, 570, 16, 26, "f"], [14, 571, 16, 26], [14, 573, 16, 26, "t"], [14, 574, 16, 26], [14, 576, 16, 26, "i"], [14, 577, 16, 26], [14, 581, 16, 26, "f"], [14, 582, 16, 26], [14, 583, 16, 26, "t"], [14, 584, 16, 26], [14, 588, 16, 26, "e"], [14, 589, 16, 26], [14, 590, 16, 26, "t"], [14, 591, 16, 26], [14, 602, 16, 26, "f"], [14, 603, 16, 26], [14, 608, 16, 26, "e"], [14, 609, 16, 26], [14, 611, 16, 26, "t"], [14, 612, 16, 26], [15, 2, 2, 0], [16, 0, 3, 0], [17, 0, 4, 0], [18, 0, 5, 0], [19, 0, 6, 0], [20, 0, 7, 0], [21, 0, 8, 0], [22, 0, 9, 0], [23, 0, 10, 0], [25, 2, 17, 15], [25, 11, 17, 24, "renderApplication"], [25, 28, 17, 41, "renderApplication"], [25, 29, 17, 42, "RootComponent"], [25, 42, 17, 55], [25, 44, 17, 57, "WrapperComponent"], [25, 60, 17, 73], [25, 62, 17, 75, "callback"], [25, 70, 17, 83], [25, 72, 17, 85, "options"], [25, 79, 17, 92], [25, 81, 17, 94], [26, 4, 18, 2], [26, 8, 18, 6, "shouldHydrate"], [26, 21, 18, 19], [26, 24, 18, 22, "options"], [26, 31, 18, 29], [26, 32, 18, 30, "hydrate"], [26, 39, 18, 37], [27, 6, 19, 4, "initialProps"], [27, 18, 19, 16], [27, 21, 19, 19, "options"], [27, 28, 19, 26], [27, 29, 19, 27, "initialProps"], [27, 41, 19, 39], [28, 6, 20, 4, "rootTag"], [28, 13, 20, 11], [28, 16, 20, 14, "options"], [28, 23, 20, 21], [28, 24, 20, 22, "rootTag"], [28, 31, 20, 29], [29, 4, 21, 2], [29, 8, 21, 6, "renderFn"], [29, 16, 21, 14], [29, 19, 21, 17, "shouldHydrate"], [29, 32, 21, 30], [29, 35, 21, 33, "hydrate"], [29, 50, 21, 40], [29, 53, 21, 43, "render"], [29, 68, 21, 49], [30, 4, 22, 2], [30, 8, 22, 2, "invariant"], [30, 26, 22, 11], [30, 28, 22, 12, "rootTag"], [30, 35, 22, 19], [30, 37, 22, 21], [30, 83, 22, 67], [30, 85, 22, 69, "rootTag"], [30, 92, 22, 76], [30, 93, 22, 77], [31, 4, 23, 2], [31, 11, 23, 9, "renderFn"], [31, 19, 23, 17], [31, 20, 23, 18], [31, 33, 23, 31, "React"], [31, 47, 23, 36], [31, 48, 23, 37, "createElement"], [31, 61, 23, 50], [31, 62, 23, 51, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [31, 83, 23, 63], [31, 85, 23, 65], [32, 6, 24, 4, "WrapperComponent"], [32, 22, 24, 20], [32, 24, 24, 22, "WrapperComponent"], [32, 40, 24, 38], [33, 6, 25, 4, "ref"], [33, 9, 25, 7], [33, 11, 25, 9, "callback"], [33, 19, 25, 17], [34, 6, 26, 4, "rootTag"], [34, 13, 26, 11], [34, 15, 26, 13, "rootTag"], [35, 4, 27, 2], [35, 5, 27, 3], [35, 7, 27, 5], [35, 20, 27, 18, "React"], [35, 34, 27, 23], [35, 35, 27, 24, "createElement"], [35, 48, 27, 37], [35, 49, 27, 38, "RootComponent"], [35, 62, 27, 51], [35, 64, 27, 53, "initialProps"], [35, 76, 27, 65], [35, 77, 27, 66], [35, 78, 27, 67], [35, 80, 27, 69, "rootTag"], [35, 87, 27, 76], [35, 88, 27, 77], [36, 2, 28, 0], [37, 2, 29, 7], [37, 11, 29, 16, "getApplication"], [37, 25, 29, 30, "getApplication"], [37, 26, 29, 31, "RootComponent"], [37, 39, 29, 44], [37, 41, 29, 46, "initialProps"], [37, 53, 29, 58], [37, 55, 29, 60, "WrapperComponent"], [37, 71, 29, 76], [37, 73, 29, 78], [38, 4, 30, 2], [38, 8, 30, 6, "element"], [38, 15, 30, 13], [38, 18, 30, 16], [38, 31, 30, 29, "React"], [38, 45, 30, 34], [38, 46, 30, 35, "createElement"], [38, 59, 30, 48], [38, 60, 30, 49, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [38, 81, 30, 61], [38, 83, 30, 63], [39, 6, 31, 4, "WrapperComponent"], [39, 22, 31, 20], [39, 24, 31, 22, "WrapperComponent"], [39, 40, 31, 38], [40, 6, 32, 4, "rootTag"], [40, 13, 32, 11], [40, 15, 32, 13], [40, 16, 32, 14], [41, 4, 33, 2], [41, 5, 33, 3], [41, 7, 33, 5], [41, 20, 33, 18, "React"], [41, 34, 33, 23], [41, 35, 33, 24, "createElement"], [41, 48, 33, 37], [41, 49, 33, 38, "RootComponent"], [41, 62, 33, 51], [41, 64, 33, 53, "initialProps"], [41, 76, 33, 65], [41, 77, 33, 66], [41, 78, 33, 67], [42, 4, 34, 2], [43, 4, 35, 2], [43, 8, 35, 6, "getStyleElement"], [43, 23, 35, 21], [43, 26, 35, 24, "props"], [43, 31, 35, 29], [43, 35, 35, 33], [44, 6, 36, 4], [44, 10, 36, 8, "sheet"], [44, 15, 36, 13], [44, 18, 36, 16, "StyleSheet"], [44, 37, 36, 26], [44, 38, 36, 27, "getSheet"], [44, 46, 36, 35], [44, 47, 36, 36], [44, 48, 36, 37], [45, 6, 37, 4], [45, 13, 37, 11], [45, 26, 37, 24, "React"], [45, 40, 37, 29], [45, 41, 37, 30, "createElement"], [45, 54, 37, 43], [45, 55, 37, 44], [45, 62, 37, 51], [45, 64, 37, 53], [45, 68, 37, 53, "_extends"], [45, 85, 37, 61], [45, 87, 37, 62], [45, 88, 37, 63], [45, 89, 37, 64], [45, 91, 37, 66, "props"], [45, 96, 37, 71], [45, 98, 37, 73], [46, 8, 38, 6, "dangerouslySetInnerHTML"], [46, 31, 38, 29], [46, 33, 38, 31], [47, 10, 39, 8, "__html"], [47, 16, 39, 14], [47, 18, 39, 16, "sheet"], [47, 23, 39, 21], [47, 24, 39, 22, "textContent"], [48, 8, 40, 6], [48, 9, 40, 7], [49, 8, 41, 6, "id"], [49, 10, 41, 8], [49, 12, 41, 10, "sheet"], [49, 17, 41, 15], [49, 18, 41, 16, "id"], [50, 6, 42, 4], [50, 7, 42, 5], [50, 8, 42, 6], [50, 9, 42, 7], [51, 4, 43, 2], [51, 5, 43, 3], [52, 4, 44, 2], [52, 11, 44, 9], [53, 6, 45, 4, "element"], [53, 13, 45, 11], [54, 6, 46, 4, "getStyleElement"], [55, 4, 47, 2], [55, 5, 47, 3], [56, 2, 48, 0], [57, 0, 48, 1], [57, 3]], "functionMap": {"names": ["<global>", "renderApplication", "getApplication", "getStyleElement"], "mappings": "AAA;eCgB;CDW;OEC;wBCM;GDQ"}}, "type": "js/module"}]}