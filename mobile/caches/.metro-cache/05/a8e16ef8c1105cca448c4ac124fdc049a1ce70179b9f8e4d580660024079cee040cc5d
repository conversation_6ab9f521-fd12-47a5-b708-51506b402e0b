{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _View = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/View\"));\n  const ScreenContentWrapper = _View.default;\n  var _default = exports.default = ScreenContentWrapper;\n});", "lineCount": 10, "map": [[8, 2, 2, 0], [8, 8, 2, 6, "ScreenContentWrapper"], [8, 28, 2, 26], [8, 31, 2, 29, "View"], [8, 44, 2, 33], [9, 2, 2, 34], [9, 6, 2, 34, "_default"], [9, 14, 2, 34], [9, 17, 2, 34, "exports"], [9, 24, 2, 34], [9, 25, 2, 34, "default"], [9, 32, 2, 34], [9, 35, 3, 15, "ScreenContentWrapper"], [9, 55, 3, 35], [10, 0, 3, 35], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}