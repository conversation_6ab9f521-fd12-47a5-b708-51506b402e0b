{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports[\"default\"] = isPrefixedValue;\n  var RE = /-webkit-|-moz-|-ms-/;\n  function isPrefixedValue(value) {\n    return typeof value === 'string' && RE.test(value);\n  }\n});", "lineCount": 12, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8], [7, 19, 6, 17], [7, 20, 6, 18], [7, 23, 6, 21, "isPrefixedValue"], [7, 38, 6, 36], [8, 2, 7, 0], [8, 6, 7, 4, "RE"], [8, 8, 7, 6], [8, 11, 7, 9], [8, 32, 7, 30], [9, 2, 9, 0], [9, 11, 9, 9, "isPrefixedValue"], [9, 26, 9, 24, "isPrefixedValue"], [9, 27, 9, 25, "value"], [9, 32, 9, 30], [9, 34, 9, 32], [10, 4, 10, 2], [10, 11, 10, 9], [10, 18, 10, 16, "value"], [10, 23, 10, 21], [10, 28, 10, 26], [10, 36, 10, 34], [10, 40, 10, 38, "RE"], [10, 42, 10, 40], [10, 43, 10, 41, "test"], [10, 47, 10, 45], [10, 48, 10, 46, "value"], [10, 53, 10, 51], [10, 54, 10, 52], [11, 2, 11, 0], [12, 0, 11, 1], [12, 3]], "functionMap": {"names": ["<global>", "isPrefixedValue"], "mappings": "AAA;ACQ"}}, "type": "js/module"}]}