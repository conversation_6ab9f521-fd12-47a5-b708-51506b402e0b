{"dependencies": [{"name": "../logger/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 44, "index": 59}}], "key": "pBiviTeVoxyQBwxnAV5OZFjetV0=", "exportNames": ["*"]}}, {"name": "./util.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 60}, "end": {"line": 4, "column": 96, "index": 156}}], "key": "+UpHPazG/Yk8JnTjB6d2Eo+vUl4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withClamp = void 0;\n  var _index = require(_dependencyMap[0], \"../logger/index.js\");\n  var _util = require(_dependencyMap[1], \"./util.js\");\n  const _worklet_11241294755947_init_data = {\n    code: \"function reactNativeReanimated_clampJs1(config,_animationToClamp){const{defineAnimation,recognizePrefixSuffix,logger,getReduceMotionForAnimation}=this.__closure;return defineAnimation(_animationToClamp,function(){'worklet';const animationToClamp=typeof _animationToClamp==='function'?_animationToClamp():_animationToClamp;const strippedMin=config.min===undefined?undefined:recognizePrefixSuffix(config.min).strippedValue;const strippedMax=config.max===undefined?undefined:recognizePrefixSuffix(config.max).strippedValue;function clampOnFrame(animation,now){const finished=animationToClamp.onFrame(animationToClamp,now);if(animationToClamp.current===undefined){logger.warn(\\\"Error inside 'withClamp' animation, the inner animation has invalid current value\\\");return true;}else{const{prefix:prefix,strippedValue:strippedValue,suffix:suffix}=recognizePrefixSuffix(animationToClamp.current);let newValue;if(strippedMax!==undefined&&strippedMax<strippedValue){newValue=strippedMax;}else if(strippedMin!==undefined&&strippedMin>strippedValue){newValue=strippedMin;}else{newValue=strippedValue;}animation.current=typeof animationToClamp.current==='number'?newValue:\\\"\\\"+(prefix===undefined?'':prefix)+newValue+(suffix===undefined?'':suffix);}return finished;}function onStart(animation,value,now,previousAnimation){animation.current=value;animation.previousAnimation=animationToClamp;const animationBeforeClamped=previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.previousAnimation;if(config.max!==undefined&&config.min!==undefined&&config.max<config.min){logger.warn('Wrong config was provided to withClamp. Min value is bigger than max');}animationToClamp.onStart(animationToClamp,(animationBeforeClamped===null||animationBeforeClamped===void 0?void 0:animationBeforeClamped.current)||value,now,animationBeforeClamped);}const callback=function(finished){if(animationToClamp.callback){animationToClamp.callback(finished);}};return{isHigherOrder:true,onFrame:clampOnFrame,onStart:onStart,current:animationToClamp.current,callback:callback,previousAnimation:null,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/clamp.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_clampJs1\\\",\\\"config\\\",\\\"_animationToClamp\\\",\\\"defineAnimation\\\",\\\"recognizePrefixSuffix\\\",\\\"logger\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"animationToClamp\\\",\\\"strippedMin\\\",\\\"min\\\",\\\"undefined\\\",\\\"strippedValue\\\",\\\"strippedMax\\\",\\\"max\\\",\\\"clampOnFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"finished\\\",\\\"onFrame\\\",\\\"current\\\",\\\"warn\\\",\\\"prefix\\\",\\\"suffix\\\",\\\"newValue\\\",\\\"onStart\\\",\\\"value\\\",\\\"previousAnimation\\\",\\\"animationBeforeClamped\\\",\\\"callback\\\",\\\"isHigherOrder\\\",\\\"reduceMotion\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/clamp.js\\\"],\\\"mappings\\\":\\\"AAIyB,SAAAA,8BAAqCA,CAAAC,MAAA,CAAAC,iBAAA,QAAAC,eAAA,CAAAC,qBAAA,CAAAC,MAAA,CAAAC,2BAAA,OAAAC,SAAA,CAG5D,MAAO,CAAAJ,eAAe,CAACD,iBAAiB,CAAE,UAAM,CAC9C,SAAS,CAET,KAAM,CAAAM,gBAAgB,CAAG,MAAO,CAAAN,iBAAiB,GAAK,UAAU,CAAGA,iBAAiB,CAAC,CAAC,CAAGA,iBAAiB,CAC1G,KAAM,CAAAO,WAAW,CAAGR,MAAM,CAACS,GAAG,GAAKC,SAAS,CAAGA,SAAS,CAAGP,qBAAqB,CAACH,MAAM,CAACS,GAAG,CAAC,CAACE,aAAa,CAC1G,KAAM,CAAAC,WAAW,CAAGZ,MAAM,CAACa,GAAG,GAAKH,SAAS,CAAGA,SAAS,CAAGP,qBAAqB,CAACH,MAAM,CAACa,GAAG,CAAC,CAACF,aAAa,CAC1G,QAAS,CAAAG,YAAYA,CAACC,SAAS,CAAEC,GAAG,CAAE,CACpC,KAAM,CAAAC,QAAQ,CAAGV,gBAAgB,CAACW,OAAO,CAACX,gBAAgB,CAAES,GAAG,CAAC,CAChE,GAAIT,gBAAgB,CAACY,OAAO,GAAKT,SAAS,CAAE,CAC1CN,MAAM,CAACgB,IAAI,CAAC,mFAAmF,CAAC,CAChG,MAAO,KAAI,CACb,CAAC,IAAM,CACL,KAAM,CACJC,MAAM,CAANA,MAAM,CACNV,aAAa,CAAbA,aAAa,CACbW,MAAA,CAAAA,MACF,CAAC,CAAGnB,qBAAqB,CAACI,gBAAgB,CAACY,OAAO,CAAC,CACnD,GAAI,CAAAI,QAAQ,CACZ,GAAIX,WAAW,GAAKF,SAAS,EAAIE,WAAW,CAAGD,aAAa,CAAE,CAC5DY,QAAQ,CAAGX,WAAW,CACxB,CAAC,IAAM,IAAIJ,WAAW,GAAKE,SAAS,EAAIF,WAAW,CAAGG,aAAa,CAAE,CACnEY,QAAQ,CAAGf,WAAW,CACxB,CAAC,IAAM,CACLe,QAAQ,CAAGZ,aAAa,CAC1B,CACAI,SAAS,CAACI,OAAO,CAAG,MAAO,CAAAZ,gBAAgB,CAACY,OAAO,GAAK,QAAQ,CAAGI,QAAQ,KAAMF,MAAM,GAAKX,SAAS,CAAG,EAAE,CAAGW,MAAM,EAAGE,QAAQ,EAAGD,MAAM,GAAKZ,SAAS,CAAG,EAAE,CAAGY,MAAM,CAAE,CACvK,CACA,MAAO,CAAAL,QAAQ,CACjB,CACA,QAAS,CAAAO,OAAOA,CAACT,SAAS,CAAEU,KAAK,CAAET,GAAG,CAAEU,iBAAiB,CAAE,CACzDX,SAAS,CAACI,OAAO,CAAGM,KAAK,CACzBV,SAAS,CAACW,iBAAiB,CAAGnB,gBAAgB,CAC9C,KAAM,CAAAoB,sBAAsB,CAAGD,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEA,iBAAiB,CACnE,GAAI1B,MAAM,CAACa,GAAG,GAAKH,SAAS,EAAIV,MAAM,CAACS,GAAG,GAAKC,SAAS,EAAIV,MAAM,CAACa,GAAG,CAAGb,MAAM,CAACS,GAAG,CAAE,CACnFL,MAAM,CAACgB,IAAI,CAAC,sEAAsE,CAAC,CACrF,CACAb,gBAAgB,CAACiB,OAAO,CAACjB,gBAAgB,CAKzC,CAAAoB,sBAAsB,SAAtBA,sBAAsB,iBAAtBA,sBAAsB,CAAER,OAAO,GAAIM,KAAK,CAAET,GAAG,CAAEW,sBAAsB,CAAC,CACxE,CACA,KAAM,CAAAC,QAAQ,CAAG,QAAAA,CAAAX,QAAQ,CAAI,CAC3B,GAAIV,gBAAgB,CAACqB,QAAQ,CAAE,CAC7BrB,gBAAgB,CAACqB,QAAQ,CAACX,QAAQ,CAAC,CACrC,CACF,CAAC,CACD,MAAO,CACLY,aAAa,CAAE,IAAI,CACnBX,OAAO,CAAEJ,YAAY,CACrBU,OAAO,CAAPA,OAAO,CACPL,OAAO,CAAEZ,gBAAgB,CAACY,OAAO,CACjCS,QAAQ,CAARA,QAAQ,CACRF,iBAAiB,CAAE,IAAI,CACvBI,YAAY,CAAEzB,2BAA2B,CAACL,MAAM,CAAC8B,YAAY,CAC/D,CAAC,CACH,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_15049981710423_init_data = {\n    code: \"function reactNativeReanimated_clampJs2(){const{_animationToClamp,config,recognizePrefixSuffix,logger,getReduceMotionForAnimation}=this.__closure;const animationToClamp=typeof _animationToClamp==='function'?_animationToClamp():_animationToClamp;const strippedMin=config.min===undefined?undefined:recognizePrefixSuffix(config.min).strippedValue;const strippedMax=config.max===undefined?undefined:recognizePrefixSuffix(config.max).strippedValue;function clampOnFrame(animation,now){const finished=animationToClamp.onFrame(animationToClamp,now);if(animationToClamp.current===undefined){logger.warn(\\\"Error inside 'withClamp' animation, the inner animation has invalid current value\\\");return true;}else{const{prefix:prefix,strippedValue:strippedValue,suffix:suffix}=recognizePrefixSuffix(animationToClamp.current);let newValue;if(strippedMax!==undefined&&strippedMax<strippedValue){newValue=strippedMax;}else if(strippedMin!==undefined&&strippedMin>strippedValue){newValue=strippedMin;}else{newValue=strippedValue;}animation.current=typeof animationToClamp.current==='number'?newValue:\\\"\\\"+(prefix===undefined?'':prefix)+newValue+(suffix===undefined?'':suffix);}return finished;}function onStart(animation,value,now,previousAnimation){animation.current=value;animation.previousAnimation=animationToClamp;const animationBeforeClamped=previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.previousAnimation;if(config.max!==undefined&&config.min!==undefined&&config.max<config.min){logger.warn('Wrong config was provided to withClamp. Min value is bigger than max');}animationToClamp.onStart(animationToClamp,(animationBeforeClamped===null||animationBeforeClamped===void 0?void 0:animationBeforeClamped.current)||value,now,animationBeforeClamped);}const callback=function(finished){if(animationToClamp.callback){animationToClamp.callback(finished);}};return{isHigherOrder:true,onFrame:clampOnFrame,onStart:onStart,current:animationToClamp.current,callback:callback,previousAnimation:null,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/clamp.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_clampJs2\\\",\\\"_animationToClamp\\\",\\\"config\\\",\\\"recognizePrefixSuffix\\\",\\\"logger\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"animationToClamp\\\",\\\"strippedMin\\\",\\\"min\\\",\\\"undefined\\\",\\\"strippedValue\\\",\\\"strippedMax\\\",\\\"max\\\",\\\"clampOnFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"finished\\\",\\\"onFrame\\\",\\\"current\\\",\\\"warn\\\",\\\"prefix\\\",\\\"suffix\\\",\\\"newValue\\\",\\\"onStart\\\",\\\"value\\\",\\\"previousAnimation\\\",\\\"animationBeforeClamped\\\",\\\"callback\\\",\\\"isHigherOrder\\\",\\\"reduceMotion\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/clamp.js\\\"],\\\"mappings\\\":\\\"AAO4C,SAAAA,8BAAMA,CAAA,QAAAC,iBAAA,CAAAC,MAAA,CAAAC,qBAAA,CAAAC,MAAA,CAAAC,2BAAA,OAAAC,SAAA,CAG9C,KAAM,CAAAC,gBAAgB,CAAG,MAAO,CAAAN,iBAAiB,GAAK,UAAU,CAAGA,iBAAiB,CAAC,CAAC,CAAGA,iBAAiB,CAC1G,KAAM,CAAAO,WAAW,CAAGN,MAAM,CAACO,GAAG,GAAKC,SAAS,CAAGA,SAAS,CAAGP,qBAAqB,CAACD,MAAM,CAACO,GAAG,CAAC,CAACE,aAAa,CAC1G,KAAM,CAAAC,WAAW,CAAGV,MAAM,CAACW,GAAG,GAAKH,SAAS,CAAGA,SAAS,CAAGP,qBAAqB,CAACD,MAAM,CAACW,GAAG,CAAC,CAACF,aAAa,CAC1G,QAAS,CAAAG,YAAYA,CAACC,SAAS,CAAEC,GAAG,CAAE,CACpC,KAAM,CAAAC,QAAQ,CAAGV,gBAAgB,CAACW,OAAO,CAACX,gBAAgB,CAAES,GAAG,CAAC,CAChE,GAAIT,gBAAgB,CAACY,OAAO,GAAKT,SAAS,CAAE,CAC1CN,MAAM,CAACgB,IAAI,CAAC,mFAAmF,CAAC,CAChG,MAAO,KAAI,CACb,CAAC,IAAM,CACL,KAAM,CACJC,MAAM,CAANA,MAAM,CACNV,aAAa,CAAbA,aAAa,CACbW,MAAA,CAAAA,MACF,CAAC,CAAGnB,qBAAqB,CAACI,gBAAgB,CAACY,OAAO,CAAC,CACnD,GAAI,CAAAI,QAAQ,CACZ,GAAIX,WAAW,GAAKF,SAAS,EAAIE,WAAW,CAAGD,aAAa,CAAE,CAC5DY,QAAQ,CAAGX,WAAW,CACxB,CAAC,IAAM,IAAIJ,WAAW,GAAKE,SAAS,EAAIF,WAAW,CAAGG,aAAa,CAAE,CACnEY,QAAQ,CAAGf,WAAW,CACxB,CAAC,IAAM,CACLe,QAAQ,CAAGZ,aAAa,CAC1B,CACAI,SAAS,CAACI,OAAO,CAAG,MAAO,CAAAZ,gBAAgB,CAACY,OAAO,GAAK,QAAQ,CAAGI,QAAQ,KAAMF,MAAM,GAAKX,SAAS,CAAG,EAAE,CAAGW,MAAM,EAAGE,QAAQ,EAAGD,MAAM,GAAKZ,SAAS,CAAG,EAAE,CAAGY,MAAM,CAAE,CACvK,CACA,MAAO,CAAAL,QAAQ,CACjB,CACA,QAAS,CAAAO,OAAOA,CAACT,SAAS,CAAEU,KAAK,CAAET,GAAG,CAAEU,iBAAiB,CAAE,CACzDX,SAAS,CAACI,OAAO,CAAGM,KAAK,CACzBV,SAAS,CAACW,iBAAiB,CAAGnB,gBAAgB,CAC9C,KAAM,CAAAoB,sBAAsB,CAAGD,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEA,iBAAiB,CACnE,GAAIxB,MAAM,CAACW,GAAG,GAAKH,SAAS,EAAIR,MAAM,CAACO,GAAG,GAAKC,SAAS,EAAIR,MAAM,CAACW,GAAG,CAAGX,MAAM,CAACO,GAAG,CAAE,CACnFL,MAAM,CAACgB,IAAI,CAAC,sEAAsE,CAAC,CACrF,CACAb,gBAAgB,CAACiB,OAAO,CAACjB,gBAAgB,CAKzC,CAAAoB,sBAAsB,SAAtBA,sBAAsB,iBAAtBA,sBAAsB,CAAER,OAAO,GAAIM,KAAK,CAAET,GAAG,CAAEW,sBAAsB,CAAC,CACxE,CACA,KAAM,CAAAC,QAAQ,CAAG,QAAAA,CAAAX,QAAQ,CAAI,CAC3B,GAAIV,gBAAgB,CAACqB,QAAQ,CAAE,CAC7BrB,gBAAgB,CAACqB,QAAQ,CAACX,QAAQ,CAAC,CACrC,CACF,CAAC,CACD,MAAO,CACLY,aAAa,CAAE,IAAI,CACnBX,OAAO,CAAEJ,YAAY,CACrBU,OAAO,CAAPA,OAAO,CACPL,OAAO,CAAEZ,gBAAgB,CAACY,OAAO,CACjCS,QAAQ,CAARA,QAAQ,CACRF,iBAAiB,CAAE,IAAI,CACvBI,YAAY,CAAEzB,2BAA2B,CAACH,MAAM,CAAC4B,YAAY,CAC/D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const withClamp = exports.withClamp = function () {\n    const _e = [new global.Error(), -5, -27];\n    const reactNativeReanimated_clampJs1 = function (config, _animationToClamp) {\n      return (0, _util.defineAnimation)(_animationToClamp, function () {\n        const _e = [new global.Error(), -6, -27];\n        const reactNativeReanimated_clampJs2 = function () {\n          const animationToClamp = typeof _animationToClamp === 'function' ? _animationToClamp() : _animationToClamp;\n          const strippedMin = config.min === undefined ? undefined : (0, _util.recognizePrefixSuffix)(config.min).strippedValue;\n          const strippedMax = config.max === undefined ? undefined : (0, _util.recognizePrefixSuffix)(config.max).strippedValue;\n          function clampOnFrame(animation, now) {\n            const finished = animationToClamp.onFrame(animationToClamp, now);\n            if (animationToClamp.current === undefined) {\n              _index.logger.warn(\"Error inside 'withClamp' animation, the inner animation has invalid current value\");\n              return true;\n            } else {\n              const {\n                prefix,\n                strippedValue,\n                suffix\n              } = (0, _util.recognizePrefixSuffix)(animationToClamp.current);\n              let newValue;\n              if (strippedMax !== undefined && strippedMax < strippedValue) {\n                newValue = strippedMax;\n              } else if (strippedMin !== undefined && strippedMin > strippedValue) {\n                newValue = strippedMin;\n              } else {\n                newValue = strippedValue;\n              }\n              animation.current = typeof animationToClamp.current === 'number' ? newValue : `${prefix === undefined ? '' : prefix}${newValue}${suffix === undefined ? '' : suffix}`;\n            }\n            return finished;\n          }\n          function onStart(animation, value, now, previousAnimation) {\n            animation.current = value;\n            animation.previousAnimation = animationToClamp;\n            const animationBeforeClamped = previousAnimation?.previousAnimation;\n            if (config.max !== undefined && config.min !== undefined && config.max < config.min) {\n              _index.logger.warn('Wrong config was provided to withClamp. Min value is bigger than max');\n            }\n            animationToClamp.onStart(animationToClamp,\n            /**\n             * Provide the current value of the previous animation of the clamped\n             * animation so we can animate from the original \"un-truncated\" value\n             */\n            animationBeforeClamped?.current || value, now, animationBeforeClamped);\n          }\n          const callback = finished => {\n            if (animationToClamp.callback) {\n              animationToClamp.callback(finished);\n            }\n          };\n          return {\n            isHigherOrder: true,\n            onFrame: clampOnFrame,\n            onStart,\n            current: animationToClamp.current,\n            callback,\n            previousAnimation: null,\n            reduceMotion: (0, _util.getReduceMotionForAnimation)(config.reduceMotion)\n          };\n        };\n        reactNativeReanimated_clampJs2.__closure = {\n          _animationToClamp,\n          config,\n          recognizePrefixSuffix: _util.recognizePrefixSuffix,\n          logger: _index.logger,\n          getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n        };\n        reactNativeReanimated_clampJs2.__workletHash = 15049981710423;\n        reactNativeReanimated_clampJs2.__initData = _worklet_15049981710423_init_data;\n        reactNativeReanimated_clampJs2.__stackDetails = _e;\n        return reactNativeReanimated_clampJs2;\n      }());\n    };\n    reactNativeReanimated_clampJs1.__closure = {\n      defineAnimation: _util.defineAnimation,\n      recognizePrefixSuffix: _util.recognizePrefixSuffix,\n      logger: _index.logger,\n      getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n    };\n    reactNativeReanimated_clampJs1.__workletHash = 11241294755947;\n    reactNativeReanimated_clampJs1.__initData = _worklet_11241294755947_init_data;\n    reactNativeReanimated_clampJs1.__stackDetails = _e;\n    return reactNativeReanimated_clampJs1;\n  }();\n});", "lineCount": 107, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "withClamp"], [7, 19, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_index"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_util"], [9, 11, 4, 0], [9, 14, 4, 0, "require"], [9, 21, 4, 0], [9, 22, 4, 0, "_dependencyMap"], [9, 36, 4, 0], [10, 2, 4, 96], [10, 8, 4, 96, "_worklet_11241294755947_init_data"], [10, 41, 4, 96], [11, 4, 4, 96, "code"], [11, 8, 4, 96], [12, 4, 4, 96, "location"], [12, 12, 4, 96], [13, 4, 4, 96, "sourceMap"], [13, 13, 4, 96], [14, 4, 4, 96, "version"], [14, 11, 4, 96], [15, 2, 4, 96], [16, 2, 4, 96], [16, 8, 4, 96, "_worklet_15049981710423_init_data"], [16, 41, 4, 96], [17, 4, 4, 96, "code"], [17, 8, 4, 96], [18, 4, 4, 96, "location"], [18, 12, 4, 96], [19, 4, 4, 96, "sourceMap"], [19, 13, 4, 96], [20, 4, 4, 96, "version"], [20, 11, 4, 96], [21, 2, 4, 96], [22, 2, 5, 7], [22, 8, 5, 13, "withClamp"], [22, 17, 5, 22], [22, 20, 5, 22, "exports"], [22, 27, 5, 22], [22, 28, 5, 22, "withClamp"], [22, 37, 5, 22], [22, 40, 5, 25], [23, 4, 5, 25], [23, 10, 5, 25, "_e"], [23, 12, 5, 25], [23, 20, 5, 25, "global"], [23, 26, 5, 25], [23, 27, 5, 25, "Error"], [23, 32, 5, 25], [24, 4, 5, 25], [24, 10, 5, 25, "reactNativeReanimated_clampJs1"], [24, 40, 5, 25], [24, 52, 5, 25, "reactNativeReanimated_clampJs1"], [24, 53, 5, 35, "config"], [24, 59, 5, 41], [24, 61, 5, 43, "_animationToClamp"], [24, 78, 5, 60], [24, 80, 5, 62], [25, 6, 8, 2], [25, 13, 8, 9], [25, 17, 8, 9, "defineAnimation"], [25, 38, 8, 24], [25, 40, 8, 25, "_animationToClamp"], [25, 57, 8, 42], [25, 59, 8, 44], [26, 8, 8, 44], [26, 14, 8, 44, "_e"], [26, 16, 8, 44], [26, 24, 8, 44, "global"], [26, 30, 8, 44], [26, 31, 8, 44, "Error"], [26, 36, 8, 44], [27, 8, 8, 44], [27, 14, 8, 44, "reactNativeReanimated_clampJs2"], [27, 44, 8, 44], [27, 56, 8, 44, "reactNativeReanimated_clampJs2"], [27, 57, 8, 44], [27, 59, 8, 50], [28, 10, 11, 4], [28, 16, 11, 10, "animationToClamp"], [28, 32, 11, 26], [28, 35, 11, 29], [28, 42, 11, 36, "_animationToClamp"], [28, 59, 11, 53], [28, 64, 11, 58], [28, 74, 11, 68], [28, 77, 11, 71, "_animationToClamp"], [28, 94, 11, 88], [28, 95, 11, 89], [28, 96, 11, 90], [28, 99, 11, 93, "_animationToClamp"], [28, 116, 11, 110], [29, 10, 12, 4], [29, 16, 12, 10, "strippedMin"], [29, 27, 12, 21], [29, 30, 12, 24, "config"], [29, 36, 12, 30], [29, 37, 12, 31, "min"], [29, 40, 12, 34], [29, 45, 12, 39, "undefined"], [29, 54, 12, 48], [29, 57, 12, 51, "undefined"], [29, 66, 12, 60], [29, 69, 12, 63], [29, 73, 12, 63, "recognizePrefixSuffix"], [29, 100, 12, 84], [29, 102, 12, 85, "config"], [29, 108, 12, 91], [29, 109, 12, 92, "min"], [29, 112, 12, 95], [29, 113, 12, 96], [29, 114, 12, 97, "strippedValue"], [29, 127, 12, 110], [30, 10, 13, 4], [30, 16, 13, 10, "strippedMax"], [30, 27, 13, 21], [30, 30, 13, 24, "config"], [30, 36, 13, 30], [30, 37, 13, 31, "max"], [30, 40, 13, 34], [30, 45, 13, 39, "undefined"], [30, 54, 13, 48], [30, 57, 13, 51, "undefined"], [30, 66, 13, 60], [30, 69, 13, 63], [30, 73, 13, 63, "recognizePrefixSuffix"], [30, 100, 13, 84], [30, 102, 13, 85, "config"], [30, 108, 13, 91], [30, 109, 13, 92, "max"], [30, 112, 13, 95], [30, 113, 13, 96], [30, 114, 13, 97, "strippedValue"], [30, 127, 13, 110], [31, 10, 14, 4], [31, 19, 14, 13, "clampOnFrame"], [31, 31, 14, 25, "clampOnFrame"], [31, 32, 14, 26, "animation"], [31, 41, 14, 35], [31, 43, 14, 37, "now"], [31, 46, 14, 40], [31, 48, 14, 42], [32, 12, 15, 6], [32, 18, 15, 12, "finished"], [32, 26, 15, 20], [32, 29, 15, 23, "animationToClamp"], [32, 45, 15, 39], [32, 46, 15, 40, "onFrame"], [32, 53, 15, 47], [32, 54, 15, 48, "animationToClamp"], [32, 70, 15, 64], [32, 72, 15, 66, "now"], [32, 75, 15, 69], [32, 76, 15, 70], [33, 12, 16, 6], [33, 16, 16, 10, "animationToClamp"], [33, 32, 16, 26], [33, 33, 16, 27, "current"], [33, 40, 16, 34], [33, 45, 16, 39, "undefined"], [33, 54, 16, 48], [33, 56, 16, 50], [34, 14, 17, 8, "logger"], [34, 27, 17, 14], [34, 28, 17, 15, "warn"], [34, 32, 17, 19], [34, 33, 17, 20], [34, 116, 17, 103], [34, 117, 17, 104], [35, 14, 18, 8], [35, 21, 18, 15], [35, 25, 18, 19], [36, 12, 19, 6], [36, 13, 19, 7], [36, 19, 19, 13], [37, 14, 20, 8], [37, 20, 20, 14], [38, 16, 21, 10, "prefix"], [38, 22, 21, 16], [39, 16, 22, 10, "strippedValue"], [39, 29, 22, 23], [40, 16, 23, 10, "suffix"], [41, 14, 24, 8], [41, 15, 24, 9], [41, 18, 24, 12], [41, 22, 24, 12, "recognizePrefixSuffix"], [41, 49, 24, 33], [41, 51, 24, 34, "animationToClamp"], [41, 67, 24, 50], [41, 68, 24, 51, "current"], [41, 75, 24, 58], [41, 76, 24, 59], [42, 14, 25, 8], [42, 18, 25, 12, "newValue"], [42, 26, 25, 20], [43, 14, 26, 8], [43, 18, 26, 12, "strippedMax"], [43, 29, 26, 23], [43, 34, 26, 28, "undefined"], [43, 43, 26, 37], [43, 47, 26, 41, "strippedMax"], [43, 58, 26, 52], [43, 61, 26, 55, "strippedValue"], [43, 74, 26, 68], [43, 76, 26, 70], [44, 16, 27, 10, "newValue"], [44, 24, 27, 18], [44, 27, 27, 21, "strippedMax"], [44, 38, 27, 32], [45, 14, 28, 8], [45, 15, 28, 9], [45, 21, 28, 15], [45, 25, 28, 19, "strippedMin"], [45, 36, 28, 30], [45, 41, 28, 35, "undefined"], [45, 50, 28, 44], [45, 54, 28, 48, "strippedMin"], [45, 65, 28, 59], [45, 68, 28, 62, "strippedValue"], [45, 81, 28, 75], [45, 83, 28, 77], [46, 16, 29, 10, "newValue"], [46, 24, 29, 18], [46, 27, 29, 21, "strippedMin"], [46, 38, 29, 32], [47, 14, 30, 8], [47, 15, 30, 9], [47, 21, 30, 15], [48, 16, 31, 10, "newValue"], [48, 24, 31, 18], [48, 27, 31, 21, "strippedValue"], [48, 40, 31, 34], [49, 14, 32, 8], [50, 14, 33, 8, "animation"], [50, 23, 33, 17], [50, 24, 33, 18, "current"], [50, 31, 33, 25], [50, 34, 33, 28], [50, 41, 33, 35, "animationToClamp"], [50, 57, 33, 51], [50, 58, 33, 52, "current"], [50, 65, 33, 59], [50, 70, 33, 64], [50, 78, 33, 72], [50, 81, 33, 75, "newValue"], [50, 89, 33, 83], [50, 92, 33, 86], [50, 95, 33, 89, "prefix"], [50, 101, 33, 95], [50, 106, 33, 100, "undefined"], [50, 115, 33, 109], [50, 118, 33, 112], [50, 120, 33, 114], [50, 123, 33, 117, "prefix"], [50, 129, 33, 123], [50, 132, 33, 126, "newValue"], [50, 140, 33, 134], [50, 143, 33, 137, "suffix"], [50, 149, 33, 143], [50, 154, 33, 148, "undefined"], [50, 163, 33, 157], [50, 166, 33, 160], [50, 168, 33, 162], [50, 171, 33, 165, "suffix"], [50, 177, 33, 171], [50, 179, 33, 173], [51, 12, 34, 6], [52, 12, 35, 6], [52, 19, 35, 13, "finished"], [52, 27, 35, 21], [53, 10, 36, 4], [54, 10, 37, 4], [54, 19, 37, 13, "onStart"], [54, 26, 37, 20, "onStart"], [54, 27, 37, 21, "animation"], [54, 36, 37, 30], [54, 38, 37, 32, "value"], [54, 43, 37, 37], [54, 45, 37, 39, "now"], [54, 48, 37, 42], [54, 50, 37, 44, "previousAnimation"], [54, 67, 37, 61], [54, 69, 37, 63], [55, 12, 38, 6, "animation"], [55, 21, 38, 15], [55, 22, 38, 16, "current"], [55, 29, 38, 23], [55, 32, 38, 26, "value"], [55, 37, 38, 31], [56, 12, 39, 6, "animation"], [56, 21, 39, 15], [56, 22, 39, 16, "previousAnimation"], [56, 39, 39, 33], [56, 42, 39, 36, "animationToClamp"], [56, 58, 39, 52], [57, 12, 40, 6], [57, 18, 40, 12, "animationBeforeClamped"], [57, 40, 40, 34], [57, 43, 40, 37, "previousAnimation"], [57, 60, 40, 54], [57, 62, 40, 56, "previousAnimation"], [57, 79, 40, 73], [58, 12, 41, 6], [58, 16, 41, 10, "config"], [58, 22, 41, 16], [58, 23, 41, 17, "max"], [58, 26, 41, 20], [58, 31, 41, 25, "undefined"], [58, 40, 41, 34], [58, 44, 41, 38, "config"], [58, 50, 41, 44], [58, 51, 41, 45, "min"], [58, 54, 41, 48], [58, 59, 41, 53, "undefined"], [58, 68, 41, 62], [58, 72, 41, 66, "config"], [58, 78, 41, 72], [58, 79, 41, 73, "max"], [58, 82, 41, 76], [58, 85, 41, 79, "config"], [58, 91, 41, 85], [58, 92, 41, 86, "min"], [58, 95, 41, 89], [58, 97, 41, 91], [59, 14, 42, 8, "logger"], [59, 27, 42, 14], [59, 28, 42, 15, "warn"], [59, 32, 42, 19], [59, 33, 42, 20], [59, 103, 42, 90], [59, 104, 42, 91], [60, 12, 43, 6], [61, 12, 44, 6, "animationToClamp"], [61, 28, 44, 22], [61, 29, 44, 23, "onStart"], [61, 36, 44, 30], [61, 37, 44, 31, "animationToClamp"], [61, 53, 44, 47], [62, 12, 45, 6], [63, 0, 46, 0], [64, 0, 47, 0], [65, 0, 48, 0], [66, 12, 49, 6, "animationBeforeClamped"], [66, 34, 49, 28], [66, 36, 49, 30, "current"], [66, 43, 49, 37], [66, 47, 49, 41, "value"], [66, 52, 49, 46], [66, 54, 49, 48, "now"], [66, 57, 49, 51], [66, 59, 49, 53, "animationBeforeClamped"], [66, 81, 49, 75], [66, 82, 49, 76], [67, 10, 50, 4], [68, 10, 51, 4], [68, 16, 51, 10, "callback"], [68, 24, 51, 18], [68, 27, 51, 21, "finished"], [68, 35, 51, 29], [68, 39, 51, 33], [69, 12, 52, 6], [69, 16, 52, 10, "animationToClamp"], [69, 32, 52, 26], [69, 33, 52, 27, "callback"], [69, 41, 52, 35], [69, 43, 52, 37], [70, 14, 53, 8, "animationToClamp"], [70, 30, 53, 24], [70, 31, 53, 25, "callback"], [70, 39, 53, 33], [70, 40, 53, 34, "finished"], [70, 48, 53, 42], [70, 49, 53, 43], [71, 12, 54, 6], [72, 10, 55, 4], [72, 11, 55, 5], [73, 10, 56, 4], [73, 17, 56, 11], [74, 12, 57, 6, "isHigherOrder"], [74, 25, 57, 19], [74, 27, 57, 21], [74, 31, 57, 25], [75, 12, 58, 6, "onFrame"], [75, 19, 58, 13], [75, 21, 58, 15, "clampOnFrame"], [75, 33, 58, 27], [76, 12, 59, 6, "onStart"], [76, 19, 59, 13], [77, 12, 60, 6, "current"], [77, 19, 60, 13], [77, 21, 60, 15, "animationToClamp"], [77, 37, 60, 31], [77, 38, 60, 32, "current"], [77, 45, 60, 39], [78, 12, 61, 6, "callback"], [78, 20, 61, 14], [79, 12, 62, 6, "previousAnimation"], [79, 29, 62, 23], [79, 31, 62, 25], [79, 35, 62, 29], [80, 12, 63, 6, "reduceMotion"], [80, 24, 63, 18], [80, 26, 63, 20], [80, 30, 63, 20, "getReduceMotionForAnimation"], [80, 63, 63, 47], [80, 65, 63, 48, "config"], [80, 71, 63, 54], [80, 72, 63, 55, "reduceMotion"], [80, 84, 63, 67], [81, 10, 64, 4], [81, 11, 64, 5], [82, 8, 65, 2], [82, 9, 65, 3], [83, 8, 65, 3, "reactNativeReanimated_clampJs2"], [83, 38, 65, 3], [83, 39, 65, 3, "__closure"], [83, 48, 65, 3], [84, 10, 65, 3, "_animationToClamp"], [84, 27, 65, 3], [85, 10, 65, 3, "config"], [85, 16, 65, 3], [86, 10, 65, 3, "recognizePrefixSuffix"], [86, 31, 65, 3], [86, 33, 12, 63, "recognizePrefixSuffix"], [86, 60, 12, 84], [87, 10, 12, 84, "logger"], [87, 16, 12, 84], [87, 18, 17, 8, "logger"], [87, 31, 17, 14], [88, 10, 17, 14, "getReduceMotionForAnimation"], [88, 37, 17, 14], [88, 39, 63, 20, "getReduceMotionForAnimation"], [89, 8, 63, 47], [90, 8, 63, 47, "reactNativeReanimated_clampJs2"], [90, 38, 63, 47], [90, 39, 63, 47, "__workletHash"], [90, 52, 63, 47], [91, 8, 63, 47, "reactNativeReanimated_clampJs2"], [91, 38, 63, 47], [91, 39, 63, 47, "__initData"], [91, 49, 63, 47], [91, 52, 63, 47, "_worklet_15049981710423_init_data"], [91, 85, 63, 47], [92, 8, 63, 47, "reactNativeReanimated_clampJs2"], [92, 38, 63, 47], [92, 39, 63, 47, "__stackDetails"], [92, 53, 63, 47], [92, 56, 63, 47, "_e"], [92, 58, 63, 47], [93, 8, 63, 47], [93, 15, 63, 47, "reactNativeReanimated_clampJs2"], [93, 45, 63, 47], [94, 6, 63, 47], [94, 7, 8, 44], [94, 9, 65, 3], [94, 10, 65, 4], [95, 4, 66, 0], [95, 5, 66, 1], [96, 4, 66, 1, "reactNativeReanimated_clampJs1"], [96, 34, 66, 1], [96, 35, 66, 1, "__closure"], [96, 44, 66, 1], [97, 6, 66, 1, "defineAnimation"], [97, 21, 66, 1], [97, 23, 8, 9, "defineAnimation"], [97, 44, 8, 24], [98, 6, 8, 24, "recognizePrefixSuffix"], [98, 27, 8, 24], [98, 29, 12, 63, "recognizePrefixSuffix"], [98, 56, 12, 84], [99, 6, 12, 84, "logger"], [99, 12, 12, 84], [99, 14, 17, 8, "logger"], [99, 27, 17, 14], [100, 6, 17, 14, "getReduceMotionForAnimation"], [100, 33, 17, 14], [100, 35, 63, 20, "getReduceMotionForAnimation"], [101, 4, 63, 47], [102, 4, 63, 47, "reactNativeReanimated_clampJs1"], [102, 34, 63, 47], [102, 35, 63, 47, "__workletHash"], [102, 48, 63, 47], [103, 4, 63, 47, "reactNativeReanimated_clampJs1"], [103, 34, 63, 47], [103, 35, 63, 47, "__initData"], [103, 45, 63, 47], [103, 48, 63, 47, "_worklet_11241294755947_init_data"], [103, 81, 63, 47], [104, 4, 63, 47, "reactNativeReanimated_clampJs1"], [104, 34, 63, 47], [104, 35, 63, 47, "__stackDetails"], [104, 49, 63, 47], [104, 52, 63, 47, "_e"], [104, 54, 63, 47], [105, 4, 63, 47], [105, 11, 63, 47, "reactNativeReanimated_clampJs1"], [105, 41, 63, 47], [106, 2, 63, 47], [106, 3, 5, 25], [106, 5, 66, 1], [107, 0, 66, 2], [107, 3]], "functionMap": {"names": ["<global>", "withClamp", "defineAnimation$argument_1", "clampOnFrame", "onStart", "callback"], "mappings": "AAA;yBCI;4CCG;ICM;KDsB;IEC;KFa;qBGC;KHI;GDU;CDC"}}, "type": "js/module"}]}