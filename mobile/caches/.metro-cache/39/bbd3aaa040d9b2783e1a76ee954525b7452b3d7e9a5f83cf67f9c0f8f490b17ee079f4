{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../exports/InteractionManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 225}, "end": {"line": 13, "column": 69, "index": 294}}], "key": "xCxB1zW50yE4RuPd5KrkShI1yG4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * @format\n   * \n   */\n\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _InteractionManager = _interopRequireDefault(require(_dependencyMap[1], \"../../../exports/InteractionManager\"));\n  /**\n   * A simple class for batching up invocations of a low-pri callback. A timeout is set to run the\n   * callback once after a delay, no matter how many times it's scheduled. Once the delay is reached,\n   * InteractionManager.runAfterInteractions is used to invoke the callback after any hi-pri\n   * interactions are done running.\n   *\n   * Make sure to cleanup with dispose().  Example:\n   *\n   *   class Widget extends React.Component {\n   *     _batchedSave: new Batchinator(() => this._saveState, 1000);\n   *     _saveSate() {\n   *       // save this.state to disk\n   *     }\n   *     componentDidUpdate() {\n   *       this._batchedSave.schedule();\n   *     }\n   *     componentWillUnmount() {\n   *       this._batchedSave.dispose();\n   *     }\n   *     ...\n   *   }\n   */\n  class Batchinator {\n    constructor(callback, delayMS) {\n      this._delay = delayMS;\n      this._callback = callback;\n    }\n    /*\n     * Cleanup any pending tasks.\n     *\n     * By default, if there is a pending task the callback is run immediately. Set the option abort to\n     * true to not call the callback if it was pending.\n     */\n    dispose(options) {\n      if (options === void 0) {\n        options = {\n          abort: false\n        };\n      }\n      if (this._taskHandle) {\n        this._taskHandle.cancel();\n        if (!options.abort) {\n          this._callback();\n        }\n        this._taskHandle = null;\n      }\n    }\n    schedule() {\n      if (this._taskHandle) {\n        return;\n      }\n      var timeoutHandle = setTimeout(() => {\n        this._taskHandle = _InteractionManager.default.runAfterInteractions(() => {\n          // Note that we clear the handle before invoking the callback so that if the callback calls\n          // schedule again, it will actually schedule another task.\n          this._taskHandle = null;\n          this._callback();\n        });\n      }, this._delay);\n      this._taskHandle = {\n        cancel: () => clearTimeout(timeoutHandle)\n      };\n    }\n  }\n  var _default = exports.default = Batchinator;\n});", "lineCount": 85, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 11, 13], [14, 6, 11, 13, "_interopRequireDefault"], [14, 28, 11, 13], [14, 31, 11, 13, "require"], [14, 38, 11, 13], [14, 39, 11, 13, "_dependencyMap"], [14, 53, 11, 13], [15, 2, 11, 13, "Object"], [15, 8, 11, 13], [15, 9, 11, 13, "defineProperty"], [15, 23, 11, 13], [15, 24, 11, 13, "exports"], [15, 31, 11, 13], [16, 4, 11, 13, "value"], [16, 9, 11, 13], [17, 2, 11, 13], [18, 2, 11, 13, "exports"], [18, 9, 11, 13], [18, 10, 11, 13, "default"], [18, 17, 11, 13], [19, 2, 13, 0], [19, 6, 13, 0, "_InteractionManager"], [19, 25, 13, 0], [19, 28, 13, 0, "_interopRequireDefault"], [19, 50, 13, 0], [19, 51, 13, 0, "require"], [19, 58, 13, 0], [19, 59, 13, 0, "_dependencyMap"], [19, 73, 13, 0], [20, 2, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 0, 21, 0], [27, 0, 22, 0], [28, 0, 23, 0], [29, 0, 24, 0], [30, 0, 25, 0], [31, 0, 26, 0], [32, 0, 27, 0], [33, 0, 28, 0], [34, 0, 29, 0], [35, 0, 30, 0], [36, 0, 31, 0], [37, 0, 32, 0], [38, 0, 33, 0], [39, 0, 34, 0], [40, 0, 35, 0], [41, 0, 36, 0], [42, 2, 37, 0], [42, 8, 37, 6, "<PERSON><PERSON><PERSON>"], [42, 19, 37, 17], [42, 20, 37, 18], [43, 4, 38, 2, "constructor"], [43, 15, 38, 13, "constructor"], [43, 16, 38, 14, "callback"], [43, 24, 38, 22], [43, 26, 38, 24, "delayMS"], [43, 33, 38, 31], [43, 35, 38, 33], [44, 6, 39, 4], [44, 10, 39, 8], [44, 11, 39, 9, "_delay"], [44, 17, 39, 15], [44, 20, 39, 18, "delayMS"], [44, 27, 39, 25], [45, 6, 40, 4], [45, 10, 40, 8], [45, 11, 40, 9, "_callback"], [45, 20, 40, 18], [45, 23, 40, 21, "callback"], [45, 31, 40, 29], [46, 4, 41, 2], [47, 4, 42, 2], [48, 0, 43, 0], [49, 0, 44, 0], [50, 0, 45, 0], [51, 0, 46, 0], [52, 0, 47, 0], [53, 4, 48, 2, "dispose"], [53, 11, 48, 9, "dispose"], [53, 12, 48, 10, "options"], [53, 19, 48, 17], [53, 21, 48, 19], [54, 6, 49, 4], [54, 10, 49, 8, "options"], [54, 17, 49, 15], [54, 22, 49, 20], [54, 27, 49, 25], [54, 28, 49, 26], [54, 30, 49, 28], [55, 8, 50, 6, "options"], [55, 15, 50, 13], [55, 18, 50, 16], [56, 10, 51, 8, "abort"], [56, 15, 51, 13], [56, 17, 51, 15], [57, 8, 52, 6], [57, 9, 52, 7], [58, 6, 53, 4], [59, 6, 54, 4], [59, 10, 54, 8], [59, 14, 54, 12], [59, 15, 54, 13, "_task<PERSON><PERSON>le"], [59, 26, 54, 24], [59, 28, 54, 26], [60, 8, 55, 6], [60, 12, 55, 10], [60, 13, 55, 11, "_task<PERSON><PERSON>le"], [60, 24, 55, 22], [60, 25, 55, 23, "cancel"], [60, 31, 55, 29], [60, 32, 55, 30], [60, 33, 55, 31], [61, 8, 56, 6], [61, 12, 56, 10], [61, 13, 56, 11, "options"], [61, 20, 56, 18], [61, 21, 56, 19, "abort"], [61, 26, 56, 24], [61, 28, 56, 26], [62, 10, 57, 8], [62, 14, 57, 12], [62, 15, 57, 13, "_callback"], [62, 24, 57, 22], [62, 25, 57, 23], [62, 26, 57, 24], [63, 8, 58, 6], [64, 8, 59, 6], [64, 12, 59, 10], [64, 13, 59, 11, "_task<PERSON><PERSON>le"], [64, 24, 59, 22], [64, 27, 59, 25], [64, 31, 59, 29], [65, 6, 60, 4], [66, 4, 61, 2], [67, 4, 62, 2, "schedule"], [67, 12, 62, 10, "schedule"], [67, 13, 62, 10], [67, 15, 62, 13], [68, 6, 63, 4], [68, 10, 63, 8], [68, 14, 63, 12], [68, 15, 63, 13, "_task<PERSON><PERSON>le"], [68, 26, 63, 24], [68, 28, 63, 26], [69, 8, 64, 6], [70, 6, 65, 4], [71, 6, 66, 4], [71, 10, 66, 8, "timeoutH<PERSON>le"], [71, 23, 66, 21], [71, 26, 66, 24, "setTimeout"], [71, 36, 66, 34], [71, 37, 66, 35], [71, 43, 66, 41], [72, 8, 67, 6], [72, 12, 67, 10], [72, 13, 67, 11, "_task<PERSON><PERSON>le"], [72, 24, 67, 22], [72, 27, 67, 25, "InteractionManager"], [72, 54, 67, 43], [72, 55, 67, 44, "runAfterInteractions"], [72, 75, 67, 64], [72, 76, 67, 65], [72, 82, 67, 71], [73, 10, 68, 8], [74, 10, 69, 8], [75, 10, 70, 8], [75, 14, 70, 12], [75, 15, 70, 13, "_task<PERSON><PERSON>le"], [75, 26, 70, 24], [75, 29, 70, 27], [75, 33, 70, 31], [76, 10, 71, 8], [76, 14, 71, 12], [76, 15, 71, 13, "_callback"], [76, 24, 71, 22], [76, 25, 71, 23], [76, 26, 71, 24], [77, 8, 72, 6], [77, 9, 72, 7], [77, 10, 72, 8], [78, 6, 73, 4], [78, 7, 73, 5], [78, 9, 73, 7], [78, 13, 73, 11], [78, 14, 73, 12, "_delay"], [78, 20, 73, 18], [78, 21, 73, 19], [79, 6, 74, 4], [79, 10, 74, 8], [79, 11, 74, 9, "_task<PERSON><PERSON>le"], [79, 22, 74, 20], [79, 25, 74, 23], [80, 8, 75, 6, "cancel"], [80, 14, 75, 12], [80, 16, 75, 14, "cancel"], [80, 17, 75, 14], [80, 22, 75, 20, "clearTimeout"], [80, 34, 75, 32], [80, 35, 75, 33, "timeoutH<PERSON>le"], [80, 48, 75, 46], [81, 6, 76, 4], [81, 7, 76, 5], [82, 4, 77, 2], [83, 2, 78, 0], [84, 2, 78, 1], [84, 6, 78, 1, "_default"], [84, 14, 78, 1], [84, 17, 78, 1, "exports"], [84, 24, 78, 1], [84, 25, 78, 1, "default"], [84, 32, 78, 1], [84, 35, 79, 15, "<PERSON><PERSON><PERSON>"], [84, 46, 79, 26], [85, 0, 79, 26], [85, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON>", "Batchinator#constructor", "Batchinator#dispose", "Batchinator#schedule", "setTimeout$argument_0", "InteractionManager.runAfterInteractions$argument_0", "_taskHandle.cancel"], "mappings": "AAA;ACoC;ECC;GDG;EEO;GFa;EGC;mCCI;iECC;ODK;KDC;cGE,iCH;GHE;CDC"}}, "type": "js/module"}]}