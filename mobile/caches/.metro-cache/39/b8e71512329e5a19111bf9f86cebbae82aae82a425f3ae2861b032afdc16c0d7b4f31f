{"dependencies": [{"name": "@react-navigation/core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 69, "index": 84}}], "key": "Wm75LgE4xYscVWo0KoLFlflJQCo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 85}, "end": {"line": 4, "column": 31, "index": 116}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useScrollToTop = useScrollToTop;\n  var _core = require(_dependencyMap[0], \"@react-navigation/core\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function getScrollableNode(ref) {\n    if (ref.current == null) {\n      return null;\n    }\n    if ('scrollToTop' in ref.current || 'scrollTo' in ref.current || 'scrollToOffset' in ref.current || 'scrollResponderScrollTo' in ref.current) {\n      // This is already a scrollable node.\n      return ref.current;\n    } else if ('getScrollResponder' in ref.current) {\n      // If the view is a wrapper like FlatList, SectionList etc.\n      // We need to use `getScrollResponder` to get access to the scroll responder\n      return ref.current.getScrollResponder();\n    } else if ('getNode' in ref.current) {\n      // When a `ScrollView` is wrapped in `Animated.createAnimatedComponent`\n      // we need to use `getNode` to get the ref to the actual scrollview.\n      // Note that `getNode` is deprecated in newer versions of react-native\n      // this is why we check if we already have a scrollable node above.\n      return ref.current.getNode();\n    } else {\n      return ref.current;\n    }\n  }\n  function useScrollToTop(ref) {\n    var navigation = React.useContext(_core.NavigationContext);\n    var route = (0, _core.useRoute)();\n    if (navigation === undefined) {\n      throw new Error(\"Couldn't find a navigation object. Is your component inside NavigationContainer?\");\n    }\n    React.useEffect(() => {\n      var tabNavigations = [];\n      var currentNavigation = navigation;\n      // If the screen is nested inside multiple tab navigators, we should scroll to top for any of them\n      // So we need to find all the parent tab navigators and add the listeners there\n      while (currentNavigation) {\n        if (currentNavigation.getState().type === 'tab') {\n          tabNavigations.push(currentNavigation);\n        }\n        currentNavigation = currentNavigation.getParent();\n      }\n      if (tabNavigations.length === 0) {\n        return;\n      }\n      var unsubscribers = tabNavigations.map(tab => {\n        return tab.addListener(\n        // We don't wanna import tab types here to avoid extra deps\n        // in addition, there are multiple tab implementations\n        // @ts-expect-error the `tabPress` event is only available when navigation type is tab\n        'tabPress', e => {\n          // We should scroll to top only when the screen is focused\n          var isFocused = navigation.isFocused();\n\n          // In a nested stack navigator, tab press resets the stack to first screen\n          // So we should scroll to top only when we are on first screen\n          var isFirst = tabNavigations.includes(navigation) || navigation.getState().routes[0].key === route.key;\n\n          // Run the operation in the next frame so we're sure all listeners have been run\n          // This is necessary to know if preventDefault() has been called\n          requestAnimationFrame(() => {\n            var scrollable = getScrollableNode(ref);\n            if (isFocused && isFirst && scrollable && !e.defaultPrevented) {\n              if ('scrollToTop' in scrollable) {\n                scrollable.scrollToTop();\n              } else if ('scrollTo' in scrollable) {\n                scrollable.scrollTo({\n                  y: 0,\n                  animated: true\n                });\n              } else if ('scrollToOffset' in scrollable) {\n                scrollable.scrollToOffset({\n                  offset: 0,\n                  animated: true\n                });\n              } else if ('scrollResponderScrollTo' in scrollable) {\n                scrollable.scrollResponderScrollTo({\n                  y: 0,\n                  animated: true\n                });\n              }\n            }\n          });\n        });\n      });\n      return () => {\n        unsubscribers.forEach(unsubscribe => unsubscribe());\n      };\n    }, [navigation, ref, route.key]);\n  }\n});", "lineCount": 97, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useScrollToTop"], [7, 24, 1, 13], [7, 27, 1, 13, "useScrollToTop"], [7, 41, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 4, 31], [10, 11, 4, 31, "_interopRequireWildcard"], [10, 35, 4, 31, "e"], [10, 36, 4, 31], [10, 38, 4, 31, "t"], [10, 39, 4, 31], [10, 68, 4, 31, "WeakMap"], [10, 75, 4, 31], [10, 81, 4, 31, "r"], [10, 82, 4, 31], [10, 89, 4, 31, "WeakMap"], [10, 96, 4, 31], [10, 100, 4, 31, "n"], [10, 101, 4, 31], [10, 108, 4, 31, "WeakMap"], [10, 115, 4, 31], [10, 127, 4, 31, "_interopRequireWildcard"], [10, 150, 4, 31], [10, 162, 4, 31, "_interopRequireWildcard"], [10, 163, 4, 31, "e"], [10, 164, 4, 31], [10, 166, 4, 31, "t"], [10, 167, 4, 31], [10, 176, 4, 31, "t"], [10, 177, 4, 31], [10, 181, 4, 31, "e"], [10, 182, 4, 31], [10, 186, 4, 31, "e"], [10, 187, 4, 31], [10, 188, 4, 31, "__esModule"], [10, 198, 4, 31], [10, 207, 4, 31, "e"], [10, 208, 4, 31], [10, 214, 4, 31, "o"], [10, 215, 4, 31], [10, 217, 4, 31, "i"], [10, 218, 4, 31], [10, 220, 4, 31, "f"], [10, 221, 4, 31], [10, 226, 4, 31, "__proto__"], [10, 235, 4, 31], [10, 243, 4, 31, "default"], [10, 250, 4, 31], [10, 252, 4, 31, "e"], [10, 253, 4, 31], [10, 270, 4, 31, "e"], [10, 271, 4, 31], [10, 294, 4, 31, "e"], [10, 295, 4, 31], [10, 320, 4, 31, "e"], [10, 321, 4, 31], [10, 330, 4, 31, "f"], [10, 331, 4, 31], [10, 337, 4, 31, "o"], [10, 338, 4, 31], [10, 341, 4, 31, "t"], [10, 342, 4, 31], [10, 345, 4, 31, "n"], [10, 346, 4, 31], [10, 349, 4, 31, "r"], [10, 350, 4, 31], [10, 358, 4, 31, "o"], [10, 359, 4, 31], [10, 360, 4, 31, "has"], [10, 363, 4, 31], [10, 364, 4, 31, "e"], [10, 365, 4, 31], [10, 375, 4, 31, "o"], [10, 376, 4, 31], [10, 377, 4, 31, "get"], [10, 380, 4, 31], [10, 381, 4, 31, "e"], [10, 382, 4, 31], [10, 385, 4, 31, "o"], [10, 386, 4, 31], [10, 387, 4, 31, "set"], [10, 390, 4, 31], [10, 391, 4, 31, "e"], [10, 392, 4, 31], [10, 394, 4, 31, "f"], [10, 395, 4, 31], [10, 409, 4, 31, "_t"], [10, 411, 4, 31], [10, 415, 4, 31, "e"], [10, 416, 4, 31], [10, 432, 4, 31, "_t"], [10, 434, 4, 31], [10, 441, 4, 31, "hasOwnProperty"], [10, 455, 4, 31], [10, 456, 4, 31, "call"], [10, 460, 4, 31], [10, 461, 4, 31, "e"], [10, 462, 4, 31], [10, 464, 4, 31, "_t"], [10, 466, 4, 31], [10, 473, 4, 31, "i"], [10, 474, 4, 31], [10, 478, 4, 31, "o"], [10, 479, 4, 31], [10, 482, 4, 31, "Object"], [10, 488, 4, 31], [10, 489, 4, 31, "defineProperty"], [10, 503, 4, 31], [10, 508, 4, 31, "Object"], [10, 514, 4, 31], [10, 515, 4, 31, "getOwnPropertyDescriptor"], [10, 539, 4, 31], [10, 540, 4, 31, "e"], [10, 541, 4, 31], [10, 543, 4, 31, "_t"], [10, 545, 4, 31], [10, 552, 4, 31, "i"], [10, 553, 4, 31], [10, 554, 4, 31, "get"], [10, 557, 4, 31], [10, 561, 4, 31, "i"], [10, 562, 4, 31], [10, 563, 4, 31, "set"], [10, 566, 4, 31], [10, 570, 4, 31, "o"], [10, 571, 4, 31], [10, 572, 4, 31, "f"], [10, 573, 4, 31], [10, 575, 4, 31, "_t"], [10, 577, 4, 31], [10, 579, 4, 31, "i"], [10, 580, 4, 31], [10, 584, 4, 31, "f"], [10, 585, 4, 31], [10, 586, 4, 31, "_t"], [10, 588, 4, 31], [10, 592, 4, 31, "e"], [10, 593, 4, 31], [10, 594, 4, 31, "_t"], [10, 596, 4, 31], [10, 607, 4, 31, "f"], [10, 608, 4, 31], [10, 613, 4, 31, "e"], [10, 614, 4, 31], [10, 616, 4, 31, "t"], [10, 617, 4, 31], [11, 2, 5, 0], [11, 11, 5, 9, "getScrollableNode"], [11, 28, 5, 26, "getScrollableNode"], [11, 29, 5, 27, "ref"], [11, 32, 5, 30], [11, 34, 5, 32], [12, 4, 6, 2], [12, 8, 6, 6, "ref"], [12, 11, 6, 9], [12, 12, 6, 10, "current"], [12, 19, 6, 17], [12, 23, 6, 21], [12, 27, 6, 25], [12, 29, 6, 27], [13, 6, 7, 4], [13, 13, 7, 11], [13, 17, 7, 15], [14, 4, 8, 2], [15, 4, 9, 2], [15, 8, 9, 6], [15, 21, 9, 19], [15, 25, 9, 23, "ref"], [15, 28, 9, 26], [15, 29, 9, 27, "current"], [15, 36, 9, 34], [15, 40, 9, 38], [15, 50, 9, 48], [15, 54, 9, 52, "ref"], [15, 57, 9, 55], [15, 58, 9, 56, "current"], [15, 65, 9, 63], [15, 69, 9, 67], [15, 85, 9, 83], [15, 89, 9, 87, "ref"], [15, 92, 9, 90], [15, 93, 9, 91, "current"], [15, 100, 9, 98], [15, 104, 9, 102], [15, 129, 9, 127], [15, 133, 9, 131, "ref"], [15, 136, 9, 134], [15, 137, 9, 135, "current"], [15, 144, 9, 142], [15, 146, 9, 144], [16, 6, 10, 4], [17, 6, 11, 4], [17, 13, 11, 11, "ref"], [17, 16, 11, 14], [17, 17, 11, 15, "current"], [17, 24, 11, 22], [18, 4, 12, 2], [18, 5, 12, 3], [18, 11, 12, 9], [18, 15, 12, 13], [18, 35, 12, 33], [18, 39, 12, 37, "ref"], [18, 42, 12, 40], [18, 43, 12, 41, "current"], [18, 50, 12, 48], [18, 52, 12, 50], [19, 6, 13, 4], [20, 6, 14, 4], [21, 6, 15, 4], [21, 13, 15, 11, "ref"], [21, 16, 15, 14], [21, 17, 15, 15, "current"], [21, 24, 15, 22], [21, 25, 15, 23, "getScrollResponder"], [21, 43, 15, 41], [21, 44, 15, 42], [21, 45, 15, 43], [22, 4, 16, 2], [22, 5, 16, 3], [22, 11, 16, 9], [22, 15, 16, 13], [22, 24, 16, 22], [22, 28, 16, 26, "ref"], [22, 31, 16, 29], [22, 32, 16, 30, "current"], [22, 39, 16, 37], [22, 41, 16, 39], [23, 6, 17, 4], [24, 6, 18, 4], [25, 6, 19, 4], [26, 6, 20, 4], [27, 6, 21, 4], [27, 13, 21, 11, "ref"], [27, 16, 21, 14], [27, 17, 21, 15, "current"], [27, 24, 21, 22], [27, 25, 21, 23, "getNode"], [27, 32, 21, 30], [27, 33, 21, 31], [27, 34, 21, 32], [28, 4, 22, 2], [28, 5, 22, 3], [28, 11, 22, 9], [29, 6, 23, 4], [29, 13, 23, 11, "ref"], [29, 16, 23, 14], [29, 17, 23, 15, "current"], [29, 24, 23, 22], [30, 4, 24, 2], [31, 2, 25, 0], [32, 2, 26, 7], [32, 11, 26, 16, "useScrollToTop"], [32, 25, 26, 30, "useScrollToTop"], [32, 26, 26, 31, "ref"], [32, 29, 26, 34], [32, 31, 26, 36], [33, 4, 27, 2], [33, 8, 27, 8, "navigation"], [33, 18, 27, 18], [33, 21, 27, 21, "React"], [33, 26, 27, 26], [33, 27, 27, 27, "useContext"], [33, 37, 27, 37], [33, 38, 27, 38, "NavigationContext"], [33, 61, 27, 55], [33, 62, 27, 56], [34, 4, 28, 2], [34, 8, 28, 8, "route"], [34, 13, 28, 13], [34, 16, 28, 16], [34, 20, 28, 16, "useRoute"], [34, 34, 28, 24], [34, 36, 28, 25], [34, 37, 28, 26], [35, 4, 29, 2], [35, 8, 29, 6, "navigation"], [35, 18, 29, 16], [35, 23, 29, 21, "undefined"], [35, 32, 29, 30], [35, 34, 29, 32], [36, 6, 30, 4], [36, 12, 30, 10], [36, 16, 30, 14, "Error"], [36, 21, 30, 19], [36, 22, 30, 20], [36, 104, 30, 102], [36, 105, 30, 103], [37, 4, 31, 2], [38, 4, 32, 2, "React"], [38, 9, 32, 7], [38, 10, 32, 8, "useEffect"], [38, 19, 32, 17], [38, 20, 32, 18], [38, 26, 32, 24], [39, 6, 33, 4], [39, 10, 33, 10, "tabNavigations"], [39, 24, 33, 24], [39, 27, 33, 27], [39, 29, 33, 29], [40, 6, 34, 4], [40, 10, 34, 8, "currentNavigation"], [40, 27, 34, 25], [40, 30, 34, 28, "navigation"], [40, 40, 34, 38], [41, 6, 35, 4], [42, 6, 36, 4], [43, 6, 37, 4], [43, 13, 37, 11, "currentNavigation"], [43, 30, 37, 28], [43, 32, 37, 30], [44, 8, 38, 6], [44, 12, 38, 10, "currentNavigation"], [44, 29, 38, 27], [44, 30, 38, 28, "getState"], [44, 38, 38, 36], [44, 39, 38, 37], [44, 40, 38, 38], [44, 41, 38, 39, "type"], [44, 45, 38, 43], [44, 50, 38, 48], [44, 55, 38, 53], [44, 57, 38, 55], [45, 10, 39, 8, "tabNavigations"], [45, 24, 39, 22], [45, 25, 39, 23, "push"], [45, 29, 39, 27], [45, 30, 39, 28, "currentNavigation"], [45, 47, 39, 45], [45, 48, 39, 46], [46, 8, 40, 6], [47, 8, 41, 6, "currentNavigation"], [47, 25, 41, 23], [47, 28, 41, 26, "currentNavigation"], [47, 45, 41, 43], [47, 46, 41, 44, "getParent"], [47, 55, 41, 53], [47, 56, 41, 54], [47, 57, 41, 55], [48, 6, 42, 4], [49, 6, 43, 4], [49, 10, 43, 8, "tabNavigations"], [49, 24, 43, 22], [49, 25, 43, 23, "length"], [49, 31, 43, 29], [49, 36, 43, 34], [49, 37, 43, 35], [49, 39, 43, 37], [50, 8, 44, 6], [51, 6, 45, 4], [52, 6, 46, 4], [52, 10, 46, 10, "unsubscribers"], [52, 23, 46, 23], [52, 26, 46, 26, "tabNavigations"], [52, 40, 46, 40], [52, 41, 46, 41, "map"], [52, 44, 46, 44], [52, 45, 46, 45, "tab"], [52, 48, 46, 48], [52, 52, 46, 52], [53, 8, 47, 6], [53, 15, 47, 13, "tab"], [53, 18, 47, 16], [53, 19, 47, 17, "addListener"], [53, 30, 47, 28], [54, 8, 48, 6], [55, 8, 49, 6], [56, 8, 50, 6], [57, 8, 51, 6], [57, 18, 51, 16], [57, 20, 51, 18, "e"], [57, 21, 51, 19], [57, 25, 51, 23], [58, 10, 52, 8], [59, 10, 53, 8], [59, 14, 53, 14, "isFocused"], [59, 23, 53, 23], [59, 26, 53, 26, "navigation"], [59, 36, 53, 36], [59, 37, 53, 37, "isFocused"], [59, 46, 53, 46], [59, 47, 53, 47], [59, 48, 53, 48], [61, 10, 55, 8], [62, 10, 56, 8], [63, 10, 57, 8], [63, 14, 57, 14, "<PERSON><PERSON><PERSON><PERSON>"], [63, 21, 57, 21], [63, 24, 57, 24, "tabNavigations"], [63, 38, 57, 38], [63, 39, 57, 39, "includes"], [63, 47, 57, 47], [63, 48, 57, 48, "navigation"], [63, 58, 57, 58], [63, 59, 57, 59], [63, 63, 57, 63, "navigation"], [63, 73, 57, 73], [63, 74, 57, 74, "getState"], [63, 82, 57, 82], [63, 83, 57, 83], [63, 84, 57, 84], [63, 85, 57, 85, "routes"], [63, 91, 57, 91], [63, 92, 57, 92], [63, 93, 57, 93], [63, 94, 57, 94], [63, 95, 57, 95, "key"], [63, 98, 57, 98], [63, 103, 57, 103, "route"], [63, 108, 57, 108], [63, 109, 57, 109, "key"], [63, 112, 57, 112], [65, 10, 59, 8], [66, 10, 60, 8], [67, 10, 61, 8, "requestAnimationFrame"], [67, 31, 61, 29], [67, 32, 61, 30], [67, 38, 61, 36], [68, 12, 62, 10], [68, 16, 62, 16, "scrollable"], [68, 26, 62, 26], [68, 29, 62, 29, "getScrollableNode"], [68, 46, 62, 46], [68, 47, 62, 47, "ref"], [68, 50, 62, 50], [68, 51, 62, 51], [69, 12, 63, 10], [69, 16, 63, 14, "isFocused"], [69, 25, 63, 23], [69, 29, 63, 27, "<PERSON><PERSON><PERSON><PERSON>"], [69, 36, 63, 34], [69, 40, 63, 38, "scrollable"], [69, 50, 63, 48], [69, 54, 63, 52], [69, 55, 63, 53, "e"], [69, 56, 63, 54], [69, 57, 63, 55, "defaultPrevented"], [69, 73, 63, 71], [69, 75, 63, 73], [70, 14, 64, 12], [70, 18, 64, 16], [70, 31, 64, 29], [70, 35, 64, 33, "scrollable"], [70, 45, 64, 43], [70, 47, 64, 45], [71, 16, 65, 14, "scrollable"], [71, 26, 65, 24], [71, 27, 65, 25, "scrollToTop"], [71, 38, 65, 36], [71, 39, 65, 37], [71, 40, 65, 38], [72, 14, 66, 12], [72, 15, 66, 13], [72, 21, 66, 19], [72, 25, 66, 23], [72, 35, 66, 33], [72, 39, 66, 37, "scrollable"], [72, 49, 66, 47], [72, 51, 66, 49], [73, 16, 67, 14, "scrollable"], [73, 26, 67, 24], [73, 27, 67, 25, "scrollTo"], [73, 35, 67, 33], [73, 36, 67, 34], [74, 18, 68, 16, "y"], [74, 19, 68, 17], [74, 21, 68, 19], [74, 22, 68, 20], [75, 18, 69, 16, "animated"], [75, 26, 69, 24], [75, 28, 69, 26], [76, 16, 70, 14], [76, 17, 70, 15], [76, 18, 70, 16], [77, 14, 71, 12], [77, 15, 71, 13], [77, 21, 71, 19], [77, 25, 71, 23], [77, 41, 71, 39], [77, 45, 71, 43, "scrollable"], [77, 55, 71, 53], [77, 57, 71, 55], [78, 16, 72, 14, "scrollable"], [78, 26, 72, 24], [78, 27, 72, 25, "scrollToOffset"], [78, 41, 72, 39], [78, 42, 72, 40], [79, 18, 73, 16, "offset"], [79, 24, 73, 22], [79, 26, 73, 24], [79, 27, 73, 25], [80, 18, 74, 16, "animated"], [80, 26, 74, 24], [80, 28, 74, 26], [81, 16, 75, 14], [81, 17, 75, 15], [81, 18, 75, 16], [82, 14, 76, 12], [82, 15, 76, 13], [82, 21, 76, 19], [82, 25, 76, 23], [82, 50, 76, 48], [82, 54, 76, 52, "scrollable"], [82, 64, 76, 62], [82, 66, 76, 64], [83, 16, 77, 14, "scrollable"], [83, 26, 77, 24], [83, 27, 77, 25, "scrollResponderScrollTo"], [83, 50, 77, 48], [83, 51, 77, 49], [84, 18, 78, 16, "y"], [84, 19, 78, 17], [84, 21, 78, 19], [84, 22, 78, 20], [85, 18, 79, 16, "animated"], [85, 26, 79, 24], [85, 28, 79, 26], [86, 16, 80, 14], [86, 17, 80, 15], [86, 18, 80, 16], [87, 14, 81, 12], [88, 12, 82, 10], [89, 10, 83, 8], [89, 11, 83, 9], [89, 12, 83, 10], [90, 8, 84, 6], [90, 9, 84, 7], [90, 10, 84, 8], [91, 6, 85, 4], [91, 7, 85, 5], [91, 8, 85, 6], [92, 6, 86, 4], [92, 13, 86, 11], [92, 19, 86, 17], [93, 8, 87, 6, "unsubscribers"], [93, 21, 87, 19], [93, 22, 87, 20, "for<PERSON>ach"], [93, 29, 87, 27], [93, 30, 87, 28, "unsubscribe"], [93, 41, 87, 39], [93, 45, 87, 43, "unsubscribe"], [93, 56, 87, 54], [93, 57, 87, 55], [93, 58, 87, 56], [93, 59, 87, 57], [94, 6, 88, 4], [94, 7, 88, 5], [95, 4, 89, 2], [95, 5, 89, 3], [95, 7, 89, 5], [95, 8, 89, 6, "navigation"], [95, 18, 89, 16], [95, 20, 89, 18, "ref"], [95, 23, 89, 21], [95, 25, 89, 23, "route"], [95, 30, 89, 28], [95, 31, 89, 29, "key"], [95, 34, 89, 32], [95, 35, 89, 33], [95, 36, 89, 34], [96, 2, 90, 0], [97, 0, 90, 1], [97, 3]], "functionMap": {"names": ["<global>", "getScrollableNode", "useScrollToTop", "React.useEffect$argument_0", "tabNavigations.map$argument_0", "tab.addListener$argument_1", "requestAnimationFrame$argument_0", "<anonymous>", "unsubscribers.forEach$argument_0"], "mappings": "AAA;ACI;CDoB;OEC;kBCM;6CCc;kBCK;8BCU;SDsB;ODC;KDC;WIC;4BCC,4BD;KJC;GDC;CFC"}}, "type": "js/module"}]}