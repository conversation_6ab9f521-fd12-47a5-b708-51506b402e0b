{"dependencies": [{"name": "../constants", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 20, "index": 187}, "end": {"line": 5, "column": 43, "index": 210}}], "key": "zmjjtqoQxi2W71eIMIIaEi1mOpU=", "exportNames": ["*"]}}, {"name": "../fork/getPathFromState-forks", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 33, "index": 245}, "end": {"line": 6, "column": 74, "index": 286}}], "key": "Pwl2s8MefiXMrO7elNt6TT2k9Bo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.defaultRouteInfo = void 0;\n  exports.getRouteInfoFromState = getRouteInfoFromState;\n  const constants_1 = require(_dependencyMap[0], \"../constants\");\n  const getPathFromState_forks_1 = require(_dependencyMap[1], \"../fork/getPathFromState-forks\");\n  exports.defaultRouteInfo = {\n    unstable_globalHref: '',\n    searchParams: new URLSearchParams(),\n    pathname: '/',\n    params: {},\n    segments: [],\n    pathnameWithParams: '/',\n    // TODO: Remove this, it is not used anywhere\n    isIndex: false\n  };\n  function getRouteInfoFromState(state) {\n    if (!state) return exports.defaultRouteInfo;\n    let route = state.routes[0];\n    if (route.name !== constants_1.INTERNAL_SLOT_NAME) {\n      throw new Error(`Expected the first route to be ${constants_1.INTERNAL_SLOT_NAME}, but got ${route.name}`);\n    }\n    state = route.state;\n    const segments = [];\n    const params = Object.create(null);\n    while (state) {\n      route = state.routes['index' in state && state.index ? state.index : 0];\n      Object.assign(params, route.params);\n      let routeName = route.name;\n      if (routeName.startsWith('/')) {\n        routeName = routeName.slice(1);\n      }\n      segments.push(...routeName.split('/'));\n      state = route.state;\n    }\n    /**\n     * If React Navigation didn't render the entire tree (e.g it was interrupted in a layout)\n     * then the state maybe incomplete. The reset of the path is in the params, instead of being a route\n     */\n    let routeParams = route.params;\n    while (routeParams && 'screen' in routeParams) {\n      if (typeof routeParams.screen === 'string') {\n        const screen = routeParams.screen.startsWith('/') ? routeParams.screen.slice(1) : routeParams.screen;\n        segments.push(...screen.split('/'));\n      }\n      if (typeof routeParams.params === 'object' && !Array.isArray(routeParams.params)) {\n        routeParams = routeParams.params;\n      } else {\n        routeParams = undefined;\n      }\n    }\n    if (route.params && 'screen' in route.params && route.params.screen === 'string') {\n      const screen = route.params.screen.startsWith('/') ? route.params.screen.slice(1) : route.params.screen;\n      segments.push(...screen.split('/'));\n    }\n    if (segments[segments.length - 1] === 'index') {\n      segments.pop();\n    }\n    delete params['screen'];\n    delete params['params'];\n    const pathParams = new Set();\n    const pathname = '/' + segments.filter(segment => {\n      return !(segment.startsWith('(') && segment.endsWith(')'));\n    }).flatMap(segment => {\n      if (segment === '+not-found') {\n        const notFoundPath = params['not-found'];\n        pathParams.add('not-found');\n        if (typeof notFoundPath === 'undefined') {\n          // Not founds are optional, do nothing if its not present\n          return [];\n        } else if (Array.isArray(notFoundPath)) {\n          return notFoundPath;\n        } else {\n          return [notFoundPath];\n        }\n      } else if (segment.startsWith('[...') && segment.endsWith(']')) {\n        let paramName = segment.slice(4, -1);\n        // Legacy for React Navigation optional params\n        if (paramName.endsWith('?')) {\n          paramName = paramName.slice(0, -1);\n        }\n        const values = params[paramName];\n        pathParams.add(paramName);\n        // Catchall params are optional\n        return values || [];\n      } else if (segment.startsWith('[') && segment.endsWith(']')) {\n        const paramName = segment.slice(1, -1);\n        const value = params[paramName];\n        pathParams.add(paramName);\n        // Optional params are optional\n        return value ? [value] : [];\n      } else {\n        return [segment];\n      }\n    }).join('/');\n    const searchParams = new URLSearchParams(Object.entries(params).flatMap(([key, value]) => {\n      // Search params should not include path params\n      if (pathParams.has(key)) {\n        return [];\n      } else if (Array.isArray(value)) {\n        return value.map(v => [key, v]);\n      }\n      return [[key, value]];\n    }));\n    let hash;\n    if (searchParams.has('#')) {\n      hash = searchParams.get('#') || undefined;\n      searchParams.delete('#');\n    }\n    // We cannot use searchParams.size because it is not included in the React Native polyfill\n    const searchParamString = searchParams.toString();\n    let pathnameWithParams = searchParamString ? pathname + '?' + searchParamString : pathname;\n    pathnameWithParams = hash ? pathnameWithParams + '#' + hash : pathnameWithParams;\n    return {\n      segments,\n      pathname,\n      params,\n      unstable_globalHref: (0, getPathFromState_forks_1.appendBaseUrl)(pathnameWithParams),\n      searchParams,\n      pathnameWithParams,\n      // TODO: Remove this, it is not used anywhere\n      isIndex: false\n    };\n  }\n});", "lineCount": 129, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "defaultRouteInfo"], [7, 26, 3, 24], [7, 29, 3, 27], [7, 34, 3, 32], [7, 35, 3, 33], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "getRouteInfoFromState"], [8, 31, 4, 29], [8, 34, 4, 32, "getRouteInfoFromState"], [8, 55, 4, 53], [9, 2, 5, 0], [9, 8, 5, 6, "constants_1"], [9, 19, 5, 17], [9, 22, 5, 20, "require"], [9, 29, 5, 27], [9, 30, 5, 27, "_dependencyMap"], [9, 44, 5, 27], [9, 63, 5, 42], [9, 64, 5, 43], [10, 2, 6, 0], [10, 8, 6, 6, "getPathFromState_forks_1"], [10, 32, 6, 30], [10, 35, 6, 33, "require"], [10, 42, 6, 40], [10, 43, 6, 40, "_dependencyMap"], [10, 57, 6, 40], [10, 94, 6, 73], [10, 95, 6, 74], [11, 2, 7, 0, "exports"], [11, 9, 7, 7], [11, 10, 7, 8, "defaultRouteInfo"], [11, 26, 7, 24], [11, 29, 7, 27], [12, 4, 8, 4, "unstable_globalHref"], [12, 23, 8, 23], [12, 25, 8, 25], [12, 27, 8, 27], [13, 4, 9, 4, "searchParams"], [13, 16, 9, 16], [13, 18, 9, 18], [13, 22, 9, 22, "URLSearchParams"], [13, 37, 9, 37], [13, 38, 9, 38], [13, 39, 9, 39], [14, 4, 10, 4, "pathname"], [14, 12, 10, 12], [14, 14, 10, 14], [14, 17, 10, 17], [15, 4, 11, 4, "params"], [15, 10, 11, 10], [15, 12, 11, 12], [15, 13, 11, 13], [15, 14, 11, 14], [16, 4, 12, 4, "segments"], [16, 12, 12, 12], [16, 14, 12, 14], [16, 16, 12, 16], [17, 4, 13, 4, "pathnameWithParams"], [17, 22, 13, 22], [17, 24, 13, 24], [17, 27, 13, 27], [18, 4, 14, 4], [19, 4, 15, 4, "isIndex"], [19, 11, 15, 11], [19, 13, 15, 13], [20, 2, 16, 0], [20, 3, 16, 1], [21, 2, 17, 0], [21, 11, 17, 9, "getRouteInfoFromState"], [21, 32, 17, 30, "getRouteInfoFromState"], [21, 33, 17, 31, "state"], [21, 38, 17, 36], [21, 40, 17, 38], [22, 4, 18, 4], [22, 8, 18, 8], [22, 9, 18, 9, "state"], [22, 14, 18, 14], [22, 16, 19, 8], [22, 23, 19, 15, "exports"], [22, 30, 19, 22], [22, 31, 19, 23, "defaultRouteInfo"], [22, 47, 19, 39], [23, 4, 20, 4], [23, 8, 20, 8, "route"], [23, 13, 20, 13], [23, 16, 20, 16, "state"], [23, 21, 20, 21], [23, 22, 20, 22, "routes"], [23, 28, 20, 28], [23, 29, 20, 29], [23, 30, 20, 30], [23, 31, 20, 31], [24, 4, 21, 4], [24, 8, 21, 8, "route"], [24, 13, 21, 13], [24, 14, 21, 14, "name"], [24, 18, 21, 18], [24, 23, 21, 23, "constants_1"], [24, 34, 21, 34], [24, 35, 21, 35, "INTERNAL_SLOT_NAME"], [24, 53, 21, 53], [24, 55, 21, 55], [25, 6, 22, 8], [25, 12, 22, 14], [25, 16, 22, 18, "Error"], [25, 21, 22, 23], [25, 22, 22, 24], [25, 56, 22, 58, "constants_1"], [25, 67, 22, 69], [25, 68, 22, 70, "INTERNAL_SLOT_NAME"], [25, 86, 22, 88], [25, 99, 22, 101, "route"], [25, 104, 22, 106], [25, 105, 22, 107, "name"], [25, 109, 22, 111], [25, 111, 22, 113], [25, 112, 22, 114], [26, 4, 23, 4], [27, 4, 24, 4, "state"], [27, 9, 24, 9], [27, 12, 24, 12, "route"], [27, 17, 24, 17], [27, 18, 24, 18, "state"], [27, 23, 24, 23], [28, 4, 25, 4], [28, 10, 25, 10, "segments"], [28, 18, 25, 18], [28, 21, 25, 21], [28, 23, 25, 23], [29, 4, 26, 4], [29, 10, 26, 10, "params"], [29, 16, 26, 16], [29, 19, 26, 19, "Object"], [29, 25, 26, 25], [29, 26, 26, 26, "create"], [29, 32, 26, 32], [29, 33, 26, 33], [29, 37, 26, 37], [29, 38, 26, 38], [30, 4, 27, 4], [30, 11, 27, 11, "state"], [30, 16, 27, 16], [30, 18, 27, 18], [31, 6, 28, 8, "route"], [31, 11, 28, 13], [31, 14, 28, 16, "state"], [31, 19, 28, 21], [31, 20, 28, 22, "routes"], [31, 26, 28, 28], [31, 27, 28, 29], [31, 34, 28, 36], [31, 38, 28, 40, "state"], [31, 43, 28, 45], [31, 47, 28, 49, "state"], [31, 52, 28, 54], [31, 53, 28, 55, "index"], [31, 58, 28, 60], [31, 61, 28, 63, "state"], [31, 66, 28, 68], [31, 67, 28, 69, "index"], [31, 72, 28, 74], [31, 75, 28, 77], [31, 76, 28, 78], [31, 77, 28, 79], [32, 6, 29, 8, "Object"], [32, 12, 29, 14], [32, 13, 29, 15, "assign"], [32, 19, 29, 21], [32, 20, 29, 22, "params"], [32, 26, 29, 28], [32, 28, 29, 30, "route"], [32, 33, 29, 35], [32, 34, 29, 36, "params"], [32, 40, 29, 42], [32, 41, 29, 43], [33, 6, 30, 8], [33, 10, 30, 12, "routeName"], [33, 19, 30, 21], [33, 22, 30, 24, "route"], [33, 27, 30, 29], [33, 28, 30, 30, "name"], [33, 32, 30, 34], [34, 6, 31, 8], [34, 10, 31, 12, "routeName"], [34, 19, 31, 21], [34, 20, 31, 22, "startsWith"], [34, 30, 31, 32], [34, 31, 31, 33], [34, 34, 31, 36], [34, 35, 31, 37], [34, 37, 31, 39], [35, 8, 32, 12, "routeName"], [35, 17, 32, 21], [35, 20, 32, 24, "routeName"], [35, 29, 32, 33], [35, 30, 32, 34, "slice"], [35, 35, 32, 39], [35, 36, 32, 40], [35, 37, 32, 41], [35, 38, 32, 42], [36, 6, 33, 8], [37, 6, 34, 8, "segments"], [37, 14, 34, 16], [37, 15, 34, 17, "push"], [37, 19, 34, 21], [37, 20, 34, 22], [37, 23, 34, 25, "routeName"], [37, 32, 34, 34], [37, 33, 34, 35, "split"], [37, 38, 34, 40], [37, 39, 34, 41], [37, 42, 34, 44], [37, 43, 34, 45], [37, 44, 34, 46], [38, 6, 35, 8, "state"], [38, 11, 35, 13], [38, 14, 35, 16, "route"], [38, 19, 35, 21], [38, 20, 35, 22, "state"], [38, 25, 35, 27], [39, 4, 36, 4], [40, 4, 37, 4], [41, 0, 38, 0], [42, 0, 39, 0], [43, 0, 40, 0], [44, 4, 41, 4], [44, 8, 41, 8, "routeParams"], [44, 19, 41, 19], [44, 22, 41, 22, "route"], [44, 27, 41, 27], [44, 28, 41, 28, "params"], [44, 34, 41, 34], [45, 4, 42, 4], [45, 11, 42, 11, "routeParams"], [45, 22, 42, 22], [45, 26, 42, 26], [45, 34, 42, 34], [45, 38, 42, 38, "routeParams"], [45, 49, 42, 49], [45, 51, 42, 51], [46, 6, 43, 8], [46, 10, 43, 12], [46, 17, 43, 19, "routeParams"], [46, 28, 43, 30], [46, 29, 43, 31, "screen"], [46, 35, 43, 37], [46, 40, 43, 42], [46, 48, 43, 50], [46, 50, 43, 52], [47, 8, 44, 12], [47, 14, 44, 18, "screen"], [47, 20, 44, 24], [47, 23, 44, 27, "routeParams"], [47, 34, 44, 38], [47, 35, 44, 39, "screen"], [47, 41, 44, 45], [47, 42, 44, 46, "startsWith"], [47, 52, 44, 56], [47, 53, 44, 57], [47, 56, 44, 60], [47, 57, 44, 61], [47, 60, 45, 18, "routeParams"], [47, 71, 45, 29], [47, 72, 45, 30, "screen"], [47, 78, 45, 36], [47, 79, 45, 37, "slice"], [47, 84, 45, 42], [47, 85, 45, 43], [47, 86, 45, 44], [47, 87, 45, 45], [47, 90, 46, 18, "routeParams"], [47, 101, 46, 29], [47, 102, 46, 30, "screen"], [47, 108, 46, 36], [48, 8, 47, 12, "segments"], [48, 16, 47, 20], [48, 17, 47, 21, "push"], [48, 21, 47, 25], [48, 22, 47, 26], [48, 25, 47, 29, "screen"], [48, 31, 47, 35], [48, 32, 47, 36, "split"], [48, 37, 47, 41], [48, 38, 47, 42], [48, 41, 47, 45], [48, 42, 47, 46], [48, 43, 47, 47], [49, 6, 48, 8], [50, 6, 49, 8], [50, 10, 49, 12], [50, 17, 49, 19, "routeParams"], [50, 28, 49, 30], [50, 29, 49, 31, "params"], [50, 35, 49, 37], [50, 40, 49, 42], [50, 48, 49, 50], [50, 52, 49, 54], [50, 53, 49, 55, "Array"], [50, 58, 49, 60], [50, 59, 49, 61, "isArray"], [50, 66, 49, 68], [50, 67, 49, 69, "routeParams"], [50, 78, 49, 80], [50, 79, 49, 81, "params"], [50, 85, 49, 87], [50, 86, 49, 88], [50, 88, 49, 90], [51, 8, 50, 12, "routeParams"], [51, 19, 50, 23], [51, 22, 50, 26, "routeParams"], [51, 33, 50, 37], [51, 34, 50, 38, "params"], [51, 40, 50, 44], [52, 6, 51, 8], [52, 7, 51, 9], [52, 13, 52, 13], [53, 8, 53, 12, "routeParams"], [53, 19, 53, 23], [53, 22, 53, 26, "undefined"], [53, 31, 53, 35], [54, 6, 54, 8], [55, 4, 55, 4], [56, 4, 56, 4], [56, 8, 56, 8, "route"], [56, 13, 56, 13], [56, 14, 56, 14, "params"], [56, 20, 56, 20], [56, 24, 56, 24], [56, 32, 56, 32], [56, 36, 56, 36, "route"], [56, 41, 56, 41], [56, 42, 56, 42, "params"], [56, 48, 56, 48], [56, 52, 56, 52, "route"], [56, 57, 56, 57], [56, 58, 56, 58, "params"], [56, 64, 56, 64], [56, 65, 56, 65, "screen"], [56, 71, 56, 71], [56, 76, 56, 76], [56, 84, 56, 84], [56, 86, 56, 86], [57, 6, 57, 8], [57, 12, 57, 14, "screen"], [57, 18, 57, 20], [57, 21, 57, 23, "route"], [57, 26, 57, 28], [57, 27, 57, 29, "params"], [57, 33, 57, 35], [57, 34, 57, 36, "screen"], [57, 40, 57, 42], [57, 41, 57, 43, "startsWith"], [57, 51, 57, 53], [57, 52, 57, 54], [57, 55, 57, 57], [57, 56, 57, 58], [57, 59, 58, 14, "route"], [57, 64, 58, 19], [57, 65, 58, 20, "params"], [57, 71, 58, 26], [57, 72, 58, 27, "screen"], [57, 78, 58, 33], [57, 79, 58, 34, "slice"], [57, 84, 58, 39], [57, 85, 58, 40], [57, 86, 58, 41], [57, 87, 58, 42], [57, 90, 59, 14, "route"], [57, 95, 59, 19], [57, 96, 59, 20, "params"], [57, 102, 59, 26], [57, 103, 59, 27, "screen"], [57, 109, 59, 33], [58, 6, 60, 8, "segments"], [58, 14, 60, 16], [58, 15, 60, 17, "push"], [58, 19, 60, 21], [58, 20, 60, 22], [58, 23, 60, 25, "screen"], [58, 29, 60, 31], [58, 30, 60, 32, "split"], [58, 35, 60, 37], [58, 36, 60, 38], [58, 39, 60, 41], [58, 40, 60, 42], [58, 41, 60, 43], [59, 4, 61, 4], [60, 4, 62, 4], [60, 8, 62, 8, "segments"], [60, 16, 62, 16], [60, 17, 62, 17, "segments"], [60, 25, 62, 25], [60, 26, 62, 26, "length"], [60, 32, 62, 32], [60, 35, 62, 35], [60, 36, 62, 36], [60, 37, 62, 37], [60, 42, 62, 42], [60, 49, 62, 49], [60, 51, 62, 51], [61, 6, 63, 8, "segments"], [61, 14, 63, 16], [61, 15, 63, 17, "pop"], [61, 18, 63, 20], [61, 19, 63, 21], [61, 20, 63, 22], [62, 4, 64, 4], [63, 4, 65, 4], [63, 11, 65, 11, "params"], [63, 17, 65, 17], [63, 18, 65, 18], [63, 26, 65, 26], [63, 27, 65, 27], [64, 4, 66, 4], [64, 11, 66, 11, "params"], [64, 17, 66, 17], [64, 18, 66, 18], [64, 26, 66, 26], [64, 27, 66, 27], [65, 4, 67, 4], [65, 10, 67, 10, "pathParams"], [65, 20, 67, 20], [65, 23, 67, 23], [65, 27, 67, 27, "Set"], [65, 30, 67, 30], [65, 31, 67, 31], [65, 32, 67, 32], [66, 4, 68, 4], [66, 10, 68, 10, "pathname"], [66, 18, 68, 18], [66, 21, 68, 21], [66, 24, 68, 24], [66, 27, 69, 8, "segments"], [66, 35, 69, 16], [66, 36, 70, 13, "filter"], [66, 42, 70, 19], [66, 43, 70, 21, "segment"], [66, 50, 70, 28], [66, 54, 70, 33], [67, 6, 71, 12], [67, 13, 71, 19], [67, 15, 71, 21, "segment"], [67, 22, 71, 28], [67, 23, 71, 29, "startsWith"], [67, 33, 71, 39], [67, 34, 71, 40], [67, 37, 71, 43], [67, 38, 71, 44], [67, 42, 71, 48, "segment"], [67, 49, 71, 55], [67, 50, 71, 56, "endsWith"], [67, 58, 71, 64], [67, 59, 71, 65], [67, 62, 71, 68], [67, 63, 71, 69], [67, 64, 71, 70], [68, 4, 72, 8], [68, 5, 72, 9], [68, 6, 72, 10], [68, 7, 73, 13, "flatMap"], [68, 14, 73, 20], [68, 15, 73, 22, "segment"], [68, 22, 73, 29], [68, 26, 73, 34], [69, 6, 74, 12], [69, 10, 74, 16, "segment"], [69, 17, 74, 23], [69, 22, 74, 28], [69, 34, 74, 40], [69, 36, 74, 42], [70, 8, 75, 16], [70, 14, 75, 22, "notFoundPath"], [70, 26, 75, 34], [70, 29, 75, 37, "params"], [70, 35, 75, 43], [70, 36, 75, 44], [70, 47, 75, 55], [70, 48, 75, 56], [71, 8, 76, 16, "pathParams"], [71, 18, 76, 26], [71, 19, 76, 27, "add"], [71, 22, 76, 30], [71, 23, 76, 31], [71, 34, 76, 42], [71, 35, 76, 43], [72, 8, 77, 16], [72, 12, 77, 20], [72, 19, 77, 27, "notFoundPath"], [72, 31, 77, 39], [72, 36, 77, 44], [72, 47, 77, 55], [72, 49, 77, 57], [73, 10, 78, 20], [74, 10, 79, 20], [74, 17, 79, 27], [74, 19, 79, 29], [75, 8, 80, 16], [75, 9, 80, 17], [75, 15, 81, 21], [75, 19, 81, 25, "Array"], [75, 24, 81, 30], [75, 25, 81, 31, "isArray"], [75, 32, 81, 38], [75, 33, 81, 39, "notFoundPath"], [75, 45, 81, 51], [75, 46, 81, 52], [75, 48, 81, 54], [76, 10, 82, 20], [76, 17, 82, 27, "notFoundPath"], [76, 29, 82, 39], [77, 8, 83, 16], [77, 9, 83, 17], [77, 15, 84, 21], [78, 10, 85, 20], [78, 17, 85, 27], [78, 18, 85, 28, "notFoundPath"], [78, 30, 85, 40], [78, 31, 85, 41], [79, 8, 86, 16], [80, 6, 87, 12], [80, 7, 87, 13], [80, 13, 88, 17], [80, 17, 88, 21, "segment"], [80, 24, 88, 28], [80, 25, 88, 29, "startsWith"], [80, 35, 88, 39], [80, 36, 88, 40], [80, 42, 88, 46], [80, 43, 88, 47], [80, 47, 88, 51, "segment"], [80, 54, 88, 58], [80, 55, 88, 59, "endsWith"], [80, 63, 88, 67], [80, 64, 88, 68], [80, 67, 88, 71], [80, 68, 88, 72], [80, 70, 88, 74], [81, 8, 89, 16], [81, 12, 89, 20, "paramName"], [81, 21, 89, 29], [81, 24, 89, 32, "segment"], [81, 31, 89, 39], [81, 32, 89, 40, "slice"], [81, 37, 89, 45], [81, 38, 89, 46], [81, 39, 89, 47], [81, 41, 89, 49], [81, 42, 89, 50], [81, 43, 89, 51], [81, 44, 89, 52], [82, 8, 90, 16], [83, 8, 91, 16], [83, 12, 91, 20, "paramName"], [83, 21, 91, 29], [83, 22, 91, 30, "endsWith"], [83, 30, 91, 38], [83, 31, 91, 39], [83, 34, 91, 42], [83, 35, 91, 43], [83, 37, 91, 45], [84, 10, 92, 20, "paramName"], [84, 19, 92, 29], [84, 22, 92, 32, "paramName"], [84, 31, 92, 41], [84, 32, 92, 42, "slice"], [84, 37, 92, 47], [84, 38, 92, 48], [84, 39, 92, 49], [84, 41, 92, 51], [84, 42, 92, 52], [84, 43, 92, 53], [84, 44, 92, 54], [85, 8, 93, 16], [86, 8, 94, 16], [86, 14, 94, 22, "values"], [86, 20, 94, 28], [86, 23, 94, 31, "params"], [86, 29, 94, 37], [86, 30, 94, 38, "paramName"], [86, 39, 94, 47], [86, 40, 94, 48], [87, 8, 95, 16, "pathParams"], [87, 18, 95, 26], [87, 19, 95, 27, "add"], [87, 22, 95, 30], [87, 23, 95, 31, "paramName"], [87, 32, 95, 40], [87, 33, 95, 41], [88, 8, 96, 16], [89, 8, 97, 16], [89, 15, 97, 23, "values"], [89, 21, 97, 29], [89, 25, 97, 33], [89, 27, 97, 35], [90, 6, 98, 12], [90, 7, 98, 13], [90, 13, 99, 17], [90, 17, 99, 21, "segment"], [90, 24, 99, 28], [90, 25, 99, 29, "startsWith"], [90, 35, 99, 39], [90, 36, 99, 40], [90, 39, 99, 43], [90, 40, 99, 44], [90, 44, 99, 48, "segment"], [90, 51, 99, 55], [90, 52, 99, 56, "endsWith"], [90, 60, 99, 64], [90, 61, 99, 65], [90, 64, 99, 68], [90, 65, 99, 69], [90, 67, 99, 71], [91, 8, 100, 16], [91, 14, 100, 22, "paramName"], [91, 23, 100, 31], [91, 26, 100, 34, "segment"], [91, 33, 100, 41], [91, 34, 100, 42, "slice"], [91, 39, 100, 47], [91, 40, 100, 48], [91, 41, 100, 49], [91, 43, 100, 51], [91, 44, 100, 52], [91, 45, 100, 53], [91, 46, 100, 54], [92, 8, 101, 16], [92, 14, 101, 22, "value"], [92, 19, 101, 27], [92, 22, 101, 30, "params"], [92, 28, 101, 36], [92, 29, 101, 37, "paramName"], [92, 38, 101, 46], [92, 39, 101, 47], [93, 8, 102, 16, "pathParams"], [93, 18, 102, 26], [93, 19, 102, 27, "add"], [93, 22, 102, 30], [93, 23, 102, 31, "paramName"], [93, 32, 102, 40], [93, 33, 102, 41], [94, 8, 103, 16], [95, 8, 104, 16], [95, 15, 104, 23, "value"], [95, 20, 104, 28], [95, 23, 104, 31], [95, 24, 104, 32, "value"], [95, 29, 104, 37], [95, 30, 104, 38], [95, 33, 104, 41], [95, 35, 104, 43], [96, 6, 105, 12], [96, 7, 105, 13], [96, 13, 106, 17], [97, 8, 107, 16], [97, 15, 107, 23], [97, 16, 107, 24, "segment"], [97, 23, 107, 31], [97, 24, 107, 32], [98, 6, 108, 12], [99, 4, 109, 8], [99, 5, 109, 9], [99, 6, 109, 10], [99, 7, 110, 13, "join"], [99, 11, 110, 17], [99, 12, 110, 18], [99, 15, 110, 21], [99, 16, 110, 22], [100, 4, 111, 4], [100, 10, 111, 10, "searchParams"], [100, 22, 111, 22], [100, 25, 111, 25], [100, 29, 111, 29, "URLSearchParams"], [100, 44, 111, 44], [100, 45, 111, 45, "Object"], [100, 51, 111, 51], [100, 52, 111, 52, "entries"], [100, 59, 111, 59], [100, 60, 111, 60, "params"], [100, 66, 111, 66], [100, 67, 111, 67], [100, 68, 111, 68, "flatMap"], [100, 75, 111, 75], [100, 76, 111, 76], [100, 77, 111, 77], [100, 78, 111, 78, "key"], [100, 81, 111, 81], [100, 83, 111, 83, "value"], [100, 88, 111, 88], [100, 89, 111, 89], [100, 94, 111, 94], [101, 6, 112, 8], [102, 6, 113, 8], [102, 10, 113, 12, "pathParams"], [102, 20, 113, 22], [102, 21, 113, 23, "has"], [102, 24, 113, 26], [102, 25, 113, 27, "key"], [102, 28, 113, 30], [102, 29, 113, 31], [102, 31, 113, 33], [103, 8, 114, 12], [103, 15, 114, 19], [103, 17, 114, 21], [104, 6, 115, 8], [104, 7, 115, 9], [104, 13, 116, 13], [104, 17, 116, 17, "Array"], [104, 22, 116, 22], [104, 23, 116, 23, "isArray"], [104, 30, 116, 30], [104, 31, 116, 31, "value"], [104, 36, 116, 36], [104, 37, 116, 37], [104, 39, 116, 39], [105, 8, 117, 12], [105, 15, 117, 19, "value"], [105, 20, 117, 24], [105, 21, 117, 25, "map"], [105, 24, 117, 28], [105, 25, 117, 30, "v"], [105, 26, 117, 31], [105, 30, 117, 36], [105, 31, 117, 37, "key"], [105, 34, 117, 40], [105, 36, 117, 42, "v"], [105, 37, 117, 43], [105, 38, 117, 44], [105, 39, 117, 45], [106, 6, 118, 8], [107, 6, 119, 8], [107, 13, 119, 15], [107, 14, 119, 16], [107, 15, 119, 17, "key"], [107, 18, 119, 20], [107, 20, 119, 22, "value"], [107, 25, 119, 27], [107, 26, 119, 28], [107, 27, 119, 29], [108, 4, 120, 4], [108, 5, 120, 5], [108, 6, 120, 6], [108, 7, 120, 7], [109, 4, 121, 4], [109, 8, 121, 8, "hash"], [109, 12, 121, 12], [110, 4, 122, 4], [110, 8, 122, 8, "searchParams"], [110, 20, 122, 20], [110, 21, 122, 21, "has"], [110, 24, 122, 24], [110, 25, 122, 25], [110, 28, 122, 28], [110, 29, 122, 29], [110, 31, 122, 31], [111, 6, 123, 8, "hash"], [111, 10, 123, 12], [111, 13, 123, 15, "searchParams"], [111, 25, 123, 27], [111, 26, 123, 28, "get"], [111, 29, 123, 31], [111, 30, 123, 32], [111, 33, 123, 35], [111, 34, 123, 36], [111, 38, 123, 40, "undefined"], [111, 47, 123, 49], [112, 6, 124, 8, "searchParams"], [112, 18, 124, 20], [112, 19, 124, 21, "delete"], [112, 25, 124, 27], [112, 26, 124, 28], [112, 29, 124, 31], [112, 30, 124, 32], [113, 4, 125, 4], [114, 4, 126, 4], [115, 4, 127, 4], [115, 10, 127, 10, "searchParamString"], [115, 27, 127, 27], [115, 30, 127, 30, "searchParams"], [115, 42, 127, 42], [115, 43, 127, 43, "toString"], [115, 51, 127, 51], [115, 52, 127, 52], [115, 53, 127, 53], [116, 4, 128, 4], [116, 8, 128, 8, "pathnameWithParams"], [116, 26, 128, 26], [116, 29, 128, 29, "searchParamString"], [116, 46, 128, 46], [116, 49, 128, 49, "pathname"], [116, 57, 128, 57], [116, 60, 128, 60], [116, 63, 128, 63], [116, 66, 128, 66, "searchParamString"], [116, 83, 128, 83], [116, 86, 128, 86, "pathname"], [116, 94, 128, 94], [117, 4, 129, 4, "pathnameWithParams"], [117, 22, 129, 22], [117, 25, 129, 25, "hash"], [117, 29, 129, 29], [117, 32, 129, 32, "pathnameWithParams"], [117, 50, 129, 50], [117, 53, 129, 53], [117, 56, 129, 56], [117, 59, 129, 59, "hash"], [117, 63, 129, 63], [117, 66, 129, 66, "pathnameWithParams"], [117, 84, 129, 84], [118, 4, 130, 4], [118, 11, 130, 11], [119, 6, 131, 8, "segments"], [119, 14, 131, 16], [120, 6, 132, 8, "pathname"], [120, 14, 132, 16], [121, 6, 133, 8, "params"], [121, 12, 133, 14], [122, 6, 134, 8, "unstable_globalHref"], [122, 25, 134, 27], [122, 27, 134, 29], [122, 28, 134, 30], [122, 29, 134, 31], [122, 31, 134, 33, "getPathFromState_forks_1"], [122, 55, 134, 57], [122, 56, 134, 58, "appendBaseUrl"], [122, 69, 134, 71], [122, 71, 134, 73, "pathnameWithParams"], [122, 89, 134, 91], [122, 90, 134, 92], [123, 6, 135, 8, "searchParams"], [123, 18, 135, 20], [124, 6, 136, 8, "pathnameWithParams"], [124, 24, 136, 26], [125, 6, 137, 8], [126, 6, 138, 8, "isIndex"], [126, 13, 138, 15], [126, 15, 138, 17], [127, 4, 139, 4], [127, 5, 139, 5], [128, 2, 140, 0], [129, 0, 140, 1], [129, 3]], "functionMap": {"names": ["<global>", "getRouteInfoFromState", "segments.filter$argument_0", "segments.filter.flatMap$argument_0", "Object.entries.flatMap$argument_0", "value.map$argument_0"], "mappings": "AAA;ACgB;oBCqD;SDE;qBEC;SFoC;4EGE;6BCM,eD;KHG;CDoB"}}, "type": "js/module"}]}