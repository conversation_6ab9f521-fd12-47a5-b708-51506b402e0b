{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Animated/AnimatedEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 72}}], "key": "FMQ+jte7xY1AQ7Ojv6STtSE5k58=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Animated/nodes/AnimatedNode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 74}}], "key": "AoMFr8bJiwUMC8Sd4Sju/ZcbWMw=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Animated/nodes/AnimatedProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 76}}], "key": "pa4nhx8ZEq7RmxPNpfvQ13nqrQQ=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Animated/nodes/AnimatedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 76}}], "key": "mVhFMz9XZ4eb4eUcMsKrknEBvNg=", "exportNames": ["*"]}}, {"name": "../../../Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstanceUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 147}}], "key": "l0INauFoDzS34ls+U8uYWTVnMc8=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Utilities/useRefEffect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 69}}], "key": "UhfxlBYq2x8KSNxc7ZpWZo6aXrc=", "exportNames": ["*"]}}, {"name": "../featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 83}}], "key": "Kmhs62QxtFmVwaol79R89dYm6iA=", "exportNames": ["*"]}}, {"name": "./createAnimatedPropsMemoHook", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 74}}], "key": "iMvFZdO4r5pboqKE6UeQe2t66N4=", "exportNames": ["*"]}}, {"name": "./NativeAnimatedHelper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 58}}], "key": "CK2Pam+7Ihh1+RECOZ7WZ3WTcXE=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 29, "column": 15}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createAnimatedPropsHook;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _AnimatedEvent = require(_dependencyMap[2], \"../../../Libraries/Animated/AnimatedEvent\");\n  var _AnimatedNode = _interopRequireDefault(require(_dependencyMap[3], \"../../../Libraries/Animated/nodes/AnimatedNode\"));\n  var _AnimatedProps = _interopRequireDefault(require(_dependencyMap[4], \"../../../Libraries/Animated/nodes/AnimatedProps\"));\n  var _AnimatedValue = _interopRequireDefault(require(_dependencyMap[5], \"../../../Libraries/Animated/nodes/AnimatedValue\"));\n  var _ReactFabricPublicInstanceUtils = require(_dependencyMap[6], \"../../../Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstanceUtils\");\n  var _useRefEffect = _interopRequireDefault(require(_dependencyMap[7], \"../../../Libraries/Utilities/useRefEffect\"));\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[8], \"../featureflags/ReactNativeFeatureFlags\"));\n  var _createAnimatedPropsMemoHook = require(_dependencyMap[9], \"./createAnimatedPropsMemoHook\");\n  var _NativeAnimatedHelper = _interopRequireDefault(require(_dependencyMap[10], \"./NativeAnimatedHelper\"));\n  var _react = require(_dependencyMap[11], \"react\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function createAnimatedPropsHook(allowlist) {\n    var useAnimatedPropsMemo = (0, _createAnimatedPropsMemoHook.createAnimatedPropsMemoHook)(allowlist);\n    return function useAnimatedProps(props) {\n      var _useReducer = (0, _react.useReducer)(count => count + 1, 0),\n        _useReducer2 = (0, _slicedToArray2.default)(_useReducer, 2),\n        scheduleUpdate = _useReducer2[1];\n      var onUpdateRef = (0, _react.useRef)(null);\n      var timerRef = (0, _react.useRef)(null);\n      var node = useAnimatedPropsMemo(() => new _AnimatedProps.default(props, () => onUpdateRef.current?.(), allowlist), props);\n      var useNativePropsInFabric = ReactNativeFeatureFlags.shouldUseSetNativePropsInFabric();\n      (0, _react.useEffect)(() => {\n        _NativeAnimatedHelper.default.API.flushQueue();\n        var drivenAnimationEndedListener = null;\n        if (node.__isNative) {\n          drivenAnimationEndedListener = _NativeAnimatedHelper.default.nativeEventEmitter.addListener('onUserDrivenAnimationEnded', data => {\n            node.update();\n          });\n        }\n        return () => {\n          drivenAnimationEndedListener?.remove();\n        };\n      });\n      var useAnimatedPropsLifecycle = ReactNativeFeatureFlags.scheduleAnimatedCleanupInMicrotask() ? useAnimatedPropsLifecycleWithCleanupInMicrotask : useAnimatedPropsLifecycleWithPrevNodeRef;\n      useAnimatedPropsLifecycle(node);\n      var refEffect = (0, _react.useCallback)(instance => {\n        node.setNativeView(instance);\n        onUpdateRef.current = () => {\n          if (process.env.NODE_ENV === 'test') {\n            return scheduleUpdate();\n          }\n          var isFabricNode = isFabricInstance(instance);\n          if (node.__isNative) {\n            if (isFabricNode) {\n              scheduleUpdate();\n            }\n            return;\n          }\n          if (typeof instance !== 'object' || typeof instance?.setNativeProps !== 'function') {\n            return scheduleUpdate();\n          }\n          if (!isFabricNode) {\n            return instance.setNativeProps(node.__getAnimatedValue());\n          }\n          if (!useNativePropsInFabric) {\n            return scheduleUpdate();\n          }\n          instance.setNativeProps(node.__getAnimatedValue());\n          if (timerRef.current != null) {\n            clearTimeout(timerRef.current);\n          }\n          timerRef.current = setTimeout(() => {\n            timerRef.current = null;\n            scheduleUpdate();\n          }, 48);\n        };\n        var target = getEventTarget(instance);\n        var events = [];\n        var animatedValueListeners = [];\n        for (var propName in props) {\n          var propValue = props[propName];\n          if (propValue instanceof _AnimatedEvent.AnimatedEvent && propValue.__isNative) {\n            propValue.__attach(target, propName);\n            events.push([propName, propValue]);\n            addListenersToPropsValue(propValue, animatedValueListeners);\n          }\n        }\n        return () => {\n          onUpdateRef.current = null;\n          for (var _ref of events) {\n            var _ref2 = (0, _slicedToArray2.default)(_ref, 2);\n            var _propName = _ref2[0];\n            var _propValue = _ref2[1];\n            _propValue.__detach(target, _propName);\n          }\n          for (var _ref3 of animatedValueListeners) {\n            var _propValue2 = _ref3.propValue;\n            var listenerId = _ref3.listenerId;\n            _propValue2.removeListener(listenerId);\n          }\n        };\n      }, [node, useNativePropsInFabric, props]);\n      var callbackRef = (0, _useRefEffect.default)(refEffect);\n      return [reduceAnimatedProps(node, props), callbackRef];\n    };\n  }\n  function reduceAnimatedProps(node, props) {\n    return {\n      ...node.__getValueWithStaticProps(props),\n      collapsable: false\n    };\n  }\n  function addListenersToPropsValue(propValue, accumulator) {\n    if (propValue instanceof _AnimatedValue.default) {\n      var listenerId = propValue.addListener(() => {});\n      accumulator.push({\n        propValue,\n        listenerId\n      });\n    } else if (Array.isArray(propValue)) {\n      for (var prop of propValue) {\n        addListenersToPropsValue(prop, accumulator);\n      }\n    } else if (propValue instanceof Object) {\n      addAnimatedValuesListenersToProps(propValue, accumulator);\n    }\n  }\n  function addAnimatedValuesListenersToProps(props, accumulator) {\n    for (var propName in props) {\n      var propValue = props[propName];\n      addListenersToPropsValue(propValue, accumulator);\n    }\n  }\n  function useAnimatedPropsLifecycleWithPrevNodeRef(node) {\n    var prevNodeRef = (0, _react.useRef)(null);\n    var isUnmountingRef = (0, _react.useRef)(false);\n    (0, _react.useInsertionEffect)(() => {\n      isUnmountingRef.current = false;\n      return () => {\n        isUnmountingRef.current = true;\n      };\n    }, []);\n    (0, _react.useInsertionEffect)(() => {\n      node.__attach();\n      if (prevNodeRef.current != null) {\n        var prevNode = prevNodeRef.current;\n        prevNode.__restoreDefaultValues();\n        prevNode.__detach();\n        prevNodeRef.current = null;\n      }\n      return () => {\n        if (isUnmountingRef.current) {\n          node.__detach();\n        } else {\n          prevNodeRef.current = node;\n        }\n      };\n    }, [node]);\n  }\n  function useAnimatedPropsLifecycleWithCleanupInMicrotask(node) {\n    var isMounted = (0, _react.useRef)(false);\n    (0, _react.useInsertionEffect)(() => {\n      isMounted.current = true;\n      node.__attach();\n      return () => {\n        isMounted.current = false;\n        queueMicrotask(() => {\n          if (isMounted.current) {\n            node.__restoreDefaultValues();\n          }\n          node.__detach();\n        });\n      };\n    }, [node]);\n  }\n  function getEventTarget(instance) {\n    return typeof instance === 'object' && typeof instance?.getScrollableNode === 'function' ? instance.getScrollableNode() : instance;\n  }\n  function isFabricInstance(instance) {\n    return (0, _ReactFabricPublicInstanceUtils.isPublicInstance)(instance) || (0, _ReactFabricPublicInstanceUtils.isPublicInstance)(instance?.getNativeScrollRef?.()) || (0, _ReactFabricPublicInstanceUtils.isPublicInstance)(instance?.getScrollResponder?.()?.getNativeScrollRef?.());\n  }\n});", "lineCount": 179, "map": [[8, 2, 14, 0], [8, 6, 14, 0, "_AnimatedEvent"], [8, 20, 14, 0], [8, 23, 14, 0, "require"], [8, 30, 14, 0], [8, 31, 14, 0, "_dependencyMap"], [8, 45, 14, 0], [9, 2, 15, 0], [9, 6, 15, 0, "_AnimatedNode"], [9, 19, 15, 0], [9, 22, 15, 0, "_interopRequireDefault"], [9, 44, 15, 0], [9, 45, 15, 0, "require"], [9, 52, 15, 0], [9, 53, 15, 0, "_dependencyMap"], [9, 67, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_AnimatedProps"], [10, 20, 16, 0], [10, 23, 16, 0, "_interopRequireDefault"], [10, 45, 16, 0], [10, 46, 16, 0, "require"], [10, 53, 16, 0], [10, 54, 16, 0, "_dependencyMap"], [10, 68, 16, 0], [11, 2, 17, 0], [11, 6, 17, 0, "_AnimatedValue"], [11, 20, 17, 0], [11, 23, 17, 0, "_interopRequireDefault"], [11, 45, 17, 0], [11, 46, 17, 0, "require"], [11, 53, 17, 0], [11, 54, 17, 0, "_dependencyMap"], [11, 68, 17, 0], [12, 2, 18, 0], [12, 6, 18, 0, "_ReactFabricPublicInstanceUtils"], [12, 37, 18, 0], [12, 40, 18, 0, "require"], [12, 47, 18, 0], [12, 48, 18, 0, "_dependencyMap"], [12, 62, 18, 0], [13, 2, 19, 0], [13, 6, 19, 0, "_useRefEffect"], [13, 19, 19, 0], [13, 22, 19, 0, "_interopRequireDefault"], [13, 44, 19, 0], [13, 45, 19, 0, "require"], [13, 52, 19, 0], [13, 53, 19, 0, "_dependencyMap"], [13, 67, 19, 0], [14, 2, 20, 0], [14, 6, 20, 0, "ReactNativeFeatureFlags"], [14, 29, 20, 0], [14, 32, 20, 0, "_interopRequireWildcard"], [14, 55, 20, 0], [14, 56, 20, 0, "require"], [14, 63, 20, 0], [14, 64, 20, 0, "_dependencyMap"], [14, 78, 20, 0], [15, 2, 21, 0], [15, 6, 21, 0, "_createAnimatedPropsMemoHook"], [15, 34, 21, 0], [15, 37, 21, 0, "require"], [15, 44, 21, 0], [15, 45, 21, 0, "_dependencyMap"], [15, 59, 21, 0], [16, 2, 22, 0], [16, 6, 22, 0, "_NativeAnimatedHelper"], [16, 27, 22, 0], [16, 30, 22, 0, "_interopRequireDefault"], [16, 52, 22, 0], [16, 53, 22, 0, "require"], [16, 60, 22, 0], [16, 61, 22, 0, "_dependencyMap"], [16, 75, 22, 0], [17, 2, 23, 0], [17, 6, 23, 0, "_react"], [17, 12, 23, 0], [17, 15, 23, 0, "require"], [17, 22, 23, 0], [17, 23, 23, 0, "_dependencyMap"], [17, 37, 23, 0], [18, 2, 29, 15], [18, 11, 29, 15, "_interopRequireWildcard"], [18, 35, 29, 15, "e"], [18, 36, 29, 15], [18, 38, 29, 15, "t"], [18, 39, 29, 15], [18, 68, 29, 15, "WeakMap"], [18, 75, 29, 15], [18, 81, 29, 15, "r"], [18, 82, 29, 15], [18, 89, 29, 15, "WeakMap"], [18, 96, 29, 15], [18, 100, 29, 15, "n"], [18, 101, 29, 15], [18, 108, 29, 15, "WeakMap"], [18, 115, 29, 15], [18, 127, 29, 15, "_interopRequireWildcard"], [18, 150, 29, 15], [18, 162, 29, 15, "_interopRequireWildcard"], [18, 163, 29, 15, "e"], [18, 164, 29, 15], [18, 166, 29, 15, "t"], [18, 167, 29, 15], [18, 176, 29, 15, "t"], [18, 177, 29, 15], [18, 181, 29, 15, "e"], [18, 182, 29, 15], [18, 186, 29, 15, "e"], [18, 187, 29, 15], [18, 188, 29, 15, "__esModule"], [18, 198, 29, 15], [18, 207, 29, 15, "e"], [18, 208, 29, 15], [18, 214, 29, 15, "o"], [18, 215, 29, 15], [18, 217, 29, 15, "i"], [18, 218, 29, 15], [18, 220, 29, 15, "f"], [18, 221, 29, 15], [18, 226, 29, 15, "__proto__"], [18, 235, 29, 15], [18, 243, 29, 15, "default"], [18, 250, 29, 15], [18, 252, 29, 15, "e"], [18, 253, 29, 15], [18, 270, 29, 15, "e"], [18, 271, 29, 15], [18, 294, 29, 15, "e"], [18, 295, 29, 15], [18, 320, 29, 15, "e"], [18, 321, 29, 15], [18, 330, 29, 15, "f"], [18, 331, 29, 15], [18, 337, 29, 15, "o"], [18, 338, 29, 15], [18, 341, 29, 15, "t"], [18, 342, 29, 15], [18, 345, 29, 15, "n"], [18, 346, 29, 15], [18, 349, 29, 15, "r"], [18, 350, 29, 15], [18, 358, 29, 15, "o"], [18, 359, 29, 15], [18, 360, 29, 15, "has"], [18, 363, 29, 15], [18, 364, 29, 15, "e"], [18, 365, 29, 15], [18, 375, 29, 15, "o"], [18, 376, 29, 15], [18, 377, 29, 15, "get"], [18, 380, 29, 15], [18, 381, 29, 15, "e"], [18, 382, 29, 15], [18, 385, 29, 15, "o"], [18, 386, 29, 15], [18, 387, 29, 15, "set"], [18, 390, 29, 15], [18, 391, 29, 15, "e"], [18, 392, 29, 15], [18, 394, 29, 15, "f"], [18, 395, 29, 15], [18, 409, 29, 15, "_t"], [18, 411, 29, 15], [18, 415, 29, 15, "e"], [18, 416, 29, 15], [18, 432, 29, 15, "_t"], [18, 434, 29, 15], [18, 441, 29, 15, "hasOwnProperty"], [18, 455, 29, 15], [18, 456, 29, 15, "call"], [18, 460, 29, 15], [18, 461, 29, 15, "e"], [18, 462, 29, 15], [18, 464, 29, 15, "_t"], [18, 466, 29, 15], [18, 473, 29, 15, "i"], [18, 474, 29, 15], [18, 478, 29, 15, "o"], [18, 479, 29, 15], [18, 482, 29, 15, "Object"], [18, 488, 29, 15], [18, 489, 29, 15, "defineProperty"], [18, 503, 29, 15], [18, 508, 29, 15, "Object"], [18, 514, 29, 15], [18, 515, 29, 15, "getOwnPropertyDescriptor"], [18, 539, 29, 15], [18, 540, 29, 15, "e"], [18, 541, 29, 15], [18, 543, 29, 15, "_t"], [18, 545, 29, 15], [18, 552, 29, 15, "i"], [18, 553, 29, 15], [18, 554, 29, 15, "get"], [18, 557, 29, 15], [18, 561, 29, 15, "i"], [18, 562, 29, 15], [18, 563, 29, 15, "set"], [18, 566, 29, 15], [18, 570, 29, 15, "o"], [18, 571, 29, 15], [18, 572, 29, 15, "f"], [18, 573, 29, 15], [18, 575, 29, 15, "_t"], [18, 577, 29, 15], [18, 579, 29, 15, "i"], [18, 580, 29, 15], [18, 584, 29, 15, "f"], [18, 585, 29, 15], [18, 586, 29, 15, "_t"], [18, 588, 29, 15], [18, 592, 29, 15, "e"], [18, 593, 29, 15], [18, 594, 29, 15, "_t"], [18, 596, 29, 15], [18, 607, 29, 15, "f"], [18, 608, 29, 15], [18, 613, 29, 15, "e"], [18, 614, 29, 15], [18, 616, 29, 15, "t"], [18, 617, 29, 15], [19, 2, 49, 15], [19, 11, 49, 24, "createAnimatedPropsHook"], [19, 34, 49, 47, "createAnimatedPropsHook"], [19, 35, 50, 2, "allowlist"], [19, 44, 50, 36], [19, 46, 51, 21], [20, 4, 52, 2], [20, 8, 52, 8, "useAnimatedPropsMemo"], [20, 28, 52, 28], [20, 31, 52, 31], [20, 35, 52, 31, "createAnimatedPropsMemoHook"], [20, 91, 52, 58], [20, 93, 52, 59, "allowlist"], [20, 102, 52, 68], [20, 103, 52, 69], [21, 4, 54, 2], [21, 11, 54, 9], [21, 20, 54, 18, "useAnimatedProps"], [21, 36, 54, 34, "useAnimatedProps"], [21, 37, 55, 4, "props"], [21, 42, 55, 17], [21, 44, 56, 59], [22, 6, 57, 4], [22, 10, 57, 4, "_useReducer"], [22, 21, 57, 4], [22, 24, 57, 31], [22, 28, 57, 31, "useReducer"], [22, 45, 57, 41], [22, 47, 57, 56, "count"], [22, 52, 57, 61], [22, 56, 57, 65, "count"], [22, 61, 57, 70], [22, 64, 57, 73], [22, 65, 57, 74], [22, 67, 57, 76], [22, 68, 57, 77], [22, 69, 57, 78], [23, 8, 57, 78, "_useReducer2"], [23, 20, 57, 78], [23, 27, 57, 78, "_slicedToArray2"], [23, 42, 57, 78], [23, 43, 57, 78, "default"], [23, 50, 57, 78], [23, 52, 57, 78, "_useReducer"], [23, 63, 57, 78], [24, 8, 57, 13, "scheduleUpdate"], [24, 22, 57, 27], [24, 25, 57, 27, "_useReducer2"], [24, 37, 57, 27], [25, 6, 58, 4], [25, 10, 58, 10, "onUpdateRef"], [25, 21, 58, 21], [25, 24, 58, 24], [25, 28, 58, 24, "useRef"], [25, 41, 58, 30], [25, 43, 58, 54], [25, 47, 58, 58], [25, 48, 58, 59], [26, 6, 59, 4], [26, 10, 59, 10, "timerRef"], [26, 18, 59, 18], [26, 21, 59, 21], [26, 25, 59, 21, "useRef"], [26, 38, 59, 27], [26, 40, 59, 46], [26, 44, 59, 50], [26, 45, 59, 51], [27, 6, 61, 4], [27, 10, 61, 10, "node"], [27, 14, 61, 14], [27, 17, 61, 17, "useAnimatedPropsMemo"], [27, 37, 61, 37], [27, 38, 62, 6], [27, 44, 62, 12], [27, 48, 62, 16, "AnimatedProps"], [27, 70, 62, 29], [27, 71, 62, 30, "props"], [27, 76, 62, 35], [27, 78, 62, 37], [27, 84, 62, 43, "onUpdateRef"], [27, 95, 62, 54], [27, 96, 62, 55, "current"], [27, 103, 62, 62], [27, 106, 62, 65], [27, 107, 62, 66], [27, 109, 62, 68, "allowlist"], [27, 118, 62, 77], [27, 119, 62, 78], [27, 121, 63, 6, "props"], [27, 126, 64, 4], [27, 127, 64, 5], [28, 6, 66, 4], [28, 10, 66, 10, "useNativePropsInFabric"], [28, 32, 66, 32], [28, 35, 67, 6, "ReactNativeFeatureFlags"], [28, 58, 67, 29], [28, 59, 67, 30, "shouldUseSetNativePropsInFabric"], [28, 90, 67, 61], [28, 91, 67, 62], [28, 92, 67, 63], [29, 6, 69, 4], [29, 10, 69, 4, "useEffect"], [29, 26, 69, 13], [29, 28, 69, 14], [29, 34, 69, 20], [30, 8, 72, 6, "NativeAnimatedHelper"], [30, 37, 72, 26], [30, 38, 72, 27, "API"], [30, 41, 72, 30], [30, 42, 72, 31, "flushQueue"], [30, 52, 72, 41], [30, 53, 72, 42], [30, 54, 72, 43], [31, 8, 73, 6], [31, 12, 73, 10, "drivenAnimationEndedListener"], [31, 40, 73, 58], [31, 43, 73, 61], [31, 47, 73, 65], [32, 8, 74, 6], [32, 12, 74, 10, "node"], [32, 16, 74, 14], [32, 17, 74, 15, "__isNative"], [32, 27, 74, 25], [32, 29, 74, 27], [33, 10, 75, 8, "drivenAnimationEndedListener"], [33, 38, 75, 36], [33, 41, 76, 10, "NativeAnimatedHelper"], [33, 70, 76, 30], [33, 71, 76, 31, "nativeEventEmitter"], [33, 89, 76, 49], [33, 90, 76, 50, "addListener"], [33, 101, 76, 61], [33, 102, 77, 12], [33, 130, 77, 40], [33, 132, 78, 12, "data"], [33, 136, 78, 16], [33, 140, 78, 20], [34, 12, 79, 14, "node"], [34, 16, 79, 18], [34, 17, 79, 19, "update"], [34, 23, 79, 25], [34, 24, 79, 26], [34, 25, 79, 27], [35, 10, 80, 12], [35, 11, 81, 10], [35, 12, 81, 11], [36, 8, 82, 6], [37, 8, 84, 6], [37, 15, 84, 13], [37, 21, 84, 19], [38, 10, 85, 8, "drivenAnimationEndedListener"], [38, 38, 85, 36], [38, 40, 85, 38, "remove"], [38, 46, 85, 44], [38, 47, 85, 45], [38, 48, 85, 46], [39, 8, 86, 6], [39, 9, 86, 7], [40, 6, 87, 4], [40, 7, 87, 5], [40, 8, 87, 6], [41, 6, 91, 4], [41, 10, 91, 10, "useAnimatedPropsLifecycle"], [41, 35, 91, 35], [41, 38, 92, 6, "ReactNativeFeatureFlags"], [41, 61, 92, 29], [41, 62, 92, 30, "scheduleAnimatedCleanupInMicrotask"], [41, 96, 92, 64], [41, 97, 92, 65], [41, 98, 92, 66], [41, 101, 93, 10, "useAnimatedPropsLifecycleWithCleanupInMicrotask"], [41, 148, 93, 57], [41, 151, 94, 10, "useAnimatedPropsLifecycleWithPrevNodeRef"], [41, 191, 94, 50], [42, 6, 96, 4, "useAnimatedPropsLifecycle"], [42, 31, 96, 29], [42, 32, 96, 30, "node"], [42, 36, 96, 34], [42, 37, 96, 35], [43, 6, 111, 4], [43, 10, 111, 10, "refEffect"], [43, 19, 111, 19], [43, 22, 111, 22], [43, 26, 111, 22, "useCallback"], [43, 44, 111, 33], [43, 46, 112, 7, "instance"], [43, 54, 112, 26], [43, 58, 112, 31], [44, 8, 115, 8, "node"], [44, 12, 115, 12], [44, 13, 115, 13, "setNativeView"], [44, 26, 115, 26], [44, 27, 115, 27, "instance"], [44, 35, 115, 35], [44, 36, 115, 36], [45, 8, 120, 8, "onUpdateRef"], [45, 19, 120, 19], [45, 20, 120, 20, "current"], [45, 27, 120, 27], [45, 30, 120, 30], [45, 36, 120, 36], [46, 10, 121, 10], [46, 14, 121, 14, "process"], [46, 21, 121, 21], [46, 22, 121, 22, "env"], [46, 25, 121, 25], [46, 26, 121, 26, "NODE_ENV"], [46, 34, 121, 34], [46, 39, 121, 39], [46, 45, 121, 45], [46, 47, 121, 47], [47, 12, 124, 12], [47, 19, 124, 19, "scheduleUpdate"], [47, 33, 124, 33], [47, 34, 124, 34], [47, 35, 124, 35], [48, 10, 125, 10], [49, 10, 127, 10], [49, 14, 127, 16, "isFabricNode"], [49, 26, 127, 28], [49, 29, 127, 31, "isFabricInstance"], [49, 45, 127, 47], [49, 46, 127, 48, "instance"], [49, 54, 127, 56], [49, 55, 127, 57], [50, 10, 128, 10], [50, 14, 128, 14, "node"], [50, 18, 128, 18], [50, 19, 128, 19, "__isNative"], [50, 29, 128, 29], [50, 31, 128, 31], [51, 12, 131, 12], [51, 16, 131, 16, "isFabricNode"], [51, 28, 131, 28], [51, 30, 131, 30], [52, 14, 134, 14, "scheduleUpdate"], [52, 28, 134, 28], [52, 29, 134, 29], [52, 30, 134, 30], [53, 12, 135, 12], [54, 12, 136, 12], [55, 10, 137, 10], [56, 10, 139, 10], [56, 14, 140, 12], [56, 21, 140, 19, "instance"], [56, 29, 140, 27], [56, 34, 140, 32], [56, 42, 140, 40], [56, 46, 141, 12], [56, 53, 141, 19, "instance"], [56, 61, 141, 27], [56, 63, 141, 29, "setNativeProps"], [56, 77, 141, 43], [56, 82, 141, 48], [56, 92, 141, 58], [56, 94, 142, 12], [57, 12, 144, 12], [57, 19, 144, 19, "scheduleUpdate"], [57, 33, 144, 33], [57, 34, 144, 34], [57, 35, 144, 35], [58, 10, 145, 10], [59, 10, 147, 10], [59, 14, 147, 14], [59, 15, 147, 15, "isFabricNode"], [59, 27, 147, 27], [59, 29, 147, 29], [60, 12, 151, 12], [60, 19, 151, 19, "instance"], [60, 27, 151, 27], [60, 28, 151, 28, "setNativeProps"], [60, 42, 151, 42], [60, 43, 151, 43, "node"], [60, 47, 151, 47], [60, 48, 151, 48, "__getAnimatedValue"], [60, 66, 151, 66], [60, 67, 151, 67], [60, 68, 151, 68], [60, 69, 151, 69], [61, 10, 152, 10], [62, 10, 154, 10], [62, 14, 154, 14], [62, 15, 154, 15, "useNativePropsInFabric"], [62, 37, 154, 37], [62, 39, 154, 39], [63, 12, 156, 12], [63, 19, 156, 19, "scheduleUpdate"], [63, 33, 156, 33], [63, 34, 156, 34], [63, 35, 156, 35], [64, 10, 157, 10], [65, 10, 163, 10, "instance"], [65, 18, 163, 18], [65, 19, 163, 19, "setNativeProps"], [65, 33, 163, 33], [65, 34, 163, 34, "node"], [65, 38, 163, 38], [65, 39, 163, 39, "__getAnimatedValue"], [65, 57, 163, 57], [65, 58, 163, 58], [65, 59, 163, 59], [65, 60, 163, 60], [66, 10, 175, 10], [66, 14, 175, 14, "timerRef"], [66, 22, 175, 22], [66, 23, 175, 23, "current"], [66, 30, 175, 30], [66, 34, 175, 34], [66, 38, 175, 38], [66, 40, 175, 40], [67, 12, 176, 12, "clearTimeout"], [67, 24, 176, 24], [67, 25, 176, 25, "timerRef"], [67, 33, 176, 33], [67, 34, 176, 34, "current"], [67, 41, 176, 41], [67, 42, 176, 42], [68, 10, 177, 10], [69, 10, 178, 10, "timerRef"], [69, 18, 178, 18], [69, 19, 178, 19, "current"], [69, 26, 178, 26], [69, 29, 178, 29, "setTimeout"], [69, 39, 178, 39], [69, 40, 178, 40], [69, 46, 178, 46], [70, 12, 179, 12, "timerRef"], [70, 20, 179, 20], [70, 21, 179, 21, "current"], [70, 28, 179, 28], [70, 31, 179, 31], [70, 35, 179, 35], [71, 12, 180, 12, "scheduleUpdate"], [71, 26, 180, 26], [71, 27, 180, 27], [71, 28, 180, 28], [72, 10, 181, 10], [72, 11, 181, 11], [72, 13, 181, 13], [72, 15, 181, 15], [72, 16, 181, 16], [73, 8, 182, 8], [73, 9, 182, 9], [74, 8, 184, 8], [74, 12, 184, 14, "target"], [74, 18, 184, 20], [74, 21, 184, 23, "getEventTarget"], [74, 35, 184, 37], [74, 36, 184, 38, "instance"], [74, 44, 184, 46], [74, 45, 184, 47], [75, 8, 185, 8], [75, 12, 185, 14, "events"], [75, 18, 185, 20], [75, 21, 185, 23], [75, 23, 185, 25], [76, 8, 186, 8], [76, 12, 186, 14, "animatedValueListeners"], [76, 34, 186, 60], [76, 37, 186, 63], [76, 39, 186, 65], [77, 8, 188, 8], [77, 13, 188, 13], [77, 17, 188, 19, "propName"], [77, 25, 188, 27], [77, 29, 188, 31, "props"], [77, 34, 188, 36], [77, 36, 188, 38], [78, 10, 190, 10], [78, 14, 190, 16, "propValue"], [78, 23, 190, 25], [78, 26, 190, 28, "props"], [78, 31, 190, 33], [78, 32, 190, 34, "propName"], [78, 40, 190, 42], [78, 41, 190, 43], [79, 10, 191, 10], [79, 14, 191, 14, "propValue"], [79, 23, 191, 23], [79, 35, 191, 35, "AnimatedEvent"], [79, 63, 191, 48], [79, 67, 191, 52, "propValue"], [79, 76, 191, 61], [79, 77, 191, 62, "__isNative"], [79, 87, 191, 72], [79, 89, 191, 74], [80, 12, 192, 12, "propValue"], [80, 21, 192, 21], [80, 22, 192, 22, "__attach"], [80, 30, 192, 30], [80, 31, 192, 31, "target"], [80, 37, 192, 37], [80, 39, 192, 39, "propName"], [80, 47, 192, 47], [80, 48, 192, 48], [81, 12, 193, 12, "events"], [81, 18, 193, 18], [81, 19, 193, 19, "push"], [81, 23, 193, 23], [81, 24, 193, 24], [81, 25, 193, 25, "propName"], [81, 33, 193, 33], [81, 35, 193, 35, "propValue"], [81, 44, 193, 44], [81, 45, 193, 45], [81, 46, 193, 46], [82, 12, 195, 12, "addListenersToPropsValue"], [82, 36, 195, 36], [82, 37, 195, 37, "propValue"], [82, 46, 195, 46], [82, 48, 195, 48, "animatedValueListeners"], [82, 70, 195, 70], [82, 71, 195, 71], [83, 10, 196, 10], [84, 8, 197, 8], [85, 8, 199, 8], [85, 15, 199, 15], [85, 21, 199, 21], [86, 10, 200, 10, "onUpdateRef"], [86, 21, 200, 21], [86, 22, 200, 22, "current"], [86, 29, 200, 29], [86, 32, 200, 32], [86, 36, 200, 36], [87, 10, 202, 10], [87, 19, 202, 10, "_ref"], [87, 23, 202, 10], [87, 27, 202, 46, "events"], [87, 33, 202, 52], [87, 35, 202, 54], [88, 12, 202, 54], [88, 16, 202, 54, "_ref2"], [88, 21, 202, 54], [88, 28, 202, 54, "_slicedToArray2"], [88, 43, 202, 54], [88, 44, 202, 54, "default"], [88, 51, 202, 54], [88, 53, 202, 54, "_ref"], [88, 57, 202, 54], [89, 12, 202, 54], [89, 16, 202, 22, "propName"], [89, 25, 202, 30], [89, 28, 202, 30, "_ref2"], [89, 33, 202, 30], [90, 12, 202, 30], [90, 16, 202, 32, "propValue"], [90, 26, 202, 41], [90, 29, 202, 41, "_ref2"], [90, 34, 202, 41], [91, 12, 203, 12, "propValue"], [91, 22, 203, 21], [91, 23, 203, 22, "__detach"], [91, 31, 203, 30], [91, 32, 203, 31, "target"], [91, 38, 203, 37], [91, 40, 203, 39, "propName"], [91, 49, 203, 47], [91, 50, 203, 48], [92, 10, 204, 10], [93, 10, 206, 10], [93, 19, 206, 10, "_ref3"], [93, 24, 206, 10], [93, 28, 206, 48, "animatedValueListeners"], [93, 50, 206, 70], [93, 52, 206, 72], [94, 12, 206, 72], [94, 16, 206, 22, "propValue"], [94, 27, 206, 31], [94, 30, 206, 31, "_ref3"], [94, 35, 206, 31], [94, 36, 206, 22, "propValue"], [94, 45, 206, 31], [95, 12, 206, 31], [95, 16, 206, 33, "listenerId"], [95, 26, 206, 43], [95, 29, 206, 43, "_ref3"], [95, 34, 206, 43], [95, 35, 206, 33, "listenerId"], [95, 45, 206, 43], [96, 12, 207, 12, "propValue"], [96, 23, 207, 21], [96, 24, 207, 22, "removeListener"], [96, 38, 207, 36], [96, 39, 207, 37, "listenerId"], [96, 49, 207, 47], [96, 50, 207, 48], [97, 10, 208, 10], [98, 8, 209, 8], [98, 9, 209, 9], [99, 6, 210, 6], [99, 7, 210, 7], [99, 9, 211, 6], [99, 10, 211, 7, "node"], [99, 14, 211, 11], [99, 16, 211, 13, "useNativePropsInFabric"], [99, 38, 211, 35], [99, 40, 211, 37, "props"], [99, 45, 211, 42], [99, 46, 212, 4], [99, 47, 212, 5], [100, 6, 213, 4], [100, 10, 213, 10, "callback<PERSON><PERSON>"], [100, 21, 213, 21], [100, 24, 213, 24], [100, 28, 213, 24, "useRefEffect"], [100, 49, 213, 36], [100, 51, 213, 48, "refEffect"], [100, 60, 213, 57], [100, 61, 213, 58], [101, 6, 215, 4], [101, 13, 215, 11], [101, 14, 215, 12, "reduceAnimatedProps"], [101, 33, 215, 31], [101, 34, 215, 40, "node"], [101, 38, 215, 44], [101, 40, 215, 46, "props"], [101, 45, 215, 51], [101, 46, 215, 52], [101, 48, 215, 54, "callback<PERSON><PERSON>"], [101, 59, 215, 65], [101, 60, 215, 66], [102, 4, 216, 2], [102, 5, 216, 3], [103, 2, 217, 0], [104, 2, 219, 0], [104, 11, 219, 9, "reduceAnimatedProps"], [104, 30, 219, 28, "reduceAnimatedProps"], [104, 31, 220, 2, "node"], [104, 35, 220, 21], [104, 37, 221, 2, "props"], [104, 42, 221, 15], [104, 44, 222, 24], [105, 4, 225, 2], [105, 11, 225, 9], [106, 6, 226, 4], [106, 9, 226, 7, "node"], [106, 13, 226, 11], [106, 14, 226, 12, "__getValueWithStaticProps"], [106, 39, 226, 37], [106, 40, 226, 38, "props"], [106, 45, 226, 43], [106, 46, 226, 44], [107, 6, 227, 4, "collapsable"], [107, 17, 227, 15], [107, 19, 227, 17], [108, 4, 228, 2], [108, 5, 228, 3], [109, 2, 229, 0], [110, 2, 231, 0], [110, 11, 231, 9, "addListenersToPropsValue"], [110, 35, 231, 33, "addListenersToPropsValue"], [110, 36, 232, 2, "propValue"], [110, 45, 232, 26], [110, 47, 233, 2, "accumulator"], [110, 58, 233, 37], [110, 60, 234, 2], [111, 4, 236, 2], [111, 8, 236, 6, "propValue"], [111, 17, 236, 15], [111, 29, 236, 27, "AnimatedValue"], [111, 51, 236, 40], [111, 53, 236, 42], [112, 6, 237, 4], [112, 10, 237, 10, "listenerId"], [112, 20, 237, 20], [112, 23, 237, 23, "propValue"], [112, 32, 237, 32], [112, 33, 237, 33, "addListener"], [112, 44, 237, 44], [112, 45, 237, 45], [112, 51, 237, 51], [112, 52, 237, 52], [112, 53, 237, 53], [112, 54, 237, 54], [113, 6, 238, 4, "accumulator"], [113, 17, 238, 15], [113, 18, 238, 16, "push"], [113, 22, 238, 20], [113, 23, 238, 21], [114, 8, 238, 22, "propValue"], [114, 17, 238, 31], [115, 8, 238, 33, "listenerId"], [116, 6, 238, 43], [116, 7, 238, 44], [116, 8, 238, 45], [117, 4, 239, 2], [117, 5, 239, 3], [117, 11, 239, 9], [117, 15, 239, 13, "Array"], [117, 20, 239, 18], [117, 21, 239, 19, "isArray"], [117, 28, 239, 26], [117, 29, 239, 27, "propValue"], [117, 38, 239, 36], [117, 39, 239, 37], [117, 41, 239, 39], [118, 6, 241, 4], [118, 11, 241, 9], [118, 15, 241, 15, "prop"], [118, 19, 241, 19], [118, 23, 241, 23, "propValue"], [118, 32, 241, 32], [118, 34, 241, 34], [119, 8, 242, 6, "addListenersToPropsValue"], [119, 32, 242, 30], [119, 33, 242, 31, "prop"], [119, 37, 242, 35], [119, 39, 242, 37, "accumulator"], [119, 50, 242, 48], [119, 51, 242, 49], [120, 6, 243, 4], [121, 4, 244, 2], [121, 5, 244, 3], [121, 11, 244, 9], [121, 15, 244, 13, "propValue"], [121, 24, 244, 22], [121, 36, 244, 34, "Object"], [121, 42, 244, 40], [121, 44, 244, 42], [122, 6, 245, 4, "addAnimatedValuesListenersToProps"], [122, 39, 245, 37], [122, 40, 245, 38, "propValue"], [122, 49, 245, 47], [122, 51, 245, 49, "accumulator"], [122, 62, 245, 60], [122, 63, 245, 61], [123, 4, 246, 2], [124, 2, 247, 0], [125, 2, 249, 0], [125, 11, 249, 9, "addAnimatedValuesListenersToProps"], [125, 44, 249, 42, "addAnimatedValuesListenersToProps"], [125, 45, 250, 2, "props"], [125, 50, 250, 21], [125, 52, 251, 2, "accumulator"], [125, 63, 251, 37], [125, 65, 252, 2], [126, 4, 253, 2], [126, 9, 253, 7], [126, 13, 253, 13, "propName"], [126, 21, 253, 21], [126, 25, 253, 25, "props"], [126, 30, 253, 30], [126, 32, 253, 32], [127, 6, 255, 4], [127, 10, 255, 10, "propValue"], [127, 19, 255, 19], [127, 22, 255, 22, "props"], [127, 27, 255, 27], [127, 28, 255, 28, "propName"], [127, 36, 255, 36], [127, 37, 255, 37], [128, 6, 256, 4, "addListenersToPropsValue"], [128, 30, 256, 28], [128, 31, 256, 29, "propValue"], [128, 40, 256, 38], [128, 42, 256, 40, "accumulator"], [128, 53, 256, 51], [128, 54, 256, 52], [129, 4, 257, 2], [130, 2, 258, 0], [131, 2, 267, 0], [131, 11, 267, 9, "useAnimatedPropsLifecycleWithPrevNodeRef"], [131, 51, 267, 49, "useAnimatedPropsLifecycleWithPrevNodeRef"], [131, 52, 267, 50, "node"], [131, 56, 267, 69], [131, 58, 267, 77], [132, 4, 268, 2], [132, 8, 268, 8, "prevNodeRef"], [132, 19, 268, 19], [132, 22, 268, 22], [132, 26, 268, 22, "useRef"], [132, 39, 268, 28], [132, 41, 268, 45], [132, 45, 268, 49], [132, 46, 268, 50], [133, 4, 269, 2], [133, 8, 269, 8, "isUnmountingRef"], [133, 23, 269, 23], [133, 26, 269, 26], [133, 30, 269, 26, "useRef"], [133, 43, 269, 32], [133, 45, 269, 42], [133, 50, 269, 47], [133, 51, 269, 48], [134, 4, 271, 2], [134, 8, 271, 2, "useInsertionEffect"], [134, 33, 271, 20], [134, 35, 271, 21], [134, 41, 271, 27], [135, 6, 272, 4, "isUnmountingRef"], [135, 21, 272, 19], [135, 22, 272, 20, "current"], [135, 29, 272, 27], [135, 32, 272, 30], [135, 37, 272, 35], [136, 6, 273, 4], [136, 13, 273, 11], [136, 19, 273, 17], [137, 8, 274, 6, "isUnmountingRef"], [137, 23, 274, 21], [137, 24, 274, 22, "current"], [137, 31, 274, 29], [137, 34, 274, 32], [137, 38, 274, 36], [138, 6, 275, 4], [138, 7, 275, 5], [139, 4, 276, 2], [139, 5, 276, 3], [139, 7, 276, 5], [139, 9, 276, 7], [139, 10, 276, 8], [140, 4, 278, 2], [140, 8, 278, 2, "useInsertionEffect"], [140, 33, 278, 20], [140, 35, 278, 21], [140, 41, 278, 27], [141, 6, 279, 4, "node"], [141, 10, 279, 8], [141, 11, 279, 9, "__attach"], [141, 19, 279, 17], [141, 20, 279, 18], [141, 21, 279, 19], [142, 6, 280, 4], [142, 10, 280, 8, "prevNodeRef"], [142, 21, 280, 19], [142, 22, 280, 20, "current"], [142, 29, 280, 27], [142, 33, 280, 31], [142, 37, 280, 35], [142, 39, 280, 37], [143, 8, 281, 6], [143, 12, 281, 12, "prevNode"], [143, 20, 281, 20], [143, 23, 281, 23, "prevNodeRef"], [143, 34, 281, 34], [143, 35, 281, 35, "current"], [143, 42, 281, 42], [144, 8, 283, 6, "prevNode"], [144, 16, 283, 14], [144, 17, 283, 15, "__restore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [144, 39, 283, 37], [144, 40, 283, 38], [144, 41, 283, 39], [145, 8, 284, 6, "prevNode"], [145, 16, 284, 14], [145, 17, 284, 15, "__detach"], [145, 25, 284, 23], [145, 26, 284, 24], [145, 27, 284, 25], [146, 8, 285, 6, "prevNodeRef"], [146, 19, 285, 17], [146, 20, 285, 18, "current"], [146, 27, 285, 25], [146, 30, 285, 28], [146, 34, 285, 32], [147, 6, 286, 4], [148, 6, 287, 4], [148, 13, 287, 11], [148, 19, 287, 17], [149, 8, 288, 6], [149, 12, 288, 10, "isUnmountingRef"], [149, 27, 288, 25], [149, 28, 288, 26, "current"], [149, 35, 288, 33], [149, 37, 288, 35], [150, 10, 290, 8, "node"], [150, 14, 290, 12], [150, 15, 290, 13, "__detach"], [150, 23, 290, 21], [150, 24, 290, 22], [150, 25, 290, 23], [151, 8, 291, 6], [151, 9, 291, 7], [151, 15, 291, 13], [152, 10, 292, 8, "prevNodeRef"], [152, 21, 292, 19], [152, 22, 292, 20, "current"], [152, 29, 292, 27], [152, 32, 292, 30, "node"], [152, 36, 292, 34], [153, 8, 293, 6], [154, 6, 294, 4], [154, 7, 294, 5], [155, 4, 295, 2], [155, 5, 295, 3], [155, 7, 295, 5], [155, 8, 295, 6, "node"], [155, 12, 295, 10], [155, 13, 295, 11], [155, 14, 295, 12], [156, 2, 296, 0], [157, 2, 312, 0], [157, 11, 312, 9, "useAnimatedPropsLifecycleWithCleanupInMicrotask"], [157, 58, 312, 56, "useAnimatedPropsLifecycleWithCleanupInMicrotask"], [157, 59, 313, 2, "node"], [157, 63, 313, 21], [157, 65, 314, 8], [158, 4, 315, 2], [158, 8, 315, 8, "isMounted"], [158, 17, 315, 17], [158, 20, 315, 20], [158, 24, 315, 20, "useRef"], [158, 37, 315, 26], [158, 39, 315, 36], [158, 44, 315, 41], [158, 45, 315, 42], [159, 4, 317, 2], [159, 8, 317, 2, "useInsertionEffect"], [159, 33, 317, 20], [159, 35, 317, 21], [159, 41, 317, 27], [160, 6, 318, 4, "isMounted"], [160, 15, 318, 13], [160, 16, 318, 14, "current"], [160, 23, 318, 21], [160, 26, 318, 24], [160, 30, 318, 28], [161, 6, 319, 4, "node"], [161, 10, 319, 8], [161, 11, 319, 9, "__attach"], [161, 19, 319, 17], [161, 20, 319, 18], [161, 21, 319, 19], [162, 6, 321, 4], [162, 13, 321, 11], [162, 19, 321, 17], [163, 8, 322, 6, "isMounted"], [163, 17, 322, 15], [163, 18, 322, 16, "current"], [163, 25, 322, 23], [163, 28, 322, 26], [163, 33, 322, 31], [164, 8, 323, 6, "queueMicrotask"], [164, 22, 323, 20], [164, 23, 323, 21], [164, 29, 323, 27], [165, 10, 325, 8], [165, 14, 325, 12, "isMounted"], [165, 23, 325, 21], [165, 24, 325, 22, "current"], [165, 31, 325, 29], [165, 33, 325, 31], [166, 12, 327, 10, "node"], [166, 16, 327, 14], [166, 17, 327, 15, "__restore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [166, 39, 327, 37], [166, 40, 327, 38], [166, 41, 327, 39], [167, 10, 328, 8], [168, 10, 329, 8, "node"], [168, 14, 329, 12], [168, 15, 329, 13, "__detach"], [168, 23, 329, 21], [168, 24, 329, 22], [168, 25, 329, 23], [169, 8, 330, 6], [169, 9, 330, 7], [169, 10, 330, 8], [170, 6, 331, 4], [170, 7, 331, 5], [171, 4, 332, 2], [171, 5, 332, 3], [171, 7, 332, 5], [171, 8, 332, 6, "node"], [171, 12, 332, 10], [171, 13, 332, 11], [171, 14, 332, 12], [172, 2, 333, 0], [173, 2, 335, 0], [173, 11, 335, 9, "getEventTarget"], [173, 25, 335, 23, "getEventTarget"], [173, 26, 335, 35, "instance"], [173, 34, 335, 54], [173, 36, 335, 67], [174, 4, 336, 2], [174, 11, 336, 9], [174, 18, 336, 16, "instance"], [174, 26, 336, 24], [174, 31, 336, 29], [174, 39, 336, 37], [174, 43, 337, 4], [174, 50, 337, 11, "instance"], [174, 58, 337, 19], [174, 60, 337, 21, "getScrollableNode"], [174, 77, 337, 38], [174, 82, 337, 43], [174, 92, 337, 53], [174, 95, 339, 6, "instance"], [174, 103, 339, 14], [174, 104, 339, 15, "getScrollableNode"], [174, 121, 339, 32], [174, 122, 339, 33], [174, 123, 339, 34], [174, 126, 340, 6, "instance"], [174, 134, 340, 14], [175, 2, 341, 0], [176, 2, 344, 0], [176, 11, 344, 9, "isFabricInstance"], [176, 27, 344, 25, "isFabricInstance"], [176, 28, 344, 26, "instance"], [176, 36, 344, 39], [176, 38, 344, 50], [177, 4, 345, 2], [177, 11, 346, 4], [177, 15, 346, 4, "isFabricPublicInstance"], [177, 63, 346, 26], [177, 65, 346, 27, "instance"], [177, 73, 346, 35], [177, 74, 346, 36], [177, 78, 355, 4], [177, 82, 355, 4, "isFabricPublicInstance"], [177, 130, 355, 26], [177, 132, 355, 27, "instance"], [177, 140, 355, 35], [177, 142, 355, 37, "getNativeScrollRef"], [177, 160, 355, 55], [177, 163, 355, 58], [177, 164, 355, 59], [177, 165, 355, 60], [177, 169, 356, 4], [177, 173, 356, 4, "isFabricPublicInstance"], [177, 221, 356, 26], [177, 223, 357, 6, "instance"], [177, 231, 357, 14], [177, 233, 357, 16, "getScrollResponder"], [177, 251, 357, 34], [177, 254, 357, 37], [177, 255, 357, 38], [177, 257, 357, 40, "getNativeScrollRef"], [177, 275, 357, 58], [177, 278, 357, 61], [177, 279, 358, 4], [177, 280, 358, 5], [178, 2, 360, 0], [179, 0, 360, 1], [179, 3]], "functionMap": {"names": ["<global>", "createAnimatedPropsHook", "useAnimatedProps", "useReducer$argument_0", "useAnimatedPropsMemo$argument_0", "AnimatedProps$argument_1", "useEffect$argument_0", "NativeAnimatedHelper.nativeEventEmitter.addListener$argument_1", "<anonymous>", "refEffect", "onUpdateRef.current", "setTimeout$argument_0", "reduceAnimatedProps", "addListenersToPropsValue", "propValue.addListener$argument_0", "addAnimatedValuesListenersToProps", "useAnimatedPropsLifecycleWithPrevNodeRef", "useInsertionEffect$argument_0", "useAnimatedPropsLifecycleWithCleanupInMicrotask", "queueMicrotask$argument_0", "getEventTarget", "isFabricInstance"], "mappings": "AAA;eCgD;SCK;wDCG,kBD;MEK,+BC,6BD,YF;cIO;YCS;aDE;aEI;OFE;KJC;MOyB;8BCQ;wCC0D;WDG;SDC;eDiB;SCU;OPC;GDM;CDC;AYE;CZU;AaE;6CCM,QD;CbU;AeE;CfS;AgBS;qBCI;WTE;KSE;GDC;qBCE;WTS;KSO;GDC;ChBC;AkBgB;qBDK;WTI;qBWE;OXO;KSC;GCC;ClBC;AoBE;CpBM;AqBG"}}, "type": "js/module"}]}