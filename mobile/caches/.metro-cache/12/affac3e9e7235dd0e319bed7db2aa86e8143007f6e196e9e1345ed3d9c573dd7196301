{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./specs/NativeSafeAreaContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 49}, "end": {"line": 2, "column": 66, "index": 115}}], "key": "Rn6npybfVmwilu1Y3utVbJONsDs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.initialWindowSafeAreaInsets = exports.initialWindowMetrics = void 0;\n  var _NativeSafeAreaContext = _interopRequireDefault(require(_dependencyMap[1], \"./specs/NativeSafeAreaContext\"));\n  var initialWindowMetrics = exports.initialWindowMetrics = _NativeSafeAreaContext.default?.getConstants?.()?.initialWindowMetrics ?? null;\n\n  /**\n   * @deprecated\n   */\n  var initialWindowSafeAreaInsets = exports.initialWindowSafeAreaInsets = initialWindowMetrics?.insets;\n});", "lineCount": 14, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_NativeSafeAreaContext"], [7, 28, 2, 0], [7, 31, 2, 0, "_interopRequireDefault"], [7, 53, 2, 0], [7, 54, 2, 0, "require"], [7, 61, 2, 0], [7, 62, 2, 0, "_dependencyMap"], [7, 76, 2, 0], [8, 2, 4, 7], [8, 6, 4, 13, "initialWindowMetrics"], [8, 26, 4, 33], [8, 29, 4, 33, "exports"], [8, 36, 4, 33], [8, 37, 4, 33, "initialWindowMetrics"], [8, 57, 4, 33], [8, 60, 4, 37, "NativeSafeAreaContext"], [8, 90, 4, 58], [8, 92, 4, 60, "getConstants"], [8, 104, 4, 72], [8, 107, 4, 75], [8, 108, 4, 76], [8, 110, 5, 4, "initialWindowMetrics"], [8, 130, 5, 24], [8, 134, 5, 28], [8, 138, 5, 51], [10, 2, 7, 0], [11, 0, 8, 0], [12, 0, 9, 0], [13, 2, 10, 7], [13, 6, 10, 13, "initialWindowSafeAreaInsets"], [13, 33, 10, 40], [13, 36, 10, 40, "exports"], [13, 43, 10, 40], [13, 44, 10, 40, "initialWindowSafeAreaInsets"], [13, 71, 10, 40], [13, 74, 10, 43, "initialWindowMetrics"], [13, 94, 10, 63], [13, 96, 10, 65, "insets"], [13, 102, 10, 71], [14, 0, 10, 72], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}