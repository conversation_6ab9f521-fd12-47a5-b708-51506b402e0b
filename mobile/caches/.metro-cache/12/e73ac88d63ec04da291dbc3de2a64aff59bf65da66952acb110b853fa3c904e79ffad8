{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./CurrentRenderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 65, "index": 112}}], "key": "GTNXIdAk+LGdgfwJMP6/M0rzCrs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useCurrentRender = useCurrentRender;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _CurrentRenderContext = require(_dependencyMap[1], \"./CurrentRenderContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Write the current options, so that server renderer can get current values\n   * Mutating values like this is not safe in async mode, but it doesn't apply to SSR\n   */\n  function useCurrentRender({\n    state,\n    navigation,\n    descriptors\n  }) {\n    const current = React.useContext(_CurrentRenderContext.CurrentRenderContext);\n    if (current && navigation.isFocused()) {\n      current.options = descriptors[state.routes[state.index].key].options;\n    }\n  }\n});", "lineCount": 25, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useCurrentRender"], [7, 26, 1, 13], [7, 29, 1, 13, "useCurrentRender"], [7, 45, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_CurrentRenderContext"], [9, 27, 4, 0], [9, 30, 4, 0, "require"], [9, 37, 4, 0], [9, 38, 4, 0, "_dependencyMap"], [9, 52, 4, 0], [10, 2, 4, 65], [10, 11, 4, 65, "_interopRequireWildcard"], [10, 35, 4, 65, "e"], [10, 36, 4, 65], [10, 38, 4, 65, "t"], [10, 39, 4, 65], [10, 68, 4, 65, "WeakMap"], [10, 75, 4, 65], [10, 81, 4, 65, "r"], [10, 82, 4, 65], [10, 89, 4, 65, "WeakMap"], [10, 96, 4, 65], [10, 100, 4, 65, "n"], [10, 101, 4, 65], [10, 108, 4, 65, "WeakMap"], [10, 115, 4, 65], [10, 127, 4, 65, "_interopRequireWildcard"], [10, 150, 4, 65], [10, 162, 4, 65, "_interopRequireWildcard"], [10, 163, 4, 65, "e"], [10, 164, 4, 65], [10, 166, 4, 65, "t"], [10, 167, 4, 65], [10, 176, 4, 65, "t"], [10, 177, 4, 65], [10, 181, 4, 65, "e"], [10, 182, 4, 65], [10, 186, 4, 65, "e"], [10, 187, 4, 65], [10, 188, 4, 65, "__esModule"], [10, 198, 4, 65], [10, 207, 4, 65, "e"], [10, 208, 4, 65], [10, 214, 4, 65, "o"], [10, 215, 4, 65], [10, 217, 4, 65, "i"], [10, 218, 4, 65], [10, 220, 4, 65, "f"], [10, 221, 4, 65], [10, 226, 4, 65, "__proto__"], [10, 235, 4, 65], [10, 243, 4, 65, "default"], [10, 250, 4, 65], [10, 252, 4, 65, "e"], [10, 253, 4, 65], [10, 270, 4, 65, "e"], [10, 271, 4, 65], [10, 294, 4, 65, "e"], [10, 295, 4, 65], [10, 320, 4, 65, "e"], [10, 321, 4, 65], [10, 330, 4, 65, "f"], [10, 331, 4, 65], [10, 337, 4, 65, "o"], [10, 338, 4, 65], [10, 341, 4, 65, "t"], [10, 342, 4, 65], [10, 345, 4, 65, "n"], [10, 346, 4, 65], [10, 349, 4, 65, "r"], [10, 350, 4, 65], [10, 358, 4, 65, "o"], [10, 359, 4, 65], [10, 360, 4, 65, "has"], [10, 363, 4, 65], [10, 364, 4, 65, "e"], [10, 365, 4, 65], [10, 375, 4, 65, "o"], [10, 376, 4, 65], [10, 377, 4, 65, "get"], [10, 380, 4, 65], [10, 381, 4, 65, "e"], [10, 382, 4, 65], [10, 385, 4, 65, "o"], [10, 386, 4, 65], [10, 387, 4, 65, "set"], [10, 390, 4, 65], [10, 391, 4, 65, "e"], [10, 392, 4, 65], [10, 394, 4, 65, "f"], [10, 395, 4, 65], [10, 411, 4, 65, "t"], [10, 412, 4, 65], [10, 416, 4, 65, "e"], [10, 417, 4, 65], [10, 433, 4, 65, "t"], [10, 434, 4, 65], [10, 441, 4, 65, "hasOwnProperty"], [10, 455, 4, 65], [10, 456, 4, 65, "call"], [10, 460, 4, 65], [10, 461, 4, 65, "e"], [10, 462, 4, 65], [10, 464, 4, 65, "t"], [10, 465, 4, 65], [10, 472, 4, 65, "i"], [10, 473, 4, 65], [10, 477, 4, 65, "o"], [10, 478, 4, 65], [10, 481, 4, 65, "Object"], [10, 487, 4, 65], [10, 488, 4, 65, "defineProperty"], [10, 502, 4, 65], [10, 507, 4, 65, "Object"], [10, 513, 4, 65], [10, 514, 4, 65, "getOwnPropertyDescriptor"], [10, 538, 4, 65], [10, 539, 4, 65, "e"], [10, 540, 4, 65], [10, 542, 4, 65, "t"], [10, 543, 4, 65], [10, 550, 4, 65, "i"], [10, 551, 4, 65], [10, 552, 4, 65, "get"], [10, 555, 4, 65], [10, 559, 4, 65, "i"], [10, 560, 4, 65], [10, 561, 4, 65, "set"], [10, 564, 4, 65], [10, 568, 4, 65, "o"], [10, 569, 4, 65], [10, 570, 4, 65, "f"], [10, 571, 4, 65], [10, 573, 4, 65, "t"], [10, 574, 4, 65], [10, 576, 4, 65, "i"], [10, 577, 4, 65], [10, 581, 4, 65, "f"], [10, 582, 4, 65], [10, 583, 4, 65, "t"], [10, 584, 4, 65], [10, 588, 4, 65, "e"], [10, 589, 4, 65], [10, 590, 4, 65, "t"], [10, 591, 4, 65], [10, 602, 4, 65, "f"], [10, 603, 4, 65], [10, 608, 4, 65, "e"], [10, 609, 4, 65], [10, 611, 4, 65, "t"], [10, 612, 4, 65], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 2, 9, 7], [15, 11, 9, 16, "useCurrentRender"], [15, 27, 9, 32, "useCurrentRender"], [15, 28, 9, 33], [16, 4, 10, 2, "state"], [16, 9, 10, 7], [17, 4, 11, 2, "navigation"], [17, 14, 11, 12], [18, 4, 12, 2, "descriptors"], [19, 2, 13, 0], [19, 3, 13, 1], [19, 5, 13, 3], [20, 4, 14, 2], [20, 10, 14, 8, "current"], [20, 17, 14, 15], [20, 20, 14, 18, "React"], [20, 25, 14, 23], [20, 26, 14, 24, "useContext"], [20, 36, 14, 34], [20, 37, 14, 35, "CurrentRenderContext"], [20, 79, 14, 55], [20, 80, 14, 56], [21, 4, 15, 2], [21, 8, 15, 6, "current"], [21, 15, 15, 13], [21, 19, 15, 17, "navigation"], [21, 29, 15, 27], [21, 30, 15, 28, "isFocused"], [21, 39, 15, 37], [21, 40, 15, 38], [21, 41, 15, 39], [21, 43, 15, 41], [22, 6, 16, 4, "current"], [22, 13, 16, 11], [22, 14, 16, 12, "options"], [22, 21, 16, 19], [22, 24, 16, 22, "descriptors"], [22, 35, 16, 33], [22, 36, 16, 34, "state"], [22, 41, 16, 39], [22, 42, 16, 40, "routes"], [22, 48, 16, 46], [22, 49, 16, 47, "state"], [22, 54, 16, 52], [22, 55, 16, 53, "index"], [22, 60, 16, 58], [22, 61, 16, 59], [22, 62, 16, 60, "key"], [22, 65, 16, 63], [22, 66, 16, 64], [22, 67, 16, 65, "options"], [22, 74, 16, 72], [23, 4, 17, 2], [24, 2, 18, 0], [25, 0, 18, 1], [25, 3]], "functionMap": {"names": ["<global>", "useCurrentRender"], "mappings": "AAA;OCQ;CDS"}}, "type": "js/module"}]}