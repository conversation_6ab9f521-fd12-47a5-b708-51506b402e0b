{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeAccessibilityManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 70}}], "key": "5nAPSfLc4JYryd2jgm5WoGa8GQI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeAccessibilityManager = _interopRequireDefault(require(_dependencyMap[1], \"./NativeAccessibilityManager\"));\n  function legacySendAccessibilityEvent(reactTag, eventType) {\n    if (eventType === 'focus' && _NativeAccessibilityManager.default) {\n      _NativeAccessibilityManager.default.setAccessibilityFocus(reactTag);\n    }\n  }\n  var _default = exports.default = legacySendAccessibilityEvent;\n});", "lineCount": 14, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeAccessibilityManager"], [7, 33, 11, 0], [7, 36, 11, 0, "_interopRequireDefault"], [7, 58, 11, 0], [7, 59, 11, 0, "require"], [7, 66, 11, 0], [7, 67, 11, 0, "_dependencyMap"], [7, 81, 11, 0], [8, 2, 17, 0], [8, 11, 17, 9, "legacySendAccessibilityEvent"], [8, 39, 17, 37, "legacySendAccessibilityEvent"], [8, 40, 18, 2, "reactTag"], [8, 48, 18, 18], [8, 50, 19, 2, "eventType"], [8, 59, 19, 19], [8, 61, 20, 8], [9, 4, 21, 2], [9, 8, 21, 6, "eventType"], [9, 17, 21, 15], [9, 22, 21, 20], [9, 29, 21, 27], [9, 33, 21, 31, "NativeAccessibilityManager"], [9, 68, 21, 57], [9, 70, 21, 59], [10, 6, 22, 4, "NativeAccessibilityManager"], [10, 41, 22, 30], [10, 42, 22, 31, "setAccessibilityFocus"], [10, 63, 22, 52], [10, 64, 22, 53, "reactTag"], [10, 72, 22, 61], [10, 73, 22, 62], [11, 4, 23, 2], [12, 2, 24, 0], [13, 2, 24, 1], [13, 6, 24, 1, "_default"], [13, 14, 24, 1], [13, 17, 24, 1, "exports"], [13, 24, 24, 1], [13, 25, 24, 1, "default"], [13, 32, 24, 1], [13, 35, 26, 15, "legacySendAccessibilityEvent"], [13, 63, 26, 43], [14, 0, 26, 43], [14, 3]], "functionMap": {"names": ["<global>", "legacySendAccessibilityEvent"], "mappings": "AAA;ACgB;CDO"}}, "type": "js/module"}]}