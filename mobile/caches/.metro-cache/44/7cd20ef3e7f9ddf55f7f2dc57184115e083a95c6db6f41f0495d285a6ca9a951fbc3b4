{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.translate = exports.toMatrix3 = exports.scale = exports.rotateZ = exports.rotateY = exports.rotateX = exports.processTransform3d = exports.pivot = exports.perspective = exports.multiply4 = exports.matrixVecMul4 = exports.mapPoint3d = exports.invert4 = exports.convertToColumnMajor = exports.convertToAffineMatrix = exports.Matrix4 = void 0;\n  const _worklet_8129029734104_init_data = {\n    code: \"function shopify_Matrix4Js1(a){throw new Error(\\\"Unexhaustive handling for \\\"+a);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js1\\\",\\\"a\\\",\\\"Error\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAAwB,QAAC,CAAAA,kBAAIA,CAAAC,CAAA,EAG3B,KAAM,IAAI,CAAAC,KAAK,8BAA8BD,CAAG,CAAC,CACnD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const exhaustiveCheck = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js1 = function (a) {\n      throw new Error(`Unexhaustive handling for ${a}`);\n    };\n    shopify_Matrix4Js1.__closure = {};\n    shopify_Matrix4Js1.__workletHash = 8129029734104;\n    shopify_Matrix4Js1.__initData = _worklet_8129029734104_init_data;\n    shopify_Matrix4Js1.__stackDetails = _e;\n    return shopify_Matrix4Js1;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_14818525896400_init_data = {\n    code: \"function shopify_Matrix4Js2(){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js2\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AASuB,SAAAA,kBAAMA,CAAA,EAG3B,MAAO,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACzD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const Matrix4 = exports.Matrix4 = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js2 = function () {\n      return [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1];\n    };\n    shopify_Matrix4Js2.__closure = {};\n    shopify_Matrix4Js2.__workletHash = 14818525896400;\n    shopify_Matrix4Js2.__initData = _worklet_14818525896400_init_data;\n    shopify_Matrix4Js2.__stackDetails = _e;\n    return shopify_Matrix4Js2;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_1283578139948_init_data = {\n    code: \"function shopify_Matrix4Js3(x,y,z=0){return[1,0,0,x,0,1,0,y,0,0,1,z,0,0,0,1];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js3\\\",\\\"x\\\",\\\"y\\\",\\\"z\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAkByB,QAAC,CAAAA,kBAAgBA,CAAAC,CAAA,CAAAC,CAAA,CAAAC,CAAA,IAGxC,MAAO,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAEF,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACzD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const translate = exports.translate = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js3 = function (x, y, z = 0) {\n      return [1, 0, 0, x, 0, 1, 0, y, 0, 0, 1, z, 0, 0, 0, 1];\n    };\n    shopify_Matrix4Js3.__closure = {};\n    shopify_Matrix4Js3.__workletHash = 1283578139948;\n    shopify_Matrix4Js3.__initData = _worklet_1283578139948_init_data;\n    shopify_Matrix4Js3.__stackDetails = _e;\n    return shopify_Matrix4Js3;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_7347197968821_init_data = {\n    code: \"function shopify_Matrix4Js4(p){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,-1/p,1];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js4\\\",\\\"p\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA2B2B,QAAC,CAAAA,kBAAIA,CAAAC,CAAA,EAG9B,MAAO,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAGA,CAAC,CAAE,CAAC,CAAC,CAC9D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const perspective = exports.perspective = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js4 = function (p) {\n      return [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, -1 / p, 1];\n    };\n    shopify_Matrix4Js4.__closure = {};\n    shopify_Matrix4Js4.__workletHash = 7347197968821;\n    shopify_Matrix4Js4.__initData = _worklet_7347197968821_init_data;\n    shopify_Matrix4Js4.__stackDetails = _e;\n    return shopify_Matrix4Js4;\n  }();\n  const _worklet_10397893766365_init_data = {\n    code: \"function shopify_Matrix4Js5(vec){const[x,y,z]=vec;const length=Math.sqrt(x*x+y*y+z*z);if(length===0){return[0,0,0];}return[x/length,y/length,z/length];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js5\\\",\\\"vec\\\",\\\"x\\\",\\\"y\\\",\\\"z\\\",\\\"length\\\",\\\"Math\\\",\\\"sqrt\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAgCqB,SAAAA,kBAAOA,CAAAC,GAAA,EAG1B,KAAM,CAACC,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAC,CAAGH,GAAG,CACrB,KAAM,CAAAI,MAAM,CAAGC,IAAI,CAACC,IAAI,CAACL,CAAC,CAAGA,CAAC,CAAGC,CAAC,CAAGA,CAAC,CAAGC,CAAC,CAAGA,CAAC,CAAC,CAE/C,GAAIC,MAAM,GAAK,CAAC,CAAE,CAChB,MAAO,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAClB,CACA,MAAO,CAACH,CAAC,CAAGG,MAAM,CAAEF,CAAC,CAAGE,MAAM,CAAED,CAAC,CAAGC,MAAM,CAAC,CAC7C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const normalizeVec = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js5 = function (vec) {\n      const [x, y, z] = vec;\n      const length = Math.sqrt(x * x + y * y + z * z);\n      // Check for zero length to avoid division by zero\n      if (length === 0) {\n        return [0, 0, 0];\n      }\n      return [x / length, y / length, z / length];\n    };\n    shopify_Matrix4Js5.__closure = {};\n    shopify_Matrix4Js5.__workletHash = 10397893766365;\n    shopify_Matrix4Js5.__initData = _worklet_10397893766365_init_data;\n    shopify_Matrix4Js5.__stackDetails = _e;\n    return shopify_Matrix4Js5;\n  }();\n  const _worklet_14051658427690_init_data = {\n    code: \"function shopify_Matrix4Js6(axisVec,sinAngle,cosAngle){const x=axisVec[0];const y=axisVec[1];const z=axisVec[2];const c=cosAngle;const s=sinAngle;const t=1-c;return[t*x*x+c,t*x*y-s*z,t*x*z+s*y,0,t*x*y+s*z,t*y*y+c,t*y*z-s*x,0,t*x*z-s*y,t*y*z+s*x,t*z*z+c,0,0,0,0,1];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js6\\\",\\\"axisVec\\\",\\\"sinAngle\\\",\\\"cosAngle\\\",\\\"x\\\",\\\"y\\\",\\\"z\\\",\\\"c\\\",\\\"s\\\",\\\"t\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA2C0B,QAAC,CAAAA,kBAASA,CAAQC,OAAE,CAAAC,QAAa,CAAAC,QAAA,EAGzD,KAAM,CAAAC,CAAC,CAAGH,OAAO,CAAC,CAAC,CAAC,CACpB,KAAM,CAAAI,CAAC,CAAGJ,OAAO,CAAC,CAAC,CAAC,CACpB,KAAM,CAAAK,CAAC,CAAGL,OAAO,CAAC,CAAC,CAAC,CACpB,KAAM,CAAAM,CAAC,CAAGJ,QAAQ,CAClB,KAAM,CAAAK,CAAC,CAAGN,QAAQ,CAClB,KAAM,CAAAO,CAAC,CAAG,CAAC,CAAGF,CAAC,CACf,MAAO,CAACE,CAAC,CAAGL,CAAC,CAAGA,CAAC,CAAGG,CAAC,CAAEE,CAAC,CAAGL,CAAC,CAAGC,CAAC,CAAGG,CAAC,CAAGF,CAAC,CAAEG,CAAC,CAAGL,CAAC,CAAGE,CAAC,CAAGE,CAAC,CAAGH,CAAC,CAAE,CAAC,CAAEI,CAAC,CAAGL,CAAC,CAAGC,CAAC,CAAGG,CAAC,CAAGF,CAAC,CAAEG,CAAC,CAAGJ,CAAC,CAAGA,CAAC,CAAGE,CAAC,CAAEE,CAAC,CAAGJ,CAAC,CAAGC,CAAC,CAAGE,CAAC,CAAGJ,CAAC,CAAE,CAAC,CAAEK,CAAC,CAAGL,CAAC,CAAGE,CAAC,CAAGE,CAAC,CAAGH,CAAC,CAAEI,CAAC,CAAGJ,CAAC,CAAGC,CAAC,CAAGE,CAAC,CAAGJ,CAAC,CAAEK,CAAC,CAAGH,CAAC,CAAGA,CAAC,CAAGC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7L\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rotatedUnitSinCos = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js6 = function (axisVec, sinAngle, cosAngle) {\n      const x = axisVec[0];\n      const y = axisVec[1];\n      const z = axisVec[2];\n      const c = cosAngle;\n      const s = sinAngle;\n      const t = 1 - c;\n      return [t * x * x + c, t * x * y - s * z, t * x * z + s * y, 0, t * x * y + s * z, t * y * y + c, t * y * z - s * x, 0, t * x * z - s * y, t * y * z + s * x, t * z * z + c, 0, 0, 0, 0, 1];\n    };\n    shopify_Matrix4Js6.__closure = {};\n    shopify_Matrix4Js6.__workletHash = 14051658427690;\n    shopify_Matrix4Js6.__initData = _worklet_14051658427690_init_data;\n    shopify_Matrix4Js6.__stackDetails = _e;\n    return shopify_Matrix4Js6;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_15831962852770_init_data = {\n    code: \"function shopify_Matrix4Js7(m,v){return[m[0]*v[0]+m[1]*v[1]+m[2]*v[2]+m[3]*v[3],m[4]*v[0]+m[5]*v[1]+m[6]*v[2]+m[7]*v[3],m[8]*v[0]+m[9]*v[1]+m[10]*v[2]+m[11]*v[3],m[12]*v[0]+m[13]*v[1]+m[14]*v[2]+m[15]*v[3]];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js7\\\",\\\"m\\\",\\\"v\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA0D6B,QAAC,CAAAA,kBAASA,CAAAC,CAAA,CAAAC,CAAA,EAGrC,MAAO,CAACD,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAED,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAED,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,CAAC,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,EAAE,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,EAAE,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAED,CAAC,CAAC,EAAE,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,EAAE,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,EAAE,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,EAAE,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC3O\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const matrixVecMul4 = exports.matrixVecMul4 = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js7 = function (m, v) {\n      return [m[0] * v[0] + m[1] * v[1] + m[2] * v[2] + m[3] * v[3], m[4] * v[0] + m[5] * v[1] + m[6] * v[2] + m[7] * v[3], m[8] * v[0] + m[9] * v[1] + m[10] * v[2] + m[11] * v[3], m[12] * v[0] + m[13] * v[1] + m[14] * v[2] + m[15] * v[3]];\n    };\n    shopify_Matrix4Js7.__closure = {};\n    shopify_Matrix4Js7.__workletHash = 15831962852770;\n    shopify_Matrix4Js7.__initData = _worklet_15831962852770_init_data;\n    shopify_Matrix4Js7.__stackDetails = _e;\n    return shopify_Matrix4Js7;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_6689500399552_init_data = {\n    code: \"function shopify_Matrix4Js8(m,v){const{matrixVecMul4}=this.__closure;const r=matrixVecMul4(m,[...v,1]);return[r[0]/r[3],r[1]/r[3],r[2]/r[3]];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js8\\\",\\\"m\\\",\\\"v\\\",\\\"matrixVecMul4\\\",\\\"__closure\\\",\\\"r\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAmE0B,QAAC,CAAAA,kBAASA,CAAAC,CAAA,CAAAC,CAAA,QAAAC,aAAA,OAAAC,SAAA,CAGlC,KAAM,CAAAC,CAAC,CAAGF,aAAa,CAACF,CAAC,CAAE,CAAC,GAAGC,CAAC,CAAE,CAAC,CAAC,CAAC,CACrC,MAAO,CAACG,CAAC,CAAC,CAAC,CAAC,CAAGA,CAAC,CAAC,CAAC,CAAC,CAAEA,CAAC,CAAC,CAAC,CAAC,CAAGA,CAAC,CAAC,CAAC,CAAC,CAAEA,CAAC,CAAC,CAAC,CAAC,CAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAChD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const mapPoint3d = exports.mapPoint3d = function () {\n    const _e = [new global.Error(), -2, -27];\n    const shopify_Matrix4Js8 = function (m, v) {\n      const r = matrixVecMul4(m, [...v, 1]);\n      return [r[0] / r[3], r[1] / r[3], r[2] / r[3]];\n    };\n    shopify_Matrix4Js8.__closure = {\n      matrixVecMul4\n    };\n    shopify_Matrix4Js8.__workletHash = 6689500399552;\n    shopify_Matrix4Js8.__initData = _worklet_6689500399552_init_data;\n    shopify_Matrix4Js8.__stackDetails = _e;\n    return shopify_Matrix4Js8;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_5765954600262_init_data = {\n    code: \"function shopify_Matrix4Js9(a,b){const result=new Array(16).fill(0);for(let i=0;i<4;i++){for(let j=0;j<4;j++){result[i*4+j]=a[i*4]*b[j]+a[i*4+1]*b[j+4]+a[i*4+2]*b[j+8]+a[i*4+3]*b[j+12];}}return result;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js9\\\",\\\"a\\\",\\\"b\\\",\\\"result\\\",\\\"Array\\\",\\\"fill\\\",\\\"i\\\",\\\"j\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA6EyB,QAAC,CAAAA,kBAASA,CAAAC,CAAA,CAAAC,CAAA,EAGjC,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CACpC,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC1B,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC1BJ,MAAM,CAACG,CAAC,CAAG,CAAC,CAAGC,CAAC,CAAC,CAAGN,CAAC,CAACK,CAAC,CAAG,CAAC,CAAC,CAAGJ,CAAC,CAACK,CAAC,CAAC,CAAGN,CAAC,CAACK,CAAC,CAAG,CAAC,CAAG,CAAC,CAAC,CAAGJ,CAAC,CAACK,CAAC,CAAG,CAAC,CAAC,CAAGN,CAAC,CAACK,CAAC,CAAG,CAAC,CAAG,CAAC,CAAC,CAAGJ,CAAC,CAACK,CAAC,CAAG,CAAC,CAAC,CAAGN,CAAC,CAACK,CAAC,CAAG,CAAC,CAAG,CAAC,CAAC,CAAGJ,CAAC,CAACK,CAAC,CAAG,EAAE,CAAC,CACpH,CACF,CACA,MAAO,CAAAJ,MAAM,CACf\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const multiply4 = exports.multiply4 = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js9 = function (a, b) {\n      const result = new Array(16).fill(0);\n      for (let i = 0; i < 4; i++) {\n        for (let j = 0; j < 4; j++) {\n          result[i * 4 + j] = a[i * 4] * b[j] + a[i * 4 + 1] * b[j + 4] + a[i * 4 + 2] * b[j + 8] + a[i * 4 + 3] * b[j + 12];\n        }\n      }\n      return result;\n    };\n    shopify_Matrix4Js9.__closure = {};\n    shopify_Matrix4Js9.__workletHash = 5765954600262;\n    shopify_Matrix4Js9.__initData = _worklet_5765954600262_init_data;\n    shopify_Matrix4Js9.__stackDetails = _e;\n    return shopify_Matrix4Js9;\n  }();\n  const _worklet_4773075386487_init_data = {\n    code: \"function shopify_Matrix4Js10(angle){return[1,Math.tan(angle),0,0,0,1,0,0,0,0,1,0,0,0,0,1];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js10\\\",\\\"angle\\\",\\\"Math\\\",\\\"tan\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAwFc,SAAAA,mBAASA,CAAAC,KAAA,EAGrB,MAAO,CAAC,CAAC,CAAEC,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACvE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const skewY = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js10 = function (angle) {\n      return [1, Math.tan(angle), 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1];\n    };\n    shopify_Matrix4Js10.__closure = {};\n    shopify_Matrix4Js10.__workletHash = 4773075386487;\n    shopify_Matrix4Js10.__initData = _worklet_4773075386487_init_data;\n    shopify_Matrix4Js10.__stackDetails = _e;\n    return shopify_Matrix4Js10;\n  }();\n  const _worklet_1871192719510_init_data = {\n    code: \"function shopify_Matrix4Js11(angle){return[1,0,0,0,Math.tan(angle),1,0,0,0,0,1,0,0,0,0,1];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js11\\\",\\\"angle\\\",\\\"Math\\\",\\\"tan\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA6Fc,SAAAA,mBAASA,CAAAC,KAAA,EAGrB,MAAO,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAEC,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACvE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const skewX = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js11 = function (angle) {\n      return [1, 0, 0, 0, Math.tan(angle), 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1];\n    };\n    shopify_Matrix4Js11.__closure = {};\n    shopify_Matrix4Js11.__workletHash = 1871192719510;\n    shopify_Matrix4Js11.__initData = _worklet_1871192719510_init_data;\n    shopify_Matrix4Js11.__stackDetails = _e;\n    return shopify_Matrix4Js11;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_10458516888298_init_data = {\n    code: \"function shopify_Matrix4Js12(m){return[m[0],m[1],m[3],m[4],m[5],m[7],m[12],m[13],m[15]];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js12\\\",\\\"m\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAsGyB,QAAC,CAAAA,mBAAIA,CAAAC,CAAA,EAG5B,MAAO,CAACA,CAAC,CAAC,CAAC,CAAC,CAAEA,CAAC,CAAC,CAAC,CAAC,CAAEA,CAAC,CAAC,CAAC,CAAC,CAAEA,CAAC,CAAC,CAAC,CAAC,CAAEA,CAAC,CAAC,CAAC,CAAC,CAAEA,CAAC,CAAC,CAAC,CAAC,CAAEA,CAAC,CAAC,EAAE,CAAC,CAAEA,CAAC,CAAC,EAAE,CAAC,CAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAClE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const toMatrix3 = exports.toMatrix3 = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js12 = function (m) {\n      return [m[0], m[1], m[3], m[4], m[5], m[7], m[12], m[13], m[15]];\n    };\n    shopify_Matrix4Js12.__closure = {};\n    shopify_Matrix4Js12.__workletHash = 10458516888298;\n    shopify_Matrix4Js12.__initData = _worklet_10458516888298_init_data;\n    shopify_Matrix4Js12.__stackDetails = _e;\n    return shopify_Matrix4Js12;\n  }();\n  const _worklet_1303846517270_init_data = {\n    code: \"function shopify_Matrix4Js13(axis,value){const{rotatedUnitSinCos,normalizeVec}=this.__closure;return rotatedUnitSinCos(normalizeVec(axis),Math.sin(value),Math.cos(value));}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js13\\\",\\\"axis\\\",\\\"value\\\",\\\"rotatedUnitSinCos\\\",\\\"normalizeVec\\\",\\\"__closure\\\",\\\"Math\\\",\\\"sin\\\",\\\"cos\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA2Ge,QAAC,CAAAA,mBAAgBA,CAAAC,IAAA,CAAAC,KAAA,QAAAC,iBAAA,CAAAC,YAAA,OAAAC,SAAA,CAG9B,MAAO,CAAAF,iBAAiB,CAACC,YAAY,CAACH,IAAI,CAAC,CAAEK,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,CAAEI,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,CAAC,CAChF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rotate = function () {\n    const _e = [new global.Error(), -3, -27];\n    const shopify_Matrix4Js13 = function (axis, value) {\n      return rotatedUnitSinCos(normalizeVec(axis), Math.sin(value), Math.cos(value));\n    };\n    shopify_Matrix4Js13.__closure = {\n      rotatedUnitSinCos,\n      normalizeVec\n    };\n    shopify_Matrix4Js13.__workletHash = 1303846517270;\n    shopify_Matrix4Js13.__initData = _worklet_1303846517270_init_data;\n    shopify_Matrix4Js13.__stackDetails = _e;\n    return shopify_Matrix4Js13;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_11430923795455_init_data = {\n    code: \"function shopify_Matrix4Js14(m,p){const{multiply4,translate}=this.__closure;return multiply4(translate(p.x,p.y),multiply4(m,translate(-p.x,-p.y)));}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js14\\\",\\\"m\\\",\\\"p\\\",\\\"multiply4\\\",\\\"translate\\\",\\\"__closure\\\",\\\"x\\\",\\\"y\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAoHqB,QAAC,CAAAA,mBAASA,CAAAC,CAAA,CAAAC,CAAA,QAAAC,SAAA,CAAAC,SAAA,OAAAC,SAAA,CAG7B,MAAO,CAAAF,SAAS,CAACC,SAAS,CAACF,CAAC,CAACI,CAAC,CAAEJ,CAAC,CAACK,CAAC,CAAC,CAAEJ,SAAS,CAACF,CAAC,CAAEG,SAAS,CAAC,CAACF,CAAC,CAACI,CAAC,CAAE,CAACJ,CAAC,CAACK,CAAC,CAAC,CAAC,CAAC,CAC5E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const pivot = exports.pivot = function () {\n    const _e = [new global.Error(), -3, -27];\n    const shopify_Matrix4Js14 = function (m, p) {\n      return multiply4(translate(p.x, p.y), multiply4(m, translate(-p.x, -p.y)));\n    };\n    shopify_Matrix4Js14.__closure = {\n      multiply4,\n      translate\n    };\n    shopify_Matrix4Js14.__workletHash = 11430923795455;\n    shopify_Matrix4Js14.__initData = _worklet_11430923795455_init_data;\n    shopify_Matrix4Js14.__stackDetails = _e;\n    return shopify_Matrix4Js14;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_16520454403957_init_data = {\n    code: \"function shopify_Matrix4Js15(sx,sy,sz=1,p){const{pivot}=this.__closure;const m4=[sx,0,0,0,0,sy,0,0,0,0,sz,0,0,0,0,1];if(p){return pivot(m4,p);}return m4;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js15\\\",\\\"sx\\\",\\\"sy\\\",\\\"sz\\\",\\\"p\\\",\\\"pivot\\\",\\\"__closure\\\",\\\"m4\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA6HqB,QAAC,CAAAA,mBAAiBA,CAAAC,EAAK,CAAAC,EAAA,CAAAC,EAAA,GAAAC,CAAA,QAAAC,KAAA,OAAAC,SAAA,CAG1C,KAAM,CAAAC,EAAE,CAAG,CAACN,EAAE,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC9D,GAAIC,CAAC,CAAE,CACL,MAAO,CAAAC,KAAK,CAACE,EAAE,CAAEH,CAAC,CAAC,CACrB,CACA,MAAO,CAAAG,EAAE,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const scale = exports.scale = function () {\n    const _e = [new global.Error(), -2, -27];\n    const shopify_Matrix4Js15 = function (sx, sy, sz = 1, p) {\n      const m4 = [sx, 0, 0, 0, 0, sy, 0, 0, 0, 0, sz, 0, 0, 0, 0, 1];\n      if (p) {\n        return pivot(m4, p);\n      }\n      return m4;\n    };\n    shopify_Matrix4Js15.__closure = {\n      pivot\n    };\n    shopify_Matrix4Js15.__workletHash = 16520454403957;\n    shopify_Matrix4Js15.__initData = _worklet_16520454403957_init_data;\n    shopify_Matrix4Js15.__stackDetails = _e;\n    return shopify_Matrix4Js15;\n  }();\n  const _worklet_16952079578652_init_data = {\n    code: \"function shopify_Matrix4Js16(axis,angle,p){const{rotate,pivot}=this.__closure;const result=rotate(axis,angle);if(p){return pivot(result,p);}return result;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js16\\\",\\\"axis\\\",\\\"angle\\\",\\\"p\\\",\\\"rotate\\\",\\\"pivot\\\",\\\"__closure\\\",\\\"result\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAsImB,QAAC,CAAAA,mBAAmBA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,CAAA,QAAAC,MAAA,CAAAC,KAAA,OAAAC,SAAA,CAGrC,KAAM,CAAAC,MAAM,CAAGH,MAAM,CAACH,IAAI,CAAEC,KAAK,CAAC,CAClC,GAAIC,CAAC,CAAE,CACL,MAAO,CAAAE,KAAK,CAACE,MAAM,CAAEJ,CAAC,CAAC,CACzB,CACA,MAAO,CAAAI,MAAM,CACf\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rotateAxis = function () {\n    const _e = [new global.Error(), -3, -27];\n    const shopify_Matrix4Js16 = function (axis, angle, p) {\n      const result = rotate(axis, angle);\n      if (p) {\n        return pivot(result, p);\n      }\n      return result;\n    };\n    shopify_Matrix4Js16.__closure = {\n      rotate,\n      pivot\n    };\n    shopify_Matrix4Js16.__workletHash = 16952079578652;\n    shopify_Matrix4Js16.__initData = _worklet_16952079578652_init_data;\n    shopify_Matrix4Js16.__stackDetails = _e;\n    return shopify_Matrix4Js16;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_3243560710632_init_data = {\n    code: \"function shopify_Matrix4Js17(value,p){const{rotateAxis}=this.__closure;return rotateAxis([0,0,1],value,p);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js17\\\",\\\"value\\\",\\\"p\\\",\\\"rotateAxis\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAmJuB,QAAC,CAAAA,mBAAaA,CAAAC,KAAA,CAAAC,CAAA,QAAAC,UAAA,OAAAC,SAAA,CAGnC,MAAO,CAAAD,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAEF,KAAK,CAAEC,CAAC,CAAC,CACxC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rotateZ = exports.rotateZ = function () {\n    const _e = [new global.Error(), -2, -27];\n    const shopify_Matrix4Js17 = function (value, p) {\n      return rotateAxis([0, 0, 1], value, p);\n    };\n    shopify_Matrix4Js17.__closure = {\n      rotateAxis\n    };\n    shopify_Matrix4Js17.__workletHash = 3243560710632;\n    shopify_Matrix4Js17.__initData = _worklet_3243560710632_init_data;\n    shopify_Matrix4Js17.__stackDetails = _e;\n    return shopify_Matrix4Js17;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_5701320477575_init_data = {\n    code: \"function shopify_Matrix4Js18(value,p){const{rotateAxis}=this.__closure;return rotateAxis([1,0,0],value,p);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js18\\\",\\\"value\\\",\\\"p\\\",\\\"rotateAxis\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA4JuB,QAAC,CAAAA,mBAAaA,CAAAC,KAAA,CAAAC,CAAA,QAAAC,UAAA,OAAAC,SAAA,CAGnC,MAAO,CAAAD,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAEF,KAAK,CAAEC,CAAC,CAAC,CACxC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rotateX = exports.rotateX = function () {\n    const _e = [new global.Error(), -2, -27];\n    const shopify_Matrix4Js18 = function (value, p) {\n      return rotateAxis([1, 0, 0], value, p);\n    };\n    shopify_Matrix4Js18.__closure = {\n      rotateAxis\n    };\n    shopify_Matrix4Js18.__workletHash = 5701320477575;\n    shopify_Matrix4Js18.__initData = _worklet_5701320477575_init_data;\n    shopify_Matrix4Js18.__stackDetails = _e;\n    return shopify_Matrix4Js18;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_12107565173542_init_data = {\n    code: \"function shopify_Matrix4Js19(value,p){const{rotateAxis}=this.__closure;return rotateAxis([0,1,0],value,p);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js19\\\",\\\"value\\\",\\\"p\\\",\\\"rotateAxis\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAqKuB,QAAC,CAAAA,mBAAaA,CAAAC,KAAA,CAAAC,CAAA,QAAAC,UAAA,OAAAC,SAAA,CAGnC,MAAO,CAAAD,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAEF,KAAK,CAAEC,CAAC,CAAC,CACxC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rotateY = exports.rotateY = function () {\n    const _e = [new global.Error(), -2, -27];\n    const shopify_Matrix4Js19 = function (value, p) {\n      return rotateAxis([0, 1, 0], value, p);\n    };\n    shopify_Matrix4Js19.__closure = {\n      rotateAxis\n    };\n    shopify_Matrix4Js19.__workletHash = 12107565173542;\n    shopify_Matrix4Js19.__initData = _worklet_12107565173542_init_data;\n    shopify_Matrix4Js19.__stackDetails = _e;\n    return shopify_Matrix4Js19;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_2225508254275_init_data = {\n    code: \"function shopify_Matrix4Js20(transforms){const{multiply4,translate,scale,skewX,skewY,rotate,perspective,exhaustiveCheck,Matrix4}=this.__closure;return transforms.reduce(function(acc,val){const key=Object.keys(val)[0];const transform=val;if(key===\\\"translateX\\\"){const value=transform[key];return multiply4(acc,translate(value,0,0));}if(key===\\\"translate\\\"){const[x,y,z=0]=transform[key];return multiply4(acc,translate(x,y,z));}if(key===\\\"translateY\\\"){const value=transform[key];return multiply4(acc,translate(0,value,0));}if(key===\\\"translateZ\\\"){const value=transform[key];return multiply4(acc,translate(0,0,value));}if(key===\\\"scale\\\"){const value=transform[key];return multiply4(acc,scale(value,value,1));}if(key===\\\"scaleX\\\"){const value=transform[key];return multiply4(acc,scale(value,1,1));}if(key===\\\"scaleY\\\"){const value=transform[key];return multiply4(acc,scale(1,value,1));}if(key===\\\"skewX\\\"){const value=transform[key];return multiply4(acc,skewX(value));}if(key===\\\"skewY\\\"){const value=transform[key];return multiply4(acc,skewY(value));}if(key===\\\"rotateX\\\"){const value=transform[key];return multiply4(acc,rotate([1,0,0],value));}if(key===\\\"rotateY\\\"){const value=transform[key];return multiply4(acc,rotate([0,1,0],value));}if(key===\\\"perspective\\\"){const value=transform[key];return multiply4(acc,perspective(value));}if(key===\\\"rotate\\\"||key===\\\"rotateZ\\\"){const value=transform[key];return multiply4(acc,rotate([0,0,1],value));}if(key===\\\"matrix\\\"){const value=transform[key];return multiply4(acc,value);}return exhaustiveCheck(key);},Matrix4());}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js20\\\",\\\"transforms\\\",\\\"multiply4\\\",\\\"translate\\\",\\\"scale\\\",\\\"skewX\\\",\\\"skewY\\\",\\\"rotate\\\",\\\"perspective\\\",\\\"exhaustiveCheck\\\",\\\"Matrix4\\\",\\\"__closure\\\",\\\"reduce\\\",\\\"acc\\\",\\\"val\\\",\\\"key\\\",\\\"Object\\\",\\\"keys\\\",\\\"transform\\\",\\\"value\\\",\\\"x\\\",\\\"y\\\",\\\"z\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AA8KkC,SAAAA,mBAAcA,CAAAC,UAAA,QAAAC,SAAA,CAAAC,SAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,eAAA,CAAAC,OAAA,OAAAC,SAAA,CAG9C,MAAO,CAAAV,UAAU,CAACW,MAAM,CAAC,SAACC,GAAG,CAAEC,GAAG,CAAK,CACrC,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAC/B,KAAM,CAAAI,SAAS,CAAGJ,GAAG,CACrB,GAAIC,GAAG,GAAK,YAAY,CAAE,CACxB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEV,SAAS,CAACgB,KAAK,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC,CAC/C,CACA,GAAIJ,GAAG,GAAK,WAAW,CAAE,CACvB,KAAM,CAACK,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAG,CAAC,CAAC,CAAGJ,SAAS,CAACH,GAAG,CAAC,CACpC,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEV,SAAS,CAACiB,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAC,CAAC,CAC3C,CACA,GAAIP,GAAG,GAAK,YAAY,CAAE,CACxB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEV,SAAS,CAAC,CAAC,CAAEgB,KAAK,CAAE,CAAC,CAAC,CAAC,CAC/C,CACA,GAAIJ,GAAG,GAAK,YAAY,CAAE,CACxB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEV,SAAS,CAAC,CAAC,CAAE,CAAC,CAAEgB,KAAK,CAAC,CAAC,CAC/C,CACA,GAAIJ,GAAG,GAAK,OAAO,CAAE,CACnB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAET,KAAK,CAACe,KAAK,CAAEA,KAAK,CAAE,CAAC,CAAC,CAAC,CAC/C,CACA,GAAIJ,GAAG,GAAK,QAAQ,CAAE,CACpB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAET,KAAK,CAACe,KAAK,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC,CAC3C,CACA,GAAIJ,GAAG,GAAK,QAAQ,CAAE,CACpB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAET,KAAK,CAAC,CAAC,CAAEe,KAAK,CAAE,CAAC,CAAC,CAAC,CAC3C,CACA,GAAIJ,GAAG,GAAK,OAAO,CAAE,CACnB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAER,KAAK,CAACc,KAAK,CAAC,CAAC,CACrC,CACA,GAAIJ,GAAG,GAAK,OAAO,CAAE,CACnB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEP,KAAK,CAACa,KAAK,CAAC,CAAC,CACrC,CACA,GAAIJ,GAAG,GAAK,SAAS,CAAE,CACrB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEN,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAEY,KAAK,CAAC,CAAC,CACjD,CACA,GAAIJ,GAAG,GAAK,SAAS,CAAE,CACrB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEN,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAEY,KAAK,CAAC,CAAC,CACjD,CACA,GAAIJ,GAAG,GAAK,aAAa,CAAE,CACzB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEL,WAAW,CAACW,KAAK,CAAC,CAAC,CAC3C,CACA,GAAIJ,GAAG,GAAK,QAAQ,EAAIA,GAAG,GAAK,SAAS,CAAE,CACzC,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEN,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAEY,KAAK,CAAC,CAAC,CACjD,CACA,GAAIJ,GAAG,GAAK,QAAQ,CAAE,CACpB,KAAM,CAAAI,KAAK,CAAGD,SAAS,CAACH,GAAG,CAAC,CAC5B,MAAO,CAAAb,SAAS,CAACW,GAAG,CAAEM,KAAK,CAAC,CAC9B,CACA,MAAO,CAAAV,eAAe,CAACM,GAAG,CAAC,CAC7B,CAAC,CAAEL,OAAO,CAAC,CAAC,CAAC,CACf\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processTransform3d = exports.processTransform3d = function () {\n    const _e = [new global.Error(), -10, -27];\n    const shopify_Matrix4Js20 = function (transforms) {\n      return transforms.reduce((acc, val) => {\n        const key = Object.keys(val)[0];\n        const transform = val;\n        if (key === \"translateX\") {\n          const value = transform[key];\n          return multiply4(acc, translate(value, 0, 0));\n        }\n        if (key === \"translate\") {\n          const [x, y, z = 0] = transform[key];\n          return multiply4(acc, translate(x, y, z));\n        }\n        if (key === \"translateY\") {\n          const value = transform[key];\n          return multiply4(acc, translate(0, value, 0));\n        }\n        if (key === \"translateZ\") {\n          const value = transform[key];\n          return multiply4(acc, translate(0, 0, value));\n        }\n        if (key === \"scale\") {\n          const value = transform[key];\n          return multiply4(acc, scale(value, value, 1));\n        }\n        if (key === \"scaleX\") {\n          const value = transform[key];\n          return multiply4(acc, scale(value, 1, 1));\n        }\n        if (key === \"scaleY\") {\n          const value = transform[key];\n          return multiply4(acc, scale(1, value, 1));\n        }\n        if (key === \"skewX\") {\n          const value = transform[key];\n          return multiply4(acc, skewX(value));\n        }\n        if (key === \"skewY\") {\n          const value = transform[key];\n          return multiply4(acc, skewY(value));\n        }\n        if (key === \"rotateX\") {\n          const value = transform[key];\n          return multiply4(acc, rotate([1, 0, 0], value));\n        }\n        if (key === \"rotateY\") {\n          const value = transform[key];\n          return multiply4(acc, rotate([0, 1, 0], value));\n        }\n        if (key === \"perspective\") {\n          const value = transform[key];\n          return multiply4(acc, perspective(value));\n        }\n        if (key === \"rotate\" || key === \"rotateZ\") {\n          const value = transform[key];\n          return multiply4(acc, rotate([0, 0, 1], value));\n        }\n        if (key === \"matrix\") {\n          const value = transform[key];\n          return multiply4(acc, value);\n        }\n        return exhaustiveCheck(key);\n      }, Matrix4());\n    };\n    shopify_Matrix4Js20.__closure = {\n      multiply4,\n      translate,\n      scale,\n      skewX,\n      skewY,\n      rotate,\n      perspective,\n      exhaustiveCheck,\n      Matrix4\n    };\n    shopify_Matrix4Js20.__workletHash = 2225508254275;\n    shopify_Matrix4Js20.__initData = _worklet_2225508254275_init_data;\n    shopify_Matrix4Js20.__stackDetails = _e;\n    return shopify_Matrix4Js20;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_8340212603925_init_data = {\n    code: \"function shopify_Matrix4Js21(rowMajorMatrix){const colMajorMatrix=new Array(16);const size=4;for(let row=0;row<size;row++){for(let col=0;col<size;col++){colMajorMatrix[col*size+row]=rowMajorMatrix[row*size+col];}}return colMajorMatrix;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js21\\\",\\\"rowMajorMatrix\\\",\\\"colMajorMatrix\\\",\\\"Array\\\",\\\"size\\\",\\\"row\\\",\\\"col\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAmPoC,SAAAA,mBAAkBA,CAAAC,cAAA,EAGpD,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAAC,KAAK,CAAC,EAAE,CAAC,CACpC,KAAM,CAAAC,IAAI,CAAG,CAAC,CACd,IAAK,GAAI,CAAAC,GAAG,CAAG,CAAC,CAAEA,GAAG,CAAGD,IAAI,CAAEC,GAAG,EAAE,CAAE,CACnC,IAAK,GAAI,CAAAC,GAAG,CAAG,CAAC,CAAEA,GAAG,CAAGF,IAAI,CAAEE,GAAG,EAAE,CAAE,CACnCJ,cAAc,CAACI,GAAG,CAAGF,IAAI,CAAGC,GAAG,CAAC,CAAGJ,cAAc,CAACI,GAAG,CAAGD,IAAI,CAAGE,GAAG,CAAC,CACrE,CACF,CACA,MAAO,CAAAJ,cAAc,CACvB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const convertToColumnMajor = exports.convertToColumnMajor = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js21 = function (rowMajorMatrix) {\n      const colMajorMatrix = new Array(16);\n      const size = 4;\n      for (let row = 0; row < size; row++) {\n        for (let col = 0; col < size; col++) {\n          colMajorMatrix[col * size + row] = rowMajorMatrix[row * size + col];\n        }\n      }\n      return colMajorMatrix;\n    };\n    shopify_Matrix4Js21.__closure = {};\n    shopify_Matrix4Js21.__workletHash = 8340212603925;\n    shopify_Matrix4Js21.__initData = _worklet_8340212603925_init_data;\n    shopify_Matrix4Js21.__stackDetails = _e;\n    return shopify_Matrix4Js21;\n  }();\n\n  /**\n   * @worklet\n   */\n  const _worklet_6037180980026_init_data = {\n    code: \"function shopify_Matrix4Js22(m4){const a=m4[0];const b=m4[1];const c=m4[4];const d=m4[5];const tx=m4[12];const ty=m4[13];return[a,b,c,d,tx,ty];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js22\\\",\\\"m4\\\",\\\"a\\\",\\\"b\\\",\\\"c\\\",\\\"d\\\",\\\"tx\\\",\\\"ty\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAmQqC,SAAAA,mBAAMA,CAAAC,EAAA,EAIzC,KAAM,CAAAC,CAAC,CAAGD,EAAE,CAAC,CAAC,CAAC,CACf,KAAM,CAAAE,CAAC,CAAGF,EAAE,CAAC,CAAC,CAAC,CACf,KAAM,CAAAG,CAAC,CAAGH,EAAE,CAAC,CAAC,CAAC,CACf,KAAM,CAAAI,CAAC,CAAGJ,EAAE,CAAC,CAAC,CAAC,CACf,KAAM,CAAAK,EAAE,CAAGL,EAAE,CAAC,EAAE,CAAC,CACjB,KAAM,CAAAM,EAAE,CAAGN,EAAE,CAAC,EAAE,CAAC,CAGjB,MAAO,CAACC,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAEC,EAAE,CAAEC,EAAE,CAAC,CAC7B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const convertToAffineMatrix = exports.convertToAffineMatrix = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js22 = function (m4) {\n      // Extracting the relevant components from the 4x4 matrix\n      const a = m4[0]; // Scale X\n      const b = m4[1]; // Skew Y\n      const c = m4[4]; // Skew X\n      const d = m4[5]; // Scale Y\n      const tx = m4[12]; // Translate X\n      const ty = m4[13]; // Translate Y\n\n      // Returning the 6-element affine transformation matrix\n      return [a, b, c, d, tx, ty];\n    };\n    shopify_Matrix4Js22.__closure = {};\n    shopify_Matrix4Js22.__workletHash = 6037180980026;\n    shopify_Matrix4Js22.__initData = _worklet_6037180980026_init_data;\n    shopify_Matrix4Js22.__stackDetails = _e;\n    return shopify_Matrix4Js22;\n  }();\n\n  /**\n   * Calculates the determinant of a 3x3 matrix\n   * @worklet\n   */\n  const _worklet_6884116785644_init_data = {\n    code: \"function shopify_Matrix4Js23(a00,a01,a02,a10,a11,a12,a20,a21,a22){return a00*(a11*a22-a12*a21)+a01*(a12*a20-a10*a22)+a02*(a10*a21-a11*a20);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js23\\\",\\\"a00\\\",\\\"a01\\\",\\\"a02\\\",\\\"a10\\\",\\\"a11\\\",\\\"a12\\\",\\\"a20\\\",\\\"a21\\\",\\\"a22\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAsRe,QAAC,CAAAA,mBAAeA,CAAGC,GAAE,CAAGC,GAAE,CAAGC,GAAE,CAAGC,GAAE,CAAGC,GAAE,CAAGC,GAAK,CAAAC,GAAA,CAAAC,GAAA,CAAAC,GAAA,EAG9D,MAAO,CAAAR,GAAG,EAAII,GAAG,CAAGI,GAAG,CAAGH,GAAG,CAAGE,GAAG,CAAC,CAAGN,GAAG,EAAII,GAAG,CAAGC,GAAG,CAAGH,GAAG,CAAGK,GAAG,CAAC,CAAGN,GAAG,EAAIC,GAAG,CAAGI,GAAG,CAAGH,GAAG,CAAGE,GAAG,CAAC,CACtG\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const det3x3 = function () {\n    const _e = [new global.Error(), 1, -27];\n    const shopify_Matrix4Js23 = function (a00, a01, a02, a10, a11, a12, a20, a21, a22) {\n      return a00 * (a11 * a22 - a12 * a21) + a01 * (a12 * a20 - a10 * a22) + a02 * (a10 * a21 - a11 * a20);\n    };\n    shopify_Matrix4Js23.__closure = {};\n    shopify_Matrix4Js23.__workletHash = 6884116785644;\n    shopify_Matrix4Js23.__initData = _worklet_6884116785644_init_data;\n    shopify_Matrix4Js23.__stackDetails = _e;\n    return shopify_Matrix4Js23;\n  }();\n\n  /**\n   * Inverts a 4x4 matrix\n   * @worklet\n   * @returns The inverted matrix, or the identity matrix if the input is not invertible\n   */\n  const _worklet_11697263420356_init_data = {\n    code: \"function shopify_Matrix4Js24(m){const{det3x3,Matrix4}=this.__closure;const a00=m[0],a01=m[1],a02=m[2],a03=m[3];const a10=m[4],a11=m[5],a12=m[6],a13=m[7];const a20=m[8],a21=m[9],a22=m[10],a23=m[11];const a30=m[12],a31=m[13],a32=m[14],a33=m[15];const b00=det3x3(a11,a12,a13,a21,a22,a23,a31,a32,a33);const b01=-det3x3(a10,a12,a13,a20,a22,a23,a30,a32,a33);const b02=det3x3(a10,a11,a13,a20,a21,a23,a30,a31,a33);const b03=-det3x3(a10,a11,a12,a20,a21,a22,a30,a31,a32);const b10=-det3x3(a01,a02,a03,a21,a22,a23,a31,a32,a33);const b11=det3x3(a00,a02,a03,a20,a22,a23,a30,a32,a33);const b12=-det3x3(a00,a01,a03,a20,a21,a23,a30,a31,a33);const b13=det3x3(a00,a01,a02,a20,a21,a22,a30,a31,a32);const b20=det3x3(a01,a02,a03,a11,a12,a13,a31,a32,a33);const b21=-det3x3(a00,a02,a03,a10,a12,a13,a30,a32,a33);const b22=det3x3(a00,a01,a03,a10,a11,a13,a30,a31,a33);const b23=-det3x3(a00,a01,a02,a10,a11,a12,a30,a31,a32);const b30=-det3x3(a01,a02,a03,a11,a12,a13,a21,a22,a23);const b31=det3x3(a00,a02,a03,a10,a12,a13,a20,a22,a23);const b32=-det3x3(a00,a01,a03,a10,a11,a13,a20,a21,a23);const b33=det3x3(a00,a01,a02,a10,a11,a12,a20,a21,a22);const det=a00*b00+a01*b01+a02*b02+a03*b03;if(Math.abs(det)<1e-8){return Matrix4();}const invDet=1.0/det;return[b00*invDet,b10*invDet,b20*invDet,b30*invDet,b01*invDet,b11*invDet,b21*invDet,b31*invDet,b02*invDet,b12*invDet,b22*invDet,b32*invDet,b03*invDet,b13*invDet,b23*invDet,b33*invDet];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"shopify_Matrix4Js24\\\",\\\"m\\\",\\\"det3x3\\\",\\\"Matrix4\\\",\\\"__closure\\\",\\\"a00\\\",\\\"a01\\\",\\\"a02\\\",\\\"a03\\\",\\\"a10\\\",\\\"a11\\\",\\\"a12\\\",\\\"a13\\\",\\\"a20\\\",\\\"a21\\\",\\\"a22\\\",\\\"a23\\\",\\\"a30\\\",\\\"a31\\\",\\\"a32\\\",\\\"a33\\\",\\\"b00\\\",\\\"b01\\\",\\\"b02\\\",\\\"b03\\\",\\\"b10\\\",\\\"b11\\\",\\\"b12\\\",\\\"b13\\\",\\\"b20\\\",\\\"b21\\\",\\\"b22\\\",\\\"b23\\\",\\\"b30\\\",\\\"b31\\\",\\\"b32\\\",\\\"b33\\\",\\\"det\\\",\\\"Math\\\",\\\"abs\\\",\\\"invDet\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/types/Matrix4.js\\\"],\\\"mappings\\\":\\\"AAiSuB,QAAC,CAAAA,mBAAIA,CAAAC,CAAA,QAAAC,MAAA,CAAAC,OAAA,OAAAC,SAAA,CAG1B,KAAM,CAAAC,GAAG,CAAGJ,CAAC,CAAC,CAAC,CAAC,CACdK,GAAG,CAAGL,CAAC,CAAC,CAAC,CAAC,CACVM,GAAG,CAAGN,CAAC,CAAC,CAAC,CAAC,CACVO,GAAG,CAAGP,CAAC,CAAC,CAAC,CAAC,CACZ,KAAM,CAAAQ,GAAG,CAAGR,CAAC,CAAC,CAAC,CAAC,CACdS,GAAG,CAAGT,CAAC,CAAC,CAAC,CAAC,CACVU,GAAG,CAAGV,CAAC,CAAC,CAAC,CAAC,CACVW,GAAG,CAAGX,CAAC,CAAC,CAAC,CAAC,CACZ,KAAM,CAAAY,GAAG,CAAGZ,CAAC,CAAC,CAAC,CAAC,CACda,GAAG,CAAGb,CAAC,CAAC,CAAC,CAAC,CACVc,GAAG,CAAGd,CAAC,CAAC,EAAE,CAAC,CACXe,GAAG,CAAGf,CAAC,CAAC,EAAE,CAAC,CACb,KAAM,CAAAgB,GAAG,CAAGhB,CAAC,CAAC,EAAE,CAAC,CACfiB,GAAG,CAAGjB,CAAC,CAAC,EAAE,CAAC,CACXkB,GAAG,CAAGlB,CAAC,CAAC,EAAE,CAAC,CACXmB,GAAG,CAAGnB,CAAC,CAAC,EAAE,CAAC,CAGb,KAAM,CAAAoB,GAAG,CAAGnB,MAAM,CAACQ,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAC/D,KAAM,CAAAE,GAAG,CAAG,CAACpB,MAAM,CAACO,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAC,CAChE,KAAM,CAAAG,GAAG,CAAGrB,MAAM,CAACO,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAC,CAC/D,KAAM,CAAAI,GAAG,CAAG,CAACtB,MAAM,CAACO,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAChE,KAAM,CAAAM,GAAG,CAAG,CAACvB,MAAM,CAACI,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEM,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAChE,KAAM,CAAAM,GAAG,CAAGxB,MAAM,CAACG,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEK,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAC,CAC/D,KAAM,CAAAO,GAAG,CAAG,CAACzB,MAAM,CAACG,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEK,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAC,CAChE,KAAM,CAAAQ,GAAG,CAAG1B,MAAM,CAACG,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEM,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAC/D,KAAM,CAAAU,GAAG,CAAG3B,MAAM,CAACI,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEM,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAC/D,KAAM,CAAAU,GAAG,CAAG,CAAC5B,MAAM,CAACG,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEK,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAC,CAChE,KAAM,CAAAW,GAAG,CAAG7B,MAAM,CAACG,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEK,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAC,CAC/D,KAAM,CAAAY,GAAG,CAAG,CAAC9B,MAAM,CAACG,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEM,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAChE,KAAM,CAAAc,GAAG,CAAG,CAAC/B,MAAM,CAACI,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAChE,KAAM,CAAAkB,GAAG,CAAGhC,MAAM,CAACG,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAC,CAC/D,KAAM,CAAAmB,GAAG,CAAG,CAACjC,MAAM,CAACG,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAC,CAChE,KAAM,CAAAoB,GAAG,CAAGlC,MAAM,CAACG,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAEE,GAAG,CAAEC,GAAG,CAAEC,GAAG,CAAC,CAG/D,KAAM,CAAAsB,GAAG,CAAGhC,GAAG,CAAGgB,GAAG,CAAGf,GAAG,CAAGgB,GAAG,CAAGf,GAAG,CAAGgB,GAAG,CAAGf,GAAG,CAAGgB,GAAG,CAGzD,GAAIc,IAAI,CAACC,GAAG,CAACF,GAAG,CAAC,CAAG,IAAI,CAAE,CAExB,MAAO,CAAAlC,OAAO,CAAC,CAAC,CAClB,CACA,KAAM,CAAAqC,MAAM,CAAG,GAAG,CAAGH,GAAG,CAGxB,MAAO,CAAChB,GAAG,CAAGmB,MAAM,CAAEf,GAAG,CAAGe,MAAM,CAAEX,GAAG,CAAGW,MAAM,CAAEP,GAAG,CAAGO,MAAM,CAAElB,GAAG,CAAGkB,MAAM,CAAEd,GAAG,CAAGc,MAAM,CAAEV,GAAG,CAAGU,MAAM,CAAEN,GAAG,CAAGM,MAAM,CAAEjB,GAAG,CAAGiB,MAAM,CAAEb,GAAG,CAAGa,MAAM,CAAET,GAAG,CAAGS,MAAM,CAAEL,GAAG,CAAGK,MAAM,CAAEhB,GAAG,CAAGgB,MAAM,CAAEZ,GAAG,CAAGY,MAAM,CAAER,GAAG,CAAGQ,MAAM,CAAEJ,GAAG,CAAGI,MAAM,CAAC,CACzO\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const invert4 = exports.invert4 = function () {\n    const _e = [new global.Error(), -3, -27];\n    const shopify_Matrix4Js24 = function (m) {\n      const a00 = m[0],\n        a01 = m[1],\n        a02 = m[2],\n        a03 = m[3];\n      const a10 = m[4],\n        a11 = m[5],\n        a12 = m[6],\n        a13 = m[7];\n      const a20 = m[8],\n        a21 = m[9],\n        a22 = m[10],\n        a23 = m[11];\n      const a30 = m[12],\n        a31 = m[13],\n        a32 = m[14],\n        a33 = m[15];\n\n      // Calculate cofactors\n      const b00 = det3x3(a11, a12, a13, a21, a22, a23, a31, a32, a33);\n      const b01 = -det3x3(a10, a12, a13, a20, a22, a23, a30, a32, a33);\n      const b02 = det3x3(a10, a11, a13, a20, a21, a23, a30, a31, a33);\n      const b03 = -det3x3(a10, a11, a12, a20, a21, a22, a30, a31, a32);\n      const b10 = -det3x3(a01, a02, a03, a21, a22, a23, a31, a32, a33);\n      const b11 = det3x3(a00, a02, a03, a20, a22, a23, a30, a32, a33);\n      const b12 = -det3x3(a00, a01, a03, a20, a21, a23, a30, a31, a33);\n      const b13 = det3x3(a00, a01, a02, a20, a21, a22, a30, a31, a32);\n      const b20 = det3x3(a01, a02, a03, a11, a12, a13, a31, a32, a33);\n      const b21 = -det3x3(a00, a02, a03, a10, a12, a13, a30, a32, a33);\n      const b22 = det3x3(a00, a01, a03, a10, a11, a13, a30, a31, a33);\n      const b23 = -det3x3(a00, a01, a02, a10, a11, a12, a30, a31, a32);\n      const b30 = -det3x3(a01, a02, a03, a11, a12, a13, a21, a22, a23);\n      const b31 = det3x3(a00, a02, a03, a10, a12, a13, a20, a22, a23);\n      const b32 = -det3x3(a00, a01, a03, a10, a11, a13, a20, a21, a23);\n      const b33 = det3x3(a00, a01, a02, a10, a11, a12, a20, a21, a22);\n\n      // Calculate determinant\n      const det = a00 * b00 + a01 * b01 + a02 * b02 + a03 * b03;\n\n      // Check if matrix is invertible\n      if (Math.abs(det) < 1e-8) {\n        // Return identity matrix if not invertible\n        return Matrix4();\n      }\n      const invDet = 1.0 / det;\n\n      // Calculate inverse matrix\n      return [b00 * invDet, b10 * invDet, b20 * invDet, b30 * invDet, b01 * invDet, b11 * invDet, b21 * invDet, b31 * invDet, b02 * invDet, b12 * invDet, b22 * invDet, b32 * invDet, b03 * invDet, b13 * invDet, b23 * invDet, b33 * invDet];\n    };\n    shopify_Matrix4Js24.__closure = {\n      det3x3,\n      Matrix4\n    };\n    shopify_Matrix4Js24.__workletHash = 11697263420356;\n    shopify_Matrix4Js24.__initData = _worklet_11697263420356_init_data;\n    shopify_Matrix4Js24.__stackDetails = _e;\n    return shopify_Matrix4Js24;\n  }();\n});", "lineCount": 666, "map": [[12, 2, 1, 0], [12, 8, 1, 6, "exhaustiveCheck"], [12, 23, 1, 21], [12, 26, 1, 24], [13, 4, 1, 24], [13, 10, 1, 24, "_e"], [13, 12, 1, 24], [13, 20, 1, 24, "global"], [13, 26, 1, 24], [13, 27, 1, 24, "Error"], [13, 32, 1, 24], [14, 4, 1, 24], [14, 10, 1, 24, "shopify_Matrix4Js1"], [14, 28, 1, 24], [14, 40, 1, 24, "shopify_Matrix4Js1"], [14, 41, 1, 24, "a"], [14, 42, 1, 25], [14, 44, 1, 29], [15, 6, 4, 2], [15, 12, 4, 8], [15, 16, 4, 12, "Error"], [15, 21, 4, 17], [15, 22, 4, 18], [15, 51, 4, 47, "a"], [15, 52, 4, 48], [15, 54, 4, 50], [15, 55, 4, 51], [16, 4, 5, 0], [16, 5, 5, 1], [17, 4, 5, 1, "shopify_Matrix4Js1"], [17, 22, 5, 1], [17, 23, 5, 1, "__closure"], [17, 32, 5, 1], [18, 4, 5, 1, "shopify_Matrix4Js1"], [18, 22, 5, 1], [18, 23, 5, 1, "__workletHash"], [18, 36, 5, 1], [19, 4, 5, 1, "shopify_Matrix4Js1"], [19, 22, 5, 1], [19, 23, 5, 1, "__initData"], [19, 33, 5, 1], [19, 36, 5, 1, "_worklet_8129029734104_init_data"], [19, 68, 5, 1], [20, 4, 5, 1, "shopify_Matrix4Js1"], [20, 22, 5, 1], [20, 23, 5, 1, "__stackDetails"], [20, 37, 5, 1], [20, 40, 5, 1, "_e"], [20, 42, 5, 1], [21, 4, 5, 1], [21, 11, 5, 1, "shopify_Matrix4Js1"], [21, 29, 5, 1], [22, 2, 5, 1], [22, 3, 1, 24], [22, 5, 5, 1], [24, 2, 7, 0], [25, 0, 8, 0], [26, 0, 9, 0], [27, 2, 7, 0], [27, 8, 7, 0, "_worklet_14818525896400_init_data"], [27, 41, 7, 0], [28, 4, 7, 0, "code"], [28, 8, 7, 0], [29, 4, 7, 0, "location"], [29, 12, 7, 0], [30, 4, 7, 0, "sourceMap"], [30, 13, 7, 0], [31, 4, 7, 0, "version"], [31, 11, 7, 0], [32, 2, 7, 0], [33, 2, 10, 7], [33, 8, 10, 13, "Matrix4"], [33, 15, 10, 20], [33, 18, 10, 20, "exports"], [33, 25, 10, 20], [33, 26, 10, 20, "Matrix4"], [33, 33, 10, 20], [33, 36, 10, 23], [34, 4, 10, 23], [34, 10, 10, 23, "_e"], [34, 12, 10, 23], [34, 20, 10, 23, "global"], [34, 26, 10, 23], [34, 27, 10, 23, "Error"], [34, 32, 10, 23], [35, 4, 10, 23], [35, 10, 10, 23, "shopify_Matrix4Js2"], [35, 28, 10, 23], [35, 40, 10, 23, "shopify_Matrix4Js2"], [35, 41, 10, 23], [35, 43, 10, 29], [36, 6, 13, 2], [36, 13, 13, 9], [36, 14, 13, 10], [36, 15, 13, 11], [36, 17, 13, 13], [36, 18, 13, 14], [36, 20, 13, 16], [36, 21, 13, 17], [36, 23, 13, 19], [36, 24, 13, 20], [36, 26, 13, 22], [36, 27, 13, 23], [36, 29, 13, 25], [36, 30, 13, 26], [36, 32, 13, 28], [36, 33, 13, 29], [36, 35, 13, 31], [36, 36, 13, 32], [36, 38, 13, 34], [36, 39, 13, 35], [36, 41, 13, 37], [36, 42, 13, 38], [36, 44, 13, 40], [36, 45, 13, 41], [36, 47, 13, 43], [36, 48, 13, 44], [36, 50, 13, 46], [36, 51, 13, 47], [36, 53, 13, 49], [36, 54, 13, 50], [36, 56, 13, 52], [36, 57, 13, 53], [36, 59, 13, 55], [36, 60, 13, 56], [36, 61, 13, 57], [37, 4, 14, 0], [37, 5, 14, 1], [38, 4, 14, 1, "shopify_Matrix4Js2"], [38, 22, 14, 1], [38, 23, 14, 1, "__closure"], [38, 32, 14, 1], [39, 4, 14, 1, "shopify_Matrix4Js2"], [39, 22, 14, 1], [39, 23, 14, 1, "__workletHash"], [39, 36, 14, 1], [40, 4, 14, 1, "shopify_Matrix4Js2"], [40, 22, 14, 1], [40, 23, 14, 1, "__initData"], [40, 33, 14, 1], [40, 36, 14, 1, "_worklet_14818525896400_init_data"], [40, 69, 14, 1], [41, 4, 14, 1, "shopify_Matrix4Js2"], [41, 22, 14, 1], [41, 23, 14, 1, "__stackDetails"], [41, 37, 14, 1], [41, 40, 14, 1, "_e"], [41, 42, 14, 1], [42, 4, 14, 1], [42, 11, 14, 1, "shopify_Matrix4Js2"], [42, 29, 14, 1], [43, 2, 14, 1], [43, 3, 10, 23], [43, 5, 14, 1], [45, 2, 16, 0], [46, 0, 17, 0], [47, 0, 18, 0], [48, 2, 16, 0], [48, 8, 16, 0, "_worklet_1283578139948_init_data"], [48, 40, 16, 0], [49, 4, 16, 0, "code"], [49, 8, 16, 0], [50, 4, 16, 0, "location"], [50, 12, 16, 0], [51, 4, 16, 0, "sourceMap"], [51, 13, 16, 0], [52, 4, 16, 0, "version"], [52, 11, 16, 0], [53, 2, 16, 0], [54, 2, 19, 7], [54, 8, 19, 13, "translate"], [54, 17, 19, 22], [54, 20, 19, 22, "exports"], [54, 27, 19, 22], [54, 28, 19, 22, "translate"], [54, 37, 19, 22], [54, 40, 19, 25], [55, 4, 19, 25], [55, 10, 19, 25, "_e"], [55, 12, 19, 25], [55, 20, 19, 25, "global"], [55, 26, 19, 25], [55, 27, 19, 25, "Error"], [55, 32, 19, 25], [56, 4, 19, 25], [56, 10, 19, 25, "shopify_Matrix4Js3"], [56, 28, 19, 25], [56, 40, 19, 25, "shopify_Matrix4Js3"], [56, 41, 19, 26, "x"], [56, 42, 19, 27], [56, 44, 19, 29, "y"], [56, 45, 19, 30], [56, 47, 19, 32, "z"], [56, 48, 19, 33], [56, 51, 19, 36], [56, 52, 19, 37], [56, 54, 19, 42], [57, 6, 22, 2], [57, 13, 22, 9], [57, 14, 22, 10], [57, 15, 22, 11], [57, 17, 22, 13], [57, 18, 22, 14], [57, 20, 22, 16], [57, 21, 22, 17], [57, 23, 22, 19, "x"], [57, 24, 22, 20], [57, 26, 22, 22], [57, 27, 22, 23], [57, 29, 22, 25], [57, 30, 22, 26], [57, 32, 22, 28], [57, 33, 22, 29], [57, 35, 22, 31, "y"], [57, 36, 22, 32], [57, 38, 22, 34], [57, 39, 22, 35], [57, 41, 22, 37], [57, 42, 22, 38], [57, 44, 22, 40], [57, 45, 22, 41], [57, 47, 22, 43, "z"], [57, 48, 22, 44], [57, 50, 22, 46], [57, 51, 22, 47], [57, 53, 22, 49], [57, 54, 22, 50], [57, 56, 22, 52], [57, 57, 22, 53], [57, 59, 22, 55], [57, 60, 22, 56], [57, 61, 22, 57], [58, 4, 23, 0], [58, 5, 23, 1], [59, 4, 23, 1, "shopify_Matrix4Js3"], [59, 22, 23, 1], [59, 23, 23, 1, "__closure"], [59, 32, 23, 1], [60, 4, 23, 1, "shopify_Matrix4Js3"], [60, 22, 23, 1], [60, 23, 23, 1, "__workletHash"], [60, 36, 23, 1], [61, 4, 23, 1, "shopify_Matrix4Js3"], [61, 22, 23, 1], [61, 23, 23, 1, "__initData"], [61, 33, 23, 1], [61, 36, 23, 1, "_worklet_1283578139948_init_data"], [61, 68, 23, 1], [62, 4, 23, 1, "shopify_Matrix4Js3"], [62, 22, 23, 1], [62, 23, 23, 1, "__stackDetails"], [62, 37, 23, 1], [62, 40, 23, 1, "_e"], [62, 42, 23, 1], [63, 4, 23, 1], [63, 11, 23, 1, "shopify_Matrix4Js3"], [63, 29, 23, 1], [64, 2, 23, 1], [64, 3, 19, 25], [64, 5, 23, 1], [66, 2, 25, 0], [67, 0, 26, 0], [68, 0, 27, 0], [69, 2, 25, 0], [69, 8, 25, 0, "_worklet_7347197968821_init_data"], [69, 40, 25, 0], [70, 4, 25, 0, "code"], [70, 8, 25, 0], [71, 4, 25, 0, "location"], [71, 12, 25, 0], [72, 4, 25, 0, "sourceMap"], [72, 13, 25, 0], [73, 4, 25, 0, "version"], [73, 11, 25, 0], [74, 2, 25, 0], [75, 2, 28, 7], [75, 8, 28, 13, "perspective"], [75, 19, 28, 24], [75, 22, 28, 24, "exports"], [75, 29, 28, 24], [75, 30, 28, 24, "perspective"], [75, 41, 28, 24], [75, 44, 28, 27], [76, 4, 28, 27], [76, 10, 28, 27, "_e"], [76, 12, 28, 27], [76, 20, 28, 27, "global"], [76, 26, 28, 27], [76, 27, 28, 27, "Error"], [76, 32, 28, 27], [77, 4, 28, 27], [77, 10, 28, 27, "shopify_Matrix4Js4"], [77, 28, 28, 27], [77, 40, 28, 27, "shopify_Matrix4Js4"], [77, 41, 28, 27, "p"], [77, 42, 28, 28], [77, 44, 28, 32], [78, 6, 31, 2], [78, 13, 31, 9], [78, 14, 31, 10], [78, 15, 31, 11], [78, 17, 31, 13], [78, 18, 31, 14], [78, 20, 31, 16], [78, 21, 31, 17], [78, 23, 31, 19], [78, 24, 31, 20], [78, 26, 31, 22], [78, 27, 31, 23], [78, 29, 31, 25], [78, 30, 31, 26], [78, 32, 31, 28], [78, 33, 31, 29], [78, 35, 31, 31], [78, 36, 31, 32], [78, 38, 31, 34], [78, 39, 31, 35], [78, 41, 31, 37], [78, 42, 31, 38], [78, 44, 31, 40], [78, 45, 31, 41], [78, 47, 31, 43], [78, 48, 31, 44], [78, 50, 31, 46], [78, 51, 31, 47], [78, 53, 31, 49], [78, 54, 31, 50], [78, 56, 31, 52], [78, 57, 31, 53], [78, 58, 31, 54], [78, 61, 31, 57, "p"], [78, 62, 31, 58], [78, 64, 31, 60], [78, 65, 31, 61], [78, 66, 31, 62], [79, 4, 32, 0], [79, 5, 32, 1], [80, 4, 32, 1, "shopify_Matrix4Js4"], [80, 22, 32, 1], [80, 23, 32, 1, "__closure"], [80, 32, 32, 1], [81, 4, 32, 1, "shopify_Matrix4Js4"], [81, 22, 32, 1], [81, 23, 32, 1, "__workletHash"], [81, 36, 32, 1], [82, 4, 32, 1, "shopify_Matrix4Js4"], [82, 22, 32, 1], [82, 23, 32, 1, "__initData"], [82, 33, 32, 1], [82, 36, 32, 1, "_worklet_7347197968821_init_data"], [82, 68, 32, 1], [83, 4, 32, 1, "shopify_Matrix4Js4"], [83, 22, 32, 1], [83, 23, 32, 1, "__stackDetails"], [83, 37, 32, 1], [83, 40, 32, 1, "_e"], [83, 42, 32, 1], [84, 4, 32, 1], [84, 11, 32, 1, "shopify_Matrix4Js4"], [84, 29, 32, 1], [85, 2, 32, 1], [85, 3, 28, 27], [85, 5, 32, 1], [86, 2, 32, 2], [86, 8, 32, 2, "_worklet_10397893766365_init_data"], [86, 41, 32, 2], [87, 4, 32, 2, "code"], [87, 8, 32, 2], [88, 4, 32, 2, "location"], [88, 12, 32, 2], [89, 4, 32, 2, "sourceMap"], [89, 13, 32, 2], [90, 4, 32, 2, "version"], [90, 11, 32, 2], [91, 2, 32, 2], [92, 2, 33, 0], [92, 8, 33, 6, "normalizeVec"], [92, 20, 33, 18], [92, 23, 33, 21], [93, 4, 33, 21], [93, 10, 33, 21, "_e"], [93, 12, 33, 21], [93, 20, 33, 21, "global"], [93, 26, 33, 21], [93, 27, 33, 21, "Error"], [93, 32, 33, 21], [94, 4, 33, 21], [94, 10, 33, 21, "shopify_Matrix4Js5"], [94, 28, 33, 21], [94, 40, 33, 21, "shopify_Matrix4Js5"], [94, 41, 33, 21, "vec"], [94, 44, 33, 24], [94, 46, 33, 28], [95, 6, 36, 2], [95, 12, 36, 8], [95, 13, 36, 9, "x"], [95, 14, 36, 10], [95, 16, 36, 12, "y"], [95, 17, 36, 13], [95, 19, 36, 15, "z"], [95, 20, 36, 16], [95, 21, 36, 17], [95, 24, 36, 20, "vec"], [95, 27, 36, 23], [96, 6, 37, 2], [96, 12, 37, 8, "length"], [96, 18, 37, 14], [96, 21, 37, 17, "Math"], [96, 25, 37, 21], [96, 26, 37, 22, "sqrt"], [96, 30, 37, 26], [96, 31, 37, 27, "x"], [96, 32, 37, 28], [96, 35, 37, 31, "x"], [96, 36, 37, 32], [96, 39, 37, 35, "y"], [96, 40, 37, 36], [96, 43, 37, 39, "y"], [96, 44, 37, 40], [96, 47, 37, 43, "z"], [96, 48, 37, 44], [96, 51, 37, 47, "z"], [96, 52, 37, 48], [96, 53, 37, 49], [97, 6, 38, 2], [98, 6, 39, 2], [98, 10, 39, 6, "length"], [98, 16, 39, 12], [98, 21, 39, 17], [98, 22, 39, 18], [98, 24, 39, 20], [99, 8, 40, 4], [99, 15, 40, 11], [99, 16, 40, 12], [99, 17, 40, 13], [99, 19, 40, 15], [99, 20, 40, 16], [99, 22, 40, 18], [99, 23, 40, 19], [99, 24, 40, 20], [100, 6, 41, 2], [101, 6, 42, 2], [101, 13, 42, 9], [101, 14, 42, 10, "x"], [101, 15, 42, 11], [101, 18, 42, 14, "length"], [101, 24, 42, 20], [101, 26, 42, 22, "y"], [101, 27, 42, 23], [101, 30, 42, 26, "length"], [101, 36, 42, 32], [101, 38, 42, 34, "z"], [101, 39, 42, 35], [101, 42, 42, 38, "length"], [101, 48, 42, 44], [101, 49, 42, 45], [102, 4, 43, 0], [102, 5, 43, 1], [103, 4, 43, 1, "shopify_Matrix4Js5"], [103, 22, 43, 1], [103, 23, 43, 1, "__closure"], [103, 32, 43, 1], [104, 4, 43, 1, "shopify_Matrix4Js5"], [104, 22, 43, 1], [104, 23, 43, 1, "__workletHash"], [104, 36, 43, 1], [105, 4, 43, 1, "shopify_Matrix4Js5"], [105, 22, 43, 1], [105, 23, 43, 1, "__initData"], [105, 33, 43, 1], [105, 36, 43, 1, "_worklet_10397893766365_init_data"], [105, 69, 43, 1], [106, 4, 43, 1, "shopify_Matrix4Js5"], [106, 22, 43, 1], [106, 23, 43, 1, "__stackDetails"], [106, 37, 43, 1], [106, 40, 43, 1, "_e"], [106, 42, 43, 1], [107, 4, 43, 1], [107, 11, 43, 1, "shopify_Matrix4Js5"], [107, 29, 43, 1], [108, 2, 43, 1], [108, 3, 33, 21], [108, 5, 43, 1], [109, 2, 43, 2], [109, 8, 43, 2, "_worklet_14051658427690_init_data"], [109, 41, 43, 2], [110, 4, 43, 2, "code"], [110, 8, 43, 2], [111, 4, 43, 2, "location"], [111, 12, 43, 2], [112, 4, 43, 2, "sourceMap"], [112, 13, 43, 2], [113, 4, 43, 2, "version"], [113, 11, 43, 2], [114, 2, 43, 2], [115, 2, 44, 0], [115, 8, 44, 6, "rotatedUnitSinCos"], [115, 25, 44, 23], [115, 28, 44, 26], [116, 4, 44, 26], [116, 10, 44, 26, "_e"], [116, 12, 44, 26], [116, 20, 44, 26, "global"], [116, 26, 44, 26], [116, 27, 44, 26, "Error"], [116, 32, 44, 26], [117, 4, 44, 26], [117, 10, 44, 26, "shopify_Matrix4Js6"], [117, 28, 44, 26], [117, 40, 44, 26, "shopify_Matrix4Js6"], [117, 41, 44, 27, "axisVec"], [117, 48, 44, 34], [117, 50, 44, 36, "sinAngle"], [117, 58, 44, 44], [117, 60, 44, 46, "cosAngle"], [117, 68, 44, 54], [117, 70, 44, 59], [118, 6, 47, 2], [118, 12, 47, 8, "x"], [118, 13, 47, 9], [118, 16, 47, 12, "axisVec"], [118, 23, 47, 19], [118, 24, 47, 20], [118, 25, 47, 21], [118, 26, 47, 22], [119, 6, 48, 2], [119, 12, 48, 8, "y"], [119, 13, 48, 9], [119, 16, 48, 12, "axisVec"], [119, 23, 48, 19], [119, 24, 48, 20], [119, 25, 48, 21], [119, 26, 48, 22], [120, 6, 49, 2], [120, 12, 49, 8, "z"], [120, 13, 49, 9], [120, 16, 49, 12, "axisVec"], [120, 23, 49, 19], [120, 24, 49, 20], [120, 25, 49, 21], [120, 26, 49, 22], [121, 6, 50, 2], [121, 12, 50, 8, "c"], [121, 13, 50, 9], [121, 16, 50, 12, "cosAngle"], [121, 24, 50, 20], [122, 6, 51, 2], [122, 12, 51, 8, "s"], [122, 13, 51, 9], [122, 16, 51, 12, "sinAngle"], [122, 24, 51, 20], [123, 6, 52, 2], [123, 12, 52, 8, "t"], [123, 13, 52, 9], [123, 16, 52, 12], [123, 17, 52, 13], [123, 20, 52, 16, "c"], [123, 21, 52, 17], [124, 6, 53, 2], [124, 13, 53, 9], [124, 14, 53, 10, "t"], [124, 15, 53, 11], [124, 18, 53, 14, "x"], [124, 19, 53, 15], [124, 22, 53, 18, "x"], [124, 23, 53, 19], [124, 26, 53, 22, "c"], [124, 27, 53, 23], [124, 29, 53, 25, "t"], [124, 30, 53, 26], [124, 33, 53, 29, "x"], [124, 34, 53, 30], [124, 37, 53, 33, "y"], [124, 38, 53, 34], [124, 41, 53, 37, "s"], [124, 42, 53, 38], [124, 45, 53, 41, "z"], [124, 46, 53, 42], [124, 48, 53, 44, "t"], [124, 49, 53, 45], [124, 52, 53, 48, "x"], [124, 53, 53, 49], [124, 56, 53, 52, "z"], [124, 57, 53, 53], [124, 60, 53, 56, "s"], [124, 61, 53, 57], [124, 64, 53, 60, "y"], [124, 65, 53, 61], [124, 67, 53, 63], [124, 68, 53, 64], [124, 70, 53, 66, "t"], [124, 71, 53, 67], [124, 74, 53, 70, "x"], [124, 75, 53, 71], [124, 78, 53, 74, "y"], [124, 79, 53, 75], [124, 82, 53, 78, "s"], [124, 83, 53, 79], [124, 86, 53, 82, "z"], [124, 87, 53, 83], [124, 89, 53, 85, "t"], [124, 90, 53, 86], [124, 93, 53, 89, "y"], [124, 94, 53, 90], [124, 97, 53, 93, "y"], [124, 98, 53, 94], [124, 101, 53, 97, "c"], [124, 102, 53, 98], [124, 104, 53, 100, "t"], [124, 105, 53, 101], [124, 108, 53, 104, "y"], [124, 109, 53, 105], [124, 112, 53, 108, "z"], [124, 113, 53, 109], [124, 116, 53, 112, "s"], [124, 117, 53, 113], [124, 120, 53, 116, "x"], [124, 121, 53, 117], [124, 123, 53, 119], [124, 124, 53, 120], [124, 126, 53, 122, "t"], [124, 127, 53, 123], [124, 130, 53, 126, "x"], [124, 131, 53, 127], [124, 134, 53, 130, "z"], [124, 135, 53, 131], [124, 138, 53, 134, "s"], [124, 139, 53, 135], [124, 142, 53, 138, "y"], [124, 143, 53, 139], [124, 145, 53, 141, "t"], [124, 146, 53, 142], [124, 149, 53, 145, "y"], [124, 150, 53, 146], [124, 153, 53, 149, "z"], [124, 154, 53, 150], [124, 157, 53, 153, "s"], [124, 158, 53, 154], [124, 161, 53, 157, "x"], [124, 162, 53, 158], [124, 164, 53, 160, "t"], [124, 165, 53, 161], [124, 168, 53, 164, "z"], [124, 169, 53, 165], [124, 172, 53, 168, "z"], [124, 173, 53, 169], [124, 176, 53, 172, "c"], [124, 177, 53, 173], [124, 179, 53, 175], [124, 180, 53, 176], [124, 182, 53, 178], [124, 183, 53, 179], [124, 185, 53, 181], [124, 186, 53, 182], [124, 188, 53, 184], [124, 189, 53, 185], [124, 191, 53, 187], [124, 192, 53, 188], [124, 193, 53, 189], [125, 4, 54, 0], [125, 5, 54, 1], [126, 4, 54, 1, "shopify_Matrix4Js6"], [126, 22, 54, 1], [126, 23, 54, 1, "__closure"], [126, 32, 54, 1], [127, 4, 54, 1, "shopify_Matrix4Js6"], [127, 22, 54, 1], [127, 23, 54, 1, "__workletHash"], [127, 36, 54, 1], [128, 4, 54, 1, "shopify_Matrix4Js6"], [128, 22, 54, 1], [128, 23, 54, 1, "__initData"], [128, 33, 54, 1], [128, 36, 54, 1, "_worklet_14051658427690_init_data"], [128, 69, 54, 1], [129, 4, 54, 1, "shopify_Matrix4Js6"], [129, 22, 54, 1], [129, 23, 54, 1, "__stackDetails"], [129, 37, 54, 1], [129, 40, 54, 1, "_e"], [129, 42, 54, 1], [130, 4, 54, 1], [130, 11, 54, 1, "shopify_Matrix4Js6"], [130, 29, 54, 1], [131, 2, 54, 1], [131, 3, 44, 26], [131, 5, 54, 1], [133, 2, 56, 0], [134, 0, 57, 0], [135, 0, 58, 0], [136, 2, 56, 0], [136, 8, 56, 0, "_worklet_15831962852770_init_data"], [136, 41, 56, 0], [137, 4, 56, 0, "code"], [137, 8, 56, 0], [138, 4, 56, 0, "location"], [138, 12, 56, 0], [139, 4, 56, 0, "sourceMap"], [139, 13, 56, 0], [140, 4, 56, 0, "version"], [140, 11, 56, 0], [141, 2, 56, 0], [142, 2, 59, 7], [142, 8, 59, 13, "matrixVecMul4"], [142, 21, 59, 26], [142, 24, 59, 26, "exports"], [142, 31, 59, 26], [142, 32, 59, 26, "matrixVecMul4"], [142, 45, 59, 26], [142, 48, 59, 29], [143, 4, 59, 29], [143, 10, 59, 29, "_e"], [143, 12, 59, 29], [143, 20, 59, 29, "global"], [143, 26, 59, 29], [143, 27, 59, 29, "Error"], [143, 32, 59, 29], [144, 4, 59, 29], [144, 10, 59, 29, "shopify_Matrix4Js7"], [144, 28, 59, 29], [144, 40, 59, 29, "shopify_Matrix4Js7"], [144, 41, 59, 30, "m"], [144, 42, 59, 31], [144, 44, 59, 33, "v"], [144, 45, 59, 34], [144, 47, 59, 39], [145, 6, 62, 2], [145, 13, 62, 9], [145, 14, 62, 10, "m"], [145, 15, 62, 11], [145, 16, 62, 12], [145, 17, 62, 13], [145, 18, 62, 14], [145, 21, 62, 17, "v"], [145, 22, 62, 18], [145, 23, 62, 19], [145, 24, 62, 20], [145, 25, 62, 21], [145, 28, 62, 24, "m"], [145, 29, 62, 25], [145, 30, 62, 26], [145, 31, 62, 27], [145, 32, 62, 28], [145, 35, 62, 31, "v"], [145, 36, 62, 32], [145, 37, 62, 33], [145, 38, 62, 34], [145, 39, 62, 35], [145, 42, 62, 38, "m"], [145, 43, 62, 39], [145, 44, 62, 40], [145, 45, 62, 41], [145, 46, 62, 42], [145, 49, 62, 45, "v"], [145, 50, 62, 46], [145, 51, 62, 47], [145, 52, 62, 48], [145, 53, 62, 49], [145, 56, 62, 52, "m"], [145, 57, 62, 53], [145, 58, 62, 54], [145, 59, 62, 55], [145, 60, 62, 56], [145, 63, 62, 59, "v"], [145, 64, 62, 60], [145, 65, 62, 61], [145, 66, 62, 62], [145, 67, 62, 63], [145, 69, 62, 65, "m"], [145, 70, 62, 66], [145, 71, 62, 67], [145, 72, 62, 68], [145, 73, 62, 69], [145, 76, 62, 72, "v"], [145, 77, 62, 73], [145, 78, 62, 74], [145, 79, 62, 75], [145, 80, 62, 76], [145, 83, 62, 79, "m"], [145, 84, 62, 80], [145, 85, 62, 81], [145, 86, 62, 82], [145, 87, 62, 83], [145, 90, 62, 86, "v"], [145, 91, 62, 87], [145, 92, 62, 88], [145, 93, 62, 89], [145, 94, 62, 90], [145, 97, 62, 93, "m"], [145, 98, 62, 94], [145, 99, 62, 95], [145, 100, 62, 96], [145, 101, 62, 97], [145, 104, 62, 100, "v"], [145, 105, 62, 101], [145, 106, 62, 102], [145, 107, 62, 103], [145, 108, 62, 104], [145, 111, 62, 107, "m"], [145, 112, 62, 108], [145, 113, 62, 109], [145, 114, 62, 110], [145, 115, 62, 111], [145, 118, 62, 114, "v"], [145, 119, 62, 115], [145, 120, 62, 116], [145, 121, 62, 117], [145, 122, 62, 118], [145, 124, 62, 120, "m"], [145, 125, 62, 121], [145, 126, 62, 122], [145, 127, 62, 123], [145, 128, 62, 124], [145, 131, 62, 127, "v"], [145, 132, 62, 128], [145, 133, 62, 129], [145, 134, 62, 130], [145, 135, 62, 131], [145, 138, 62, 134, "m"], [145, 139, 62, 135], [145, 140, 62, 136], [145, 141, 62, 137], [145, 142, 62, 138], [145, 145, 62, 141, "v"], [145, 146, 62, 142], [145, 147, 62, 143], [145, 148, 62, 144], [145, 149, 62, 145], [145, 152, 62, 148, "m"], [145, 153, 62, 149], [145, 154, 62, 150], [145, 156, 62, 152], [145, 157, 62, 153], [145, 160, 62, 156, "v"], [145, 161, 62, 157], [145, 162, 62, 158], [145, 163, 62, 159], [145, 164, 62, 160], [145, 167, 62, 163, "m"], [145, 168, 62, 164], [145, 169, 62, 165], [145, 171, 62, 167], [145, 172, 62, 168], [145, 175, 62, 171, "v"], [145, 176, 62, 172], [145, 177, 62, 173], [145, 178, 62, 174], [145, 179, 62, 175], [145, 181, 62, 177, "m"], [145, 182, 62, 178], [145, 183, 62, 179], [145, 185, 62, 181], [145, 186, 62, 182], [145, 189, 62, 185, "v"], [145, 190, 62, 186], [145, 191, 62, 187], [145, 192, 62, 188], [145, 193, 62, 189], [145, 196, 62, 192, "m"], [145, 197, 62, 193], [145, 198, 62, 194], [145, 200, 62, 196], [145, 201, 62, 197], [145, 204, 62, 200, "v"], [145, 205, 62, 201], [145, 206, 62, 202], [145, 207, 62, 203], [145, 208, 62, 204], [145, 211, 62, 207, "m"], [145, 212, 62, 208], [145, 213, 62, 209], [145, 215, 62, 211], [145, 216, 62, 212], [145, 219, 62, 215, "v"], [145, 220, 62, 216], [145, 221, 62, 217], [145, 222, 62, 218], [145, 223, 62, 219], [145, 226, 62, 222, "m"], [145, 227, 62, 223], [145, 228, 62, 224], [145, 230, 62, 226], [145, 231, 62, 227], [145, 234, 62, 230, "v"], [145, 235, 62, 231], [145, 236, 62, 232], [145, 237, 62, 233], [145, 238, 62, 234], [145, 239, 62, 235], [146, 4, 63, 0], [146, 5, 63, 1], [147, 4, 63, 1, "shopify_Matrix4Js7"], [147, 22, 63, 1], [147, 23, 63, 1, "__closure"], [147, 32, 63, 1], [148, 4, 63, 1, "shopify_Matrix4Js7"], [148, 22, 63, 1], [148, 23, 63, 1, "__workletHash"], [148, 36, 63, 1], [149, 4, 63, 1, "shopify_Matrix4Js7"], [149, 22, 63, 1], [149, 23, 63, 1, "__initData"], [149, 33, 63, 1], [149, 36, 63, 1, "_worklet_15831962852770_init_data"], [149, 69, 63, 1], [150, 4, 63, 1, "shopify_Matrix4Js7"], [150, 22, 63, 1], [150, 23, 63, 1, "__stackDetails"], [150, 37, 63, 1], [150, 40, 63, 1, "_e"], [150, 42, 63, 1], [151, 4, 63, 1], [151, 11, 63, 1, "shopify_Matrix4Js7"], [151, 29, 63, 1], [152, 2, 63, 1], [152, 3, 59, 29], [152, 5, 63, 1], [154, 2, 65, 0], [155, 0, 66, 0], [156, 0, 67, 0], [157, 2, 65, 0], [157, 8, 65, 0, "_worklet_6689500399552_init_data"], [157, 40, 65, 0], [158, 4, 65, 0, "code"], [158, 8, 65, 0], [159, 4, 65, 0, "location"], [159, 12, 65, 0], [160, 4, 65, 0, "sourceMap"], [160, 13, 65, 0], [161, 4, 65, 0, "version"], [161, 11, 65, 0], [162, 2, 65, 0], [163, 2, 68, 7], [163, 8, 68, 13, "mapPoint3d"], [163, 18, 68, 23], [163, 21, 68, 23, "exports"], [163, 28, 68, 23], [163, 29, 68, 23, "mapPoint3d"], [163, 39, 68, 23], [163, 42, 68, 26], [164, 4, 68, 26], [164, 10, 68, 26, "_e"], [164, 12, 68, 26], [164, 20, 68, 26, "global"], [164, 26, 68, 26], [164, 27, 68, 26, "Error"], [164, 32, 68, 26], [165, 4, 68, 26], [165, 10, 68, 26, "shopify_Matrix4Js8"], [165, 28, 68, 26], [165, 40, 68, 26, "shopify_Matrix4Js8"], [165, 41, 68, 27, "m"], [165, 42, 68, 28], [165, 44, 68, 30, "v"], [165, 45, 68, 31], [165, 47, 68, 36], [166, 6, 71, 2], [166, 12, 71, 8, "r"], [166, 13, 71, 9], [166, 16, 71, 12, "matrixVecMul4"], [166, 29, 71, 25], [166, 30, 71, 26, "m"], [166, 31, 71, 27], [166, 33, 71, 29], [166, 34, 71, 30], [166, 37, 71, 33, "v"], [166, 38, 71, 34], [166, 40, 71, 36], [166, 41, 71, 37], [166, 42, 71, 38], [166, 43, 71, 39], [167, 6, 72, 2], [167, 13, 72, 9], [167, 14, 72, 10, "r"], [167, 15, 72, 11], [167, 16, 72, 12], [167, 17, 72, 13], [167, 18, 72, 14], [167, 21, 72, 17, "r"], [167, 22, 72, 18], [167, 23, 72, 19], [167, 24, 72, 20], [167, 25, 72, 21], [167, 27, 72, 23, "r"], [167, 28, 72, 24], [167, 29, 72, 25], [167, 30, 72, 26], [167, 31, 72, 27], [167, 34, 72, 30, "r"], [167, 35, 72, 31], [167, 36, 72, 32], [167, 37, 72, 33], [167, 38, 72, 34], [167, 40, 72, 36, "r"], [167, 41, 72, 37], [167, 42, 72, 38], [167, 43, 72, 39], [167, 44, 72, 40], [167, 47, 72, 43, "r"], [167, 48, 72, 44], [167, 49, 72, 45], [167, 50, 72, 46], [167, 51, 72, 47], [167, 52, 72, 48], [168, 4, 73, 0], [168, 5, 73, 1], [169, 4, 73, 1, "shopify_Matrix4Js8"], [169, 22, 73, 1], [169, 23, 73, 1, "__closure"], [169, 32, 73, 1], [170, 6, 73, 1, "matrixVecMul4"], [171, 4, 73, 1], [172, 4, 73, 1, "shopify_Matrix4Js8"], [172, 22, 73, 1], [172, 23, 73, 1, "__workletHash"], [172, 36, 73, 1], [173, 4, 73, 1, "shopify_Matrix4Js8"], [173, 22, 73, 1], [173, 23, 73, 1, "__initData"], [173, 33, 73, 1], [173, 36, 73, 1, "_worklet_6689500399552_init_data"], [173, 68, 73, 1], [174, 4, 73, 1, "shopify_Matrix4Js8"], [174, 22, 73, 1], [174, 23, 73, 1, "__stackDetails"], [174, 37, 73, 1], [174, 40, 73, 1, "_e"], [174, 42, 73, 1], [175, 4, 73, 1], [175, 11, 73, 1, "shopify_Matrix4Js8"], [175, 29, 73, 1], [176, 2, 73, 1], [176, 3, 68, 26], [176, 5, 73, 1], [178, 2, 75, 0], [179, 0, 76, 0], [180, 0, 77, 0], [181, 2, 75, 0], [181, 8, 75, 0, "_worklet_5765954600262_init_data"], [181, 40, 75, 0], [182, 4, 75, 0, "code"], [182, 8, 75, 0], [183, 4, 75, 0, "location"], [183, 12, 75, 0], [184, 4, 75, 0, "sourceMap"], [184, 13, 75, 0], [185, 4, 75, 0, "version"], [185, 11, 75, 0], [186, 2, 75, 0], [187, 2, 78, 7], [187, 8, 78, 13, "multiply4"], [187, 17, 78, 22], [187, 20, 78, 22, "exports"], [187, 27, 78, 22], [187, 28, 78, 22, "multiply4"], [187, 37, 78, 22], [187, 40, 78, 25], [188, 4, 78, 25], [188, 10, 78, 25, "_e"], [188, 12, 78, 25], [188, 20, 78, 25, "global"], [188, 26, 78, 25], [188, 27, 78, 25, "Error"], [188, 32, 78, 25], [189, 4, 78, 25], [189, 10, 78, 25, "shopify_Matrix4Js9"], [189, 28, 78, 25], [189, 40, 78, 25, "shopify_Matrix4Js9"], [189, 41, 78, 26, "a"], [189, 42, 78, 27], [189, 44, 78, 29, "b"], [189, 45, 78, 30], [189, 47, 78, 35], [190, 6, 81, 2], [190, 12, 81, 8, "result"], [190, 18, 81, 14], [190, 21, 81, 17], [190, 25, 81, 21, "Array"], [190, 30, 81, 26], [190, 31, 81, 27], [190, 33, 81, 29], [190, 34, 81, 30], [190, 35, 81, 31, "fill"], [190, 39, 81, 35], [190, 40, 81, 36], [190, 41, 81, 37], [190, 42, 81, 38], [191, 6, 82, 2], [191, 11, 82, 7], [191, 15, 82, 11, "i"], [191, 16, 82, 12], [191, 19, 82, 15], [191, 20, 82, 16], [191, 22, 82, 18, "i"], [191, 23, 82, 19], [191, 26, 82, 22], [191, 27, 82, 23], [191, 29, 82, 25, "i"], [191, 30, 82, 26], [191, 32, 82, 28], [191, 34, 82, 30], [192, 8, 83, 4], [192, 13, 83, 9], [192, 17, 83, 13, "j"], [192, 18, 83, 14], [192, 21, 83, 17], [192, 22, 83, 18], [192, 24, 83, 20, "j"], [192, 25, 83, 21], [192, 28, 83, 24], [192, 29, 83, 25], [192, 31, 83, 27, "j"], [192, 32, 83, 28], [192, 34, 83, 30], [192, 36, 83, 32], [193, 10, 84, 6, "result"], [193, 16, 84, 12], [193, 17, 84, 13, "i"], [193, 18, 84, 14], [193, 21, 84, 17], [193, 22, 84, 18], [193, 25, 84, 21, "j"], [193, 26, 84, 22], [193, 27, 84, 23], [193, 30, 84, 26, "a"], [193, 31, 84, 27], [193, 32, 84, 28, "i"], [193, 33, 84, 29], [193, 36, 84, 32], [193, 37, 84, 33], [193, 38, 84, 34], [193, 41, 84, 37, "b"], [193, 42, 84, 38], [193, 43, 84, 39, "j"], [193, 44, 84, 40], [193, 45, 84, 41], [193, 48, 84, 44, "a"], [193, 49, 84, 45], [193, 50, 84, 46, "i"], [193, 51, 84, 47], [193, 54, 84, 50], [193, 55, 84, 51], [193, 58, 84, 54], [193, 59, 84, 55], [193, 60, 84, 56], [193, 63, 84, 59, "b"], [193, 64, 84, 60], [193, 65, 84, 61, "j"], [193, 66, 84, 62], [193, 69, 84, 65], [193, 70, 84, 66], [193, 71, 84, 67], [193, 74, 84, 70, "a"], [193, 75, 84, 71], [193, 76, 84, 72, "i"], [193, 77, 84, 73], [193, 80, 84, 76], [193, 81, 84, 77], [193, 84, 84, 80], [193, 85, 84, 81], [193, 86, 84, 82], [193, 89, 84, 85, "b"], [193, 90, 84, 86], [193, 91, 84, 87, "j"], [193, 92, 84, 88], [193, 95, 84, 91], [193, 96, 84, 92], [193, 97, 84, 93], [193, 100, 84, 96, "a"], [193, 101, 84, 97], [193, 102, 84, 98, "i"], [193, 103, 84, 99], [193, 106, 84, 102], [193, 107, 84, 103], [193, 110, 84, 106], [193, 111, 84, 107], [193, 112, 84, 108], [193, 115, 84, 111, "b"], [193, 116, 84, 112], [193, 117, 84, 113, "j"], [193, 118, 84, 114], [193, 121, 84, 117], [193, 123, 84, 119], [193, 124, 84, 120], [194, 8, 85, 4], [195, 6, 86, 2], [196, 6, 87, 2], [196, 13, 87, 9, "result"], [196, 19, 87, 15], [197, 4, 88, 0], [197, 5, 88, 1], [198, 4, 88, 1, "shopify_Matrix4Js9"], [198, 22, 88, 1], [198, 23, 88, 1, "__closure"], [198, 32, 88, 1], [199, 4, 88, 1, "shopify_Matrix4Js9"], [199, 22, 88, 1], [199, 23, 88, 1, "__workletHash"], [199, 36, 88, 1], [200, 4, 88, 1, "shopify_Matrix4Js9"], [200, 22, 88, 1], [200, 23, 88, 1, "__initData"], [200, 33, 88, 1], [200, 36, 88, 1, "_worklet_5765954600262_init_data"], [200, 68, 88, 1], [201, 4, 88, 1, "shopify_Matrix4Js9"], [201, 22, 88, 1], [201, 23, 88, 1, "__stackDetails"], [201, 37, 88, 1], [201, 40, 88, 1, "_e"], [201, 42, 88, 1], [202, 4, 88, 1], [202, 11, 88, 1, "shopify_Matrix4Js9"], [202, 29, 88, 1], [203, 2, 88, 1], [203, 3, 78, 25], [203, 5, 88, 1], [204, 2, 88, 2], [204, 8, 88, 2, "_worklet_4773075386487_init_data"], [204, 40, 88, 2], [205, 4, 88, 2, "code"], [205, 8, 88, 2], [206, 4, 88, 2, "location"], [206, 12, 88, 2], [207, 4, 88, 2, "sourceMap"], [207, 13, 88, 2], [208, 4, 88, 2, "version"], [208, 11, 88, 2], [209, 2, 88, 2], [210, 2, 89, 0], [210, 8, 89, 6, "skewY"], [210, 13, 89, 11], [210, 16, 89, 14], [211, 4, 89, 14], [211, 10, 89, 14, "_e"], [211, 12, 89, 14], [211, 20, 89, 14, "global"], [211, 26, 89, 14], [211, 27, 89, 14, "Error"], [211, 32, 89, 14], [212, 4, 89, 14], [212, 10, 89, 14, "shopify_Matrix4Js10"], [212, 29, 89, 14], [212, 41, 89, 14, "shopify_Matrix4Js10"], [212, 42, 89, 14, "angle"], [212, 47, 89, 19], [212, 49, 89, 23], [213, 6, 92, 2], [213, 13, 92, 9], [213, 14, 92, 10], [213, 15, 92, 11], [213, 17, 92, 13, "Math"], [213, 21, 92, 17], [213, 22, 92, 18, "tan"], [213, 25, 92, 21], [213, 26, 92, 22, "angle"], [213, 31, 92, 27], [213, 32, 92, 28], [213, 34, 92, 30], [213, 35, 92, 31], [213, 37, 92, 33], [213, 38, 92, 34], [213, 40, 92, 36], [213, 41, 92, 37], [213, 43, 92, 39], [213, 44, 92, 40], [213, 46, 92, 42], [213, 47, 92, 43], [213, 49, 92, 45], [213, 50, 92, 46], [213, 52, 92, 48], [213, 53, 92, 49], [213, 55, 92, 51], [213, 56, 92, 52], [213, 58, 92, 54], [213, 59, 92, 55], [213, 61, 92, 57], [213, 62, 92, 58], [213, 64, 92, 60], [213, 65, 92, 61], [213, 67, 92, 63], [213, 68, 92, 64], [213, 70, 92, 66], [213, 71, 92, 67], [213, 73, 92, 69], [213, 74, 92, 70], [213, 75, 92, 71], [214, 4, 93, 0], [214, 5, 93, 1], [215, 4, 93, 1, "shopify_Matrix4Js10"], [215, 23, 93, 1], [215, 24, 93, 1, "__closure"], [215, 33, 93, 1], [216, 4, 93, 1, "shopify_Matrix4Js10"], [216, 23, 93, 1], [216, 24, 93, 1, "__workletHash"], [216, 37, 93, 1], [217, 4, 93, 1, "shopify_Matrix4Js10"], [217, 23, 93, 1], [217, 24, 93, 1, "__initData"], [217, 34, 93, 1], [217, 37, 93, 1, "_worklet_4773075386487_init_data"], [217, 69, 93, 1], [218, 4, 93, 1, "shopify_Matrix4Js10"], [218, 23, 93, 1], [218, 24, 93, 1, "__stackDetails"], [218, 38, 93, 1], [218, 41, 93, 1, "_e"], [218, 43, 93, 1], [219, 4, 93, 1], [219, 11, 93, 1, "shopify_Matrix4Js10"], [219, 30, 93, 1], [220, 2, 93, 1], [220, 3, 89, 14], [220, 5, 93, 1], [221, 2, 93, 2], [221, 8, 93, 2, "_worklet_1871192719510_init_data"], [221, 40, 93, 2], [222, 4, 93, 2, "code"], [222, 8, 93, 2], [223, 4, 93, 2, "location"], [223, 12, 93, 2], [224, 4, 93, 2, "sourceMap"], [224, 13, 93, 2], [225, 4, 93, 2, "version"], [225, 11, 93, 2], [226, 2, 93, 2], [227, 2, 94, 0], [227, 8, 94, 6, "skewX"], [227, 13, 94, 11], [227, 16, 94, 14], [228, 4, 94, 14], [228, 10, 94, 14, "_e"], [228, 12, 94, 14], [228, 20, 94, 14, "global"], [228, 26, 94, 14], [228, 27, 94, 14, "Error"], [228, 32, 94, 14], [229, 4, 94, 14], [229, 10, 94, 14, "shopify_Matrix4Js11"], [229, 29, 94, 14], [229, 41, 94, 14, "shopify_Matrix4Js11"], [229, 42, 94, 14, "angle"], [229, 47, 94, 19], [229, 49, 94, 23], [230, 6, 97, 2], [230, 13, 97, 9], [230, 14, 97, 10], [230, 15, 97, 11], [230, 17, 97, 13], [230, 18, 97, 14], [230, 20, 97, 16], [230, 21, 97, 17], [230, 23, 97, 19], [230, 24, 97, 20], [230, 26, 97, 22, "Math"], [230, 30, 97, 26], [230, 31, 97, 27, "tan"], [230, 34, 97, 30], [230, 35, 97, 31, "angle"], [230, 40, 97, 36], [230, 41, 97, 37], [230, 43, 97, 39], [230, 44, 97, 40], [230, 46, 97, 42], [230, 47, 97, 43], [230, 49, 97, 45], [230, 50, 97, 46], [230, 52, 97, 48], [230, 53, 97, 49], [230, 55, 97, 51], [230, 56, 97, 52], [230, 58, 97, 54], [230, 59, 97, 55], [230, 61, 97, 57], [230, 62, 97, 58], [230, 64, 97, 60], [230, 65, 97, 61], [230, 67, 97, 63], [230, 68, 97, 64], [230, 70, 97, 66], [230, 71, 97, 67], [230, 73, 97, 69], [230, 74, 97, 70], [230, 75, 97, 71], [231, 4, 98, 0], [231, 5, 98, 1], [232, 4, 98, 1, "shopify_Matrix4Js11"], [232, 23, 98, 1], [232, 24, 98, 1, "__closure"], [232, 33, 98, 1], [233, 4, 98, 1, "shopify_Matrix4Js11"], [233, 23, 98, 1], [233, 24, 98, 1, "__workletHash"], [233, 37, 98, 1], [234, 4, 98, 1, "shopify_Matrix4Js11"], [234, 23, 98, 1], [234, 24, 98, 1, "__initData"], [234, 34, 98, 1], [234, 37, 98, 1, "_worklet_1871192719510_init_data"], [234, 69, 98, 1], [235, 4, 98, 1, "shopify_Matrix4Js11"], [235, 23, 98, 1], [235, 24, 98, 1, "__stackDetails"], [235, 38, 98, 1], [235, 41, 98, 1, "_e"], [235, 43, 98, 1], [236, 4, 98, 1], [236, 11, 98, 1, "shopify_Matrix4Js11"], [236, 30, 98, 1], [237, 2, 98, 1], [237, 3, 94, 14], [237, 5, 98, 1], [239, 2, 100, 0], [240, 0, 101, 0], [241, 0, 102, 0], [242, 2, 100, 0], [242, 8, 100, 0, "_worklet_10458516888298_init_data"], [242, 41, 100, 0], [243, 4, 100, 0, "code"], [243, 8, 100, 0], [244, 4, 100, 0, "location"], [244, 12, 100, 0], [245, 4, 100, 0, "sourceMap"], [245, 13, 100, 0], [246, 4, 100, 0, "version"], [246, 11, 100, 0], [247, 2, 100, 0], [248, 2, 103, 7], [248, 8, 103, 13, "toMatrix3"], [248, 17, 103, 22], [248, 20, 103, 22, "exports"], [248, 27, 103, 22], [248, 28, 103, 22, "toMatrix3"], [248, 37, 103, 22], [248, 40, 103, 25], [249, 4, 103, 25], [249, 10, 103, 25, "_e"], [249, 12, 103, 25], [249, 20, 103, 25, "global"], [249, 26, 103, 25], [249, 27, 103, 25, "Error"], [249, 32, 103, 25], [250, 4, 103, 25], [250, 10, 103, 25, "shopify_Matrix4Js12"], [250, 29, 103, 25], [250, 41, 103, 25, "shopify_Matrix4Js12"], [250, 42, 103, 25, "m"], [250, 43, 103, 26], [250, 45, 103, 30], [251, 6, 106, 2], [251, 13, 106, 9], [251, 14, 106, 10, "m"], [251, 15, 106, 11], [251, 16, 106, 12], [251, 17, 106, 13], [251, 18, 106, 14], [251, 20, 106, 16, "m"], [251, 21, 106, 17], [251, 22, 106, 18], [251, 23, 106, 19], [251, 24, 106, 20], [251, 26, 106, 22, "m"], [251, 27, 106, 23], [251, 28, 106, 24], [251, 29, 106, 25], [251, 30, 106, 26], [251, 32, 106, 28, "m"], [251, 33, 106, 29], [251, 34, 106, 30], [251, 35, 106, 31], [251, 36, 106, 32], [251, 38, 106, 34, "m"], [251, 39, 106, 35], [251, 40, 106, 36], [251, 41, 106, 37], [251, 42, 106, 38], [251, 44, 106, 40, "m"], [251, 45, 106, 41], [251, 46, 106, 42], [251, 47, 106, 43], [251, 48, 106, 44], [251, 50, 106, 46, "m"], [251, 51, 106, 47], [251, 52, 106, 48], [251, 54, 106, 50], [251, 55, 106, 51], [251, 57, 106, 53, "m"], [251, 58, 106, 54], [251, 59, 106, 55], [251, 61, 106, 57], [251, 62, 106, 58], [251, 64, 106, 60, "m"], [251, 65, 106, 61], [251, 66, 106, 62], [251, 68, 106, 64], [251, 69, 106, 65], [251, 70, 106, 66], [252, 4, 107, 0], [252, 5, 107, 1], [253, 4, 107, 1, "shopify_Matrix4Js12"], [253, 23, 107, 1], [253, 24, 107, 1, "__closure"], [253, 33, 107, 1], [254, 4, 107, 1, "shopify_Matrix4Js12"], [254, 23, 107, 1], [254, 24, 107, 1, "__workletHash"], [254, 37, 107, 1], [255, 4, 107, 1, "shopify_Matrix4Js12"], [255, 23, 107, 1], [255, 24, 107, 1, "__initData"], [255, 34, 107, 1], [255, 37, 107, 1, "_worklet_10458516888298_init_data"], [255, 70, 107, 1], [256, 4, 107, 1, "shopify_Matrix4Js12"], [256, 23, 107, 1], [256, 24, 107, 1, "__stackDetails"], [256, 38, 107, 1], [256, 41, 107, 1, "_e"], [256, 43, 107, 1], [257, 4, 107, 1], [257, 11, 107, 1, "shopify_Matrix4Js12"], [257, 30, 107, 1], [258, 2, 107, 1], [258, 3, 103, 25], [258, 5, 107, 1], [259, 2, 107, 2], [259, 8, 107, 2, "_worklet_1303846517270_init_data"], [259, 40, 107, 2], [260, 4, 107, 2, "code"], [260, 8, 107, 2], [261, 4, 107, 2, "location"], [261, 12, 107, 2], [262, 4, 107, 2, "sourceMap"], [262, 13, 107, 2], [263, 4, 107, 2, "version"], [263, 11, 107, 2], [264, 2, 107, 2], [265, 2, 108, 0], [265, 8, 108, 6, "rotate"], [265, 14, 108, 12], [265, 17, 108, 15], [266, 4, 108, 15], [266, 10, 108, 15, "_e"], [266, 12, 108, 15], [266, 20, 108, 15, "global"], [266, 26, 108, 15], [266, 27, 108, 15, "Error"], [266, 32, 108, 15], [267, 4, 108, 15], [267, 10, 108, 15, "shopify_Matrix4Js13"], [267, 29, 108, 15], [267, 41, 108, 15, "shopify_Matrix4Js13"], [267, 42, 108, 16, "axis"], [267, 46, 108, 20], [267, 48, 108, 22, "value"], [267, 53, 108, 27], [267, 55, 108, 32], [268, 6, 111, 2], [268, 13, 111, 9, "rotatedUnitSinCos"], [268, 30, 111, 26], [268, 31, 111, 27, "normalizeVec"], [268, 43, 111, 39], [268, 44, 111, 40, "axis"], [268, 48, 111, 44], [268, 49, 111, 45], [268, 51, 111, 47, "Math"], [268, 55, 111, 51], [268, 56, 111, 52, "sin"], [268, 59, 111, 55], [268, 60, 111, 56, "value"], [268, 65, 111, 61], [268, 66, 111, 62], [268, 68, 111, 64, "Math"], [268, 72, 111, 68], [268, 73, 111, 69, "cos"], [268, 76, 111, 72], [268, 77, 111, 73, "value"], [268, 82, 111, 78], [268, 83, 111, 79], [268, 84, 111, 80], [269, 4, 112, 0], [269, 5, 112, 1], [270, 4, 112, 1, "shopify_Matrix4Js13"], [270, 23, 112, 1], [270, 24, 112, 1, "__closure"], [270, 33, 112, 1], [271, 6, 112, 1, "rotatedUnitSinCos"], [271, 23, 112, 1], [272, 6, 112, 1, "normalizeVec"], [273, 4, 112, 1], [274, 4, 112, 1, "shopify_Matrix4Js13"], [274, 23, 112, 1], [274, 24, 112, 1, "__workletHash"], [274, 37, 112, 1], [275, 4, 112, 1, "shopify_Matrix4Js13"], [275, 23, 112, 1], [275, 24, 112, 1, "__initData"], [275, 34, 112, 1], [275, 37, 112, 1, "_worklet_1303846517270_init_data"], [275, 69, 112, 1], [276, 4, 112, 1, "shopify_Matrix4Js13"], [276, 23, 112, 1], [276, 24, 112, 1, "__stackDetails"], [276, 38, 112, 1], [276, 41, 112, 1, "_e"], [276, 43, 112, 1], [277, 4, 112, 1], [277, 11, 112, 1, "shopify_Matrix4Js13"], [277, 30, 112, 1], [278, 2, 112, 1], [278, 3, 108, 15], [278, 5, 112, 1], [280, 2, 114, 0], [281, 0, 115, 0], [282, 0, 116, 0], [283, 2, 114, 0], [283, 8, 114, 0, "_worklet_11430923795455_init_data"], [283, 41, 114, 0], [284, 4, 114, 0, "code"], [284, 8, 114, 0], [285, 4, 114, 0, "location"], [285, 12, 114, 0], [286, 4, 114, 0, "sourceMap"], [286, 13, 114, 0], [287, 4, 114, 0, "version"], [287, 11, 114, 0], [288, 2, 114, 0], [289, 2, 117, 7], [289, 8, 117, 13, "pivot"], [289, 13, 117, 18], [289, 16, 117, 18, "exports"], [289, 23, 117, 18], [289, 24, 117, 18, "pivot"], [289, 29, 117, 18], [289, 32, 117, 21], [290, 4, 117, 21], [290, 10, 117, 21, "_e"], [290, 12, 117, 21], [290, 20, 117, 21, "global"], [290, 26, 117, 21], [290, 27, 117, 21, "Error"], [290, 32, 117, 21], [291, 4, 117, 21], [291, 10, 117, 21, "shopify_Matrix4Js14"], [291, 29, 117, 21], [291, 41, 117, 21, "shopify_Matrix4Js14"], [291, 42, 117, 22, "m"], [291, 43, 117, 23], [291, 45, 117, 25, "p"], [291, 46, 117, 26], [291, 48, 117, 31], [292, 6, 120, 2], [292, 13, 120, 9, "multiply4"], [292, 22, 120, 18], [292, 23, 120, 19, "translate"], [292, 32, 120, 28], [292, 33, 120, 29, "p"], [292, 34, 120, 30], [292, 35, 120, 31, "x"], [292, 36, 120, 32], [292, 38, 120, 34, "p"], [292, 39, 120, 35], [292, 40, 120, 36, "y"], [292, 41, 120, 37], [292, 42, 120, 38], [292, 44, 120, 40, "multiply4"], [292, 53, 120, 49], [292, 54, 120, 50, "m"], [292, 55, 120, 51], [292, 57, 120, 53, "translate"], [292, 66, 120, 62], [292, 67, 120, 63], [292, 68, 120, 64, "p"], [292, 69, 120, 65], [292, 70, 120, 66, "x"], [292, 71, 120, 67], [292, 73, 120, 69], [292, 74, 120, 70, "p"], [292, 75, 120, 71], [292, 76, 120, 72, "y"], [292, 77, 120, 73], [292, 78, 120, 74], [292, 79, 120, 75], [292, 80, 120, 76], [293, 4, 121, 0], [293, 5, 121, 1], [294, 4, 121, 1, "shopify_Matrix4Js14"], [294, 23, 121, 1], [294, 24, 121, 1, "__closure"], [294, 33, 121, 1], [295, 6, 121, 1, "multiply4"], [295, 15, 121, 1], [296, 6, 121, 1, "translate"], [297, 4, 121, 1], [298, 4, 121, 1, "shopify_Matrix4Js14"], [298, 23, 121, 1], [298, 24, 121, 1, "__workletHash"], [298, 37, 121, 1], [299, 4, 121, 1, "shopify_Matrix4Js14"], [299, 23, 121, 1], [299, 24, 121, 1, "__initData"], [299, 34, 121, 1], [299, 37, 121, 1, "_worklet_11430923795455_init_data"], [299, 70, 121, 1], [300, 4, 121, 1, "shopify_Matrix4Js14"], [300, 23, 121, 1], [300, 24, 121, 1, "__stackDetails"], [300, 38, 121, 1], [300, 41, 121, 1, "_e"], [300, 43, 121, 1], [301, 4, 121, 1], [301, 11, 121, 1, "shopify_Matrix4Js14"], [301, 30, 121, 1], [302, 2, 121, 1], [302, 3, 117, 21], [302, 5, 121, 1], [304, 2, 123, 0], [305, 0, 124, 0], [306, 0, 125, 0], [307, 2, 123, 0], [307, 8, 123, 0, "_worklet_16520454403957_init_data"], [307, 41, 123, 0], [308, 4, 123, 0, "code"], [308, 8, 123, 0], [309, 4, 123, 0, "location"], [309, 12, 123, 0], [310, 4, 123, 0, "sourceMap"], [310, 13, 123, 0], [311, 4, 123, 0, "version"], [311, 11, 123, 0], [312, 2, 123, 0], [313, 2, 126, 7], [313, 8, 126, 13, "scale"], [313, 13, 126, 18], [313, 16, 126, 18, "exports"], [313, 23, 126, 18], [313, 24, 126, 18, "scale"], [313, 29, 126, 18], [313, 32, 126, 21], [314, 4, 126, 21], [314, 10, 126, 21, "_e"], [314, 12, 126, 21], [314, 20, 126, 21, "global"], [314, 26, 126, 21], [314, 27, 126, 21, "Error"], [314, 32, 126, 21], [315, 4, 126, 21], [315, 10, 126, 21, "shopify_Matrix4Js15"], [315, 29, 126, 21], [315, 41, 126, 21, "shopify_Matrix4Js15"], [315, 42, 126, 22, "sx"], [315, 44, 126, 24], [315, 46, 126, 26, "sy"], [315, 48, 126, 28], [315, 50, 126, 30, "sz"], [315, 52, 126, 32], [315, 55, 126, 35], [315, 56, 126, 36], [315, 58, 126, 38, "p"], [315, 59, 126, 39], [315, 61, 126, 44], [316, 6, 129, 2], [316, 12, 129, 8, "m4"], [316, 14, 129, 10], [316, 17, 129, 13], [316, 18, 129, 14, "sx"], [316, 20, 129, 16], [316, 22, 129, 18], [316, 23, 129, 19], [316, 25, 129, 21], [316, 26, 129, 22], [316, 28, 129, 24], [316, 29, 129, 25], [316, 31, 129, 27], [316, 32, 129, 28], [316, 34, 129, 30, "sy"], [316, 36, 129, 32], [316, 38, 129, 34], [316, 39, 129, 35], [316, 41, 129, 37], [316, 42, 129, 38], [316, 44, 129, 40], [316, 45, 129, 41], [316, 47, 129, 43], [316, 48, 129, 44], [316, 50, 129, 46, "sz"], [316, 52, 129, 48], [316, 54, 129, 50], [316, 55, 129, 51], [316, 57, 129, 53], [316, 58, 129, 54], [316, 60, 129, 56], [316, 61, 129, 57], [316, 63, 129, 59], [316, 64, 129, 60], [316, 66, 129, 62], [316, 67, 129, 63], [316, 68, 129, 64], [317, 6, 130, 2], [317, 10, 130, 6, "p"], [317, 11, 130, 7], [317, 13, 130, 9], [318, 8, 131, 4], [318, 15, 131, 11, "pivot"], [318, 20, 131, 16], [318, 21, 131, 17, "m4"], [318, 23, 131, 19], [318, 25, 131, 21, "p"], [318, 26, 131, 22], [318, 27, 131, 23], [319, 6, 132, 2], [320, 6, 133, 2], [320, 13, 133, 9, "m4"], [320, 15, 133, 11], [321, 4, 134, 0], [321, 5, 134, 1], [322, 4, 134, 1, "shopify_Matrix4Js15"], [322, 23, 134, 1], [322, 24, 134, 1, "__closure"], [322, 33, 134, 1], [323, 6, 134, 1, "pivot"], [324, 4, 134, 1], [325, 4, 134, 1, "shopify_Matrix4Js15"], [325, 23, 134, 1], [325, 24, 134, 1, "__workletHash"], [325, 37, 134, 1], [326, 4, 134, 1, "shopify_Matrix4Js15"], [326, 23, 134, 1], [326, 24, 134, 1, "__initData"], [326, 34, 134, 1], [326, 37, 134, 1, "_worklet_16520454403957_init_data"], [326, 70, 134, 1], [327, 4, 134, 1, "shopify_Matrix4Js15"], [327, 23, 134, 1], [327, 24, 134, 1, "__stackDetails"], [327, 38, 134, 1], [327, 41, 134, 1, "_e"], [327, 43, 134, 1], [328, 4, 134, 1], [328, 11, 134, 1, "shopify_Matrix4Js15"], [328, 30, 134, 1], [329, 2, 134, 1], [329, 3, 126, 21], [329, 5, 134, 1], [330, 2, 134, 2], [330, 8, 134, 2, "_worklet_16952079578652_init_data"], [330, 41, 134, 2], [331, 4, 134, 2, "code"], [331, 8, 134, 2], [332, 4, 134, 2, "location"], [332, 12, 134, 2], [333, 4, 134, 2, "sourceMap"], [333, 13, 134, 2], [334, 4, 134, 2, "version"], [334, 11, 134, 2], [335, 2, 134, 2], [336, 2, 135, 0], [336, 8, 135, 6, "rotateAxis"], [336, 18, 135, 16], [336, 21, 135, 19], [337, 4, 135, 19], [337, 10, 135, 19, "_e"], [337, 12, 135, 19], [337, 20, 135, 19, "global"], [337, 26, 135, 19], [337, 27, 135, 19, "Error"], [337, 32, 135, 19], [338, 4, 135, 19], [338, 10, 135, 19, "shopify_Matrix4Js16"], [338, 29, 135, 19], [338, 41, 135, 19, "shopify_Matrix4Js16"], [338, 42, 135, 20, "axis"], [338, 46, 135, 24], [338, 48, 135, 26, "angle"], [338, 53, 135, 31], [338, 55, 135, 33, "p"], [338, 56, 135, 34], [338, 58, 135, 39], [339, 6, 138, 2], [339, 12, 138, 8, "result"], [339, 18, 138, 14], [339, 21, 138, 17, "rotate"], [339, 27, 138, 23], [339, 28, 138, 24, "axis"], [339, 32, 138, 28], [339, 34, 138, 30, "angle"], [339, 39, 138, 35], [339, 40, 138, 36], [340, 6, 139, 2], [340, 10, 139, 6, "p"], [340, 11, 139, 7], [340, 13, 139, 9], [341, 8, 140, 4], [341, 15, 140, 11, "pivot"], [341, 20, 140, 16], [341, 21, 140, 17, "result"], [341, 27, 140, 23], [341, 29, 140, 25, "p"], [341, 30, 140, 26], [341, 31, 140, 27], [342, 6, 141, 2], [343, 6, 142, 2], [343, 13, 142, 9, "result"], [343, 19, 142, 15], [344, 4, 143, 0], [344, 5, 143, 1], [345, 4, 143, 1, "shopify_Matrix4Js16"], [345, 23, 143, 1], [345, 24, 143, 1, "__closure"], [345, 33, 143, 1], [346, 6, 143, 1, "rotate"], [346, 12, 143, 1], [347, 6, 143, 1, "pivot"], [348, 4, 143, 1], [349, 4, 143, 1, "shopify_Matrix4Js16"], [349, 23, 143, 1], [349, 24, 143, 1, "__workletHash"], [349, 37, 143, 1], [350, 4, 143, 1, "shopify_Matrix4Js16"], [350, 23, 143, 1], [350, 24, 143, 1, "__initData"], [350, 34, 143, 1], [350, 37, 143, 1, "_worklet_16952079578652_init_data"], [350, 70, 143, 1], [351, 4, 143, 1, "shopify_Matrix4Js16"], [351, 23, 143, 1], [351, 24, 143, 1, "__stackDetails"], [351, 38, 143, 1], [351, 41, 143, 1, "_e"], [351, 43, 143, 1], [352, 4, 143, 1], [352, 11, 143, 1, "shopify_Matrix4Js16"], [352, 30, 143, 1], [353, 2, 143, 1], [353, 3, 135, 19], [353, 5, 143, 1], [355, 2, 145, 0], [356, 0, 146, 0], [357, 0, 147, 0], [358, 2, 145, 0], [358, 8, 145, 0, "_worklet_3243560710632_init_data"], [358, 40, 145, 0], [359, 4, 145, 0, "code"], [359, 8, 145, 0], [360, 4, 145, 0, "location"], [360, 12, 145, 0], [361, 4, 145, 0, "sourceMap"], [361, 13, 145, 0], [362, 4, 145, 0, "version"], [362, 11, 145, 0], [363, 2, 145, 0], [364, 2, 148, 7], [364, 8, 148, 13, "rotateZ"], [364, 15, 148, 20], [364, 18, 148, 20, "exports"], [364, 25, 148, 20], [364, 26, 148, 20, "rotateZ"], [364, 33, 148, 20], [364, 36, 148, 23], [365, 4, 148, 23], [365, 10, 148, 23, "_e"], [365, 12, 148, 23], [365, 20, 148, 23, "global"], [365, 26, 148, 23], [365, 27, 148, 23, "Error"], [365, 32, 148, 23], [366, 4, 148, 23], [366, 10, 148, 23, "shopify_Matrix4Js17"], [366, 29, 148, 23], [366, 41, 148, 23, "shopify_Matrix4Js17"], [366, 42, 148, 24, "value"], [366, 47, 148, 29], [366, 49, 148, 31, "p"], [366, 50, 148, 32], [366, 52, 148, 37], [367, 6, 151, 2], [367, 13, 151, 9, "rotateAxis"], [367, 23, 151, 19], [367, 24, 151, 20], [367, 25, 151, 21], [367, 26, 151, 22], [367, 28, 151, 24], [367, 29, 151, 25], [367, 31, 151, 27], [367, 32, 151, 28], [367, 33, 151, 29], [367, 35, 151, 31, "value"], [367, 40, 151, 36], [367, 42, 151, 38, "p"], [367, 43, 151, 39], [367, 44, 151, 40], [368, 4, 152, 0], [368, 5, 152, 1], [369, 4, 152, 1, "shopify_Matrix4Js17"], [369, 23, 152, 1], [369, 24, 152, 1, "__closure"], [369, 33, 152, 1], [370, 6, 152, 1, "rotateAxis"], [371, 4, 152, 1], [372, 4, 152, 1, "shopify_Matrix4Js17"], [372, 23, 152, 1], [372, 24, 152, 1, "__workletHash"], [372, 37, 152, 1], [373, 4, 152, 1, "shopify_Matrix4Js17"], [373, 23, 152, 1], [373, 24, 152, 1, "__initData"], [373, 34, 152, 1], [373, 37, 152, 1, "_worklet_3243560710632_init_data"], [373, 69, 152, 1], [374, 4, 152, 1, "shopify_Matrix4Js17"], [374, 23, 152, 1], [374, 24, 152, 1, "__stackDetails"], [374, 38, 152, 1], [374, 41, 152, 1, "_e"], [374, 43, 152, 1], [375, 4, 152, 1], [375, 11, 152, 1, "shopify_Matrix4Js17"], [375, 30, 152, 1], [376, 2, 152, 1], [376, 3, 148, 23], [376, 5, 152, 1], [378, 2, 154, 0], [379, 0, 155, 0], [380, 0, 156, 0], [381, 2, 154, 0], [381, 8, 154, 0, "_worklet_5701320477575_init_data"], [381, 40, 154, 0], [382, 4, 154, 0, "code"], [382, 8, 154, 0], [383, 4, 154, 0, "location"], [383, 12, 154, 0], [384, 4, 154, 0, "sourceMap"], [384, 13, 154, 0], [385, 4, 154, 0, "version"], [385, 11, 154, 0], [386, 2, 154, 0], [387, 2, 157, 7], [387, 8, 157, 13, "rotateX"], [387, 15, 157, 20], [387, 18, 157, 20, "exports"], [387, 25, 157, 20], [387, 26, 157, 20, "rotateX"], [387, 33, 157, 20], [387, 36, 157, 23], [388, 4, 157, 23], [388, 10, 157, 23, "_e"], [388, 12, 157, 23], [388, 20, 157, 23, "global"], [388, 26, 157, 23], [388, 27, 157, 23, "Error"], [388, 32, 157, 23], [389, 4, 157, 23], [389, 10, 157, 23, "shopify_Matrix4Js18"], [389, 29, 157, 23], [389, 41, 157, 23, "shopify_Matrix4Js18"], [389, 42, 157, 24, "value"], [389, 47, 157, 29], [389, 49, 157, 31, "p"], [389, 50, 157, 32], [389, 52, 157, 37], [390, 6, 160, 2], [390, 13, 160, 9, "rotateAxis"], [390, 23, 160, 19], [390, 24, 160, 20], [390, 25, 160, 21], [390, 26, 160, 22], [390, 28, 160, 24], [390, 29, 160, 25], [390, 31, 160, 27], [390, 32, 160, 28], [390, 33, 160, 29], [390, 35, 160, 31, "value"], [390, 40, 160, 36], [390, 42, 160, 38, "p"], [390, 43, 160, 39], [390, 44, 160, 40], [391, 4, 161, 0], [391, 5, 161, 1], [392, 4, 161, 1, "shopify_Matrix4Js18"], [392, 23, 161, 1], [392, 24, 161, 1, "__closure"], [392, 33, 161, 1], [393, 6, 161, 1, "rotateAxis"], [394, 4, 161, 1], [395, 4, 161, 1, "shopify_Matrix4Js18"], [395, 23, 161, 1], [395, 24, 161, 1, "__workletHash"], [395, 37, 161, 1], [396, 4, 161, 1, "shopify_Matrix4Js18"], [396, 23, 161, 1], [396, 24, 161, 1, "__initData"], [396, 34, 161, 1], [396, 37, 161, 1, "_worklet_5701320477575_init_data"], [396, 69, 161, 1], [397, 4, 161, 1, "shopify_Matrix4Js18"], [397, 23, 161, 1], [397, 24, 161, 1, "__stackDetails"], [397, 38, 161, 1], [397, 41, 161, 1, "_e"], [397, 43, 161, 1], [398, 4, 161, 1], [398, 11, 161, 1, "shopify_Matrix4Js18"], [398, 30, 161, 1], [399, 2, 161, 1], [399, 3, 157, 23], [399, 5, 161, 1], [401, 2, 163, 0], [402, 0, 164, 0], [403, 0, 165, 0], [404, 2, 163, 0], [404, 8, 163, 0, "_worklet_12107565173542_init_data"], [404, 41, 163, 0], [405, 4, 163, 0, "code"], [405, 8, 163, 0], [406, 4, 163, 0, "location"], [406, 12, 163, 0], [407, 4, 163, 0, "sourceMap"], [407, 13, 163, 0], [408, 4, 163, 0, "version"], [408, 11, 163, 0], [409, 2, 163, 0], [410, 2, 166, 7], [410, 8, 166, 13, "rotateY"], [410, 15, 166, 20], [410, 18, 166, 20, "exports"], [410, 25, 166, 20], [410, 26, 166, 20, "rotateY"], [410, 33, 166, 20], [410, 36, 166, 23], [411, 4, 166, 23], [411, 10, 166, 23, "_e"], [411, 12, 166, 23], [411, 20, 166, 23, "global"], [411, 26, 166, 23], [411, 27, 166, 23, "Error"], [411, 32, 166, 23], [412, 4, 166, 23], [412, 10, 166, 23, "shopify_Matrix4Js19"], [412, 29, 166, 23], [412, 41, 166, 23, "shopify_Matrix4Js19"], [412, 42, 166, 24, "value"], [412, 47, 166, 29], [412, 49, 166, 31, "p"], [412, 50, 166, 32], [412, 52, 166, 37], [413, 6, 169, 2], [413, 13, 169, 9, "rotateAxis"], [413, 23, 169, 19], [413, 24, 169, 20], [413, 25, 169, 21], [413, 26, 169, 22], [413, 28, 169, 24], [413, 29, 169, 25], [413, 31, 169, 27], [413, 32, 169, 28], [413, 33, 169, 29], [413, 35, 169, 31, "value"], [413, 40, 169, 36], [413, 42, 169, 38, "p"], [413, 43, 169, 39], [413, 44, 169, 40], [414, 4, 170, 0], [414, 5, 170, 1], [415, 4, 170, 1, "shopify_Matrix4Js19"], [415, 23, 170, 1], [415, 24, 170, 1, "__closure"], [415, 33, 170, 1], [416, 6, 170, 1, "rotateAxis"], [417, 4, 170, 1], [418, 4, 170, 1, "shopify_Matrix4Js19"], [418, 23, 170, 1], [418, 24, 170, 1, "__workletHash"], [418, 37, 170, 1], [419, 4, 170, 1, "shopify_Matrix4Js19"], [419, 23, 170, 1], [419, 24, 170, 1, "__initData"], [419, 34, 170, 1], [419, 37, 170, 1, "_worklet_12107565173542_init_data"], [419, 70, 170, 1], [420, 4, 170, 1, "shopify_Matrix4Js19"], [420, 23, 170, 1], [420, 24, 170, 1, "__stackDetails"], [420, 38, 170, 1], [420, 41, 170, 1, "_e"], [420, 43, 170, 1], [421, 4, 170, 1], [421, 11, 170, 1, "shopify_Matrix4Js19"], [421, 30, 170, 1], [422, 2, 170, 1], [422, 3, 166, 23], [422, 5, 170, 1], [424, 2, 172, 0], [425, 0, 173, 0], [426, 0, 174, 0], [427, 2, 172, 0], [427, 8, 172, 0, "_worklet_2225508254275_init_data"], [427, 40, 172, 0], [428, 4, 172, 0, "code"], [428, 8, 172, 0], [429, 4, 172, 0, "location"], [429, 12, 172, 0], [430, 4, 172, 0, "sourceMap"], [430, 13, 172, 0], [431, 4, 172, 0, "version"], [431, 11, 172, 0], [432, 2, 172, 0], [433, 2, 175, 7], [433, 8, 175, 13, "processTransform3d"], [433, 26, 175, 31], [433, 29, 175, 31, "exports"], [433, 36, 175, 31], [433, 37, 175, 31, "processTransform3d"], [433, 55, 175, 31], [433, 58, 175, 34], [434, 4, 175, 34], [434, 10, 175, 34, "_e"], [434, 12, 175, 34], [434, 20, 175, 34, "global"], [434, 26, 175, 34], [434, 27, 175, 34, "Error"], [434, 32, 175, 34], [435, 4, 175, 34], [435, 10, 175, 34, "shopify_Matrix4Js20"], [435, 29, 175, 34], [435, 41, 175, 34, "shopify_Matrix4Js20"], [435, 42, 175, 34, "transforms"], [435, 52, 175, 44], [435, 54, 175, 48], [436, 6, 178, 2], [436, 13, 178, 9, "transforms"], [436, 23, 178, 19], [436, 24, 178, 20, "reduce"], [436, 30, 178, 26], [436, 31, 178, 27], [436, 32, 178, 28, "acc"], [436, 35, 178, 31], [436, 37, 178, 33, "val"], [436, 40, 178, 36], [436, 45, 178, 41], [437, 8, 179, 4], [437, 14, 179, 10, "key"], [437, 17, 179, 13], [437, 20, 179, 16, "Object"], [437, 26, 179, 22], [437, 27, 179, 23, "keys"], [437, 31, 179, 27], [437, 32, 179, 28, "val"], [437, 35, 179, 31], [437, 36, 179, 32], [437, 37, 179, 33], [437, 38, 179, 34], [437, 39, 179, 35], [438, 8, 180, 4], [438, 14, 180, 10, "transform"], [438, 23, 180, 19], [438, 26, 180, 22, "val"], [438, 29, 180, 25], [439, 8, 181, 4], [439, 12, 181, 8, "key"], [439, 15, 181, 11], [439, 20, 181, 16], [439, 32, 181, 28], [439, 34, 181, 30], [440, 10, 182, 6], [440, 16, 182, 12, "value"], [440, 21, 182, 17], [440, 24, 182, 20, "transform"], [440, 33, 182, 29], [440, 34, 182, 30, "key"], [440, 37, 182, 33], [440, 38, 182, 34], [441, 10, 183, 6], [441, 17, 183, 13, "multiply4"], [441, 26, 183, 22], [441, 27, 183, 23, "acc"], [441, 30, 183, 26], [441, 32, 183, 28, "translate"], [441, 41, 183, 37], [441, 42, 183, 38, "value"], [441, 47, 183, 43], [441, 49, 183, 45], [441, 50, 183, 46], [441, 52, 183, 48], [441, 53, 183, 49], [441, 54, 183, 50], [441, 55, 183, 51], [442, 8, 184, 4], [443, 8, 185, 4], [443, 12, 185, 8, "key"], [443, 15, 185, 11], [443, 20, 185, 16], [443, 31, 185, 27], [443, 33, 185, 29], [444, 10, 186, 6], [444, 16, 186, 12], [444, 17, 186, 13, "x"], [444, 18, 186, 14], [444, 20, 186, 16, "y"], [444, 21, 186, 17], [444, 23, 186, 19, "z"], [444, 24, 186, 20], [444, 27, 186, 23], [444, 28, 186, 24], [444, 29, 186, 25], [444, 32, 186, 28, "transform"], [444, 41, 186, 37], [444, 42, 186, 38, "key"], [444, 45, 186, 41], [444, 46, 186, 42], [445, 10, 187, 6], [445, 17, 187, 13, "multiply4"], [445, 26, 187, 22], [445, 27, 187, 23, "acc"], [445, 30, 187, 26], [445, 32, 187, 28, "translate"], [445, 41, 187, 37], [445, 42, 187, 38, "x"], [445, 43, 187, 39], [445, 45, 187, 41, "y"], [445, 46, 187, 42], [445, 48, 187, 44, "z"], [445, 49, 187, 45], [445, 50, 187, 46], [445, 51, 187, 47], [446, 8, 188, 4], [447, 8, 189, 4], [447, 12, 189, 8, "key"], [447, 15, 189, 11], [447, 20, 189, 16], [447, 32, 189, 28], [447, 34, 189, 30], [448, 10, 190, 6], [448, 16, 190, 12, "value"], [448, 21, 190, 17], [448, 24, 190, 20, "transform"], [448, 33, 190, 29], [448, 34, 190, 30, "key"], [448, 37, 190, 33], [448, 38, 190, 34], [449, 10, 191, 6], [449, 17, 191, 13, "multiply4"], [449, 26, 191, 22], [449, 27, 191, 23, "acc"], [449, 30, 191, 26], [449, 32, 191, 28, "translate"], [449, 41, 191, 37], [449, 42, 191, 38], [449, 43, 191, 39], [449, 45, 191, 41, "value"], [449, 50, 191, 46], [449, 52, 191, 48], [449, 53, 191, 49], [449, 54, 191, 50], [449, 55, 191, 51], [450, 8, 192, 4], [451, 8, 193, 4], [451, 12, 193, 8, "key"], [451, 15, 193, 11], [451, 20, 193, 16], [451, 32, 193, 28], [451, 34, 193, 30], [452, 10, 194, 6], [452, 16, 194, 12, "value"], [452, 21, 194, 17], [452, 24, 194, 20, "transform"], [452, 33, 194, 29], [452, 34, 194, 30, "key"], [452, 37, 194, 33], [452, 38, 194, 34], [453, 10, 195, 6], [453, 17, 195, 13, "multiply4"], [453, 26, 195, 22], [453, 27, 195, 23, "acc"], [453, 30, 195, 26], [453, 32, 195, 28, "translate"], [453, 41, 195, 37], [453, 42, 195, 38], [453, 43, 195, 39], [453, 45, 195, 41], [453, 46, 195, 42], [453, 48, 195, 44, "value"], [453, 53, 195, 49], [453, 54, 195, 50], [453, 55, 195, 51], [454, 8, 196, 4], [455, 8, 197, 4], [455, 12, 197, 8, "key"], [455, 15, 197, 11], [455, 20, 197, 16], [455, 27, 197, 23], [455, 29, 197, 25], [456, 10, 198, 6], [456, 16, 198, 12, "value"], [456, 21, 198, 17], [456, 24, 198, 20, "transform"], [456, 33, 198, 29], [456, 34, 198, 30, "key"], [456, 37, 198, 33], [456, 38, 198, 34], [457, 10, 199, 6], [457, 17, 199, 13, "multiply4"], [457, 26, 199, 22], [457, 27, 199, 23, "acc"], [457, 30, 199, 26], [457, 32, 199, 28, "scale"], [457, 37, 199, 33], [457, 38, 199, 34, "value"], [457, 43, 199, 39], [457, 45, 199, 41, "value"], [457, 50, 199, 46], [457, 52, 199, 48], [457, 53, 199, 49], [457, 54, 199, 50], [457, 55, 199, 51], [458, 8, 200, 4], [459, 8, 201, 4], [459, 12, 201, 8, "key"], [459, 15, 201, 11], [459, 20, 201, 16], [459, 28, 201, 24], [459, 30, 201, 26], [460, 10, 202, 6], [460, 16, 202, 12, "value"], [460, 21, 202, 17], [460, 24, 202, 20, "transform"], [460, 33, 202, 29], [460, 34, 202, 30, "key"], [460, 37, 202, 33], [460, 38, 202, 34], [461, 10, 203, 6], [461, 17, 203, 13, "multiply4"], [461, 26, 203, 22], [461, 27, 203, 23, "acc"], [461, 30, 203, 26], [461, 32, 203, 28, "scale"], [461, 37, 203, 33], [461, 38, 203, 34, "value"], [461, 43, 203, 39], [461, 45, 203, 41], [461, 46, 203, 42], [461, 48, 203, 44], [461, 49, 203, 45], [461, 50, 203, 46], [461, 51, 203, 47], [462, 8, 204, 4], [463, 8, 205, 4], [463, 12, 205, 8, "key"], [463, 15, 205, 11], [463, 20, 205, 16], [463, 28, 205, 24], [463, 30, 205, 26], [464, 10, 206, 6], [464, 16, 206, 12, "value"], [464, 21, 206, 17], [464, 24, 206, 20, "transform"], [464, 33, 206, 29], [464, 34, 206, 30, "key"], [464, 37, 206, 33], [464, 38, 206, 34], [465, 10, 207, 6], [465, 17, 207, 13, "multiply4"], [465, 26, 207, 22], [465, 27, 207, 23, "acc"], [465, 30, 207, 26], [465, 32, 207, 28, "scale"], [465, 37, 207, 33], [465, 38, 207, 34], [465, 39, 207, 35], [465, 41, 207, 37, "value"], [465, 46, 207, 42], [465, 48, 207, 44], [465, 49, 207, 45], [465, 50, 207, 46], [465, 51, 207, 47], [466, 8, 208, 4], [467, 8, 209, 4], [467, 12, 209, 8, "key"], [467, 15, 209, 11], [467, 20, 209, 16], [467, 27, 209, 23], [467, 29, 209, 25], [468, 10, 210, 6], [468, 16, 210, 12, "value"], [468, 21, 210, 17], [468, 24, 210, 20, "transform"], [468, 33, 210, 29], [468, 34, 210, 30, "key"], [468, 37, 210, 33], [468, 38, 210, 34], [469, 10, 211, 6], [469, 17, 211, 13, "multiply4"], [469, 26, 211, 22], [469, 27, 211, 23, "acc"], [469, 30, 211, 26], [469, 32, 211, 28, "skewX"], [469, 37, 211, 33], [469, 38, 211, 34, "value"], [469, 43, 211, 39], [469, 44, 211, 40], [469, 45, 211, 41], [470, 8, 212, 4], [471, 8, 213, 4], [471, 12, 213, 8, "key"], [471, 15, 213, 11], [471, 20, 213, 16], [471, 27, 213, 23], [471, 29, 213, 25], [472, 10, 214, 6], [472, 16, 214, 12, "value"], [472, 21, 214, 17], [472, 24, 214, 20, "transform"], [472, 33, 214, 29], [472, 34, 214, 30, "key"], [472, 37, 214, 33], [472, 38, 214, 34], [473, 10, 215, 6], [473, 17, 215, 13, "multiply4"], [473, 26, 215, 22], [473, 27, 215, 23, "acc"], [473, 30, 215, 26], [473, 32, 215, 28, "skewY"], [473, 37, 215, 33], [473, 38, 215, 34, "value"], [473, 43, 215, 39], [473, 44, 215, 40], [473, 45, 215, 41], [474, 8, 216, 4], [475, 8, 217, 4], [475, 12, 217, 8, "key"], [475, 15, 217, 11], [475, 20, 217, 16], [475, 29, 217, 25], [475, 31, 217, 27], [476, 10, 218, 6], [476, 16, 218, 12, "value"], [476, 21, 218, 17], [476, 24, 218, 20, "transform"], [476, 33, 218, 29], [476, 34, 218, 30, "key"], [476, 37, 218, 33], [476, 38, 218, 34], [477, 10, 219, 6], [477, 17, 219, 13, "multiply4"], [477, 26, 219, 22], [477, 27, 219, 23, "acc"], [477, 30, 219, 26], [477, 32, 219, 28, "rotate"], [477, 38, 219, 34], [477, 39, 219, 35], [477, 40, 219, 36], [477, 41, 219, 37], [477, 43, 219, 39], [477, 44, 219, 40], [477, 46, 219, 42], [477, 47, 219, 43], [477, 48, 219, 44], [477, 50, 219, 46, "value"], [477, 55, 219, 51], [477, 56, 219, 52], [477, 57, 219, 53], [478, 8, 220, 4], [479, 8, 221, 4], [479, 12, 221, 8, "key"], [479, 15, 221, 11], [479, 20, 221, 16], [479, 29, 221, 25], [479, 31, 221, 27], [480, 10, 222, 6], [480, 16, 222, 12, "value"], [480, 21, 222, 17], [480, 24, 222, 20, "transform"], [480, 33, 222, 29], [480, 34, 222, 30, "key"], [480, 37, 222, 33], [480, 38, 222, 34], [481, 10, 223, 6], [481, 17, 223, 13, "multiply4"], [481, 26, 223, 22], [481, 27, 223, 23, "acc"], [481, 30, 223, 26], [481, 32, 223, 28, "rotate"], [481, 38, 223, 34], [481, 39, 223, 35], [481, 40, 223, 36], [481, 41, 223, 37], [481, 43, 223, 39], [481, 44, 223, 40], [481, 46, 223, 42], [481, 47, 223, 43], [481, 48, 223, 44], [481, 50, 223, 46, "value"], [481, 55, 223, 51], [481, 56, 223, 52], [481, 57, 223, 53], [482, 8, 224, 4], [483, 8, 225, 4], [483, 12, 225, 8, "key"], [483, 15, 225, 11], [483, 20, 225, 16], [483, 33, 225, 29], [483, 35, 225, 31], [484, 10, 226, 6], [484, 16, 226, 12, "value"], [484, 21, 226, 17], [484, 24, 226, 20, "transform"], [484, 33, 226, 29], [484, 34, 226, 30, "key"], [484, 37, 226, 33], [484, 38, 226, 34], [485, 10, 227, 6], [485, 17, 227, 13, "multiply4"], [485, 26, 227, 22], [485, 27, 227, 23, "acc"], [485, 30, 227, 26], [485, 32, 227, 28, "perspective"], [485, 43, 227, 39], [485, 44, 227, 40, "value"], [485, 49, 227, 45], [485, 50, 227, 46], [485, 51, 227, 47], [486, 8, 228, 4], [487, 8, 229, 4], [487, 12, 229, 8, "key"], [487, 15, 229, 11], [487, 20, 229, 16], [487, 28, 229, 24], [487, 32, 229, 28, "key"], [487, 35, 229, 31], [487, 40, 229, 36], [487, 49, 229, 45], [487, 51, 229, 47], [488, 10, 230, 6], [488, 16, 230, 12, "value"], [488, 21, 230, 17], [488, 24, 230, 20, "transform"], [488, 33, 230, 29], [488, 34, 230, 30, "key"], [488, 37, 230, 33], [488, 38, 230, 34], [489, 10, 231, 6], [489, 17, 231, 13, "multiply4"], [489, 26, 231, 22], [489, 27, 231, 23, "acc"], [489, 30, 231, 26], [489, 32, 231, 28, "rotate"], [489, 38, 231, 34], [489, 39, 231, 35], [489, 40, 231, 36], [489, 41, 231, 37], [489, 43, 231, 39], [489, 44, 231, 40], [489, 46, 231, 42], [489, 47, 231, 43], [489, 48, 231, 44], [489, 50, 231, 46, "value"], [489, 55, 231, 51], [489, 56, 231, 52], [489, 57, 231, 53], [490, 8, 232, 4], [491, 8, 233, 4], [491, 12, 233, 8, "key"], [491, 15, 233, 11], [491, 20, 233, 16], [491, 28, 233, 24], [491, 30, 233, 26], [492, 10, 234, 6], [492, 16, 234, 12, "value"], [492, 21, 234, 17], [492, 24, 234, 20, "transform"], [492, 33, 234, 29], [492, 34, 234, 30, "key"], [492, 37, 234, 33], [492, 38, 234, 34], [493, 10, 235, 6], [493, 17, 235, 13, "multiply4"], [493, 26, 235, 22], [493, 27, 235, 23, "acc"], [493, 30, 235, 26], [493, 32, 235, 28, "value"], [493, 37, 235, 33], [493, 38, 235, 34], [494, 8, 236, 4], [495, 8, 237, 4], [495, 15, 237, 11, "exhaustiveCheck"], [495, 30, 237, 26], [495, 31, 237, 27, "key"], [495, 34, 237, 30], [495, 35, 237, 31], [496, 6, 238, 2], [496, 7, 238, 3], [496, 9, 238, 5, "Matrix4"], [496, 16, 238, 12], [496, 17, 238, 13], [496, 18, 238, 14], [496, 19, 238, 15], [497, 4, 239, 0], [497, 5, 239, 1], [498, 4, 239, 1, "shopify_Matrix4Js20"], [498, 23, 239, 1], [498, 24, 239, 1, "__closure"], [498, 33, 239, 1], [499, 6, 239, 1, "multiply4"], [499, 15, 239, 1], [500, 6, 239, 1, "translate"], [500, 15, 239, 1], [501, 6, 239, 1, "scale"], [501, 11, 239, 1], [502, 6, 239, 1, "skewX"], [502, 11, 239, 1], [503, 6, 239, 1, "skewY"], [503, 11, 239, 1], [504, 6, 239, 1, "rotate"], [504, 12, 239, 1], [505, 6, 239, 1, "perspective"], [505, 17, 239, 1], [506, 6, 239, 1, "exhaustiveCheck"], [506, 21, 239, 1], [507, 6, 239, 1, "Matrix4"], [508, 4, 239, 1], [509, 4, 239, 1, "shopify_Matrix4Js20"], [509, 23, 239, 1], [509, 24, 239, 1, "__workletHash"], [509, 37, 239, 1], [510, 4, 239, 1, "shopify_Matrix4Js20"], [510, 23, 239, 1], [510, 24, 239, 1, "__initData"], [510, 34, 239, 1], [510, 37, 239, 1, "_worklet_2225508254275_init_data"], [510, 69, 239, 1], [511, 4, 239, 1, "shopify_Matrix4Js20"], [511, 23, 239, 1], [511, 24, 239, 1, "__stackDetails"], [511, 38, 239, 1], [511, 41, 239, 1, "_e"], [511, 43, 239, 1], [512, 4, 239, 1], [512, 11, 239, 1, "shopify_Matrix4Js20"], [512, 30, 239, 1], [513, 2, 239, 1], [513, 3, 175, 34], [513, 5, 239, 1], [515, 2, 241, 0], [516, 0, 242, 0], [517, 0, 243, 0], [518, 2, 241, 0], [518, 8, 241, 0, "_worklet_8340212603925_init_data"], [518, 40, 241, 0], [519, 4, 241, 0, "code"], [519, 8, 241, 0], [520, 4, 241, 0, "location"], [520, 12, 241, 0], [521, 4, 241, 0, "sourceMap"], [521, 13, 241, 0], [522, 4, 241, 0, "version"], [522, 11, 241, 0], [523, 2, 241, 0], [524, 2, 244, 7], [524, 8, 244, 13, "convertToColumnMajor"], [524, 28, 244, 33], [524, 31, 244, 33, "exports"], [524, 38, 244, 33], [524, 39, 244, 33, "convertToColumnMajor"], [524, 59, 244, 33], [524, 62, 244, 36], [525, 4, 244, 36], [525, 10, 244, 36, "_e"], [525, 12, 244, 36], [525, 20, 244, 36, "global"], [525, 26, 244, 36], [525, 27, 244, 36, "Error"], [525, 32, 244, 36], [526, 4, 244, 36], [526, 10, 244, 36, "shopify_Matrix4Js21"], [526, 29, 244, 36], [526, 41, 244, 36, "shopify_Matrix4Js21"], [526, 42, 244, 36, "rowMajorMatrix"], [526, 56, 244, 50], [526, 58, 244, 54], [527, 6, 247, 2], [527, 12, 247, 8, "colMajorMatrix"], [527, 26, 247, 22], [527, 29, 247, 25], [527, 33, 247, 29, "Array"], [527, 38, 247, 34], [527, 39, 247, 35], [527, 41, 247, 37], [527, 42, 247, 38], [528, 6, 248, 2], [528, 12, 248, 8, "size"], [528, 16, 248, 12], [528, 19, 248, 15], [528, 20, 248, 16], [529, 6, 249, 2], [529, 11, 249, 7], [529, 15, 249, 11, "row"], [529, 18, 249, 14], [529, 21, 249, 17], [529, 22, 249, 18], [529, 24, 249, 20, "row"], [529, 27, 249, 23], [529, 30, 249, 26, "size"], [529, 34, 249, 30], [529, 36, 249, 32, "row"], [529, 39, 249, 35], [529, 41, 249, 37], [529, 43, 249, 39], [530, 8, 250, 4], [530, 13, 250, 9], [530, 17, 250, 13, "col"], [530, 20, 250, 16], [530, 23, 250, 19], [530, 24, 250, 20], [530, 26, 250, 22, "col"], [530, 29, 250, 25], [530, 32, 250, 28, "size"], [530, 36, 250, 32], [530, 38, 250, 34, "col"], [530, 41, 250, 37], [530, 43, 250, 39], [530, 45, 250, 41], [531, 10, 251, 6, "colMajorMatrix"], [531, 24, 251, 20], [531, 25, 251, 21, "col"], [531, 28, 251, 24], [531, 31, 251, 27, "size"], [531, 35, 251, 31], [531, 38, 251, 34, "row"], [531, 41, 251, 37], [531, 42, 251, 38], [531, 45, 251, 41, "rowMajorMatrix"], [531, 59, 251, 55], [531, 60, 251, 56, "row"], [531, 63, 251, 59], [531, 66, 251, 62, "size"], [531, 70, 251, 66], [531, 73, 251, 69, "col"], [531, 76, 251, 72], [531, 77, 251, 73], [532, 8, 252, 4], [533, 6, 253, 2], [534, 6, 254, 2], [534, 13, 254, 9, "colMajorMatrix"], [534, 27, 254, 23], [535, 4, 255, 0], [535, 5, 255, 1], [536, 4, 255, 1, "shopify_Matrix4Js21"], [536, 23, 255, 1], [536, 24, 255, 1, "__closure"], [536, 33, 255, 1], [537, 4, 255, 1, "shopify_Matrix4Js21"], [537, 23, 255, 1], [537, 24, 255, 1, "__workletHash"], [537, 37, 255, 1], [538, 4, 255, 1, "shopify_Matrix4Js21"], [538, 23, 255, 1], [538, 24, 255, 1, "__initData"], [538, 34, 255, 1], [538, 37, 255, 1, "_worklet_8340212603925_init_data"], [538, 69, 255, 1], [539, 4, 255, 1, "shopify_Matrix4Js21"], [539, 23, 255, 1], [539, 24, 255, 1, "__stackDetails"], [539, 38, 255, 1], [539, 41, 255, 1, "_e"], [539, 43, 255, 1], [540, 4, 255, 1], [540, 11, 255, 1, "shopify_Matrix4Js21"], [540, 30, 255, 1], [541, 2, 255, 1], [541, 3, 244, 36], [541, 5, 255, 1], [543, 2, 257, 0], [544, 0, 258, 0], [545, 0, 259, 0], [546, 2, 257, 0], [546, 8, 257, 0, "_worklet_6037180980026_init_data"], [546, 40, 257, 0], [547, 4, 257, 0, "code"], [547, 8, 257, 0], [548, 4, 257, 0, "location"], [548, 12, 257, 0], [549, 4, 257, 0, "sourceMap"], [549, 13, 257, 0], [550, 4, 257, 0, "version"], [550, 11, 257, 0], [551, 2, 257, 0], [552, 2, 260, 7], [552, 8, 260, 13, "convertToAffineMatrix"], [552, 29, 260, 34], [552, 32, 260, 34, "exports"], [552, 39, 260, 34], [552, 40, 260, 34, "convertToAffineMatrix"], [552, 61, 260, 34], [552, 64, 260, 37], [553, 4, 260, 37], [553, 10, 260, 37, "_e"], [553, 12, 260, 37], [553, 20, 260, 37, "global"], [553, 26, 260, 37], [553, 27, 260, 37, "Error"], [553, 32, 260, 37], [554, 4, 260, 37], [554, 10, 260, 37, "shopify_Matrix4Js22"], [554, 29, 260, 37], [554, 41, 260, 37, "shopify_Matrix4Js22"], [554, 42, 260, 37, "m4"], [554, 44, 260, 39], [554, 46, 260, 43], [555, 6, 263, 2], [556, 6, 264, 2], [556, 12, 264, 8, "a"], [556, 13, 264, 9], [556, 16, 264, 12, "m4"], [556, 18, 264, 14], [556, 19, 264, 15], [556, 20, 264, 16], [556, 21, 264, 17], [556, 22, 264, 18], [556, 23, 264, 19], [557, 6, 265, 2], [557, 12, 265, 8, "b"], [557, 13, 265, 9], [557, 16, 265, 12, "m4"], [557, 18, 265, 14], [557, 19, 265, 15], [557, 20, 265, 16], [557, 21, 265, 17], [557, 22, 265, 18], [557, 23, 265, 19], [558, 6, 266, 2], [558, 12, 266, 8, "c"], [558, 13, 266, 9], [558, 16, 266, 12, "m4"], [558, 18, 266, 14], [558, 19, 266, 15], [558, 20, 266, 16], [558, 21, 266, 17], [558, 22, 266, 18], [558, 23, 266, 19], [559, 6, 267, 2], [559, 12, 267, 8, "d"], [559, 13, 267, 9], [559, 16, 267, 12, "m4"], [559, 18, 267, 14], [559, 19, 267, 15], [559, 20, 267, 16], [559, 21, 267, 17], [559, 22, 267, 18], [559, 23, 267, 19], [560, 6, 268, 2], [560, 12, 268, 8, "tx"], [560, 14, 268, 10], [560, 17, 268, 13, "m4"], [560, 19, 268, 15], [560, 20, 268, 16], [560, 22, 268, 18], [560, 23, 268, 19], [560, 24, 268, 20], [560, 25, 268, 21], [561, 6, 269, 2], [561, 12, 269, 8, "ty"], [561, 14, 269, 10], [561, 17, 269, 13, "m4"], [561, 19, 269, 15], [561, 20, 269, 16], [561, 22, 269, 18], [561, 23, 269, 19], [561, 24, 269, 20], [561, 25, 269, 21], [563, 6, 271, 2], [564, 6, 272, 2], [564, 13, 272, 9], [564, 14, 272, 10, "a"], [564, 15, 272, 11], [564, 17, 272, 13, "b"], [564, 18, 272, 14], [564, 20, 272, 16, "c"], [564, 21, 272, 17], [564, 23, 272, 19, "d"], [564, 24, 272, 20], [564, 26, 272, 22, "tx"], [564, 28, 272, 24], [564, 30, 272, 26, "ty"], [564, 32, 272, 28], [564, 33, 272, 29], [565, 4, 273, 0], [565, 5, 273, 1], [566, 4, 273, 1, "shopify_Matrix4Js22"], [566, 23, 273, 1], [566, 24, 273, 1, "__closure"], [566, 33, 273, 1], [567, 4, 273, 1, "shopify_Matrix4Js22"], [567, 23, 273, 1], [567, 24, 273, 1, "__workletHash"], [567, 37, 273, 1], [568, 4, 273, 1, "shopify_Matrix4Js22"], [568, 23, 273, 1], [568, 24, 273, 1, "__initData"], [568, 34, 273, 1], [568, 37, 273, 1, "_worklet_6037180980026_init_data"], [568, 69, 273, 1], [569, 4, 273, 1, "shopify_Matrix4Js22"], [569, 23, 273, 1], [569, 24, 273, 1, "__stackDetails"], [569, 38, 273, 1], [569, 41, 273, 1, "_e"], [569, 43, 273, 1], [570, 4, 273, 1], [570, 11, 273, 1, "shopify_Matrix4Js22"], [570, 30, 273, 1], [571, 2, 273, 1], [571, 3, 260, 37], [571, 5, 273, 1], [573, 2, 275, 0], [574, 0, 276, 0], [575, 0, 277, 0], [576, 0, 278, 0], [577, 2, 275, 0], [577, 8, 275, 0, "_worklet_6884116785644_init_data"], [577, 40, 275, 0], [578, 4, 275, 0, "code"], [578, 8, 275, 0], [579, 4, 275, 0, "location"], [579, 12, 275, 0], [580, 4, 275, 0, "sourceMap"], [580, 13, 275, 0], [581, 4, 275, 0, "version"], [581, 11, 275, 0], [582, 2, 275, 0], [583, 2, 279, 0], [583, 8, 279, 6, "det3x3"], [583, 14, 279, 12], [583, 17, 279, 15], [584, 4, 279, 15], [584, 10, 279, 15, "_e"], [584, 12, 279, 15], [584, 20, 279, 15, "global"], [584, 26, 279, 15], [584, 27, 279, 15, "Error"], [584, 32, 279, 15], [585, 4, 279, 15], [585, 10, 279, 15, "shopify_Matrix4Js23"], [585, 29, 279, 15], [585, 41, 279, 15, "shopify_Matrix4Js23"], [585, 42, 279, 16, "a00"], [585, 45, 279, 19], [585, 47, 279, 21, "a01"], [585, 50, 279, 24], [585, 52, 279, 26, "a02"], [585, 55, 279, 29], [585, 57, 279, 31, "a10"], [585, 60, 279, 34], [585, 62, 279, 36, "a11"], [585, 65, 279, 39], [585, 67, 279, 41, "a12"], [585, 70, 279, 44], [585, 72, 279, 46, "a20"], [585, 75, 279, 49], [585, 77, 279, 51, "a21"], [585, 80, 279, 54], [585, 82, 279, 56, "a22"], [585, 85, 279, 59], [585, 87, 279, 64], [586, 6, 282, 2], [586, 13, 282, 9, "a00"], [586, 16, 282, 12], [586, 20, 282, 16, "a11"], [586, 23, 282, 19], [586, 26, 282, 22, "a22"], [586, 29, 282, 25], [586, 32, 282, 28, "a12"], [586, 35, 282, 31], [586, 38, 282, 34, "a21"], [586, 41, 282, 37], [586, 42, 282, 38], [586, 45, 282, 41, "a01"], [586, 48, 282, 44], [586, 52, 282, 48, "a12"], [586, 55, 282, 51], [586, 58, 282, 54, "a20"], [586, 61, 282, 57], [586, 64, 282, 60, "a10"], [586, 67, 282, 63], [586, 70, 282, 66, "a22"], [586, 73, 282, 69], [586, 74, 282, 70], [586, 77, 282, 73, "a02"], [586, 80, 282, 76], [586, 84, 282, 80, "a10"], [586, 87, 282, 83], [586, 90, 282, 86, "a21"], [586, 93, 282, 89], [586, 96, 282, 92, "a11"], [586, 99, 282, 95], [586, 102, 282, 98, "a20"], [586, 105, 282, 101], [586, 106, 282, 102], [587, 4, 283, 0], [587, 5, 283, 1], [588, 4, 283, 1, "shopify_Matrix4Js23"], [588, 23, 283, 1], [588, 24, 283, 1, "__closure"], [588, 33, 283, 1], [589, 4, 283, 1, "shopify_Matrix4Js23"], [589, 23, 283, 1], [589, 24, 283, 1, "__workletHash"], [589, 37, 283, 1], [590, 4, 283, 1, "shopify_Matrix4Js23"], [590, 23, 283, 1], [590, 24, 283, 1, "__initData"], [590, 34, 283, 1], [590, 37, 283, 1, "_worklet_6884116785644_init_data"], [590, 69, 283, 1], [591, 4, 283, 1, "shopify_Matrix4Js23"], [591, 23, 283, 1], [591, 24, 283, 1, "__stackDetails"], [591, 38, 283, 1], [591, 41, 283, 1, "_e"], [591, 43, 283, 1], [592, 4, 283, 1], [592, 11, 283, 1, "shopify_Matrix4Js23"], [592, 30, 283, 1], [593, 2, 283, 1], [593, 3, 279, 15], [593, 5, 283, 1], [595, 2, 285, 0], [596, 0, 286, 0], [597, 0, 287, 0], [598, 0, 288, 0], [599, 0, 289, 0], [600, 2, 285, 0], [600, 8, 285, 0, "_worklet_11697263420356_init_data"], [600, 41, 285, 0], [601, 4, 285, 0, "code"], [601, 8, 285, 0], [602, 4, 285, 0, "location"], [602, 12, 285, 0], [603, 4, 285, 0, "sourceMap"], [603, 13, 285, 0], [604, 4, 285, 0, "version"], [604, 11, 285, 0], [605, 2, 285, 0], [606, 2, 290, 7], [606, 8, 290, 13, "invert4"], [606, 15, 290, 20], [606, 18, 290, 20, "exports"], [606, 25, 290, 20], [606, 26, 290, 20, "invert4"], [606, 33, 290, 20], [606, 36, 290, 23], [607, 4, 290, 23], [607, 10, 290, 23, "_e"], [607, 12, 290, 23], [607, 20, 290, 23, "global"], [607, 26, 290, 23], [607, 27, 290, 23, "Error"], [607, 32, 290, 23], [608, 4, 290, 23], [608, 10, 290, 23, "shopify_Matrix4Js24"], [608, 29, 290, 23], [608, 41, 290, 23, "shopify_Matrix4Js24"], [608, 42, 290, 23, "m"], [608, 43, 290, 24], [608, 45, 290, 28], [609, 6, 293, 2], [609, 12, 293, 8, "a00"], [609, 15, 293, 11], [609, 18, 293, 14, "m"], [609, 19, 293, 15], [609, 20, 293, 16], [609, 21, 293, 17], [609, 22, 293, 18], [610, 8, 294, 4, "a01"], [610, 11, 294, 7], [610, 14, 294, 10, "m"], [610, 15, 294, 11], [610, 16, 294, 12], [610, 17, 294, 13], [610, 18, 294, 14], [611, 8, 295, 4, "a02"], [611, 11, 295, 7], [611, 14, 295, 10, "m"], [611, 15, 295, 11], [611, 16, 295, 12], [611, 17, 295, 13], [611, 18, 295, 14], [612, 8, 296, 4, "a03"], [612, 11, 296, 7], [612, 14, 296, 10, "m"], [612, 15, 296, 11], [612, 16, 296, 12], [612, 17, 296, 13], [612, 18, 296, 14], [613, 6, 297, 2], [613, 12, 297, 8, "a10"], [613, 15, 297, 11], [613, 18, 297, 14, "m"], [613, 19, 297, 15], [613, 20, 297, 16], [613, 21, 297, 17], [613, 22, 297, 18], [614, 8, 298, 4, "a11"], [614, 11, 298, 7], [614, 14, 298, 10, "m"], [614, 15, 298, 11], [614, 16, 298, 12], [614, 17, 298, 13], [614, 18, 298, 14], [615, 8, 299, 4, "a12"], [615, 11, 299, 7], [615, 14, 299, 10, "m"], [615, 15, 299, 11], [615, 16, 299, 12], [615, 17, 299, 13], [615, 18, 299, 14], [616, 8, 300, 4, "a13"], [616, 11, 300, 7], [616, 14, 300, 10, "m"], [616, 15, 300, 11], [616, 16, 300, 12], [616, 17, 300, 13], [616, 18, 300, 14], [617, 6, 301, 2], [617, 12, 301, 8, "a20"], [617, 15, 301, 11], [617, 18, 301, 14, "m"], [617, 19, 301, 15], [617, 20, 301, 16], [617, 21, 301, 17], [617, 22, 301, 18], [618, 8, 302, 4, "a21"], [618, 11, 302, 7], [618, 14, 302, 10, "m"], [618, 15, 302, 11], [618, 16, 302, 12], [618, 17, 302, 13], [618, 18, 302, 14], [619, 8, 303, 4, "a22"], [619, 11, 303, 7], [619, 14, 303, 10, "m"], [619, 15, 303, 11], [619, 16, 303, 12], [619, 18, 303, 14], [619, 19, 303, 15], [620, 8, 304, 4, "a23"], [620, 11, 304, 7], [620, 14, 304, 10, "m"], [620, 15, 304, 11], [620, 16, 304, 12], [620, 18, 304, 14], [620, 19, 304, 15], [621, 6, 305, 2], [621, 12, 305, 8, "a30"], [621, 15, 305, 11], [621, 18, 305, 14, "m"], [621, 19, 305, 15], [621, 20, 305, 16], [621, 22, 305, 18], [621, 23, 305, 19], [622, 8, 306, 4, "a31"], [622, 11, 306, 7], [622, 14, 306, 10, "m"], [622, 15, 306, 11], [622, 16, 306, 12], [622, 18, 306, 14], [622, 19, 306, 15], [623, 8, 307, 4, "a32"], [623, 11, 307, 7], [623, 14, 307, 10, "m"], [623, 15, 307, 11], [623, 16, 307, 12], [623, 18, 307, 14], [623, 19, 307, 15], [624, 8, 308, 4, "a33"], [624, 11, 308, 7], [624, 14, 308, 10, "m"], [624, 15, 308, 11], [624, 16, 308, 12], [624, 18, 308, 14], [624, 19, 308, 15], [626, 6, 310, 2], [627, 6, 311, 2], [627, 12, 311, 8, "b00"], [627, 15, 311, 11], [627, 18, 311, 14, "det3x3"], [627, 24, 311, 20], [627, 25, 311, 21, "a11"], [627, 28, 311, 24], [627, 30, 311, 26, "a12"], [627, 33, 311, 29], [627, 35, 311, 31, "a13"], [627, 38, 311, 34], [627, 40, 311, 36, "a21"], [627, 43, 311, 39], [627, 45, 311, 41, "a22"], [627, 48, 311, 44], [627, 50, 311, 46, "a23"], [627, 53, 311, 49], [627, 55, 311, 51, "a31"], [627, 58, 311, 54], [627, 60, 311, 56, "a32"], [627, 63, 311, 59], [627, 65, 311, 61, "a33"], [627, 68, 311, 64], [627, 69, 311, 65], [628, 6, 312, 2], [628, 12, 312, 8, "b01"], [628, 15, 312, 11], [628, 18, 312, 14], [628, 19, 312, 15, "det3x3"], [628, 25, 312, 21], [628, 26, 312, 22, "a10"], [628, 29, 312, 25], [628, 31, 312, 27, "a12"], [628, 34, 312, 30], [628, 36, 312, 32, "a13"], [628, 39, 312, 35], [628, 41, 312, 37, "a20"], [628, 44, 312, 40], [628, 46, 312, 42, "a22"], [628, 49, 312, 45], [628, 51, 312, 47, "a23"], [628, 54, 312, 50], [628, 56, 312, 52, "a30"], [628, 59, 312, 55], [628, 61, 312, 57, "a32"], [628, 64, 312, 60], [628, 66, 312, 62, "a33"], [628, 69, 312, 65], [628, 70, 312, 66], [629, 6, 313, 2], [629, 12, 313, 8, "b02"], [629, 15, 313, 11], [629, 18, 313, 14, "det3x3"], [629, 24, 313, 20], [629, 25, 313, 21, "a10"], [629, 28, 313, 24], [629, 30, 313, 26, "a11"], [629, 33, 313, 29], [629, 35, 313, 31, "a13"], [629, 38, 313, 34], [629, 40, 313, 36, "a20"], [629, 43, 313, 39], [629, 45, 313, 41, "a21"], [629, 48, 313, 44], [629, 50, 313, 46, "a23"], [629, 53, 313, 49], [629, 55, 313, 51, "a30"], [629, 58, 313, 54], [629, 60, 313, 56, "a31"], [629, 63, 313, 59], [629, 65, 313, 61, "a33"], [629, 68, 313, 64], [629, 69, 313, 65], [630, 6, 314, 2], [630, 12, 314, 8, "b03"], [630, 15, 314, 11], [630, 18, 314, 14], [630, 19, 314, 15, "det3x3"], [630, 25, 314, 21], [630, 26, 314, 22, "a10"], [630, 29, 314, 25], [630, 31, 314, 27, "a11"], [630, 34, 314, 30], [630, 36, 314, 32, "a12"], [630, 39, 314, 35], [630, 41, 314, 37, "a20"], [630, 44, 314, 40], [630, 46, 314, 42, "a21"], [630, 49, 314, 45], [630, 51, 314, 47, "a22"], [630, 54, 314, 50], [630, 56, 314, 52, "a30"], [630, 59, 314, 55], [630, 61, 314, 57, "a31"], [630, 64, 314, 60], [630, 66, 314, 62, "a32"], [630, 69, 314, 65], [630, 70, 314, 66], [631, 6, 315, 2], [631, 12, 315, 8, "b10"], [631, 15, 315, 11], [631, 18, 315, 14], [631, 19, 315, 15, "det3x3"], [631, 25, 315, 21], [631, 26, 315, 22, "a01"], [631, 29, 315, 25], [631, 31, 315, 27, "a02"], [631, 34, 315, 30], [631, 36, 315, 32, "a03"], [631, 39, 315, 35], [631, 41, 315, 37, "a21"], [631, 44, 315, 40], [631, 46, 315, 42, "a22"], [631, 49, 315, 45], [631, 51, 315, 47, "a23"], [631, 54, 315, 50], [631, 56, 315, 52, "a31"], [631, 59, 315, 55], [631, 61, 315, 57, "a32"], [631, 64, 315, 60], [631, 66, 315, 62, "a33"], [631, 69, 315, 65], [631, 70, 315, 66], [632, 6, 316, 2], [632, 12, 316, 8, "b11"], [632, 15, 316, 11], [632, 18, 316, 14, "det3x3"], [632, 24, 316, 20], [632, 25, 316, 21, "a00"], [632, 28, 316, 24], [632, 30, 316, 26, "a02"], [632, 33, 316, 29], [632, 35, 316, 31, "a03"], [632, 38, 316, 34], [632, 40, 316, 36, "a20"], [632, 43, 316, 39], [632, 45, 316, 41, "a22"], [632, 48, 316, 44], [632, 50, 316, 46, "a23"], [632, 53, 316, 49], [632, 55, 316, 51, "a30"], [632, 58, 316, 54], [632, 60, 316, 56, "a32"], [632, 63, 316, 59], [632, 65, 316, 61, "a33"], [632, 68, 316, 64], [632, 69, 316, 65], [633, 6, 317, 2], [633, 12, 317, 8, "b12"], [633, 15, 317, 11], [633, 18, 317, 14], [633, 19, 317, 15, "det3x3"], [633, 25, 317, 21], [633, 26, 317, 22, "a00"], [633, 29, 317, 25], [633, 31, 317, 27, "a01"], [633, 34, 317, 30], [633, 36, 317, 32, "a03"], [633, 39, 317, 35], [633, 41, 317, 37, "a20"], [633, 44, 317, 40], [633, 46, 317, 42, "a21"], [633, 49, 317, 45], [633, 51, 317, 47, "a23"], [633, 54, 317, 50], [633, 56, 317, 52, "a30"], [633, 59, 317, 55], [633, 61, 317, 57, "a31"], [633, 64, 317, 60], [633, 66, 317, 62, "a33"], [633, 69, 317, 65], [633, 70, 317, 66], [634, 6, 318, 2], [634, 12, 318, 8, "b13"], [634, 15, 318, 11], [634, 18, 318, 14, "det3x3"], [634, 24, 318, 20], [634, 25, 318, 21, "a00"], [634, 28, 318, 24], [634, 30, 318, 26, "a01"], [634, 33, 318, 29], [634, 35, 318, 31, "a02"], [634, 38, 318, 34], [634, 40, 318, 36, "a20"], [634, 43, 318, 39], [634, 45, 318, 41, "a21"], [634, 48, 318, 44], [634, 50, 318, 46, "a22"], [634, 53, 318, 49], [634, 55, 318, 51, "a30"], [634, 58, 318, 54], [634, 60, 318, 56, "a31"], [634, 63, 318, 59], [634, 65, 318, 61, "a32"], [634, 68, 318, 64], [634, 69, 318, 65], [635, 6, 319, 2], [635, 12, 319, 8, "b20"], [635, 15, 319, 11], [635, 18, 319, 14, "det3x3"], [635, 24, 319, 20], [635, 25, 319, 21, "a01"], [635, 28, 319, 24], [635, 30, 319, 26, "a02"], [635, 33, 319, 29], [635, 35, 319, 31, "a03"], [635, 38, 319, 34], [635, 40, 319, 36, "a11"], [635, 43, 319, 39], [635, 45, 319, 41, "a12"], [635, 48, 319, 44], [635, 50, 319, 46, "a13"], [635, 53, 319, 49], [635, 55, 319, 51, "a31"], [635, 58, 319, 54], [635, 60, 319, 56, "a32"], [635, 63, 319, 59], [635, 65, 319, 61, "a33"], [635, 68, 319, 64], [635, 69, 319, 65], [636, 6, 320, 2], [636, 12, 320, 8, "b21"], [636, 15, 320, 11], [636, 18, 320, 14], [636, 19, 320, 15, "det3x3"], [636, 25, 320, 21], [636, 26, 320, 22, "a00"], [636, 29, 320, 25], [636, 31, 320, 27, "a02"], [636, 34, 320, 30], [636, 36, 320, 32, "a03"], [636, 39, 320, 35], [636, 41, 320, 37, "a10"], [636, 44, 320, 40], [636, 46, 320, 42, "a12"], [636, 49, 320, 45], [636, 51, 320, 47, "a13"], [636, 54, 320, 50], [636, 56, 320, 52, "a30"], [636, 59, 320, 55], [636, 61, 320, 57, "a32"], [636, 64, 320, 60], [636, 66, 320, 62, "a33"], [636, 69, 320, 65], [636, 70, 320, 66], [637, 6, 321, 2], [637, 12, 321, 8, "b22"], [637, 15, 321, 11], [637, 18, 321, 14, "det3x3"], [637, 24, 321, 20], [637, 25, 321, 21, "a00"], [637, 28, 321, 24], [637, 30, 321, 26, "a01"], [637, 33, 321, 29], [637, 35, 321, 31, "a03"], [637, 38, 321, 34], [637, 40, 321, 36, "a10"], [637, 43, 321, 39], [637, 45, 321, 41, "a11"], [637, 48, 321, 44], [637, 50, 321, 46, "a13"], [637, 53, 321, 49], [637, 55, 321, 51, "a30"], [637, 58, 321, 54], [637, 60, 321, 56, "a31"], [637, 63, 321, 59], [637, 65, 321, 61, "a33"], [637, 68, 321, 64], [637, 69, 321, 65], [638, 6, 322, 2], [638, 12, 322, 8, "b23"], [638, 15, 322, 11], [638, 18, 322, 14], [638, 19, 322, 15, "det3x3"], [638, 25, 322, 21], [638, 26, 322, 22, "a00"], [638, 29, 322, 25], [638, 31, 322, 27, "a01"], [638, 34, 322, 30], [638, 36, 322, 32, "a02"], [638, 39, 322, 35], [638, 41, 322, 37, "a10"], [638, 44, 322, 40], [638, 46, 322, 42, "a11"], [638, 49, 322, 45], [638, 51, 322, 47, "a12"], [638, 54, 322, 50], [638, 56, 322, 52, "a30"], [638, 59, 322, 55], [638, 61, 322, 57, "a31"], [638, 64, 322, 60], [638, 66, 322, 62, "a32"], [638, 69, 322, 65], [638, 70, 322, 66], [639, 6, 323, 2], [639, 12, 323, 8, "b30"], [639, 15, 323, 11], [639, 18, 323, 14], [639, 19, 323, 15, "det3x3"], [639, 25, 323, 21], [639, 26, 323, 22, "a01"], [639, 29, 323, 25], [639, 31, 323, 27, "a02"], [639, 34, 323, 30], [639, 36, 323, 32, "a03"], [639, 39, 323, 35], [639, 41, 323, 37, "a11"], [639, 44, 323, 40], [639, 46, 323, 42, "a12"], [639, 49, 323, 45], [639, 51, 323, 47, "a13"], [639, 54, 323, 50], [639, 56, 323, 52, "a21"], [639, 59, 323, 55], [639, 61, 323, 57, "a22"], [639, 64, 323, 60], [639, 66, 323, 62, "a23"], [639, 69, 323, 65], [639, 70, 323, 66], [640, 6, 324, 2], [640, 12, 324, 8, "b31"], [640, 15, 324, 11], [640, 18, 324, 14, "det3x3"], [640, 24, 324, 20], [640, 25, 324, 21, "a00"], [640, 28, 324, 24], [640, 30, 324, 26, "a02"], [640, 33, 324, 29], [640, 35, 324, 31, "a03"], [640, 38, 324, 34], [640, 40, 324, 36, "a10"], [640, 43, 324, 39], [640, 45, 324, 41, "a12"], [640, 48, 324, 44], [640, 50, 324, 46, "a13"], [640, 53, 324, 49], [640, 55, 324, 51, "a20"], [640, 58, 324, 54], [640, 60, 324, 56, "a22"], [640, 63, 324, 59], [640, 65, 324, 61, "a23"], [640, 68, 324, 64], [640, 69, 324, 65], [641, 6, 325, 2], [641, 12, 325, 8, "b32"], [641, 15, 325, 11], [641, 18, 325, 14], [641, 19, 325, 15, "det3x3"], [641, 25, 325, 21], [641, 26, 325, 22, "a00"], [641, 29, 325, 25], [641, 31, 325, 27, "a01"], [641, 34, 325, 30], [641, 36, 325, 32, "a03"], [641, 39, 325, 35], [641, 41, 325, 37, "a10"], [641, 44, 325, 40], [641, 46, 325, 42, "a11"], [641, 49, 325, 45], [641, 51, 325, 47, "a13"], [641, 54, 325, 50], [641, 56, 325, 52, "a20"], [641, 59, 325, 55], [641, 61, 325, 57, "a21"], [641, 64, 325, 60], [641, 66, 325, 62, "a23"], [641, 69, 325, 65], [641, 70, 325, 66], [642, 6, 326, 2], [642, 12, 326, 8, "b33"], [642, 15, 326, 11], [642, 18, 326, 14, "det3x3"], [642, 24, 326, 20], [642, 25, 326, 21, "a00"], [642, 28, 326, 24], [642, 30, 326, 26, "a01"], [642, 33, 326, 29], [642, 35, 326, 31, "a02"], [642, 38, 326, 34], [642, 40, 326, 36, "a10"], [642, 43, 326, 39], [642, 45, 326, 41, "a11"], [642, 48, 326, 44], [642, 50, 326, 46, "a12"], [642, 53, 326, 49], [642, 55, 326, 51, "a20"], [642, 58, 326, 54], [642, 60, 326, 56, "a21"], [642, 63, 326, 59], [642, 65, 326, 61, "a22"], [642, 68, 326, 64], [642, 69, 326, 65], [644, 6, 328, 2], [645, 6, 329, 2], [645, 12, 329, 8, "det"], [645, 15, 329, 11], [645, 18, 329, 14, "a00"], [645, 21, 329, 17], [645, 24, 329, 20, "b00"], [645, 27, 329, 23], [645, 30, 329, 26, "a01"], [645, 33, 329, 29], [645, 36, 329, 32, "b01"], [645, 39, 329, 35], [645, 42, 329, 38, "a02"], [645, 45, 329, 41], [645, 48, 329, 44, "b02"], [645, 51, 329, 47], [645, 54, 329, 50, "a03"], [645, 57, 329, 53], [645, 60, 329, 56, "b03"], [645, 63, 329, 59], [647, 6, 331, 2], [648, 6, 332, 2], [648, 10, 332, 6, "Math"], [648, 14, 332, 10], [648, 15, 332, 11, "abs"], [648, 18, 332, 14], [648, 19, 332, 15, "det"], [648, 22, 332, 18], [648, 23, 332, 19], [648, 26, 332, 22], [648, 30, 332, 26], [648, 32, 332, 28], [649, 8, 333, 4], [650, 8, 334, 4], [650, 15, 334, 11, "Matrix4"], [650, 22, 334, 18], [650, 23, 334, 19], [650, 24, 334, 20], [651, 6, 335, 2], [652, 6, 336, 2], [652, 12, 336, 8, "invDet"], [652, 18, 336, 14], [652, 21, 336, 17], [652, 24, 336, 20], [652, 27, 336, 23, "det"], [652, 30, 336, 26], [654, 6, 338, 2], [655, 6, 339, 2], [655, 13, 339, 9], [655, 14, 339, 10, "b00"], [655, 17, 339, 13], [655, 20, 339, 16, "invDet"], [655, 26, 339, 22], [655, 28, 339, 24, "b10"], [655, 31, 339, 27], [655, 34, 339, 30, "invDet"], [655, 40, 339, 36], [655, 42, 339, 38, "b20"], [655, 45, 339, 41], [655, 48, 339, 44, "invDet"], [655, 54, 339, 50], [655, 56, 339, 52, "b30"], [655, 59, 339, 55], [655, 62, 339, 58, "invDet"], [655, 68, 339, 64], [655, 70, 339, 66, "b01"], [655, 73, 339, 69], [655, 76, 339, 72, "invDet"], [655, 82, 339, 78], [655, 84, 339, 80, "b11"], [655, 87, 339, 83], [655, 90, 339, 86, "invDet"], [655, 96, 339, 92], [655, 98, 339, 94, "b21"], [655, 101, 339, 97], [655, 104, 339, 100, "invDet"], [655, 110, 339, 106], [655, 112, 339, 108, "b31"], [655, 115, 339, 111], [655, 118, 339, 114, "invDet"], [655, 124, 339, 120], [655, 126, 339, 122, "b02"], [655, 129, 339, 125], [655, 132, 339, 128, "invDet"], [655, 138, 339, 134], [655, 140, 339, 136, "b12"], [655, 143, 339, 139], [655, 146, 339, 142, "invDet"], [655, 152, 339, 148], [655, 154, 339, 150, "b22"], [655, 157, 339, 153], [655, 160, 339, 156, "invDet"], [655, 166, 339, 162], [655, 168, 339, 164, "b32"], [655, 171, 339, 167], [655, 174, 339, 170, "invDet"], [655, 180, 339, 176], [655, 182, 339, 178, "b03"], [655, 185, 339, 181], [655, 188, 339, 184, "invDet"], [655, 194, 339, 190], [655, 196, 339, 192, "b13"], [655, 199, 339, 195], [655, 202, 339, 198, "invDet"], [655, 208, 339, 204], [655, 210, 339, 206, "b23"], [655, 213, 339, 209], [655, 216, 339, 212, "invDet"], [655, 222, 339, 218], [655, 224, 339, 220, "b33"], [655, 227, 339, 223], [655, 230, 339, 226, "invDet"], [655, 236, 339, 232], [655, 237, 339, 233], [656, 4, 340, 0], [656, 5, 340, 1], [657, 4, 340, 1, "shopify_Matrix4Js24"], [657, 23, 340, 1], [657, 24, 340, 1, "__closure"], [657, 33, 340, 1], [658, 6, 340, 1, "det3x3"], [658, 12, 340, 1], [659, 6, 340, 1, "Matrix4"], [660, 4, 340, 1], [661, 4, 340, 1, "shopify_Matrix4Js24"], [661, 23, 340, 1], [661, 24, 340, 1, "__workletHash"], [661, 37, 340, 1], [662, 4, 340, 1, "shopify_Matrix4Js24"], [662, 23, 340, 1], [662, 24, 340, 1, "__initData"], [662, 34, 340, 1], [662, 37, 340, 1, "_worklet_11697263420356_init_data"], [662, 70, 340, 1], [663, 4, 340, 1, "shopify_Matrix4Js24"], [663, 23, 340, 1], [663, 24, 340, 1, "__stackDetails"], [663, 38, 340, 1], [663, 41, 340, 1, "_e"], [663, 43, 340, 1], [664, 4, 340, 1], [664, 11, 340, 1, "shopify_Matrix4Js24"], [664, 30, 340, 1], [665, 2, 340, 1], [665, 3, 290, 23], [665, 5, 340, 1], [666, 0, 340, 2], [666, 3]], "functionMap": {"names": ["<global>", "exhaustiveCheck", "Matrix4", "translate", "perspective", "normalizeVec", "rotatedUnitSinCos", "matrixVecMul4", "mapPoint3d", "multiply4", "skewY", "skewX", "toMatrix3", "rotate", "pivot", "scale", "rotateAxis", "rotateZ", "rotateX", "rotateY", "processTransform3d", "transforms.reduce$argument_0", "convertToColumnMajor", "convertToAffineMatrix", "det3x3", "invert4"], "mappings": "AAA,wBC;CDI;uBEK;CFI;yBGK;CHI;2BIK;CJI;qBKC;CLU;0BMC;CNU;6BOK;CPI;0BQK;CRK;yBSK;CTU;cUC;CVI;cWC;CXI;yBYK;CZI;eaC;CbI;qBcK;CdI;qBeK;CfQ;mBgBC;ChBQ;uBiBK;CjBI;uBkBK;ClBI;uBmBK;CnBI;kCoBK;2BCG;GD4D;CpBC;oCsBK;CtBW;qCuBK;CvBa;ewBM;CxBI;uByBO;CzBkD"}}, "type": "js/module"}]}