{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.scrollTo = scrollTo;\n  function scrollTo(animatedRef, x, y, animated) {\n    const element = animatedRef();\n\n    // This prevents crashes if ref has not been set yet\n    if (element !== -1) {\n      // By ScrollView we mean any scrollable component\n      const scrollView = element;\n      scrollView?.scrollTo({\n        x,\n        y,\n        animated\n      });\n    }\n  }\n});", "lineCount": 22, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "scrollTo"], [7, 18, 1, 13], [7, 21, 1, 13, "scrollTo"], [7, 29, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "scrollTo"], [8, 19, 3, 24, "scrollTo"], [8, 20, 3, 25, "animatedRef"], [8, 31, 3, 36], [8, 33, 3, 38, "x"], [8, 34, 3, 39], [8, 36, 3, 41, "y"], [8, 37, 3, 42], [8, 39, 3, 44, "animated"], [8, 47, 3, 52], [8, 49, 3, 54], [9, 4, 4, 2], [9, 10, 4, 8, "element"], [9, 17, 4, 15], [9, 20, 4, 18, "animatedRef"], [9, 31, 4, 29], [9, 32, 4, 30], [9, 33, 4, 31], [11, 4, 6, 2], [12, 4, 7, 2], [12, 8, 7, 6, "element"], [12, 15, 7, 13], [12, 20, 7, 18], [12, 21, 7, 19], [12, 22, 7, 20], [12, 24, 7, 22], [13, 6, 8, 4], [14, 6, 9, 4], [14, 12, 9, 10, "scrollView"], [14, 22, 9, 20], [14, 25, 9, 23, "element"], [14, 32, 9, 30], [15, 6, 10, 4, "scrollView"], [15, 16, 10, 14], [15, 18, 10, 16, "scrollTo"], [15, 26, 10, 24], [15, 27, 10, 25], [16, 8, 11, 6, "x"], [16, 9, 11, 7], [17, 8, 12, 6, "y"], [17, 9, 12, 7], [18, 8, 13, 6, "animated"], [19, 6, 14, 4], [19, 7, 14, 5], [19, 8, 14, 6], [20, 4, 15, 2], [21, 2, 16, 0], [22, 0, 16, 1], [22, 3]], "functionMap": {"names": ["<global>", "scrollTo"], "mappings": "AAA;OCE;CDa"}}, "type": "js/module"}]}