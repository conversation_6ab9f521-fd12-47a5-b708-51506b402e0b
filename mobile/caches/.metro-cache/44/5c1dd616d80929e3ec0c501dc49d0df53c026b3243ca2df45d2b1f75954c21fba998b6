{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "./createMultiStyleIconSet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 41}, "end": {"line": 2, "column": 64, "index": 105}}], "key": "HozWuSEpaSlotSgGuE+YIlwZNA0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FA5Style = void 0;\n  exports.createFA5iconSet = createFA5iconSet;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/Platform\"));\n  var _createMultiStyleIconSet = _interopRequireDefault(require(_dependencyMap[2], \"./createMultiStyleIconSet\"));\n  const FA5Style = exports.FA5Style = {\n    regular: 'regular',\n    light: 'light',\n    solid: 'solid',\n    brand: 'brand'\n  };\n  function createFA5iconSet(glyphMap, metadata = {}, fonts, pro = false) {\n    const metadataKeys = Object.keys(metadata);\n    const fontFamily = `FontAwesome5${pro ? 'Pro' : 'Free'}`;\n    function fallbackFamily(glyph) {\n      for (let i = 0; i < metadataKeys.length; i += 1) {\n        const family = metadataKeys[i];\n        if (metadata[family].indexOf(glyph) !== -1) {\n          return family === 'brands' ? 'brand' : family;\n        }\n      }\n      return 'regular';\n    }\n    function glyphValidator(glyph, style) {\n      const family = style === 'brand' ? 'brands' : style;\n      if (metadataKeys.indexOf(family) === -1) return false;\n      return metadata[family].indexOf(glyph) !== -1;\n    }\n    function createFontAwesomeStyle(styleName, fontWeight, family = fontFamily) {\n      const fontFile = fonts[styleName];\n      return {\n        fontFamily: `${family}-${styleName}`,\n        fontFile,\n        fontStyle: _Platform.default.select({\n          ios: {\n            fontWeight\n          },\n          default: {}\n        }),\n        glyphMap\n      };\n    }\n    const brandIcons = createFontAwesomeStyle('Brand', '400');\n    const lightIcons = createFontAwesomeStyle('Light', '100');\n    const regularIcons = createFontAwesomeStyle('Regular', '400');\n    const solidIcons = createFontAwesomeStyle('Solid', '700');\n    const Icon = (0, _createMultiStyleIconSet.default)({\n      brand: brandIcons,\n      light: lightIcons,\n      regular: regularIcons,\n      solid: solidIcons\n    }, {\n      defaultStyle: 'regular',\n      fallbackFamily,\n      glyphValidator\n    });\n    return Icon;\n  }\n});", "lineCount": 63, "map": [[9, 2, 2, 0], [9, 6, 2, 0, "_createMultiStyleIconSet"], [9, 30, 2, 0], [9, 33, 2, 0, "_interopRequireDefault"], [9, 55, 2, 0], [9, 56, 2, 0, "require"], [9, 63, 2, 0], [9, 64, 2, 0, "_dependencyMap"], [9, 78, 2, 0], [10, 2, 3, 7], [10, 8, 3, 13, "FA5Style"], [10, 16, 3, 21], [10, 19, 3, 21, "exports"], [10, 26, 3, 21], [10, 27, 3, 21, "FA5Style"], [10, 35, 3, 21], [10, 38, 3, 24], [11, 4, 4, 4, "regular"], [11, 11, 4, 11], [11, 13, 4, 13], [11, 22, 4, 22], [12, 4, 5, 4, "light"], [12, 9, 5, 9], [12, 11, 5, 11], [12, 18, 5, 18], [13, 4, 6, 4, "solid"], [13, 9, 6, 9], [13, 11, 6, 11], [13, 18, 6, 18], [14, 4, 7, 4, "brand"], [14, 9, 7, 9], [14, 11, 7, 11], [15, 2, 8, 0], [15, 3, 8, 1], [16, 2, 9, 7], [16, 11, 9, 16, "createFA5iconSet"], [16, 27, 9, 32, "createFA5iconSet"], [16, 28, 9, 33, "glyphMap"], [16, 36, 9, 41], [16, 38, 9, 43, "metadata"], [16, 46, 9, 51], [16, 49, 9, 54], [16, 50, 9, 55], [16, 51, 9, 56], [16, 53, 9, 58, "fonts"], [16, 58, 9, 63], [16, 60, 9, 65, "pro"], [16, 63, 9, 68], [16, 66, 9, 71], [16, 71, 9, 76], [16, 73, 9, 78], [17, 4, 10, 4], [17, 10, 10, 10, "metadataKeys"], [17, 22, 10, 22], [17, 25, 10, 25, "Object"], [17, 31, 10, 31], [17, 32, 10, 32, "keys"], [17, 36, 10, 36], [17, 37, 10, 37, "metadata"], [17, 45, 10, 45], [17, 46, 10, 46], [18, 4, 11, 4], [18, 10, 11, 10, "fontFamily"], [18, 20, 11, 20], [18, 23, 11, 23], [18, 38, 11, 38, "pro"], [18, 41, 11, 41], [18, 44, 11, 44], [18, 49, 11, 49], [18, 52, 11, 52], [18, 58, 11, 58], [18, 60, 11, 60], [19, 4, 12, 4], [19, 13, 12, 13, "fallbackFamily"], [19, 27, 12, 27, "fallbackFamily"], [19, 28, 12, 28, "glyph"], [19, 33, 12, 33], [19, 35, 12, 35], [20, 6, 13, 8], [20, 11, 13, 13], [20, 15, 13, 17, "i"], [20, 16, 13, 18], [20, 19, 13, 21], [20, 20, 13, 22], [20, 22, 13, 24, "i"], [20, 23, 13, 25], [20, 26, 13, 28, "metadataKeys"], [20, 38, 13, 40], [20, 39, 13, 41, "length"], [20, 45, 13, 47], [20, 47, 13, 49, "i"], [20, 48, 13, 50], [20, 52, 13, 54], [20, 53, 13, 55], [20, 55, 13, 57], [21, 8, 14, 12], [21, 14, 14, 18, "family"], [21, 20, 14, 24], [21, 23, 14, 27, "metadataKeys"], [21, 35, 14, 39], [21, 36, 14, 40, "i"], [21, 37, 14, 41], [21, 38, 14, 42], [22, 8, 15, 12], [22, 12, 15, 16, "metadata"], [22, 20, 15, 24], [22, 21, 15, 25, "family"], [22, 27, 15, 31], [22, 28, 15, 32], [22, 29, 15, 33, "indexOf"], [22, 36, 15, 40], [22, 37, 15, 41, "glyph"], [22, 42, 15, 46], [22, 43, 15, 47], [22, 48, 15, 52], [22, 49, 15, 53], [22, 50, 15, 54], [22, 52, 15, 56], [23, 10, 16, 16], [23, 17, 16, 23, "family"], [23, 23, 16, 29], [23, 28, 16, 34], [23, 36, 16, 42], [23, 39, 16, 45], [23, 46, 16, 52], [23, 49, 16, 55, "family"], [23, 55, 16, 61], [24, 8, 17, 12], [25, 6, 18, 8], [26, 6, 19, 8], [26, 13, 19, 15], [26, 22, 19, 24], [27, 4, 20, 4], [28, 4, 21, 4], [28, 13, 21, 13, "glyphValidator"], [28, 27, 21, 27, "glyphValidator"], [28, 28, 21, 28, "glyph"], [28, 33, 21, 33], [28, 35, 21, 35, "style"], [28, 40, 21, 40], [28, 42, 21, 42], [29, 6, 22, 8], [29, 12, 22, 14, "family"], [29, 18, 22, 20], [29, 21, 22, 23, "style"], [29, 26, 22, 28], [29, 31, 22, 33], [29, 38, 22, 40], [29, 41, 22, 43], [29, 49, 22, 51], [29, 52, 22, 54, "style"], [29, 57, 22, 59], [30, 6, 23, 8], [30, 10, 23, 12, "metadataKeys"], [30, 22, 23, 24], [30, 23, 23, 25, "indexOf"], [30, 30, 23, 32], [30, 31, 23, 33, "family"], [30, 37, 23, 39], [30, 38, 23, 40], [30, 43, 23, 45], [30, 44, 23, 46], [30, 45, 23, 47], [30, 47, 24, 12], [30, 54, 24, 19], [30, 59, 24, 24], [31, 6, 25, 8], [31, 13, 25, 15, "metadata"], [31, 21, 25, 23], [31, 22, 25, 24, "family"], [31, 28, 25, 30], [31, 29, 25, 31], [31, 30, 25, 32, "indexOf"], [31, 37, 25, 39], [31, 38, 25, 40, "glyph"], [31, 43, 25, 45], [31, 44, 25, 46], [31, 49, 25, 51], [31, 50, 25, 52], [31, 51, 25, 53], [32, 4, 26, 4], [33, 4, 27, 4], [33, 13, 27, 13, "createFontAwesomeStyle"], [33, 35, 27, 35, "createFontAwesomeStyle"], [33, 36, 27, 36, "styleName"], [33, 45, 27, 45], [33, 47, 27, 47, "fontWeight"], [33, 57, 27, 57], [33, 59, 27, 59, "family"], [33, 65, 27, 65], [33, 68, 27, 68, "fontFamily"], [33, 78, 27, 78], [33, 80, 27, 80], [34, 6, 28, 8], [34, 12, 28, 14, "fontFile"], [34, 20, 28, 22], [34, 23, 28, 25, "fonts"], [34, 28, 28, 30], [34, 29, 28, 31, "styleName"], [34, 38, 28, 40], [34, 39, 28, 41], [35, 6, 29, 8], [35, 13, 29, 15], [36, 8, 30, 12, "fontFamily"], [36, 18, 30, 22], [36, 20, 30, 24], [36, 23, 30, 27, "family"], [36, 29, 30, 33], [36, 33, 30, 37, "styleName"], [36, 42, 30, 46], [36, 44, 30, 48], [37, 8, 31, 12, "fontFile"], [37, 16, 31, 20], [38, 8, 32, 12, "fontStyle"], [38, 17, 32, 21], [38, 19, 32, 23, "Platform"], [38, 36, 32, 31], [38, 37, 32, 32, "select"], [38, 43, 32, 38], [38, 44, 32, 39], [39, 10, 33, 16, "ios"], [39, 13, 33, 19], [39, 15, 33, 21], [40, 12, 34, 20, "fontWeight"], [41, 10, 35, 16], [41, 11, 35, 17], [42, 10, 36, 16, "default"], [42, 17, 36, 23], [42, 19, 36, 25], [42, 20, 36, 26], [43, 8, 37, 12], [43, 9, 37, 13], [43, 10, 37, 14], [44, 8, 38, 12, "glyphMap"], [45, 6, 39, 8], [45, 7, 39, 9], [46, 4, 40, 4], [47, 4, 41, 4], [47, 10, 41, 10, "brandIcons"], [47, 20, 41, 20], [47, 23, 41, 23, "createFontAwesomeStyle"], [47, 45, 41, 45], [47, 46, 41, 46], [47, 53, 41, 53], [47, 55, 41, 55], [47, 60, 41, 60], [47, 61, 41, 61], [48, 4, 42, 4], [48, 10, 42, 10, "lightIcons"], [48, 20, 42, 20], [48, 23, 42, 23, "createFontAwesomeStyle"], [48, 45, 42, 45], [48, 46, 42, 46], [48, 53, 42, 53], [48, 55, 42, 55], [48, 60, 42, 60], [48, 61, 42, 61], [49, 4, 43, 4], [49, 10, 43, 10, "regularIcons"], [49, 22, 43, 22], [49, 25, 43, 25, "createFontAwesomeStyle"], [49, 47, 43, 47], [49, 48, 43, 48], [49, 57, 43, 57], [49, 59, 43, 59], [49, 64, 43, 64], [49, 65, 43, 65], [50, 4, 44, 4], [50, 10, 44, 10, "solidIcons"], [50, 20, 44, 20], [50, 23, 44, 23, "createFontAwesomeStyle"], [50, 45, 44, 45], [50, 46, 44, 46], [50, 53, 44, 53], [50, 55, 44, 55], [50, 60, 44, 60], [50, 61, 44, 61], [51, 4, 45, 4], [51, 10, 45, 10, "Icon"], [51, 14, 45, 14], [51, 17, 45, 17], [51, 21, 45, 17, "createMultiStyleIconSet"], [51, 53, 45, 40], [51, 55, 45, 41], [52, 6, 46, 8, "brand"], [52, 11, 46, 13], [52, 13, 46, 15, "brandIcons"], [52, 23, 46, 25], [53, 6, 47, 8, "light"], [53, 11, 47, 13], [53, 13, 47, 15, "lightIcons"], [53, 23, 47, 25], [54, 6, 48, 8, "regular"], [54, 13, 48, 15], [54, 15, 48, 17, "regularIcons"], [54, 27, 48, 29], [55, 6, 49, 8, "solid"], [55, 11, 49, 13], [55, 13, 49, 15, "solidIcons"], [56, 4, 50, 4], [56, 5, 50, 5], [56, 7, 50, 7], [57, 6, 51, 8, "defaultStyle"], [57, 18, 51, 20], [57, 20, 51, 22], [57, 29, 51, 31], [58, 6, 52, 8, "fallbackFamily"], [58, 20, 52, 22], [59, 6, 53, 8, "glyphValidator"], [60, 4, 54, 4], [60, 5, 54, 5], [60, 6, 54, 6], [61, 4, 55, 4], [61, 11, 55, 11, "Icon"], [61, 15, 55, 15], [62, 2, 56, 0], [63, 0, 56, 1], [63, 3]], "functionMap": {"names": ["<global>", "createFA5iconSet", "fallbackFamily", "glyphValidator", "createFontAwesomeStyle"], "mappings": "AAA;OCQ;ICG;KDQ;IEC;KFK;IGC;KHa;CDgB"}}, "type": "js/module"}]}