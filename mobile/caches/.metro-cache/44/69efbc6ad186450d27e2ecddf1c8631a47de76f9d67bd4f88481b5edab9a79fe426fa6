{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "./PerformanceEntry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 52}}], "key": "brFNAt3Zh5rA+ZZUGgMallCwpmE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PerformanceMeasure = exports.PerformanceMark = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[7], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _PerformanceEntry3 = require(_dependencyMap[8], \"./PerformanceEntry\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var _detail = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"detail\");\n  var PerformanceMark = exports.PerformanceMark = /*#__PURE__*/function (_PerformanceEntry) {\n    function PerformanceMark(markName, markOptions) {\n      var _this;\n      (0, _classCallCheck2.default)(this, PerformanceMark);\n      _this = _callSuper(this, PerformanceMark, [{\n        name: markName,\n        entryType: 'mark',\n        startTime: markOptions?.startTime ?? performance.now(),\n        duration: 0\n      }]);\n      Object.defineProperty(_this, _detail, {\n        writable: true,\n        value: void 0\n      });\n      if (markOptions) {\n        (0, _classPrivateFieldLooseBase2.default)(_this, _detail)[_detail] = markOptions.detail;\n      }\n      return _this;\n    }\n    (0, _inherits2.default)(PerformanceMark, _PerformanceEntry);\n    return (0, _createClass2.default)(PerformanceMark, [{\n      key: \"detail\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _detail)[_detail];\n      }\n    }]);\n  }(_PerformanceEntry3.PerformanceEntry);\n  var _detail2 = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"detail\");\n  var PerformanceMeasure = exports.PerformanceMeasure = /*#__PURE__*/function (_PerformanceEntry2) {\n    function PerformanceMeasure(measureName, measureOptions) {\n      var _this2;\n      (0, _classCallCheck2.default)(this, PerformanceMeasure);\n      _this2 = _callSuper(this, PerformanceMeasure, [{\n        name: measureName,\n        entryType: 'measure',\n        startTime: measureOptions.startTime,\n        duration: measureOptions.duration\n      }]);\n      Object.defineProperty(_this2, _detail2, {\n        writable: true,\n        value: void 0\n      });\n      if (measureOptions) {\n        (0, _classPrivateFieldLooseBase2.default)(_this2, _detail2)[_detail2] = measureOptions.detail;\n      }\n      return _this2;\n    }\n    (0, _inherits2.default)(PerformanceMeasure, _PerformanceEntry2);\n    return (0, _createClass2.default)(PerformanceMeasure, [{\n      key: \"detail\",\n      get: function () {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _detail2)[_detail2];\n      }\n    }]);\n  }(_PerformanceEntry3.PerformanceEntry);\n});", "lineCount": 73, "map": [[14, 2, 15, 0], [14, 6, 15, 0, "_PerformanceEntry3"], [14, 24, 15, 0], [14, 27, 15, 0, "require"], [14, 34, 15, 0], [14, 35, 15, 0, "_dependencyMap"], [14, 49, 15, 0], [15, 2, 15, 52], [15, 11, 15, 52, "_callSuper"], [15, 22, 15, 52, "t"], [15, 23, 15, 52], [15, 25, 15, 52, "o"], [15, 26, 15, 52], [15, 28, 15, 52, "e"], [15, 29, 15, 52], [15, 40, 15, 52, "o"], [15, 41, 15, 52], [15, 48, 15, 52, "_getPrototypeOf2"], [15, 64, 15, 52], [15, 65, 15, 52, "default"], [15, 72, 15, 52], [15, 74, 15, 52, "o"], [15, 75, 15, 52], [15, 82, 15, 52, "_possibleConstructorReturn2"], [15, 109, 15, 52], [15, 110, 15, 52, "default"], [15, 117, 15, 52], [15, 119, 15, 52, "t"], [15, 120, 15, 52], [15, 122, 15, 52, "_isNativeReflectConstruct"], [15, 147, 15, 52], [15, 152, 15, 52, "Reflect"], [15, 159, 15, 52], [15, 160, 15, 52, "construct"], [15, 169, 15, 52], [15, 170, 15, 52, "o"], [15, 171, 15, 52], [15, 173, 15, 52, "e"], [15, 174, 15, 52], [15, 186, 15, 52, "_getPrototypeOf2"], [15, 202, 15, 52], [15, 203, 15, 52, "default"], [15, 210, 15, 52], [15, 212, 15, 52, "t"], [15, 213, 15, 52], [15, 215, 15, 52, "constructor"], [15, 226, 15, 52], [15, 230, 15, 52, "o"], [15, 231, 15, 52], [15, 232, 15, 52, "apply"], [15, 237, 15, 52], [15, 238, 15, 52, "t"], [15, 239, 15, 52], [15, 241, 15, 52, "e"], [15, 242, 15, 52], [16, 2, 15, 52], [16, 11, 15, 52, "_isNativeReflectConstruct"], [16, 37, 15, 52], [16, 51, 15, 52, "t"], [16, 52, 15, 52], [16, 56, 15, 52, "Boolean"], [16, 63, 15, 52], [16, 64, 15, 52, "prototype"], [16, 73, 15, 52], [16, 74, 15, 52, "valueOf"], [16, 81, 15, 52], [16, 82, 15, 52, "call"], [16, 86, 15, 52], [16, 87, 15, 52, "Reflect"], [16, 94, 15, 52], [16, 95, 15, 52, "construct"], [16, 104, 15, 52], [16, 105, 15, 52, "Boolean"], [16, 112, 15, 52], [16, 145, 15, 52, "t"], [16, 146, 15, 52], [16, 159, 15, 52, "_isNativeReflectConstruct"], [16, 184, 15, 52], [16, 196, 15, 52, "_isNativeReflectConstruct"], [16, 197, 15, 52], [16, 210, 15, 52, "t"], [16, 211, 15, 52], [17, 2, 15, 52], [17, 6, 15, 52, "_detail"], [17, 13, 15, 52], [17, 33, 15, 52, "_classPrivateFieldLooseKey2"], [17, 60, 15, 52], [17, 61, 15, 52, "default"], [17, 68, 15, 52], [18, 2, 15, 52], [18, 6, 32, 13, "PerformanceMark"], [18, 21, 32, 28], [18, 24, 32, 28, "exports"], [18, 31, 32, 28], [18, 32, 32, 28, "PerformanceMark"], [18, 47, 32, 28], [18, 73, 32, 28, "_PerformanceEntry"], [18, 90, 32, 28], [19, 4, 35, 2], [19, 13, 35, 2, "PerformanceMark"], [19, 29, 35, 14, "<PERSON><PERSON><PERSON>"], [19, 37, 35, 30], [19, 39, 35, 32, "markOptions"], [19, 50, 35, 68], [19, 52, 35, 70], [20, 6, 35, 70], [20, 10, 35, 70, "_this"], [20, 15, 35, 70], [21, 6, 35, 70], [21, 10, 35, 70, "_classCallCheck2"], [21, 26, 35, 70], [21, 27, 35, 70, "default"], [21, 34, 35, 70], [21, 42, 35, 70, "PerformanceMark"], [21, 57, 35, 70], [22, 6, 36, 4, "_this"], [22, 11, 36, 4], [22, 14, 36, 4, "_callSuper"], [22, 24, 36, 4], [22, 31, 36, 4, "PerformanceMark"], [22, 46, 36, 4], [22, 49, 36, 10], [23, 8, 37, 6, "name"], [23, 12, 37, 10], [23, 14, 37, 12, "<PERSON><PERSON><PERSON>"], [23, 22, 37, 20], [24, 8, 38, 6, "entryType"], [24, 17, 38, 15], [24, 19, 38, 17], [24, 25, 38, 23], [25, 8, 39, 6, "startTime"], [25, 17, 39, 15], [25, 19, 39, 17, "markOptions"], [25, 30, 39, 28], [25, 32, 39, 30, "startTime"], [25, 41, 39, 39], [25, 45, 39, 43, "performance"], [25, 56, 39, 54], [25, 57, 39, 55, "now"], [25, 60, 39, 58], [25, 61, 39, 59], [25, 62, 39, 60], [26, 8, 40, 6, "duration"], [26, 16, 40, 14], [26, 18, 40, 16], [27, 6, 41, 4], [27, 7, 41, 5], [28, 6, 41, 7, "Object"], [28, 12, 41, 7], [28, 13, 41, 7, "defineProperty"], [28, 27, 41, 7], [28, 28, 41, 7, "_this"], [28, 33, 41, 7], [28, 35, 41, 7, "_detail"], [28, 42, 41, 7], [29, 8, 41, 7, "writable"], [29, 16, 41, 7], [30, 8, 41, 7, "value"], [30, 13, 41, 7], [31, 6, 41, 7], [32, 6, 43, 4], [32, 10, 43, 8, "markOptions"], [32, 21, 43, 19], [32, 23, 43, 21], [33, 8, 44, 6], [33, 12, 44, 6, "_classPrivateFieldLooseBase2"], [33, 40, 44, 6], [33, 41, 44, 6, "default"], [33, 48, 44, 6], [33, 50, 44, 6, "_this"], [33, 55, 44, 6], [33, 57, 44, 6, "_detail"], [33, 64, 44, 6], [33, 66, 44, 6, "_detail"], [33, 73, 44, 6], [33, 77, 44, 21, "markOptions"], [33, 88, 44, 32], [33, 89, 44, 33, "detail"], [33, 95, 44, 39], [34, 6, 45, 4], [35, 6, 45, 5], [35, 13, 45, 5, "_this"], [35, 18, 45, 5], [36, 4, 46, 2], [37, 4, 46, 3], [37, 8, 46, 3, "_inherits2"], [37, 18, 46, 3], [37, 19, 46, 3, "default"], [37, 26, 46, 3], [37, 28, 46, 3, "PerformanceMark"], [37, 43, 46, 3], [37, 45, 46, 3, "_PerformanceEntry"], [37, 62, 46, 3], [38, 4, 46, 3], [38, 15, 46, 3, "_createClass2"], [38, 28, 46, 3], [38, 29, 46, 3, "default"], [38, 36, 46, 3], [38, 38, 46, 3, "PerformanceMark"], [38, 53, 46, 3], [39, 6, 46, 3, "key"], [39, 9, 46, 3], [40, 6, 46, 3, "get"], [40, 9, 46, 3], [40, 11, 48, 2], [40, 20, 48, 2, "get"], [40, 21, 48, 2], [40, 23, 48, 27], [41, 8, 49, 4], [41, 19, 49, 4, "_classPrivateFieldLooseBase2"], [41, 47, 49, 4], [41, 48, 49, 4, "default"], [41, 55, 49, 4], [41, 57, 49, 11], [41, 61, 49, 15], [41, 63, 49, 15, "_detail"], [41, 70, 49, 15], [41, 72, 49, 15, "_detail"], [41, 79, 49, 15], [42, 6, 50, 2], [43, 4, 50, 3], [44, 2, 50, 3], [44, 4, 32, 37, "PerformanceEntry"], [44, 39, 32, 53], [45, 2, 32, 53], [45, 6, 32, 53, "_detail2"], [45, 14, 32, 53], [45, 34, 32, 53, "_classPrivateFieldLooseKey2"], [45, 61, 32, 53], [45, 62, 32, 53, "default"], [45, 69, 32, 53], [46, 2, 32, 53], [46, 6, 53, 13, "PerformanceMeasure"], [46, 24, 53, 31], [46, 27, 53, 31, "exports"], [46, 34, 53, 31], [46, 35, 53, 31, "PerformanceMeasure"], [46, 53, 53, 31], [46, 79, 53, 31, "_PerformanceEntry2"], [46, 97, 53, 31], [47, 4, 56, 2], [47, 13, 56, 2, "PerformanceMeasure"], [47, 32, 56, 14, "measureName"], [47, 43, 56, 33], [47, 45, 56, 35, "measureOptions"], [47, 59, 56, 73], [47, 61, 56, 75], [48, 6, 56, 75], [48, 10, 56, 75, "_this2"], [48, 16, 56, 75], [49, 6, 56, 75], [49, 10, 56, 75, "_classCallCheck2"], [49, 26, 56, 75], [49, 27, 56, 75, "default"], [49, 34, 56, 75], [49, 42, 56, 75, "PerformanceMeasure"], [49, 60, 56, 75], [50, 6, 57, 4, "_this2"], [50, 12, 57, 4], [50, 15, 57, 4, "_callSuper"], [50, 25, 57, 4], [50, 32, 57, 4, "PerformanceMeasure"], [50, 50, 57, 4], [50, 53, 57, 10], [51, 8, 58, 6, "name"], [51, 12, 58, 10], [51, 14, 58, 12, "measureName"], [51, 25, 58, 23], [52, 8, 59, 6, "entryType"], [52, 17, 59, 15], [52, 19, 59, 17], [52, 28, 59, 26], [53, 8, 60, 6, "startTime"], [53, 17, 60, 15], [53, 19, 60, 17, "measureOptions"], [53, 33, 60, 31], [53, 34, 60, 32, "startTime"], [53, 43, 60, 41], [54, 8, 61, 6, "duration"], [54, 16, 61, 14], [54, 18, 61, 16, "measureOptions"], [54, 32, 61, 30], [54, 33, 61, 31, "duration"], [55, 6, 62, 4], [55, 7, 62, 5], [56, 6, 62, 7, "Object"], [56, 12, 62, 7], [56, 13, 62, 7, "defineProperty"], [56, 27, 62, 7], [56, 28, 62, 7, "_this2"], [56, 34, 62, 7], [56, 36, 62, 7, "_detail2"], [56, 44, 62, 7], [57, 8, 62, 7, "writable"], [57, 16, 62, 7], [58, 8, 62, 7, "value"], [58, 13, 62, 7], [59, 6, 62, 7], [60, 6, 64, 4], [60, 10, 64, 8, "measureOptions"], [60, 24, 64, 22], [60, 26, 64, 24], [61, 8, 65, 6], [61, 12, 65, 6, "_classPrivateFieldLooseBase2"], [61, 40, 65, 6], [61, 41, 65, 6, "default"], [61, 48, 65, 6], [61, 50, 65, 6, "_this2"], [61, 56, 65, 6], [61, 58, 65, 6, "_detail2"], [61, 66, 65, 6], [61, 68, 65, 6, "_detail2"], [61, 76, 65, 6], [61, 80, 65, 21, "measureOptions"], [61, 94, 65, 35], [61, 95, 65, 36, "detail"], [61, 101, 65, 42], [62, 6, 66, 4], [63, 6, 66, 5], [63, 13, 66, 5, "_this2"], [63, 19, 66, 5], [64, 4, 67, 2], [65, 4, 67, 3], [65, 8, 67, 3, "_inherits2"], [65, 18, 67, 3], [65, 19, 67, 3, "default"], [65, 26, 67, 3], [65, 28, 67, 3, "PerformanceMeasure"], [65, 46, 67, 3], [65, 48, 67, 3, "_PerformanceEntry2"], [65, 66, 67, 3], [66, 4, 67, 3], [66, 15, 67, 3, "_createClass2"], [66, 28, 67, 3], [66, 29, 67, 3, "default"], [66, 36, 67, 3], [66, 38, 67, 3, "PerformanceMeasure"], [66, 56, 67, 3], [67, 6, 67, 3, "key"], [67, 9, 67, 3], [68, 6, 67, 3, "get"], [68, 9, 67, 3], [68, 11, 69, 2], [68, 20, 69, 2, "get"], [68, 21, 69, 2], [68, 23, 69, 27], [69, 8, 70, 4], [69, 19, 70, 4, "_classPrivateFieldLooseBase2"], [69, 47, 70, 4], [69, 48, 70, 4, "default"], [69, 55, 70, 4], [69, 57, 70, 11], [69, 61, 70, 15], [69, 63, 70, 15, "_detail2"], [69, 71, 70, 15], [69, 73, 70, 15, "_detail2"], [69, 81, 70, 15], [70, 6, 71, 2], [71, 4, 71, 3], [72, 2, 71, 3], [72, 4, 53, 40, "PerformanceEntry"], [72, 39, 53, 56], [73, 0, 53, 56], [73, 3]], "functionMap": {"names": ["<global>", "PerformanceMark", "PerformanceMark#constructor", "PerformanceMark#get__detail", "PerformanceMeasure", "PerformanceMeasure#constructor", "PerformanceMeasure#get__detail"], "mappings": "AAA;OC+B;ECG;GDW;EEE;GFE;CDC;OIE;ECG;GDW;EEE;GFE"}}, "type": "js/module"}]}