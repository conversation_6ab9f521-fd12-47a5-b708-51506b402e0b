{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./ReadOnlyCharacterData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 60}}], "key": "r0vfs/kjSd8ZDBhhWGTkljxNWlA=", "exportNames": ["*"]}}, {"name": "./ReadOnlyNode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 42}}], "key": "OYDeHP1Dzx6fXOFHsRIU9CjY1bo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _ReadOnlyCharacterData = _interopRequireDefault(require(_dependencyMap[6], \"./ReadOnlyCharacterData\"));\n  var _ReadOnlyNode = _interopRequireDefault(require(_dependencyMap[7], \"./ReadOnlyNode\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var ReadOnlyText = exports.default = /*#__PURE__*/function (_ReadOnlyCharacterDat) {\n    function ReadOnlyText() {\n      (0, _classCallCheck2.default)(this, ReadOnlyText);\n      return _callSuper(this, ReadOnlyText, arguments);\n    }\n    (0, _inherits2.default)(ReadOnlyText, _ReadOnlyCharacterDat);\n    return (0, _createClass2.default)(ReadOnlyText, [{\n      key: \"nodeName\",\n      get: function () {\n        return '#text';\n      }\n    }, {\n      key: \"nodeType\",\n      get: function () {\n        return _ReadOnlyNode.default.TEXT_NODE;\n      }\n    }]);\n  }(_ReadOnlyCharacterData.default);\n});", "lineCount": 34, "map": [[12, 2, 13, 0], [12, 6, 13, 0, "_ReadOnlyCharacterData"], [12, 28, 13, 0], [12, 31, 13, 0, "_interopRequireDefault"], [12, 53, 13, 0], [12, 54, 13, 0, "require"], [12, 61, 13, 0], [12, 62, 13, 0, "_dependencyMap"], [12, 76, 13, 0], [13, 2, 14, 0], [13, 6, 14, 0, "_ReadOnlyNode"], [13, 19, 14, 0], [13, 22, 14, 0, "_interopRequireDefault"], [13, 44, 14, 0], [13, 45, 14, 0, "require"], [13, 52, 14, 0], [13, 53, 14, 0, "_dependencyMap"], [13, 67, 14, 0], [14, 2, 14, 42], [14, 11, 14, 42, "_callSuper"], [14, 22, 14, 42, "t"], [14, 23, 14, 42], [14, 25, 14, 42, "o"], [14, 26, 14, 42], [14, 28, 14, 42, "e"], [14, 29, 14, 42], [14, 40, 14, 42, "o"], [14, 41, 14, 42], [14, 48, 14, 42, "_getPrototypeOf2"], [14, 64, 14, 42], [14, 65, 14, 42, "default"], [14, 72, 14, 42], [14, 74, 14, 42, "o"], [14, 75, 14, 42], [14, 82, 14, 42, "_possibleConstructorReturn2"], [14, 109, 14, 42], [14, 110, 14, 42, "default"], [14, 117, 14, 42], [14, 119, 14, 42, "t"], [14, 120, 14, 42], [14, 122, 14, 42, "_isNativeReflectConstruct"], [14, 147, 14, 42], [14, 152, 14, 42, "Reflect"], [14, 159, 14, 42], [14, 160, 14, 42, "construct"], [14, 169, 14, 42], [14, 170, 14, 42, "o"], [14, 171, 14, 42], [14, 173, 14, 42, "e"], [14, 174, 14, 42], [14, 186, 14, 42, "_getPrototypeOf2"], [14, 202, 14, 42], [14, 203, 14, 42, "default"], [14, 210, 14, 42], [14, 212, 14, 42, "t"], [14, 213, 14, 42], [14, 215, 14, 42, "constructor"], [14, 226, 14, 42], [14, 230, 14, 42, "o"], [14, 231, 14, 42], [14, 232, 14, 42, "apply"], [14, 237, 14, 42], [14, 238, 14, 42, "t"], [14, 239, 14, 42], [14, 241, 14, 42, "e"], [14, 242, 14, 42], [15, 2, 14, 42], [15, 11, 14, 42, "_isNativeReflectConstruct"], [15, 37, 14, 42], [15, 51, 14, 42, "t"], [15, 52, 14, 42], [15, 56, 14, 42, "Boolean"], [15, 63, 14, 42], [15, 64, 14, 42, "prototype"], [15, 73, 14, 42], [15, 74, 14, 42, "valueOf"], [15, 81, 14, 42], [15, 82, 14, 42, "call"], [15, 86, 14, 42], [15, 87, 14, 42, "Reflect"], [15, 94, 14, 42], [15, 95, 14, 42, "construct"], [15, 104, 14, 42], [15, 105, 14, 42, "Boolean"], [15, 112, 14, 42], [15, 145, 14, 42, "t"], [15, 146, 14, 42], [15, 159, 14, 42, "_isNativeReflectConstruct"], [15, 184, 14, 42], [15, 196, 14, 42, "_isNativeReflectConstruct"], [15, 197, 14, 42], [15, 210, 14, 42, "t"], [15, 211, 14, 42], [16, 2, 14, 42], [16, 6, 16, 21, "ReadOnlyText"], [16, 18, 16, 33], [16, 21, 16, 33, "exports"], [16, 28, 16, 33], [16, 29, 16, 33, "default"], [16, 36, 16, 33], [16, 62, 16, 33, "_ReadOnlyCharacterDat"], [16, 83, 16, 33], [17, 4, 16, 33], [17, 13, 16, 33, "ReadOnlyText"], [17, 26, 16, 33], [18, 6, 16, 33], [18, 10, 16, 33, "_classCallCheck2"], [18, 26, 16, 33], [18, 27, 16, 33, "default"], [18, 34, 16, 33], [18, 42, 16, 33, "ReadOnlyText"], [18, 54, 16, 33], [19, 6, 16, 33], [19, 13, 16, 33, "_callSuper"], [19, 23, 16, 33], [19, 30, 16, 33, "ReadOnlyText"], [19, 42, 16, 33], [19, 44, 16, 33, "arguments"], [19, 53, 16, 33], [20, 4, 16, 33], [21, 4, 16, 33], [21, 8, 16, 33, "_inherits2"], [21, 18, 16, 33], [21, 19, 16, 33, "default"], [21, 26, 16, 33], [21, 28, 16, 33, "ReadOnlyText"], [21, 40, 16, 33], [21, 42, 16, 33, "_ReadOnlyCharacterDat"], [21, 63, 16, 33], [22, 4, 16, 33], [22, 15, 16, 33, "_createClass2"], [22, 28, 16, 33], [22, 29, 16, 33, "default"], [22, 36, 16, 33], [22, 38, 16, 33, "ReadOnlyText"], [22, 50, 16, 33], [23, 6, 16, 33, "key"], [23, 9, 16, 33], [24, 6, 16, 33, "get"], [24, 9, 16, 33], [24, 11, 20, 2], [24, 20, 20, 2, "get"], [24, 21, 20, 2], [24, 23, 20, 25], [25, 8, 21, 4], [25, 15, 21, 11], [25, 22, 21, 18], [26, 6, 22, 2], [27, 4, 22, 3], [28, 6, 22, 3, "key"], [28, 9, 22, 3], [29, 6, 22, 3, "get"], [29, 9, 22, 3], [29, 11, 27, 2], [29, 20, 27, 2, "get"], [29, 21, 27, 2], [29, 23, 27, 25], [30, 8, 28, 4], [30, 15, 28, 11, "ReadOnlyNode"], [30, 36, 28, 23], [30, 37, 28, 24, "TEXT_NODE"], [30, 46, 28, 33], [31, 6, 29, 2], [32, 4, 29, 3], [33, 2, 29, 3], [33, 4, 16, 42, "ReadOnlyCharacterData"], [33, 34, 16, 63], [34, 0, 16, 63], [34, 3]], "functionMap": {"names": ["<global>", "ReadOnlyText", "get__nodeName", "get__nodeType"], "mappings": "AAA;eCe;ECI;GDE;EEK;GFE"}}, "type": "js/module"}]}