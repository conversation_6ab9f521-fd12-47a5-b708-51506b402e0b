{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./cssifyDeclaration", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 52, "index": 52}}], "key": "uAn2G6p9P4Z0dRmH3/+LAeF1DG0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = cssifyObject;\n  var _cssifyDeclaration = _interopRequireDefault(require(_dependencyMap[1], \"./cssifyDeclaration\"));\n  function cssifyObject(style) {\n    var css = '';\n    for (var property in style) {\n      var value = style[property];\n      if (typeof value !== 'string' && typeof value !== 'number') {\n        continue;\n      } // prevents the semicolon after\n      // the last rule declaration\n\n      if (css) {\n        css += ';';\n      }\n      css += (0, _cssifyDeclaration.default)(property, value);\n    }\n    return css;\n  }\n});", "lineCount": 24, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_cssifyDeclaration"], [7, 24, 1, 0], [7, 27, 1, 0, "_interopRequireDefault"], [7, 49, 1, 0], [7, 50, 1, 0, "require"], [7, 57, 1, 0], [7, 58, 1, 0, "_dependencyMap"], [7, 72, 1, 0], [8, 2, 2, 15], [8, 11, 2, 24, "cssifyObject"], [8, 23, 2, 36, "cssifyObject"], [8, 24, 2, 37, "style"], [8, 29, 2, 42], [8, 31, 2, 44], [9, 4, 3, 2], [9, 8, 3, 6, "css"], [9, 11, 3, 9], [9, 14, 3, 12], [9, 16, 3, 14], [10, 4, 5, 2], [10, 9, 5, 7], [10, 13, 5, 11, "property"], [10, 21, 5, 19], [10, 25, 5, 23, "style"], [10, 30, 5, 28], [10, 32, 5, 30], [11, 6, 6, 4], [11, 10, 6, 8, "value"], [11, 15, 6, 13], [11, 18, 6, 16, "style"], [11, 23, 6, 21], [11, 24, 6, 22, "property"], [11, 32, 6, 30], [11, 33, 6, 31], [12, 6, 8, 4], [12, 10, 8, 8], [12, 17, 8, 15, "value"], [12, 22, 8, 20], [12, 27, 8, 25], [12, 35, 8, 33], [12, 39, 8, 37], [12, 46, 8, 44, "value"], [12, 51, 8, 49], [12, 56, 8, 54], [12, 64, 8, 62], [12, 66, 8, 64], [13, 8, 9, 6], [14, 6, 10, 4], [14, 7, 10, 5], [14, 8, 10, 6], [15, 6, 11, 4], [17, 6, 14, 4], [17, 10, 14, 8, "css"], [17, 13, 14, 11], [17, 15, 14, 13], [18, 8, 15, 6, "css"], [18, 11, 15, 9], [18, 15, 15, 13], [18, 18, 15, 16], [19, 6, 16, 4], [20, 6, 18, 4, "css"], [20, 9, 18, 7], [20, 13, 18, 11], [20, 17, 18, 11, "cssifyDeclaration"], [20, 43, 18, 28], [20, 45, 18, 29, "property"], [20, 53, 18, 37], [20, 55, 18, 39, "value"], [20, 60, 18, 44], [20, 61, 18, 45], [21, 4, 19, 2], [22, 4, 21, 2], [22, 11, 21, 9, "css"], [22, 14, 21, 12], [23, 2, 22, 0], [24, 0, 22, 1], [24, 3]], "functionMap": {"names": ["<global>", "cssifyObject"], "mappings": "AAA;eCC"}}, "type": "js/module"}]}