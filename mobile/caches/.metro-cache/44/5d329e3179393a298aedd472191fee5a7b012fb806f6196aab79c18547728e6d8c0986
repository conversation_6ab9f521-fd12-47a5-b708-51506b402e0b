{"dependencies": [{"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 38, "column": 17, "index": 1547}, "end": {"line": 38, "column": 52, "index": 1582}}], "key": "uE+cRVNnMKkS9OYKR5fpRqPul5s=", "exportNames": ["*"]}}, {"name": "expo-linking", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 33, "index": 1617}, "end": {"line": 39, "column": 56, "index": 1640}}], "key": "F3IRuZxT1cyHB74rJR7WrB3Q6GA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 40, "column": 27, "index": 1670}, "end": {"line": 40, "column": 43, "index": 1686}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 41, "column": 23, "index": 1712}, "end": {"line": 41, "column": 46, "index": 1735}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "./extractPathFromURL", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 42, "column": 29, "index": 1766}, "end": {"line": 42, "column": 60, "index": 1797}}], "key": "KXOk4hev8FkRJgWPmw0XnnwXkL4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = {\n        enumerable: true,\n        get: function () {\n          return m[k];\n        }\n      };\n    }\n    Object.defineProperty(o, k2, desc);\n  } : function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n  });\n  var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n    Object.defineProperty(o, \"default\", {\n      enumerable: true,\n      value: v\n    });\n  } : function (o, v) {\n    o[\"default\"] = v;\n  });\n  var __importStar = this && this.__importStar || function () {\n    var ownKeys = function (o) {\n      ownKeys = Object.getOwnPropertyNames || function (o) {\n        var ar = [];\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n        return ar;\n      };\n      return ownKeys(o);\n    };\n    return function (mod) {\n      if (mod && mod.__esModule) return mod;\n      var result = {};\n      if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n      __setModuleDefault(result, mod);\n      return result;\n    };\n  }();\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useLinking = useLinking;\n  exports.getInitialURLWithTimeout = getInitialURLWithTimeout;\n  var native_1 = require(_dependencyMap[0], \"@react-navigation/native\");\n  var ExpoLinking = __importStar(require(_dependencyMap[1], \"expo-linking\"));\n  var React = __importStar(require(_dependencyMap[2], \"react\"));\n  var react_native_1 = require(_dependencyMap[3], \"react-native\");\n  var extractPathFromURL_1 = require(_dependencyMap[4], \"./extractPathFromURL\");\n  var linkingHandlers = [];\n  function useLinking(ref, _ref, onUnhandledLinking) {\n    var _ref$enabled = _ref.enabled,\n      enabled = _ref$enabled === void 0 ? true : _ref$enabled,\n      prefixes = _ref.prefixes,\n      filter = _ref.filter,\n      config = _ref.config,\n      _ref$getInitialURL = _ref.getInitialURL,\n      getInitialURL = _ref$getInitialURL === void 0 ? () => getInitialURLWithTimeout() : _ref$getInitialURL,\n      _ref$subscribe = _ref.subscribe,\n      subscribe = _ref$subscribe === void 0 ? listener => {\n        var callback = _ref2 => {\n          var url = _ref2.url;\n          return listener(url);\n        };\n        var subscription = react_native_1.Linking.addEventListener('url', callback);\n        // Storing this in a local variable stops Jest from complaining about import after teardown\n        // @ts-expect-error: removeEventListener is not present in newer RN versions\n        var removeEventListener = react_native_1.Linking.removeEventListener?.bind(react_native_1.Linking);\n        return () => {\n          // https://github.com/facebook/react-native/commit/6d1aca806cee86ad76de771ed3a1cc62982ebcd7\n          if (subscription?.remove) {\n            subscription.remove();\n          } else {\n            removeEventListener?.('url', callback);\n          }\n        };\n      } : _ref$subscribe,\n      _ref$getStateFromPath = _ref.getStateFromPath,\n      getStateFromPath = _ref$getStateFromPath === void 0 ? native_1.getStateFromPath : _ref$getStateFromPath,\n      _ref$getActionFromSta = _ref.getActionFromState,\n      getActionFromState = _ref$getActionFromSta === void 0 ? native_1.getActionFromState : _ref$getActionFromSta;\n    var independent = (0, native_1.useNavigationIndependentTree)();\n    React.useEffect(() => {\n      if (process.env.NODE_ENV === 'production') {\n        return undefined;\n      }\n      if (independent) {\n        return undefined;\n      }\n      if (enabled !== false && linkingHandlers.length) {\n        console.error(['Looks like you have configured linking in multiple places. This is likely an error since deep links should only be handled in one place to avoid conflicts. Make sure that:', \"- You don't have multiple NavigationContainers in the app each with 'linking' enabled\", '- Only a single instance of the root component is rendered', react_native_1.Platform.OS === 'android' ? \"- You have set 'android:launchMode=singleTask' in the '<activity />' section of the 'AndroidManifest.xml' file to avoid launching multiple instances\" : ''].join('\\n').trim());\n      }\n      var handler = Symbol();\n      if (enabled !== false) {\n        linkingHandlers.push(handler);\n      }\n      return () => {\n        var index = linkingHandlers.indexOf(handler);\n        if (index > -1) {\n          linkingHandlers.splice(index, 1);\n        }\n      };\n    }, [enabled, independent]);\n    // We store these options in ref to avoid re-creating getInitialState and re-subscribing listeners\n    // This lets user avoid wrapping the items in `React.useCallback` or `React.useMemo`\n    // Not re-creating `getInitialState` is important coz it makes it easier for the user to use in an effect\n    var enabledRef = React.useRef(enabled);\n    var prefixesRef = React.useRef(prefixes);\n    var filterRef = React.useRef(filter);\n    var configRef = React.useRef(config);\n    var getInitialURLRef = React.useRef(getInitialURL);\n    var getStateFromPathRef = React.useRef(getStateFromPath);\n    var getActionFromStateRef = React.useRef(getActionFromState);\n    React.useEffect(() => {\n      enabledRef.current = enabled;\n      prefixesRef.current = prefixes;\n      filterRef.current = filter;\n      configRef.current = config;\n      getInitialURLRef.current = getInitialURL;\n      getStateFromPathRef.current = getStateFromPath;\n      getActionFromStateRef.current = getActionFromState;\n    });\n    var getStateFromURL = React.useCallback(url => {\n      if (!url || filterRef.current && !filterRef.current(url)) {\n        return undefined;\n      }\n      var path = (0, extractPathFromURL_1.extractExpoPathFromURL)(prefixesRef.current, url);\n      return path !== undefined ? getStateFromPathRef.current(path, configRef.current) : undefined;\n    }, []);\n    var getInitialState = React.useCallback(() => {\n      var state;\n      if (enabledRef.current) {\n        var url = getInitialURLRef.current();\n        if (url != null) {\n          if (typeof url !== 'string') {\n            return url.then(url => {\n              var state = getStateFromURL(url);\n              if (typeof url === 'string') {\n                // If the link were handled, it gets cleared in NavigationContainer\n                onUnhandledLinking((0, extractPathFromURL_1.extractExpoPathFromURL)(prefixes, url));\n              }\n              return state;\n            });\n          } else {\n            onUnhandledLinking((0, extractPathFromURL_1.extractExpoPathFromURL)(prefixes, url));\n          }\n        }\n        state = getStateFromURL(url);\n      }\n      var thenable = {\n        then(onfulfilled) {\n          return Promise.resolve(onfulfilled ? onfulfilled(state) : state);\n        },\n        catch() {\n          return thenable;\n        }\n      };\n      return thenable;\n    }, [getStateFromURL, onUnhandledLinking, prefixes]);\n    React.useEffect(() => {\n      var listener = url => {\n        if (!enabled) {\n          return;\n        }\n        var navigation = ref.current;\n        var state = navigation ? getStateFromURL(url) : undefined;\n        if (navigation && state) {\n          // If the link were handled, it gets cleared in NavigationContainer\n          onUnhandledLinking((0, extractPathFromURL_1.extractExpoPathFromURL)(prefixes, url));\n          var rootState = navigation.getRootState();\n          if (state.routes.some(r => !rootState?.routeNames.includes(r.name))) {\n            return;\n          }\n          var action = getActionFromStateRef.current(state, configRef.current);\n          if (action !== undefined) {\n            try {\n              navigation.dispatch(action);\n            } catch (e) {\n              // Ignore any errors from deep linking.\n              // This could happen in case of malformed links, navigation object not being initialized etc.\n              console.warn(`An error occurred when trying to handle the link '${url}': ${typeof e === 'object' && e != null && 'message' in e ? e.message : e}`);\n            }\n          } else {\n            navigation.resetRoot(state);\n          }\n        }\n      };\n      return subscribe(listener);\n    }, [enabled, getStateFromURL, onUnhandledLinking, prefixes, ref, subscribe]);\n    return {\n      getInitialState\n    };\n  }\n  function getInitialURLWithTimeout() {\n    if (typeof window === 'undefined') {\n      return '';\n    } else if (react_native_1.Platform.OS === 'ios') {\n      // Use the new Expo API for iOS. This has better support for App Clips and handoff.\n      return ExpoLinking.getLinkingURL();\n    }\n    return Promise.race([\n    // TODO: Phase this out in favor of expo-linking on Android.\n    react_native_1.Linking.getInitialURL(), new Promise(resolve =>\n    // Timeout in 150ms if `getInitialState` doesn't resolve\n    // Workaround for https://github.com/facebook/react-native/issues/25675\n    setTimeout(() => resolve(null), 150))]);\n  }\n});", "lineCount": 213, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__createBinding"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__createBinding"], [4, 52, 2, 51], [4, 57, 2, 57, "Object"], [4, 63, 2, 63], [4, 64, 2, 64, "create"], [4, 70, 2, 70], [4, 73, 2, 74], [4, 83, 2, 83, "o"], [4, 84, 2, 84], [4, 86, 2, 86, "m"], [4, 87, 2, 87], [4, 89, 2, 89, "k"], [4, 90, 2, 90], [4, 92, 2, 92, "k2"], [4, 94, 2, 94], [4, 96, 2, 96], [5, 4, 3, 4], [5, 8, 3, 8, "k2"], [5, 10, 3, 10], [5, 15, 3, 15, "undefined"], [5, 24, 3, 24], [5, 26, 3, 26, "k2"], [5, 28, 3, 28], [5, 31, 3, 31, "k"], [5, 32, 3, 32], [6, 4, 4, 4], [6, 8, 4, 8, "desc"], [6, 12, 4, 12], [6, 15, 4, 15, "Object"], [6, 21, 4, 21], [6, 22, 4, 22, "getOwnPropertyDescriptor"], [6, 46, 4, 46], [6, 47, 4, 47, "m"], [6, 48, 4, 48], [6, 50, 4, 50, "k"], [6, 51, 4, 51], [6, 52, 4, 52], [7, 4, 5, 4], [7, 8, 5, 8], [7, 9, 5, 9, "desc"], [7, 13, 5, 13], [7, 18, 5, 18], [7, 23, 5, 23], [7, 27, 5, 27, "desc"], [7, 31, 5, 31], [7, 34, 5, 34], [7, 35, 5, 35, "m"], [7, 36, 5, 36], [7, 37, 5, 37, "__esModule"], [7, 47, 5, 47], [7, 50, 5, 50, "desc"], [7, 54, 5, 54], [7, 55, 5, 55, "writable"], [7, 63, 5, 63], [7, 67, 5, 67, "desc"], [7, 71, 5, 71], [7, 72, 5, 72, "configurable"], [7, 84, 5, 84], [7, 85, 5, 85], [7, 87, 5, 87], [8, 6, 6, 6, "desc"], [8, 10, 6, 10], [8, 13, 6, 13], [9, 8, 6, 15, "enumerable"], [9, 18, 6, 25], [9, 20, 6, 27], [9, 24, 6, 31], [10, 8, 6, 33, "get"], [10, 11, 6, 36], [10, 13, 6, 38], [10, 22, 6, 38, "get"], [10, 23, 6, 38], [10, 25, 6, 49], [11, 10, 6, 51], [11, 17, 6, 58, "m"], [11, 18, 6, 59], [11, 19, 6, 60, "k"], [11, 20, 6, 61], [11, 21, 6, 62], [12, 8, 6, 64], [13, 6, 6, 66], [13, 7, 6, 67], [14, 4, 7, 4], [15, 4, 8, 4, "Object"], [15, 10, 8, 10], [15, 11, 8, 11, "defineProperty"], [15, 25, 8, 25], [15, 26, 8, 26, "o"], [15, 27, 8, 27], [15, 29, 8, 29, "k2"], [15, 31, 8, 31], [15, 33, 8, 33, "desc"], [15, 37, 8, 37], [15, 38, 8, 38], [16, 2, 9, 0], [16, 3, 9, 1], [16, 6, 9, 6], [16, 16, 9, 15, "o"], [16, 17, 9, 16], [16, 19, 9, 18, "m"], [16, 20, 9, 19], [16, 22, 9, 21, "k"], [16, 23, 9, 22], [16, 25, 9, 24, "k2"], [16, 27, 9, 26], [16, 29, 9, 28], [17, 4, 10, 4], [17, 8, 10, 8, "k2"], [17, 10, 10, 10], [17, 15, 10, 15, "undefined"], [17, 24, 10, 24], [17, 26, 10, 26, "k2"], [17, 28, 10, 28], [17, 31, 10, 31, "k"], [17, 32, 10, 32], [18, 4, 11, 4, "o"], [18, 5, 11, 5], [18, 6, 11, 6, "k2"], [18, 8, 11, 8], [18, 9, 11, 9], [18, 12, 11, 12, "m"], [18, 13, 11, 13], [18, 14, 11, 14, "k"], [18, 15, 11, 15], [18, 16, 11, 16], [19, 2, 12, 0], [19, 3, 12, 2], [19, 4, 12, 3], [20, 2, 13, 0], [20, 6, 13, 4, "__setModuleDefault"], [20, 24, 13, 22], [20, 27, 13, 26], [20, 31, 13, 30], [20, 35, 13, 34], [20, 39, 13, 38], [20, 40, 13, 39, "__setModuleDefault"], [20, 58, 13, 57], [20, 63, 13, 63, "Object"], [20, 69, 13, 69], [20, 70, 13, 70, "create"], [20, 76, 13, 76], [20, 79, 13, 80], [20, 89, 13, 89, "o"], [20, 90, 13, 90], [20, 92, 13, 92, "v"], [20, 93, 13, 93], [20, 95, 13, 95], [21, 4, 14, 4, "Object"], [21, 10, 14, 10], [21, 11, 14, 11, "defineProperty"], [21, 25, 14, 25], [21, 26, 14, 26, "o"], [21, 27, 14, 27], [21, 29, 14, 29], [21, 38, 14, 38], [21, 40, 14, 40], [22, 6, 14, 42, "enumerable"], [22, 16, 14, 52], [22, 18, 14, 54], [22, 22, 14, 58], [23, 6, 14, 60, "value"], [23, 11, 14, 65], [23, 13, 14, 67, "v"], [24, 4, 14, 69], [24, 5, 14, 70], [24, 6, 14, 71], [25, 2, 15, 0], [25, 3, 15, 1], [25, 6, 15, 5], [25, 16, 15, 14, "o"], [25, 17, 15, 15], [25, 19, 15, 17, "v"], [25, 20, 15, 18], [25, 22, 15, 20], [26, 4, 16, 4, "o"], [26, 5, 16, 5], [26, 6, 16, 6], [26, 15, 16, 15], [26, 16, 16, 16], [26, 19, 16, 19, "v"], [26, 20, 16, 20], [27, 2, 17, 0], [27, 3, 17, 1], [27, 4, 17, 2], [28, 2, 18, 0], [28, 6, 18, 4, "__importStar"], [28, 18, 18, 16], [28, 21, 18, 20], [28, 25, 18, 24], [28, 29, 18, 28], [28, 33, 18, 32], [28, 34, 18, 33, "__importStar"], [28, 46, 18, 45], [28, 50, 18, 51], [28, 62, 18, 63], [29, 4, 19, 4], [29, 8, 19, 8, "ownKeys"], [29, 15, 19, 15], [29, 18, 19, 18], [29, 27, 19, 18, "ownKeys"], [29, 28, 19, 27, "o"], [29, 29, 19, 28], [29, 31, 19, 30], [30, 6, 20, 8, "ownKeys"], [30, 13, 20, 15], [30, 16, 20, 18, "Object"], [30, 22, 20, 24], [30, 23, 20, 25, "getOwnPropertyNames"], [30, 42, 20, 44], [30, 46, 20, 48], [30, 56, 20, 58, "o"], [30, 57, 20, 59], [30, 59, 20, 61], [31, 8, 21, 12], [31, 12, 21, 16, "ar"], [31, 14, 21, 18], [31, 17, 21, 21], [31, 19, 21, 23], [32, 8, 22, 12], [32, 13, 22, 17], [32, 17, 22, 21, "k"], [32, 18, 22, 22], [32, 22, 22, 26, "o"], [32, 23, 22, 27], [32, 25, 22, 29], [32, 29, 22, 33, "Object"], [32, 35, 22, 39], [32, 36, 22, 40, "prototype"], [32, 45, 22, 49], [32, 46, 22, 50, "hasOwnProperty"], [32, 60, 22, 64], [32, 61, 22, 65, "call"], [32, 65, 22, 69], [32, 66, 22, 70, "o"], [32, 67, 22, 71], [32, 69, 22, 73, "k"], [32, 70, 22, 74], [32, 71, 22, 75], [32, 73, 22, 77, "ar"], [32, 75, 22, 79], [32, 76, 22, 80, "ar"], [32, 78, 22, 82], [32, 79, 22, 83, "length"], [32, 85, 22, 89], [32, 86, 22, 90], [32, 89, 22, 93, "k"], [32, 90, 22, 94], [33, 8, 23, 12], [33, 15, 23, 19, "ar"], [33, 17, 23, 21], [34, 6, 24, 8], [34, 7, 24, 9], [35, 6, 25, 8], [35, 13, 25, 15, "ownKeys"], [35, 20, 25, 22], [35, 21, 25, 23, "o"], [35, 22, 25, 24], [35, 23, 25, 25], [36, 4, 26, 4], [36, 5, 26, 5], [37, 4, 27, 4], [37, 11, 27, 11], [37, 21, 27, 21, "mod"], [37, 24, 27, 24], [37, 26, 27, 26], [38, 6, 28, 8], [38, 10, 28, 12, "mod"], [38, 13, 28, 15], [38, 17, 28, 19, "mod"], [38, 20, 28, 22], [38, 21, 28, 23, "__esModule"], [38, 31, 28, 33], [38, 33, 28, 35], [38, 40, 28, 42, "mod"], [38, 43, 28, 45], [39, 6, 29, 8], [39, 10, 29, 12, "result"], [39, 16, 29, 18], [39, 19, 29, 21], [39, 20, 29, 22], [39, 21, 29, 23], [40, 6, 30, 8], [40, 10, 30, 12, "mod"], [40, 13, 30, 15], [40, 17, 30, 19], [40, 21, 30, 23], [40, 23, 30, 25], [40, 28, 30, 30], [40, 32, 30, 34, "k"], [40, 33, 30, 35], [40, 36, 30, 38, "ownKeys"], [40, 43, 30, 45], [40, 44, 30, 46, "mod"], [40, 47, 30, 49], [40, 48, 30, 50], [40, 50, 30, 52, "i"], [40, 51, 30, 53], [40, 54, 30, 56], [40, 55, 30, 57], [40, 57, 30, 59, "i"], [40, 58, 30, 60], [40, 61, 30, 63, "k"], [40, 62, 30, 64], [40, 63, 30, 65, "length"], [40, 69, 30, 71], [40, 71, 30, 73, "i"], [40, 72, 30, 74], [40, 74, 30, 76], [40, 76, 30, 78], [40, 80, 30, 82, "k"], [40, 81, 30, 83], [40, 82, 30, 84, "i"], [40, 83, 30, 85], [40, 84, 30, 86], [40, 89, 30, 91], [40, 98, 30, 100], [40, 100, 30, 102, "__createBinding"], [40, 115, 30, 117], [40, 116, 30, 118, "result"], [40, 122, 30, 124], [40, 124, 30, 126, "mod"], [40, 127, 30, 129], [40, 129, 30, 131, "k"], [40, 130, 30, 132], [40, 131, 30, 133, "i"], [40, 132, 30, 134], [40, 133, 30, 135], [40, 134, 30, 136], [41, 6, 31, 8, "__setModuleDefault"], [41, 24, 31, 26], [41, 25, 31, 27, "result"], [41, 31, 31, 33], [41, 33, 31, 35, "mod"], [41, 36, 31, 38], [41, 37, 31, 39], [42, 6, 32, 8], [42, 13, 32, 15, "result"], [42, 19, 32, 21], [43, 4, 33, 4], [43, 5, 33, 5], [44, 2, 34, 0], [44, 3, 34, 1], [44, 4, 34, 3], [44, 5, 34, 4], [45, 2, 35, 0, "Object"], [45, 8, 35, 6], [45, 9, 35, 7, "defineProperty"], [45, 23, 35, 21], [45, 24, 35, 22, "exports"], [45, 31, 35, 29], [45, 33, 35, 31], [45, 45, 35, 43], [45, 47, 35, 45], [46, 4, 35, 47, "value"], [46, 9, 35, 52], [46, 11, 35, 54], [47, 2, 35, 59], [47, 3, 35, 60], [47, 4, 35, 61], [48, 2, 36, 0, "exports"], [48, 9, 36, 7], [48, 10, 36, 8, "useLinking"], [48, 20, 36, 18], [48, 23, 36, 21, "useLinking"], [48, 33, 36, 31], [49, 2, 37, 0, "exports"], [49, 9, 37, 7], [49, 10, 37, 8, "getInitialURLWithTimeout"], [49, 34, 37, 32], [49, 37, 37, 35, "getInitialURLWithTimeout"], [49, 61, 37, 59], [50, 2, 38, 0], [50, 6, 38, 6, "native_1"], [50, 14, 38, 14], [50, 17, 38, 17, "require"], [50, 24, 38, 24], [50, 25, 38, 24, "_dependencyMap"], [50, 39, 38, 24], [50, 70, 38, 51], [50, 71, 38, 52], [51, 2, 39, 0], [51, 6, 39, 6, "ExpoLinking"], [51, 17, 39, 17], [51, 20, 39, 20, "__importStar"], [51, 32, 39, 32], [51, 33, 39, 33, "require"], [51, 40, 39, 40], [51, 41, 39, 40, "_dependencyMap"], [51, 55, 39, 40], [51, 74, 39, 55], [51, 75, 39, 56], [51, 76, 39, 57], [52, 2, 40, 0], [52, 6, 40, 6, "React"], [52, 11, 40, 11], [52, 14, 40, 14, "__importStar"], [52, 26, 40, 26], [52, 27, 40, 27, "require"], [52, 34, 40, 34], [52, 35, 40, 34, "_dependencyMap"], [52, 49, 40, 34], [52, 61, 40, 42], [52, 62, 40, 43], [52, 63, 40, 44], [53, 2, 41, 0], [53, 6, 41, 6, "react_native_1"], [53, 20, 41, 20], [53, 23, 41, 23, "require"], [53, 30, 41, 30], [53, 31, 41, 30, "_dependencyMap"], [53, 45, 41, 30], [53, 64, 41, 45], [53, 65, 41, 46], [54, 2, 42, 0], [54, 6, 42, 6, "extractPathFromURL_1"], [54, 26, 42, 26], [54, 29, 42, 29, "require"], [54, 36, 42, 36], [54, 37, 42, 36, "_dependencyMap"], [54, 51, 42, 36], [54, 78, 42, 59], [54, 79, 42, 60], [55, 2, 43, 0], [55, 6, 43, 6, "linkingHandlers"], [55, 21, 43, 21], [55, 24, 43, 24], [55, 26, 43, 26], [56, 2, 44, 0], [56, 11, 44, 9, "useLinking"], [56, 21, 44, 19, "useLinking"], [56, 22, 44, 20, "ref"], [56, 25, 44, 23], [56, 27, 44, 23, "_ref"], [56, 31, 44, 23], [56, 33, 59, 102, "onUnhandledLinking"], [56, 51, 59, 120], [56, 53, 59, 122], [57, 4, 59, 122], [57, 8, 59, 122, "_ref$enabled"], [57, 20, 59, 122], [57, 23, 59, 122, "_ref"], [57, 27, 59, 122], [57, 28, 44, 27, "enabled"], [57, 35, 44, 34], [58, 6, 44, 27, "enabled"], [58, 13, 44, 34], [58, 16, 44, 34, "_ref$enabled"], [58, 28, 44, 34], [58, 42, 44, 37], [58, 46, 44, 41], [58, 49, 44, 41, "_ref$enabled"], [58, 61, 44, 41], [59, 6, 44, 43, "prefixes"], [59, 14, 44, 51], [59, 17, 44, 51, "_ref"], [59, 21, 44, 51], [59, 22, 44, 43, "prefixes"], [59, 30, 44, 51], [60, 6, 44, 53, "filter"], [60, 12, 44, 59], [60, 15, 44, 59, "_ref"], [60, 19, 44, 59], [60, 20, 44, 53, "filter"], [60, 26, 44, 59], [61, 6, 44, 61, "config"], [61, 12, 44, 67], [61, 15, 44, 67, "_ref"], [61, 19, 44, 67], [61, 20, 44, 61, "config"], [61, 26, 44, 67], [62, 6, 44, 67, "_ref$getInitialURL"], [62, 24, 44, 67], [62, 27, 44, 67, "_ref"], [62, 31, 44, 67], [62, 32, 44, 69, "getInitialURL"], [62, 45, 44, 82], [63, 6, 44, 69, "getInitialURL"], [63, 19, 44, 82], [63, 22, 44, 82, "_ref$getInitialURL"], [63, 40, 44, 82], [63, 54, 44, 85], [63, 60, 44, 91, "getInitialURLWithTimeout"], [63, 84, 44, 115], [63, 85, 44, 116], [63, 86, 44, 117], [63, 89, 44, 117, "_ref$getInitialURL"], [63, 107, 44, 117], [64, 6, 44, 117, "_ref$subscribe"], [64, 20, 44, 117], [64, 23, 44, 117, "_ref"], [64, 27, 44, 117], [64, 28, 44, 119, "subscribe"], [64, 37, 44, 128], [65, 6, 44, 119, "subscribe"], [65, 15, 44, 128], [65, 18, 44, 128, "_ref$subscribe"], [65, 32, 44, 128], [65, 46, 44, 132, "listener"], [65, 54, 44, 140], [65, 58, 44, 145], [66, 8, 45, 4], [66, 12, 45, 10, "callback"], [66, 20, 45, 18], [66, 23, 45, 21, "_ref2"], [66, 28, 45, 21], [67, 10, 45, 21], [67, 14, 45, 24, "url"], [67, 17, 45, 27], [67, 20, 45, 27, "_ref2"], [67, 25, 45, 27], [67, 26, 45, 24, "url"], [67, 29, 45, 27], [68, 10, 45, 27], [68, 17, 45, 34, "listener"], [68, 25, 45, 42], [68, 26, 45, 43, "url"], [68, 29, 45, 46], [68, 30, 45, 47], [69, 8, 45, 47], [70, 8, 46, 4], [70, 12, 46, 10, "subscription"], [70, 24, 46, 22], [70, 27, 46, 25, "react_native_1"], [70, 41, 46, 39], [70, 42, 46, 40, "Linking"], [70, 49, 46, 47], [70, 50, 46, 48, "addEventListener"], [70, 66, 46, 64], [70, 67, 46, 65], [70, 72, 46, 70], [70, 74, 46, 72, "callback"], [70, 82, 46, 80], [70, 83, 46, 81], [71, 8, 47, 4], [72, 8, 48, 4], [73, 8, 49, 4], [73, 12, 49, 10, "removeEventListener"], [73, 31, 49, 29], [73, 34, 49, 32, "react_native_1"], [73, 48, 49, 46], [73, 49, 49, 47, "Linking"], [73, 56, 49, 54], [73, 57, 49, 55, "removeEventListener"], [73, 76, 49, 74], [73, 78, 49, 76, "bind"], [73, 82, 49, 80], [73, 83, 49, 81, "react_native_1"], [73, 97, 49, 95], [73, 98, 49, 96, "Linking"], [73, 105, 49, 103], [73, 106, 49, 104], [74, 8, 50, 4], [74, 15, 50, 11], [74, 21, 50, 17], [75, 10, 51, 8], [76, 10, 52, 8], [76, 14, 52, 12, "subscription"], [76, 26, 52, 24], [76, 28, 52, 26, "remove"], [76, 34, 52, 32], [76, 36, 52, 34], [77, 12, 53, 12, "subscription"], [77, 24, 53, 24], [77, 25, 53, 25, "remove"], [77, 31, 53, 31], [77, 32, 53, 32], [77, 33, 53, 33], [78, 10, 54, 8], [78, 11, 54, 9], [78, 17, 55, 13], [79, 12, 56, 12, "removeEventListener"], [79, 31, 56, 31], [79, 34, 56, 34], [79, 39, 56, 39], [79, 41, 56, 41, "callback"], [79, 49, 56, 49], [79, 50, 56, 50], [80, 10, 57, 8], [81, 8, 58, 4], [81, 9, 58, 5], [82, 6, 59, 0], [82, 7, 59, 1], [82, 10, 59, 1, "_ref$subscribe"], [82, 24, 59, 1], [83, 6, 59, 1, "_ref$getStateFromPath"], [83, 27, 59, 1], [83, 30, 59, 1, "_ref"], [83, 34, 59, 1], [83, 35, 59, 3, "getStateFromPath"], [83, 51, 59, 19], [84, 6, 59, 3, "getStateFromPath"], [84, 22, 59, 19], [84, 25, 59, 19, "_ref$getStateFromPath"], [84, 46, 59, 19], [84, 60, 59, 22, "native_1"], [84, 68, 59, 30], [84, 69, 59, 31, "getStateFromPath"], [84, 85, 59, 47], [84, 88, 59, 47, "_ref$getStateFromPath"], [84, 109, 59, 47], [85, 6, 59, 47, "_ref$getActionFromSta"], [85, 27, 59, 47], [85, 30, 59, 47, "_ref"], [85, 34, 59, 47], [85, 35, 59, 49, "getActionFromState"], [85, 53, 59, 67], [86, 6, 59, 49, "getActionFromState"], [86, 24, 59, 67], [86, 27, 59, 67, "_ref$getActionFromSta"], [86, 48, 59, 67], [86, 62, 59, 70, "native_1"], [86, 70, 59, 78], [86, 71, 59, 79, "getActionFromState"], [86, 89, 59, 97], [86, 92, 59, 97, "_ref$getActionFromSta"], [86, 113, 59, 97], [87, 4, 60, 4], [87, 8, 60, 10, "independent"], [87, 19, 60, 21], [87, 22, 60, 24], [87, 23, 60, 25], [87, 24, 60, 26], [87, 26, 60, 28, "native_1"], [87, 34, 60, 36], [87, 35, 60, 37, "useNavigationIndependentTree"], [87, 63, 60, 65], [87, 65, 60, 67], [87, 66, 60, 68], [88, 4, 61, 4, "React"], [88, 9, 61, 9], [88, 10, 61, 10, "useEffect"], [88, 19, 61, 19], [88, 20, 61, 20], [88, 26, 61, 26], [89, 6, 62, 8], [89, 10, 62, 12, "process"], [89, 17, 62, 19], [89, 18, 62, 20, "env"], [89, 21, 62, 23], [89, 22, 62, 24, "NODE_ENV"], [89, 30, 62, 32], [89, 35, 62, 37], [89, 47, 62, 49], [89, 49, 62, 51], [90, 8, 63, 12], [90, 15, 63, 19, "undefined"], [90, 24, 63, 28], [91, 6, 64, 8], [92, 6, 65, 8], [92, 10, 65, 12, "independent"], [92, 21, 65, 23], [92, 23, 65, 25], [93, 8, 66, 12], [93, 15, 66, 19, "undefined"], [93, 24, 66, 28], [94, 6, 67, 8], [95, 6, 68, 8], [95, 10, 68, 12, "enabled"], [95, 17, 68, 19], [95, 22, 68, 24], [95, 27, 68, 29], [95, 31, 68, 33, "linkingHandlers"], [95, 46, 68, 48], [95, 47, 68, 49, "length"], [95, 53, 68, 55], [95, 55, 68, 57], [96, 8, 69, 12, "console"], [96, 15, 69, 19], [96, 16, 69, 20, "error"], [96, 21, 69, 25], [96, 22, 69, 26], [96, 23, 70, 16], [96, 196, 70, 189], [96, 198, 71, 16], [96, 285, 71, 103], [96, 287, 72, 16], [96, 347, 72, 76], [96, 349, 73, 16, "react_native_1"], [96, 363, 73, 30], [96, 364, 73, 31, "Platform"], [96, 372, 73, 39], [96, 373, 73, 40, "OS"], [96, 375, 73, 42], [96, 380, 73, 47], [96, 389, 73, 56], [96, 392, 74, 22], [96, 542, 74, 172], [96, 545, 75, 22], [96, 547, 75, 24], [96, 548, 76, 13], [96, 549, 77, 17, "join"], [96, 553, 77, 21], [96, 554, 77, 22], [96, 558, 77, 26], [96, 559, 77, 27], [96, 560, 78, 17, "trim"], [96, 564, 78, 21], [96, 565, 78, 22], [96, 566, 78, 23], [96, 567, 78, 24], [97, 6, 79, 8], [98, 6, 80, 8], [98, 10, 80, 14, "handler"], [98, 17, 80, 21], [98, 20, 80, 24, "Symbol"], [98, 26, 80, 30], [98, 27, 80, 31], [98, 28, 80, 32], [99, 6, 81, 8], [99, 10, 81, 12, "enabled"], [99, 17, 81, 19], [99, 22, 81, 24], [99, 27, 81, 29], [99, 29, 81, 31], [100, 8, 82, 12, "linkingHandlers"], [100, 23, 82, 27], [100, 24, 82, 28, "push"], [100, 28, 82, 32], [100, 29, 82, 33, "handler"], [100, 36, 82, 40], [100, 37, 82, 41], [101, 6, 83, 8], [102, 6, 84, 8], [102, 13, 84, 15], [102, 19, 84, 21], [103, 8, 85, 12], [103, 12, 85, 18, "index"], [103, 17, 85, 23], [103, 20, 85, 26, "linkingHandlers"], [103, 35, 85, 41], [103, 36, 85, 42, "indexOf"], [103, 43, 85, 49], [103, 44, 85, 50, "handler"], [103, 51, 85, 57], [103, 52, 85, 58], [104, 8, 86, 12], [104, 12, 86, 16, "index"], [104, 17, 86, 21], [104, 20, 86, 24], [104, 21, 86, 25], [104, 22, 86, 26], [104, 24, 86, 28], [105, 10, 87, 16, "linkingHandlers"], [105, 25, 87, 31], [105, 26, 87, 32, "splice"], [105, 32, 87, 38], [105, 33, 87, 39, "index"], [105, 38, 87, 44], [105, 40, 87, 46], [105, 41, 87, 47], [105, 42, 87, 48], [106, 8, 88, 12], [107, 6, 89, 8], [107, 7, 89, 9], [108, 4, 90, 4], [108, 5, 90, 5], [108, 7, 90, 7], [108, 8, 90, 8, "enabled"], [108, 15, 90, 15], [108, 17, 90, 17, "independent"], [108, 28, 90, 28], [108, 29, 90, 29], [108, 30, 90, 30], [109, 4, 91, 4], [110, 4, 92, 4], [111, 4, 93, 4], [112, 4, 94, 4], [112, 8, 94, 10, "enabledRef"], [112, 18, 94, 20], [112, 21, 94, 23, "React"], [112, 26, 94, 28], [112, 27, 94, 29, "useRef"], [112, 33, 94, 35], [112, 34, 94, 36, "enabled"], [112, 41, 94, 43], [112, 42, 94, 44], [113, 4, 95, 4], [113, 8, 95, 10, "prefixesRef"], [113, 19, 95, 21], [113, 22, 95, 24, "React"], [113, 27, 95, 29], [113, 28, 95, 30, "useRef"], [113, 34, 95, 36], [113, 35, 95, 37, "prefixes"], [113, 43, 95, 45], [113, 44, 95, 46], [114, 4, 96, 4], [114, 8, 96, 10, "filterRef"], [114, 17, 96, 19], [114, 20, 96, 22, "React"], [114, 25, 96, 27], [114, 26, 96, 28, "useRef"], [114, 32, 96, 34], [114, 33, 96, 35, "filter"], [114, 39, 96, 41], [114, 40, 96, 42], [115, 4, 97, 4], [115, 8, 97, 10, "configRef"], [115, 17, 97, 19], [115, 20, 97, 22, "React"], [115, 25, 97, 27], [115, 26, 97, 28, "useRef"], [115, 32, 97, 34], [115, 33, 97, 35, "config"], [115, 39, 97, 41], [115, 40, 97, 42], [116, 4, 98, 4], [116, 8, 98, 10, "getInitialURLRef"], [116, 24, 98, 26], [116, 27, 98, 29, "React"], [116, 32, 98, 34], [116, 33, 98, 35, "useRef"], [116, 39, 98, 41], [116, 40, 98, 42, "getInitialURL"], [116, 53, 98, 55], [116, 54, 98, 56], [117, 4, 99, 4], [117, 8, 99, 10, "getStateFromPathRef"], [117, 27, 99, 29], [117, 30, 99, 32, "React"], [117, 35, 99, 37], [117, 36, 99, 38, "useRef"], [117, 42, 99, 44], [117, 43, 99, 45, "getStateFromPath"], [117, 59, 99, 61], [117, 60, 99, 62], [118, 4, 100, 4], [118, 8, 100, 10, "getActionFromStateRef"], [118, 29, 100, 31], [118, 32, 100, 34, "React"], [118, 37, 100, 39], [118, 38, 100, 40, "useRef"], [118, 44, 100, 46], [118, 45, 100, 47, "getActionFromState"], [118, 63, 100, 65], [118, 64, 100, 66], [119, 4, 101, 4, "React"], [119, 9, 101, 9], [119, 10, 101, 10, "useEffect"], [119, 19, 101, 19], [119, 20, 101, 20], [119, 26, 101, 26], [120, 6, 102, 8, "enabledRef"], [120, 16, 102, 18], [120, 17, 102, 19, "current"], [120, 24, 102, 26], [120, 27, 102, 29, "enabled"], [120, 34, 102, 36], [121, 6, 103, 8, "prefixesRef"], [121, 17, 103, 19], [121, 18, 103, 20, "current"], [121, 25, 103, 27], [121, 28, 103, 30, "prefixes"], [121, 36, 103, 38], [122, 6, 104, 8, "filterRef"], [122, 15, 104, 17], [122, 16, 104, 18, "current"], [122, 23, 104, 25], [122, 26, 104, 28, "filter"], [122, 32, 104, 34], [123, 6, 105, 8, "configRef"], [123, 15, 105, 17], [123, 16, 105, 18, "current"], [123, 23, 105, 25], [123, 26, 105, 28, "config"], [123, 32, 105, 34], [124, 6, 106, 8, "getInitialURLRef"], [124, 22, 106, 24], [124, 23, 106, 25, "current"], [124, 30, 106, 32], [124, 33, 106, 35, "getInitialURL"], [124, 46, 106, 48], [125, 6, 107, 8, "getStateFromPathRef"], [125, 25, 107, 27], [125, 26, 107, 28, "current"], [125, 33, 107, 35], [125, 36, 107, 38, "getStateFromPath"], [125, 52, 107, 54], [126, 6, 108, 8, "getActionFromStateRef"], [126, 27, 108, 29], [126, 28, 108, 30, "current"], [126, 35, 108, 37], [126, 38, 108, 40, "getActionFromState"], [126, 56, 108, 58], [127, 4, 109, 4], [127, 5, 109, 5], [127, 6, 109, 6], [128, 4, 110, 4], [128, 8, 110, 10, "getStateFromURL"], [128, 23, 110, 25], [128, 26, 110, 28, "React"], [128, 31, 110, 33], [128, 32, 110, 34, "useCallback"], [128, 43, 110, 45], [128, 44, 110, 47, "url"], [128, 47, 110, 50], [128, 51, 110, 55], [129, 6, 111, 8], [129, 10, 111, 12], [129, 11, 111, 13, "url"], [129, 14, 111, 16], [129, 18, 111, 21, "filterRef"], [129, 27, 111, 30], [129, 28, 111, 31, "current"], [129, 35, 111, 38], [129, 39, 111, 42], [129, 40, 111, 43, "filterRef"], [129, 49, 111, 52], [129, 50, 111, 53, "current"], [129, 57, 111, 60], [129, 58, 111, 61, "url"], [129, 61, 111, 64], [129, 62, 111, 66], [129, 64, 111, 68], [130, 8, 112, 12], [130, 15, 112, 19, "undefined"], [130, 24, 112, 28], [131, 6, 113, 8], [132, 6, 114, 8], [132, 10, 114, 14, "path"], [132, 14, 114, 18], [132, 17, 114, 21], [132, 18, 114, 22], [132, 19, 114, 23], [132, 21, 114, 25, "extractPathFromURL_1"], [132, 41, 114, 45], [132, 42, 114, 46, "extractExpoPathFromURL"], [132, 64, 114, 68], [132, 66, 114, 70, "prefixesRef"], [132, 77, 114, 81], [132, 78, 114, 82, "current"], [132, 85, 114, 89], [132, 87, 114, 91, "url"], [132, 90, 114, 94], [132, 91, 114, 95], [133, 6, 115, 8], [133, 13, 115, 15, "path"], [133, 17, 115, 19], [133, 22, 115, 24, "undefined"], [133, 31, 115, 33], [133, 34, 115, 36, "getStateFromPathRef"], [133, 53, 115, 55], [133, 54, 115, 56, "current"], [133, 61, 115, 63], [133, 62, 115, 64, "path"], [133, 66, 115, 68], [133, 68, 115, 70, "configRef"], [133, 77, 115, 79], [133, 78, 115, 80, "current"], [133, 85, 115, 87], [133, 86, 115, 88], [133, 89, 115, 91, "undefined"], [133, 98, 115, 100], [134, 4, 116, 4], [134, 5, 116, 5], [134, 7, 116, 7], [134, 9, 116, 9], [134, 10, 116, 10], [135, 4, 117, 4], [135, 8, 117, 10, "getInitialState"], [135, 23, 117, 25], [135, 26, 117, 28, "React"], [135, 31, 117, 33], [135, 32, 117, 34, "useCallback"], [135, 43, 117, 45], [135, 44, 117, 46], [135, 50, 117, 52], [136, 6, 118, 8], [136, 10, 118, 12, "state"], [136, 15, 118, 17], [137, 6, 119, 8], [137, 10, 119, 12, "enabledRef"], [137, 20, 119, 22], [137, 21, 119, 23, "current"], [137, 28, 119, 30], [137, 30, 119, 32], [138, 8, 120, 12], [138, 12, 120, 18, "url"], [138, 15, 120, 21], [138, 18, 120, 24, "getInitialURLRef"], [138, 34, 120, 40], [138, 35, 120, 41, "current"], [138, 42, 120, 48], [138, 43, 120, 49], [138, 44, 120, 50], [139, 8, 121, 12], [139, 12, 121, 16, "url"], [139, 15, 121, 19], [139, 19, 121, 23], [139, 23, 121, 27], [139, 25, 121, 29], [140, 10, 122, 16], [140, 14, 122, 20], [140, 21, 122, 27, "url"], [140, 24, 122, 30], [140, 29, 122, 35], [140, 37, 122, 43], [140, 39, 122, 45], [141, 12, 123, 20], [141, 19, 123, 27, "url"], [141, 22, 123, 30], [141, 23, 123, 31, "then"], [141, 27, 123, 35], [141, 28, 123, 37, "url"], [141, 31, 123, 40], [141, 35, 123, 45], [142, 14, 124, 24], [142, 18, 124, 30, "state"], [142, 23, 124, 35], [142, 26, 124, 38, "getStateFromURL"], [142, 41, 124, 53], [142, 42, 124, 54, "url"], [142, 45, 124, 57], [142, 46, 124, 58], [143, 14, 125, 24], [143, 18, 125, 28], [143, 25, 125, 35, "url"], [143, 28, 125, 38], [143, 33, 125, 43], [143, 41, 125, 51], [143, 43, 125, 53], [144, 16, 126, 28], [145, 16, 127, 28, "onUnhandledLinking"], [145, 34, 127, 46], [145, 35, 127, 47], [145, 36, 127, 48], [145, 37, 127, 49], [145, 39, 127, 51, "extractPathFromURL_1"], [145, 59, 127, 71], [145, 60, 127, 72, "extractExpoPathFromURL"], [145, 82, 127, 94], [145, 84, 127, 96, "prefixes"], [145, 92, 127, 104], [145, 94, 127, 106, "url"], [145, 97, 127, 109], [145, 98, 127, 110], [145, 99, 127, 111], [146, 14, 128, 24], [147, 14, 129, 24], [147, 21, 129, 31, "state"], [147, 26, 129, 36], [148, 12, 130, 20], [148, 13, 130, 21], [148, 14, 130, 22], [149, 10, 131, 16], [149, 11, 131, 17], [149, 17, 132, 21], [150, 12, 133, 20, "onUnhandledLinking"], [150, 30, 133, 38], [150, 31, 133, 39], [150, 32, 133, 40], [150, 33, 133, 41], [150, 35, 133, 43, "extractPathFromURL_1"], [150, 55, 133, 63], [150, 56, 133, 64, "extractExpoPathFromURL"], [150, 78, 133, 86], [150, 80, 133, 88, "prefixes"], [150, 88, 133, 96], [150, 90, 133, 98, "url"], [150, 93, 133, 101], [150, 94, 133, 102], [150, 95, 133, 103], [151, 10, 134, 16], [152, 8, 135, 12], [153, 8, 136, 12, "state"], [153, 13, 136, 17], [153, 16, 136, 20, "getStateFromURL"], [153, 31, 136, 35], [153, 32, 136, 36, "url"], [153, 35, 136, 39], [153, 36, 136, 40], [154, 6, 137, 8], [155, 6, 138, 8], [155, 10, 138, 14, "thenable"], [155, 18, 138, 22], [155, 21, 138, 25], [156, 8, 139, 12, "then"], [156, 12, 139, 16, "then"], [156, 13, 139, 17, "onfulfilled"], [156, 24, 139, 28], [156, 26, 139, 30], [157, 10, 140, 16], [157, 17, 140, 23, "Promise"], [157, 24, 140, 30], [157, 25, 140, 31, "resolve"], [157, 32, 140, 38], [157, 33, 140, 39, "onfulfilled"], [157, 44, 140, 50], [157, 47, 140, 53, "onfulfilled"], [157, 58, 140, 64], [157, 59, 140, 65, "state"], [157, 64, 140, 70], [157, 65, 140, 71], [157, 68, 140, 74, "state"], [157, 73, 140, 79], [157, 74, 140, 80], [158, 8, 141, 12], [158, 9, 141, 13], [159, 8, 142, 12, "catch"], [159, 13, 142, 17, "catch"], [159, 14, 142, 17], [159, 16, 142, 20], [160, 10, 143, 16], [160, 17, 143, 23, "thenable"], [160, 25, 143, 31], [161, 8, 144, 12], [162, 6, 145, 8], [162, 7, 145, 9], [163, 6, 146, 8], [163, 13, 146, 15, "thenable"], [163, 21, 146, 23], [164, 4, 147, 4], [164, 5, 147, 5], [164, 7, 147, 7], [164, 8, 147, 8, "getStateFromURL"], [164, 23, 147, 23], [164, 25, 147, 25, "onUnhandledLinking"], [164, 43, 147, 43], [164, 45, 147, 45, "prefixes"], [164, 53, 147, 53], [164, 54, 147, 54], [164, 55, 147, 55], [165, 4, 148, 4, "React"], [165, 9, 148, 9], [165, 10, 148, 10, "useEffect"], [165, 19, 148, 19], [165, 20, 148, 20], [165, 26, 148, 26], [166, 6, 149, 8], [166, 10, 149, 14, "listener"], [166, 18, 149, 22], [166, 21, 149, 26, "url"], [166, 24, 149, 29], [166, 28, 149, 34], [167, 8, 150, 12], [167, 12, 150, 16], [167, 13, 150, 17, "enabled"], [167, 20, 150, 24], [167, 22, 150, 26], [168, 10, 151, 16], [169, 8, 152, 12], [170, 8, 153, 12], [170, 12, 153, 18, "navigation"], [170, 22, 153, 28], [170, 25, 153, 31, "ref"], [170, 28, 153, 34], [170, 29, 153, 35, "current"], [170, 36, 153, 42], [171, 8, 154, 12], [171, 12, 154, 18, "state"], [171, 17, 154, 23], [171, 20, 154, 26, "navigation"], [171, 30, 154, 36], [171, 33, 154, 39, "getStateFromURL"], [171, 48, 154, 54], [171, 49, 154, 55, "url"], [171, 52, 154, 58], [171, 53, 154, 59], [171, 56, 154, 62, "undefined"], [171, 65, 154, 71], [172, 8, 155, 12], [172, 12, 155, 16, "navigation"], [172, 22, 155, 26], [172, 26, 155, 30, "state"], [172, 31, 155, 35], [172, 33, 155, 37], [173, 10, 156, 16], [174, 10, 157, 16, "onUnhandledLinking"], [174, 28, 157, 34], [174, 29, 157, 35], [174, 30, 157, 36], [174, 31, 157, 37], [174, 33, 157, 39, "extractPathFromURL_1"], [174, 53, 157, 59], [174, 54, 157, 60, "extractExpoPathFromURL"], [174, 76, 157, 82], [174, 78, 157, 84, "prefixes"], [174, 86, 157, 92], [174, 88, 157, 94, "url"], [174, 91, 157, 97], [174, 92, 157, 98], [174, 93, 157, 99], [175, 10, 158, 16], [175, 14, 158, 22, "rootState"], [175, 23, 158, 31], [175, 26, 158, 34, "navigation"], [175, 36, 158, 44], [175, 37, 158, 45, "getRootState"], [175, 49, 158, 57], [175, 50, 158, 58], [175, 51, 158, 59], [176, 10, 159, 16], [176, 14, 159, 20, "state"], [176, 19, 159, 25], [176, 20, 159, 26, "routes"], [176, 26, 159, 32], [176, 27, 159, 33, "some"], [176, 31, 159, 37], [176, 32, 159, 39, "r"], [176, 33, 159, 40], [176, 37, 159, 45], [176, 38, 159, 46, "rootState"], [176, 47, 159, 55], [176, 49, 159, 57, "routeNames"], [176, 59, 159, 67], [176, 60, 159, 68, "includes"], [176, 68, 159, 76], [176, 69, 159, 77, "r"], [176, 70, 159, 78], [176, 71, 159, 79, "name"], [176, 75, 159, 83], [176, 76, 159, 84], [176, 77, 159, 85], [176, 79, 159, 87], [177, 12, 160, 20], [178, 10, 161, 16], [179, 10, 162, 16], [179, 14, 162, 22, "action"], [179, 20, 162, 28], [179, 23, 162, 31, "getActionFromStateRef"], [179, 44, 162, 52], [179, 45, 162, 53, "current"], [179, 52, 162, 60], [179, 53, 162, 61, "state"], [179, 58, 162, 66], [179, 60, 162, 68, "configRef"], [179, 69, 162, 77], [179, 70, 162, 78, "current"], [179, 77, 162, 85], [179, 78, 162, 86], [180, 10, 163, 16], [180, 14, 163, 20, "action"], [180, 20, 163, 26], [180, 25, 163, 31, "undefined"], [180, 34, 163, 40], [180, 36, 163, 42], [181, 12, 164, 20], [181, 16, 164, 24], [182, 14, 165, 24, "navigation"], [182, 24, 165, 34], [182, 25, 165, 35, "dispatch"], [182, 33, 165, 43], [182, 34, 165, 44, "action"], [182, 40, 165, 50], [182, 41, 165, 51], [183, 12, 166, 20], [183, 13, 166, 21], [183, 14, 167, 20], [183, 21, 167, 27, "e"], [183, 22, 167, 28], [183, 24, 167, 30], [184, 14, 168, 24], [185, 14, 169, 24], [186, 14, 170, 24, "console"], [186, 21, 170, 31], [186, 22, 170, 32, "warn"], [186, 26, 170, 36], [186, 27, 170, 37], [186, 80, 170, 90, "url"], [186, 83, 170, 93], [186, 89, 170, 99], [186, 96, 170, 106, "e"], [186, 97, 170, 107], [186, 102, 170, 112], [186, 110, 170, 120], [186, 114, 170, 124, "e"], [186, 115, 170, 125], [186, 119, 170, 129], [186, 123, 170, 133], [186, 127, 170, 137], [186, 136, 170, 146], [186, 140, 170, 150, "e"], [186, 141, 170, 151], [186, 144, 170, 154, "e"], [186, 145, 170, 155], [186, 146, 170, 156, "message"], [186, 153, 170, 163], [186, 156, 170, 166, "e"], [186, 157, 170, 167], [186, 159, 170, 169], [186, 160, 170, 170], [187, 12, 171, 20], [188, 10, 172, 16], [188, 11, 172, 17], [188, 17, 173, 21], [189, 12, 174, 20, "navigation"], [189, 22, 174, 30], [189, 23, 174, 31, "resetRoot"], [189, 32, 174, 40], [189, 33, 174, 41, "state"], [189, 38, 174, 46], [189, 39, 174, 47], [190, 10, 175, 16], [191, 8, 176, 12], [192, 6, 177, 8], [192, 7, 177, 9], [193, 6, 178, 8], [193, 13, 178, 15, "subscribe"], [193, 22, 178, 24], [193, 23, 178, 25, "listener"], [193, 31, 178, 33], [193, 32, 178, 34], [194, 4, 179, 4], [194, 5, 179, 5], [194, 7, 179, 7], [194, 8, 179, 8, "enabled"], [194, 15, 179, 15], [194, 17, 179, 17, "getStateFromURL"], [194, 32, 179, 32], [194, 34, 179, 34, "onUnhandledLinking"], [194, 52, 179, 52], [194, 54, 179, 54, "prefixes"], [194, 62, 179, 62], [194, 64, 179, 64, "ref"], [194, 67, 179, 67], [194, 69, 179, 69, "subscribe"], [194, 78, 179, 78], [194, 79, 179, 79], [194, 80, 179, 80], [195, 4, 180, 4], [195, 11, 180, 11], [196, 6, 181, 8, "getInitialState"], [197, 4, 182, 4], [197, 5, 182, 5], [198, 2, 183, 0], [199, 2, 184, 0], [199, 11, 184, 9, "getInitialURLWithTimeout"], [199, 35, 184, 33, "getInitialURLWithTimeout"], [199, 36, 184, 33], [199, 38, 184, 36], [200, 4, 185, 4], [200, 8, 185, 8], [200, 15, 185, 15, "window"], [200, 21, 185, 21], [200, 26, 185, 26], [200, 37, 185, 37], [200, 39, 185, 39], [201, 6, 186, 8], [201, 13, 186, 15], [201, 15, 186, 17], [202, 4, 187, 4], [202, 5, 187, 5], [202, 11, 188, 9], [202, 15, 188, 13, "react_native_1"], [202, 29, 188, 27], [202, 30, 188, 28, "Platform"], [202, 38, 188, 36], [202, 39, 188, 37, "OS"], [202, 41, 188, 39], [202, 46, 188, 44], [202, 51, 188, 49], [202, 53, 188, 51], [203, 6, 189, 8], [204, 6, 190, 8], [204, 13, 190, 15, "ExpoLinking"], [204, 24, 190, 26], [204, 25, 190, 27, "getLinkingURL"], [204, 38, 190, 40], [204, 39, 190, 41], [204, 40, 190, 42], [205, 4, 191, 4], [206, 4, 192, 4], [206, 11, 192, 11, "Promise"], [206, 18, 192, 18], [206, 19, 192, 19, "race"], [206, 23, 192, 23], [206, 24, 192, 24], [207, 4, 193, 8], [208, 4, 194, 8, "react_native_1"], [208, 18, 194, 22], [208, 19, 194, 23, "Linking"], [208, 26, 194, 30], [208, 27, 194, 31, "getInitialURL"], [208, 40, 194, 44], [208, 41, 194, 45], [208, 42, 194, 46], [208, 44, 195, 8], [208, 48, 195, 12, "Promise"], [208, 55, 195, 19], [208, 56, 195, 21, "resolve"], [208, 63, 195, 28], [209, 4, 196, 8], [210, 4, 197, 8], [211, 4, 198, 8, "setTimeout"], [211, 14, 198, 18], [211, 15, 198, 19], [211, 21, 198, 25, "resolve"], [211, 28, 198, 32], [211, 29, 198, 33], [211, 33, 198, 37], [211, 34, 198, 38], [211, 36, 198, 40], [211, 39, 198, 43], [211, 40, 198, 44], [211, 41, 198, 45], [211, 42, 199, 5], [211, 43, 199, 6], [212, 2, 200, 0], [213, 0, 200, 1], [213, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "desc.get", "ownKeys", "useLinking", "callback", "React.useEffect$argument_0", "getStateFromURL", "getInitialState", "url.then$argument_0", "thenable.then", "thenable._catch", "listener", "state.routes.some$argument_0", "getInitialURLWithTimeout", "Promise$argument_0", "setTimeout$argument_0"], "mappings": "AAA;0ECC;sCCI,2BD;CDG,KC;CDG;gFCC;CDE,IC;CDE;mDCC;kBEC;gDFC;SEI;KFE;CDQ;AIU,qFH,gCG,cH;qBIC,0BJ;CGc;oBEE;eLuB;SKK;KFC;oBEW;KFQ;8CGC;KHM;8CIC;oCCM;qBDO;YES;aFE;YGC;aHE;KJG;oBEC;yBMC;sCCU,8CD;SNkB;KFE;CJI;AcC;oBCW;mBCG,mBD,MD;CdE"}}, "type": "js/module"}]}