{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.VertexMode = void 0;\n  let VertexMode = exports.VertexMode = /*#__PURE__*/function (VertexMode) {\n    VertexMode[VertexMode[\"Triangles\"] = 0] = \"Triangles\";\n    VertexMode[VertexMode[\"TriangleStrip\"] = 1] = \"TriangleStrip\";\n    VertexMode[VertexMode[\"TriangleFan\"] = 2] = \"TriangleFan\";\n    return VertexMode;\n  }({});\n});", "lineCount": 12, "map": [[6, 2, 1, 7], [6, 6, 1, 11, "VertexMode"], [6, 16, 1, 21], [6, 19, 1, 21, "exports"], [6, 26, 1, 21], [6, 27, 1, 21, "VertexMode"], [6, 37, 1, 21], [6, 40, 1, 24], [6, 53, 1, 37], [6, 63, 1, 47, "VertexMode"], [6, 73, 1, 57], [6, 75, 1, 59], [7, 4, 2, 2, "VertexMode"], [7, 14, 2, 12], [7, 15, 2, 13, "VertexMode"], [7, 25, 2, 23], [7, 26, 2, 24], [7, 37, 2, 35], [7, 38, 2, 36], [7, 41, 2, 39], [7, 42, 2, 40], [7, 43, 2, 41], [7, 46, 2, 44], [7, 57, 2, 55], [8, 4, 3, 2, "VertexMode"], [8, 14, 3, 12], [8, 15, 3, 13, "VertexMode"], [8, 25, 3, 23], [8, 26, 3, 24], [8, 41, 3, 39], [8, 42, 3, 40], [8, 45, 3, 43], [8, 46, 3, 44], [8, 47, 3, 45], [8, 50, 3, 48], [8, 65, 3, 63], [9, 4, 4, 2, "VertexMode"], [9, 14, 4, 12], [9, 15, 4, 13, "VertexMode"], [9, 25, 4, 23], [9, 26, 4, 24], [9, 39, 4, 37], [9, 40, 4, 38], [9, 43, 4, 41], [9, 44, 4, 42], [9, 45, 4, 43], [9, 48, 4, 46], [9, 61, 4, 59], [10, 4, 5, 2], [10, 11, 5, 9, "VertexMode"], [10, 21, 5, 19], [11, 2, 6, 0], [11, 3, 6, 1], [11, 4, 6, 2], [11, 5, 6, 3], [11, 6, 6, 4], [11, 7, 6, 5], [12, 0, 6, 6], [12, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA,qCC;CDK"}}, "type": "js/module"}]}