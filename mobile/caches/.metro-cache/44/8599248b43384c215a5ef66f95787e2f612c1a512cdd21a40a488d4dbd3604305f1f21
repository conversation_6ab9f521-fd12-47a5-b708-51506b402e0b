{"dependencies": [{"name": "query-string", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 44, "index": 59}}], "key": "Tk6zkk+/XfK89igjx1MNWAcG1Q8=", "exportNames": ["*"]}}, {"name": "./getPatternParts.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 60}, "end": {"line": 4, "column": 55, "index": 115}}], "key": "ZBgxhB8YmJlp1KxhfM5ocx9xUB0=", "exportNames": ["*"]}}, {"name": "./validatePathConfig.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 116}, "end": {"line": 5, "column": 61, "index": 177}}], "key": "zoUaUuxQP5qPvRrno+lraXgTGfI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getPathFromState = getPathFromState;\n  var queryString = _interopRequireWildcard(require(_dependencyMap[0], \"query-string\"));\n  var _getPatternParts = require(_dependencyMap[1], \"./getPatternParts.js\");\n  var _validatePathConfig = require(_dependencyMap[2], \"./validatePathConfig.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const getActiveRoute = state => {\n    const route = typeof state.index === 'number' ? state.routes[state.index] : state.routes[state.routes.length - 1];\n    if (route.state) {\n      return getActiveRoute(route.state);\n    }\n    return route;\n  };\n  const cachedNormalizedConfigs = new WeakMap();\n  const getNormalizedConfigs = options => {\n    if (!options?.screens) return {};\n    const cached = cachedNormalizedConfigs.get(options?.screens);\n    if (cached) return cached;\n    const normalizedConfigs = createNormalizedConfigs(options.screens);\n    cachedNormalizedConfigs.set(options.screens, normalizedConfigs);\n    return normalizedConfigs;\n  };\n\n  /**\n   * Utility to serialize a navigation state object to a path string.\n   *\n   * @example\n   * ```js\n   * getPathFromState(\n   *   {\n   *     routes: [\n   *       {\n   *         name: 'Chat',\n   *         params: { author: 'Jane', id: 42 },\n   *       },\n   *     ],\n   *   },\n   *   {\n   *     screens: {\n   *       Chat: {\n   *         path: 'chat/:author/:id',\n   *         stringify: { author: author => author.toLowerCase() }\n   *       }\n   *     }\n   *   }\n   * )\n   * ```\n   *\n   * @param state Navigation state to serialize.\n   * @param options Extra options to fine-tune how to serialize the path.\n   * @returns Path representing the state, e.g. /foo/bar?count=42.\n   */\n  function getPathFromState(state, options) {\n    if (state == null) {\n      throw Error(`Got '${String(state)}' for the navigation state. You must pass a valid state object.`);\n    }\n    if (options) {\n      (0, _validatePathConfig.validatePathConfig)(options);\n    }\n    const configs = getNormalizedConfigs(options);\n    let path = '/';\n    let current = state;\n    const allParams = {};\n    while (current) {\n      let index = typeof current.index === 'number' ? current.index : 0;\n      let route = current.routes[index];\n      let parts;\n      let focusedParams;\n      let currentOptions = configs;\n      const focusedRoute = getActiveRoute(state);\n\n      // Keep all the route names that appeared during going deeper in config in case the pattern is resolved to undefined\n      const nestedRouteNames = [];\n      let hasNext = true;\n      while (route.name in currentOptions && hasNext) {\n        parts = currentOptions[route.name].parts;\n        nestedRouteNames.push(route.name);\n        if (route.params) {\n          const options = currentOptions[route.name];\n          const currentParams = Object.fromEntries(Object.entries(route.params).map(([key, value]) => {\n            if (value === undefined) {\n              if (options) {\n                const optional = options.parts?.find(part => part.param === key)?.optional;\n                if (optional) {\n                  return null;\n                }\n              } else {\n                return null;\n              }\n            }\n            const stringify = options?.stringify?.[key] ?? String;\n            return [key, stringify(value)];\n          }).filter(entry => entry != null));\n          if (parts?.length) {\n            Object.assign(allParams, currentParams);\n          }\n          if (focusedRoute === route) {\n            // If this is the focused route, keep the params for later use\n            // We save it here since it's been stringified already\n            focusedParams = {\n              ...currentParams\n            };\n            parts\n            // eslint-disable-next-line no-loop-func\n            ?.forEach(({\n              param\n            }) => {\n              if (param) {\n                // Remove the params present in the pattern since we'll only use the rest for query string\n                if (focusedParams) {\n                  // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n                  delete focusedParams[param];\n                }\n              }\n            });\n          }\n        }\n\n        // If there is no `screens` property or no nested state, we return pattern\n        if (!currentOptions[route.name].screens || route.state === undefined) {\n          hasNext = false;\n        } else {\n          index = typeof route.state.index === 'number' ? route.state.index : route.state.routes.length - 1;\n          const nextRoute = route.state.routes[index];\n          const nestedConfig = currentOptions[route.name].screens;\n\n          // if there is config for next route name, we go deeper\n          if (nestedConfig && nextRoute.name in nestedConfig) {\n            route = nextRoute;\n            currentOptions = nestedConfig;\n          } else {\n            // If not, there is no sense in going deeper in config\n            hasNext = false;\n          }\n        }\n      }\n      if (currentOptions[route.name] !== undefined) {\n        path += parts?.map(({\n          segment,\n          param,\n          optional\n        }) => {\n          // We don't know what to show for wildcard patterns\n          // Showing the route name seems ok, though whatever we show here will be incorrect\n          // Since the page doesn't actually exist\n          if (segment === '*') {\n            return route.name;\n          }\n\n          // If the path has a pattern for a param, put the param in the path\n          if (param) {\n            const value = allParams[param];\n            if (value === undefined && optional) {\n              // Optional params without value assigned in route.params should be ignored\n              return '';\n            }\n\n            // Valid characters according to\n            // https://datatracker.ietf.org/doc/html/rfc3986#section-3.3 (see pchar definition)\n            return String(value).replace(/[^A-Za-z0-9\\-._~!$&'()*+,;=:@]/g, char => encodeURIComponent(char));\n          }\n          return encodeURIComponent(segment);\n        }).join('/');\n      } else {\n        path += encodeURIComponent(route.name);\n      }\n      if (!focusedParams && focusedRoute.params) {\n        focusedParams = Object.fromEntries(Object.entries(focusedRoute.params).map(([key, value]) => [key, String(value)]));\n      }\n      if (route.state) {\n        path += '/';\n      } else if (focusedParams) {\n        for (const param in focusedParams) {\n          if (focusedParams[param] === 'undefined') {\n            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n            delete focusedParams[param];\n          }\n        }\n        const query = queryString.stringify(focusedParams, {\n          sort: false\n        });\n        if (query) {\n          path += `?${query}`;\n        }\n      }\n      current = route.state;\n    }\n\n    // Include the root path if specified\n    if (options?.path) {\n      path = `${options.path}/${path}`;\n    }\n\n    // Remove multiple as well as trailing slashes\n    path = path.replace(/\\/+/g, '/');\n    path = path.length > 1 ? path.replace(/\\/$/, '') : path;\n\n    // If path doesn't start with a slash, add it\n    // This makes sure that history.pushState will update the path correctly instead of appending\n    if (!path.startsWith('/')) {\n      path = `/${path}`;\n    }\n    return path;\n  }\n  const createConfigItem = (config, parentParts) => {\n    if (typeof config === 'string') {\n      // If a string is specified as the value of the key(e.g. Foo: '/path'), use it as the pattern\n      const parts = (0, _getPatternParts.getPatternParts)(config);\n      if (parentParts) {\n        return {\n          parts: [...parentParts, ...parts]\n        };\n      }\n      return {\n        parts\n      };\n    }\n    if (config.exact && config.path === undefined) {\n      throw new Error(\"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\");\n    }\n\n    // If an object is specified as the value (e.g. Foo: { ... }),\n    // It can have `path` property and `screens` prop which has nested configs\n    const parts = config.exact !== true ? [...(parentParts || []), ...(config.path ? (0, _getPatternParts.getPatternParts)(config.path) : [])] : config.path ? (0, _getPatternParts.getPatternParts)(config.path) : undefined;\n    const screens = config.screens ? createNormalizedConfigs(config.screens, parts) : undefined;\n    return {\n      parts,\n      stringify: config.stringify,\n      screens\n    };\n  };\n  const createNormalizedConfigs = (options, parts) => Object.fromEntries(Object.entries(options).map(([name, c]) => {\n    const result = createConfigItem(c, parts);\n    return [name, result];\n  }));\n});", "lineCount": 241, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getPathFromState"], [7, 26, 1, 13], [7, 29, 1, 13, "getPathFromState"], [7, 45, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "queryString"], [8, 17, 3, 0], [8, 20, 3, 0, "_interopRequireWildcard"], [8, 43, 3, 0], [8, 44, 3, 0, "require"], [8, 51, 3, 0], [8, 52, 3, 0, "_dependencyMap"], [8, 66, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_getPatternParts"], [9, 22, 4, 0], [9, 25, 4, 0, "require"], [9, 32, 4, 0], [9, 33, 4, 0, "_dependencyMap"], [9, 47, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_validatePathConfig"], [10, 25, 5, 0], [10, 28, 5, 0, "require"], [10, 35, 5, 0], [10, 36, 5, 0, "_dependencyMap"], [10, 50, 5, 0], [11, 2, 5, 61], [11, 11, 5, 61, "_interopRequireWildcard"], [11, 35, 5, 61, "e"], [11, 36, 5, 61], [11, 38, 5, 61, "t"], [11, 39, 5, 61], [11, 68, 5, 61, "WeakMap"], [11, 75, 5, 61], [11, 81, 5, 61, "r"], [11, 82, 5, 61], [11, 89, 5, 61, "WeakMap"], [11, 96, 5, 61], [11, 100, 5, 61, "n"], [11, 101, 5, 61], [11, 108, 5, 61, "WeakMap"], [11, 115, 5, 61], [11, 127, 5, 61, "_interopRequireWildcard"], [11, 150, 5, 61], [11, 162, 5, 61, "_interopRequireWildcard"], [11, 163, 5, 61, "e"], [11, 164, 5, 61], [11, 166, 5, 61, "t"], [11, 167, 5, 61], [11, 176, 5, 61, "t"], [11, 177, 5, 61], [11, 181, 5, 61, "e"], [11, 182, 5, 61], [11, 186, 5, 61, "e"], [11, 187, 5, 61], [11, 188, 5, 61, "__esModule"], [11, 198, 5, 61], [11, 207, 5, 61, "e"], [11, 208, 5, 61], [11, 214, 5, 61, "o"], [11, 215, 5, 61], [11, 217, 5, 61, "i"], [11, 218, 5, 61], [11, 220, 5, 61, "f"], [11, 221, 5, 61], [11, 226, 5, 61, "__proto__"], [11, 235, 5, 61], [11, 243, 5, 61, "default"], [11, 250, 5, 61], [11, 252, 5, 61, "e"], [11, 253, 5, 61], [11, 270, 5, 61, "e"], [11, 271, 5, 61], [11, 294, 5, 61, "e"], [11, 295, 5, 61], [11, 320, 5, 61, "e"], [11, 321, 5, 61], [11, 330, 5, 61, "f"], [11, 331, 5, 61], [11, 337, 5, 61, "o"], [11, 338, 5, 61], [11, 341, 5, 61, "t"], [11, 342, 5, 61], [11, 345, 5, 61, "n"], [11, 346, 5, 61], [11, 349, 5, 61, "r"], [11, 350, 5, 61], [11, 358, 5, 61, "o"], [11, 359, 5, 61], [11, 360, 5, 61, "has"], [11, 363, 5, 61], [11, 364, 5, 61, "e"], [11, 365, 5, 61], [11, 375, 5, 61, "o"], [11, 376, 5, 61], [11, 377, 5, 61, "get"], [11, 380, 5, 61], [11, 381, 5, 61, "e"], [11, 382, 5, 61], [11, 385, 5, 61, "o"], [11, 386, 5, 61], [11, 387, 5, 61, "set"], [11, 390, 5, 61], [11, 391, 5, 61, "e"], [11, 392, 5, 61], [11, 394, 5, 61, "f"], [11, 395, 5, 61], [11, 411, 5, 61, "t"], [11, 412, 5, 61], [11, 416, 5, 61, "e"], [11, 417, 5, 61], [11, 433, 5, 61, "t"], [11, 434, 5, 61], [11, 441, 5, 61, "hasOwnProperty"], [11, 455, 5, 61], [11, 456, 5, 61, "call"], [11, 460, 5, 61], [11, 461, 5, 61, "e"], [11, 462, 5, 61], [11, 464, 5, 61, "t"], [11, 465, 5, 61], [11, 472, 5, 61, "i"], [11, 473, 5, 61], [11, 477, 5, 61, "o"], [11, 478, 5, 61], [11, 481, 5, 61, "Object"], [11, 487, 5, 61], [11, 488, 5, 61, "defineProperty"], [11, 502, 5, 61], [11, 507, 5, 61, "Object"], [11, 513, 5, 61], [11, 514, 5, 61, "getOwnPropertyDescriptor"], [11, 538, 5, 61], [11, 539, 5, 61, "e"], [11, 540, 5, 61], [11, 542, 5, 61, "t"], [11, 543, 5, 61], [11, 550, 5, 61, "i"], [11, 551, 5, 61], [11, 552, 5, 61, "get"], [11, 555, 5, 61], [11, 559, 5, 61, "i"], [11, 560, 5, 61], [11, 561, 5, 61, "set"], [11, 564, 5, 61], [11, 568, 5, 61, "o"], [11, 569, 5, 61], [11, 570, 5, 61, "f"], [11, 571, 5, 61], [11, 573, 5, 61, "t"], [11, 574, 5, 61], [11, 576, 5, 61, "i"], [11, 577, 5, 61], [11, 581, 5, 61, "f"], [11, 582, 5, 61], [11, 583, 5, 61, "t"], [11, 584, 5, 61], [11, 588, 5, 61, "e"], [11, 589, 5, 61], [11, 590, 5, 61, "t"], [11, 591, 5, 61], [11, 602, 5, 61, "f"], [11, 603, 5, 61], [11, 608, 5, 61, "e"], [11, 609, 5, 61], [11, 611, 5, 61, "t"], [11, 612, 5, 61], [12, 2, 6, 0], [12, 8, 6, 6, "getActiveRoute"], [12, 22, 6, 20], [12, 25, 6, 23, "state"], [12, 30, 6, 28], [12, 34, 6, 32], [13, 4, 7, 2], [13, 10, 7, 8, "route"], [13, 15, 7, 13], [13, 18, 7, 16], [13, 25, 7, 23, "state"], [13, 30, 7, 28], [13, 31, 7, 29, "index"], [13, 36, 7, 34], [13, 41, 7, 39], [13, 49, 7, 47], [13, 52, 7, 50, "state"], [13, 57, 7, 55], [13, 58, 7, 56, "routes"], [13, 64, 7, 62], [13, 65, 7, 63, "state"], [13, 70, 7, 68], [13, 71, 7, 69, "index"], [13, 76, 7, 74], [13, 77, 7, 75], [13, 80, 7, 78, "state"], [13, 85, 7, 83], [13, 86, 7, 84, "routes"], [13, 92, 7, 90], [13, 93, 7, 91, "state"], [13, 98, 7, 96], [13, 99, 7, 97, "routes"], [13, 105, 7, 103], [13, 106, 7, 104, "length"], [13, 112, 7, 110], [13, 115, 7, 113], [13, 116, 7, 114], [13, 117, 7, 115], [14, 4, 8, 2], [14, 8, 8, 6, "route"], [14, 13, 8, 11], [14, 14, 8, 12, "state"], [14, 19, 8, 17], [14, 21, 8, 19], [15, 6, 9, 4], [15, 13, 9, 11, "getActiveRoute"], [15, 27, 9, 25], [15, 28, 9, 26, "route"], [15, 33, 9, 31], [15, 34, 9, 32, "state"], [15, 39, 9, 37], [15, 40, 9, 38], [16, 4, 10, 2], [17, 4, 11, 2], [17, 11, 11, 9, "route"], [17, 16, 11, 14], [18, 2, 12, 0], [18, 3, 12, 1], [19, 2, 13, 0], [19, 8, 13, 6, "cachedNormalizedConfigs"], [19, 31, 13, 29], [19, 34, 13, 32], [19, 38, 13, 36, "WeakMap"], [19, 45, 13, 43], [19, 46, 13, 44], [19, 47, 13, 45], [20, 2, 14, 0], [20, 8, 14, 6, "getNormalizedConfigs"], [20, 28, 14, 26], [20, 31, 14, 29, "options"], [20, 38, 14, 36], [20, 42, 14, 40], [21, 4, 15, 2], [21, 8, 15, 6], [21, 9, 15, 7, "options"], [21, 16, 15, 14], [21, 18, 15, 16, "screens"], [21, 25, 15, 23], [21, 27, 15, 25], [21, 34, 15, 32], [21, 35, 15, 33], [21, 36, 15, 34], [22, 4, 16, 2], [22, 10, 16, 8, "cached"], [22, 16, 16, 14], [22, 19, 16, 17, "cachedNormalizedConfigs"], [22, 42, 16, 40], [22, 43, 16, 41, "get"], [22, 46, 16, 44], [22, 47, 16, 45, "options"], [22, 54, 16, 52], [22, 56, 16, 54, "screens"], [22, 63, 16, 61], [22, 64, 16, 62], [23, 4, 17, 2], [23, 8, 17, 6, "cached"], [23, 14, 17, 12], [23, 16, 17, 14], [23, 23, 17, 21, "cached"], [23, 29, 17, 27], [24, 4, 18, 2], [24, 10, 18, 8, "normalizedConfigs"], [24, 27, 18, 25], [24, 30, 18, 28, "createNormalizedConfigs"], [24, 53, 18, 51], [24, 54, 18, 52, "options"], [24, 61, 18, 59], [24, 62, 18, 60, "screens"], [24, 69, 18, 67], [24, 70, 18, 68], [25, 4, 19, 2, "cachedNormalizedConfigs"], [25, 27, 19, 25], [25, 28, 19, 26, "set"], [25, 31, 19, 29], [25, 32, 19, 30, "options"], [25, 39, 19, 37], [25, 40, 19, 38, "screens"], [25, 47, 19, 45], [25, 49, 19, 47, "normalizedConfigs"], [25, 66, 19, 64], [25, 67, 19, 65], [26, 4, 20, 2], [26, 11, 20, 9, "normalizedConfigs"], [26, 28, 20, 26], [27, 2, 21, 0], [27, 3, 21, 1], [29, 2, 23, 0], [30, 0, 24, 0], [31, 0, 25, 0], [32, 0, 26, 0], [33, 0, 27, 0], [34, 0, 28, 0], [35, 0, 29, 0], [36, 0, 30, 0], [37, 0, 31, 0], [38, 0, 32, 0], [39, 0, 33, 0], [40, 0, 34, 0], [41, 0, 35, 0], [42, 0, 36, 0], [43, 0, 37, 0], [44, 0, 38, 0], [45, 0, 39, 0], [46, 0, 40, 0], [47, 0, 41, 0], [48, 0, 42, 0], [49, 0, 43, 0], [50, 0, 44, 0], [51, 0, 45, 0], [52, 0, 46, 0], [53, 0, 47, 0], [54, 0, 48, 0], [55, 0, 49, 0], [56, 0, 50, 0], [57, 0, 51, 0], [58, 2, 52, 7], [58, 11, 52, 16, "getPathFromState"], [58, 27, 52, 32, "getPathFromState"], [58, 28, 52, 33, "state"], [58, 33, 52, 38], [58, 35, 52, 40, "options"], [58, 42, 52, 47], [58, 44, 52, 49], [59, 4, 53, 2], [59, 8, 53, 6, "state"], [59, 13, 53, 11], [59, 17, 53, 15], [59, 21, 53, 19], [59, 23, 53, 21], [60, 6, 54, 4], [60, 12, 54, 10, "Error"], [60, 17, 54, 15], [60, 18, 54, 16], [60, 26, 54, 24, "String"], [60, 32, 54, 30], [60, 33, 54, 31, "state"], [60, 38, 54, 36], [60, 39, 54, 37], [60, 104, 54, 102], [60, 105, 54, 103], [61, 4, 55, 2], [62, 4, 56, 2], [62, 8, 56, 6, "options"], [62, 15, 56, 13], [62, 17, 56, 15], [63, 6, 57, 4], [63, 10, 57, 4, "validatePathConfig"], [63, 48, 57, 22], [63, 50, 57, 23, "options"], [63, 57, 57, 30], [63, 58, 57, 31], [64, 4, 58, 2], [65, 4, 59, 2], [65, 10, 59, 8, "configs"], [65, 17, 59, 15], [65, 20, 59, 18, "getNormalizedConfigs"], [65, 40, 59, 38], [65, 41, 59, 39, "options"], [65, 48, 59, 46], [65, 49, 59, 47], [66, 4, 60, 2], [66, 8, 60, 6, "path"], [66, 12, 60, 10], [66, 15, 60, 13], [66, 18, 60, 16], [67, 4, 61, 2], [67, 8, 61, 6, "current"], [67, 15, 61, 13], [67, 18, 61, 16, "state"], [67, 23, 61, 21], [68, 4, 62, 2], [68, 10, 62, 8, "allParams"], [68, 19, 62, 17], [68, 22, 62, 20], [68, 23, 62, 21], [68, 24, 62, 22], [69, 4, 63, 2], [69, 11, 63, 9, "current"], [69, 18, 63, 16], [69, 20, 63, 18], [70, 6, 64, 4], [70, 10, 64, 8, "index"], [70, 15, 64, 13], [70, 18, 64, 16], [70, 25, 64, 23, "current"], [70, 32, 64, 30], [70, 33, 64, 31, "index"], [70, 38, 64, 36], [70, 43, 64, 41], [70, 51, 64, 49], [70, 54, 64, 52, "current"], [70, 61, 64, 59], [70, 62, 64, 60, "index"], [70, 67, 64, 65], [70, 70, 64, 68], [70, 71, 64, 69], [71, 6, 65, 4], [71, 10, 65, 8, "route"], [71, 15, 65, 13], [71, 18, 65, 16, "current"], [71, 25, 65, 23], [71, 26, 65, 24, "routes"], [71, 32, 65, 30], [71, 33, 65, 31, "index"], [71, 38, 65, 36], [71, 39, 65, 37], [72, 6, 66, 4], [72, 10, 66, 8, "parts"], [72, 15, 66, 13], [73, 6, 67, 4], [73, 10, 67, 8, "focusedParams"], [73, 23, 67, 21], [74, 6, 68, 4], [74, 10, 68, 8, "currentOptions"], [74, 24, 68, 22], [74, 27, 68, 25, "configs"], [74, 34, 68, 32], [75, 6, 69, 4], [75, 12, 69, 10, "focusedRoute"], [75, 24, 69, 22], [75, 27, 69, 25, "getActiveRoute"], [75, 41, 69, 39], [75, 42, 69, 40, "state"], [75, 47, 69, 45], [75, 48, 69, 46], [77, 6, 71, 4], [78, 6, 72, 4], [78, 12, 72, 10, "nestedRouteNames"], [78, 28, 72, 26], [78, 31, 72, 29], [78, 33, 72, 31], [79, 6, 73, 4], [79, 10, 73, 8, "hasNext"], [79, 17, 73, 15], [79, 20, 73, 18], [79, 24, 73, 22], [80, 6, 74, 4], [80, 13, 74, 11, "route"], [80, 18, 74, 16], [80, 19, 74, 17, "name"], [80, 23, 74, 21], [80, 27, 74, 25, "currentOptions"], [80, 41, 74, 39], [80, 45, 74, 43, "hasNext"], [80, 52, 74, 50], [80, 54, 74, 52], [81, 8, 75, 6, "parts"], [81, 13, 75, 11], [81, 16, 75, 14, "currentOptions"], [81, 30, 75, 28], [81, 31, 75, 29, "route"], [81, 36, 75, 34], [81, 37, 75, 35, "name"], [81, 41, 75, 39], [81, 42, 75, 40], [81, 43, 75, 41, "parts"], [81, 48, 75, 46], [82, 8, 76, 6, "nestedRouteNames"], [82, 24, 76, 22], [82, 25, 76, 23, "push"], [82, 29, 76, 27], [82, 30, 76, 28, "route"], [82, 35, 76, 33], [82, 36, 76, 34, "name"], [82, 40, 76, 38], [82, 41, 76, 39], [83, 8, 77, 6], [83, 12, 77, 10, "route"], [83, 17, 77, 15], [83, 18, 77, 16, "params"], [83, 24, 77, 22], [83, 26, 77, 24], [84, 10, 78, 8], [84, 16, 78, 14, "options"], [84, 23, 78, 21], [84, 26, 78, 24, "currentOptions"], [84, 40, 78, 38], [84, 41, 78, 39, "route"], [84, 46, 78, 44], [84, 47, 78, 45, "name"], [84, 51, 78, 49], [84, 52, 78, 50], [85, 10, 79, 8], [85, 16, 79, 14, "currentParams"], [85, 29, 79, 27], [85, 32, 79, 30, "Object"], [85, 38, 79, 36], [85, 39, 79, 37, "fromEntries"], [85, 50, 79, 48], [85, 51, 79, 49, "Object"], [85, 57, 79, 55], [85, 58, 79, 56, "entries"], [85, 65, 79, 63], [85, 66, 79, 64, "route"], [85, 71, 79, 69], [85, 72, 79, 70, "params"], [85, 78, 79, 76], [85, 79, 79, 77], [85, 80, 79, 78, "map"], [85, 83, 79, 81], [85, 84, 79, 82], [85, 85, 79, 83], [85, 86, 79, 84, "key"], [85, 89, 79, 87], [85, 91, 79, 89, "value"], [85, 96, 79, 94], [85, 97, 79, 95], [85, 102, 79, 100], [86, 12, 80, 10], [86, 16, 80, 14, "value"], [86, 21, 80, 19], [86, 26, 80, 24, "undefined"], [86, 35, 80, 33], [86, 37, 80, 35], [87, 14, 81, 12], [87, 18, 81, 16, "options"], [87, 25, 81, 23], [87, 27, 81, 25], [88, 16, 82, 14], [88, 22, 82, 20, "optional"], [88, 30, 82, 28], [88, 33, 82, 31, "options"], [88, 40, 82, 38], [88, 41, 82, 39, "parts"], [88, 46, 82, 44], [88, 48, 82, 46, "find"], [88, 52, 82, 50], [88, 53, 82, 51, "part"], [88, 57, 82, 55], [88, 61, 82, 59, "part"], [88, 65, 82, 63], [88, 66, 82, 64, "param"], [88, 71, 82, 69], [88, 76, 82, 74, "key"], [88, 79, 82, 77], [88, 80, 82, 78], [88, 82, 82, 80, "optional"], [88, 90, 82, 88], [89, 16, 83, 14], [89, 20, 83, 18, "optional"], [89, 28, 83, 26], [89, 30, 83, 28], [90, 18, 84, 16], [90, 25, 84, 23], [90, 29, 84, 27], [91, 16, 85, 14], [92, 14, 86, 12], [92, 15, 86, 13], [92, 21, 86, 19], [93, 16, 87, 14], [93, 23, 87, 21], [93, 27, 87, 25], [94, 14, 88, 12], [95, 12, 89, 10], [96, 12, 90, 10], [96, 18, 90, 16, "stringify"], [96, 27, 90, 25], [96, 30, 90, 28, "options"], [96, 37, 90, 35], [96, 39, 90, 37, "stringify"], [96, 48, 90, 46], [96, 51, 90, 49, "key"], [96, 54, 90, 52], [96, 55, 90, 53], [96, 59, 90, 57, "String"], [96, 65, 90, 63], [97, 12, 91, 10], [97, 19, 91, 17], [97, 20, 91, 18, "key"], [97, 23, 91, 21], [97, 25, 91, 23, "stringify"], [97, 34, 91, 32], [97, 35, 91, 33, "value"], [97, 40, 91, 38], [97, 41, 91, 39], [97, 42, 91, 40], [98, 10, 92, 8], [98, 11, 92, 9], [98, 12, 92, 10], [98, 13, 92, 11, "filter"], [98, 19, 92, 17], [98, 20, 92, 18, "entry"], [98, 25, 92, 23], [98, 29, 92, 27, "entry"], [98, 34, 92, 32], [98, 38, 92, 36], [98, 42, 92, 40], [98, 43, 92, 41], [98, 44, 92, 42], [99, 10, 93, 8], [99, 14, 93, 12, "parts"], [99, 19, 93, 17], [99, 21, 93, 19, "length"], [99, 27, 93, 25], [99, 29, 93, 27], [100, 12, 94, 10, "Object"], [100, 18, 94, 16], [100, 19, 94, 17, "assign"], [100, 25, 94, 23], [100, 26, 94, 24, "allParams"], [100, 35, 94, 33], [100, 37, 94, 35, "currentParams"], [100, 50, 94, 48], [100, 51, 94, 49], [101, 10, 95, 8], [102, 10, 96, 8], [102, 14, 96, 12, "focusedRoute"], [102, 26, 96, 24], [102, 31, 96, 29, "route"], [102, 36, 96, 34], [102, 38, 96, 36], [103, 12, 97, 10], [104, 12, 98, 10], [105, 12, 99, 10, "focusedParams"], [105, 25, 99, 23], [105, 28, 99, 26], [106, 14, 100, 12], [106, 17, 100, 15, "currentParams"], [107, 12, 101, 10], [107, 13, 101, 11], [108, 12, 102, 10, "parts"], [109, 12, 103, 10], [110, 12, 103, 10], [110, 14, 104, 12, "for<PERSON>ach"], [110, 21, 104, 19], [110, 22, 104, 20], [110, 23, 104, 21], [111, 14, 105, 12, "param"], [112, 12, 106, 10], [112, 13, 106, 11], [112, 18, 106, 16], [113, 14, 107, 12], [113, 18, 107, 16, "param"], [113, 23, 107, 21], [113, 25, 107, 23], [114, 16, 108, 14], [115, 16, 109, 14], [115, 20, 109, 18, "focusedParams"], [115, 33, 109, 31], [115, 35, 109, 33], [116, 18, 110, 16], [117, 18, 111, 16], [117, 25, 111, 23, "focusedParams"], [117, 38, 111, 36], [117, 39, 111, 37, "param"], [117, 44, 111, 42], [117, 45, 111, 43], [118, 16, 112, 14], [119, 14, 113, 12], [120, 12, 114, 10], [120, 13, 114, 11], [120, 14, 114, 12], [121, 10, 115, 8], [122, 8, 116, 6], [124, 8, 118, 6], [125, 8, 119, 6], [125, 12, 119, 10], [125, 13, 119, 11, "currentOptions"], [125, 27, 119, 25], [125, 28, 119, 26, "route"], [125, 33, 119, 31], [125, 34, 119, 32, "name"], [125, 38, 119, 36], [125, 39, 119, 37], [125, 40, 119, 38, "screens"], [125, 47, 119, 45], [125, 51, 119, 49, "route"], [125, 56, 119, 54], [125, 57, 119, 55, "state"], [125, 62, 119, 60], [125, 67, 119, 65, "undefined"], [125, 76, 119, 74], [125, 78, 119, 76], [126, 10, 120, 8, "hasNext"], [126, 17, 120, 15], [126, 20, 120, 18], [126, 25, 120, 23], [127, 8, 121, 6], [127, 9, 121, 7], [127, 15, 121, 13], [128, 10, 122, 8, "index"], [128, 15, 122, 13], [128, 18, 122, 16], [128, 25, 122, 23, "route"], [128, 30, 122, 28], [128, 31, 122, 29, "state"], [128, 36, 122, 34], [128, 37, 122, 35, "index"], [128, 42, 122, 40], [128, 47, 122, 45], [128, 55, 122, 53], [128, 58, 122, 56, "route"], [128, 63, 122, 61], [128, 64, 122, 62, "state"], [128, 69, 122, 67], [128, 70, 122, 68, "index"], [128, 75, 122, 73], [128, 78, 122, 76, "route"], [128, 83, 122, 81], [128, 84, 122, 82, "state"], [128, 89, 122, 87], [128, 90, 122, 88, "routes"], [128, 96, 122, 94], [128, 97, 122, 95, "length"], [128, 103, 122, 101], [128, 106, 122, 104], [128, 107, 122, 105], [129, 10, 123, 8], [129, 16, 123, 14, "nextRoute"], [129, 25, 123, 23], [129, 28, 123, 26, "route"], [129, 33, 123, 31], [129, 34, 123, 32, "state"], [129, 39, 123, 37], [129, 40, 123, 38, "routes"], [129, 46, 123, 44], [129, 47, 123, 45, "index"], [129, 52, 123, 50], [129, 53, 123, 51], [130, 10, 124, 8], [130, 16, 124, 14, "nestedConfig"], [130, 28, 124, 26], [130, 31, 124, 29, "currentOptions"], [130, 45, 124, 43], [130, 46, 124, 44, "route"], [130, 51, 124, 49], [130, 52, 124, 50, "name"], [130, 56, 124, 54], [130, 57, 124, 55], [130, 58, 124, 56, "screens"], [130, 65, 124, 63], [132, 10, 126, 8], [133, 10, 127, 8], [133, 14, 127, 12, "nestedConfig"], [133, 26, 127, 24], [133, 30, 127, 28, "nextRoute"], [133, 39, 127, 37], [133, 40, 127, 38, "name"], [133, 44, 127, 42], [133, 48, 127, 46, "nestedConfig"], [133, 60, 127, 58], [133, 62, 127, 60], [134, 12, 128, 10, "route"], [134, 17, 128, 15], [134, 20, 128, 18, "nextRoute"], [134, 29, 128, 27], [135, 12, 129, 10, "currentOptions"], [135, 26, 129, 24], [135, 29, 129, 27, "nestedConfig"], [135, 41, 129, 39], [136, 10, 130, 8], [136, 11, 130, 9], [136, 17, 130, 15], [137, 12, 131, 10], [138, 12, 132, 10, "hasNext"], [138, 19, 132, 17], [138, 22, 132, 20], [138, 27, 132, 25], [139, 10, 133, 8], [140, 8, 134, 6], [141, 6, 135, 4], [142, 6, 136, 4], [142, 10, 136, 8, "currentOptions"], [142, 24, 136, 22], [142, 25, 136, 23, "route"], [142, 30, 136, 28], [142, 31, 136, 29, "name"], [142, 35, 136, 33], [142, 36, 136, 34], [142, 41, 136, 39, "undefined"], [142, 50, 136, 48], [142, 52, 136, 50], [143, 8, 137, 6, "path"], [143, 12, 137, 10], [143, 16, 137, 14, "parts"], [143, 21, 137, 19], [143, 23, 137, 21, "map"], [143, 26, 137, 24], [143, 27, 137, 25], [143, 28, 137, 26], [144, 10, 138, 8, "segment"], [144, 17, 138, 15], [145, 10, 139, 8, "param"], [145, 15, 139, 13], [146, 10, 140, 8, "optional"], [147, 8, 141, 6], [147, 9, 141, 7], [147, 14, 141, 12], [148, 10, 142, 8], [149, 10, 143, 8], [150, 10, 144, 8], [151, 10, 145, 8], [151, 14, 145, 12, "segment"], [151, 21, 145, 19], [151, 26, 145, 24], [151, 29, 145, 27], [151, 31, 145, 29], [152, 12, 146, 10], [152, 19, 146, 17, "route"], [152, 24, 146, 22], [152, 25, 146, 23, "name"], [152, 29, 146, 27], [153, 10, 147, 8], [155, 10, 149, 8], [156, 10, 150, 8], [156, 14, 150, 12, "param"], [156, 19, 150, 17], [156, 21, 150, 19], [157, 12, 151, 10], [157, 18, 151, 16, "value"], [157, 23, 151, 21], [157, 26, 151, 24, "allParams"], [157, 35, 151, 33], [157, 36, 151, 34, "param"], [157, 41, 151, 39], [157, 42, 151, 40], [158, 12, 152, 10], [158, 16, 152, 14, "value"], [158, 21, 152, 19], [158, 26, 152, 24, "undefined"], [158, 35, 152, 33], [158, 39, 152, 37, "optional"], [158, 47, 152, 45], [158, 49, 152, 47], [159, 14, 153, 12], [160, 14, 154, 12], [160, 21, 154, 19], [160, 23, 154, 21], [161, 12, 155, 10], [163, 12, 157, 10], [164, 12, 158, 10], [165, 12, 159, 10], [165, 19, 159, 17, "String"], [165, 25, 159, 23], [165, 26, 159, 24, "value"], [165, 31, 159, 29], [165, 32, 159, 30], [165, 33, 159, 31, "replace"], [165, 40, 159, 38], [165, 41, 159, 39], [165, 74, 159, 72], [165, 76, 159, 74, "char"], [165, 80, 159, 78], [165, 84, 159, 82, "encodeURIComponent"], [165, 102, 159, 100], [165, 103, 159, 101, "char"], [165, 107, 159, 105], [165, 108, 159, 106], [165, 109, 159, 107], [166, 10, 160, 8], [167, 10, 161, 8], [167, 17, 161, 15, "encodeURIComponent"], [167, 35, 161, 33], [167, 36, 161, 34, "segment"], [167, 43, 161, 41], [167, 44, 161, 42], [168, 8, 162, 6], [168, 9, 162, 7], [168, 10, 162, 8], [168, 11, 162, 9, "join"], [168, 15, 162, 13], [168, 16, 162, 14], [168, 19, 162, 17], [168, 20, 162, 18], [169, 6, 163, 4], [169, 7, 163, 5], [169, 13, 163, 11], [170, 8, 164, 6, "path"], [170, 12, 164, 10], [170, 16, 164, 14, "encodeURIComponent"], [170, 34, 164, 32], [170, 35, 164, 33, "route"], [170, 40, 164, 38], [170, 41, 164, 39, "name"], [170, 45, 164, 43], [170, 46, 164, 44], [171, 6, 165, 4], [172, 6, 166, 4], [172, 10, 166, 8], [172, 11, 166, 9, "focusedParams"], [172, 24, 166, 22], [172, 28, 166, 26, "focusedRoute"], [172, 40, 166, 38], [172, 41, 166, 39, "params"], [172, 47, 166, 45], [172, 49, 166, 47], [173, 8, 167, 6, "focusedParams"], [173, 21, 167, 19], [173, 24, 167, 22, "Object"], [173, 30, 167, 28], [173, 31, 167, 29, "fromEntries"], [173, 42, 167, 40], [173, 43, 167, 41, "Object"], [173, 49, 167, 47], [173, 50, 167, 48, "entries"], [173, 57, 167, 55], [173, 58, 167, 56, "focusedRoute"], [173, 70, 167, 68], [173, 71, 167, 69, "params"], [173, 77, 167, 75], [173, 78, 167, 76], [173, 79, 167, 77, "map"], [173, 82, 167, 80], [173, 83, 167, 81], [173, 84, 167, 82], [173, 85, 167, 83, "key"], [173, 88, 167, 86], [173, 90, 167, 88, "value"], [173, 95, 167, 93], [173, 96, 167, 94], [173, 101, 167, 99], [173, 102, 167, 100, "key"], [173, 105, 167, 103], [173, 107, 167, 105, "String"], [173, 113, 167, 111], [173, 114, 167, 112, "value"], [173, 119, 167, 117], [173, 120, 167, 118], [173, 121, 167, 119], [173, 122, 167, 120], [173, 123, 167, 121], [174, 6, 168, 4], [175, 6, 169, 4], [175, 10, 169, 8, "route"], [175, 15, 169, 13], [175, 16, 169, 14, "state"], [175, 21, 169, 19], [175, 23, 169, 21], [176, 8, 170, 6, "path"], [176, 12, 170, 10], [176, 16, 170, 14], [176, 19, 170, 17], [177, 6, 171, 4], [177, 7, 171, 5], [177, 13, 171, 11], [177, 17, 171, 15, "focusedParams"], [177, 30, 171, 28], [177, 32, 171, 30], [178, 8, 172, 6], [178, 13, 172, 11], [178, 19, 172, 17, "param"], [178, 24, 172, 22], [178, 28, 172, 26, "focusedParams"], [178, 41, 172, 39], [178, 43, 172, 41], [179, 10, 173, 8], [179, 14, 173, 12, "focusedParams"], [179, 27, 173, 25], [179, 28, 173, 26, "param"], [179, 33, 173, 31], [179, 34, 173, 32], [179, 39, 173, 37], [179, 50, 173, 48], [179, 52, 173, 50], [180, 12, 174, 10], [181, 12, 175, 10], [181, 19, 175, 17, "focusedParams"], [181, 32, 175, 30], [181, 33, 175, 31, "param"], [181, 38, 175, 36], [181, 39, 175, 37], [182, 10, 176, 8], [183, 8, 177, 6], [184, 8, 178, 6], [184, 14, 178, 12, "query"], [184, 19, 178, 17], [184, 22, 178, 20, "queryString"], [184, 33, 178, 31], [184, 34, 178, 32, "stringify"], [184, 43, 178, 41], [184, 44, 178, 42, "focusedParams"], [184, 57, 178, 55], [184, 59, 178, 57], [185, 10, 179, 8, "sort"], [185, 14, 179, 12], [185, 16, 179, 14], [186, 8, 180, 6], [186, 9, 180, 7], [186, 10, 180, 8], [187, 8, 181, 6], [187, 12, 181, 10, "query"], [187, 17, 181, 15], [187, 19, 181, 17], [188, 10, 182, 8, "path"], [188, 14, 182, 12], [188, 18, 182, 16], [188, 22, 182, 20, "query"], [188, 27, 182, 25], [188, 29, 182, 27], [189, 8, 183, 6], [190, 6, 184, 4], [191, 6, 185, 4, "current"], [191, 13, 185, 11], [191, 16, 185, 14, "route"], [191, 21, 185, 19], [191, 22, 185, 20, "state"], [191, 27, 185, 25], [192, 4, 186, 2], [194, 4, 188, 2], [195, 4, 189, 2], [195, 8, 189, 6, "options"], [195, 15, 189, 13], [195, 17, 189, 15, "path"], [195, 21, 189, 19], [195, 23, 189, 21], [196, 6, 190, 4, "path"], [196, 10, 190, 8], [196, 13, 190, 11], [196, 16, 190, 14, "options"], [196, 23, 190, 21], [196, 24, 190, 22, "path"], [196, 28, 190, 26], [196, 32, 190, 30, "path"], [196, 36, 190, 34], [196, 38, 190, 36], [197, 4, 191, 2], [199, 4, 193, 2], [200, 4, 194, 2, "path"], [200, 8, 194, 6], [200, 11, 194, 9, "path"], [200, 15, 194, 13], [200, 16, 194, 14, "replace"], [200, 23, 194, 21], [200, 24, 194, 22], [200, 30, 194, 28], [200, 32, 194, 30], [200, 35, 194, 33], [200, 36, 194, 34], [201, 4, 195, 2, "path"], [201, 8, 195, 6], [201, 11, 195, 9, "path"], [201, 15, 195, 13], [201, 16, 195, 14, "length"], [201, 22, 195, 20], [201, 25, 195, 23], [201, 26, 195, 24], [201, 29, 195, 27, "path"], [201, 33, 195, 31], [201, 34, 195, 32, "replace"], [201, 41, 195, 39], [201, 42, 195, 40], [201, 47, 195, 45], [201, 49, 195, 47], [201, 51, 195, 49], [201, 52, 195, 50], [201, 55, 195, 53, "path"], [201, 59, 195, 57], [203, 4, 197, 2], [204, 4, 198, 2], [205, 4, 199, 2], [205, 8, 199, 6], [205, 9, 199, 7, "path"], [205, 13, 199, 11], [205, 14, 199, 12, "startsWith"], [205, 24, 199, 22], [205, 25, 199, 23], [205, 28, 199, 26], [205, 29, 199, 27], [205, 31, 199, 29], [206, 6, 200, 4, "path"], [206, 10, 200, 8], [206, 13, 200, 11], [206, 17, 200, 15, "path"], [206, 21, 200, 19], [206, 23, 200, 21], [207, 4, 201, 2], [208, 4, 202, 2], [208, 11, 202, 9, "path"], [208, 15, 202, 13], [209, 2, 203, 0], [210, 2, 204, 0], [210, 8, 204, 6, "createConfigItem"], [210, 24, 204, 22], [210, 27, 204, 25, "createConfigItem"], [210, 28, 204, 26, "config"], [210, 34, 204, 32], [210, 36, 204, 34, "parentParts"], [210, 47, 204, 45], [210, 52, 204, 50], [211, 4, 205, 2], [211, 8, 205, 6], [211, 15, 205, 13, "config"], [211, 21, 205, 19], [211, 26, 205, 24], [211, 34, 205, 32], [211, 36, 205, 34], [212, 6, 206, 4], [213, 6, 207, 4], [213, 12, 207, 10, "parts"], [213, 17, 207, 15], [213, 20, 207, 18], [213, 24, 207, 18, "getPatternParts"], [213, 56, 207, 33], [213, 58, 207, 34, "config"], [213, 64, 207, 40], [213, 65, 207, 41], [214, 6, 208, 4], [214, 10, 208, 8, "parentParts"], [214, 21, 208, 19], [214, 23, 208, 21], [215, 8, 209, 6], [215, 15, 209, 13], [216, 10, 210, 8, "parts"], [216, 15, 210, 13], [216, 17, 210, 15], [216, 18, 210, 16], [216, 21, 210, 19, "parentParts"], [216, 32, 210, 30], [216, 34, 210, 32], [216, 37, 210, 35, "parts"], [216, 42, 210, 40], [217, 8, 211, 6], [217, 9, 211, 7], [218, 6, 212, 4], [219, 6, 213, 4], [219, 13, 213, 11], [220, 8, 214, 6, "parts"], [221, 6, 215, 4], [221, 7, 215, 5], [222, 4, 216, 2], [223, 4, 217, 2], [223, 8, 217, 6, "config"], [223, 14, 217, 12], [223, 15, 217, 13, "exact"], [223, 20, 217, 18], [223, 24, 217, 22, "config"], [223, 30, 217, 28], [223, 31, 217, 29, "path"], [223, 35, 217, 33], [223, 40, 217, 38, "undefined"], [223, 49, 217, 47], [223, 51, 217, 49], [224, 6, 218, 4], [224, 12, 218, 10], [224, 16, 218, 14, "Error"], [224, 21, 218, 19], [224, 22, 218, 20], [224, 172, 218, 170], [224, 173, 218, 171], [225, 4, 219, 2], [227, 4, 221, 2], [228, 4, 222, 2], [229, 4, 223, 2], [229, 10, 223, 8, "parts"], [229, 15, 223, 13], [229, 18, 223, 16, "config"], [229, 24, 223, 22], [229, 25, 223, 23, "exact"], [229, 30, 223, 28], [229, 35, 223, 33], [229, 39, 223, 37], [229, 42, 223, 40], [229, 43, 223, 41], [229, 47, 223, 45, "parentParts"], [229, 58, 223, 56], [229, 62, 223, 60], [229, 64, 223, 62], [229, 65, 223, 63], [229, 67, 223, 65], [229, 71, 223, 69, "config"], [229, 77, 223, 75], [229, 78, 223, 76, "path"], [229, 82, 223, 80], [229, 85, 223, 83], [229, 89, 223, 83, "getPatternParts"], [229, 121, 223, 98], [229, 123, 223, 99, "config"], [229, 129, 223, 105], [229, 130, 223, 106, "path"], [229, 134, 223, 110], [229, 135, 223, 111], [229, 138, 223, 114], [229, 140, 223, 116], [229, 141, 223, 117], [229, 142, 223, 118], [229, 145, 223, 121, "config"], [229, 151, 223, 127], [229, 152, 223, 128, "path"], [229, 156, 223, 132], [229, 159, 223, 135], [229, 163, 223, 135, "getPatternParts"], [229, 195, 223, 150], [229, 197, 223, 151, "config"], [229, 203, 223, 157], [229, 204, 223, 158, "path"], [229, 208, 223, 162], [229, 209, 223, 163], [229, 212, 223, 166, "undefined"], [229, 221, 223, 175], [230, 4, 224, 2], [230, 10, 224, 8, "screens"], [230, 17, 224, 15], [230, 20, 224, 18, "config"], [230, 26, 224, 24], [230, 27, 224, 25, "screens"], [230, 34, 224, 32], [230, 37, 224, 35, "createNormalizedConfigs"], [230, 60, 224, 58], [230, 61, 224, 59, "config"], [230, 67, 224, 65], [230, 68, 224, 66, "screens"], [230, 75, 224, 73], [230, 77, 224, 75, "parts"], [230, 82, 224, 80], [230, 83, 224, 81], [230, 86, 224, 84, "undefined"], [230, 95, 224, 93], [231, 4, 225, 2], [231, 11, 225, 9], [232, 6, 226, 4, "parts"], [232, 11, 226, 9], [233, 6, 227, 4, "stringify"], [233, 15, 227, 13], [233, 17, 227, 15, "config"], [233, 23, 227, 21], [233, 24, 227, 22, "stringify"], [233, 33, 227, 31], [234, 6, 228, 4, "screens"], [235, 4, 229, 2], [235, 5, 229, 3], [236, 2, 230, 0], [236, 3, 230, 1], [237, 2, 231, 0], [237, 8, 231, 6, "createNormalizedConfigs"], [237, 31, 231, 29], [237, 34, 231, 32, "createNormalizedConfigs"], [237, 35, 231, 33, "options"], [237, 42, 231, 40], [237, 44, 231, 42, "parts"], [237, 49, 231, 47], [237, 54, 231, 52, "Object"], [237, 60, 231, 58], [237, 61, 231, 59, "fromEntries"], [237, 72, 231, 70], [237, 73, 231, 71, "Object"], [237, 79, 231, 77], [237, 80, 231, 78, "entries"], [237, 87, 231, 85], [237, 88, 231, 86, "options"], [237, 95, 231, 93], [237, 96, 231, 94], [237, 97, 231, 95, "map"], [237, 100, 231, 98], [237, 101, 231, 99], [237, 102, 231, 100], [237, 103, 231, 101, "name"], [237, 107, 231, 105], [237, 109, 231, 107, "c"], [237, 110, 231, 108], [237, 111, 231, 109], [237, 116, 231, 114], [238, 4, 232, 2], [238, 10, 232, 8, "result"], [238, 16, 232, 14], [238, 19, 232, 17, "createConfigItem"], [238, 35, 232, 33], [238, 36, 232, 34, "c"], [238, 37, 232, 35], [238, 39, 232, 37, "parts"], [238, 44, 232, 42], [238, 45, 232, 43], [239, 4, 233, 2], [239, 11, 233, 9], [239, 12, 233, 10, "name"], [239, 16, 233, 14], [239, 18, 233, 16, "result"], [239, 24, 233, 22], [239, 25, 233, 23], [240, 2, 234, 0], [240, 3, 234, 1], [240, 4, 234, 2], [240, 5, 234, 3], [241, 0, 234, 4], [241, 3]], "functionMap": {"names": ["<global>", "getActiveRoute", "getNormalizedConfigs", "getPathFromState", "Object.entries.map$argument_0", "options.parts.find$argument_0", "Object.entries.map.filter$argument_0", "parts.forEach$argument_0", "parts.map$argument_0", "String.replace$argument_1", "createConfigItem", "createNormalizedConfigs"], "mappings": "AAA;uBCK;CDM;6BEE;CFO;OG+B;kFC2B;mDCG,0BD;SDU,SG,sBH;oBIY;WJU;yBKuB;0ECsB,gCD;OLG;iFCK,sCD;CHoC;yBUC;CV0B;gCWC,mEP;COG,EX"}}, "type": "js/module"}]}