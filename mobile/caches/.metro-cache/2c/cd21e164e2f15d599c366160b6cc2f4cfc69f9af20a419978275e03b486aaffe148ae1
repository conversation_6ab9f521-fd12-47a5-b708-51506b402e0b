{"dependencies": [{"name": "./SafeAreaContext", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 23, "index": 117}, "end": {"line": 7, "column": 51, "index": 145}}], "key": "X/SOlGy4gR+Kh9eioeWMkn5axYQ=", "exportNames": ["*"]}}, {"name": "./SafeAreaView", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 20, "index": 488}, "end": {"line": 18, "column": 45, "index": 513}}], "key": "+Ejf0w3jegRKxylMYYyuwGvzJ04=", "exportNames": ["*"]}}, {"name": "./InitialWindow", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 29, "column": 21, "index": 848}, "end": {"line": 29, "column": 47, "index": 874}}], "key": "viiP/RbM9N6IFQtO17WpI3dez0w=", "exportNames": ["*"]}}, {"name": "./SafeArea.types", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 40, "column": 16, "index": 1207}, "end": {"line": 40, "column": 43, "index": 1234}}], "key": "G0YAB1/VIk1s4wSeOdRJp9zQO10=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _SafeAreaContext = require(_dependencyMap[0], \"./SafeAreaContext\");\n  Object.keys(_SafeAreaContext).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _SafeAreaContext[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _SafeAreaContext[key];\n      }\n    });\n  });\n  var _SafeAreaView = require(_dependencyMap[1], \"./SafeAreaView\");\n  Object.keys(_SafeAreaView).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _SafeAreaView[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _SafeAreaView[key];\n      }\n    });\n  });\n  var _InitialWindow = require(_dependencyMap[2], \"./InitialWindow\");\n  Object.keys(_InitialWindow).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _InitialWindow[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _InitialWindow[key];\n      }\n    });\n  });\n  var _SafeArea = require(_dependencyMap[3], \"./SafeArea.types\");\n  Object.keys(_SafeArea).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _SafeArea[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _SafeArea[key];\n      }\n    });\n  });\n});", "lineCount": 52, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 4, 0, "Object"], [5, 8, 4, 6], [5, 9, 4, 7, "defineProperty"], [5, 23, 4, 21], [5, 24, 4, 22, "exports"], [5, 31, 4, 29], [5, 33, 4, 31], [5, 45, 4, 43], [5, 47, 4, 45], [6, 4, 5, 2, "value"], [6, 9, 5, 7], [6, 11, 5, 9], [7, 2, 6, 0], [7, 3, 6, 1], [7, 4, 6, 2], [8, 2, 7, 0], [8, 6, 7, 4, "_SafeAreaContext"], [8, 22, 7, 20], [8, 25, 7, 23, "require"], [8, 32, 7, 30], [8, 33, 7, 30, "_dependencyMap"], [8, 47, 7, 30], [8, 71, 7, 50], [8, 72, 7, 51], [9, 2, 8, 0, "Object"], [9, 8, 8, 6], [9, 9, 8, 7, "keys"], [9, 13, 8, 11], [9, 14, 8, 12, "_SafeAreaContext"], [9, 30, 8, 28], [9, 31, 8, 29], [9, 32, 8, 30, "for<PERSON>ach"], [9, 39, 8, 37], [9, 40, 8, 38], [9, 50, 8, 48, "key"], [9, 53, 8, 51], [9, 55, 8, 53], [10, 4, 9, 2], [10, 8, 9, 6, "key"], [10, 11, 9, 9], [10, 16, 9, 14], [10, 25, 9, 23], [10, 29, 9, 27, "key"], [10, 32, 9, 30], [10, 37, 9, 35], [10, 49, 9, 47], [10, 51, 9, 49], [11, 4, 10, 2], [11, 8, 10, 6, "key"], [11, 11, 10, 9], [11, 15, 10, 13, "exports"], [11, 22, 10, 20], [11, 26, 10, 24, "exports"], [11, 33, 10, 31], [11, 34, 10, 32, "key"], [11, 37, 10, 35], [11, 38, 10, 36], [11, 43, 10, 41, "_SafeAreaContext"], [11, 59, 10, 57], [11, 60, 10, 58, "key"], [11, 63, 10, 61], [11, 64, 10, 62], [11, 66, 10, 64], [12, 4, 11, 2, "Object"], [12, 10, 11, 8], [12, 11, 11, 9, "defineProperty"], [12, 25, 11, 23], [12, 26, 11, 24, "exports"], [12, 33, 11, 31], [12, 35, 11, 33, "key"], [12, 38, 11, 36], [12, 40, 11, 38], [13, 6, 12, 4, "enumerable"], [13, 16, 12, 14], [13, 18, 12, 16], [13, 22, 12, 20], [14, 6, 13, 4, "get"], [14, 9, 13, 7], [14, 11, 13, 9], [14, 20, 13, 9, "get"], [14, 21, 13, 9], [14, 23, 13, 21], [15, 8, 14, 6], [15, 15, 14, 13, "_SafeAreaContext"], [15, 31, 14, 29], [15, 32, 14, 30, "key"], [15, 35, 14, 33], [15, 36, 14, 34], [16, 6, 15, 4], [17, 4, 16, 2], [17, 5, 16, 3], [17, 6, 16, 4], [18, 2, 17, 0], [18, 3, 17, 1], [18, 4, 17, 2], [19, 2, 18, 0], [19, 6, 18, 4, "_SafeAreaView"], [19, 19, 18, 17], [19, 22, 18, 20, "require"], [19, 29, 18, 27], [19, 30, 18, 27, "_dependencyMap"], [19, 44, 18, 27], [19, 65, 18, 44], [19, 66, 18, 45], [20, 2, 19, 0, "Object"], [20, 8, 19, 6], [20, 9, 19, 7, "keys"], [20, 13, 19, 11], [20, 14, 19, 12, "_SafeAreaView"], [20, 27, 19, 25], [20, 28, 19, 26], [20, 29, 19, 27, "for<PERSON>ach"], [20, 36, 19, 34], [20, 37, 19, 35], [20, 47, 19, 45, "key"], [20, 50, 19, 48], [20, 52, 19, 50], [21, 4, 20, 2], [21, 8, 20, 6, "key"], [21, 11, 20, 9], [21, 16, 20, 14], [21, 25, 20, 23], [21, 29, 20, 27, "key"], [21, 32, 20, 30], [21, 37, 20, 35], [21, 49, 20, 47], [21, 51, 20, 49], [22, 4, 21, 2], [22, 8, 21, 6, "key"], [22, 11, 21, 9], [22, 15, 21, 13, "exports"], [22, 22, 21, 20], [22, 26, 21, 24, "exports"], [22, 33, 21, 31], [22, 34, 21, 32, "key"], [22, 37, 21, 35], [22, 38, 21, 36], [22, 43, 21, 41, "_SafeAreaView"], [22, 56, 21, 54], [22, 57, 21, 55, "key"], [22, 60, 21, 58], [22, 61, 21, 59], [22, 63, 21, 61], [23, 4, 22, 2, "Object"], [23, 10, 22, 8], [23, 11, 22, 9, "defineProperty"], [23, 25, 22, 23], [23, 26, 22, 24, "exports"], [23, 33, 22, 31], [23, 35, 22, 33, "key"], [23, 38, 22, 36], [23, 40, 22, 38], [24, 6, 23, 4, "enumerable"], [24, 16, 23, 14], [24, 18, 23, 16], [24, 22, 23, 20], [25, 6, 24, 4, "get"], [25, 9, 24, 7], [25, 11, 24, 9], [25, 20, 24, 9, "get"], [25, 21, 24, 9], [25, 23, 24, 21], [26, 8, 25, 6], [26, 15, 25, 13, "_SafeAreaView"], [26, 28, 25, 26], [26, 29, 25, 27, "key"], [26, 32, 25, 30], [26, 33, 25, 31], [27, 6, 26, 4], [28, 4, 27, 2], [28, 5, 27, 3], [28, 6, 27, 4], [29, 2, 28, 0], [29, 3, 28, 1], [29, 4, 28, 2], [30, 2, 29, 0], [30, 6, 29, 4, "_InitialWindow"], [30, 20, 29, 18], [30, 23, 29, 21, "require"], [30, 30, 29, 28], [30, 31, 29, 28, "_dependencyMap"], [30, 45, 29, 28], [30, 67, 29, 46], [30, 68, 29, 47], [31, 2, 30, 0, "Object"], [31, 8, 30, 6], [31, 9, 30, 7, "keys"], [31, 13, 30, 11], [31, 14, 30, 12, "_InitialWindow"], [31, 28, 30, 26], [31, 29, 30, 27], [31, 30, 30, 28, "for<PERSON>ach"], [31, 37, 30, 35], [31, 38, 30, 36], [31, 48, 30, 46, "key"], [31, 51, 30, 49], [31, 53, 30, 51], [32, 4, 31, 2], [32, 8, 31, 6, "key"], [32, 11, 31, 9], [32, 16, 31, 14], [32, 25, 31, 23], [32, 29, 31, 27, "key"], [32, 32, 31, 30], [32, 37, 31, 35], [32, 49, 31, 47], [32, 51, 31, 49], [33, 4, 32, 2], [33, 8, 32, 6, "key"], [33, 11, 32, 9], [33, 15, 32, 13, "exports"], [33, 22, 32, 20], [33, 26, 32, 24, "exports"], [33, 33, 32, 31], [33, 34, 32, 32, "key"], [33, 37, 32, 35], [33, 38, 32, 36], [33, 43, 32, 41, "_InitialWindow"], [33, 57, 32, 55], [33, 58, 32, 56, "key"], [33, 61, 32, 59], [33, 62, 32, 60], [33, 64, 32, 62], [34, 4, 33, 2, "Object"], [34, 10, 33, 8], [34, 11, 33, 9, "defineProperty"], [34, 25, 33, 23], [34, 26, 33, 24, "exports"], [34, 33, 33, 31], [34, 35, 33, 33, "key"], [34, 38, 33, 36], [34, 40, 33, 38], [35, 6, 34, 4, "enumerable"], [35, 16, 34, 14], [35, 18, 34, 16], [35, 22, 34, 20], [36, 6, 35, 4, "get"], [36, 9, 35, 7], [36, 11, 35, 9], [36, 20, 35, 9, "get"], [36, 21, 35, 9], [36, 23, 35, 21], [37, 8, 36, 6], [37, 15, 36, 13, "_InitialWindow"], [37, 29, 36, 27], [37, 30, 36, 28, "key"], [37, 33, 36, 31], [37, 34, 36, 32], [38, 6, 37, 4], [39, 4, 38, 2], [39, 5, 38, 3], [39, 6, 38, 4], [40, 2, 39, 0], [40, 3, 39, 1], [40, 4, 39, 2], [41, 2, 40, 0], [41, 6, 40, 4, "_SafeArea"], [41, 15, 40, 13], [41, 18, 40, 16, "require"], [41, 25, 40, 23], [41, 26, 40, 23, "_dependencyMap"], [41, 40, 40, 23], [41, 63, 40, 42], [41, 64, 40, 43], [42, 2, 41, 0, "Object"], [42, 8, 41, 6], [42, 9, 41, 7, "keys"], [42, 13, 41, 11], [42, 14, 41, 12, "_SafeArea"], [42, 23, 41, 21], [42, 24, 41, 22], [42, 25, 41, 23, "for<PERSON>ach"], [42, 32, 41, 30], [42, 33, 41, 31], [42, 43, 41, 41, "key"], [42, 46, 41, 44], [42, 48, 41, 46], [43, 4, 42, 2], [43, 8, 42, 6, "key"], [43, 11, 42, 9], [43, 16, 42, 14], [43, 25, 42, 23], [43, 29, 42, 27, "key"], [43, 32, 42, 30], [43, 37, 42, 35], [43, 49, 42, 47], [43, 51, 42, 49], [44, 4, 43, 2], [44, 8, 43, 6, "key"], [44, 11, 43, 9], [44, 15, 43, 13, "exports"], [44, 22, 43, 20], [44, 26, 43, 24, "exports"], [44, 33, 43, 31], [44, 34, 43, 32, "key"], [44, 37, 43, 35], [44, 38, 43, 36], [44, 43, 43, 41, "_SafeArea"], [44, 52, 43, 50], [44, 53, 43, 51, "key"], [44, 56, 43, 54], [44, 57, 43, 55], [44, 59, 43, 57], [45, 4, 44, 2, "Object"], [45, 10, 44, 8], [45, 11, 44, 9, "defineProperty"], [45, 25, 44, 23], [45, 26, 44, 24, "exports"], [45, 33, 44, 31], [45, 35, 44, 33, "key"], [45, 38, 44, 36], [45, 40, 44, 38], [46, 6, 45, 4, "enumerable"], [46, 16, 45, 14], [46, 18, 45, 16], [46, 22, 45, 20], [47, 6, 46, 4, "get"], [47, 9, 46, 7], [47, 11, 46, 9], [47, 20, 46, 9, "get"], [47, 21, 46, 9], [47, 23, 46, 21], [48, 8, 47, 6], [48, 15, 47, 13, "_SafeArea"], [48, 24, 47, 22], [48, 25, 47, 23, "key"], [48, 28, 47, 26], [48, 29, 47, 27], [49, 6, 48, 4], [50, 4, 49, 2], [50, 5, 49, 3], [50, 6, 49, 4], [51, 2, 50, 0], [51, 3, 50, 1], [51, 4, 50, 2], [52, 0, 50, 3], [52, 3]], "functionMap": {"names": ["<global>", "Object.keys.forEach$argument_0", "Object.defineProperty$argument_2.get"], "mappings": "AAA;sCCO;SCK;KDE;CDE;mCCE;SCK;KDE;CDE;oCCE;SCK;KDE;CDE;+BCE;SCK;KDE;CDE"}}, "type": "js/module"}]}