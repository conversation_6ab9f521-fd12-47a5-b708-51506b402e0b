{"dependencies": [{"name": "./flingGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 46, "index": 46}}], "key": "h24ijk3pJCmeakiNLvaxhU4oD+0=", "exportNames": ["*"]}}, {"name": "./forceTouchGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 47}, "end": {"line": 2, "column": 56, "index": 103}}], "key": "1QIx9s/Hb/tSlS4sC64N+Adyv7M=", "exportNames": ["*"]}}, {"name": "./gestureComposition", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 104}, "end": {"line": 3, "column": 94, "index": 198}}], "key": "J0ugy1LMUGf5KgbYvNV+9auzxk4=", "exportNames": ["*"]}}, {"name": "./longPressGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 199}, "end": {"line": 4, "column": 54, "index": 253}}], "key": "1o89s2ZbLCAJzQNlPSeE8o2+cH8=", "exportNames": ["*"]}}, {"name": "./panGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 254}, "end": {"line": 5, "column": 42, "index": 296}}], "key": "aBzYQKsfDy415OM5yEWHEC+JvOM=", "exportNames": ["*"]}}, {"name": "./pinchGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 297}, "end": {"line": 6, "column": 46, "index": 343}}], "key": "Dpg/8aAltyIIC/a4wQAoQVMdkg4=", "exportNames": ["*"]}}, {"name": "./rotationGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 344}, "end": {"line": 7, "column": 52, "index": 396}}], "key": "/7dx2ayCyD336a+OWcSLUeam5aE=", "exportNames": ["*"]}}, {"name": "./tapGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 397}, "end": {"line": 8, "column": 42, "index": 439}}], "key": "0AhBYBLv6GsrGEF0r8aS4Nb6QGo=", "exportNames": ["*"]}}, {"name": "./nativeGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 440}, "end": {"line": 9, "column": 48, "index": 488}}], "key": "cjDWt0y1Cq1VxsEGc4geilXmt0Q=", "exportNames": ["*"]}}, {"name": "./manualGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 489}, "end": {"line": 10, "column": 48, "index": 537}}], "key": "HUWWiRL9QzXRRI3alfUEUrUAkqk=", "exportNames": ["*"]}}, {"name": "./hoverGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 538}, "end": {"line": 11, "column": 46, "index": 584}}], "key": "+OU9Hr4DiheWQiFQL42cwzfCmfI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.GestureObjects = void 0;\n  var _flingGesture = require(_dependencyMap[0], \"./flingGesture\");\n  var _forceTouchGesture = require(_dependencyMap[1], \"./forceTouchGesture\");\n  var _gestureComposition = require(_dependencyMap[2], \"./gestureComposition\");\n  var _longPressGesture = require(_dependencyMap[3], \"./longPressGesture\");\n  var _panGesture = require(_dependencyMap[4], \"./panGesture\");\n  var _pinchGesture = require(_dependencyMap[5], \"./pinchGesture\");\n  var _rotationGesture = require(_dependencyMap[6], \"./rotationGesture\");\n  var _tapGesture = require(_dependencyMap[7], \"./tapGesture\");\n  var _nativeGesture = require(_dependencyMap[8], \"./nativeGesture\");\n  var _manualGesture = require(_dependencyMap[9], \"./manualGesture\");\n  var _hoverGesture = require(_dependencyMap[10], \"./hoverGesture\");\n  /**\n   * `Gesture` is the object that allows you to create and compose gestures.\n   *\n   * ### Remarks\n   * - Consider wrapping your gesture configurations with `useMemo`, as it will reduce the amount of work Gesture Handler has to do under the hood when updating gestures.\n   *\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/gesture\n   */\n\n  const GestureObjects = exports.GestureObjects = {\n    /**\n     * A discrete gesture that recognizes one or many taps.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/tap-gesture\n     */\n    Tap: () => {\n      return new _tapGesture.TapGesture();\n    },\n    /**\n     * A continuous gesture that can recognize a panning (dragging) gesture and track its movement.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture\n     */\n    Pan: () => {\n      return new _panGesture.PanGesture();\n    },\n    /**\n     * A continuous gesture that recognizes pinch gesture. It allows for tracking the distance between two fingers and use that information to scale or zoom your content.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pinch-gesture\n     */\n    Pinch: () => {\n      return new _pinchGesture.PinchGesture();\n    },\n    /**\n     * A continuous gesture that can recognize rotation and track its movement.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/rotation-gesture\n     */\n    Rotation: () => {\n      return new _rotationGesture.RotationGesture();\n    },\n    /**\n     * A discrete gesture that activates when the movement is sufficiently fast.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/fling-gesture\n     */\n    Fling: () => {\n      return new _flingGesture.FlingGesture();\n    },\n    /**\n     * A discrete gesture that activates when the corresponding view is pressed for a sufficiently long time.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/long-press-gesture\n     */\n    LongPress: () => {\n      return new _longPressGesture.LongPressGesture();\n    },\n    /**\n     * #### iOS only\n     * A continuous gesture that recognizes force of a touch. It allows for tracking pressure of touch on some iOS devices.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/force-touch-gesture\n     */\n    ForceTouch: () => {\n      return new _forceTouchGesture.ForceTouchGesture();\n    },\n    /**\n     * A gesture that allows other touch handling components to participate in RNGH's gesture system.\n     * When used, the other component should be the direct child of a `GestureDetector`.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/native-gesture\n     */\n    Native: () => {\n      return new _nativeGesture.NativeGesture();\n    },\n    /**\n     * A plain gesture that has no specific activation criteria nor event data set.\n     * Its state has to be controlled manually using a state manager.\n     * It will not fail when all the pointers are lifted from the screen.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/manual-gesture\n     */\n    Manual: () => {\n      return new _manualGesture.ManualGesture();\n    },\n    /**\n     * A continuous gesture that can recognize hovering above the view it's attached to.\n     * The hover effect may be activated by moving a mouse or a stylus over the view.\n     *\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/hover-gesture\n     */\n    Hover: () => {\n      return new _hoverGesture.HoverGesture();\n    },\n    /**\n     * Builds a composed gesture consisting of gestures provided as parameters.\n     * The first one that becomes active cancels the rest of gestures.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#race\n     */\n    Race: (...gestures) => {\n      return new _gestureComposition.ComposedGesture(...gestures);\n    },\n    /**\n     * Builds a composed gesture that allows all base gestures to run simultaneously.\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#simultaneous\n     */\n    Simultaneous(...gestures) {\n      return new _gestureComposition.SimultaneousGesture(...gestures);\n    },\n    /**\n     * Builds a composed gesture where only one of the provided gestures can become active.\n     * Priority is decided through the order of gestures: the first one has higher priority\n     * than the second one, second one has higher priority than the third one, and so on.\n     * For example, to make a gesture that recognizes both single and double tap you need\n     * to call Exclusive(doubleTap, singleTap).\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#exclusive\n     */\n    Exclusive(...gestures) {\n      return new _gestureComposition.ExclusiveGesture(...gestures);\n    }\n  };\n});", "lineCount": 130, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_flingGesture"], [6, 19, 1, 0], [6, 22, 1, 0, "require"], [6, 29, 1, 0], [6, 30, 1, 0, "_dependencyMap"], [6, 44, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_forceTouchGesture"], [7, 24, 2, 0], [7, 27, 2, 0, "require"], [7, 34, 2, 0], [7, 35, 2, 0, "_dependencyMap"], [7, 49, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_gestureComposition"], [8, 25, 3, 0], [8, 28, 3, 0, "require"], [8, 35, 3, 0], [8, 36, 3, 0, "_dependencyMap"], [8, 50, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_longPressGesture"], [9, 23, 4, 0], [9, 26, 4, 0, "require"], [9, 33, 4, 0], [9, 34, 4, 0, "_dependencyMap"], [9, 48, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_panGesture"], [10, 17, 5, 0], [10, 20, 5, 0, "require"], [10, 27, 5, 0], [10, 28, 5, 0, "_dependencyMap"], [10, 42, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_pinchGesture"], [11, 19, 6, 0], [11, 22, 6, 0, "require"], [11, 29, 6, 0], [11, 30, 6, 0, "_dependencyMap"], [11, 44, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_rotationGesture"], [12, 22, 7, 0], [12, 25, 7, 0, "require"], [12, 32, 7, 0], [12, 33, 7, 0, "_dependencyMap"], [12, 47, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_tapGesture"], [13, 17, 8, 0], [13, 20, 8, 0, "require"], [13, 27, 8, 0], [13, 28, 8, 0, "_dependencyMap"], [13, 42, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_nativeGesture"], [14, 20, 9, 0], [14, 23, 9, 0, "require"], [14, 30, 9, 0], [14, 31, 9, 0, "_dependencyMap"], [14, 45, 9, 0], [15, 2, 10, 0], [15, 6, 10, 0, "_manualGesture"], [15, 20, 10, 0], [15, 23, 10, 0, "require"], [15, 30, 10, 0], [15, 31, 10, 0, "_dependencyMap"], [15, 45, 10, 0], [16, 2, 11, 0], [16, 6, 11, 0, "_hoverGesture"], [16, 19, 11, 0], [16, 22, 11, 0, "require"], [16, 29, 11, 0], [16, 30, 11, 0, "_dependencyMap"], [16, 44, 11, 0], [17, 2, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [26, 2, 21, 7], [26, 8, 21, 13, "GestureObjects"], [26, 22, 21, 27], [26, 25, 21, 27, "exports"], [26, 32, 21, 27], [26, 33, 21, 27, "GestureObjects"], [26, 47, 21, 27], [26, 50, 21, 30], [27, 4, 22, 2], [28, 0, 23, 0], [29, 0, 24, 0], [30, 0, 25, 0], [31, 4, 26, 2, "Tap"], [31, 7, 26, 5], [31, 9, 26, 7, "Tap"], [31, 10, 26, 7], [31, 15, 26, 13], [32, 6, 27, 4], [32, 13, 27, 11], [32, 17, 27, 15, "TapGesture"], [32, 39, 27, 25], [32, 40, 27, 26], [32, 41, 27, 27], [33, 4, 28, 2], [33, 5, 28, 3], [34, 4, 30, 2], [35, 0, 31, 0], [36, 0, 32, 0], [37, 0, 33, 0], [38, 4, 34, 2, "Pan"], [38, 7, 34, 5], [38, 9, 34, 7, "Pan"], [38, 10, 34, 7], [38, 15, 34, 13], [39, 6, 35, 4], [39, 13, 35, 11], [39, 17, 35, 15, "PanGesture"], [39, 39, 35, 25], [39, 40, 35, 26], [39, 41, 35, 27], [40, 4, 36, 2], [40, 5, 36, 3], [41, 4, 38, 2], [42, 0, 39, 0], [43, 0, 40, 0], [44, 0, 41, 0], [45, 4, 42, 2, "Pinch"], [45, 9, 42, 7], [45, 11, 42, 9, "Pinch"], [45, 12, 42, 9], [45, 17, 42, 15], [46, 6, 43, 4], [46, 13, 43, 11], [46, 17, 43, 15, "PinchGesture"], [46, 43, 43, 27], [46, 44, 43, 28], [46, 45, 43, 29], [47, 4, 44, 2], [47, 5, 44, 3], [48, 4, 46, 2], [49, 0, 47, 0], [50, 0, 48, 0], [51, 0, 49, 0], [52, 4, 50, 2, "Rotation"], [52, 12, 50, 10], [52, 14, 50, 12, "Rotation"], [52, 15, 50, 12], [52, 20, 50, 18], [53, 6, 51, 4], [53, 13, 51, 11], [53, 17, 51, 15, "RotationGesture"], [53, 49, 51, 30], [53, 50, 51, 31], [53, 51, 51, 32], [54, 4, 52, 2], [54, 5, 52, 3], [55, 4, 54, 2], [56, 0, 55, 0], [57, 0, 56, 0], [58, 0, 57, 0], [59, 4, 58, 2, "Fling"], [59, 9, 58, 7], [59, 11, 58, 9, "Fling"], [59, 12, 58, 9], [59, 17, 58, 15], [60, 6, 59, 4], [60, 13, 59, 11], [60, 17, 59, 15, "FlingGesture"], [60, 43, 59, 27], [60, 44, 59, 28], [60, 45, 59, 29], [61, 4, 60, 2], [61, 5, 60, 3], [62, 4, 62, 2], [63, 0, 63, 0], [64, 0, 64, 0], [65, 0, 65, 0], [66, 4, 66, 2, "Long<PERSON>ress"], [66, 13, 66, 11], [66, 15, 66, 13, "Long<PERSON>ress"], [66, 16, 66, 13], [66, 21, 66, 19], [67, 6, 67, 4], [67, 13, 67, 11], [67, 17, 67, 15, "LongPressGesture"], [67, 51, 67, 31], [67, 52, 67, 32], [67, 53, 67, 33], [68, 4, 68, 2], [68, 5, 68, 3], [69, 4, 70, 2], [70, 0, 71, 0], [71, 0, 72, 0], [72, 0, 73, 0], [73, 0, 74, 0], [74, 4, 75, 2, "ForceTouch"], [74, 14, 75, 12], [74, 16, 75, 14, "ForceTouch"], [74, 17, 75, 14], [74, 22, 75, 20], [75, 6, 76, 4], [75, 13, 76, 11], [75, 17, 76, 15, "ForceTouchGesture"], [75, 53, 76, 32], [75, 54, 76, 33], [75, 55, 76, 34], [76, 4, 77, 2], [76, 5, 77, 3], [77, 4, 79, 2], [78, 0, 80, 0], [79, 0, 81, 0], [80, 0, 82, 0], [81, 0, 83, 0], [82, 4, 84, 2, "Native"], [82, 10, 84, 8], [82, 12, 84, 10, "Native"], [82, 13, 84, 10], [82, 18, 84, 16], [83, 6, 85, 4], [83, 13, 85, 11], [83, 17, 85, 15, "NativeGesture"], [83, 45, 85, 28], [83, 46, 85, 29], [83, 47, 85, 30], [84, 4, 86, 2], [84, 5, 86, 3], [85, 4, 88, 2], [86, 0, 89, 0], [87, 0, 90, 0], [88, 0, 91, 0], [89, 0, 92, 0], [90, 0, 93, 0], [91, 4, 94, 2, "Manual"], [91, 10, 94, 8], [91, 12, 94, 10, "Manual"], [91, 13, 94, 10], [91, 18, 94, 16], [92, 6, 95, 4], [92, 13, 95, 11], [92, 17, 95, 15, "ManualGesture"], [92, 45, 95, 28], [92, 46, 95, 29], [92, 47, 95, 30], [93, 4, 96, 2], [93, 5, 96, 3], [94, 4, 98, 2], [95, 0, 99, 0], [96, 0, 100, 0], [97, 0, 101, 0], [98, 0, 102, 0], [99, 0, 103, 0], [100, 4, 104, 2, "Hover"], [100, 9, 104, 7], [100, 11, 104, 9, "Hover"], [100, 12, 104, 9], [100, 17, 104, 15], [101, 6, 105, 4], [101, 13, 105, 11], [101, 17, 105, 15, "HoverGesture"], [101, 43, 105, 27], [101, 44, 105, 28], [101, 45, 105, 29], [102, 4, 106, 2], [102, 5, 106, 3], [103, 4, 108, 2], [104, 0, 109, 0], [105, 0, 110, 0], [106, 0, 111, 0], [107, 0, 112, 0], [108, 4, 113, 2, "Race"], [108, 8, 113, 6], [108, 10, 113, 8, "Race"], [108, 11, 113, 9], [108, 14, 113, 12, "gestures"], [108, 22, 113, 20], [108, 27, 113, 25], [109, 6, 114, 4], [109, 13, 114, 11], [109, 17, 114, 15, "ComposedGesture"], [109, 52, 114, 30], [109, 53, 114, 31], [109, 56, 114, 34, "gestures"], [109, 64, 114, 42], [109, 65, 114, 43], [110, 4, 115, 2], [110, 5, 115, 3], [111, 4, 117, 2], [112, 0, 118, 0], [113, 0, 119, 0], [114, 0, 120, 0], [115, 4, 121, 2, "Simultaneous"], [115, 16, 121, 14, "Simultaneous"], [115, 17, 121, 15], [115, 20, 121, 18, "gestures"], [115, 28, 121, 26], [115, 30, 121, 28], [116, 6, 122, 4], [116, 13, 122, 11], [116, 17, 122, 15, "SimultaneousGesture"], [116, 56, 122, 34], [116, 57, 122, 35], [116, 60, 122, 38, "gestures"], [116, 68, 122, 46], [116, 69, 122, 47], [117, 4, 123, 2], [117, 5, 123, 3], [118, 4, 125, 2], [119, 0, 126, 0], [120, 0, 127, 0], [121, 0, 128, 0], [122, 0, 129, 0], [123, 0, 130, 0], [124, 0, 131, 0], [125, 0, 132, 0], [126, 4, 133, 2, "Exclusive"], [126, 13, 133, 11, "Exclusive"], [126, 14, 133, 12], [126, 17, 133, 15, "gestures"], [126, 25, 133, 23], [126, 27, 133, 25], [127, 6, 134, 4], [127, 13, 134, 11], [127, 17, 134, 15, "ExclusiveGesture"], [127, 53, 134, 31], [127, 54, 134, 32], [127, 57, 134, 35, "gestures"], [127, 65, 134, 43], [127, 66, 134, 44], [128, 4, 135, 2], [129, 2, 137, 0], [129, 3, 137, 1], [130, 0, 137, 2], [130, 3]], "functionMap": {"names": ["<global>", "GestureObjects.Tap", "GestureObjects.Pan", "GestureObjects.Pinch", "GestureObjects.Rotation", "GestureObjects.Fling", "GestureObjects.LongPress", "GestureObjects.ForceTouch", "GestureObjects.Native", "GestureObjects.Manual", "GestureObjects.Hover", "GestureObjects.Race", "GestureObjects.Simultaneous", "GestureObjects.Exclusive"], "mappings": "AAA;OCyB;GDE;OEM;GFE;SGM;GHE;YIM;GJE;SKM;GLE;aMM;GNE;cOO;GPE;UQO;GRE;USQ;GTE;SUQ;GVE;QWO;GXE;EYM;GZE;EaU;GbE"}}, "type": "js/module"}]}