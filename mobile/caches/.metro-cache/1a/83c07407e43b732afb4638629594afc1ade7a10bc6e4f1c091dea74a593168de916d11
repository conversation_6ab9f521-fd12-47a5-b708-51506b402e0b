{"dependencies": [{"name": "../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 136}, "end": {"line": 5, "column": 44, "index": 180}}], "key": "ioSJ9iLOtXMo2uBjbVE14/NC9RQ=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 181}, "end": {"line": 6, "column": 46, "index": 227}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Renderer/shims/ReactFabric", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 48, "column": 26, "index": 1340}, "end": {"line": 48, "column": 86, "index": 1400}}], "key": "4C8TqIjR7vX3v3JWvuJOu+XiEA8=", "exportNames": ["*"], "isOptional": true}}, {"name": "react-native/Libraries/Renderer/shims/ReactNative", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 60, "column": 24, "index": 1870}, "end": {"line": 60, "column": 84, "index": 1930}}], "key": "/EeqBiDyBz5H+n0DyFidb4XRXWo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /* eslint-disable camelcase */\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.findHostInstance = findHostInstance;\n  var _errors = require(_dependencyMap[0], \"../errors\");\n  var _PlatformChecker = require(_dependencyMap[1], \"../PlatformChecker\");\n  function findHostInstanceFastPath(maybeNativeRef) {\n    if (!maybeNativeRef) {\n      return undefined;\n    }\n    if (maybeNativeRef.__internalInstanceHandle && maybeNativeRef.__nativeTag && maybeNativeRef._viewConfig) {\n      // This is a native ref to a Fabric component\n      return maybeNativeRef;\n    }\n    if (maybeNativeRef._nativeTag && maybeNativeRef.viewConfig) {\n      // This is a native ref to a Paper component\n      return maybeNativeRef;\n    }\n    // That means it’s a ref to a non-native component, and it’s necessary\n    // to call `findHostInstance_DEPRECATED` on them.\n    return undefined;\n  }\n  function resolveFindHostInstance_DEPRECATED() {\n    if (findHostInstance_DEPRECATED !== undefined) {\n      return;\n    }\n    if ((0, _PlatformChecker.isFabric)()) {\n      try {\n        var ReactFabric = require(_dependencyMap[2], \"react-native/Libraries/Renderer/shims/ReactFabric\");\n        // Since RN 0.77 ReactFabric exports findHostInstance_DEPRECATED in default object so we're trying to\n        // access it first, then fallback on named export\n        findHostInstance_DEPRECATED = ReactFabric?.default?.findHostInstance_DEPRECATED ?? ReactFabric?.findHostInstance_DEPRECATED;\n      } catch (e) {\n        throw new _errors.ReanimatedError('Failed to resolve findHostInstance_DEPRECATED');\n      }\n    } else {\n      var ReactNative = require(_dependencyMap[3], \"react-native/Libraries/Renderer/shims/ReactNative\");\n      // Since RN 0.77 ReactFabric exports findHostInstance_DEPRECATED in default object so we're trying to\n      // access it first, then fallback on named export\n      findHostInstance_DEPRECATED = ReactNative?.default?.findHostInstance_DEPRECATED ?? ReactNative?.findHostInstance_DEPRECATED;\n    }\n  }\n  var findHostInstance_DEPRECATED;\n  function findHostInstance(component) {\n    // Fast path for native refs\n    var hostInstance = findHostInstanceFastPath(component._componentRef);\n    if (hostInstance !== undefined) {\n      return hostInstance;\n    }\n    resolveFindHostInstance_DEPRECATED();\n    /*\n      The Fabric implementation of `findHostInstance_DEPRECATED` requires a React ref as an argument\n      rather than a native ref. If a component implements the `getAnimatableRef` method, it must use \n      the ref provided by this method. It is the component's responsibility to ensure that this is \n      a valid React ref.\n    */\n    return findHostInstance_DEPRECATED(!(0, _PlatformChecker.isFabric)() || component._hasAnimatedRef ? component._componentRef : component);\n  }\n});", "lineCount": 63, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13, "Object"], [5, 8, 2, 13], [5, 9, 2, 13, "defineProperty"], [5, 23, 2, 13], [5, 24, 2, 13, "exports"], [5, 31, 2, 13], [6, 4, 2, 13, "value"], [6, 9, 2, 13], [7, 2, 2, 13], [8, 2, 2, 13, "exports"], [8, 9, 2, 13], [8, 10, 2, 13, "findHostInstance"], [8, 26, 2, 13], [8, 29, 2, 13, "findHostInstance"], [8, 45, 2, 13], [9, 2, 5, 0], [9, 6, 5, 0, "_errors"], [9, 13, 5, 0], [9, 16, 5, 0, "require"], [9, 23, 5, 0], [9, 24, 5, 0, "_dependencyMap"], [9, 38, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_PlatformChecker"], [10, 22, 6, 0], [10, 25, 6, 0, "require"], [10, 32, 6, 0], [10, 33, 6, 0, "_dependencyMap"], [10, 47, 6, 0], [11, 2, 21, 0], [11, 11, 21, 9, "findHostInstanceFastPath"], [11, 35, 21, 33, "findHostInstanceFastPath"], [11, 36, 21, 34, "maybeNativeRef"], [11, 50, 21, 74], [11, 52, 21, 76], [12, 4, 22, 2], [12, 8, 22, 6], [12, 9, 22, 7, "maybeNativeRef"], [12, 23, 22, 21], [12, 25, 22, 23], [13, 6, 23, 4], [13, 13, 23, 11, "undefined"], [13, 22, 23, 20], [14, 4, 24, 2], [15, 4, 25, 2], [15, 8, 26, 4, "maybeNativeRef"], [15, 22, 26, 18], [15, 23, 26, 19, "__internalInstanceHandle"], [15, 47, 26, 43], [15, 51, 27, 4, "maybeNativeRef"], [15, 65, 27, 18], [15, 66, 27, 19, "__nativeTag"], [15, 77, 27, 30], [15, 81, 28, 4, "maybeNativeRef"], [15, 95, 28, 18], [15, 96, 28, 19, "_viewConfig"], [15, 107, 28, 30], [15, 109, 29, 4], [16, 6, 30, 4], [17, 6, 31, 4], [17, 13, 31, 11, "maybeNativeRef"], [17, 27, 31, 25], [18, 4, 32, 2], [19, 4, 33, 2], [19, 8, 33, 6, "maybeNativeRef"], [19, 22, 33, 20], [19, 23, 33, 21, "_nativeTag"], [19, 33, 33, 31], [19, 37, 33, 35, "maybeNativeRef"], [19, 51, 33, 49], [19, 52, 33, 50, "viewConfig"], [19, 62, 33, 60], [19, 64, 33, 62], [20, 6, 34, 4], [21, 6, 35, 4], [21, 13, 35, 11, "maybeNativeRef"], [21, 27, 35, 25], [22, 4, 36, 2], [23, 4, 37, 2], [24, 4, 38, 2], [25, 4, 39, 2], [25, 11, 39, 9, "undefined"], [25, 20, 39, 18], [26, 2, 40, 0], [27, 2, 42, 0], [27, 11, 42, 9, "resolveFindHostInstance_DEPRECATED"], [27, 45, 42, 43, "resolveFindHostInstance_DEPRECATED"], [27, 46, 42, 43], [27, 48, 42, 46], [28, 4, 43, 2], [28, 8, 43, 6, "findHostInstance_DEPRECATED"], [28, 35, 43, 33], [28, 40, 43, 38, "undefined"], [28, 49, 43, 47], [28, 51, 43, 49], [29, 6, 44, 4], [30, 4, 45, 2], [31, 4, 46, 2], [31, 8, 46, 6], [31, 12, 46, 6, "isF<PERSON><PERSON>"], [31, 37, 46, 14], [31, 39, 46, 15], [31, 40, 46, 16], [31, 42, 46, 18], [32, 6, 47, 4], [32, 10, 47, 8], [33, 8, 48, 6], [33, 12, 48, 12, "ReactFabric"], [33, 23, 48, 23], [33, 26, 48, 26, "require"], [33, 33, 48, 33], [33, 34, 48, 33, "_dependencyMap"], [33, 48, 48, 33], [33, 104, 48, 85], [33, 105, 48, 86], [34, 8, 49, 6], [35, 8, 50, 6], [36, 8, 51, 6, "findHostInstance_DEPRECATED"], [36, 35, 51, 33], [36, 38, 52, 8, "ReactFabric"], [36, 49, 52, 19], [36, 51, 52, 21, "default"], [36, 58, 52, 28], [36, 60, 52, 30, "findHostInstance_DEPRECATED"], [36, 87, 52, 57], [36, 91, 53, 8, "ReactFabric"], [36, 102, 53, 19], [36, 104, 53, 21, "findHostInstance_DEPRECATED"], [36, 131, 53, 48], [37, 6, 54, 4], [37, 7, 54, 5], [37, 8, 54, 6], [37, 15, 54, 13, "e"], [37, 16, 54, 14], [37, 18, 54, 16], [38, 8, 55, 6], [38, 14, 55, 12], [38, 18, 55, 16, "ReanimatedError"], [38, 41, 55, 31], [38, 42, 56, 8], [38, 89, 57, 6], [38, 90, 57, 7], [39, 6, 58, 4], [40, 4, 59, 2], [40, 5, 59, 3], [40, 11, 59, 9], [41, 6, 60, 4], [41, 10, 60, 10, "ReactNative"], [41, 21, 60, 21], [41, 24, 60, 24, "require"], [41, 31, 60, 31], [41, 32, 60, 31, "_dependencyMap"], [41, 46, 60, 31], [41, 102, 60, 83], [41, 103, 60, 84], [42, 6, 61, 4], [43, 6, 62, 4], [44, 6, 63, 4, "findHostInstance_DEPRECATED"], [44, 33, 63, 31], [44, 36, 64, 6, "ReactNative"], [44, 47, 64, 17], [44, 49, 64, 19, "default"], [44, 56, 64, 26], [44, 58, 64, 28, "findHostInstance_DEPRECATED"], [44, 85, 64, 55], [44, 89, 65, 6, "ReactNative"], [44, 100, 65, 17], [44, 102, 65, 19, "findHostInstance_DEPRECATED"], [44, 129, 65, 46], [45, 4, 66, 2], [46, 2, 67, 0], [47, 2, 69, 0], [47, 6, 69, 4, "findHostInstance_DEPRECATED"], [47, 33, 69, 63], [48, 2, 70, 7], [48, 11, 70, 16, "findHostInstance"], [48, 27, 70, 32, "findHostInstance"], [48, 28, 71, 2, "component"], [48, 37, 71, 57], [48, 39, 72, 16], [49, 4, 73, 2], [50, 4, 74, 2], [50, 8, 74, 8, "hostInstance"], [50, 20, 74, 20], [50, 23, 74, 23, "findHostInstanceFastPath"], [50, 47, 74, 47], [50, 48, 75, 5, "component"], [50, 57, 75, 14], [50, 58, 75, 46, "_componentRef"], [50, 71, 76, 2], [50, 72, 76, 3], [51, 4, 77, 2], [51, 8, 77, 6, "hostInstance"], [51, 20, 77, 18], [51, 25, 77, 23, "undefined"], [51, 34, 77, 32], [51, 36, 77, 34], [52, 6, 78, 4], [52, 13, 78, 11, "hostInstance"], [52, 25, 78, 23], [53, 4, 79, 2], [54, 4, 81, 2, "resolveFindHostInstance_DEPRECATED"], [54, 38, 81, 36], [54, 39, 81, 37], [54, 40, 81, 38], [55, 4, 82, 2], [56, 0, 83, 0], [57, 0, 84, 0], [58, 0, 85, 0], [59, 0, 86, 0], [60, 0, 87, 0], [61, 4, 88, 2], [61, 11, 88, 9, "findHostInstance_DEPRECATED"], [61, 38, 88, 36], [61, 39, 89, 4], [61, 40, 89, 5], [61, 44, 89, 5, "isF<PERSON><PERSON>"], [61, 69, 89, 13], [61, 71, 89, 14], [61, 72, 89, 15], [61, 76, 89, 20, "component"], [61, 85, 89, 29], [61, 86, 89, 61, "_hasAnimatedRef"], [61, 101, 89, 76], [61, 104, 90, 9, "component"], [61, 113, 90, 18], [61, 114, 90, 50, "_componentRef"], [61, 127, 90, 63], [61, 130, 91, 8, "component"], [61, 139, 92, 2], [61, 140, 92, 3], [62, 2, 93, 0], [63, 0, 93, 1], [63, 3]], "functionMap": {"names": ["<global>", "findHostInstanceFastPath", "resolveFindHostInstance_DEPRECATED", "findHostInstance"], "mappings": "AAA;ACoB;CDmB;AEE;CFyB;OGG;CHuB"}}, "type": "js/module"}]}